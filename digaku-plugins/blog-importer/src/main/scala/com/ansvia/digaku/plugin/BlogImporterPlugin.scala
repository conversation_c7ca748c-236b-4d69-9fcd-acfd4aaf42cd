/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.plugin

import com.ansvia.digaku.web.{WebConfig, Au<PERSON>, DigakuWebSession}
import net.liftweb.http._
import scala.xml.NodeSeq
import net.liftweb.util.Helpers._
import net.liftweb.sitemap.Menu.Menuable
import net.liftweb.sitemap.Loc._
import com.ansvia.digaku.model.{Group, Post, User}
import com.ansvia.digaku.exc.DigakuException
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import com.ansvia.commons.logging.Slf4jLogger
import net.liftweb.sitemap.Menu
import scala.Left
import scala.Right
import scala.xml.Text
import com.ansvia.digaku.exc.NotExistsException
import net.liftweb.sitemap.Loc.If
import org.jsoup.parser.Parser
import java.net.URL
import scala.actors.Futures
import com.ning.http.client.{AsyncHttpClientConfigBean, AsyncHttpClient}
import net.liftweb.util.NamedPF

/**
 * Author: robin
 * Date: 10/19/13
 * Time: 2:59 PM
 *
 * Plugin Digaku yang digunakan untuk import data dari blog
 * ke Digaku sebagai artikel dalam group.
 */
class BlogImporterPlugin
    extends DigakuPlugin("blog-importer", "Blog importer tools", "0.0.1-SNAPSHOT")
    with FileCommons {


    def init() = {
        debug("initializing...")
    }

    def close() = {
        debug("closed.")
    }

    override def install() = {
        debug("installing...")

        copyResToWebApp("/webapp/blog-importer.html", "/plugins/blog-importer.html")
        copyResToWebApp("/webapp/blog-importer/success.html", "/plugins/blog-importer/success.html")
    }

    def uninstall() = {
        "uninstalling..."
        removeFileFromWebapp("/plugins/blog-importer.html")
        removeFileFromWebapp("/plugins/blog-importer/success.html")
    }


    /**
     * session ini panggil otomatis
     * setelah digaku engine terinisialisasi.
     */
    override def setSession(sess: DigakuWebSession) =
        BlogImporterPlugin.setSession(sess)

    private object cache extends RequestMemoize[String, Boolean]

    override def isReady = {
        cache.get("installed").getOrElse {
            fileExists(webappDir + "/plugins/blog-importer.html") &&
            fileExists(webappDir + "/plugins/blog-importer/success.html")
        }
    }

    /************************************************
      * STATE
      * **********************************************/
    override def installNeedRestart = true



    object snippet extends DispatchSnippet {

        def dispatch:DispatchIt = {
            case "submitUrl" => submitUrl
        }

        private object urlVar extends RequestVar("")
        private object channelVar extends RequestVar("")

        def submitUrl(in:NodeSeq):NodeSeq = {

            def doSubmit() = {
                try {
                    val group = Group.getByName(channelVar).getOrElse {
                        throw NotExistsException("No group with name " + channelVar.is)
                    }

                    BlogImporterPlugin.importBlog(urlVar, group)

                    S.redirectTo("/tool/blog-importer/success")
                }
                catch {
                    case e:DigakuException =>
                        S.error(e.getMessage)
                }
            }

            bind("in", in,
                "url" -> SHtml.text(urlVar, urlVar(_), "class" -> "form-control"),
                "group" -> SHtml.text(channelVar, channelVar(_), "class" -> "form-control"),
                "submit" -> SHtml.submit("Submit", doSubmit, "class" -> "btn btn-primary")
            )
        }

    }

    /**
     * Called before rewrite operation performed.
     * @param liftRules
     */
    override def beforeRewrite(liftRules: LiftRules) = {
        liftRules.statelessRewrite.append(NamedPF("BlogImporterRewrite"){
            case RewriteRequest(ParsePath("tool" :: "blog-importer" :: "success" :: Nil, _, _, _), _, _) =>
                RewriteResponse("plugins/blog-importer/success" :: Nil)

            case RewriteRequest(ParsePath("tool" :: "blog-importer" :: Nil, _, _, _), _, _) =>
                RewriteResponse("plugins/blog-importer" :: Nil)
        })
    }

    /**
     * Apabila plugin ingin memanipulasi sitemap
     * maka return-kan true
     * @see [[com.ansvia.digaku.plugin.DigakuPlugin.buildSitemapEntries]]
     * @return
     */
    override def handleSitemap = true

    override def buildSitemapEntries(entries: List[Menuable]) = {
        entries ++ List(
            Menu("BlogImporterPluginSuccessPage", S.loc("BlogImporterPluginSuccessPage", Text("Blog Importer Success"))) / "plugins/blog-importer/success" >>
                If(()=> Auth.isLoggedIn, "404 Not found") >> Hidden,
            Menu("BlogImporterPluginPage", S.loc("BlogImporterPluginPage", Text("Blog Importer"))) / "plugins/blog-importer" >>
                If(()=> Auth.isLoggedIn, "404 Not found") >> Hidden
        )
    }

    /**
     * Dipanggil sebelum digaku engine di-init.
     * @param liftRules
     */
    override def beforeSetupDigakuEngine(liftRules: LiftRules) = {
        liftRules.snippetDispatch.append({
            case "BlogImporter" => snippet
        })
    }
}




object BlogImporterPlugin {

    import scala.collection.JavaConversions._
    import dispatch._

    private var session:DigakuWebSession = _
    private val http = new Http {
        override lazy val client = {
            val config = new AsyncHttpClientConfigBean()
                .setRedirectEnabled(true).setMaxDefaultRedirects(5)
            val _client = new AsyncHttpClient(config)
            _client
        }
    }

    def setSession(sess:DigakuWebSession) = {
        session = sess
    }

    case class Importer(blogUrl:String, user:User, origin:Group) extends Slf4jLogger {

        lazy val baseUrl = {
            val url = new URL(blogUrl)
            url.getProtocol + "://" + url.getHost
        }

//
//        def getRssUrl(_url:String):String = {
//
//            debug("getting rss data...")
//
//            UrlValidator.validate(_url)
//
//            def notFound() = {
//                throw new DigakuException("RSS url not found")
//            }
//
//            val lUrl = {
//                if (!(_url.endsWith("/feed") || _url.endsWith("/feed/")))
//                    _url + "/feed/"
//                else
//                    _url
//            }
//
//            http(url(lUrl).HEAD).either() match {
//                case Right(resp) =>
//                    if (resp.getStatusCode == 200 && resp.getContentType == "text/xml")
//                        lUrl
//                    else
//                        notFound()
//                case _ =>
//                    notFound()
//            }
//
//        }


        private def processRss(doc: Document){
            debug("processing rss...")
            val links = doc.select("rss group item link")
            links.iterator().foreach { elm =>
                processPage(elm.text())
            }
        }

        private def processPage(link:String) = {
            debug("processing page: " + link)
            http(url(link)).either() match {
                case Right(resp) => {
                    val doc = Jsoup.parse(resp.getResponseBodyAsStream, null,  baseUrl, Parser.htmlParser())

                    doc.select("script, jscript, vbscript").remove()

                    // Search for title
                    val title = doc.select(".post .post-header h1").headOption
                        .getOrElse {
                            doc.select(".entry-title").headOption.getOrElse {
                                doc.select(".post-title").headOption.getOrElse {
                                    doc.select("h1").headOption.getOrElse {
                                        doc.select("title").headOption.getOrElse {
                                            throw NotExistsException("no title")
                                        }
                                    }
                                }
                            }
                        }
                    var content = doc.select(".post .entry p")

                    if (content.isEmpty){
                        content = doc.select(".post-entry p")
                        if (content.isEmpty)
                            content = doc.select(".entry-content p")
                        if (content.isEmpty)
                            content = doc.select(".post p")
                    }

                    if (content.isEmpty)
                        throw NotExistsException("No content")


                    var tags = title.text().split("\\W+").map(_.trim.toLowerCase).filter(_.length > 1)

                    if (tags.length > 5)
                        tags = tags.slice(0, 5)

                    val tagsStr = tags.reduceLeftOption(_ + "," + _).getOrElse("")

                    var normContent = content
                        .filter { elm =>
                            val tag = elm.tagName().toLowerCase
                            tag != "script" && tag != "jscript" && tag != "vbscript"
                        }
                        .map(_.text())
                        .reduceLeftOption(_ + "\n\n" + _).getOrElse("")

                    normContent = normContent + "\n\nImported from: " + baseUrl + "\n" +
                        "Using " + WebConfig.BASE_URL + "/tool/blog-importer"

                    val art = Article.create(user, title.text(), normContent, tagsStr, origin)

                    info("article created: " + art.title)

                }
                case Left(e) =>
                    error(e.getMessage)
            }
        }


        def start(){
            debug("importing blog " + blogUrl + "...")

//            val rssUrl = getRssUrl(blogUrl)

            Futures.future {
                http(url(blogUrl)).either() match {
                    case Right(resp) => {
                        processRss(Jsoup.parse(resp.getResponseBodyAsStream, "UTF-8", blogUrl, Parser.xmlParser()))
                    }
                    case Left(e) =>
                        error(e.getMessage)
                }
                info("import blog " + blogUrl + " done.")
            }

        }
    }

    def importBlog(blogUrl:String, group:Group){
        session.currentUser.map { cu =>
            Importer(blogUrl, cu, group).start()
        }
    }


}