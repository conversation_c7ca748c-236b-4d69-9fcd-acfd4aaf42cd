/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.plugin

import scala.xml.NodeSeq
import com.ansvia.digaku.plugin.DigakuPlugin.WidgetPosition

/**
 * Author: robin
 * Date: 3/11/13
 * Time: 3:22 PM
 *
 */
class DigakuHelloPlugin() extends DigakuPlugin("Hello world plugin", "Example Digaku 2 Plugin", "0.0.1-SNAPSHOT") {


    def init(){
        debug("initializing...")
    }

    def close() {
        debug("%s closed".format(this))
    }

    override def install() {
        debug("installing...")

    }

    override def hasWidget(pos: WidgetPosition) =
        pos == WidgetPosition.HomeFooter

    override def getWidget(pos: WidgetPosition, ref:Any) = {
        if(pos == WidgetPosition.HomeFooter){
            <em>Hello World! {version}</em>
        }else
            NodeSeq.Empty
    }

//
//    private def myAdminMenu:List[Menuable] = {
//        List(
//            Menu("AdminChat", S.loc("AdminChat", Text("Chat"))) / "admin" / "chat" >> LocGroup("admin")
//        )
//    }
    def uninstall() {
        debug("uninstalling...")
    }
}
