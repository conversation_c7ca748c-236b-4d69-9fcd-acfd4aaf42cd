///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.plugin.chat
//
//import net.liftweb.http._
//import net.liftweb.actor.LiftActor
//import scala.collection.mutable
//
//
///**
// * Author: robin
// * Date: 3/12/13
// * Time: 3:22 AM
// *
// */
//object DigakuChatPluginCometServer extends LiftActor with ListenerManager {
//
//    private val onlineCount = new mutable.HashMap[String, Int]()
//        with mutable.SynchronizedMap[String, Int]
//
//    var msgs = Array.empty[String]
//
//    protected def createUpdate = msgs
//
//    override protected def lowPriority = {
//
//        case msg:MessageFor =>
//            updateListeners(msg)
//
//        case OnlineCount(chName) =>
////            println("onlineCount.getOrElseUpdate(%s, 0): ".format(chName) + onlineCount.getOrElseUpdate(chName, 0))
//            reply(onlineCount.getOrElseUpdate(chName, 0))
//
//        case IncOnlineCount(chName) =>
////            println("IncOnlineCount(%s)".format(chName))
//            onlineCount.update(chName, onlineCount.getOrElseUpdate(chName, 0) + 1)
//
//        case DecOnlineCount(chName) =>
//            onlineCount.update(chName, onlineCount.getOrElseUpdate(chName, 1) - 1)
//
//    }
//
//}
