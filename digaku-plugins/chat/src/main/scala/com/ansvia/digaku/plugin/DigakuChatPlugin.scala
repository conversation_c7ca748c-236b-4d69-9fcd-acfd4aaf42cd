///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.plugin
//
//import net.liftweb._
//
//import sitemap._
//import Loc._
//import net.liftweb.sitemap.Menu.Menuable
//import net.liftweb.http._
//import scala.xml.NodeSeq
//import net.liftweb.sitemap.Menu
//import com.ansvia.digaku.plugin.DigakuPlugin.WidgetPosition
//import java.io.File
//import com.ansvia.digaku.web.{Auth, DigakuWebSession}
//import com.ansvia.digaku.plugin.chat._
//import net.liftweb.util.NamedPF
//import net.liftweb.util.BindHelpers._
//import com.ansvia.digaku.plugin.chat.OnlineCount
//import net.liftweb.http.RewriteRequest
//import net.liftweb.http.ParsePath
//import scala.xml.Text
//import com.ansvia.digaku.model.Forum
//import net.liftweb.sitemap.Loc.{If, LocGroup}
//
///**
// * Author: robin
// * Date: 3/11/13
// * Time: 3:22 PM
// *
// */
//class DigakuChatPlugin()
//    extends DigakuPlugin("digaku-chat", "Chat for Digaku", "0.0.7-SNAPSHOT")
//    with FileCommons {
//
//    def init(){
//        debug("initializing...")
//    }
//
//    override def setSession(sess: DigakuWebSession) {
//        super.setSession(sess)
//        Shared.session = sess
//    }
//
//    def close() {
//        debug("closed.")
//    }
//
//    override def install() {
//        super.install()
//
//        debug("installing...")
//
//        extractTemplates()
//
//    }
//
//
//    def uninstall() {
//        debug("uninstalling...")
//
//        removeTemplates()
//    }
//
//    private def extractTemplates(){
//
//        copyResToWebApp("/webapp/admin/chat.html", "/admin/chat.html")
//        copyResToWebApp("/webapp/forum/chat.html", "/forum/chat.html")
//
//    }
//
//    private def removeTemplates(){
//        new File(webappDir + "/admin/chat.html").delete()
//        new File(webappDir + "/forum/chat.html").delete()
//    }
//
//    // is installed cache
//    private object cache extends RequestMemoize[String, Boolean]
//
//    /**
//     * Apakah plugin ini ready?
//     * return true apabila ready, sebaliknya return false kalo broken.
//     * @return
//     */
//    override def isReady = {
//        val o = cache.get("ready")
//        if(o.isDefined)
//            o.get
//        else{
//            val rv = {
////                debug("check for: " + webappDir + "/admin/chat.html. exists: " + (fileExists(webappDir + "/admin/chat.html")))
////                debug("check for: " + webappDir + "/forum/chat.html. exists: " + (fileExists(webappDir + "/forum/chat.html")))
////                debug("check for: " + libDir + "/" + fileName + ". exists: " + (fileExists(libDir + "/" + fileName)))
//                fileExists(webappDir + "/admin/chat.html") &&
//                    fileExists(webappDir + "/forum/chat.html") &&
//                    fileExists(libDir + "/" + fileName)
//            }
//            cache.set("ready", rv)
//            rv
//        }
//    }
//
//    override def handleSitemap = true
//
//    override def buildSitemapEntries(entries: List[Menuable]) = {
//        val adminEntries = entries.filter(_.name == "AdminIndex").head
//        val newEntries = adminEntries.submenus(myAdminMenu)
//        val rv = entries.map { x =>
//            if(x.name == "AdminIndex")
//                newEntries
//            else
//                x
//        }
////        println((rv ++ chatPage).map(_.name).toList)
//        (rv ++ chatPage)
//    }
//
//    override def hasWidget(pos: WidgetPosition) =
//        pos == WidgetPosition.ChannelRightHead
//
//    import com.ansvia.digaku.web.builder.MyOriginBuilder._
//
//    override def getWidget(pos: WidgetPosition, ref:Any) = {
//        if(pos == WidgetPosition.ChannelRightHead){
//
//            var chatUrl = ""
//            val onlineCount = {
//                ref match {
//                    case ch:Group =>
//                        chatUrl = ch.url + "/chat"
//                        <span class="icon-comments-alt">
//                            {DigakuChatPluginCometServer.!!(OnlineCount(ch.lowerName), 2000).openOr(0)} online!</span>
//                    case _ =>
//                        <span class="icon-comments-alt"> Join a chat!</span>
//                }
//            }
//
//            <div class="well sidebar-nav">
//                <strong>Chat</strong>
//                <div>
//                    <a href={chatUrl}>
//                        {onlineCount}
//                    </a>
//                </div>
//            </div>
//        }else
//            NodeSeq.Empty
//    }
//
//    private def chatPage = {
//        List(
//            Menu("ChannelChatPage", S.loc("ChannelChatPage", Text("Chat"))) / "forum/chat" >>
//                If(() => Auth.isLoggedIn, "Unauthorized, you must login first.")
//        )
//    }
//
//
//    override def beforeRewrite(liftRules: LiftRules) {
//        liftRules.statelessRewrite.append(NamedPF("ChannelChatRewrite"){
//            case RewriteRequest(
//                ParsePath("group" :: channelName :: "chat" :: Nil, _, _, _), _, _) =>
//                    RewriteResponse("forum/chat" :: Nil, Map("forumId" -> channelName.toLowerCase))
//        })
//    }
//
//    private def myAdminMenu:List[Menuable] = {
//        List(
//            Menu("AdminChat", S.loc("AdminChat", Text("Chat"))) / "admin" / "chat" >> LocGroup("admin")
//        )
//    }
//
//    override def installNeedRestart = true
//    override def uninstallNeedRestart = true
//
//
//    object snippet extends DispatchSnippet {
//
//        def dispatch:DispatchIt = {
//            case "initComet" => init
//        }
//
//        def init = {
//            "#ChatRoom [data-lift]" #>
//                ("comet?type=DigakuChatPluginComet&name=" + S.param("forumId").openOr("no-group").toLowerCase)
//        }
//    }
//
//    /**
//     * Called after digaku engine initialized.
//     */
//    override def afterSetupDigakuEngine(liftRules: LiftRules) {
//        liftRules.snippetDispatch.append({
//            case "DigakuChatPlugin" => snippet
//        })
//    }
//}
//
//
//object Shared {
//    var session:DigakuWebSession = null
//}
//
//
//
