///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.mindtalk.comet
//
//import net.liftweb.http.{CometListener, SHtml, CometActor}
//import com.ansvia.digaku.model.Forum
//import net.liftweb.http.js.JsCmd
//import com.ansvia.digaku.plugin.chat.{DigakuChatPluginCometServer, DecOnlineCount, IncOnlineCount, MessageFor}
//import com.ansvia.digaku.utils.TextCompiler
//import scala.collection.mutable
//import scala.xml.Unparsed
//import com.ansvia.util.idgen.IncrementalNumIdGenerator
//import net.liftweb.common.{Box, Full}
//import net.liftweb.util.Helpers._
//
///**
// * Author: robin
// * Date: 3/12/13
// * Time: 12:00 AM
// *
// */
//class DigakuChatPluginComet extends IncrementalNumIdGenerator(1L)
//        with CometActor with CometListener {
//
//    protected def registerWith = DigakuChatPluginCometServer
//
//    /**
//     * Kill after 1 minutes without activity.
//     * @return
//     */
//    override def lifespan: Box[TimeSpan] = Full(1 minutes)
//
//
//    private lazy val group = {
//        Group.getByName(name.get)
//    }
//
//    private def currentUser = com.ansvia.digaku.plugin.Shared.session.currentUser
//
//    override protected def localSetup() {
//        super.localSetup()
//        registerWith ! IncOnlineCount(name.get)
//    }
//
//    override protected def localShutdown() {
//        super.localShutdown()
//        registerWith ! DecOnlineCount(name.get)
//    }
//
//    def render = {
//        "h4 *" #> ("Welcome to #" + group.get.name + " chat!") &
//        ".chat-warning-msg *" #> <div class="post-attributes">Welcome to #{name.get} chat room.
//            <br /> <span class="icon-warning-sign"></span> Warning! all messages are inpersistent, will be lost when server reboot.</div> &
//        ".chat-msg *" #> {
//            Messages.messagesMap.get(name.get).getOrElse(mutable.Queue.empty[Message])
//                .toList.map { msg =>
//                    (<div><strong>{msg.userName}</strong>: {Unparsed(TextCompiler.compileMessage(msg.text))}</div>)
//                }
//        } &
//        "#Input *" #>
//            SHtml.ajaxForm(SHtml.text("", submitMessage _,"placeholder" -> "type text here", "class" -> "col-md-12"))
////            SHtml.onSubmit((text) => submitMessage(text))
//    }
//
//    private def submitMessage(text:String):JsCmd = {
////        this ! MessageFor(group.get, currentUser.get, text)
//        DigakuChatPluginCometServer ! MessageFor(group.get, currentUser.get, text, nextId())
//        new JsCmd {
//            def toJsCmd = ""
//        }
//    }
//
//    override def lowPriority = {
//        case MessageFor(ch, user, text, id) =>
//            group.map { currentChannel =>
//                if(currentChannel == ch && text.trim.length > 0){
//
////                    val escapedText = StringEscapeUtils.escapeHtml(text)
//
////                    println("escapedText: " + escapedText)
////                    println("compiledText: " + TextCompiler.compileMessage(text))
//
//                    if(!Messages.messagesMap.contains(name.get))
//                        Messages.messagesMap += name.get -> mutable.Queue.empty[Message]
//
//                    Messages.messagesMap.get(name.get).map { msgs =>
//                        if(msgs.length > 50)
//                            msgs.dequeue()
//
//                        val newMsg = Message(user.name, text.trim, id)
//
//                        if(!msgs.toList.contains(newMsg)){
//                            msgs.enqueue(newMsg)
//                            Messages.messagesMap += name.get -> msgs
//                        }
//
//                    }
//
//                    partialUpdate(new JsCmd {
//                        def toJsCmd =
//                            """
//                              |appendMessage('%s','%s');
//                              | """.stripMargin.format(user.name,
//                                TextCompiler.compileMessage(text.trim)
//                                    .replaceAll("'", "\\'"))
//                    })
//                }
//            }
//
//    }
//}
//
//sealed case class Message(userName:String, text:String, id:Long) {
//    override def hashCode() = id.hashCode()
//}
//
//private[this] object Messages {
//    var messagesMap = Map.empty[String, mutable.Queue[Message]]
//}
