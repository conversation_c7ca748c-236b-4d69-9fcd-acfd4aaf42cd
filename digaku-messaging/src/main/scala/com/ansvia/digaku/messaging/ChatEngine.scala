package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.persistence.{CassandraBackedSeqStore, SnowFlakeIdFactory}
import com.netflix.astyanax.model.ColumnFamily
import com.netflix.astyanax.serializers.{ByteSerializer, LongSerializer, StringSerializer}

/**
 * Author: nadir (<EMAIL>)
 */

trait ChatEngine {
    def generateId():Long
}

trait ChatComponent {
    def engine:ChatEngine
}

class StandardEngine extends ChatEngine {
    protected val idGen = new SnowFlakeIdFactory(Digaku.engine.config.machineId)

    override def generateId(): Long = idGen.createId().asInstanceOf[Long]
}

trait ChatComponentConfig extends ChatComponent {
    override def engine: ChatEngine = ChatComponentConfig._engine
}

object ChatComponentConfig {
    private var _engine:ChatEngine = new StandardEngine

    def setEngine(_engine:ChatEngine) = {
        this._engine = _engine
    }
}

trait ChatSeqStore {
    protected val CFName:String

    protected lazy val CF = new ColumnFamily[String, java.lang.Long](
        CFName,
        StringSerializer.get(),
        LongSerializer.get(),
        ByteSerializer.get())

    lazy val seqStore:CassandraBackedSeqStore[java.lang.Long] = new CassandraBackedSeqStore(CF,
        Digaku.config.mainDatabase.keyspaceName,
        Digaku.config.mainDatabase.clusterName,
        Digaku.config.mainDatabase.hostName,
        Digaku.config.mainDatabase.replStrategy,
        Digaku.config.mainDatabase.replStrategyOpts)
        .setComparatorType("LongType(reversed=true)")
        .setCaching("NONE")

    val totalText = "total"
    val unreadText = "unread"
}
