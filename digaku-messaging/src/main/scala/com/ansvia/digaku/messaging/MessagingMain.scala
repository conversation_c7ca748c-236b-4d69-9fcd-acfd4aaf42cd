/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import java.io.FileNotFoundException
import java.net.InetSocketAddress
import java.util.concurrent.TimeUnit
import com.ansvia.digaku.live.LiveObjectRegistry
import com.ansvia.digaku.messaging.cluster.MessagingInterNodeMessageHandler
import com.ansvia.digaku.messaging.live.OnlineTimeReminder
import com.ansvia.digaku.messaging.se.{DistributedElasticSearchEngineWithChat, MTSearchEngineWithChat}
import com.ansvia.digaku.messaging.utils.ScChatAutoTimeout
import com.ansvia.digaku.se.{DistributedElasticSearchEngine, AbstractElasticSearchEngine, MTSearchEngine, DigakuSearchEngine}
import com.ansvia.digaku.utils.RichString._
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.exc.{DigakuException, NotExistsException}
import com.ansvia.digaku.lib.WebEngineWithConfig
import com.ansvia.digaku.model.User
import com.ansvia.digaku.notifications.{NotificationBase, PersistentNotification}
import com.ansvia.digaku.nsq.NsqDataCompressor
import com.ansvia.digaku.thrift.messaging.{MessagingService, PushMessage}
import com.ansvia.digaku.web.{WebGlobal, WebConfig}
import com.ansvia.digaku.web.notification._
import com.ansvia.digaku.web.pushnotificationsender._
import ly.bit.nsq
import ly.bit.nsq.lookupd.BasicLookupd
import ly.bit.nsq.syncresponse.{SyncResponseReader, SyncResponseHandler}
import ly.bit.nsq.NSQProducer
import net.liftweb.http.LiftRules
import net.liftweb.json.{NoTypeHints, Serialization}
import org.apache.thrift.protocol.TCompactProtocol
import org.apache.thrift.server.{THsHaServer, TServer, TThreadPoolServer}
import org.apache.thrift.transport._
import org.elasticsearch.client.transport.NoNodeAvailableException
import org.streum.configrity.Configuration
import ImplicitChatUser._

import scala.concurrent.ExecutionContext

// scalastyle:off regex.line magic.number

/**
 * Author: robin (<EMAIL>)
 */


class WebEngineWithConfigWithMessaging(conf:Configuration) extends WebEngineWithConfig(conf){

    class MessagingNotifGateway extends EWCNsqNotifGateway {
        override var webNotifCometServer: WebNotificationCometServerBase = new WebNotificationCometServerBase {
            override protected def currentUser: Option[User] = None
        }
    }

    // setup notification gateway
    override lazy val notifGateway: EWCNotifGateway = {
        if (config.supportNsq){
            new MessagingNotifGateway
        }else{
            //                    throw new DigakuException("should use NSQ")
            warn("no nsq support, will use NopNotifGateway instead")
            new EWCNopNotifGateway
        }
    }

    type HandlerType = PartialFunction[NotificationBase, Unit]

    override protected lazy val _searchEngine: DigakuSearchEngine = {
        fileConf[String]("search.engine", "embedded-elastic-search") match {
            case "custom" =>
                new MTSearchEngineWithChat(fileConf[String]("search.engine.host", "127.0.0.1:9300"),
                    fileConf[String]("search.engine.cluster"))
            case "distributed-elastic-search" =>
                val _se = new DistributedElasticSearchEngineWithChat(
                    fileConf[String]("search.engine.host"),
                    fileConf[String]("search.engine.cluster")
                )
                try {
                    _se.ensureIndicesCreated()
                    _se.ensureChatIndicesCreated()
                } catch {
                    case e:NoNodeAvailableException =>
                        warn("No distributed-search-engine node available")
                }
                _se
            case _ =>
                super.searchEngine
        }
    }

    override lazy val setupNotifHandlers = {
//        debug("setup notification handlers...")

        object NotifToEmailSendHandler extends NotificationWebHandlerBase(notifGateway){
            protected def sendEmail(ntf: PersistentNotification){
                // jangan aktifkan dulu untuk email notification
                // terkait dengan di BCA tidak semua user memiliki email.
//                SendToEmail.send(ntf)
            }
        }

        getNotificationSender.addHandlers(NotifToEmailSendHandler)

        if (config.IS_SUPPORT_ANDROID_PUSH_NOTIF) {
//            println("setup android push notif handler...")
            val androidGCMHandler = new AndroidGCMPushNotificationSendHandler() {

                override protected def asyncDispatch: PartialFunction[NotificationBase, Unit] = {

                    val handler:HandlerType = {
                        case ntf:ChatPushNotification if ntf.isValid =>

//                            debug(s"starting to send notif via GCM: $ntf, isValid: ${ntf.isValid}")

                            extractUsersAndConns(ntf).foreach { case (user, conn) =>

                                val timestamp = ntf.getCreationTime
                                val payload = ntf.renderFor(user)
                                val group = ntf.message.getTargetChatId
                                val notifID = ntf.getId

                                val notifData = new AndroidNotifData(timestamp, payload, "chat", group, notifID, 0L)

                                debug(s"[safe] notification pushed to $user, conn.uid: ${conn.uid}, notif data: $notifData")
                                AndroidGCMPushNotification.push(conn.uid, notifData)

                            }

                        case ntf:ChatGeneralPushNotification if ntf.isValid =>
                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
                                val timestamp = ntf.getCreationTime
                                val payload = ntf.renderFor(user)
                                val group = ntf.chat.getId
                                val notifID = ntf.getId
                                val notifType = ntf.notifType

                                val notifData = new AndroidNotifData(timestamp, payload, notifType, group, notifID, 0L)

                                debug(s"[safe] general notification pushed to $user, conn.uid: ${conn.uid}, notif data: $notifData")
                                AndroidGCMPushNotification.push(conn.uid, notifData)
                            }

                        // Tidak perlu mengirimkan typing notification karena sudah dihandle oleh thrift listener
                        // dan typing notif ini hanya ditampilkan ketika apps sedang dijalankan, jadi otomatis apps pasti listen thrift
                        // dan mendapatkan event typing.
//                        case ntf:ChatTypingPushNotification if ntf.isValid =>
//                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
//                                val timestamp = ntf.getCreationTime
//                                val payload = ntf.renderFor(user)
//                                val group = ntf.chat.getId
//                                val notifID = ntf.getId
//                                val notifType = ntf.notifType
//
//                                val notifData = new AndroidNotifData(timestamp, payload, notifType, group, notifID, 0L)
//
//                                warn(s"[safe] typing notification pushed to $user, conn.uid: ${conn.uid}, notif data: $notifData")
//                                AndroidGCMPushNotification.push(conn.uid, notifData)
//                            }

                        case ntf:ScChatPushNotification if ntf.isValid =>
                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
                                val msg = ntf.message match {
                                    case me:MessageEvent =>
                                        val scChat = ntf.scChat.get
                                        val textRendered = user.renderEventText(scChat, me)
                                        val metaData = me.metaData + ("textRendered" -> textRendered)
                                        me.setMetaData(metaData)
                                    case m => m
                                }

                                val timestamp = ntf.getCreationTime
                                val payload = msg.toJsonString
                                val group = ntf.message.getTargetChatId
                                val notifID = ntf.getId

                                val notifData = new AndroidNotifData(timestamp, payload, "sc-chat", group, notifID, 0L)

                                debug(s"[safe] ScChat notification pushed to $user, conn.uid: ${conn.uid}, notif data: $notifData")

                                AndroidGCMPushNotification.push(conn.uid, notifData)
                            }

                        case ntf:ScChatDeliveryStatusPushNotification if ntf.isValid =>
                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
                                val payload = ntf.renderFor(user)
                                val group = ntf.scChat.getId
                                val notifID = ntf.getId
                                val timestamp = ntf.getCreationTime

                                val notifData = new AndroidNotifData(timestamp, payload, "sc-chat", group, notifID, 0L)

                                debug(s"[safe] ScChat delivery status notification pushed to $user, conn.uid: ${conn.uid}, notif data: $notifData")

                                AndroidGCMPushNotification.push(conn.uid, notifData)
                            }
                    }

                    // harus ditaruh di-depan agar tidak di-swallow oleh `case _ =>`
                    handler orElse super.asyncDispatch
                }
            }

            val androidC2DMHandler = new AndroidC2DMPushNotificationSendHandler() {

                override protected def asyncDispatch: PartialFunction[NotificationBase, Unit] = {
                    val handler:HandlerType = {
                        case ntf:ChatPushNotification if ntf.isValid =>

                            extractUsersAndConns(ntf).foreach { case (user, conn) =>

                                val timestamp = ntf.getCreationTime
                                val payload = ntf.renderFor(user)
                                val group = ntf.message.getTargetChatId
                                val notifID = ntf.getId

                                val notifData = new NotifData(timestamp, payload, group, notifID, 0L)

                                AndroidC2DMPushNotification.push(conn.uid, notifData)
                            }

                        case ntf:ChatGeneralPushNotification if ntf.isValid =>
                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
                                val timestamp = ntf.getCreationTime
                                val payload = ntf.renderFor(user)
                                val group = ntf.chat.getId
                                val notifID = ntf.getId

                                val notifData = new NotifData(timestamp, payload, group, notifID, 0L)

                                AndroidC2DMPushNotification.push(conn.uid, notifData)
                            }

                        // Tidak perlu mengirimkan typing notification karena sudah dihandle oleh thrift listener
                        // dan typing notif ini hanya ditampilkan ketika apps sedang dijalankan, jadi otomatis apps pasti listen thrift
                        // dan mendapatkan event typing.
//                        case ntf:ChatTypingPushNotification if ntf.isValid =>
//                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
//                                val timestamp = ntf.getCreationTime
//                                val payload = ntf.renderFor(user)
//                                val group = ntf.chat.getId
//                                val notifID = ntf.getId
//
//                                val notifData = new NotifData(timestamp, payload, group, notifID, 0L)
//
//                                AndroidC2DMPushNotification.push(conn.uid, notifData)
//                            }

                        case ntf:ScChatPushNotification if ntf.isValid =>
                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
                                val msg = ntf.message match {
                                    case me:MessageEvent =>
                                        val scChat = ntf.scChat.get
                                        val textRendered = user.renderEventText(scChat, me)
                                        val metaData = me.metaData + ("textRendered" -> textRendered)
                                        me.setMetaData(metaData)
                                    case m => m
                                }

                                val timestamp = ntf.getCreationTime
                                val payload = msg.toJsonString
                                val group = ntf.message.getTargetChatId
                                val notifID = ntf.getId

                                val notifData = new NotifData(timestamp, payload, group, notifID, 0L)
                                AndroidC2DMPushNotification.push(conn.uid, notifData)
                            }

                        case ntf:ScChatDeliveryStatusPushNotification if ntf.isValid =>
                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
                                val payload = ntf.renderFor(user)
                                val group = ntf.scChat.getId
                                val notifID = ntf.getId
                                val timestamp = ntf.getCreationTime

                                val notifData = new NotifData(timestamp, payload, group, notifID, 0L)
                                AndroidC2DMPushNotification.push(conn.uid, notifData)
                            }
                    }

                    // harus ditaruh di-depan agar tidak di-swallow oleh `case _ =>`
                    handler orElse super.asyncDispatch
                }
            }

            getNotificationSender.addHandlers(androidGCMHandler, androidC2DMHandler)
        }

        if (config.IS_SUPPORT_ANDROID_FCM_NOTIF) {
            val androidFCMHandler = new AndroidFCMPushNotificationSendHandler() {

                override protected def asyncDispatch: PartialFunction[NotificationBase, Unit] = {

                    val handler:HandlerType = {
                        case ntf:ChatPushNotification if ntf.isValid =>

                            //                            debug(s"starting to send notif via GCM: $ntf, isValid: ${ntf.isValid}")

                            extractUsersAndConns(ntf).foreach { case (user, conn) =>

                                val timestamp = ntf.getCreationTime
                                val payload = ntf.renderFor(user)
                                val group = ntf.message.getTargetChatId
                                val notifID = ntf.getId

                                val notifData = new AndroidNotifData(timestamp, payload, "chat", group, notifID, 0L)

                                debug(s"[safe] notification pushed to $user, conn.uid: ${conn.uid}, notif data: $notifData")
                                pushNotif(conn.uid, notifData)
                            }

                        case ntf:ChatGeneralPushNotification if ntf.isValid =>
                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
                                val timestamp = ntf.getCreationTime
                                val payload = ntf.renderFor(user)
                                val group = ntf.chat.getId
                                val notifID = ntf.getId
                                val notifType = ntf.notifType

                                val notifData = new AndroidNotifData(timestamp, payload, notifType, group, notifID, 0L)

                                debug(s"[safe] general notification pushed to $user, conn.uid: ${conn.uid}, notif data: $notifData")
                                pushNotif(conn.uid, notifData)
                            }

                        // Tidak perlu mengirimkan typing notification karena sudah dihandle oleh thrift listener
                        // dan typing notif ini hanya ditampilkan ketika apps sedang dijalankan, jadi otomatis apps pasti listen thrift
                        // dan mendapatkan event typing.
//                        case ntf:ChatTypingPushNotification if ntf.isValid =>
//                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
//                                val timestamp = ntf.getCreationTime
//                                val payload = ntf.renderFor(user)
//                                val group = ntf.chat.getId
//                                val notifID = ntf.getId
//                                val notifType = ntf.notifType
//
//                                val notifData = new AndroidNotifData(timestamp, payload, notifType, group, notifID, 0L)
//
//                                debug(s"[safe] typing notification pushed to $user, conn.uid: ${conn.uid}, notif data: $notifData")
//                                pushNotif(conn.uid, notifData)
//                            }

                        case ntf:ScChatPushNotification if ntf.isValid =>
                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
                                val msg = ntf.message match {
                                    case me:MessageEvent =>
                                        val scChat = ntf.scChat.get
                                        val textRendered = user.renderEventText(scChat, me)
                                        val metaData = me.metaData + ("textRendered" -> textRendered)
                                        me.setMetaData(metaData)
                                    case m => m
                                }

                                val timestamp = ntf.getCreationTime
                                val payload = msg.toJsonString
                                val group = ntf.message.getTargetChatId
                                val notifID = ntf.getId

                                val notifData = new AndroidNotifData(timestamp, payload, "sc-chat", group, notifID, 0L)

                                debug(s"[safe] ScChat notification pushed to $user, conn.uid: ${conn.uid}, notif data: $notifData")

                                pushNotif(conn.uid, notifData)
                            }

                        case ntf:ScChatDeliveryStatusPushNotification if ntf.isValid =>
                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
                                val payload = ntf.renderFor(user)
                                val group = ntf.scChat.getId
                                val notifID = ntf.getId
                                val timestamp = ntf.getCreationTime

                                val notifData = new AndroidNotifData(timestamp, payload, "sc-chat", group, notifID, 0L)

                                debug(s"[safe] ScChat delivery status notification pushed to $user, conn.uid: ${conn.uid}, notif data: $notifData")

                                pushNotif(conn.uid, notifData)
                            }
                    }

                    // harus ditaruh di-depan agar tidak di-swallow oleh `case _ =>`
                    handler orElse super.asyncDispatch
                }
            }

            getNotificationSender.addHandlers(androidFCMHandler)
        }

        if(config.IS_SUPPORT_APPLE_PUSH_NOTIF) {
            val apnsHandler = new ApplePushNotificationServiceHandler(){
                override protected def asyncDispatch: PartialFunction[NotificationBase, Unit] = {
                    val handler:HandlerType = {
                        case ntf:ChatPushNotification if ntf.isValid =>

                            extractUsersAndConns(ntf).foreach { case (user, conn) =>

                                val payload = ntf.renderFor(user)
                                val group = ntf.message.getTargetChatId
                                val notifID = ntf.getId

                                val notifData = new AppleNotifData(payload, group, "message-sent-event", "new message", notifID, "", 0L)

                                ApplePushNotification.push(conn.uid, notifData)
                            }

                        case ntf:ChatGeneralPushNotification if ntf.isValid =>
                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
                                val payload = ntf.renderFor(user)
                                val group = ntf.chat.getId
                                val notifID = ntf.getId
                                val notifType = ntf.notifType
                                val hiddenMsg = ntf.hiddenMessage

                                val notifData = new AppleNotifData(payload, group, notifType, "chat", notifID, hiddenMsg, 0L, sound = false)

                                ApplePushNotification.push(conn.uid, notifData)
                            }

                        // Tidak perlu mengirimkan typing notification karena sudah dihandle oleh thrift listener
                        // dan typing notif ini hanya ditampilkan ketika apps sedang dijalankan, jadi otomatis apps pasti listen thrift
                        // dan mendapatkan event typing.
//                        case ntf:ChatTypingPushNotification if ntf.isValid =>
//                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
//                                val payload = ntf.renderFor(user)
//                                val group = ntf.chat.getId
//                                val notifID = ntf.getId
//                                val notifType = ntf.notifType
//                                val hiddenMsg = ntf.hiddenMessage
//
//                                val notifData = new AppleNotifData(payload, group, notifType, "chat", notifID, hiddenMsg, 0L, false)
//
//                                ApplePushNotification.push(conn.uid, notifData)
//                            }

                        case ntf:ScChatPushNotification if ntf.isValid =>
                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
                                val msg = ntf.message match {
                                    case me:MessageEvent =>
                                        val scChat = ntf.scChat.get
                                        val textRendered = user.renderEventText(scChat, me)
                                        val metaData = me.metaData + ("textRendered" -> textRendered)
                                        me.setMetaData(metaData)
                                    case m => m
                                }

                                val payload = msg.toJsonString
                                val group = ntf.message.getTargetChatId
                                val notifID = ntf.getId

                                val notifData = new AppleNotifData(payload, group, "scc-message-sent-event", "new sc-chat message", notifID, "", 0L)

                                ApplePushNotification.push(conn.uid, notifData)
                            }

                        case ntf:ScChatDeliveryStatusPushNotification if ntf.isValid =>
                            extractUsersAndConns(ntf).foreach { case (user, conn) =>
                                val payload = ntf.renderFor(user)
                                val group = ntf.scChat.getId
                                val notifID = ntf.getId

                                val notifData = new AppleNotifData(payload, group, "delivery-status-update-event", "sc-chat", notifID, "", 0L, sound = false)

                                ApplePushNotification.push(conn.uid, notifData)
                            }
                    }

                    handler orElse super.asyncDispatch
                }
            }
            getNotificationSender.addHandlers(apnsHandler)
        }


        //// temporary disabled, not used in DSO project.
//        if (config.IS_SUPPORT_BB_PUSH_NOTIF) {
//            val blackberryPushHandler = new BlackberryPushNotificationSendHandler()
//            getNotificationSender.addHandlers(blackberryPushHandler)
//        }
//
//        if(config.IS_SUPPORT_MICROSOFT_PUSH_NOTIF) {
//            val mpnsHandler = new MicrosoftPushNotificationServiceHandler()
//            getNotificationSender.addHandlers(mpnsHandler)
//        }
    }

}


trait ActiveDigakuEngine extends Slf4jLogger {


    /**
     * setup database.
     */
    protected def setupDigakuEngine(configFile:String){

        debug("loading configuration...")

        val conf = Configuration.load(configFile)

        try {
            info("Using config file: " + configFile)
        }catch{
            case e:FileNotFoundException =>
                error(e.getMessage)
                e.printStackTrace()
                System.exit(4)
        }


        Digaku.engine = new WebEngineWithConfigWithMessaging(conf) {

            // Messaging gateway ini hanya uspport untuk pengiriman ke nsq
            // dan tidak implement SyncResponseHandler sehingga tidak menerima message dari nsq
            class EWCNsqMessagingGateway extends EWCCometNotifGateway with SyncResponseHandler {

                private implicit val formats = Serialization.formats(NoTypeHints)

                private val topic = WebConfig.BRAND_NAME.urlize.toLowerCase + ".digaku-notif"
                private lazy val nsqProducer =
                    new NSQProducer("http://" + config.nsqPublisherHost, topic)

                private val reader: SyncResponseReader = new SyncResponseReader(topic, WebConfig.NODE_ID, this)
                reader.addLookupd(new BasicLookupd("http://" + config.nsqLookupHost))

                // ping to ensure it readiness
                nsqProducer.putAsync("ping")

                def pipe(data: Any){
                    sendToNsqServer(data)
                }

                /**
                 * Ini akan mengirimkan message ke Nsq server
                 * sebelumnya message aka di-encode dulu ke JSON.
                 * @param data msg yang akan di-encode.
                 */
                private def sendToNsqServer(data: Any){

                    data match {

                        case am:WebActorMessage with CustomImplementation =>
                            nsqProducer.putAsync(NsqDataCompressor.compress(am.toNsqMessage))

                        case am:WebActorMessage =>
                            import com.ansvia.digaku.web.notification.NotificationNsqBuilder._

                            nsqProducer.putAsync(NsqDataCompressor.compress(am.toNsqMessage))

                        case x =>
                            throw new DigakuException("Unknown to handle " + x)
                    }

                }

                def close(){
                    nsqProducer.shutdown()
                }

                override var webNotifCometServer: WebNotificationCometServerBase = new WebNotificationCometServerBase {
                    override protected def currentUser: Option[User] = None
                }

                // Digaku messaging service tidak menghandle message notif dari NSQ
                override def handleMessage(msg: nsq.Message): Boolean = {
                    true
                }

            }

            override lazy val notifGateway: EWCNotifGateway = {
                if (config.supportNsq){
                    new EWCNsqMessagingGateway
                }else{
                    //                    throw new DigakuException("should use NSQ")
                    warn("no nsq support, will use NopNotifGateway instead")
                    new EWCNopNotifGateway
                }
            }
        }

        /**
         * Dispatch Digaku engine initialization
         * procedure.
         */
        Digaku.init()

        Messaging.init()

        Digaku.engine.print()

        /**
         * Ensure indices.
         */
        try {
            Digaku.engine.database.index()
        }
        catch {
            case e:Throwable =>
        }


        LiftRules.unloadHooks.append { ()=>
            shutdown()
        }


        // Jalankan reminder live online/offline time
        if (ScChatSettings.supportOnlineTimeReminder) {
            OnlineTimeReminder.setReminder().foreach(_.start())
        }

        // Initial auto timeout sc chat
        if (ScChatSettings.supportAutoTimeoutChat) {
            ScChatAutoTimeout.resetAutoTimeout()
        }

        LiveObjectRegistry.getAll().foreach(lo => println("   + " + lo))

        // Registerkan MessagingInterNodeMessageHandler ke interNodeComm untuk menangani
        // message dari NSQ
        if (WebConfig.supportNsq){
            Digaku.engine.interNodeComm.registerHandlers(new MessagingInterNodeMessageHandler())
        }

        debug("setup digaku engine done.")
    }

    protected def shutdown() {
        debug("Unload all...")
        Digaku.shutdown()
    }
}

object MessagingMain extends ActiveDigakuEngine {

    val VERSION = "0.2.0" // autogenerated, don't edit this by hand

    def main(args: Array[String]) {

        println("Digaku Messaging Service " + VERSION)

        try {
            val configFile =
                if (args.length > 0){
                    args(0)
                }else{
                    throw NotExistsException("Usage: digaku-messaging [DIGAKU-CONFIG-FILE]")
                }

            setupDigakuEngine(configFile)

            Digaku.engine.eventStream.addListeners(ChatEventListener, AnalChatEventListener)

            println("Starting messaging service...")

            val nonZlibService = setupNonZlibService()
            val pushService = setupPushService()

            val handler: DigakuMessagingService = new DigakuMessagingService
            val processor = new MessagingService.Processor(handler)

            //            val protFactory: TBinaryProtocol.Factory = new TBinaryProtocol.Factory(true, true)
            val protFactory = new TCompactProtocol.Factory()
            var socket: TServerSocket = null
            //            val transportFactory = new TFramedTransport.Factory(2048)
            val transportFactory = new TZlibTransport.Factory()
            try {
                socket = new TServerSocket(5566)
            }
            catch {
                case e: TTransportException =>
                    e.printStackTrace()
            }
            val server: TThreadPoolServer = new TThreadPoolServer(new TThreadPoolServer.Args(socket)
                .processor(processor)
                .protocolFactory(protFactory)
                .transportFactory(transportFactory))

            System.out.println("messaging serving at :5566 ...")
            server.serve()

        }catch{
            case e:DigakuException =>
                println(e.getMessage)
        }
    }


    // @TODO(robin): remove this
    /**
      * Ini adalah solusi sementara untuk mengejar deadline target chat
      * karena di iOS belum ada solusi yg working untuk masalah Zlib transport,
      * sampai ketemu solusi untuk iOS maka fungsi ini akan digunakan untuk sementara.
      * @return
      */
    private def setupNonZlibService() = {

        val thread = new Thread(){
            override def run(): Unit = {

                val params = new TSSLTransportFactory.TSSLTransportParameters()
                val keystorePath = Digaku.engine.asInstanceOf[WebEngineWithConfigWithMessaging].fileConf[String]("security.keystore")
                val password = Digaku.engine.asInstanceOf[WebEngineWithConfigWithMessaging].fileConf[String]("security.password")
                params.setKeyStore(keystorePath, password)

                val protFactory = new TCompactProtocol.Factory()
                var socket: TServerSocket = null

                val handler: DigakuMessagingService = new DigakuMessagingService
                val processor = new MessagingService.Processor(handler)

                try {
//                    socket = new TServerSocket(5588)

                    socket = TSSLTransportFactory.getServerSocket(5588, 10000, new InetSocketAddress(0).getAddress, params)
//                    socket = TSSLTransportFactory.getServerSocket(5588, 10000, true, new InetSocketAddress(0).getAddress)


                }
                catch {
                    case e: TTransportException =>
                        e.printStackTrace()
                }
                val maxLength = 2048 * 20
                val server: TThreadPoolServer = new TThreadPoolServer(new TThreadPoolServer.Args(socket)
                    .processor(processor)
                    .protocolFactory(protFactory)
                    .transportFactory(new TFramedTransport.Factory(maxLength)))

                System.out.println("messaging serving (without zlib) at :5588 ...")
                server.serve()
            }
        }
        thread.start()
        thread
    }


    private def setupPushService() = {
        val thread = new Thread(){

            override def run(): Unit = {
                val transport = new TNonblockingServerSocket(5577)
                val handler = new DigakuPushMessage

                val processor = new PushMessage.AsyncProcessor(handler)
                //                val prot = new TBinaryProtocol.Factory()
                val prot = new TCompactProtocol.Factory()


                //                val args = new TNonblockingServer.Args(transport)
                //                    .processor(processor).protocolFactory(prot)
                //                    .transportFactory(new TFramedTransport.Factory(2048))

                //                val server:TServer = new TNonblockingServer(args)

                val args = new THsHaServer.Args(transport)
                    .processor(processor).protocolFactory(prot)
                    .transportFactory(new TFramedTransport.Factory(2048))

                val server:TServer = new THsHaServer(args)

                println("push service serving at :5577 ...")
                server.serve()
            }
        }
        thread.start()
        thread
    }



}
