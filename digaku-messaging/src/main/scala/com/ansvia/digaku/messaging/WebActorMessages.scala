/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.digaku.Types._
import com.ansvia.digaku.model
import com.ansvia.digaku.messaging.js.MessageDeliveryStatus
import com.ansvia.digaku.web.WebConfig
import com.ansvia.digaku.web.notification.{CustomImplementation, WebActorMessage}
import net.liftweb.json._
import JsonDSL._

/**
 * Author: robin (<EMAIL>)
 */


//case class ChatMessageIncoming(toUserId:IDType, chatMessage:Message)
//    extends WebActorMessage("chat-message-incoming") with CustomImplementation {
//
//    def toNsqMessage: String = {
//        compact(render(("initiator" -> WebConfig.NODE_ID) ~
//            ("id" -> chatMessage.getId) ~
//            ("cl" -> this.getClass.getCanonicalName) ~
//            ("fromUserId" -> chatMessage.getInitiatorId) ~
//            ("toUserId" -> toUserId) ~
//            ("chatId" -> chatMessage.getTargetChatId) ~
//            ("msgId" -> chatMessage.getId)))
//    }
//}
case class ChatUserTyping(forUserId:IDType, typingUserId:IDType, userName:String, chatId:Long)
    extends WebActorMessage("chat-user-typing") with CustomImplementation {

    def toNsqMessage: String = {
        compact(render(("initiator" -> WebConfig.NODE_ID) ~
            ("id" -> chatId) ~
            ("cl" -> this.getClass.getCanonicalName) ~
            ("forUserId" -> forUserId) ~
            ("typingUserId" -> typingUserId) ~
            ("userName" -> userName) ~
            ("chatId" -> chatId)))
    }
}


// (Optimize) Typing untuk group chat
case class GroupChatUserTyping(typingUserId:IDType, userName:String, chatId:Long)
    extends WebActorMessage("chat-user-typing") with CustomImplementation {
    def toNsqMessage: String = {
        compact(render(("initiator" -> WebConfig.NODE_ID) ~
            ("id" -> chatId) ~
            ("cl" -> this.getClass.getCanonicalName) ~
            ("typingUserId" -> typingUserId) ~
            ("userName" -> userName) ~
            ("chatId" -> chatId)))
    }
}

case class DeliveryStatusUpdate(chatId:Long, targetUserId:Long, statusUserId:Long, msgId:Long, status:MessageDeliveryStatus)
    extends WebActorMessage("chat-delivery-status-update") with CustomImplementation {

    def toNsqMessage: String = {
        compact(render(("initiator" -> WebConfig.NODE_ID) ~
            ("cl" -> this.getClass.getCanonicalName) ~
            ("chatId" -> chatId) ~
            ("targetUserId" -> targetUserId) ~
            ("statusUserId" -> targetUserId) ~
            ("msgId" -> msgId) ~
            ("status" -> status.getSimpleName)
        ))
    }
}

case class GroupDeliveryStatusUpdate(chatId:Long, targetUserId:Long, msgId:Long, status:MessageDeliveryStatus)
    extends WebActorMessage("chat-delivery-status-update") with CustomImplementation {

    def toNsqMessage: String = {
        compact(render(("initiator" -> WebConfig.NODE_ID) ~
            ("cl" -> this.getClass.getCanonicalName) ~
            ("chatId" -> chatId) ~
            ("targetUserId" -> targetUserId) ~
            ("msgId" -> msgId) ~
            ("status" -> status.getSimpleName)
        ))
    }
}
//case class ChatUserRemovedFromGroup(forUserId:Long, kickerUserId:Long, chatId:Long, targetUserId:Long, msgId:Long)
//    extends WebActorMessage("chat-user-removed-from-group") with CustomImplementation {
//
//    def toNsqMessage: String = {
//        compact(render(("initiator" -> WebConfig.NODE_ID) ~
//            ("cl" -> this.getClass.getCanonicalName) ~
//            ("chatId" -> chatId) ~
//            ("kickerUserId" -> kickerUserId) ~
//            ("targetUserId" -> targetUserId) ~
//            ("msgId" -> msgId) ~
//            ("forUserId" -> forUserId)
//        ))
//    }
//}
case class PublishChatMessage(forUserId:Long, initiatorUserId:Long, chatId:Long, msgId:Long)
    extends WebActorMessage("chat-user-removed-from-group") with CustomImplementation {

    def toNsqMessage: String = {
        compact(render(("initiator" -> WebConfig.NODE_ID) ~
            ("cl" -> this.getClass.getCanonicalName) ~
            ("chatId" -> chatId) ~
            ("initiatorUserId" -> initiatorUserId) ~
            ("msgId" -> msgId) ~
            ("forUserId" -> forUserId)
        ))
    }
}


// (Optimize) Untuk group chat perbedaan dengan PublishChatMessage agar pengiriman message tidak dilakukan per-user
case class GroupPublishChatMessage(initiatorUserId:Long, chatId:Long, msgId:Long)
    extends WebActorMessage("chat-user-removed-from-group") with CustomImplementation {

    def toNsqMessage: String = {
        compact(render(("initiator" -> WebConfig.NODE_ID) ~
            ("cl" -> this.getClass.getCanonicalName) ~
            ("chatId" -> chatId) ~
            ("initiatorUserId" -> initiatorUserId) ~
            ("msgId" -> msgId)
        ))
    }
}

case class PublishScChatMessage(forUserId:Long, scChatId:Long, msgId:Long) extends WebActorMessage("publish-sc-chat-message")
    with CustomImplementation {

    override def toNsqMessage: String = {
        compact(render(("initiator" -> WebConfig.NODE_ID) ~
            ("cl" -> this.getClass.getCanonicalName) ~
            ("chatId" -> scChatId) ~
            ("msgId" -> msgId) ~
            ("forUserId" -> forUserId)
        ))
    }
}

case class ScChatUserTyping(typingUserId:IDType, userName:String, chatId:Long)
    extends WebActorMessage("sc-chat-user-typing") with CustomImplementation {

    def toNsqMessage: String = {
        compact(render(("initiator" -> WebConfig.NODE_ID) ~
            ("id" -> chatId) ~
            ("cl" -> this.getClass.getCanonicalName) ~
            ("typingUserId" -> typingUserId) ~
            ("userName" -> userName) ~
            ("chatId" -> chatId)))
    }
}

case class ScChatDeliveryStatusUpdate(chatId:Long, targetUserId:Long, statusUserId:Long, msgId:Long, status:MessageDeliveryStatus)
    extends WebActorMessage("sc-chat-delivery-status-update") with CustomImplementation {

    def toNsqMessage: String = {
        compact(render(("initiator" -> WebConfig.NODE_ID) ~
            ("cl" -> this.getClass.getCanonicalName) ~
            ("chatId" -> chatId) ~
            ("targetUserId" -> targetUserId) ~
            ("statusUserId" -> targetUserId) ~
            ("msgId" -> msgId) ~
            ("status" -> status.getSimpleName)
        ))
    }
}

case class ScChatOnlineTime(isOnline:Boolean) extends WebActorMessage("sc-chat-online-time") with CustomImplementation {
    def toNsqMessage: String = {
        compact(render(("initiator" -> WebConfig.NODE_ID) ~
            ("cl" -> this.getClass.getCanonicalName) ~
            ("isOnline" -> isOnline)
        ))
    }
}

case class ScChatArchiveTopic(topicId:Long)  extends WebActorMessage("sc-chat-topic-archive") with CustomImplementation {
    def toNsqMessage: String = {
        compact(render(("initiator" -> WebConfig.NODE_ID) ~
            ("cl" -> this.getClass.getCanonicalName) ~
            ("topicId" -> topicId)
        ))
    }
}
