package com.ansvia.digaku.messaging.se

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.messaging.ScChat
import com.ansvia.digaku.se.MTSearchEngine
import com.ansvia.digaku.utils.AsLong
import com.ansvia.perf.PerfTiming
import ly.bit.nsq.Message


/**
 * Author: nadir (<EMAIL>)
 */

class MTSearchEngineWithChat(host:String, clusterName:String) extends MTSearchEngine(host, clusterName) with ScChatElasticSearchEngine with Slf4jLogger with PerfTiming {

    val distributedElasticSearchEngine = this

    override def toString = "MTSearchEngineWithChat(" + host + ")"

    override def send:PF = {
        case m:ScChat =>
            nsqProducer.putAsync("sc-chat:" + m.getId.toString)
        case m =>
            super.send(m)
    }

    override def receive: PF = {
        super.indexScChat orElse super.receive
    }

    override def handleMessage(msg: Message): Boolean = {
        try {
            val str = new String(msg.getBody)
            val arr = str.split(":")

            if (arr.length > 1) {
                arr(1) match {
                    case AsLong(id) =>
                        debug("got model id to index: " + str)

                        val scChat = ScChat.getById(id)

                        if (scChat.isDefined) {
                            try {
                                scChat.foreach(receive)
                            } catch {
                                case e: Exception =>
                                    error(e.toString)
                                    throw e
                            }
                        } else {
                            error("cannot index data via nsq, sc-chat is not exist for id " + id)
                        }

                    case x =>
                        warn(s"received data from nsq to index is not numeric: $x")
                }
            } else {
                super.handleMessage(msg)
            }
        } catch {
            case e:Exception =>
                error(e.getMessage)
                error(e.getStackTrace.map(_.toString).mkString("\n  "))
                throw e
        }

        true
    }

    override def indexScChat:PF = send
}
