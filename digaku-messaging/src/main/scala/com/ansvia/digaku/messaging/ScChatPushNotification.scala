package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.model.User
import com.ansvia.digaku.notifications.NonPersistentNotification
import com.ansvia.digaku.utils.RichString._
import com.ansvia.digaku.utils.UserSettings._

/**
 * Author: nadir (<EMAIL>)
 */

case class ScChatPushNotification(sccHistory:ScChatHistory, message: Message) extends NonPersistentNotification(104) {

    private val id = Digaku.engine.idFactory.createId().asInstanceOf[IDType]

    lazy val scChat = ScChat.getById(sccHistory.scChatId)

    override def getId: IDType = id

    override lazy val hash: String = message.toString.md5

    override def getDefaultReceivers: Seq[User] = sccHistory.userO.toSeq.filterNot(_.isAgentTopic)

    override def getCreationTime: IDType = message.getTimestamp

    override def isValid: Boolean = sccHistory.userO.isDefined && scChat.isDefined

    override def renderFor(user: User): String = ""
}
