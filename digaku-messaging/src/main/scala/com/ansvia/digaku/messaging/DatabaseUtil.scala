/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.database.{IdxHelper, TitanDatabase}
//import com.ansvia.digaku.exc.NotImplementedException
//import com.ansvia.graph.IdGraphTitanDbWrapper._
//import com.thinkaurelius.titan.core.Order._
//import com.thinkaurelius.titan.core.TitanGraph
//import com.thinkaurelius.titan.core.schema.TitanManagement
//import com.tinkerpop.blueprints.Direction._
//import com.tinkerpop.blueprints.util.wrappers.id.IdGraph

/**
 * Author: robin (<EMAIL>)
 */

object DatabaseUtil extends Slf4jLogger {

    def index(){
//        info("indexing messaging specific edges...")
//
//        import Label._
//
//        val mgmt: TitanManagement = Digaku.engine.database match {
//            case titan:TitanDatabase =>
//                titan.getGraph match {
//                    case tit:TitanGraph => tit.getManagementSystem
//                    case id:IdGraph[TitanGraph] => id.getBaseGraph.getManagementSystem
//                    case x =>
//                        throw NotImplementedException("unsupported graph db backend: " + x)
//                }
//            case x =>
//                throw NotImplementedException("unsupported db backend: " + x)
//        }
//
//        val idh = new IdxHelper(mgmt)
//
//        val sourceIdKey = idh.getOrCreatePropKey("sourceId", classOf[java.lang.Long])
//        val targetIdKey = idh.getOrCreatePropKey("targetId", classOf[java.lang.Long])
//        val targetNameKey = idh.getOrCreatePropKey("targetName", classOf[java.lang.String])
//        val timeOrderKey = idh.getOrCreatePropKey("timeOrder", classOf[java.lang.Long])
//
//        {
//            val lbl = idh.ifEdgeLabelNotExists(CONTACT){ name =>
//                mgmt.makeEdgeLabel(CONTACT)
//                    .signature(sourceIdKey, targetIdKey)
//                    .sortKey(targetNameKey)
//                    .sortOrder(DESC)
//                    .make()
//            }
//            idh.ifRelationIdxNotExists(CONTACT, "CONTACTByName"){ name =>
//                mgmt.buildEdgeIndex(lbl, name, BOTH, ASC, targetNameKey)
//            }
//        }
//        {
//            val lbl = idh.ifEdgeLabelNotExists(INITIATOR){ name =>
//                mgmt.makeEdgeLabel(INITIATOR)
//                    .sortKey(timeOrderKey)
//                    .sortOrder(DESC)
//                    .make()
//            }
//            idh.ifRelationIdxNotExists(INITIATOR, "INITIATORByTimeOrder"){ name =>
//                mgmt.buildEdgeIndex(lbl, name, BOTH, DESC, timeOrderKey)
//            }
//        }
//        {
//            val lbl = idh.ifEdgeLabelNotExists(PARTICIPANT){ name =>
//                mgmt.makeEdgeLabel(name)
////                    .signature(timeOrderKey)
//                    .signature(sourceIdKey, targetIdKey)
//                    .sortKey(timeOrderKey)
//                    .sortOrder(DESC)
//                    .make()
//            }
//            idh.ifRelationIdxNotExists(PARTICIPANT, "PARTICIPANTByTimeOrder"){ name =>
//                mgmt.buildEdgeIndex(lbl, name, OUT, DESC, timeOrderKey)
//            }
//        }
//        mgmt.commit()
    }

}
