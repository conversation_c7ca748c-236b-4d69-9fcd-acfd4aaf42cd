/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.event.impl.{AddTopicAgentEvent, ArchiveTopicEvent, StartupEvent}
import com.ansvia.digaku.event.{AsyncEventStreamListener, StreamEvent}
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.lib.WebEngineWithConfig
import com.ansvia.digaku.messaging.js.{MessageDeliveryStatus, MessageEventKind, ScTopicJs}
import com.ansvia.digaku.model.{Topic, User}
import com.ansvia.digaku.utils.AsLong
import com.ansvia.digaku.web.event.{LocationViewEvent, OnlineEvent}
import com.ansvia.digaku.web.notification.NotificationNsqBuilder
import net.liftweb.common.Full
import net.liftweb.http.ParsePath
import net.liftweb.json.DefaultFormats
import net.liftweb.json.JsonAST._
import net.liftweb.json.Serialization.write
import com.ansvia.digaku.utils.UserSettings._
import com.ansvia.digaku.helpers.TypeHelpers._
import ScChatTopic._
import com.ansvia.digaku.database.GraphCompat._


/**
 * Author: robin (<EMAIL>)
 */

case class TypingEvent(chat:Chat, user:User, text:String) extends StreamEvent("typing-event")
case class ChatMessageSentEvent(msg:Message, ch:ChatHistory) extends StreamEvent("message-sent-event")

// (Optimize) Sent Event yang di emit setelah selesai pengiriman message
// perbedaan dengan ChatMessageSentEvent agar pengiriman message tidak dilakukan per-user
case class GroupChatMessageSentEvent(msg:Message, ch:Chat) extends StreamEvent("group-message-sent-event")
case class CreateMessageEvent(msg:Message) extends StreamEvent("create-message-event")
case class DeliveryStatusUpdateEvent(statusUser:User, chat:Chat, messageIds:List[Long], status:MessageDeliveryStatus) extends StreamEvent("message-deliver-status-update-event")
case class RemoveParticipantEvent(kicker:User, chat:Chat, victim:User) extends StreamEvent("chat-remove-participant")
case class ChatInviteUserEvent(invitor:User, targetUser:User, chat:Chat) extends StreamEvent("chat-invite-user-to-group")
case class ChatLeaveGroupEvent(user:User, chat:Chat) extends StreamEvent("chat-leave-group")

// SCCHAT
case class ScChatMessageSentEvent(msg:Message, scChatHistory: ScChatHistory) extends StreamEvent("sc-message-sent-event")
case class ScChatTypingEvent(scChat: ScChat, user: User, text:String) extends StreamEvent("sc-typing-event")
case class ScChatDeliveryStatusUpdateEvent(statusUser:User, scChat:ScChat, messageIds:List[Long], status:MessageDeliveryStatus) extends
    StreamEvent("sc-message-deliver-status-update-event")
case class ScChatOnlineTimeEvent(isOnline:Boolean) extends StreamEvent("sc-online-time-event")

object ChatEventListener extends AsyncEventStreamListener with Slf4jLogger {
    override val actorName: String = "chat-esl"

    private val events = Seq("typing-event",
        "message-sent-event",
        "message-deliver-status-update-event",
        "group-message-sent-event",
//        "online-event",
//        "user-logged-in",
        "startup",
        "location-view",
        "chat-invite-user-to-group",
        "chat-remove-participant",
        "chat-leave-group",
        "sc-typing-event",
        "sc-message-sent-event",
        "sc-message-deliver-status-update-event",
        "sc-online-time-event",
        "online-event",
        "add-topic-agent",
        "archive-topic")

    /**
     * Check is event handled by this listener.
     * WARNING: don't use `transact` inside this method
     * to avoid dead-lock.
     * @param eventName name of event.
     * @return
     */
    override def isEventHandled(eventName: String): Boolean = events.contains(eventName)

    private lazy val webEngineWithConfig = Digaku.engine.asInstanceOf[WebEngineWithConfig]
    private lazy val notifGateway = webEngineWithConfig.notifGateway

    private val oneText = "1"
    private val FIVE_MINUTES = 300

    implicit val f = DefaultFormats


    override def asyncDispatch: PartialFunction[StreamEvent, Unit] = {
        case TypingEvent(chat, user, text) =>

//            debug("typing participant: " + chat.getActiveParticipants.map(_.getName).mkString(", "))

            val participantIds = chat.getActiveParticipantIds

            participantIds.foreach { partId =>
                // hanya untuk online user saja
                if (ChatSessionStore.onlineUsers.get(partId.toString, "") == oneText){
                    ChatSessionStore.get(partId)
                        .insert("|typing|:" + chat.getId + ":" + user.getId + ":" + ChatType.SCCHAT + ":" + user.getName, FIVE_MINUTES)
                }

                // ini gak perlu ada di scope apakah user online atau tidak
                // karena ini digunakan di layer web yang mana sudah otomatis dihandle oleh Lift comet server
                if (user.getId != partId && chat.kind != ChatKind.Group){
                    notifGateway ! ChatUserTyping(partId, user.getId, user.getName, chat.getId)
                }
            }

            // Group chat
            if (chat.kind == ChatKind.Group) {
                notifGateway ! GroupChatUserTyping(user.getId, user.getName, chat.getId)
            }

        case ChatMessageSentEvent(msg, ch) =>
            User.getById(ch.userId) match {
                case Some(user) =>

                    // chat notif count ini sifatnya volatile beda dengan
                    // unread chat count, chat notif count ini begitu di-klik langsung hilang
                    if (msg.getInitiatorId != user.getId){
                        user.getCounter.increment("chat_notif_count")
                    }

                    // hanya di-insert apabila user online saja
                    if (ChatSessionStore.onlineUsers.get(user.getId.toString, "") == oneText){
                        ChatSessionStore.get(user).insert("|msg|:" + msg.toJsonString, FIVE_MINUTES)
                    }

                    // ini gak perlu ada di scope apakah user online atau tidak
                    // karena ini digunakan di layer web yang mana sudah otomatis dihandle oleh Lift comet server
                    msg.getTargetChat match {
                        case Some(chat) =>
                            if (user.getId != msg.getInitiatorId){

                                debug(s"publish chat message for user: ${user.getName}, initiator: ${msg.getInitiator.map(_.getName)}, chat: ${chat}, msg: ${msg}")

                                notifGateway ! PublishChatMessage(user.getId, msg.getInitiatorId, chat.getId, msg.getId)
                            }
                        case _ =>
                            error("no chat target for message " + msg)
                    }

                case _ =>
                    warn("no user with id: " + ch.userId)
            }

        case GroupChatMessageSentEvent(msg, chat) =>
            for (participant <- chat.getActiveParticipants){
                // gak perlu dikirim ke dirinya sendiri
                // chat notif count ini sifatnya volatile beda dengan
                // unread chat count, chat notif count ini begitu di-klik langsung hilang
                if (msg.getInitiatorId != participant.getId){
                    participant.getCounter.increment("chat_notif_count")
                }

                // hanya di-insert apabila user online saja
                if (ChatSessionStore.onlineUsers.get(participant.getId.toString, "") == oneText){
                    ChatSessionStore.get(participant).insert("|msg|:" + msg.toJsonString, FIVE_MINUTES)
                }
            }

            notifGateway ! GroupPublishChatMessage(msg.getInitiatorId, chat.getId, msg.getId)

        case DeliveryStatusUpdateEvent(statusUser, chat, messageIds, status) =>

            // (Optimasi) Ketika status sent langsung dikirim ke initiatornya dan cukup dikirim sekali
            // karena status Sent di eksekusi setelah semua message dikirim
            if (status == MessageDeliveryStatus.Sent) {
                messageIds.foreach { id =>
                    chat.getMessageById(id).foreach { m =>
                        val initiator = m.getInitiatorId

                        if (ChatSessionStore.onlineUsers.get(initiator.toString, "") == oneText){

                            ChatSessionStore.get(initiator).insert("|msg-status|:%s:%s:%s:%d".format(chat.getId, ChatType.CHAT,
                                messageIds.map(_.toString).mkString(","), status.getCode), FIVE_MINUTES)

                        }

                        notifGateway ! DeliveryStatusUpdate(chat.getId, initiator, initiator, id, status)
                    }
                }
            } else {
                for (participant <- chat.getActiveParticipants){
                    // gak perlu dikirim ke dirinya sendiri
                    if (participant.getId != statusUser.getId){
                        if (ChatSessionStore.onlineUsers.get(participant.getId.toString, "") == oneText){

                            ChatSessionStore.get(participant).insert("|msg-status|:%s:%s:%d".format(chat.getId,
                                messageIds.map(_.toString).mkString(","), status.getCode), FIVE_MINUTES)

                        }

                        // ini gak perlu ada di scope apakah user online atau tidak
                        // karena ini digunakan di layer web yang mana sudah otomatis dihandle oleh Lift comet server
                        messageIds.foreach { id =>
                            chat.getMessageById(id).find(_.getInitiatorId == participant.getId).foreach { _ =>
                                notifGateway ! DeliveryStatusUpdate(chat.getId, participant.getId, statusUser.getId, id, status)
                            }
                        }
                    }
                }
            }

        case RemoveParticipantEvent(kicker, chat, victim) =>

            val metaData = Map("targetUserId" -> victim.getId.toString,
                "eventKind" -> MessageEventKind.REMOVE_PARTICIPANT.toString)

            val kickMessage = "%s menghapus %s dari dalam grup.".format(kicker.getName, victim.getName)
            val msg = chat.publishEvent(kicker, kickMessage, includeInactiveHistory = true, noEmitEvent = true, metaData)

            val participants = chat.getActiveParticipants.:+(victim).distinct
            for (participant <- participants){

                // hanya di-insert apabila user online saja
                if (ChatSessionStore.onlineUsers.get(participant.getId.toString, "") == oneText){
                    ChatSessionStore.get(participant).insert("|msg|:" + msg.toJsonString, FIVE_MINUTES)
                }

                // ini gak perlu ada di scope apakah user online atau tidak
                // karena ini digunakan di layer web yang mana sudah otomatis dihandle oleh Lift comet server
                if (participant.getId != victim.getId) {
                    notifGateway ! PublishChatMessage(participant.getId, kicker.getId, chat.getId, msg.getId)
                }
            }

            // khusus buat target user perlu di-deactivate chat-nya
            val msgCloned = msg.copy(id = chat.genMessageId)
            msgCloned.metaData = msgCloned.metaData ++ Map("activateChat" -> "false")
            chat.updateMessage(msgCloned)
            notifGateway ! PublishChatMessage(victim.getId, kicker.getId, chat.getId, msgCloned.getId)

        case ChatInviteUserEvent(invitor, targetUser, chat) =>

            val metaData = Map("targetUserId" -> targetUser.getId.toString,
                "eventKind" -> MessageEventKind.ADD_PARTICIPANT.toString)

            val msg = chat.publishEvent(invitor, "%s menambahkan %s ke dalam grup.".format(invitor.getName, targetUser.getName),
                includeInactiveHistory = false, noEmitEvent = true, metaData)

            val participants = chat.getActiveParticipants.:+(targetUser).distinct
            for (participant <- participants){
//                println(s" dispatch to---> $participant")

                // hanya di-insert apabila user online saja
                if (ChatSessionStore.onlineUsers.get(participant.getId.toString, "") == oneText){
                    ChatSessionStore.get(participant).insert("|msg|:" + msg.toJsonString, FIVE_MINUTES)
                }

                // ini gak perlu ada di scope apakah user online atau tidak
                // karena ini digunakan di layer web yang mana sudah otomatis dihandle oleh Lift comet server
                if (participant.getId != targetUser.getId) {
                    notifGateway ! PublishChatMessage(participant.getId, invitor.getId, chat.getId, msg.getId)
                }
            }


            // khusus buat target user perlu di-activate chat-nya
            val msgCloned = msg.copy(id = chat.genMessageId)
            msgCloned.metaData = msgCloned.metaData ++ Map("activateChat" -> "true")
            chat.updateMessage(msgCloned)
            notifGateway ! PublishChatMessage(targetUser.getId, invitor.getId, chat.getId, msgCloned.getId)


        case ChatLeaveGroupEvent(user, chat) =>

            val metaData = Map("targetUserId" -> user.getId.toString,
                "eventKind" -> MessageEventKind.REMOVE_PARTICIPANT.toString)

            val msg = chat.publishEvent(user, "%s left this group".format(user.getName), includeInactiveHistory = false,
                noEmitEvent = true, metaData)

            val participants = chat.getActiveParticipants
            for (participant <- participants){

                // hanya di-insert apabila user online saja
                if (ChatSessionStore.onlineUsers.get(participant.getId.toString, "") == oneText){
                    ChatSessionStore.get(participant).insert("|msg|:" + msg.toJsonString, FIVE_MINUTES)
                }

                // ini gak perlu ada di scope apakah user online atau tidak
                // karena ini digunakan di layer web yang mana sudah otomatis dihandle oleh Lift comet server
                notifGateway ! PublishChatMessage(participant.getId, user.getId, chat.getId, msg.getId)
            }


        case StartupEvent() =>
            setup


//        case OnlineEvent(user) =>
            // untuk online user yang dari web jangan diset di chat store
            // agar bisa membedakan apakah user online di chat app-nya saja
            // atau di web
//            if (ChatSessionStore.onlineUsers.get(user.getId.toString, "") != oneText){
//                ChatSessionStore.onlineUsers.set(user.getId.toString, oneText, Some(1800))
//            }


//        case UserLoggedInEvent(user) =>
//            if (ChatSessionStore.onlineUsers.get(user.getId.toString, "") != oneText){
//                ChatSessionStore.onlineUsers.set(user.getId.toString, oneText, Some(1800))
//            }

        case LocationViewEvent(user, parsePath, Full(req)) =>
            parsePath match {
                case ParsePath("messages/single-view" :: _, _, _, _) =>
                    req.params("chatId") match {
                        case AsLong(chatId) :: _ =>
                            Chat.getById(chatId) match {
                                case Some(chat) if chat.getUnreadMessageCount(user) > 0 =>
                                    val marked = chat.markAllMessagesRead(user)
                                    debug("auto mark messages as read for user `%s` in chat `%s`. marked: %d".format(user.getName,
                                        chat.getTitleFor(user), marked))
                                case _ =>
                            }
                        case _ =>
                    }
//                    val chats = user.getChatGremlinPipe
//                        .iterator().flatMap(_.toCC[Chat])
//                    for (chat <- chats){
//                        chat.markAllMessagesRead(user)
//                    }
                case _ =>
            }

        // UNTUK SCCHAT
        case ScChatMessageSentEvent(msg, sccHistory) =>
            User.getById(sccHistory.userId) match {
                case Some(user) =>
                    if (msg.getInitiatorId != user.getId || msg.isInstanceOf[MessageEvent]) {
                        user.getCounter.increment("sc_chat_notif_count")
                    }

                    if (ChatSessionStore.onlineUsers.get(user.getId.toString, "") == oneText) {
                        ChatSessionStore.get(user).insert("|msg|:" + msg.toJsonString, FIVE_MINUTES)
                    }

                    notifGateway ! PublishScChatMessage(user.getId, sccHistory.scChatId, msg.getId)

                case _ =>
                    warn("no user with id: " + sccHistory.userId)
            }

        case ScChatTypingEvent(scChat, user, text) =>
            Seq(scChat.getAgentId, scChat.getInitiatorId).foreach { partId =>
                if (ChatSessionStore.onlineUsers.get(partId.toString, "") == oneText){
                    ChatSessionStore.get(partId)
                        .insert("|typing|:" + scChat.getId + ":" + user.getId + ":" + ChatType.SCCHAT + ":" + user.getName, FIVE_MINUTES)
                }
            }

            notifGateway ! ScChatUserTyping(user.getId, user.getName, scChat.getId)

        case ScChatDeliveryStatusUpdateEvent(statusUser, scChat, messageIds, status) =>
            messageIds.foreach { mId =>
                scChat.getMessageById(mId).foreach { msg =>
                    val initiator = msg.getInitiatorId
                    if (ChatSessionStore.onlineUsers.get(initiator.toString, "") == oneText){
                        ChatSessionStore.get(initiator).insert("|msg-status|:%s:%s:%s:%d".format(scChat.getId, ChatType.SCCHAT,
                            messageIds.map(_.toString).mkString(","), status.getCode), FIVE_MINUTES)
                    }
                    scChat.getParticipantIds.filter(_ == initiator).foreach { targetUser =>
                        notifGateway ! ScChatDeliveryStatusUpdate(scChat.getId, targetUser, statusUser.getId, msg.getId, status)
                    }
                }
            }

        case ScChatOnlineTimeEvent(isOnline) =>
            notifGateway ! ScChatOnlineTime(isOnline)

        case OnlineEvent(user) =>
            if (user.isAgentTopic) {
                debug("Distribute ScChat queue for online user : " + user)
                user.getTopicList.foreach { topic =>
                    topic.distributeQueueScChatFor(user)
                }
            }

        case AddTopicAgentEvent(topic, user) =>
            topic.distributeQueueScChatFor(user)

        case ArchiveTopicEvent(topicId) =>
            try {
                tx { t =>
                    val tp = getFromTx[Topic](topicId, t)
                    val tpJs = write(ScTopicJs(tp.getId, tp.name, tp.description, tp.archived, tp.archivedReason,
                        tp.archivedMessage, tp.archivedTime))

                    ChatSessionStore.onlineUsers.getAll.foreach { u =>
                        if (u._2 == "1") {
                            ChatSessionStore.get(u._1.toLongOr(0L)).insert("|archive-topic|:" + tpJs, FIVE_MINUTES)
                        }
                    }
                }
            } catch {
                case e:Exception =>
                    e.printStackTrace()
            }

            notifGateway ! ScChatArchiveTopic(topicId)

    }

    // agar web actor converter bisa memahami WebActorMessage kita
    lazy val setup = {
        info("setup custom web actor converter...")
        NotificationNsqBuilder.customWebActorConverter = NotificationNsqBuilder.customWebActorConverter orElse {
//            case json:JValue if json \ "cl" == JString("com.ansvia.digaku.messaging.ChatMessageIncoming") =>
//
//                val toUser = json \ "toUserId" match {
//                    case JInt(_id) => User.getById(_id.toLong).getOrElse {
//                        throw NotExistsException("no user with id " + _id.toLong)
//                    }
//                    case x => throw new DigakuException("unexpected toUserId: " + x)
//                }
//                val msgId = json \ "msgId" match {
//                    case JInt(_id) => _id.toLong
//                    case x => throw new DigakuException("unexpected msgId: " + x)
//                }
//
//                val chatMessage = json \ "chatId" match {
//                    case JInt(_id) =>
//                        val chat = Chat.getById(_id.toLong).getOrElse {
//                            throw NotExistsException("no chat with id " + _id.toLong)
//                        }
//                        chat.getChatHistoryFor(toUser).map { ch =>
//                            ch.getMessage(msgId).getOrElse {
//                                throw NotExistsException("no message with id " + msgId)
//                            }
//                        }.getOrElse {
//                            throw NotExistsException("cannot get message with id " + msgId +
//                                " from chat history " + toUser.getName)
//                        }
//                    case x => throw new DigakuException("unexpected toUserId: " + x)
//                }
//
//                Some(ChatMessageIncoming(toUser.getId, chatMessage))

            case json:JValue if json \ "cl" == JString("com.ansvia.digaku.messaging.ChatUserTyping") =>

                val forUserId = json \ "forUserId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected forUserId: " + x)
                }
                val typingUserId = json \ "typingUserId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected typingUserId: " + x)
                }
                val userName = json \ "userName" match {
                    case JString(_userName) => _userName
                    case x => throw new DigakuException("unexpected userName: " + x)
                }

                val chatId = json \ "chatId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected chatId: " + x)
                }

                Some(ChatUserTyping(forUserId, typingUserId, userName, chatId))

            case json:JValue if json \ "cl" == JString("com.ansvia.digaku.messaging.GroupChatUserTyping") =>
                Some(json.extract[GroupChatUserTyping])

            case json:JValue if json \ "cl" == JString("com.ansvia.digaku.messaging.GroupPublishChatMessage") =>
                Some(json.extract[GroupPublishChatMessage])

            case json:JValue if json \ "cl" == JString("com.ansvia.digaku.messaging.DeliveryStatusUpdate") =>

                val targetUserId = json \ "targetUserId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected targetUserId: " + x)
                }

                val chatId = json \ "chatId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected chatId: " + x)
                }

                val statusUserId = json \ "statusUserId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected statusUserId: " + x)
                }

                val msgId:Long = json \ "msgId" match {
                    case JInt(_id) => _id.longValue()
                    case _ => 0L
                }

                val status = json \ "status" match {
                    case JString(simpleStatus) => MessageDeliveryStatus.fromStr(simpleStatus)
                    case x => throw new DigakuException("unexpected status: " + x)
                }

                Some(DeliveryStatusUpdate(chatId, targetUserId, statusUserId, msgId, status))

//            case json:JValue if json \ "cl" == JString("com.ansvia.digaku.messaging.ChatUserRemovedFromGroup") =>
//
//                val forUserId = json \ "forUserId" match {
//                    case JInt(_id) => _id.longValue()
//                    case x => throw new DigakuException("unexpected forUserId: " + x)
//                }
//                val kickerUserId = json \ "kickerUserId" match {
//                    case JInt(_id) => _id.longValue()
//                    case x => throw new DigakuException("unexpected kickerUserId: " + x)
//                }
//                val targetUserId = json \ "targetUserId" match {
//                    case JInt(_id) => _id.longValue()
//                    case x => throw new DigakuException("unexpected targetUserId: " + x)
//                }
//
//                val chatId = json \ "chatId" match {
//                    case JInt(_id) => _id.longValue()
//                    case x => throw new DigakuException("unexpected chatId: " + x)
//                }
//
//                val msgId = json \ "msgId" match {
//                    case JInt(_id) => _id.longValue()
//                    case x => throw new DigakuException("unexpected msgId: " + x)
//                }
//
////                val msgText = json \ "msgText" match {
////                    case JString(_str) => _str
////                    case x => throw new DigakuException("unexpected msgText: " + x)
////                }
//
//                Some(ChatUserRemovedFromGroup(forUserId, kickerUserId, chatId, targetUserId, msgId))

             case json:JValue if json \ "cl" == JString("com.ansvia.digaku.messaging.PublishChatMessage") =>

                val forUserId = json \ "forUserId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected forUserId: " + x)
                }
                val initiatorUserId = json \ "initiatorUserId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected initiatorUserId: " + x)
                }

                val chatId = json \ "chatId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected chatId: " + x)
                }

                val msgId = json \ "msgId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected msgId: " + x)
                }

//                val msgText = json \ "msgText" match {
//                    case JString(_str) => _str
//                    case x => throw new DigakuException("unexpected msgText: " + x)
//                }

                Some(PublishChatMessage(forUserId, initiatorUserId, chatId, msgId))


            case json:JValue if json \ "cl" == JString("com.ansvia.digaku.messaging.ScChatUserTyping") =>
                val typingUserId = json \ "typingUserId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected typingUserId: " + x)
                }
                val userName = json \ "userName" match {
                    case JString(_userName) => _userName
                    case x => throw new DigakuException("unexpected userName: " + x)
                }

                val chatId = json \ "chatId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected chatId: " + x)
                }

                Some(ScChatUserTyping(typingUserId, userName, chatId))

            case json:JValue if json \ "cl" == JString("com.ansvia.digaku.messaging.PublishScChatMessage") =>

                val forUserId = json \ "forUserId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected forUserId: " + x)
                }

                val chatId = json \ "chatId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected chatId: " + x)
                }

                val msgId = json \ "msgId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected msgId: " + x)
                }

                Some(PublishScChatMessage(forUserId, chatId, msgId))

            case json:JValue if json \ "cl" == JString("com.ansvia.digaku.messaging.ScChatDeliveryStatusUpdate") =>

                val targetUserId = json \ "targetUserId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected targetUserId: " + x)
                }

                val chatId = json \ "chatId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected chatId: " + x)
                }

                val statusUserId = json \ "statusUserId" match {
                    case JInt(_id) => _id.longValue()
                    case x => throw new DigakuException("unexpected statusUserId: " + x)
                }

                val msgId:Long = json \ "msgId" match {
                    case JInt(_id) => _id.longValue()
                    case _ => 0L
                }

                val status = json \ "status" match {
                    case JString(simpleStatus) => MessageDeliveryStatus.fromStr(simpleStatus)
                    case x => throw new DigakuException("unexpected status: " + x)
                }

                Some(ScChatDeliveryStatusUpdate(chatId, targetUserId, statusUserId, msgId, status))

            case json:JValue if json \ "cl" == JString("com.ansvia.digaku.messaging.ScChatOnlineTime") =>

                val isOnline = json \ "isOnline" match {
                    case JBool(_isOnline) => _isOnline
                    case x => throw new DigakuException("unexpected targetUserId: " + x)
                }

                Some(ScChatOnlineTime(isOnline))

            case json:JValue if json \ "cl" == JString("com.ansvia.digaku.messaging.ScChatArchiveTopic") =>

                val topicId = json \ "topicId" match {
                    case JInt(_topicId) => _topicId.longValue()
                    case x => throw new DigakuException("unexpected topicId: " + x)
                }

                Some(ScChatArchiveTopic(topicId))

        }
    }



}
