package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.messaging.live.OnlineTimeReminder
import com.ansvia.digaku.web.WebConfig
import org.joda.time.{DateTime, DateTimeZone}
import scala.concurrent.duration._

/**
 * Author: nadir (<EMAIL>)
 */

object ScChatSettings {
    val configKvStore = Digaku.engine.kvStoreProvider.build("sc-chat-settings")

    private val startOnlineTimeKey = "start-online-time"
    private val endOnlineTimeKey = "end-online-time"
    private val timezoneKey = "timezone"

    // Waktu untuk agent timeout
    lazy val agentTimeout = 15.minutes.toMillis

    // Waktu untuk customer timeout
    lazy val initiatorTimeout = 15.minutes.toMillis

    // Waktu agent di-suspend ketika timeout
    lazy val agentTimeoutSuspend = 15.minutes.toMillis

    // Maximal ScChat yang di-handle oleh masing-masing agent
    lazy val maxScChatPerAgent = 3

    // Check config apakah support dengan online time reminder live. true ketika support
    lazy val supportOnlineTimeReminder = WebConfig.conf("messaging.online-time-reminder", false)
    // Check apakah support auto timeout ScChat, true ketika support
    lazy val supportAutoTimeoutChat = WebConfig.conf("messaging.auto-timeout", false)

    def timeSpliter(millis: Long): (Int, Int, Int) = {
        val totalSeconds = millis / 1000

        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60

        (hours.toInt, minutes.toInt, seconds.toInt)
    }

    /**
     * Ubah waktu online untuk SC Chat
     * @param startTimeMillis time millis untuk hh:mm:ss
     * @param endTimeMillis time millis untuk hh:mm:ss
     * @param timeZone see http://joda-time.sourceforge.net/timezones.html
     */
    def setOnlineTime(startTimeMillis:Long, endTimeMillis:Long, timeZone:String): Unit ={
        configKvStore.set(startOnlineTimeKey, startTimeMillis)
        configKvStore.set(endOnlineTimeKey, endTimeMillis)
        configKvStore.set(timezoneKey, timeZone)

        Digaku.engine.interNodeComm.broadcast("reset-online-time")
    }

    def getStartOnlineTime:Long = {
        configKvStore.get(startOnlineTimeKey, 18000000L) // default jam 8:00:00
    }

    def getEndOnlineTime:Long = {
        configKvStore.get(endOnlineTimeKey, 61200000L) // default jam 17:00:00
    }

    def getTimezoneOnlineTime:String = {
        configKvStore.get(timezoneKey, "Asia/Jakarta") // default asia/jakarta
    }

    def isOnlineTime:Boolean = {
        val now = new DateTime().withZone(DateTimeZone.forID(getTimezoneOnlineTime))

        val (startHours, startMinutes, startSeconds) = timeSpliter(getStartOnlineTime)
        val (endHours, endMinutes, endSeconds) = timeSpliter(getEndOnlineTime)

        val startDate = now.withTime(startHours, startMinutes, startSeconds, 0)
        val endDate = now.withTime(endHours, endMinutes, endSeconds, 0)

        now.isAfter(startDate.minusSeconds(1)) && now.isBefore(endDate.plusSeconds(1))
    }

}
