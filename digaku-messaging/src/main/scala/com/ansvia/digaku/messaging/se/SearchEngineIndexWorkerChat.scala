package com.ansvia.digaku.messaging.se

import akka.actor.SupervisorStrategy.{Restart, Resume}
import akka.actor.{OneForOneStrategy, Actor}
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.event.impl.IndexEvent
import com.ansvia.digaku.exc.IgnoredException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.messaging.ScChat
import com.ansvia.digaku.se.DistributedElasticSearchEngine
import com.ansvia.perf.PerfTiming
import scala.concurrent.{Future, ExecutionContext}

/**
 * Author: nadir (<EMAIL>)
 */

object SearchEngineIndexWorkerChat extends DbAccess with Slf4jLogger with PerfTiming {

    implicit val ec:ExecutionContext = Digaku.engine.actorSystem.dispatcher
    sealed case class ChatSetSearchEngine(se:DistributedElasticSearchEngine)
    sealed case class ChatIndexGetStatus()
    sealed case class StartChatIndex()

    sealed case class ChatIndexStatus(idx:Long, of:Long, current:String){
        var lastIndexed = "-"
        var inProcessing = false
    }

    private var workIndex:Long = 0L
    private var total:Long = 0
    private var currentWork = ""

    var processing:Option[Long] = None

    @volatile
    private var _working = false

    def start(_se:DistributedElasticSearchEngine): Unit = {
        if (!_working) {
            total = ScChat.scChatRegistry.getCount.longValue()

            debug("will indexing at total " + total + " vertices")

            _working = true

            workIndex = 0L

            Future {
                println("Start indexing ----------------------------------------------")

                try {

                    debug("Start indexing ScChat")

                    ScChat.scChatRegistry.paging(None, None) {
                        case (key, value) =>
                            processing = Some(key)

                            val scChat = ScChat.fromJsonString(value)

                            currentWork = scChat.toString

                            _se match {
                                case se:ScChatElasticSearchEngine =>
                                    se.indexScChat(scChat)
                            }

                            workIndex = workIndex + 1

                            if (workIndex % 100 == 0 || workIndex == total) {
                                Digaku.engine.eventStream.emit(IndexEvent(workIndex, total, currentWork, (workIndex.toDouble * 100D) / total.toDouble, "sc-chat"))
                            }

                            true
                    }
                } catch {
                    case e:IgnoredException =>
                        debug("ignored: " + e.getMessage)

                    case e:Exception =>
                        error("%s".format(e))
                        error("error \"" + e.getMessage + "\" in/after processing: " + processing)
                        error(e.getStackTraceString)
                        warn("staled indexing.")
                } finally {
                    _working = false
                }

                debug("%d sc-chat object indexed".format(workIndex))
            }
        } else {
            warn("batch indexing already running.")
        }
    }

}


class SearchEngineIndexWorkerChat extends Actor with DbAccess with Slf4jLogger {
    import SearchEngineIndexWorkerChat._
    import scala.concurrent.duration._

    override val supervisorStrategy =
        OneForOneStrategy(maxNrOfRetries = 15, withinTimeRange = 30.seconds) {
            case _: ArithmeticException      => Resume
            case _: NullPointerException     => Restart
            case _: IllegalArgumentException => akka.actor.SupervisorStrategy.Stop
            case _: Exception                => Restart
        }

    private var _se:DistributedElasticSearchEngine = null

    def receive = {
        case ChatSetSearchEngine(se) =>
            _se = se

        case StartChatIndex() =>
            debug("start batch indexing...")
            try {
                start(_se) // Nil assume all
            }catch{
                case e:Exception =>
                    error(e.getMessage)
                    error(e.getStackTraceString)
            }

        case ChatIndexGetStatus() =>
            val status = ChatIndexStatus(workIndex, total, currentWork)

            status.inProcessing = _working

            info("indexing [%d of %d] %s".format(workIndex, total, currentWork))

            sender ! status
    }



}
