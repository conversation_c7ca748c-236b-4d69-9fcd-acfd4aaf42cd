/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import java.util.concurrent.TimeUnit

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types.IDType
import com.ansvia.digaku.exc.{InvalidParameterException, AlreadyExistsException, DigakuException, LimitationReachedException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.messaging.ImplicitChatUser._
import com.ansvia.digaku.messaging.js.MessageDeliveryStatus
import com.ansvia.digaku.model.User
import com.ansvia.digaku.persistence.{CounterProviderOperator, MapDb, SeqStore, SnowFlakeIdFactory}
import com.ansvia.digaku.util.ContentNormalizer
import com.ansvia.digaku.utils.AsLong
import com.ansvia.digaku.web.{DigakuWebCore, WebConfig}
import net.liftweb.json.JsonDSL._
import net.liftweb.json.{JValue, _}
import org.joda.time.DateTime
import com.ansvia.digaku.utils.RichString._
import org.streum.configrity.Configuration
import com.ansvia.digaku.database.GraphCompat._

import scala.collection.mutable.ListBuffer

/**
 * Author: robin (<EMAIL>)
 */

abstract class ChatBase(title:String) extends JsonSupport with Slf4jLogger {

    val userHistoryStore:SeqStore[java.lang.Long]
    protected val counter:CounterProviderOperator
    protected val messagesText:String

    protected val chatMessageSeqStore: SeqStore[java.lang.Long]

    protected var _id = 0L
    protected var initiatorId:IDType = 0L
    protected var _creationTime:Long = 0L
    var lastUpdated = 0L

    override def hashCode(): Int = getId.toString.hashCode

    def getId:Long = _id
    def setId(id: IDType): this.type = {
        this._id = id
        this
    }

    def getInitiatorId = initiatorId
    def getInitiator:Option[User] = {
        require(initiatorId != 0L, "initiatorId is 0 or not set")
        User.getById(initiatorId)
    }

    def setInitiator(user:User): this.type = {
        setInitiatorId(user.getId)
        this
    }

    def setInitiatorId(id:IDType): this.type = {
        require(this.initiatorId == 0L, "initiator already set, prevent overwrite.")
        this.initiatorId = id
        this
    }

    def creationTime: Long = _creationTime
    def setCreationTime(ct:Long): this.type = {
        this._creationTime = ct
        this
    }

    def getLastUpdatedTime() = new DateTime(lastUpdated)
    def setLastUpdated(lastUpdated: IDType): this.type = {
        this.lastUpdated = lastUpdated
        this
    }

    def getMessageById(id:Long): Option[Message] = {
        chatMessageSeqStore.get(id).map(Message.fromJsonString)
    }

    def getMessageCount: Int = {
        counter.get(messagesText).toInt
    }

    def incrementMessageCount(by:Long=1) = {
        counter.incrementBy(messagesText, by)
    }

    def decrementMessageCount(by:Long=1) = {
        counter.decrementBy(messagesText, by)
    }

    def registerMessage(msg:Message){
        // masukkan ke per-chat message store
        chatMessageSeqStore.insert(msg.getId, msg.toJsonString)
    }
}

/**
 * Chat room.
 *
 * @param title chat title if any, by default ""
 * @param kind chat kind, see [[ChatKind]]
 */
case class Chat(var title:String="", kind:Int) extends ChatBase(title) {

    protected lazy val counter = Digaku.engine.counterProvider("chat-" + getId)

    override lazy val userHistoryStore = {
        require(getId != 0, "id not set, unsaved Chat?")
        ChatHistory.seqStore.build("user-history-store-" + getId, ()=> Chat.engine.generateId())
    }

    protected val messagesText = Chat.messagesText

    // per-chat messages store to easy track messages from chat scope
    protected lazy val chatMessageSeqStore: SeqStore[java.lang.Long] =
        ChatHistory.seqStore.build("chat-messages-%s".format(getId), ()=> Chat.engine.generateId())

    // chat bisa ada banner-nya apabila mau, by default gak ada
    private var thumbnail = ""

    override def toString: String = {
        kind match {
            case ChatKind.Group => "ChatGroup{id=%s,title=%s}".format(_id, title)
            case ChatKind.PersonToPerson => "ChatP2P{id=%s}".format(_id)
        }
    }

    def toJson:JValue = {
        require(_id != 0L, "cannot convert to json, id still 0, unsaved chat object?")
        require(this.initiatorId != 0L, "initiator must set")

        ("id" -> this._id) ~ ("initiatorId" -> this.initiatorId) ~
            ("title" -> title) ~ ("kind" -> kind) ~
            ("lastUpdatedTime" -> lastUpdated) ~ ("thumbnail" -> thumbnail) ~
            ("creationTime" -> _creationTime)
    }

    def getConnectedChatHistories:Seq[ChatHistory] = {
        userHistoryStore.getStream(None, None, 300).map(d => ChatHistory.fromJsonString(d._2)).toSeq
    }


    def getChatHistoryFor(user:User):Option[ChatHistory] = {
        userHistoryStore.get(user.getId).map(ChatHistory.fromJsonString)
    }


    def addInactiveParticipants(users:User*) = {
        require(_id != 0L, "cannot add participant while Chat object is not saved yet.")

        // check max participants limitation
        if (getParticipantCount >= 150){
            throw LimitationReachedException("max participants is 150 persons")
        }

        var count = 0

        for (_user <- users){

            if (isParticipant(_user)){
                throw AlreadyExistsException("cannot add user as participant, " +
                    _user.getName + " already participant.")
            }

            val ch = ChatHistory(_user.getId, this.getId)
                .setActive(false)

            // untuk inactive chat history tidak dimasukkan langsung di user chatSeqStore di sini
//            // apabila bukan initiator masukkan, untuk initiator sendiri gak perlu karena sudah dilakukan
//            // ketika pertama kali build
//            if (_user.getId != initiatorId){
//                val index = _user.chatSeqStore.insert(this.toJsonString)
//                _user.chatSeqStoreIndex.insert(this.getId, index.toString)
//            }

            this.userHistoryStore.insert(_user.getId, ch.toJsonString)

            count += 1
        }

//        for ( user <- users ){
//            user.getCounter.increment("chats")
//        }

        count

    }

    def addParticipants(users:User*) = {
        require(_id != 0L, "cannot add participant while Chat object is not saved yet.")

        val participantCount = getParticipantCount

        if (kind == ChatKind.PersonToPerson){
            if (participantCount >= 2){
                throw InvalidParameterException("Cannot add participant more than 1 into P2P chat mode")
            }
        }

        var count = 0

        // check max participants limitation
        if (participantCount >= 150){
            throw LimitationReachedException("max participants is 150 persons")
        }

        for (_user <- users){
            if (isParticipant(_user)){
                throw AlreadyExistsException("cannot add user as participant, " +
                    _user.getName + " already participant.")
            }
        }

        for (_user <- users){

//                val user = _user.reload()(t)

            val ch = ChatHistory(_user.getId, this.getId).setActive(true)

            val chatUser = new ChatUser(_user)

            // apabila bukan initiator masukkan. Untuk initiator sendiri gak perlu karena sudah dilakukan
            // ketika pertama kali build
            if (_user.getId != initiatorId){
                // kalo sudah ada abaikan
                if (chatUser.chatSeqStoreIndex.get(this.getId).isEmpty){
                    val index = chatUser.chatSeqStore.insert(this.toJsonString)
                    chatUser.chatSeqStoreIndex.insert(this.getId, index.toString)
                }
            }

            this.userHistoryStore.insert(_user.getId, ch.toJsonString)

            count += 1
        }

        for ( user <- users ){
            user.getCounter.increment("chats")
        }

        count
    }

    def removeParticipants(users:User*) = {
        var count = 0

        for ( user <- users ){

            // remove from chat history store
            userHistoryStore.delete(user.getId)
            user.getCounter.decrement("chats")

            val chatUser = new ChatUser(user)


            // remove from user seq store
            chatUser.chatSeqStoreIndex.get(getId).foreach {
                case AsLong(index) =>
                    chatUser.chatSeqStore.delete(index)
                    chatUser.chatSeqStoreIndex.delete(getId)
                case _ =>
            }


            count += 1

//            this.getInitiator.foreach { initiator =>
//                Digaku.engine.eventStream.emit(RemoveParticipantEvent(initiator, this, user))
//            }
        }

        count
    }

    def deactivateHistory(users:User*) = {
        for (user <- users){
            getChatHistoryFor(user).foreach { ch =>
                ch.setActive(false)
                userHistoryStore.update(user.getId, ch.toJsonString)
            }
        }
    }

    def activateHistory(users:User*) = {
        for (user <- users){
            getChatHistoryFor(user).foreach { ch =>
                ch.setActive(true)
                userHistoryStore.update(user.getId, ch.toJsonString)
            }
        }
    }



    def getParticipants: Seq[User] = {
        userHistoryStore.getStream(None, None, 150).map {
            case (userId, _) => tx { t =>
                getFromTx[User](userId, t)
            }
        }.toSeq
    }

    def getParticipantIds: Seq[IDType] = {
        userHistoryStore.getStream(None, None, 150).map(a => Long.unbox(a._1)).toSeq
    }


    /**
     * Like getParticipants but filter for active only.
     * @return
     */
    def getActiveParticipants: Seq[User] = {
        userHistoryStore.getStream(None, None, 150).flatMap {
            case (userId, chStr) =>
                val ch = ChatHistory.fromJsonString(chStr)
                if (ch.isActive){
                    User.getById(userId)
                }else{
                    None
                }
        }.toSeq
    }

    /**
     * Like getParticipantIds but filter for active only.
     * @return
     */
    def getActiveParticipantIds: Seq[Long] = {
        userHistoryStore.getStream(None, None, 150).flatMap {
            case (userId, chStr) =>
                val ch = ChatHistory.fromJsonString(chStr)
                if (ch.isActive){
                    Some(Long.unbox(userId))
                }else{
                    None
                }
        }.toSeq
    }

    def getParticipantCount:Int = {
        userHistoryStore.getCount
    }



    def genMessageId = {
        // apabila single node bisa pakai id angka-angka kecil
//        Chat.getIdgen(getId).nextId()

        // apabila di distributed mode harus pake ini
//        Digaku.engine.idFactory match {
//            case snow:SnowFlakeIdFactory =>
//                snow.createId().asInstanceOf[Long]
//            case x =>
//                throw new DigakuException("unsupported id factory generator: " + x)
//        }

        Chat.engine.generateId()
    }

    def isParticipant(user: User): Boolean = {
        isParticipant(user.getId)
    }

    def isParticipant(userId:IDType) = {
        userHistoryStore.get(userId).isDefined
    }


    /**
     * Sama seperti isParticipant
     * bedanya ini juga memeriksa apakah history-nya dalam mode aktif atau tidak.
     * @param user user yang akan diperiksa.
     * @return
     */
    def isActiveParticipant(user: User): Boolean = {
        userHistoryStore.get(user.getId).map(ChatHistory.fromJsonString).exists(_.isActive)
    }


    def ackMessages(user:User, messageIds:List[Long]) = {

        getChatHistoryFor(user).foreach { ch =>
            ch.ackMessages(messageIds)
        }

        Digaku.engine.eventStream.emit(DeliveryStatusUpdateEvent(user, this, messageIds, MessageDeliveryStatus.Delivered))

    }

    def markAllMessagesRead(user:User) = {
        var marked = 0
        getChatHistoryFor(user).foreach { ch =>
            marked += ch.markAllMessagesRead()
        }
        marked
    }

    def markMessagesRead(user: User, messageIds: List[Long]): Int = {
        getChatHistoryFor(user).map { ch =>
            /*val count = */ch.markMessagesRead(messageIds)
//            user.getCounter.decrementBy("chats", count)
        }.getOrElse(0)
    }

    def setTyping(user: User) = {
        Digaku.engine.eventStream.emit(TypingEvent(this, user, "..."))
    }

    def participantsTitle = {
        val ps = getParticipants.slice(0, 3)
        if (ps.length == 2){
            ps.map(_.getName).mkString(" and ")
        }else if (ps.length > 2){
            ps.slice(0, ps.length - 1).map(_.getName).mkString(", ") + " and " + ps.last.getName
        }else{
            "???"
        }
    }

    def getTitleFor(user:User) = {
        kind match {
            case ChatKind.Group =>
                if (title.trim.length > 0){
                    title
                }else{
                    participantsTitle
                }
            case ChatKind.PersonToPerson =>
                if (isParticipant(user)){
                    getParticipants.filterNot(_.getId == user.getId).map(_.getName).headOption.getOrElse("???")
                }else{
                    getInitiator.map(_.getName).getOrElse("???")
                }
        }
    }

    def getUnreadMessageCount(forUser:User): Long = {
        getChatHistoryFor(forUser).map(_.getUnreadMessageCount).getOrElse(0L)
    }

    def update() {

        setLastUpdated(Digaku.engine.dateUtils.nowMilis)

        // update all participants
        getParticipants.foreach { user =>

            val chatUser = new ChatUser(user)

            // remove old ref from index if any

            chatUser.chatSeqStoreIndex.get(getId) match {
                case Some(AsLong(index)) =>
                    chatUser.chatSeqStore.delete(index)

                    // gak perlu di-delete karena akan di-overwrite oleh code di bawahnya
                    //                        chatUser.chatSeqStoreIndex.delete(getId)

                    // update seq store
                    val newIndex = chatUser.chatSeqStore.insert(toJsonString)

                    // update new index
                    chatUser.chatSeqStoreIndex.insert(getId, newIndex.toString)

                case _ =>
            }

        }

        // masukkan ke global registry
        Chat.chatRegistry.insert(getId, toJsonString)

    }


    def updateHistory(ch:ChatHistory) = {
        this.userHistoryStore.insert(ch.userId, ch.toJsonString)
    }

    def getThumbnailFor(user:User) = {
        if (this.thumbnail.nonEmpty) {
            ContentNormalizer.normalizeUrl(this.thumbnail)
        } else {
            if (this.kind == ChatKind.PersonToPerson) {
                // "http://www.gravatar.com/avatar/%s.png?d=identicon".format(getTitleFor(user).md5)
                lazy val defaultThumbnail = DigakuWebCore.fixHttpProtocol(WebConfig.BASE_URL + "/assets/img/default/profile/2_64.png")

                getParticipants.filterNot(_.getId == user.getId).map { user =>
                    if (user.photoSmall.nonEmpty) {
                        ContentNormalizer.normalizeUrl(user.photoSmall)
                    } else {
                        defaultThumbnail
                    }
                }.headOption.getOrElse(defaultThumbnail)
            } else {
                // "http://www.gravatar.com/avatar/%s.png?d=identicon".format((getId.toString + getTitleFor(user)).md5)
                DigakuWebCore.fixHttpProtocol(WebConfig.BASE_URL + "/assets/img/default/group/0_64.png")
            }
        }
    }

    def setThumbnail(url:String) = {
        this.thumbnail = url
        this
    }

    def setTitle(_title:String) = {
        this.title = _title
        this
    }

    def removeUserHistory(user:User){
        getChatHistoryFor(user).foreach { ch =>
            ch.clear()
            userHistoryStore.delete(user.getId)
        }
    }

    def removeUserHistory(ch: ChatHistory){
        ch.clear()
        userHistoryStore.delete(ch.userId)
    }

    def publishEvent(initiator: User, eventText: String, includeInactiveHistory:Boolean=false,
                     noEmitEvent:Boolean=false, metaData:Map[String, String] = Map.empty[String, String]): MessageEvent = {

        val id = genMessageId
        val msg = MessageEvent(id, eventText)
            .setInitiator(initiator)
            .setTargetChatId(getId)
            .setTimestamp(Digaku.engine.dateUtils.nowMilis)
            .setMetaData(metaData)

        chatMessageSeqStore.insert(id, msg.toJsonString)

        for (ch <- getConnectedChatHistories){

            if (includeInactiveHistory){
                ch.addMessage(msg)
            }else{
                if (ch.isActive){
                    ch.addMessage(msg)
                }
            }
            if (!noEmitEvent){
                Digaku.engine.eventStream.emit(ChatMessageSentEvent(msg, ch))
            }
        }

        this.incrementMessageCount()
        msg.asInstanceOf[MessageEvent]
    }

    def updateMessage(msg:Message) = {
        assert(msg.getId != 0L, "cannot update message, message id is 0, maybe not saved yet")
        chatMessageSeqStore.update(msg.getId, msg.toJsonString)
    }


    def addMessage(msg: Message) = {

        // ini harus duluan agar dipastikan sudah terdaftar di chat ini
        // sehingga chat.getMessageById akan dipastikan selalu dapat
        registerMessage(msg)

        val chatHistories = getConnectedChatHistories

        update()

        for (ch <- chatHistories){

            // check apakah user ada di connected histories apa tidak?
            // karena ada kemungkinan user dari sisi lain menghapus chat
            // yang mana menghapus dari chatSeqStore user
            // nah ini perlu dibalikin di mode p2p, karena apabila target user-nya gak exists
            // kalau tidak maka getChats tidak akan pernah menampilkan chat ini lagi
            ch.userO match {
                case Some(targetUser) =>

                    // operasi balikin hanya untuk P2P
                    if (kind == ChatKind.PersonToPerson){
                        val targetChatUser = new ChatUser(targetUser)

                        val storeIndex = targetChatUser.chatSeqStoreIndex.get(getId)
                        // for super strong consistency reason
//                        val cacheKey = s"chat-$getId-${targetUser.getId}"
                        if (storeIndex.isEmpty || !ch.isActive){

                            if (ch.isActive){
                                debug("user not member of chat room, then recreate it.")
                            }else{
                                debug("user history not active, activate it first.")
                            }

                            // ensure to always to activate
                            ch.setActive(true)
                            userHistoryStore.insert(targetUser.getId, ch.toJsonString)

                            // masukkan kembali ke user seqStore apabila belum ada
                            if (targetChatUser.chatSeqStoreIndex.get(getId).isEmpty){
                                val index = targetChatUser.chatSeqStore.insert(this.toJsonString)
//                                Chat.consistencyHack.put(cacheKey, this.toJsonString)
                                targetChatUser.chatSeqStoreIndex.insert(getId, index.toString)
                            }
                            targetUser.getCounter.increment("chats")
                        }

                        ch.addMessage(msg.addConsumersByIds(ch.userId))
                        Digaku.engine.eventStream.emit(ChatMessageSentEvent(msg, ch))
                    }else{
                        // untuk Group
                        if (ch.isActive){
                            msg.addConsumersByIds(ch.userId)
                            ch.addMessage(msg)
                        }
                    }


                case _ =>
            }


        }

        // Group Message
        msg.getTargetChat.foreach { chat =>
            if (chat.kind == ChatKind.Group) {
                msg.getInitiator.foreach { u =>
                    // Set delivery Sent status message di tambahkan
                    msg.setDeliveryStatusFor(u, MessageDeliveryStatus.Sent)
                }

                Digaku.engine.eventStream.emit(GroupChatMessageSentEvent(msg, chat))
            }
        }

        incrementMessageCount()

        msg
    }

    /**
     * Unlike addMessage, this only register to the chat message sequence store.
     * @param msg message to register.
     */
    override def registerMessage(msg:Message){
        // masukkan ke per-chat message store
        super.registerMessage(msg)
        Digaku.engine.eventStream.emit(CreateMessageEvent(msg))
    }


}

object Chat extends ChatComponentConfig with Slf4jLogger {

    val messagesText = "messages"

    def createChat(initiator:User, title: String, kind: Int) = {

        build(Chat(title, kind)
            .setInitiator(initiator))

    }

    lazy val chatRegistry: SeqStore[java.lang.Long] = ChatHistory.seqStore.buildCrypted("global-chat-store",
        ()=> engine.generateId())


    def build(chat:Chat) = {
        require(chat.getId == 0L, "id already defined, prevent overwrite operation.")
        require(chat.getInitiatorId != 0L, "initiator must set")

        val id = Chat.engine.generateId()

        chat.setId(id)
            .setLastUpdated(Digaku.engine.dateUtils.nowMilis)

        chatRegistry.insert(id, chat.toJsonString)

        User.getById(chat.initiatorId).foreach { initiator =>

            val initiatorChatUser = new ChatUser(initiator)

            assert(initiatorChatUser.chatSeqStoreIndex.get(id).isEmpty, "cannot update chatSeqStoreIndex, already exists, " +
                "this is to prevent overwrite")

            val index = initiatorChatUser.chatSeqStore.insert(chat.toJsonString)
            initiatorChatUser.chatSeqStoreIndex.insert(id, index.toString)
        }
        chat
    }

    def update(chat:Chat) = {
        require(chat.getId != 0L, "id is zero, not saved yet?")
        require(chat.getInitiatorId != 0L, "initiator must set")

        // update in global registry
        chatRegistry.insert(chat.getId, chat.toJsonString)

        // update in local user registry
        // for initiator and all participants

        val userIds = List(chat.initiatorId) ++ chat.getParticipantIds

        userIds.flatMap(id => User.getById(id))
            .foreach { u =>

            u.chatSeqStoreIndex.get(chat.getId)
                .map(_.toLong)
                .foreach { index =>
                    u.chatSeqStore.insert(index, chat.toJsonString)
                }

        }

        chat
    }



    /**
     * Dapatkan chat dari global registry.
     * @param chatId id dari chat yang ingin didapatkan.
     * @return Option[Chat]
     */
    def getById(chatId:Long): Option[Chat] = {


        chatRegistry.get(chatId).map(fromJsonString)

    }

    def fromJsonString(jsonText: String):Chat = {
        try {

            val json = parse(jsonText)

            val id = json \ "id" match {
                case JInt(_id) => _id.longValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting chat id. source: $jsonText")
            }
            val initiatorId = json \ "initiatorId" match {
                case JInt(_id) => _id.longValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting chat initiator id.")
            }


            val JString(title) = json \ "title"


            val kind = json \ "kind" match {
                case JInt(_id) => _id.intValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting chat kind.")
            }

            val lastUpdatedTime = json \ "lastUpdatedTime" match {
                case JInt(_id) => _id.longValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting chat last updated time.")
            }

            val thumbnail = json \ "thumbnail" match {
                case JString(_thumbnail) => _thumbnail
                case x => ""
            }

            val creationTime = json \ "creationTime" match {
                case JInt(_cr) => _cr.longValue()
                case _ =>
                    // for backward compatibility only
                    // get from last updated time
                    lastUpdatedTime
            }


            Chat(title, kind)
                .setId(id)
                .setInitiatorId(initiatorId)
                .setThumbnail(thumbnail)
                .setLastUpdated(lastUpdatedTime)
                .setCreationTime(creationTime)

        }catch{
            case e:net.liftweb.json.JsonParser.ParseException =>
                error("error parsing: " + jsonText)
                e.printStackTrace()
                throw e
        }
    }

    def drop(chat: Chat){

        // remove from initiator and all participants seq stores

        chat.getInitiator.foreach { initiator =>

//            initiator.chatSeqStoreIndex.get(chat.getId) match {
//                case Some(AsLong(index)) =>
//                    initiator.chatSeqStore.delete(index)
//                case _ =>
//            }

            val chatUser = new ChatUser(initiator)


            val Some(AsLong(index)) = chatUser.chatSeqStoreIndex.get(chat.getId)

            chatUser.chatSeqStoreIndex.delete(chat.getId)
            chatUser.chatSeqStore.delete(index)

            // search and destroy!
            var indexToDeletes = new ListBuffer[IDType]

            chatUser.chatSeqStore.paging(None, None){ case (idx, b) =>
                val c = Chat.fromJsonString(b)
                if (c.getId == chat.getId){
                    indexToDeletes += idx
                }
                true
            }

            indexToDeletes.result().foreach(idx => chatUser.chatSeqStore.delete(idx))
            debug(s"deleting chat ref for chat with id ${chat.getId} from chatSeqStore")

//            val cacheKey = s"chat-${chat.getId}-${initiator.getId}"
//            Chat.consistencyHack.remove(cacheKey)

        }

        chat.getParticipants.foreach { participant =>
//            val Some(AsLong(index)) = participant.chatSeqStoreIndex.get(chat.getId)

            val chatUser = new ChatUser(participant)

            chatUser.chatSeqStoreIndex.get(chat.getId) match {
                case Some(AsLong(index)) =>
                    chatUser.chatSeqStore.delete(index)

                    // search and destroy!
                    var indexToDeletes = new ListBuffer[IDType]

                    chatUser.chatSeqStore.paging(None, None){ case (idx, b) =>
                        val c = Chat.fromJsonString(b)
                        if (c.getId == chat.getId){
                            indexToDeletes += idx
                        }
                        true
                    }

                    indexToDeletes.result().foreach(idx => chatUser.chatSeqStore.delete(idx))

                case _ =>
            }
            chatUser.chatSeqStoreIndex.delete(chat.getId)

//            val cacheKey = s"chat-${chat.getId}-${participant.getId}"
//            Chat.consistencyHack.remove(cacheKey)

        }

        // finally remove from public registry
        chatRegistry.delete(chat.getId)
    }

}

object ChatType {
    val CHAT = 1
    val SCCHAT = 2
}
