package com.ansvia.digaku.messaging.se

import java.util.concurrent.TimeUnit

import akka.actor.Props
import akka.util.Timeout
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.messaging.ScChat
import com.ansvia.digaku.messaging.js.ScChatStatusType
import com.ansvia.digaku.messaging.se.SearchEngineIndexWorkerChat.{ChatSetSearchEngine, StartChatIndex, ChatIndexStatus, ChatIndexGetStatus}
import com.ansvia.digaku.model.{Topic, User}
import com.ansvia.digaku.se.{DistributedElasticSearchEngine, SpecificSearchResult}
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest
import org.elasticsearch.action.admin.indices.exists.indices.IndicesExistsRequest
import org.elasticsearch.action.search.SearchType
import org.elasticsearch.client.Client
import org.elasticsearch.common.settings.ImmutableSettings
import org.elasticsearch.common.xcontent.XContentFactory._
import org.elasticsearch.index.query.{FilterBuilders, SimpleQueryStringFlag, QueryBuilders}
import org.elasticsearch.indices.IndexMissingException
import akka.pattern.ask
import org.elasticsearch.search.sort.SortOrder
import scala.concurrent.Await
import scala.concurrent.duration.Duration
import scala.collection.JavaConversions._

/**
 * Author: nadir (<EMAIL>)
 */

/**
 * Elastic search untuk Sc Chat
 */
trait ScChatElasticSearchEngine extends Slf4jLogger {

    val distributedElasticSearchEngine:DistributedElasticSearchEngine

    private var checkChatIndicesEnsured = false
    private val chatIndexNames = Seq(("digaku13", "sc-chat" :: "sc-chat-tags" :: Nil))

    private lazy val chatWorker = {
        val rv = Digaku.engine.actorSystem.actorOf(Props[SearchEngineIndexWorkerChat], "chat-se-index-worker")
        rv ! ChatSetSearchEngine(distributedElasticSearchEngine)
        rv
    }

    def ensureChatIndicesCreated(): Unit ={
        if (!checkChatIndicesEnsured) {
            def _index(_indexName: String, _types: List[String]) {
                if (!distributedElasticSearchEngine.indexExists(_indexName)) {

                    val createIndexReqBuilder = distributedElasticSearchEngine.client.admin().indices().prepareCreate(_indexName)

                    for (_t <- _types) {
                        val mappingBuilder = jsonBuilder()
                            .startObject()
                            .startObject(_t)
                            .startObject("_timestamp").field("enabled", true).field("store", true).endObject()
                            .endObject()
                            .endObject()

                        createIndexReqBuilder.addMapping(_t, mappingBuilder)
                    }

                    createIndexReqBuilder.setSettings(ImmutableSettings.settingsBuilder().loadFromSource(
                        jsonBuilder().startObject()
                            .startObject("analysis")
                            .startObject("analyzer")
                            .startObject("standard")
                            .field("type", "custom")
                            .field("tokenizer", "standard")
                            .field("filter", Array("snowball", "standard", "whitespace", "lowercase"))
                            .endObject()
                            .endObject()
                            .endObject()
                            .endObject().string()
                    ))

                    try {
                        createIndexReqBuilder.execute().actionGet()
                    } catch {
                        case _: org.elasticsearch.indices.IndexAlreadyExistsException =>
                            warn(s"index ${_indexName} already exists")
                    }

                }
            }

            for ((idxName, _types) <- chatIndexNames) {
                _index(idxName, _types)
            }


            distributedElasticSearchEngine.client.admin().cluster().prepareHealth().setWaitForYellowStatus().execute().actionGet()
            Thread.sleep(5000)

            checkChatIndicesEnsured = true
        }
    }

    /**
     * Index tags yang ada pada sc chat
     * @param str
     */
    private def indexTag(str:String): Unit ={
        val lowerTag = str.trim.toLowerCase
        distributedElasticSearchEngine.client.prepareIndex("digaku13", "sc-chat-tags", lowerTag)
            .setSource(
                jsonBuilder().startObject()
                    .field("lowerName", lowerTag)
                    .field("name", str.trim)
            ).execute()
            .actionGet()
    }

    /**
     * Index Sc Chat
     * @return
     */
    def indexScChat:PartialFunction[Any, Unit] = {
        case scChat:ScChat =>
            info("indexing " + scChat)
            ensureChatIndicesCreated()

            val lastStatus = scChat.getLastStatus

            val agents = scChat.getConnectedChatHistories(100).map(_.userId).mkString(" ")
            val initiator =  scChat.getInitiator

            distributedElasticSearchEngine.client.prepareIndex("digaku13", "sc-chat", scChat.getId.toString)
                .setSource(
                    jsonBuilder().startObject()
                        .field("vid", scChat.getId.toString)
                        .field("question", scChat.getQuestion)
                        .field("agentId", scChat.getAgentId)
                        .field("topicId", scChat.getTopicId)
                        .field("creationTime", scChat.creationTime)
                        .field("lastStatusTime", lastStatus.map(_.getCreationTime).getOrElse(0L))
                        .field("agentHistoryIds", agents)
                        .field("initiatorId", scChat.getInitiatorId)
                        .field("initiatorName", initiator.map(_.name).getOrElse(""))
                        .field("initiatorFullName", initiator.map(_.getName).getOrElse(""))
                        .field("lastStatus", lastStatus.map(_.status).getOrElse(0))
                        .field("tags", scChat.getTags)
                        .field("categoryId", scChat.getCategoryId)
                        .field("closedQuestion", scChat.getClosedQuestion)
                        .endObject()
                ).execute()
                .actionGet()

            scChat.getTags.split(",").foreach { tag =>
                indexTag(tag)
            }
    }

    /**
     * Index Ulang semua ScChat
     */
    def reindexScChat(): Unit = {
        resetIndexScChat()

        chatWorker ! StartChatIndex()
    }

    /**
     * Reset Index ScChat
     */
    def resetIndexScChat(): Unit ={
        try {
            val rv = distributedElasticSearchEngine.client.admin().indices().delete(new DeleteIndexRequest(
                chatIndexNames.map(_._1): _*)).actionGet()

            if (!rv.isAcknowledged){
                error("search index deletion failed")
            }
        }catch{
            case e:IndexMissingException =>
                error(e.getMessage)
        }finally{
            checkChatIndicesEnsured = false
            ensureChatIndicesCreated()
        }
    }

    /**
     * Status pengindexan dari ScChat
     * @return
     */
    def indexingStatusScChat:ChatIndexStatus = {

        implicit val tm:Timeout = Timeout(Duration(5, TimeUnit.MINUTES))

        val status = try {
            Await.result(chatWorker ? ChatIndexGetStatus(), tm.duration).asInstanceOf[ChatIndexStatus]
        }catch{
            case e:Exception =>
                error(e.getMessage)
                e.printStackTrace()
                val rv = ChatIndexStatus(0L, 0L, "unknown: " + e.getMessage)
                rv
        }

        status
    }

    /**
     * Search ScChat
     * @param query Query pencarian untuk question pada ScChat
     * @param agent filter agent tertentu.
     * @param topic filter topic tertentu.
     * @param tags filter berdasarkan tags
     * @param chatStatus Filter ScChat yang memiliki status tertentu
     * @param offset
     * @param limit
     * @param sortKey
     * @param sortOrder
     * @return
     */
    def searchScChat(query:String, agent:Option[User], topic:Option[Topic], tags:String, chatStatus:Option[Int], offset:Int, limit:Int,
                     sortKey:String = "creationTime", sortOrder: SortOrder = SortOrder.DESC):SpecificSearchResult[ScChat] = {
        val mainQuery = query.trim.toLowerCase

        val q1 = QueryBuilders.boolQuery()
            .should(QueryBuilders.commonTerms("question", mainQuery).boost(1.0F))
            .minimumNumberShouldMatch(1)

        agent.foreach { user =>
            q1.must(QueryBuilders.termQuery("agentId", user.getId.toString))
        }

        topic.foreach { tp =>
            q1.must(QueryBuilders.matchQuery("topicId", tp.getId.toString))
        }

        if (chatStatus.isDefined) {
            q1.must(QueryBuilders.matchQuery("lastStatus", chatStatus.get))
        } else {
            q1.mustNot(QueryBuilders.matchQuery("lastStatus", ScChatStatusType.OPEN))
                .mustNot(QueryBuilders.matchQuery("lastStatus", ScChatStatusType.QUEUE))
        }

        val _tags = tags.trim.toLowerCase

        if (_tags.nonEmpty) {
            val tagsQuery = _tags.replaceAll(",", " ").replaceAll("\\s+", "+")
            q1.must(QueryBuilders.simpleQueryString(tagsQuery).field("tags")
                .flags(SimpleQueryStringFlag.AND))
        }

        val resp = distributedElasticSearchEngine.client.prepareSearch("digaku13")
            .setTypes("sc-chat")
            .setSearchType(SearchType.DEFAULT)
            .addFields("vid")
            .addSort(sortKey, sortOrder)
            .setFrom(offset).setSize(limit)
            .setQuery(q1)
            .execute()
            .actionGet()

        val entries = resp.getHits.iterator().flatMap { hit =>
            if (hit.getFields.get("vid") != null){
                val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong
                ScChat.getById(id)
            } else {
                None
            }
        }.toSeq

        SpecificSearchResult[ScChat](entries, resp.getHits.getTotalHits,
            resp.getTookInMillis, resp.getTook.format())
    }

    def searchAgentScChat(agent:User, query:String, chatStatus:Option[Int], offset:Int, limit:Int): SpecificSearchResult[ScChat] = {
        val mainQuery = query.trim

        val q1 = QueryBuilders.boolQuery()

        if (mainQuery.length > 1) {
            q1.should(QueryBuilders.matchQuery("vid", mainQuery).boost(3.0F))
                .should(QueryBuilders.commonTerms("question", mainQuery).boost(2.5F))
                .minimumNumberShouldMatch(1)
                .should(QueryBuilders.matchQuery("initiatorName", mainQuery).boost(1.5F))
                // support phrase prefix misal pencarian: "maria sa" untuk mencari "maria salsa"
                .should(QueryBuilders.matchPhrasePrefixQuery("initiatorFullName", mainQuery).boost(2.0F))
                // supaya support pencarian first name dan prefix last name tanpa menggunakan middle name,
                // misal: "maria ana" untuk mencari "maria salsa ananta"
                .should(QueryBuilders.simpleQueryString(mainQuery.replaceAll("\\s+", "+") + "*").field("initiatorFullName", 1.0F)
                .flags(SimpleQueryStringFlag.AND, SimpleQueryStringFlag.PREFIX))
        }

        q1.must(QueryBuilders.termQuery("agentHistoryIds", agent.getId.toString))

        val q2 = chatStatus.map {
            case ScChatStatusType.AGENT_TIMEOUT =>
                val filter = FilterBuilders.orFilter(FilterBuilders.termsFilter("lastStatus", ScChatStatusType.AGENT_TIMEOUT),
                    FilterBuilders.andFilter(FilterBuilders.termFilter("lastStatus", ScChatStatusType.OPEN),
                        FilterBuilders.queryFilter(QueryBuilders.boolQuery().mustNot(QueryBuilders.matchQuery("agentId", agent.getId)))))

                QueryBuilders.filteredQuery(q1, filter)
            case ScChatStatusType.OPEN =>
                q1.must(QueryBuilders.termQuery("lastStatus", ScChatStatusType.OPEN))
                q1.must(QueryBuilders.matchQuery("agentId", agent.getId))
            case st =>
                q1.must(QueryBuilders.termQuery("lastStatus", st))
        }.getOrElse(q1)

        val resp = distributedElasticSearchEngine.client.prepareSearch("digaku13")
            .setTypes("sc-chat")
            .setSearchType(SearchType.DEFAULT)
            .addFields("vid")
            .setFrom(offset).setSize(limit)
            .setQuery(q2)
            .execute()
            .actionGet()

        val entries = resp.getHits.iterator().flatMap { hit =>
            if (hit.getFields.get("vid") != null){
                val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong
                ScChat.getById(id)
            } else {
                None
            }
        }.toSeq

        SpecificSearchResult[ScChat](entries, resp.getHits.getTotalHits,
            resp.getTookInMillis, resp.getTook.format())
    }

    /**
     * Mencari tags dari ScChat
     * @param query Query tags name
     * @param offset
     * @param limit
     * @return
     */
    def searchScChatTags(query:String, offset:Int, limit:Int): SpecificSearchResult[String] = {
        val mainQuery = query.trim.toLowerCase

        val q1 = QueryBuilders.boolQuery()
            .should(QueryBuilders.matchQuery("lowerName", mainQuery).boost(2.0F))
            .should(QueryBuilders.matchPhrasePrefixQuery("lowerName", mainQuery).boost(1.5F))
            .should(QueryBuilders.wildcardQuery("lowerName", "*" + query.toLowerCase + "*").boost(1.0F))
            .minimumNumberShouldMatch(1)

        val resp = distributedElasticSearchEngine.client.prepareSearch("digaku13")
            .setTypes("sc-chat-tags")
            .setSearchType(SearchType.DEFAULT)
            .addFields("name")
            .setFrom(offset)
            .setSize(limit)
            .setQuery(q1)
            .execute()
            .actionGet()

        val entries = resp.getHits.iterator().flatMap { hit =>
            if (hit.getFields.get("name") != null){
                val name = hit.getFields.get("name").getValue[java.lang.String]
                Some(name)
            } else {
                None
            }
        }.toSeq

        SpecificSearchResult[String](entries, resp.getHits.getTotalHits,
            resp.getTookInMillis, resp.getTook.format())
    }

}
