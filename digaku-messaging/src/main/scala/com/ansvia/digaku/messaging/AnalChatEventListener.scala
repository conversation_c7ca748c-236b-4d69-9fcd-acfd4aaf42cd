/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.event.{StreamEvent, AsyncEventStreamListener}
import com.ansvia.digaku.stats.model.DigakuStats
import com.ansvia.digaku.utils.AsInt
import org.joda.time.DateTime

/**
* Author: nadir (<EMAIL>)
*
*/

/**
 * Listener untuk analytics yang ada di chat.
 */
object AnalChatEventListener extends AsyncEventStreamListener with Slf4jLogger {

    private val events = Seq("create-message-event")

    override val actorName: String = "chat-anal"

    override def asyncDispatch: PartialFunction[StreamEvent, Unit] = {
        case CreateMessageEvent(msg) =>
            val dt = new DateTime()
//            val key = s"message-${dt.getYear}-${dt.getMonthOfYear}-${dt.getDayOfMonth}"
            val key = "%04d%02d%02d".format(dt.getYear, dt.getMonthOfYear, dt.getDayOfMonth).toLong

            DigakuStats.chatMessageCounter.get(key) match {
                case Some(AsInt(_value)) =>
                    DigakuStats.chatMessageCounter.insert(key, (_value + 1).toString)
                case _ =>
                    DigakuStats.chatMessageCounter.insert(key, "1")
            }

        case _ =>

    }

    /**
     * Check is event handled by this listener.
     * WARNING: don't use `transact` inside this method
     * to avoid dead-lock.
     * @param eventName name of event.
     * @return
     */
    override def isEventHandled(eventName: String): Boolean = events.contains(eventName)
}
