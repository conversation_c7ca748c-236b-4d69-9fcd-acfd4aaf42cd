package com.ansvia.digaku.messaging

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.messaging.js.ScChatStatusType
import com.ansvia.digaku.model.{Topic, User}
import com.ansvia.digaku.web.MtSession
import com.ansvia.perf.PerfTiming
import ImplicitChatUser._
import scala.collection.mutable.ListBuffer

/**
 * Author: nadir (<EMAIL>)
 */

object ScChatTopic {

    implicit class ImplicitScChatTopic(topic:Topic) extends PerfTiming with Slf4jLogger {
        lazy val scChatTopicIdStore = QueueScChat.seqStore.build("topic-sc-chat-" + topic.getId, ()=> ScChat.engine.generateId())
        lazy val queueScChatSeqStore = QueueScChat.seqStore.build("topic-queue-sc-chat-" + topic.getId,  ()=> ScChat.engine.generateId())
        lazy val priorityQueueScChatSeqStore = QueueScChat.seqStore.build("prior-topic-queue-sc-chat-" + topic.getId,  ()=> ScChat.engine.generateId())


        def getQueueScChat(limit:Int):Seq[(java.lang.Long, String)] = {
            val priorQueueScChat = priorityQueueScChatSeqStore.getStream(None, None, limit).toSeq
            val queueScChat = queueScChatSeqStore.getStream(None, None, limit - priorQueueScChat.length).toSeq

            priorQueueScChat ++ queueScChat
        }

        /**
         * Menambahkan ScChat ke queue ketika tidak ada agent yang available
         * @param scChat
         * @param prioritized
         * @return
         */
        def addScChat(scChat: ScChat, prioritized:Boolean = false): ScChat = {
            if (!scChatTopicIdStore.get(scChat.getId).isDefined) {
                scChatTopicIdStore.insert(scChat.getId, scChat.getId.toString)
            }

            if (ScChatSettings.isOnlineTime) {
                val agent = getAllAvailableAgents.headOption

                if (agent.isDefined) {
                    agent.get._1.asAgentScChat(scChat)
                } else {
                    addToQueue(scChat, prioritized)
                    scChat
                }
            } else {
                addToQueue(scChat, prioritized)
                scChat
            }
        }

        private def addToQueue(scChat: ScChat, prioritized:Boolean): Unit = {
            if (prioritized) {
                priorityQueueScChatSeqStore.insert(scChat.toJsonString)
            } else {
                queueScChatSeqStore.insert(scChat.toJsonString)
            }
        }

        /**
         * Menambahkan antrian ScChat berikutnya ke agent tertentu
         * @param agent
         * @return
         */
        def nextScChatFor(agent:User):Option[ScChat] = {
            if (isAvailableUser(agent) && ScChatSettings.isOnlineTime) {
                getQueueScChat(1).headOption.map { rv =>
                    val id = rv._1
                    val sc = ScChat.fromJsonString(rv._2)

                    priorityQueueScChatSeqStore.delete(id)
                    queueScChatSeqStore.delete(id)

                    agent.asAgentScChat(sc)
                }
            } else {
                None
            }
        }

        /**
         * Mendistribusikan semua queue chat ke semua agent yang avaliable
         */
        def distributeQueueScChat(): Unit = {
            if (ScChatSettings.isOnlineTime) {
                var agents = getAllAvailableAgents
                val totalAvailable = agents.map(_._2).sum

                getQueueScChat(totalAvailable).foreach { rv =>
                    val scChat = ScChat.fromJsonString(rv._2)

                    if (agents.filter(_._2 > 0).nonEmpty) {
                        val agent = agents.maxBy(_._2)
                        agent._1.asAgentScChat(scChat)

                        priorityQueueScChatSeqStore.delete(rv._1)
                        queueScChatSeqStore.delete(rv._1)

                        agents += agent._1 -> (agent._2 - 1)
                    }
                }
            }
        }

        /**
         * Distribute semua Queue ScChat di topic ini
         * untuk user tertentu
         * @param user
         */
        def distributeQueueScChatFor(user:User): Unit = {
            if (ScChatSettings.isOnlineTime) {
                if (isAvailableUser(user)) {

                    val rv = getAvailabilityAgent(user)

                    getQueueScChat(rv._1).foreach { rv =>
                        val id = rv._1
                        val sc = ScChat.fromJsonString(rv._2)

                        priorityQueueScChatSeqStore.delete(id)
                        queueScChatSeqStore.delete(id)

                        user.asAgentScChat(sc)
                    }
                }
            }
        }

        /**
         * Get availble agent dari topic
         * @return
         */
        private def getAllAvailableAgents: Map[User, Int] = {
            val agentList = new ListBuffer[(User, Int, Long)]

            topic.userAgentSeqStore.paging(None, None) {
                case (k, v) =>
                    User.getById(k).foreach { user =>
                        if (isAvailableUser(user)) {
                            val (availableCount, lastClosed) = getAvailabilityAgent(user)

                            if (availableCount > 0) {
                                agentList.append((user, availableCount, lastClosed))
                            }
                        }
                    }

                    true
            }

            agentList.sortBy(x => (-x._2, x._3))
                .map(x => (x._1, x._2)).result().toMap
        }

        /**
         * Get available count for add scchat to agent
         * @param user agent
         * @return
         */
        private def getAvailabilityAgent(user:User): (Int, Long) = {
            val (openScc, closedScc) = user.getAgentScChats(None, None, 4)
                .partition { scc =>
                    scc.getLastStatus.exists(_.status == ScChatStatusType.OPEN) && scc.getAgentId == user.getId
                }

            val availableCount = ScChatSettings.maxScChatPerAgent - openScc.length
            val lastClosedChat = closedScc.headOption.map(_.lastUpdated).getOrElse(0L)

            (availableCount, lastClosedChat)
        }

        private def isAvailableUser(user: User): Boolean = {
            !user.isSuspendedAgent && MtSession.isOnlineUserWeb(user)
        }
    }
}
