/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.digaku.model.User
import com.ansvia.digaku.utils.RichString._

/**
 * Author: fajr (<EMAIL>)
 */

/**
 * Digunakan untuk penggunaan push notification secara general pada chat
 * @param chat
 * @param userSource
 * @param userTargetO
 * @param message
 * @param hiddenMessage
 * @param messageIdsO
 * @param notifType
 */
case class ChatGeneralPushNotification(chat:Chat, userSource:User, userTargetO:Option[User], message:String,
                                       hiddenMessage:String, messageIdsO:Option[List[Long]], notifType:String)
    extends ChatPushNotificationBase(chat:Chat, userSource:User, userTargetO:Option[User], message:String,
        hiddenMessage:String, messageIdsO:Option[List[Long]], notifType:String) {

    override lazy val hash: String = ""

    // Comment out karena menyebabkan notifikasi remove participant dan add participant kadang tidak jalan
    // karena ketika ada duplikat akan di throttle-kan 12 jam.
    // see [[com.ansvia.digaku.notifications.AsyncNotificationSendHandler]]
//    override lazy val hash: String = "%s:%s:%s:%s:%s:%s:%s".format(chat.getId, userSource.getId,
//        userTargetO.map(_.getId).getOrElse(0L), message, hiddenMessage, messageIdsO.map(_.mkString(",")).getOrElse(""),
//        notifType).md5
    override def renderFor(user:User):String = {
        val name = userSource.getName

        notifType match {
            case "remove-participant-event" =>
                "%s: %s menghapus %s dari dalam grup.".format(name, name, userTargetO.map(_.getName).getOrElse("User"))

            case "chat-invite-user-event" =>
                "%s: %s menambahkan %s ke dalam grup.".format(name, name, userTargetO.map(_.getName).getOrElse("User"))

            case _ => message
        }
    }

}
