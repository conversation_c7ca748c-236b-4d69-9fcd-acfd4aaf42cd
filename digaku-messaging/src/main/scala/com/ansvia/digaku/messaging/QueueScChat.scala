package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.persistence.CassandraBackedSeqStore
import com.netflix.astyanax.model.ColumnFamily
import com.netflix.astyanax.serializers.{ByteSerializer, LongSerializer, StringSerializer}

/**
 * Author: nadir (<EMAIL>)
 */

object QueueScChat {

    private lazy val CF = new ColumnFamily[String, java.lang.Long](
        "queue_sc_chat_store",
        StringSerializer.get(),
        LongSerializer.get(),
        ByteSerializer.get())

    /**
     * Seq store untuk menyimpan antrian SC Chat yang belum ditangani oleh
     * seorang agent
     */
    lazy val seqStore:CassandraBackedSeqStore[java.lang.Long] = new CassandraBackedSeqStore(CF,
        Digaku.config.mainDatabase.keyspaceName,
        Digaku.config.mainDatabase.clusterName,
        Digaku.config.mainDatabase.hostName,
        Digaku.config.mainDatabase.replStrategy,
        Digaku.config.mainDatabase.replStrategyOpts)
        // Tidak usah di-reverse karena queue urutannya dari yang terkecil
        .setComparatorType("LongType")
        .setCaching("NONE")
}
