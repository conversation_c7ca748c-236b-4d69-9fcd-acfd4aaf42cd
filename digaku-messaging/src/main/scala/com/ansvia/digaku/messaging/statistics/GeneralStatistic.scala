package com.ansvia.digaku.messaging.statistics

import java.util.Calendar

import com.ansvia.digaku.messaging.JsonSupport
import net.liftweb.json.JValue
import net.liftweb.json.JsonDSL._
import net.liftweb.json._

/**
 * Author: nadir (<EMAIL>)
 */

/**
 * Digunakan untuk memodelkan data general statistic yang yang nantinya
 * akan disimpan berupa json string
 * see com.ansvia.digaku.shell.commands.scchatstatistic.GeneralStatisticGenerator
 */

case class GeneralDateStatistic(date:Int, count:Int) extends JsonSupport {
    override def toJson: JValue = {
        ("date" -> date) ~ ("count" -> count)
    }
}

case class GeneralStatistic(name:String, year:Int, month:Int, var queue:Seq[GeneralDateStatistic], var question:Seq[GeneralDateStatistic],
                            var timeoutAgent:Seq[GeneralDateStatistic]) extends ScChatStatisticModel(name) with JsonSupport {
    override def toJson: JValue = {
        ("name" -> name) ~
            ("year" -> year) ~
            ("month" -> month) ~
            ("queue" -> queue.map(_.toJson)) ~
            ("question" -> question.map(_.toJson)) ~
            ("timeoutAgent" -> timeoutAgent.map(_.toJson))
    }

    override def toCsv: String = {
        val sb = new StringBuilder()

        val calendar = Calendar.getInstance()

        calendar.set(Calendar.YEAR, year)
        calendar.set(Calendar.MONTH, month)
        calendar.set(Calendar.DATE, 1)

        val maxDays = calendar.getMaximum(Calendar.DATE)

        val days = (1 to maxDays).toList

        sb.append(s"date,${days.mkString(",")}\n")

        val queueData = days.map(date => queue.find(_.date == date).map(_.count).getOrElse(0)).mkString(",")
        sb.append(s"Question in Queue,$queueData\n")

        val questionData = days.map(date => question.find(_.date == date).map(_.count).getOrElse(0)).mkString(",")
        sb.append(s"New Question,$questionData\n")

        val timeoutAgentData = days.map(date => timeoutAgent.find(_.date == date).map(_.count).getOrElse(0)).mkString(",")
        sb.append(s"Agent Timeout,$timeoutAgentData")

        sb.result()
    }
}
object GeneralStatistic {
    implicit val f = DefaultFormats

    def fromJsonString(str:String): GeneralStatistic = {
        parse(str).extract[GeneralStatistic]
    }
}