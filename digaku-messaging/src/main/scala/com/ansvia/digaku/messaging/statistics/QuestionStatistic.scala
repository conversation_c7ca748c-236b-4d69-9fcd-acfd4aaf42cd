package com.ansvia.digaku.messaging.statistics

import com.ansvia.digaku.messaging.JsonSupport
import com.ansvia.digaku.model.Topic
import net.liftweb.json.JValue
import net.liftweb.json.JsonDSL._
import net.liftweb.json._

/**
 * Author: nadir (<EMAIL>)
 */

/**
 * Digunakan untuk memodelkan data created question statistic yang yang nantinya
 * akan disimpan berupa json string
 * see com.ansvia.digaku.shell.commands.scchatstatistic.QuestionStatisticGenerator
 */

case class QuestionTopicStatistic(topicId:Long, solved:Int, unsolved:Int, markAsResolved:Int) extends JsonSupport {
    override def toJson: JValue = {
        ("topicId" -> topicId) ~
            ("solved" -> solved) ~
            ("unsolved" -> unsolved) ~
            ("markAsResolved" -> markAsResolved)
    }
}

case class QuestionStatistic(name:String, questionTopic:Seq[QuestionTopicStatistic]) extends ScChatStatisticModel(name) with J<PERSON>Sup<PERSON> {
    override def toJson: JValue = {
        ("name" -> name) ~
            ("questionTopic" -> questionTopic.map(_.toJson))
    }

    override def toCsv: String = {
        if (questionTopic.length > 0) {
            val sb = new StringBuilder()

            sb.append("ID,Question Topic Name,Solved,Unsolved,Mark as Resolved\n")

            questionTopic.foreach { qt =>
                Topic.getById(qt.topicId).foreach { topic =>
                    val name = normStrCsvColumn(topic.name)
                    sb.append(s"${qt.topicId},$name,${qt.solved},${qt.unsolved},${qt.markAsResolved}\n")
                }
            }

            sb.result().trim
        } else {
            ""
        }
    }
}

object QuestionStatistic {
    implicit val f = DefaultFormats

    def fromJsonString(str:String):QuestionStatistic = {
        parse(str).extract[QuestionStatistic]
    }
}
