package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.model.User
import com.ansvia.digaku.notifications.NonPersistentNotification
import com.ansvia.digaku.utils.UserSettings._

/**
 * Author: nadir (<EMAIL>)
 */

case class ScChatDeliveryStatusPushNotification(scChat: ScChat, message: String)
    extends NonPersistentNotification(105) {

    private val id = Digaku.engine.idFactory.createId().asInstanceOf[IDType]

    override def getId: IDType = id

    override def getDefaultReceivers: Seq[User] = scChat.getParticipants.filterNot(_.isAgentTopic)

    override def getCreationTime: IDType = Digaku.engine.dateUtils.nowMilis

    override def isValid: Boolean = true

    override def renderFor(user: User): String = message

    override val hash: String = ""
}