/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types.IDType
import com.ansvia.digaku.model.User
import com.ansvia.digaku.notifications.NonPersistentNotification
import com.ansvia.digaku.utils.RichString._

/**
 * Author: robin (<EMAIL>)
 */

case class ChatPushNotification(message:Message) extends NonPersistentNotification(101) {

    private val id = Digaku.engine.idFactory.createId().asInstanceOf[IDType]

    override def getId: IDType = id

    override lazy val hash: String = message.getId.toString

    override def getDefaultReceivers: Seq[User] = {

        message.getTargetChat match {
            case Some(chat) if chat.kind == ChatKind.Group =>
                chat.getActiveParticipants.filterNot(_.getId == message.getInitiatorId)
            case Some(chat) if chat.kind == ChatKind.PersonToPerson =>
                chat.getParticipants.filterNot(_.getId == message.getInitiatorId)
        }

    }


    override def getCreationTime: IDType = message.getTimestamp


    override def isValid: Boolean = message.getTargetChat.isDefined

    def renderFor(user:User):String = {
        message match {
            case m:MessageText =>

                m.getTargetChat match {
//                    case Some(chat) if chat.kind == ChatKind.Group =>
//                        s"""${chat.getti} ${sender.getName}: ${m.getText.truncate(50)}"""
//                    case Some(chat) if chat.kind == ChatKind.PersonToPerson =>
//
                    case Some(chat) =>
                        s"""${chat.getTitleFor(user)}: ${m.getText.truncate(50)}"""
                    case _ =>
                        "new chat event"
                }

            case m:MessageMedia =>
                m.getTargetChat match {
                    case Some(chat) =>
                        s"""${chat.getTitleFor(user)}: send image to you"""
                    case _ =>
                        "new chat event"
                }

            case m:MessageEvent =>
                m.getTargetChat match {
                    case Some(chat) =>
                        s"""${chat.getTitleFor(user)}: ${m.eventText}"""
                    case _ =>
                        "new chat event"
                }


            case _ =>
                "new chat event"
        }
    }

}
