/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

/**
 * Author: robin (<EMAIL>)
 */

object Json {

    def escapeText(text:String) = {
        text.replaceAll(""""""", """\"""") // " -> \"
    }

}
