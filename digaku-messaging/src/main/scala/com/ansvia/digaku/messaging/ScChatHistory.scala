package com.ansvia.digaku.messaging

import net.liftweb.json.JsonAST.JBool
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.messaging.js.MessageDeliveryStatus
import com.ansvia.digaku.exc.DigakuException
import net.liftweb.json.JsonAST.JInt
import net.liftweb.json._

/**
 * Author: nadir (<EMAIL>)
 */

case class ScChatHistory(userId:Long, scChatId:Long) extends ChatHistoryBase(userId, scChatId) {

    private val key:String = "chat-history-%s-%s".format(userId, scChatId)

    override protected lazy val storeNew = ScChatHistory.seqStore.buildCrypted(key + "-new", () => Chat.engine.generateId())
    override protected lazy val storeOld = ScChatHistory.seqStore.buildCrypted(key + "-old", () => Chat.engine.generateId())
    override protected lazy val counter = Digaku.engine.counterProvider("sc-" + key)

    // Digunakan untuk penyimpanan message ketika belum ada agent
    private lazy val storeQueue = ScChatHistory.seqStore.buildCrypted(key + "-queue", () => Chat.engine.generateId())

    private lazy val scChatOpt = ScChat.getById(scChatId)

    override val unreadText: String = ScChatHistory.unreadText
    override val totalText: String = ScChatHistory.totalText

    def addMessage(message:Message) = {
        assert(message.getId != 0L, "no id")
        assert(message.getInitiatorId != 0L, "initiator id not set")
        assert(message.getTimestamp != 0L, "timestamp not set")

        val isMessageEvent = message.isInstanceOf[MessageEvent]

        if (!isMessageEvent) {
            scChatOpt.foreach { scChat =>
                if (scChat.getAgentId == 0L && message.getInitiatorId == scChat.getInitiatorId &&
                    userId == scChat.getInitiatorId && scChat.getQuestionMessage.exists(_.getId != message.getId)) {
                    storeQueue.insert(message.getId, message.toJsonString)
                }
            }
        }

        storeOld.insert(message.getId, message.toJsonString)

        if (isMessageEvent || userId != message.getInitiatorId) {
            storeNew.insert(message.getId, message.toJsonString)
            counter.increment(unreadText)
            counter.increment(totalText)
        }

    }

    /**
     * Mendapatkan message yang belum di terima
     * oleh agent
     * @return
     */
    def getQueueMessages = {
        storeQueue.getStream(None, None, storeQueue.getCount)
            .map(rv => Message.fromJsonString(rv._2))
    }

    /**
     * Clear message ketika agent sudah mendapatkan
     * message-nya
     */
    def clearQueueMessages(): Unit ={
        storeQueue.clear()
    }

    def markAllMessagesRead() = {
        var count = 0

        // dari store-new dipindah ke store-old
        // hal ini untuk optimasi agar tidak selalu
        // melakukan iterasi ke semua messages setiap
        // kali melakukan mark all messages read
        storeNew.paging(None, None){ (_id, _content) =>
            val msg = Message.fromJsonString(_content)

            if (!msg.isInstanceOf[MessageEvent]){
                userO.foreach(user => msg.setDeliveryStatusFor(user, MessageDeliveryStatus.Read))
            }

            // hapus dari new store
            storeNew.delete(_id)

            storeOld.update(_id, msg.toJsonString)

            count += 1

            true
        }

        // reset unread counter
        counter.decrementBy(unreadText, counter.get(unreadText))

        count
    }

    def markMessagesRead(messageIds: List[Long]) = {
        var count = 0
        var initiatorCount = 0

        // dari store-new dipindah ke store-old
        // hal ini untuk optimasi agar tidak selalu
        // melakukan iterasi ke semua messages setiap
        // kali melakukan mark all messages read
        storeNew.paging(None, None){ (_id, _content) =>
            val msg = Message.fromJsonString(_content)
            if (messageIds.contains(msg.getId)){

                // hapus dari new store
                storeNew.delete(_id)

                if (counter.get(unreadText) > 0){
                    initiatorCount += 1
                }

                if (!msg.isInstanceOf[MessageEvent]){
                    userO.foreach(user => msg.setDeliveryStatusFor(user, MessageDeliveryStatus.Read))
                }

                // masukkan ke old
                storeOld.update(_id, msg.toJsonString)

                count += 1
            }

            true
        }

        counter.decrementBy(unreadText, initiatorCount)

        count
    }
}

object ScChatHistory extends ChatSeqStore {
    override val CFName: String = "sc_chat_store"

    def fromJsonString(jsonText:String):ScChatHistory = {
        try {
            val json = parse(jsonText)

            val userId = json \ "userId" match {
                case JInt(_userId) => _userId.longValue()
                case data => throw new DigakuException(s"unexpected data $data, expecting user id.")
            }

            val chatId = json \ "chatId" match {
                case JInt(_id) => _id.toLong
                case data => throw new DigakuException(s"unexpected data $data, expecting chat id.")
            }

            val active = json \ "active" match {
                case JBool(_state) => _state
                case _ => false
            }

            ScChatHistory(userId, chatId).setActive(active)
        }catch{
            case e:net.liftweb.json.JsonParser.ParseException =>
                error("error parsing: " + jsonText)
                e.printStackTrace()
                throw e
        }
    }

}
