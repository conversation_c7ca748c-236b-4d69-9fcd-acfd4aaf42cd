package com.ansvia.digaku.messaging.statistics

/**
 * Author: nadir (<EMAIL>)
 */

abstract class ScChatStatisticModel(name:String) {
    def toCsv:String

    def getName = name

    /**
      * Normalize string untuk dijadikan column di csv
      * @param str
      * @return
      */
    protected def normStrCsvColumn(str:String): String = {
        str.replaceAll("""[\n|\r]+""", " ").replace(",", ";")
    }
}
