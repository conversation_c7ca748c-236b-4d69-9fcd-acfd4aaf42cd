package com.ansvia.digaku.messaging.utils

import akka.actor.Cancellable
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.helpers.TypeHelpers._
import com.ansvia.digaku.live.LiveObject
import com.ansvia.digaku.messaging.{ScChatSettings, ScChat}

import scala.concurrent.duration._

/**
 * Author: nadir (<EMAIL>)
 */

/**
 * Digunakan untuk auto timeout scchat
 * ketika chat tidak di balas dalam beberapa waktu baik oleh customer atau agent
 */
object ScChatAutoTimeout extends Slf4jLogger {

    implicit private def executor = LiveObject.actorSystem.dispatcher

    private var currentProcessedId = 0L
    private var currentProcessedTime = 0L

    private var schedule:Cancellable = null

    /**
     * Ambil chat yang paling dekat dengan timeout
     * @return
     */
    private def nextTimeoutScChat:Option[(Long, Long)] = {
        val sccs = ScChat.openScChatRegistry.getStream(None, None, ScChat.openScChatRegistry.getCount)

        if (sccs.hasNext) {
            Some(sccs.minBy(_._2.toLongOr(0L))).map(rv => (rv._1.toLong, rv._2.toLongOr(0L)))
        } else {
            None
        }
    }

    /**
     * Reset scheduler untuk men-timeout-kan scchat yang
     * paling dekat dengan timeout
     */
    def resetAutoTimeout(): Unit = synchronized {
        if (ScChatSettings.supportAutoTimeoutChat) {

            if (schedule != null) {
                schedule.cancel()
            }

            nextTimeoutScChat.foreach { rv =>
                setScheduler(rv._1, rv._2)
            }
        }
    }

    /**
     * Set scheduler untuk id scchat dan waktu timeout tertentu
     * @param id id ScChat
     * @param timeout waktu timeout
     */
    private def setScheduler(id:Long, timeout:Long): Unit = {
        this.currentProcessedId = id
        this.currentProcessedTime = timeout
        val nowMillis = Digaku.engine.dateUtils.nowMilis

        if (timeout <= nowMillis) {
            setTimeoutId()
            resetAutoTimeout()
        } else {
            val delay = (timeout - nowMillis).milliseconds

            schedule = Digaku.engine.actorSystem.scheduler.scheduleOnce(delay) {
                setTimeoutId()
                resetAutoTimeout()
            }
        }
    }

    /**
     * Set timeout untuk current processed id
     */
    private def setTimeoutId(): Unit = {
        ScChat.getById(this.currentProcessedId).foreach { scChat =>
            scChat.setTimeout()
        }

        this.currentProcessedId = 0L
        this.currentProcessedTime = 0L
    }

    /**
     * Reset auto timeout untuk scChat id dan waktu timeout tertentu
     * @param chatId ScChat Id
     * @param timeout waktu timeout
     */
    def resetAutoTimeout(chatId:Long, timeout:Long): Unit = {
        synchronized {
            ScChat.getById(chatId).foreach { scChat =>
                if (schedule != null) {
                    if (schedule.isCancelled || this.currentProcessedId == 0L) {
                        resetAutoTimeout()
                    } else {
                        if (currentProcessedId == chatId || timeout < this.currentProcessedTime) {
                            resetAutoTimeout()
                        }
                    }
                } else {
                    resetAutoTimeout()
                }
            }
        }
    }
}
