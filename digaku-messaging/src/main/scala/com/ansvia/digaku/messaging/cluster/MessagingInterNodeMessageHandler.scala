package com.ansvia.digaku.messaging.cluster

import java.util.Date

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.cluster.InternodeMessageHandler
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.messaging.ScChatSettings
import com.ansvia.digaku.messaging.live.OnlineTimeReminder
import com.ansvia.digaku.messaging.utils.ScChatAutoTimeout
import com.ansvia.digaku.utils.AsLong

/**
 * Author: nadir (<EMAIL>)
 */

/**
 * Internode communication untuk Messaging
 */
class MessagingInterNodeMessageHandler extends InternodeMessageHandler
    with DbAccess
    with Slf4jLogger {

    override def handleInternodeMessage(initiator: String, payload: Array[Byte]): Unit = {
        val str = new String(payload)
        str.split("=").toList match {
            case cmd :: params :: Nil =>
                cmd match {
                    case "set-timeout" =>
                        params.split("\\:").toList match {
                            case AsLong(chatId) :: AsLong(timeId) :: Nil =>
                                if (ScChatSettings.supportAutoTimeoutChat) {
                                    debug(s"Read message : $str timeout at ${new Date(timeId)}")
                                    ScChatAutoTimeout.resetAutoTimeout(chatId, timeId)
                                }

                            case _ =>

                        }
                    case _ =>
                }
            case cmd :: Nil =>
                cmd match {
                    case "reset-online-time" =>
                        if (ScChatSettings.supportOnlineTimeReminder) {
                            debug("Read message :" + str)
                            OnlineTimeReminder.setReminder().foreach(_.start())
                        }

                    case _ =>
                }
            case _ =>
        }
    }
}
