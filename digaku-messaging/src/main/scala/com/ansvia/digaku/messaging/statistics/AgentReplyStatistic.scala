package com.ansvia.digaku.messaging.statistics

import com.ansvia.digaku.messaging.JsonSupport
import com.ansvia.digaku.model.User
import net.liftweb.json.JValue
import net.liftweb.json.JsonDSL._
import net.liftweb.json._

/**
 * Author: nadir (<EMAIL>)
 */

/**
 * Digunakan untuk memodelkan data agent reply statistic yang yang nantinya
 * akan disimpan berupa json string
 * see com.ansvia.digaku.shell.commands.scchatstatistic.AgentRepliedStatisticGenerator
 */

case class AgentReplyCount(userId:Long, var count:Int) extends JsonSupport {
    override def toJson: JValue = {
        ("userId" -> userId) ~
            ("count" -> count)
    }
}

case class AgentReplyStatistic(name:String, agentReplies:Seq[AgentReplyCount]) extends ScChatStatisticModel(name) with JsonSupport {
    override def toJson: JValue = {
        ("name" -> name) ~
            ("agentReplies", agentReplies.map(_.toJson))
    }

    override def toCsv: String = {
        val sb = new StringBuilder()

        sb.append("ID,Agent Name,Question Replied\n")
        agentReplies.foreach { arc =>
            User.getById(arc.userId).foreach { user =>
                val name = user.getName.replace(",", "")
                sb.append(s"${arc.userId},$name,${arc.count}\n")
            }
        }

        sb.result().trim
    }
}

object AgentReplyStatistic {
    implicit val f = DefaultFormats

    def fromJsonString(str:String):AgentReplyStatistic = {
        parse(str).extract[AgentReplyStatistic]
    }
}
