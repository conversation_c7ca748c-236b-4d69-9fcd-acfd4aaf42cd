/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import java.lang
import java.util.regex.Pattern

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.exc.{PermissionDeniedException, InvalidParameterException}
import com.ansvia.digaku.messaging.js.MessageDeliveryStatus
import com.ansvia.digaku.model.{Topic, User}
import com.ansvia.digaku.persistence.{SeqStore, SnowFlakeIdFactory}
import com.ansvia.digaku.utils.{AsInt, AsLong}
import com.ansvia.perf.PerfTiming
import ScChatTopic._
import scala.collection.mutable
import scala.collection.mutable.ListBuffer
import com.ansvia.digaku.utils.UserSettings._
import com.ansvia.digaku.helpers.TypeHelpers._
import com.ansvia.digaku.messaging.js.MessageEventText
import com.ansvia.digaku.messaging.js.ScChatStatusType


/**
 * Author: robin (<EMAIL>)
 */

object ChatKind {
    val PersonToPerson = 1
    val Group = 2

    def toStr(kindCode:Int) = {
        kindCode match {
            case PersonToPerson => "person-to-person"
            case Group => "group"
        }
    }

}

object ChatSessionStore {

//    private val hashMap = new mutable.HashMap[IDType, mutable.Queue[String]]()
    private val hashMap = new mutable.HashMap[IDType, SeqStore[java.lang.Long]]()

//    def get(user:User):mutable.Queue[String] = {
//        hashMap.getOrElseUpdate(user.getId, {
//            new mutable.Queue[String]()
//        })
//    }

    private lazy val idgen = SnowFlakeIdFactory.build(Digaku.config.machineId + 2)
    def get(user:User): SeqStore[lang.Long] = {
        get(user.getId)
    }

    def get(userId:IDType): SeqStore[lang.Long] = {
        hashMap.getOrElseUpdate(userId, {
            ChatHistory.seqStore.build("user-notif-store-" + userId,
                () => idgen.createId().asInstanceOf[Long])
        })
    }

    // hanya untuk online user yang dari chat saja, bukan dari web
    lazy val onlineUsers = Digaku.engine.kvStoreProvider.build("chat.online-user-ids")

}



object ImplicitChatUser extends Slf4jLogger {

    implicit class ChatUser(user:User) extends PerfTiming {

        lazy val config = Digaku.engine.kvStoreProvider.build("chat-config-" + user.getId)
        lazy val chatSeqStore = ChatHistory.seqStore.buildCrypted("user-chat-store-" + user.getId, ()=> Chat.engine.generateId())
        lazy val chatSeqStoreIndex = ChatHistory.seqStore.build("user-chat-store-idx-" + user.getId, ()=> Chat.engine.generateId())
        lazy val contactSeqStore = ChatHistory.seqStore.build("user-chat-contact-store-" + user.getId, ()=> Chat.engine.generateId())

        // KvStore config untuk ScChat
        lazy val scChatConfig = Digaku.engine.kvStoreProvider.build("user-sc-chat-config-" + user.getId)

        // SeqStore untuk agent ScChat
        lazy val agentScChatSeqStore = ScChatHistory.seqStore.buildCrypted("agent-sc-chat-store-" + user.getId, ()=> ScChat.engine.generateId())
        lazy val agentScChatSeqStoreIndex = ScChatHistory.seqStore.build("agent-sc-chat-store-idx-" + user.getId, ()=> ScChat.engine.generateId())

        /**
         * SeqStore ini untuk keperluan data statistic, entries didalamnya hanya akan disimpan selama 5 hari
         * see com.ansvia.digaku.shell.commands.scchatstatistic.AgentRepliedStatisticGenerator
         */
        lazy val agentReplySeqStore = ScChatHistory.seqStore.build("agent-reply-store-" + user.getId, ()=> ScChat.engine.generateId())

        private val unauthorizedStr = "Unauthorized"

        def createP2PChat(targetUser:User) = {

            // check dulu apakah sudah ada ref-nya
            user.findP2PChatByUserId(targetUser.getId) match {
                case Some(_chat) =>
                    debug("p2p chat for paired user %s->%s already exists %s, using that one instead".format(user, targetUser, _chat))

                    // reinsert target user to the chat if not already exists
                    List(user, targetUser).foreach { _u =>
                        if (!_chat.isParticipant(_u)) {
                            _chat.addParticipants(_u)
                        }
                    }

                    val chatUser = new ChatUser(user)

                    // apabila di local user registry tidak ada / hilang
                    // maka perlu ditambahkan agar muncul di getChats
                    if (chatUser.chatSeqStoreIndex.get(_chat.getId).isEmpty){
                        val index = chatUser.chatSeqStore.insert(_chat.toJsonString)
                        chatUser.chatSeqStoreIndex.insert(_chat.getId, index.toString)
                    }

                    // activate initiator chat history
                    _chat.getChatHistoryFor(user).foreach { ch =>
                        ch.setActive(true)
                        _chat.updateHistory(ch)
                    }
//                    // khusus untuk target user harusnya gak langsung active
//                    // karena dianggap sebagai inactive participants
//                    _chat.getChatHistoryFor(targetUser).foreach { ch =>
//                        ch.setActive(false)
//                        _chat.updateHistory(ch)
//                    }

                    // tidak perlu create
                    // kembalikan referensi lama yang sudah ada
                    _chat
                case _ =>
                    // kalo belum ada di-create dulu
                    createChat(Set(targetUser), "", ChatKind.PersonToPerson)
            }
        }

        def createGroupChat(participants:Set[User], title:String="", logoUrl:String):Chat = {
            val chat = createChat(participants, title, ChatKind.Group)
            chat.setThumbnail(logoUrl)
            Chat.update(chat)
            chat
        }

        def createChat(participants:Set[User], title:String="", kind:Int):Chat = {

            if (participants.isEmpty){
                throw InvalidParameterException("no participants")
            }

            if (kind == ChatKind.PersonToPerson){
                if (participants.size > 1){
                    throw InvalidParameterException("person-to-person chat cannot have more than 1 participants")
                }
            }


            val chat = Chat.createChat(user, title, kind)

            if (kind == ChatKind.PersonToPerson){
                for (participant <- participants){
                    contactSeqStore.insert(participant.getId, participant.getName + "||" + chat.getId.toString + "||1")
                    participant.contactSeqStore.insert(user.getId, participant.getName + "||" + chat.getId.toString + "||2")
                }
            }

            kind match {
                case ChatKind.PersonToPerson =>

                    // khusus untuk P2P participant lainnya hanya di-add ketika sendMessage
                    // ketika ini belum, jadi status participant-nya masih inactive participant.
//                    val users = (Seq(user) ++ participants).toSet.toList
                    chat.addParticipants(user)
                    chat.addInactiveParticipants(participants.toSeq: _*)

                case ChatKind.Group =>
                    val users = (Seq(user) ++ participants).toSet.toList
                    chat.addParticipants(users: _*)

                    // add chat message event
                    chat.publishEvent(user, "%s membuat grup baru.".format(user.getName))
            }

            chat
        }

        def getChats(fromIdx:Option[Long], toIdx:Option[Long], limit:Int): List[Chat] = {

            val rv = new ListBuffer[Chat]
            val duplicated = new ListBuffer[Long]

            chatSeqStore.paging(fromIdx.map(Long.box), toIdx.map(Long.box)){
                case (index, data) =>

                    val chat = Chat.fromJsonString(data)

                    if (!rv.exists(_.getId == chat.getId)){
                        rv.append(chat)
                    }else{
                        duplicated.append(index)
                    }

                    rv.length < limit
            }

            // @TODO(robin): check this again
            // this is dirty hack to prevent double/duplicate ref to chat
            val dups = duplicated.result()
            if (dups.nonEmpty){
                warn(s"${dups.length} duplicated chat ref in chatSeqStore detected, remove it")
                dups.foreach { index =>
                    chatSeqStore.delete(index)
                }
            }

//            chatSeqStore.getStream(fromIdx.map(Long.box), toIdx.map(Long.box), limit)
//                .toList.map { d =>
//
//                // get from global registry instead of current user registry
//                // where current user is more up-to-date record
//                Chat.fromJsonString(d._2)
//            }

            rv.result()
        }


        /**
         * Cari chat P2P berdasarkan user id target.
         * @param userId id dari user yang akan dicari.
         * @return Option[Chat
         */
        def findP2PChatByUserId(userId:IDType): Option[Chat] = {
            contactSeqStore.get(userId) match {
                case Some(str) =>
                    str.split(Pattern.quote("||")).toList match {
                        case a :: AsLong(chatId) :: AsInt(flags) :: Nil =>
                            Chat.getById(chatId)
                        case _ =>
                            None
                    }
                case _ =>
                    None
            }
        }


        def getChatsCount = {
            user.getCounter.get("chats")
        }


        def addChatUsers(chatRoom:Chat, users:Seq[User]) = {
            chatRoom.addParticipants(users: _*)
        }

        def removeChatUsers(chatRoom:Chat, users:Seq[User]) = {
            chatRoom.removeParticipants(users: _*)
        }


        def getContacts(): Seq[User] = {
            contactSeqStore.getStream(None, None, 1000).flatMap { case (userId, data) =>
                data.split(Pattern.quote("||")).toList match {
                    case a :: AsLong(chatId) :: AsInt(flags) :: Nil =>
                        if ((flags | 1) == flags){
                            User.getById(userId)
                        }else{
                            None
                        }
                    case _ => None
                }
            }.toSeq
        }

        def isInContact(user:User) = {
            contactSeqStore.get(user.getId)
        }

//        private def updateRecentlyAccessedChat(userId:Long, chat:Chat){
//////            val year = Digaku.engine.dateUtils.getCurrentYear
//////            val month = Digaku.engine.dateUtils.getCurrentMonth
////
////            val userV = t.getVertex(userId)
////
////            if (userV != null){
////                userV.pipe.out(COLLECTION_VERTEX)
////                    .has("kind", CollectionVertex.Kind.CHAT)
////                    .outE(PARTICIPANT).has("targetId", chat.getId)
////                    .asInstanceOf[GremPipeEdge].sideEffect { (ed:Edge) =>
////
////                        // update timeOrder ini agar
////                        // setiap chat latest yang baru saja di-update
////                        // akan selalu di barisan paling atas
////                        ed.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)
////                }.iterate()
////            }
//
//            chat.update()
//            // perlu update juga untuk ref di-user-nya, karena datanya redundant
//            // ada dua yang di global chat store dan di user chat store.
////            chatSeqStore.insert(chat.getId, chat.toJsonString)
//
//        }


        def sendMessage(chatRoom:Chat, message:Message): List[Message] = {
            sendMessages(chatRoom, List(message))
        }


        def sendMessages(chatRoom:Chat, messages:List[Message]): List[Message] = {


            for (msg <- messages){

                msg.setId(chatRoom.genMessageId)
                    .setInitiator(user)
                    .setTargetChat(chatRoom)
                    .setTimestamp(Digaku.engine.dateUtils.nowMilis)
                    // untuk current user otomatis sudah Read
                    .setDeliveryStatusFor(user, MessageDeliveryStatus.Read)


                chatRoom.addMessage(msg)


            }

//            user.reload()


            messages
        }


        def sendMessagesText(chatRoom:Chat, messages:List[String]) = {

            sendMessages(chatRoom,
                messages.map( text => new MessageText(0L, user.getId, chatRoom.getId, text, 0L)))

        }


//        /**
//         * helper for build gremlin pipe to get connected chats.
//         * @return GremlinPipe
//         */
//        def getChatGremlinPipe = {
//            user.getVertex.pipe.outE(COLLECTION_VERTEX)
//                .has("kind", CollectionVertex.Kind.CHAT)
//                .inV()
//                .out(PARTICIPANT)
//                .dedup()
//        }


        /**
         * untuk mendapatkan daftar chat-chat yang berisi messages
         * belum dibaca.
         * @return
         */
        def getUnreadMessageChats(): Iterator[Chat] = timing("getUnreadMessageChats"){
            getChats(None, None, 1000).filter(_.getUnreadMessageCount(user) > 0).iterator
        }


        /**
         * Get unread message count
         * @return
         */
        def getUnreadMessageCount:Long = timing("getUnreadMessageCount"){
            getUnreadMessageChats()
                .map(_.getUnreadMessageCount(user))
                .sum
        }

        /**
         * Remove chat dari user, ini tidak meng-drop referensi ke main chat object-nya
         * tapi hanya me-remove ref link ke user-nya.
         * @param chatId id dari chat yang ingin di-remove.
         */
        def removeChat(chatId:Long){

            val cacheKey = s"chat-$chatId-${user.getId}"

            // remove chat dari user khusus mode p2p harusnya tidak menghapus user history di seq-store
            // karena user di mode p2p harus selalu jadi participant
            Chat.getById(chatId) match {
                case Some(chat) =>

                    if (chat.kind == ChatKind.Group){
                        chat.removeUserHistory(user)
                    }else{
                        // otherwise clear only
                        chat.getChatHistoryFor(user).foreach { ch =>
                            ch.setActive(false)
                            chat.updateHistory(ch)
                            ch.clear()
                        }
                    }

//                    Chat.consistencyHack.remove(cacheKey)
                case _ =>
            }

//            chatSeqStoreIndex.get(chatId) match {
//                case Some(AsLong(index)) =>
//                    chatSeqStore.delete(index)
//
//                    // search and destroy!
//                    var indexToDeletes = new ListBuffer[IDType]
//
//                    chatSeqStore.paging(None, None){ case (idx, b) =>
//                        val c = Chat.fromJsonString(b)
//                        if (c.getId == chatId){
//                            indexToDeletes += idx
//                        }
//                        true
//                    }
//
//                    indexToDeletes.result().foreach(idx => chatSeqStore.delete(idx))
//                    debug(s"deleting index $index from chatSeqStore ref by $chatId")
//                case _ =>
//                    warn(s"no chat $chatId in chatSeqStoreIndex")
//            }
//            chatSeqStoreIndex.delete(chatId)

            // search and destroy!
            var indexToDeletes = new ListBuffer[IDType]

            chatSeqStore.paging(None, None){ case (idx, b) =>
                val c = Chat.fromJsonString(b)
                if (c.getId == chatId){
                    indexToDeletes += idx
                }
                true
            }

            indexToDeletes.result().foreach(idx => chatSeqStore.delete(idx))
            debug(s"deleting chat ref for chat with id $chatId from chatSeqStore")
            chatSeqStoreIndex.delete(chatId)

//            Chat.consistencyHack.remove(cacheKey)

            debug(s"decrement chats ref for user $user")

            user.getCounter.decrement("chats")

        }


        /* SCCHAT */

        /**
         * Membuat SC Chat, Create SC Chat ini hanya bisa dibuat oleh customers
         * dan tidak bisa di buat oleh agent see[[com.ansvia.digaku.utils.UserSettings.UserSettingsWrapper#isAgentTopic()]]
         * @param topicId topic ID see [[com.ansvia.digaku.model.Topic]]
         * @param title
         * @param question
         * @return
         */
        def createScChat(topicId:Long, title:String, question:String): ScChat = {
            Topic.getById(topicId).map { topic =>

                if (user.isAgentTopic) {
                    throw PermissionDeniedException("Create sc chat is not for agents.")
                }

                if (getActiveScChat.isDefined) {
                    throw InvalidParameterException(s"User have active chat.")
                }

                if (question.trim.length < 3 && question.trim.length > 500) {
                    throw InvalidParameterException(s"Question minimum 3 and maximum 500 character.")
                }

                // Build atau create chat
                val scChat = ScChat.createChat(user, topicId, title, question)

                scChat.addChatHistoryFor(user)

                // Tambahkan chat ke initiator nya.
                setActiveScChat(scChat)

                val firstMessage = new MessageText(question)

                firstMessage.setId(scChat.genMessageId)
                    .setInitiator(user)
                    .setTargetChatId(scChat.getId)
                    .setChatType(ChatType.SCCHAT)
                    .setTimestamp(Digaku.engine.dateUtils.nowMilis)
                    // untuk current user otomatis sudah Read
                    .setDeliveryStatusFor(user, MessageDeliveryStatus.Read)

                scChat.addMessage(firstMessage)

                scChat.setQuestionMessage(firstMessage)

                // Tambahkan ke topic dan avalible agent jika ada
                topic.addScChat(scChat)

                scChat
            }.getOrElse {
                throw InvalidParameterException(s"Topic not exist with id: $topicId.")
            }
        }

        /**
         * Set user sebagai agent pada sebuah chat
         * ini hanya untuk agent di sebuah topic.
         * @param scChat
         * @return
         */
        def asAgentScChat(scChat: ScChat) = {
            scChat.setAgent(user)

            val scStatus = ScChatStatus(ScChatStatusType.OPEN)
                .setAgentId(user.getId)
                .setCreationTime(Digaku.engine.dateUtils.nowMilis)
                .setScChatId(scChat.getId)
                .setId(scChat.genMessageId)

            scChat.addStatus(scStatus)
            scChat.addChatHistoryFor(user)

            scChat.getQuestionMessage.foreach { msg =>
                scChat.addMessageFor(user, msg)
            }

            scChat.update()

            scChat.getInitiator.foreach { initiator =>
                scChat.getChatHistoryFor(initiator).foreach { ch =>
                    ch.getQueueMessages.foreach { queueMsg =>
                        scChat.addMessageFor(user, queueMsg)
                    }

                    ch.clearQueueMessages()
                }

                scChat.addEventMessage(initiator, MessageEventText.CONNECT_AGENT, user.getId,
                    Map.empty[String, String], initiator)

                scChat.addEventMessage(initiator, MessageEventText.TIMEOUT_WARNING, user.getId,
                    Map.empty[String, String], initiator)
            }

            scChat.setAutoTimeout(Digaku.engine.dateUtils.nowMilis + ScChatSettings.agentTimeout)

            // Tandai chat belum dibuka oleh agent
            scChatConfig.set(s"view-chat-${scChat.getId}", scChat.getId.toString)

            scChat
        }

        /**
         * View chat digunakan untuk menambahkan event bahwa seorang agent telah melihat sebuah chat.
         * method ini hanya di gunakan untuk seorang agent.
         * @param scChat
         */
        def viewScChat(scChat: ScChat): Unit = {
            if (scChat.getAgentId == user.getId) {
                scChatConfig.getOption[String](s"view-chat-${scChat.getId}").foreach { rv =>
                    if (scChat.getAgentId == user.getId) {
                        if (scChat.getLastStatus.exists(_.status == ScChatStatusType.OPEN)) {
                            scChat.addEventMessage(user, MessageEventText.VIEW_CHAT, scChat.getId,
                                Map.empty[String, String], user)

                            scChat.addEventMessage(user, MessageEventText.TIMEOUT_WARNING, scChat.getId,
                                Map.empty[String, String], user)
                        }
                    }

                    scChatConfig.delete(s"view-chat-${scChat.getId}")
                }
            }
        }

        /**
         * Set active SC Chat digunakan untuk menandai bahwa
         * customer telah memiliki active sc chat
         * @param scChat
         */
        def setActiveScChat(scChat: ScChat):Unit = {
            if (scChat.getInitiatorId == user.getId) {
                scChatConfig.set("active-chat", scChat.getId)
            }
        }

        /**
         * Mendapatkan active chat untuk customer/user
         * @return
         */
        def getActiveScChat:Option[ScChat] = {
            val scChatId = scChatConfig.get("active-chat", 0L)
            ScChat.getById(scChatId)
        }

        /**
         * Suspend agent dalam waktu tertentu
         * see [[com.ansvia.digaku.messaging.ScChatSettings.agentTimeoutSuspend()]]
         */
        def suspendAgent(): Unit = {
            if (user.isAgentTopic) {
                val suspendTime = Digaku.engine.dateUtils.nowMilis + ScChatSettings.agentTimeoutSuspend

                debug("Suspend agent %s : %s".format(user, suspendTime))
                scChatConfig.set("suspend-time", suspendTime)
            }
        }

        /**
         * Unsuspend user agent
         */
        def unsuspendAgent(): Unit ={
            scChatConfig.delete("suspend-time")
        }

        /**
         * Check apakah agent dalam keadaan di-suspend
         * @return
         */
        def isSuspendedAgent:Boolean = {
            val suspendTime = scChatConfig.get("suspend-time", 0L)
            suspendTime > 0L && suspendTime > Digaku.engine.dateUtils.nowMilis
        }

        /**
         * Remove active chat untuk customer/user
         */
        def removeActiveScChat(): Unit ={
            getActiveScChat.foreach { scc =>
                if(scc.getInitiatorId != user.getId) {
                    throw PermissionDeniedException("Only the initiator can remove active chat")
                }

                scc.getLastStatus match {
                    case Some(scStatus) =>
                        scStatus.status match {
                            case ScChatStatusType.OPEN | ScChatStatusType.QUEUE | ScChatStatusType.AGENT_TIMEOUT =>
                                throw PermissionDeniedException("Cannot remove active chat with open, agent timeout or in queue status.")
                            case _ =>
                        }

                    case _ =>
                        throw PermissionDeniedException("Cannot remove active chat with status open or in queue.")
                }
            }

            scChatConfig.delete("active-chat")
        }

        /**
         * Digunakan untuk mendapatkan list SC Chat seorang agent.
         * @param fromIdx
         * @param toIdx
         * @param limit
         * @return
         */
        def getAgentScChats(fromIdx:Option[Long], toIdx:Option[Long], limit:Int):List[ScChat] = {

            val fromId = fromIdx.flatMap(id => agentScChatSeqStoreIndex.get(id)).map(_.toLong)
            val toId = toIdx.flatMap(id => agentScChatSeqStoreIndex.get(id)).map(_.toLong)


            val rv = new ListBuffer[ScChat]
            val duplicated = new ListBuffer[Long]

            agentScChatSeqStore.paging(fromId.map(Long.box), toId.map(Long.box)){
                case (index, data) =>

                    val scChat =  ScChat.fromJsonString(data)

                    if (!rv.exists(_.getId == scChat.getId)){
                        rv.append(scChat)
                    }else{
                        duplicated.append(index)
                    }

                    rv.length < limit
            }

            // @TODO(robin): check this again
            // this is dirty hack to prevent double/duplicate ref to chat
            val dups = duplicated.result()
            if (dups.nonEmpty){
                warn(s"${dups.length} duplicated sc-chat ref in agentScChatSeqStore detected, remove it")
                dups.foreach { index =>
                    agentScChatSeqStore.delete(index)
                }
            }


//            agentScChatSeqStore.getStream(fromId.map(Long.box), toId.map(Long.box), limit)
//                .map(rv => ScChat.fromJsonString(rv._2)).toList

            rv.result()

        }

        /**
         * Digunakan untuk agent menutup sebuah SC Chat
         * @param scChatId
         * @param statusType see [[com.ansvia.digaku.messaging.js.ScChatStatusType]]
         * @param tags
         * @param reason
         * @return
         */
        def closeScChat(scChatId:Long, statusType:Int, tags:String, reason:String, question:String, categoryId:Long) = {

            val scChat = ScChat.getById(scChatId).get

            val lastStatus = scChat.getLastStatus

            if (!scChat.getTopic.exists(_.isAgent(user))) {
                throw InvalidParameterException("Only topic agent can close this chat.")
            }

            if (lastStatus.isEmpty) {
                throw InvalidParameterException("Can't close chat which is still in the queue")
            }

            statusType match {
                case ScChatStatusType.OPEN | ScChatStatusType.QUEUE =>
                    throw InvalidParameterException("Invalid close type")

                case ScChatStatusType.UNSOLVED =>
                    require(question.trim.nonEmpty, "Question cannot be empty")
                    require(reason.trim.nonEmpty, "Reason cannot be empty")

                case ScChatStatusType.SOLVED =>
                    require(question.trim.nonEmpty, "Question cannot be empty")
                    require(reason.trim.nonEmpty, "Answer cannot be empty")

                case ScChatStatusType.MARK_AS_SOLVED =>
                    require(reason.trim.nonEmpty, "Reason cannot be empty")

                    if (!lastStatus.exists(_.status == ScChatStatusType.UNSOLVED)) {
                        throw InvalidParameterException("Invalid close type")
                    }

                case _ =>
            }

            lastStatus.foreach { st =>
                st.status match {
                    case ScChatStatusType.OPEN =>
                    case ScChatStatusType.QUEUE =>
                        throw InvalidParameterException("Can't close chat which is still in the queue")
                    case ScChatStatusType.UNSOLVED =>
                        if (statusType != ScChatStatusType.MARK_AS_SOLVED) {
                            throw InvalidParameterException("Invalid close type")
                        }
                    case ScChatStatusType.AGENT_TIMEOUT =>
                        throw InvalidParameterException("Can't close chat with status agent timeout")

                    case x =>
                        throw InvalidParameterException("Chat already closed")
                }
            }

            if (statusType == ScChatStatusType.UNSOLVED || statusType == ScChatStatusType.SOLVED) {
                val tagsArr = tags.split(",").map(_.trim).filter(_.nonEmpty)

                if (tagsArr.isEmpty) {
                    throw InvalidParameterException("Tags cannot be empty")
                } else {
                    scChat.setTags(tagsArr.mkString(","))
                    scChat.setClosedQuestion(question)
                    scChat.setCategoryId(categoryId)
                }
            }

            val scStatus = ScChatStatus(statusType)
                .setAgentId(user.getId)
                .setCreationTime(Digaku.engine.dateUtils.nowMilis)
                .setScChatId(scChat.getId)
                .setId(scChat.genMessageId)
                .setReason(reason)

            scChat.addStatus(scStatus)

            scChat.update()

            scChat.addEventMessage(user, MessageEventText.CLOSE_CHAT, scChat.getInitiatorId, Map("statusType" -> statusType.toString))

            // Swap scchat yang statusnya masih open harus paling atas
            getAgentScChats(None, None, 5).reverse.foreach { sc =>
                sc.getLastStatus.foreach { st =>
                    st.status match {
                        case ScChatStatusType.OPEN =>
                            sc.update()
                        case _ =>
                    }
                }
            }

            scChat.getTopic.map(_.nextScChatFor(user))

            ScChat.openScChatRegistry.delete(scChat.getId)

            scChat
        }

        /**
         * Digunakan untuk mengirim message pada chat tertentu
         * @param scChat
         * @param msg
         * @return
         */
        def sendScMessage(scChat: ScChat, msg: Message): Message = {
            val nowMillis = Digaku.engine.dateUtils.nowMilis

            if (!msg.isInstanceOf[MessageEvent]) {
                val isInitiatorScChat = user.getId == scChat.getInitiatorId
                val lastStatus = scChat.getLastStatus.map(_.status)

                if (lastStatus.isDefined) {
                    lastStatus.get match {
                        case ScChatStatusType.OPEN =>
                        case ScChatStatusType.AGENT_TIMEOUT =>
                            if (!isInitiatorScChat) {
                                throw PermissionDeniedException(unauthorizedStr)
                            }
                        case ScChatStatusType.QUEUE =>
                            if (!isInitiatorScChat) {
                                throw PermissionDeniedException(unauthorizedStr)
                            }
                        case _ =>
                            throw PermissionDeniedException(unauthorizedStr)
                    }
                } else {
                    if (!isInitiatorScChat) {
                        throw PermissionDeniedException(unauthorizedStr)
                    }
                }

                if (!scChat.getParticipantIds.contains(user.getId)) {
                    throw PermissionDeniedException(unauthorizedStr)
                }

                if (user.isAgentTopic) {
                    /**
                     * Data ini untuk keperluan statistic hanya akan disimpan 5 hari
                     * see com.ansvia.digaku.shell.commands.scchatstatistic.AgentRepliedStatisticGenerator
                     */
                    agentReplySeqStore.insert(scChat.getId, nowMillis.toString, 432000)
                }
            }
            msg.setId(scChat.genMessageId)
                .setInitiator(user)
                .setTargetChatId(scChat.getId)
                .setTimestamp(nowMillis)
                .setChatType(ChatType.SCCHAT)
                .setDeliveryStatusFor(user, MessageDeliveryStatus.Read)

            scChat.addMessage(msg)
        }

        /**
         * Render event text berdasarkan perspektif user untuk model
         * MessageEvent yang disesuaikan dari property eventText
         * dan di-match-kan dengan com.ansvia.digaku.messaging.MessageEventText
         * @param scChat
         * @param mEvent
         * @return
         */
        def renderEventText(scChat: ScChat, mEvent: MessageEvent): String = {
            val targetUserName = mEvent.metaData.get("targetUserId").flatMap { idStr =>
                User.getById(idStr.toLongOr(0L)).map(_.getName)
            }

            val messageInitiator = user.getId == mEvent.getInitiatorId

            val messageInitiatorName = if (messageInitiator) "Anda" else mEvent.getInitiator.map(_.getName).getOrElse("-")

            mEvent.eventText match {
                case MessageEventText.CONNECT_AGENT =>
                    s"$messageInitiatorName telah terhubung dengan ${targetUserName.getOrElse("Agent")}"

                case MessageEventText.TIMEOUT_WARNING =>
                    val timeText = if (mEvent.getInitiatorId == scChat.getInitiatorId) {
                        "15 Menit"
                    } else {
                        "30 Menit"
                    }

                    s"Jika $messageInitiatorName tidak membalas chat dalam waktu $timeText maka chat akan ditutup"

                case MessageEventText.AGENT_TIMEOUT =>
                    if (messageInitiator) {
                        s"Chat telah ditutup karena tidak ada balasan dari Anda"
                    } else {
                        s"Tidak ada tanggapan dari $messageInitiatorName, chat dialihkan ke agent lain"
                    }

                case MessageEventText.CUSTOMER_TIMEOUT =>
                    if (messageInitiator) {
                        s"Chat telah ditutup karena tidak ada balasan dari Anda"
                    } else {
                        s"Chat ditutup karena tidak ada tanggapan dari $messageInitiatorName"
                    }

                case MessageEventText.VIEW_CHAT =>
                    s"$messageInitiatorName telah membuka pesan"

                case MessageEventText.CLOSE_CHAT =>
                    val closedType = mEvent.metaData.get("statusType").map { typeStr =>
                        val statusType = typeStr.toIntOr(ScChatStatusType.UNSOLVED)

                        val statusStr = statusType match {
                            case ScChatStatusType.SOLVED =>
                                "Solved"
                            case ScChatStatusType.MARK_AS_SOLVED =>
                                "Mark as Solved"
                            case ScChatStatusType.UNSOLVED =>
                                "Unsolved"
                            case _ => "-"
                        }

                        statusStr
                    }

                    s"$messageInitiatorName telah mengubah status chat menjadi ${closedType.getOrElse("-")}"

                case et => et
            }
        }

    }

}
