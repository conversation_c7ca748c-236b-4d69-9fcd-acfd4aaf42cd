package com.ansvia.digaku.messaging

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.model.User
import net.liftweb.json._
import com.ansvia.digaku.utils.AsLong
import net.liftweb.json.JsonDSL._

/**
 * Author: nadir (<EMAIL>)
 */

case class ScChatStatus(status:Int) extends JsonSupport with Slf4jLogger {
    private var _id = 0L
    private var scChatId = 0L
    private var agentId = 0L
    private var creationTime = 0L
    private var reason = ""

    def getId = this._id

    def setId(id:Long)= {
        this._id = id

        this
    }

    def getScChatId = this.scChatId
    def setScChatId(scChatId:Long) = {
        this.scChatId = scChatId

        this
    }

    def getAgentId = this.agentId
    def getAgent = User.getById(this.agentId)
    def setAgentId(agentId:Long) = {
        this.agentId = agentId

        this
    }

    def getCreationTime = this.creationTime
    def setCreationTime(creationTime:Long) = {
        this.creationTime = creationTime

        this
    }

    def getReason = this.reason
    def setReason(reason:String) = {
        this.reason = reason

        this
    }

    override def toJson: JValue = {
        require(_id != 0L, "cannot convert to json, id still 0, unsaved chat object?")
        require(this.agentId != 0L, "initiator must set")
        require(this.scChatId != 0L, "topic must set")

        ("id" -> this._id) ~ ("status" -> this.status) ~ ("scChatId" -> this.scChatId) ~
            ("agentId" -> this.agentId) ~ ("creationTime" -> this.creationTime) ~
            ("reason" -> this.reason)
    }
}

object ScChatStatus {

    def fromJsonString(jsonText:String): ScChatStatus ={
        try {
            val json = parse(jsonText)

            val id = json \ "id" match {
                case JInt(_id) => _id.longValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting ScChatStatus id. source: $jsonText")
            }

            val status = json \ "status" match {
                case JInt(_status) => _status.intValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting ScChatStatus status. source: $jsonText")
            }

            val scChatId = json \ "scChatId" match {
                case JInt(_id) => _id.longValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting ScChatStatus scChatId. source: $jsonText")
            }

            val agentId = json \ "agentId" match {
                case JInt(_id) => _id.longValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting ScChatStatus agentId. source: $jsonText")
            }

            val creationTime = json \ "creationTime" match {
                case JInt(_id) => _id.longValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting ScChatStatus creationTime. source: $jsonText")
            }

            val reason = json \ "reason" match {
                case JString(_reason) => _reason
                case _ => ""
            }

            ScChatStatus(status)
                .setId(id).setScChatId(scChatId)
                .setAgentId(agentId)
                .setCreationTime(creationTime)
                .setReason(reason)

        } catch {
            case e:net.liftweb.json.JsonParser.ParseException =>
                error("error parsing: " + jsonText)
                e.printStackTrace()
                throw e
        }
    }
}
