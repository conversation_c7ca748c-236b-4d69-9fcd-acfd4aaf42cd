/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import java.util.Date

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types.IDType
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.messaging.js.{DeliveryStatusInfo, MessageDeliveryStatus, MessageKind}
import com.ansvia.digaku.model.User
import com.ansvia.digaku.utils.{AsInt, AsLong}
import net.liftweb.json.JsonAST.{JInt, JString}
import net.liftweb.json.JsonDSL._
import net.liftweb.json._

//import org.msgpack.annotation.Message

// scalastyle:off magic.number number.of.methods

/**
 * Author: robin (<EMAIL>)
 */



//trait MessagePackSupport {
//    def toMessagePack:Array[Byte]
//}
//
//
//trait StoreDataFormatEntity

//@org.msgpack.annotation.Message
//class StoreDataFormatMessageText(var id:Long, var writerName:String, var writerId:Long, var text:String, var ts:Long) extends StoreDataFormatEntity {
//    def this() = this(0,"", 0L, "",0L)
//}

//@org.msgpack.annotation.Message
//class StoreDataFormatMessageMedia(var id:Long, var writer:String, var writerId:Long, var media:String, var ts:Long, var status:Byte) extends StoreDataFormatEntity {
//    def this() = this(0,"","", 0L,0x00)
//}


sealed abstract class Message(id:Long)
    extends JsonSupport with Slf4jLogger {

    private lazy val store = Digaku.engine.kvStoreProvider.build("chat-message-settings-" + getId)

    private var _id:Long = id
    private var initiatorId:Long = 0
    private var targetChatId:Long = 0
    private var timestamp:Long = Digaku.engine.dateUtils.nowMilis
    private var consumers:List[IDType] = Nil

    /**
     * Chat type see [[com.ansvia.digaku.messaging.ChatType]]
     */
    private var chatType:Int = ChatType.CHAT

    override def hashCode(): Int = id.toString.hashCode

    def getId = this._id

    def setId(__id:Long) = {
        assert(this._id == 0L, "id already set to %d, cannot overwrite to %d".format(this._id, __id))
        this._id = __id
        this
    }

    def accumulateStatus(): MessageDeliveryStatus = {
        getDeliveryStatusInfoByIdsFor(consumers.filterNot(id => this.initiatorId == id))
            .sortBy(_.status.getCode).map(_.status)
            .headOption.getOrElse(MessageDeliveryStatus.Sent)
    }

    def getInitiatorId = initiatorId
    def getInitiator:Option[User] = User.getById(initiatorId)

    def setInitiator(user: User):this.type = {
        setInitiatorId(user.getId)
    }
    def setInitiatorId(id: IDType):this.type = {
        this.initiatorId = id
        this
    }

    def getTargetChatId = targetChatId
    def getTargetChat:Option[Chat] = Chat.getById(targetChatId)

    def setTargetChat(chat:Chat):this.type = {
        setTargetChatId(chat.getId)
    }
    def setTargetChatId(id:Long):this.type = {
        this.targetChatId = id
        this
    }

    def getTimestamp = timestamp
    def setTimestamp(ts:Long):this.type = {
        this.timestamp = ts
        this
    }

    def loadConsumers():this.type = {
        this.consumers = store.get("consumer-user-ids", "").split(",")
            .filter(_.length > 0).map(_.toLong).toList
        this
    }

    def getConsumers = consumers

    def addConsumers(consumers:User*): Message = {
        addConsumersByIds(consumers.map(_.getId): _*)
        this
    }
    def addConsumersByIds(consumerIds:IDType*): Message = {
        this.consumers = (this.consumers ++ consumerIds).distinct
        store.set("consumer-user-ids", this.consumers.map(_.toString).mkString(","))
        this
    }

    def getChatType = chatType

    def setChatType(chatType:Int): this.type ={
        this.chatType = chatType
        this
    }

    import Message.deliveryStatusStr

    def getDeliveryStatuses: List[(User, DeliveryStatusInfo)] = {
        for {
            id <- consumers
            user <- User.getById(id)
        } yield (user,
            getDeliveryStatusInfoFor(user))
    }

    def getDeliveryStatusInfoFor(user:User):DeliveryStatusInfo = {
        val s = store.get(deliveryStatusStr + user.getId, "%s:0".format(MessageDeliveryStatus.Sent.getCode))
        s.split("\\:").toList match {
            case AsInt(byteCode) :: AsLong(time) :: Nil =>
                DeliveryStatusInfo(MessageDeliveryStatus.fromByte(byteCode.toByte), time)
            case x =>
                throw new DigakuException("Invalid delivery status data format schema, it's look like your db schema is out dated " +
                    "you must reset your db schema first.")
        }
    }

    def getDeliveryStatusInfoByIdsFor(userIds:List[Long]):List[DeliveryStatusInfo] = {
        val getAll = store.getAll.toList
        userIds.map { id =>
            getAll.find(rv => rv._1 == deliveryStatusStr + id).map { rv =>
                rv._2.toString.split("\\:").toList match {
                    case AsInt(byteCode) :: AsLong(time) :: Nil =>
                        DeliveryStatusInfo(MessageDeliveryStatus.fromByte(byteCode.toByte), time)
                    case x =>
                        throw new DigakuException("Invalid delivery status data format schema, it's look like your db schema is out dated " +
                            "you must reset your db schema first.")
                }
            }.getOrElse(DeliveryStatusInfo(MessageDeliveryStatus.Sent, 0))
        }
    }

    def getDeliveryStatusInfoByIdFor(userId:Long):DeliveryStatusInfo = {
        val s = store.get(deliveryStatusStr + userId, "%s:0".format(MessageDeliveryStatus.Sent.getCode))
        s.split("\\:").toList match {
            case AsInt(byteCode) :: AsLong(time) :: Nil =>
                DeliveryStatusInfo(MessageDeliveryStatus.fromByte(byteCode.toByte), time)
            case x =>
                throw new DigakuException("Invalid delivery status data format schema, it's look like your db schema is out dated " +
                    "you must reset your db schema first.")
        }
    }

    /**
     * Tandai status deliver untuk user tertentu.
     *
     * @param user adalah user penerima yang berimbas pada perubahan status delivery-nya, apabila user ini
     *             telah membaca maka msgStatus-nya diset [[MessageDeliveryStatus.Read]]
     * @param msgStatus delivery status constanta.
     * @return
     */
    def setDeliveryStatusFor(user:User, msgStatus:MessageDeliveryStatus):this.type = {

        val data = "%s:%s".format(msgStatus.getCode.toInt, Digaku.engine.dateUtils.nowMilis)
        store.set(deliveryStatusStr + user.getId, data)

//        debug("setting delivery status update for %s -> %s".format(user, msgStatus.getName))

        chatType match {
            case ChatType.CHAT =>
                getTargetChat.foreach { chat =>
                    Digaku.engine.eventStream.emit(DeliveryStatusUpdateEvent(user, chat, List(this.getId), msgStatus))
                }
            case ChatType.SCCHAT =>
                ScChat.getById(targetChatId).foreach { scChat =>
                    Digaku.engine.eventStream.emit(ScChatDeliveryStatusUpdateEvent(user, scChat, List(this.getId), msgStatus))
                }
        }

        this
    }

    def getKind:Byte

//    def toStoreDataFormat:StoreDataFormatEntity

//    override def toMessagePack: Array[Byte]
}

object Message extends Slf4jLogger {

    val deliveryStatusStr = "delivery-status-"


//    def getDeliveryStatus(chatId:Long, userId:IDType, msgId:Long) = {
//        val store = Digaku.engine.kvStoreProvider.build("chat-message-settings-" + chatId)
//        MessageStatus.fromByte(store.get(deliveryStatusStr + userId, MessageStatus.Sent.getCode))
//    }

//
//    def fromMessagePack(data:Array[Byte]):Message = {
//        val _data = data
//
//        val textSign = MessageKind.TEXT_BYTE_SIGN
////        val mediaSign = MessageKind.MEDIA_BYTE_SIGN
//
//        if (_data(0) == textSign(0) && _data(1) == textSign(1) && _data(2) == textSign(2) && _data(3) == textSign(3)){
//            ScalaMessagePack.read[StoreDataFormatMessageText](data.slice(4, data.length)) match {
//                case m:StoreDataFormatMessageText =>
//                    val writer = {
//                        User.getByName(m.writerName).getOrElse {
//                            throw NotExistsException("user not exists: " + m.writerName)
//                        }
//                    }
//                    // fix ini, sementara targetChatId di-nol-kan
//                    new MessageText(m.id, writer.getId, 0L, m.text, m.ts)
//            }
////        }else if (_data(0) == mediaSign(0) && _data(1) == mediaSign(1) && _data(2) == mediaSign(2) && _data(3) == mediaSign(3)){
////            ScalaMessagePack.read[StoreDataFormatMessageMedia](data.slice(4, data.length)) match {
////                case m:StoreDataFormatMessageMedia =>
////                    val writer = {
////                        User.getByName(m.writer).getOrElse {
////                            throw NotExistsException("user not exists: " + m.writer)
////                        }
////                    }
////                    // fix ini, sementara targetChatId di-nol-kan
////                    new MessageMedia(m.id, writer.getId, 0L, m.media, m.ts)
////                        .setStatus(MessageStatus.fromByte(m.status))
////            }
//        }else{
//            throw new DigakuException("invalid metadata: " + _data.slice(0, 3).toList.map(x => "%02x".format(x)))
//        }
//
//
//    }

//    lazy val messageSeqStore: SeqStore[java.lang.Long] = ChatHistory.seqStore.build("global-message-seq-store",
//        ()=> engine.generateId())
//
//    def getById(id:Long) = {
//        // @TODO(robin): code here
//    }
//

    def fromJsonString(jsonText:String):Message = {
        try {
            val json = parse(jsonText)

            val id = json \ "id" match {
                case JInt(_id) => _id.longValue()
                case _ =>
                    throw new DigakuException("no id in fromJsonString when parsing for Message. src: " + jsonText)
            }

            val initiatorId = json \ "initiatorId" match {
                case JInt(_initiatorId) => _initiatorId.longValue()
                case _ => 0L
            }
            val kind = json \ "kind" match {
                case JInt(_kind) => _kind.toByte
                case x =>
                    warn("unknown message kind: " + x)
                    MessageKind.UNKNOWN
            }

            val ts = json \ "ts" match {
                case JInt(_ts) => _ts.toLong
                case _ => 0L
            }
            val targetChatId = json \ "targetChatId" match {
                case JInt(_id) => _id.toLong
                case _ => 0L
            }

            val chatType = json \ "chatType" match {
                case JInt(_chatType) => _chatType.intValue()
                case _ => ChatType.CHAT
            }

            kind match {
                case MessageKind.TEXT =>
//                    debug("got message text")
                    json \ "text" match {
                        case JString(text) =>
                            new MessageText(id, initiatorId, targetChatId, text, ts)
                                .setChatType(chatType)
                                .loadConsumers()

                        case _ =>
                            throw new DigakuException("no text in json message data. src: " + jsonText)
//                            json \ "media" match {
//                                case JString(media) =>
//                                    new MessageText(id, initiatorId, targetChatId, media, ts)
//                                        .loadConsumers()
//                                //                                .setStatus(status)
//                                case x =>
//                                    throw new DigakuException("no text nor media filed in json message data. src: " + jsonText)
//                            }

                    }
                case MessageKind.EVENT =>
//                    debug("got message event")

                    val metaData: Map[String, String] = json \ "metaData" match {
                        case o:JObject => o.values.map { case (a,b) =>  (a, b.toString) }
                        case _ => Map.empty[String, String]
                    }

                    json \ "eventText" match {
                        case JString(text) =>
                            new MessageEvent(id, text)
                                .setInitiatorId(initiatorId)
                                .setTimestamp(ts)
                                .setTargetChatId(targetChatId)
                                .setChatType(chatType)
                                .loadConsumers()
                                .setMetaData(metaData)
                        case x =>
                            error("Unexpected data structure for ChatEvent, got: " + x + ". src: " + jsonText)
                            throw new DigakuException("Unexpected data structure")
                    }

                case MessageKind.MEDIA =>

                    val media = json \ "media" match {
                        case JString(t) => t
                        case x =>
                            throw new DigakuException(s"no media in message data. src: $jsonText")
                    }
                    val title = json \ "title" match {
                        case JString(t) => t
                        case x =>
                            //                            throw new DigakuException(s"no title in message data. src: $jsonText")
                            ""
                    }
                    val mediaType = json \ "mediaType" match {
                        case JInt(d) => d.intValue()
                        case x =>
                            throw new DigakuException(s"got unexpected mediaType: $x from json data src: $jsonText")
                    }

                    new MessageMedia(id, initiatorId, targetChatId, media, mediaType, title, ts)
                        .setChatType(chatType)
                        .loadConsumers()

                case x =>
                    throw new DigakuException(s"Unknown chat kind $x retrieved from db. src: " + jsonText)
            }


        }catch{
            case e:net.liftweb.json.JsonParser.ParseException =>
                error("error parsing: " + jsonText)
                e.printStackTrace()
                throw e
        }
    }
}


trait JsonSupport {
    def toJson:JValue
    def toJsonString:String = compact(render(toJson))
}

// dipindah ke shared lib messaging js
//object MessageKind {
//    val UNKNOWN:Byte = 0x00
//    val TEXT:Byte = 0x01
//    val MEDIA:Byte = 0x02
//    val EVENT:Byte = 0x03
//}

case class MessageText(id:Long, initiatorId:Long, targetChatId:Long, text:String,
                       timestamp:Long)
    extends Message(id) {

    def this(text:String) = {
        this(0L, 0L, 0L, text, 0L)
    }

    setInitiatorId(initiatorId)
    setTargetChatId(targetChatId)
    setTimestamp(timestamp)


    override def toString: String = "Text(%s, '%s')".format(getId, text)

    def getText = text


    def getKind = MessageKind.TEXT


    override def toJson: JValue = {

        ("id" -> getId) ~
            ("v" -> MessageText.VERSION) ~
            ("initiatorId" -> getInitiatorId) ~
            ("chatType" -> getChatType) ~
            ("text" -> getText) ~
            ("targetChatId" -> getTargetChatId) ~
            ("ts" -> getTimestamp) ~
            ("kind" -> getKind.toInt)

    }

}

object MessageText {
    val VERSION = 1
}



case class MessageEvent(id:Long, eventText:String) extends Message(id) {

    import scala.collection.mutable

    type me = MessageEvent

    var metaData:Map[String, String] = Map[String, String]()

    override def toString: String = "MessageEvent(%s, '%s')".format(getId, eventText)

    override def getKind: Byte = MessageKind.EVENT

    override def toJson: JValue = {
        ("id" -> getId) ~
            ("v" -> MessageEvent.VERSION) ~
            ("initiatorId" -> getInitiatorId) ~
            ("chatType" -> getChatType) ~
            ("eventText" -> eventText) ~
            ("targetChatId" -> getTargetChatId) ~
            ("ts" -> getTimestamp) ~
            ("kind" -> getKind.toInt) ~
            ("metaData" -> metaData)

    }

    def setMetaData(md:Map[String, String]) = {
        this.metaData = md
        this
    }

}

object MessageEvent {
    val VERSION = 1
}

class MessageMedia(id:Long, writerId:Long, targetChatId:Long,
                   media:String, mediaType:Int, val title:String, timestamp:Long)
    extends Message(id){

    setInitiatorId(writerId)
    setTargetChatId(targetChatId)
    setTimestamp(timestamp)

    def getKind = MessageKind.MEDIA
    def getMedia = media
    def getMediaType = mediaType

    override def toJson: JValue = {
        ("id" -> getId) ~
            ("v" -> MessageMedia.VERSION) ~
            ("initiatorId" -> getInitiatorId) ~
            ("chatType" -> getChatType) ~
            ("mediaType" -> mediaType) ~
            ("media" -> media) ~
            ("title" -> title) ~
            ("targetChatId" -> getTargetChatId) ~
            ("ts" -> getTimestamp) ~
            ("kind" -> getKind.toInt)
    }
}


object MessageMedia {
    val VERSION = 1
}

