/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import java.security.SecureRandom
import java.security.cert.X509Certificate
import javax.net.ssl.{KeyManagerFactory, X509TrustManager, SSLContext, SSLSocketFactory}

import com.ansvia.digaku.model.SexType
import com.ansvia.digaku.thrift.messaging.PushMessage.AsyncClient
import com.ansvia.digaku.thrift.messaging.{MessagingService, PushMessage, User => UserThrift}
import com.ansvia.digaku.validator.EmailValidator
import com.ansvia.perf.PerfTiming
import com.ansvia.util.idgen.TokenIdGenerator
import jline.ConsoleReader
import org.apache.thrift.async.{AsyncMethodCallback, TAsyncClientManager}
import org.apache.thrift.protocol.{TBinaryProtocol, TCompactProtocol}
import org.apache.thrift.transport._

import scala.collection.JavaConversions._
import scala.util.Random

// scalastyle:off

/**
 * Author: robin (<EMAIL>)
 */

object AcceptAllTrustManager extends X509TrustManager {
    override def getAcceptedIssuers: Array[X509Certificate] = null

    override def checkClientTrusted(x509Certificates: Array[X509Certificate], s: String): Unit = {
        // check ignored
    }

    override def checkServerTrusted(x509Certificates: Array[X509Certificate], s: String): Unit = {
        // check ignored
    }
}

object ChatTestClient extends UserCredentialAccess with ActiveDigakuEngine with PerfTiming {

    var USING_ZLIB = false
    var USING_SSL = false

    lazy val usage = {
        """  usage: App [OPT] [METHOD] [API-KEY]"
          |
          |OPT can be:
          |     + --ssl     - to enable ssl
          |     + --zlib    - to enable zlib
          |
          |example: App create_chat \"access_token:rXMb2rJaCI2BewQ5kMy456rewLVrSTYGw78WuI7JcHgAzt\" [OTHER-USER-ID]
          |example: App create_chat_single \"access_token:rXMb2rJaCI2BewQ5kMy456rewLVrSTYGw78WuI7JcHgAzt\" [OTHER-USER-ID]
          |example: App publish \"access_token:rXMb2rJaCI2BewQ5kMy456rewLVrSTYGw78WuI7JcHgAzt\" [OTHER-USER-ID]
          |example: App consume \"private_key:rXMb2rJaCI2BewQ5kMy456rewLVrSTYGw78WuI7JcHgAzt\"
          |example: App interactive \"private_key:rXMb2rJaCI2BewQ5kMy456rewLVrSTYGw78WuI7JcHgAzt\" [CHAT-ID]
          |example: App benchmark \"private_key:rXMb2rJaCI2BewQ5kMy456rewLVrSTYGw78WuI7JcHgAzt\"
          |example: App active_scchat \"access_token:rXMb2rJaCI2BewQ5kMy456rewLVrSTYGw78WuI7JcHgAzt\"
          |example: App create_scchat \"access_token:rXMb2rJaCI2BewQ5kMy456rewLVrSTYGw78WuI7JcHgAzt\" [TOPIC-ID]
          |example: App create_message_scchat \"access_token:rXMb2rJaCI2BewQ5kMy456rewLVrSTYGw78WuI7JcHgAzt\"
          |example: App version \"private_key:rXMb2rJaCI2BewQ5kMy456rewLVrSTYGw78WuI7JcHgAzt\"""".stripMargin
    }


    def main(args: Array[String]) {

        USING_ZLIB = args.contains("--zlib")
        USING_SSL = args.contains("--ssl")

        println("using zlib: " + USING_ZLIB)
        println("using ssl: " + USING_SSL)

        def getSocket(port:Int) = {
            val socket = {
                if (USING_SSL){
//                    val params = new TSSLTransportFactory.TSSLTransportParameters()
//                    params.setTrustStore("etc/ssl/truststore.jks", "123456")

                    val km = KeyManagerFactory.getInstance("SunX509")
                    val ctx = SSLContext.getInstance("TLS")

                    ctx.init(Array(), Array(AcceptAllTrustManager), new SecureRandom())
                    val factory = ctx.getSocketFactory

                    val _socket = factory.createSocket("127.0.0.1", port)


//                    TSSLTransportFactory.getClientSocket("127.0.0.1", port, 10000)
                    new TSocket(_socket)
                }else{
                    new TSocket("127.0.0.1", port)
                }
            }
            socket
        }

        val client =
            if (USING_ZLIB){
                val socket = getSocket(5566)
                //        val transport = new TFramedTransport(socket, 2048)
                val transport = new TZlibTransport(socket, 9)
                //        val protocol = new TBinaryProtocol(transport, true, true)
                val protocol = new TCompactProtocol(transport)
                transport.open()
                new MessagingService.Client(protocol)
            }else{
                val socket = getSocket(5588)
                val transport = new TFramedTransport(socket, 2048)
                //        val transport = new TZlibTransport(socket, 9)
//                        val protocol = new TBinaryProtocol(socket, true, true)
                val protocol = new TCompactProtocol(transport)
//                transport.open()
                new MessagingService.Client(protocol)
            }


        if (args.length < 2){
            println(usage)
            return
        }

        // zufar = zD1Ve8CoYUKuyYc7fZmHThZiKNhVI4sfVH0sNYVtpyKjeh

        val method = args(0)
        val apiKey = args(1)

        method match {
            case "version" =>

                val result = client.getServiceInfo(apiKey)

                println("app id: " + result.getServiceInfo.getAppId)
                println("version: " + result.getServiceInfo.getVersion)
                println("core version: " + result.getServiceInfo.getDigakuCoreVersion)
                println("GIT hash: " + result.getServiceInfo.getGitHash)
                println("GIT branch: " + result.getServiceInfo.getGitBranch)

            case "create_chat" =>

                if (args.length < 2) {
                    println(usage)
                    return
                }

                println("+ creating chat")
                val userId = args(2).toLong
                val result = client.createChatGroup(apiKey, "ansvia team", List(Long.box(userId)), "")
                if (result.isSetError){
                    error(result.getError.getDesc)
                    return
                }
                val chat = result.getChatInfo.getChat

                if (chat.getId == 0){
                    println("failed")
                }else{
                    println("  created, chat id: " + chat.getId)
                }


            case "publish" =>
                println("+ creating chat: ansvia team ...")
                val userId = args(2).toLong
                val result = client.createChatGroup(apiKey, "ansvia team", List(Long.box(userId)), "'")
                val chat = result.getChatInfo.getChat

                if (chat.getId == 0){
                    println("  cannot create chat")
                }else{
                    //        val message = new MessageThrift(0L, 0x00, userO.get.getId, userO.get.getName, 0x01)

                    println("  chat created with id: " + chat.getId)

                    println("+ sending message...")

                    val error = client.sendMessages(apiKey, chat.getId, List("hello :)"))

                    println("  code: %s, desc: %s".format(error.getErrorCode, error.getDesc))

                    println("+ get messages...")

                    val rv = client.getMessages(apiKey, chat.getId, 0L, 0L, 10)

                    if (rv.isSetError){
                        println("  error: " + rv.getError.getErrorCode + ", " + rv.getError.getDesc)
                    }else{
                        println("  messages: ")
                        rv.getMessages.foreach(m => println("    %d. %s: `%s`".format(m.getId, m.getSenderName, m.getText)))

                        val message = rv.getMessages.head

                        println("+ get message info for %s...".format(message.getId))

                        val messageInfo = client.getMessageInfo(apiKey, chat.getId, message.getId)

                        if (messageInfo.isSetError){
                            val error = messageInfo.getError
                            println("  code: %s, desc: %s".format(error.getErrorCode, error.getDesc))
                        }else{
                            val rss = messageInfo.getMessageInfo.getReceiverStatus

                            for (rs <- rss){
                                println("  user: %s , status: %s".format(rs.getReceiver.getName, rs.getStatus))
                            }
                        }
                    }
                }

            case "consume" =>
                println("+ listening for new events...")

                val transport = new TNonblockingSocket("127.0.0.1", 5577)
                val clientManager = new TAsyncClientManager()
//                val protocolFactory = new TBinaryProtocol.Factory()
                val protocolFactory = new TCompactProtocol.Factory()

                val pushListener = new PushMessage.AsyncClient(protocolFactory, clientManager, transport)

//                var done = false

                val callback:AsyncMethodCallback[PushMessage.AsyncClient.listen_call] = new AsyncMethodCallback[PushMessage.AsyncClient.listen_call]() {
                    override def onError(e: Exception): Unit = {
                        println("error: " + e.getMessage)
                        e.printStackTrace()
                    }

                    override def onComplete(_push: PushMessage.AsyncClient.listen_call): Unit = {
                        val push = _push.getResult
                        if (push.isSetError && push.getError.getErrorCode != 200){
                            println("error: " + push.getError.getErrorCode + " - " + push.getError.getDesc)
                        }
                        if (push.isSetUserTyping){
                            println("...%s is typing...".format(push.getUserTyping))
                        }
                        if (push.isSetNewMessages){
                            val newChatMessages = push.getNewMessages
                            println("got new chat count: " + newChatMessages.getCount)
                            for (pm <- newChatMessages.getChatMessages){
                                val msg = pm.getMessage
                                println(" chat[%s] -> %d. %s: `%s`".format(pm.getChatId, msg.getId, msg.getSenderName, msg.getText))
                            }
                        }
                        if (push.isSetArchiveTopic) {
                            println("got event archive topic: " + push.getArchiveTopic)
                        }

                        pushListener.listen(apiKey, this)
                    }
                }
//
//                while (!done){
                    pushListener.listen(apiKey, callback)
//                    Thread.sleep(1500)
//                    print("loop")
//                }

                while(true){
                    Thread.sleep(1500)
                }

//
//                val chats = client.getChats(apiKey, 0, 10)
//                chats.getChats.find(_.getTitle == "ansvia team")
//                    .foreach { chat =>
//
//                        val rv = client.getMessages(apiKey, chat.getId, 0, 0, 20)
//                        if (rv.isSetError){
//                            println("  error: " + rv.getError.getErrorCode + ", " + rv.getError.getDesc)
//                        }else{
//                            println("  messages: ")
//                            rv.getMessages.foreach(m => println("    %d. %s: `%s`".format(m.getId, m.getSenderName, m.getText)))
//                        }
//
//                    }
            case "interactive" => {
                setupDigakuEngine("digaku-messaging-test.conf")

                if (args.length > 2){
                    val chatId = args(2).toLong

                    val user = getUserByApiKeyOrError(apiKey)

                    {
                        // SETUP LISTENER
                        println("+ listening for new events...")


                        var pushListener: AsyncClient = null

                        def rebuildConnection(){
                            try {
                                val _socket = new TNonblockingSocket("127.0.0.1", 5577)
                                val _clientManager = new TAsyncClientManager()
                                //                        val protocolFactory = new TBinaryProtocol.Factory(true, true)
                                val _protocolFactory = new TCompactProtocol.Factory()
                                pushListener = new PushMessage.AsyncClient(_protocolFactory, _clientManager, _socket)
                            }catch{
                                case e:Throwable =>
                                    error("%s: %s".format(e.getClass.getCanonicalName, e.getMessage))
                            }
                        }

                        rebuildConnection()

                        def robustListen(apiKey:String, _callback:AsyncMethodCallback[PushMessage.AsyncClient.listen_call]){
                            try {
                                pushListener.listen(apiKey, _callback)
                            }catch{
                                case e if e.getMessage.contains("Client has an error") =>
                                    warn(s"connection lost, retrying...")
                                    Thread.sleep( /*backoff*/ 1000)
                                    rebuildConnection()
                                    robustListen(apiKey, _callback)
                            }
                        }


                        val callback:AsyncMethodCallback[PushMessage.AsyncClient.listen_call] = new AsyncMethodCallback[PushMessage.AsyncClient.listen_call]() {
                            override def onError(e: Exception): Unit = {
                                println("error: " + e.getMessage)
                                if (e.getMessage != "Connection refused"){
                                    e.printStackTrace()
                                }
                                robustListen(apiKey, this)
                            }

                            override def onComplete(_push: PushMessage.AsyncClient.listen_call): Unit = {
                                val push = _push.getResult
                                if (push.isSetError && push.getError.getErrorCode != 200){
                                    println("error: " + push.getError.getErrorCode + " - " + push.getError.getDesc)
                                }
                                if (push.isSetUserTyping){
                                    println("%s typing...".format(push.getUserTyping.getKnownUserName))
                                }
                                if (push.isSetNewMessages){
                                    val newChatMessages = push.getNewMessages
                                    println("got new chat count: " + newChatMessages.getCount)
                                    for (pm <- newChatMessages.getChatMessages){
                                        val msg = pm.getMessage
                                        if (msg.getSenderId != user.getId){
                                            println(" chat[%s] -> %d. %s: `%s`".format(pm.getChatId, msg.getId, msg.getSenderName, msg.getText))
                                        }
                                    }
                                }
                                robustListen(apiKey, this)
                            }
                        }

                        robustListen(apiKey, callback)
                    }


                    println("Entering interactive chat interface, type: `exit` to exit")

                    print("participants: ")

                    val onlineUserIds = client.getOnlineUsersIds(apiKey)

                    val rv = client.getChatParticipants(apiKey, chatId)
                    if (rv.isSetError){
                        error(rv.getError.getDesc)
                    }else{
                        println(rv.getUsers.map { u =>
                            val onlineFlag = onlineUserIds.contains(u.getId) match {
                                case true => " [online]"
                                case _ => ""
                            }
                            u.getName + onlineFlag
                        }.mkString(", "))

                        println("recently messages:")
                        client.getLatestMessages(apiKey, chatId)
                            .getMessages
                            .foreach { msg =>
                                println("%s: %s".format(msg.senderName, msg.getText))
                            }

                        val consoleReader = new ConsoleReader()

                        var done = false
                        while(!done){
                            consoleReader.readLine("%s > ".format(user.getName)) match {
                                case null =>
                                    done = true
                                case "q" | "exit" | "close" =>
                                    done = true
                                case "" =>
                                    client.setTyping(apiKey, chatId)
                                case text =>
                                    client.sendMessages(apiKey, chatId, List(text.trim()))
                            }
                        }
                    }

                }else{
                    println(usage)
                }


            }
            case "benchmark" =>

                setupDigakuEngine("digaku-messaging-test.conf")

                println("Starting benchmark process...")

                //if (args.length > 2) {
                    //val chatId = args(2).toLong

                EmailValidator.mxRecordCheck = false

                println("generating dummy users...")

                val participants = for (i <- 1 to 10) yield {
                    com.ansvia.digaku.model.User.getByName(s"user$i") match {
                        case Some(user) => user
                        case _ =>
                            com.ansvia.digaku.model.User.create(s"user$i", s"user$<EMAIL>", SexType.MALE, "01/05/1986", "123123", "", activated = true, emailValidator = true)
                    }
                }

                val participantIds = participants.map(_.getId).toList.map(Long.box)

                println("generated dummy users: " + participants.map(_.getName).mkString(", "))

                val result = client.createChatGroup(apiKey, "Benchmark test", participantIds, "")

                if (result.isSetError){
                    error(result.getError.getDesc + " (" + result.getError.getErrorCode + ")")
                    return
                }

                val chat = result.getChatInfo.getChat

                println(s"chat generated: id=${chat.getId}, name=${chat.getTitle}")

                val randomStringGenerator = new TokenIdGenerator
                val texts = for (t <- (0 to 100)) yield randomStringGenerator.nextId()


                // just a simple throttle test
                assert(client.createChatSingle(apiKey, participantIds(2)).isSetChatInfo)
                assert(client.createChatSingle(apiKey, participantIds(2)).isSetError)
                assert(client.createChatSingle(apiKey, participantIds(2)).isSetError)
                assert(client.createChatSingle(apiKey, participantIds(2)).isSetError)
                assert(client.createChatSingle(apiKey, participantIds(2)).getError.getErrorCode == 200) // harusnya ini gak ngefek apa-apa karena kena throttle
                assert(client.createChatSingle(apiKey, participantIds(2)).getError.getErrorCode == 200) // harusnya ini gak ngefek apa-apa karena kena throttle

                Thread.sleep(5000)

                // tapi ini ngefek
                assert(client.createChatSingle(apiKey, participantIds(2)).isSetChatInfo)

                timing("Benchmark: write"){
                    for (i <- 0 to 100){
                        client.sendMessages(apiKey, chat.getId, List(texts(Random.nextInt(100)) + " " + texts(Random.nextInt(100))))
                    }
                }
                timing("Benchmark: read"){
                    for (i <- 0 to 100){
                        client.getLatestMessages(apiKey, chat.getId)
                    }
                }

                println("done.")

            case "active_scchat" =>
                val result = client.getActiveScChat(apiKey)

                if (result.isSetError) {
                    println("-- ERROR: " + result.getError.desc)
                } else {
                    println(result.getScChat)
                }

            case "create_scchat" =>
                val topicId = args(2).toLong
                val result = client.createScChat(apiKey, topicId, "Title scchat from ChatTestClient", "Desc scchat from ChatTestClient")

                if (result.isSetError) {
                    println("-- ERROR: " + result.getError.desc)
                } else {
                    println(result.getScChat)
                }

            case "create_message_scchat" =>
                val result = client.sendMessagesScChat(apiKey, "Message scchat from ChatTestClient")

                if (result.isSetError) {
                    println("-- ERROR: " + result.getError.desc)
                } else {
                    println(result.getMessage)
                }


            case x => println("unknown method: " + x)
        }
    }
}
