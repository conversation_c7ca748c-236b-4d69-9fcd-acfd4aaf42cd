package com.ansvia.digaku.messaging.live

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.helpers.TypeHelpers._
import com.ansvia.digaku.live.{LiveObject, LiveObjectRegistry}
import com.ansvia.digaku.messaging.ScChatTopic._
import com.ansvia.digaku.messaging.{ChatSessionStore, ScChatOnlineTimeEvent, ScChatSettings}
import com.ansvia.digaku.model.Topic
import com.ansvia.graph.BlueprintsWrapper._
import com.twitter.util.Future
import org.joda.time.{DateTime, DateTimeZone}

import scala.concurrent.ExecutionContext
import scala.concurrent.duration._


/**
 * Author: nadir (<EMAIL>)
 */

/**
 * Online time reminder ini digunakan untuk mengirimkan status online/offline ScChat
 * ketika setatus-nya akan di online-kan maka live objek ini juga akan men-distribusi-kan
 * queue ScChat kepada setiap agent yang available.
 * @param setTimeOnline true jikan ingin di-online-kan dan set false ketika akan di-offline-kan
 * @param initialDelay
 * @param interval
 */
class OnlineTimeReminderLive(setTimeOnline:Boolean, initialDelay:FiniteDuration, interval:FiniteDuration) extends LiveObject
    with DbAccess with Slf4jLogger {

    implicit private def ec:ExecutionContext = scala.concurrent.ExecutionContext.global

    override val period: FiniteDuration = interval
    override lazy val initial:FiniteDuration = initialDelay

    override lazy val name:String = this.getClass.getSimpleName.replace("$","") + s"($setTimeOnline)"

    private var inProcessing = false

    /**
     * Distribusikan semua ScChat yang masih dalam antrian
     */
    def distributeScChat(): Unit ={
        Future {
            Topic.paging(None, None, 100) { case (p, vs) =>
                vs.flatMap(_.toCC[Topic]).foreach { topic =>
                    topic.distributeQueueScChat()
                }

                true
            }
        }
    }

    def run(){
        synchronized {
            if (!inProcessing){
                try {
                    inProcessing = true

                    debug("Start Online %s : %s".format(setTimeOnline, Digaku.engine.dateUtils.getCurrentTime()))

                    ChatSessionStore.onlineUsers.getAll.foreach { u =>
                        if (u._2 == "1") {
                            ChatSessionStore.get(u._1.toLongOr(0L)).insert("|online-time|:" + setTimeOnline, 300)
                        }
                    }

                    Digaku.engine.eventStream.emit(ScChatOnlineTimeEvent(setTimeOnline))

                    if (setTimeOnline) {
                        distributeScChat()
                    }
                } finally {
                    inProcessing = false
                }
            }
        }
    }
}

object OnlineTimeReminder extends Slf4jLogger {

    import ScChatSettings._

    private var onlineReminder:Option[OnlineTimeReminderLive] = None
    private var offlineReminder:Option[OnlineTimeReminderLive] = None

    /**
     * Reset OnlineTimeReminderLive dengan yang baru, ketika inisialisasi atau
     * ada pengubahan waktu online
     * @return
     */
    def setReminder(): Seq[OnlineTimeReminderLive] = {

        if (ScChatSettings.supportOnlineTimeReminder) {
            debug("Reset ScChat Online Time")

            val oneDays = 1.days

            val now = new DateTime().withZone(DateTimeZone.forID(getTimezoneOnlineTime))

            val (startHours, startMinutes, startSeconds) = timeSpliter(getStartOnlineTime)
            val startDate = now.withTime(startHours, startMinutes, startSeconds, 0)

            val fOnlineInitial = if (now.isBefore(startDate)) {
                (startDate.getMillis - now.getMillis).milliseconds
            } else {
                (startDate.plusDays(1).getMillis - now.getMillis).milliseconds
            }

            val (endHours, endMinutes, endSeconds) = timeSpliter(getEndOnlineTime)
            val endDate = now.withTime(endHours, endMinutes, endSeconds, 0)

            val fOfflineInitial = if (now.isBefore(endDate)) {
                Math.abs(endDate.getMillis - now.getMillis).milliseconds
            } else {
                (endDate.plusDays(1).getMillis - now.getMillis).milliseconds
            }

            onlineReminder.foreach { lo =>
                lo.stop()
                LiveObjectRegistry.remove(lo)
            }

            val newOnlineReminder = new OnlineTimeReminderLive(true, fOnlineInitial, oneDays)
            onlineReminder = Some(newOnlineReminder)

            offlineReminder.foreach { lo =>
                lo.stop()
                LiveObjectRegistry.remove(lo)
            }

            val newOfflineReminder = new OnlineTimeReminderLive(false, fOfflineInitial, oneDays)
            offlineReminder = Some(newOfflineReminder)

            Seq(newOnlineReminder, newOfflineReminder)
        } else {
            Seq.empty[OnlineTimeReminderLive]
        }
    }
}
