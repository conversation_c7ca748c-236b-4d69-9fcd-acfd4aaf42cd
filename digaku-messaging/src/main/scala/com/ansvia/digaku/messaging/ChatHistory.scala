/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import java.lang

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.messaging.js.MessageDeliveryStatus
import com.ansvia.digaku.model
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.persistence.{CounterProviderOperator, CassandraBackedSeqStore, SeqStore}
import com.netflix.astyanax.model.ColumnFamily
import com.netflix.astyanax.serializers.{ByteSerializer, LongSerializer, StringSerializer}
import net.liftweb.json.JsonAST.JInt
import net.liftweb.json.JsonDSL._
import net.liftweb.json._
import ImplicitChatUser._

/**
 * Author: robin (<EMAIL>)
 */



abstract class ChatHistoryBase(userId:Long, chatId:Long) extends JsonSupport with Slf4jLogger {

    protected val storeNew:SeqStore[java.lang.Long]
    protected val storeOld:SeqStore[java.lang.Long]
    protected val counter:CounterProviderOperator

    protected var _active = false
    lazy val userO = model.User.getById(userId)

    val unreadText:String
    val totalText:String

    def isActive = _active
    def setActive(state:Boolean):this.type = {
        this._active = state
        this
    }


    override def toJson: JValue = {
        ("userId" -> userId) ~ ("chatId" -> chatId) ~ ("active" -> _active)
    }

    def addMessage(message:Message):Unit

    private def getStreamInternal(seqStore: SeqStore[lang.Long], fromId:Option[Long], toId:Option[Long], limit:Int): List[Message] = {
        seqStore.getStream(fromId.map(Long.box), toId.map(Long.box), limit)
            .flatMap {
                case (uuid, data) =>
                    try {
                        Some(Message.fromJsonString(data))
                    }catch{
                        case x:Throwable =>
                            x.printStackTrace()
                            None
                    }
            }.toList
    }

    def getMessages(fromId:Option[Long], toId:Option[Long], limit:Int):List[Message] = {
        getStreamInternal(storeOld, fromId, toId, limit)
    }


    def getLastMessages = {
        getMessages(None, None, 20)
    }

    /**
     * Get single message.
     * @param msgId id of message to get.
     * @return
     */
    def getMessage(msgId: Long):Option[Message] = {
        storeOld.get(msgId).map(Message.fromJsonString)
    }

    /**
     * Mark messages as received
     * @param messageIds id of messages.
     * @return
     */
    def ackMessages(messageIds: List[Long]) = {
        var count = 0

        for ( msgId <- messageIds ){
            storeNew.get(msgId) match {
                case Some(dataStr) =>
                    val msg = Message.fromJsonString(dataStr)

                    userO.foreach(user => msg.setDeliveryStatusFor(user, MessageDeliveryStatus.Delivered))

                    if (storeNew.get(msgId).isDefined){
                        storeNew.update(msgId, msg.toJsonString)
                    }

                    count += 1
                case _ =>
            }
        }

        count
    }

    def markAllMessagesRead():Int

    def markMessagesRead(messageIds: List[Long]):Int

    def getTotalMessageCount = {
        counter.get(totalText)
    }

    def getUnreadMessageCount = {
        counter.get(unreadText)
    }

    def getUnreadMessages(fromId:Option[Long], toId:Option[Long], limit:Int): List[Message] = {
        storeNew.getStream(fromId.map(Long.box), toId.map(Long.box), limit)
            .toList.map(d => Message.fromJsonString(d._2))
    }

    def getCounter = counter
}

case class ChatHistory(userId:Long, chatId:Long) extends ChatHistoryBase(userId, chatId) {

    val key = "chat-history-%s-%s".format(userId, chatId)

    override protected lazy val storeNew = ChatHistory.seqStore.buildCrypted(key + "-new", () => Chat.engine.generateId())
    override protected lazy val storeOld = ChatHistory.seqStore.buildCrypted(key + "-old", () => Chat.engine.generateId())
    override protected lazy val counter = Digaku.engine.counterProvider(key)

    override val unreadText: String = ChatHistory.unreadText
    override val totalText: String = ChatHistory.totalText

    def addMessage(message:Message) = {
        assert(message.getId != 0L, "no id")
        assert(message.getInitiatorId != 0L, "initiator id not set")
        assert(message.getTimestamp != 0L, "timestamp not set")

        /**
         * Setiap message baru akan ditaruh di storeNew
         * ini dimaksudkan untuk optimasi.
         */
        if (!message.isInstanceOf[MessageEvent]){
            storeNew.insert(message.getId, message.toJsonString)
        }

        // taruh juga di store old, karena stream pengambilan utama ada di store old
        storeOld.insert(message.getId, message.toJsonString)

        // jangan hitung message event
        if (!message.isInstanceOf[MessageEvent]){
            counter.increment(totalText)
        }

        if (userId != message.getInitiatorId){
            if (!message.isInstanceOf[MessageEvent]){
                counter.increment(unreadText)
            }
        }
    }

    def markAllMessagesRead() = {
        var count = 0

        // dari store-new dipindah ke store-old
        // hal ini untuk optimasi agar tidak selalu
        // melakukan iterasi ke semua messages setiap
        // kali melakukan mark all messages read
        storeNew.paging(None, None){ (_id, _content) =>
            val msg = Message.fromJsonString(_content)

            if (!msg.isInstanceOf[MessageEvent]){
                // hapus dari new store
                storeNew.delete(_id)

//                if (counter.get(unreadText) > 0){
//                    if (msg.getInitiatorId != userId){
//                        counter.decrement(unreadText)
//                    }
//                }

                // masukkan ke old
                userO.foreach(user => msg.setDeliveryStatusFor(user, MessageDeliveryStatus.Read))

                storeOld.update(_id, msg.toJsonString)

                count += 1
            }

            true
        }

        // reset unread counter
        counter.decrementBy(unreadText, counter.get(unreadText))

        count
    }

    def markMessagesRead(messageIds: List[Long]) = {
        var count = 0
        var initiatorCount = 0

        // dari store-new dipindah ke store-old
        // hal ini untuk optimasi agar tidak selalu
        // melakukan iterasi ke semua messages setiap
        // kali melakukan mark all messages read
        storeNew.paging(None, None){ (_id, _content) =>
            val msg = Message.fromJsonString(_content)
            if (messageIds.contains(msg.getId)){

                if (!msg.isInstanceOf[MessageEvent]){

                    // hapus dari new store
                    storeNew.delete(_id)

                    if (counter.get(unreadText) > 0){
                        if (msg.getInitiatorId != userId) {
                            //                            counter.decrement(unreadText)
                            initiatorCount += 1
                        }
                    }

                    userO.foreach(user => msg.setDeliveryStatusFor(user, MessageDeliveryStatus.Read))

                    // masukkan ke old
                    storeOld.update(_id, msg.toJsonString)

                    count += 1
                }
            }
            true
        }

        counter.decrementBy(unreadText, initiatorCount)
        count
    }

    def clear(){
        storeNew.clear()
        storeOld.clear()

        // reset counter
        counter.incrementBy(unreadText, -counter.get(unreadText))
        counter.incrementBy(totalText, -counter.get(totalText))
    }


//    def drop(){
//        clear()
//
//        // hapus dari history-nya user
//        userO match {
//            case Some(user) =>
//                user.seq
//            case None =>
//        }
//
//        // harusnya jangan disini tapi di layer user-nya
////        // hapus dari seq store-nya user
////        userO match {
////            case Some(_user) =>
////                _user.removeChat(chatId)
////            case _ =>
////        }
//
//    }
}

object ChatHistory extends Slf4jLogger with ChatSeqStore {

    override val CFName: String =  "chat_store"

    def fromJsonString(jsonText:String):ChatHistory = {
        try {
            val json = parse(jsonText)

            val userId = json \ "userId" match {
                case JInt(_userId) => _userId.longValue()
                case data => throw new DigakuException(s"unexpected data $data, expecting user id.")
            }

            val chatId = json \ "chatId" match {
                case JInt(_id) => _id.toLong
                case data => throw new DigakuException(s"unexpected data $data, expecting chat id.")
            }

            val active = json \ "active" match {
                case JBool(_state) => _state
                case _ => false
            }

            ChatHistory(userId, chatId).setActive(active)

        }catch{
            case e:net.liftweb.json.JsonParser.ParseException =>
                error("error parsing: " + jsonText)
                e.printStackTrace()
                throw e
        }
    }
}
