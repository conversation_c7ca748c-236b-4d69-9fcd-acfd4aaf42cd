/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.digaku.model.User

/**
 * Author: fajr (<EMAIL>)
 */

/**
 * Digunakan untuk push notifikasi typing,
 * karena dapat berulang kali, maka tidak ada hash nya dan tidak di throttle
 * @param chat
 * @param userSource
 * @param userTargetO
 * @param message
 * @param hiddenMessage
 * @param messageIdsO
 * @param notifType
 */
case class ChatTypingPushNotification(chat:Chat, userSource:User, userTargetO:Option[User], message:String,
                                      hiddenMessage:String, messageIdsO:Option[List[Long]], notifType:String)
    extends ChatPushNotificationBase(chat:Chat, userSource:User, userTargetO:Option[User], message:String,
                                       hiddenMessage:String, messageIdsO:Option[List[Long]], notifType:String) {

    override lazy val hash: String = ""

}
