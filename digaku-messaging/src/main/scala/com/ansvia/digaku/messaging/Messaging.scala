/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.notifications.EventToNotificationMapper

/**
 * Author: robin (<EMAIL>)
 */

object Messaging extends Slf4jLogger {

    val VERSION = "0.1.15" // auto generated, don't change this.

    def init(){

        debug("initialize Messaging engine...")

        DatabaseUtil.index()

        // inject custom mapper to notification listener
        Digaku.engine.getNotificationListener match {
            case mapper:EventToNotificationMapper => mapper.customMappers.append {
                case ev:ChatMessageSentEvent =>
                    debug(s"event handled: $ev, msg: ${ev.msg}")

                    (ev.msg.getInitiator, ev.msg) match {
                        case (Some(sender), message:MessageText) if message.getTargetChat.isDefined && message.getInitiator.isDefined =>
                            debug(s"sender: $sender, message: $message")
                            Seq(ChatPushNotification(message))
//                            Seq(ChatGeneralPushNotification(message.getTargetChat.get, message.getInitiator.get, None, "", "", None, "message-sent-event"))
                        case (Some(sender), message:MessageMedia) if message.getTargetChat.isDefined && message.getInitiator.isDefined =>
                            debug(s"sender: $sender, message: $message")
                            Seq(ChatPushNotification(message))

                        case (Some(sender), message:MessageEvent) if message.getTargetChat.isDefined && message.getInitiator.isDefined =>
                            debug(s"sender: $sender, message: $message")
                            Seq(ChatPushNotification(message))

                        case _ =>
                            Nil
                    }
                // Tidak perlu mengirimkan typing notification karena sudah dihandle oleh thrift listener
                // dan typing notif ini hanya ditampilkan ketika apps sedang dijalankan, jadi otomatis apps pasti listen thrift
                // dan mendapatkan event typing.
                //                case te:TypingEvent =>
//                    debug(s"event handled: $te, chat room: ${te.chat}, user: ${te.user}, text: ${te.text}")
//
//                    (te.chat, te.user, te.text) match {
//                        case (chat, user, message:String) =>
//                            debug(s"user: $user, message: $message")
//                            //yang dikirimkan adalah username yg melakukan typing
//                            val messageToSend = user.name
//                            Seq(ChatTypingPushNotification(chat, user, None, "",  messageToSend, None, "typing-event"))
//                        case _ =>
//                            Nil
//                    }
                case rpe:RemoveParticipantEvent =>
                    Seq(ChatGeneralPushNotification(rpe.chat, rpe.kicker, Some(rpe.victim), "", "", None, "remove-participant-event"))

                case ciue:ChatInviteUserEvent =>
                    Seq(ChatGeneralPushNotification(ciue.chat, ciue.invitor, Some(ciue.targetUser), "", "", None, "chat-invite-user-event"))

                case clge:ChatLeaveGroupEvent =>
                    Seq(ChatGeneralPushNotification(clge.chat, clge.user, None, "", "", None, "chat-leave-group-event"))

                case dsue:DeliveryStatusUpdateEvent =>
                    val msg = dsue.messageIds.map(_.toString).mkString(",") + "|" + dsue.status.getName
                    Seq(ChatGeneralPushNotification(dsue.chat, dsue.statusUser, Some(dsue.statusUser), "", msg, None, "delivery-status-update-event"))

                case gcms:GroupChatMessageSentEvent =>
                    debug(s"event handled: $gcms, msg: ${gcms.msg}")

                    (gcms.msg.getInitiator, gcms.msg) match {
                        case (Some(sender), message:MessageText) if message.getTargetChat.isDefined && message.getInitiator.isDefined =>
                            debug(s"sender: $sender, message: $message")
                            Seq(ChatPushNotification(message))
                        //                            Seq(ChatGeneralPushNotification(message.getTargetChat.get, message.getInitiator.get, None, "", "", None, "message-sent-event"))
                        case (Some(sender), message:MessageMedia) if message.getTargetChat.isDefined && message.getInitiator.isDefined =>
                            debug(s"sender: $sender, message: $message")
                            Seq(ChatPushNotification(message))
                        case _ =>
                            Nil
                    }

                case scms:ScChatMessageSentEvent =>
                    Seq(ScChatPushNotification(scms.scChatHistory, scms.msg))

                case scds:ScChatDeliveryStatusUpdateEvent =>
                    val msg = scds.messageIds.map(_.toString).mkString(",") + "|" + scds.status.getName
                    Seq(ScChatDeliveryStatusPushNotification(scds.scChat, msg))
            }
        }

        ChatEventListener.setup

        Digaku.engine.eventStream.addListeners(ChatEventListener, AnalChatEventListener)

    }

}
