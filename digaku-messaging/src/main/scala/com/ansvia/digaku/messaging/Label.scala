/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

/**
 * Author: robin (<EMAIL>)
 */
object Label {
    val INITIATOR = "chat_initiator"
    val PARTICIPANT = "chat_participant"
    val CHAT_HISTORY = "chat_history"
    val CONTACT = "chat_contact"
}

object CollectionVertex {
    object Kind {
        val CHAT = 222
        val CHAT_HISTORY = 223
        val CONTACT = 224
    }
}
