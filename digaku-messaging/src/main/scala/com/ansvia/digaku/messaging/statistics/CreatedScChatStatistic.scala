package com.ansvia.digaku.messaging.statistics

import com.ansvia.digaku.messaging.{JsonSupport, ScChat}
import net.liftweb.json.JValue
import net.liftweb.json.JsonDSL._
import net.liftweb.json._
import com.ansvia.digaku.model.TopicCategory
import com.ansvia.digaku.messaging.js.ScChatStatusType._

/**
  * Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
  */

/**
  * Digunakan untuk memodelkan data created sccchat statistic yang yang nantinya
  * akan disimpan berupa json string
  * see com.ansvia.digaku.shell.commands.scchatstatistic.CreatedStatisticGenerator
  * see com.ansvia.digaku.shell.commands.scchatstatistic.MonthlyCreatedStatisticGenerator
  */
case class CreatedScChatStatistic(name:String, chatIds:Seq[Long]) extends ScChatStatisticModel(name) with JsonSupport {
    override def toJson: JValue = {
        ("name" -> name) ~
            ("chatIds", chatIds)
    }

    override def toCsv: String = {
        val sb = new StringBuilder()

        sb.append("Chat Ref,Agent Name,Topic,Question,Answer/Reason,Keywords,Category,Sub Category,Status\n")

        chatIds.foreach { id =>
            ScChat.getById(id).foreach { chat =>

                val category = TopicCategory.getById(chat.getCategoryId)
                val parentCategory = category.flatMap(_.getParentCategory())

                val agentName = chat.getAgent.map(aget => normStrCsvColumn(aget.getName)).getOrElse("-")
                val topicName = chat.getTopic.map(topic => normStrCsvColumn(topic.name)).getOrElse("-")
                val question = normStrCsvColumn(chat.getClosedQuestion)
                val lastStatus = chat.getLastStatus
                val reason = lastStatus.map(st => normStrCsvColumn(st.getReason)).getOrElse("-")
                val keywords = normStrCsvColumn(chat.getTags)
                val status = lastStatus.map(st => describe(st.status).name).getOrElse("-")
                val categoryName = parentCategory.map(_.name).getOrElse(category.map(_.name).getOrElse("-"))
                val subCategoryName = if (parentCategory.isDefined) category.map(_.name).getOrElse("-") else "-"

                sb.append(s"""$id,$agentName,$topicName,"$question","$reason",$keywords,$categoryName,$subCategoryName,$status\n""")
            }
        }

        sb.result()
    }
}

object CreatedScChatStatistic {
    implicit val f = DefaultFormats

    def fromJsonString(str:String): CreatedScChatStatistic = {
        parse(str).extract[CreatedScChatStatistic]
    }
}
