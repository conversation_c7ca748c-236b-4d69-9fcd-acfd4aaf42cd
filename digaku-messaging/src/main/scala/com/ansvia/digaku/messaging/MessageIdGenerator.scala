/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import java.util.concurrent.atomic.AtomicLong

import com.ansvia.digaku.Digaku

/**
 * Author: robin (<EMAIL>)
 */
case class MessageIdGenerator(chatId:Long) {
    private lazy val counter = Digaku.engine.counterProvider("chat-" + chatId)

    private lazy val atomic = new AtomicLong(counter.get("idgen"))

    def nextId() = {
        counter.increment("idgen")
        atomic.incrementAndGet()
    }
}
