package com.ansvia.digaku.messaging

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.exc._
import com.ansvia.digaku.messaging.js.{MessageEventText, ScChatStatusType, MessageDeliveryStatus}
import com.ansvia.digaku.messaging.se.{ScChatElasticSearchEngine, MTSearchEngineWithChat}
import com.ansvia.digaku.model.{Topic, User}
import com.ansvia.digaku.persistence.SeqStore
import com.ansvia.digaku.util.ContentNormalizer
import com.ansvia.digaku.utils.AsLong
import com.ansvia.digaku.web.{WebConfig, DigakuWebCore}
import net.liftweb.json.JsonAST.{JString, JInt}
import net.liftweb.json.JsonDSL._
import net.liftweb.json.{JValue, _}
import org.joda.time.DateTime
import ScChatTopic._
import ImplicitChatUser._

import scala.collection.mutable.ListBuffer

/**
 * Author: nadir (<EMAIL>)
 */

case class ScChat(var title:String) extends ChatBase(title) {

    private var topicId:IDType = 0L
    private var agentId:IDType = 0L
    private var question = ""
    private var tags = ""
    private var closedQuestion = ""
    private var categoryId = 0L

    // per-chat messages store to easy track messages from chat scope
    protected lazy val chatMessageSeqStore: SeqStore[java.lang.Long] =
        ScChatHistory.seqStore.build("sc-chat-messages-%s".format(getId), ()=> ScChat.engine.generateId())

    private lazy val scChatStatusSeqStore: SeqStore[java.lang.Long] =
        ScChatHistory.seqStore.build("sc-chat-status-%s".format(getId), ()=> ScChat.engine.generateId())

    private lazy val scChatConfig = Digaku.engine.kvStoreProvider.build("sc-chat-config-" + getId)

    protected lazy val counter = Digaku.engine.counterProvider("sc-chat-" + getId)

    override lazy val userHistoryStore = {
        require(getId != 0, "id not set, unsaved Chat?")
        ScChatHistory.seqStore.build("user-history-store-" + getId, ()=> ScChat.engine.generateId())
    }

    protected val messagesText = ScChat.messagesText

    override def toString: String = "ScChat{id=%s}".format(_id)

    def getTopicId = topicId
    def getTopic:Option[Topic] = Topic.getById(topicId)

    def setTopicId(topicId:Long) = {
        this.topicId = topicId
        this
    }

    def setTopic(topic: Topic) = {
        setTopicId(topic.getId)
    }

    def getAgentId = agentId
    def getAgent:Option[User] = User.getById(agentId)

    def setAgentId(agentId:Long) = {
        this.agentId = agentId
        this
    }

    def setAgent(user: User) = {
        setAgentId(user.getId)
    }

    def setQuestion(question:String) = {
        this.question = question
        this
    }

    def getQuestion = question

    def getParticipants:Seq[User] = {
        getParticipantIds.flatMap(idx => User.getById(idx))
    }

    def getParticipantIds:Seq[Long] = {
        Seq(initiatorId, agentId)
    }

    def isParticipant(user: User): Boolean = {
        getParticipantIds.contains(user.getId)
    }

    def getTags = this.tags
    def setTags(tags:String) = {
        this.tags = tags

        this
    }

    def getClosedQuestion = this.closedQuestion
    def setClosedQuestion(closedQuestion:String) = {
        this.closedQuestion = closedQuestion

        this
    }

    def getCategoryId = this.categoryId
    def setCategoryId(categoryId:Long) = {
        this.categoryId = categoryId

        this
    }

    def setQuestionMessage(message: Message): Unit ={
        scChatConfig.set("question-message", message.toJsonString)
    }

    def getQuestionMessage: Option[Message] = {
        scChatConfig.getOption("question-message").map(Message.fromJsonString)
    }

    /**
     * Digunakan untuk menandai message pertama yang belum dibalas ke sebuah chat
     * sebagai penentu apakah chat sudah waktunya di-timeout-kan atau tidak
     * see [[setTimeout()]] or [[addMessage()]]
     * @param msg
     */
    private def setFirstUnrepliedMessage(msg:Message): Unit = {
        scChatConfig.set("first-unreplied-message", msg.getId.toString)
    }

    /**
     * Mendapatkan message pertama yang belum dibalas yang ditandai sebagai penentu
     * chat ini akan di-timeout-kan atau tidak.
     * @return
     */
    private def getFirstUnrepliedMessage:Option[Message] = {
        scChatConfig.get("first-unreplied-message", "") match {
            case AsLong(id) =>
                this.getMessageById(id)
            case _ =>
                None
        }
    }

    def addEventMessage(initiator:User, eventText: String, targetUserId:Long, metaData:Map[String, String], onlyFor:User*): Unit ={
        val id = genMessageId

        val _metaData = metaData + ("targetUserId" -> targetUserId.toString)

        val msg = MessageEvent(id, eventText)
            .setInitiator(initiator)
            .setTargetChatId(getId)
            .setChatType(ChatType.SCCHAT)
            .setTimestamp(Digaku.engine.dateUtils.nowMilis)
            .setMetaData(_metaData)

        chatMessageSeqStore.insert(id, msg.toJsonString)

        if (onlyFor.nonEmpty) {
            onlyFor.flatMap(getChatHistoryFor).foreach { ch =>
                ch.addMessage(msg)

                Digaku.engine.eventStream.emit(ScChatMessageSentEvent(msg, ch))
            }
        } else {

            val chs = eventText match {
                case MessageEventText.CLOSE_CHAT | MessageEventText.CUSTOMER_TIMEOUT =>
                    getConnectedChatHistories()

                case _ =>
                    getParticipants.flatMap(getChatHistoryFor)

            }

            for (ch <- chs){
                ch.addMessage(msg)
                Digaku.engine.eventStream.emit(ScChatMessageSentEvent(msg, ch))
            }
        }

        this.incrementMessageCount()
        msg.asInstanceOf[MessageEvent]
    }

    def update() {

        require(ScChat.getById(getId).isDefined, "cannot update unsaved chat object.")

        setLastUpdated(Digaku.engine.dateUtils.nowMilis)

        val participant = getParticipantIds

        // update all participants
        userHistoryStore.paging(None, None) { case (k, v) =>
            val ch = ScChatHistory.fromJsonString(v)

            ch.userO.foreach { user =>
                val chatUser = new ChatUser(user)

                if (user.getId == initiatorId) {
                    chatUser.setActiveScChat(this)
                } else {
                    // remove old ref from index if any
                    chatUser.agentScChatSeqStoreIndex.get(getId) match {
                        case Some(AsLong(index)) =>
                            if (participant.contains(user.getId)) {
                                chatUser.agentScChatSeqStore.delete(index)

                                // update seq store
                                val newIndex = chatUser.agentScChatSeqStore.insert(toJsonString)

                                // update new index
                                chatUser.agentScChatSeqStoreIndex.insert(getId, newIndex.toString)

                            } else {
                                chatUser.agentScChatSeqStore.update(index, toJsonString)
                            }

                        case _ =>
                            // update seq store
                            val newIndex = chatUser.agentScChatSeqStore.insert(toJsonString)

                            // update new index
                            chatUser.agentScChatSeqStoreIndex.insert(getId, newIndex.toString)
                    }
                }
            }

            true
        }

        reindexSearchEngine()

        // masukkan ke global registry
        ScChat.scChatRegistry.insert(getId, toJsonString)

    }

    override def toJson: JValue = {
        require(_id != 0L, "cannot convert to json, id still 0, unsaved chat object?")
        require(this.initiatorId != 0L, "initiator must set")
        require(this.topicId != 0L, "topic must set")

        ("id" -> this._id) ~ ("title" -> title) ~ ("question", question) ~
            ("initiatorId" -> initiatorId) ~ ("topicId" -> topicId) ~ ("agentId" -> agentId) ~
            ("creationTime" -> _creationTime) ~ ("lastUpdated" -> lastUpdated)  ~ ("tags" -> tags) ~
            ("closedQuestion" -> closedQuestion) ~ ("categoryId" -> categoryId)
    }

    def addChatHistoryFor(users:User) = {
        require(_id != 0L, "cannot add participant while Chat object is not saved yet.")
        val ch = ScChatHistory(users.getId, this.getId).setActive(true)

        this.userHistoryStore.insert(users.getId, ch.toJsonString)
    }

    def getConnectedChatHistories(limit:Int = 300):Seq[ScChatHistory] = {
        userHistoryStore.getStream(None, None, limit).map(d => ScChatHistory.fromJsonString(d._2)).toSeq
    }

    def getChatHistoryFor(user:User):Option[ScChatHistory] = {
        userHistoryStore.get(user.getId).map(ScChatHistory.fromJsonString)
    }

    def getStatuses(limit:Int) = {
        scChatStatusSeqStore.getStream(None, None, scChatStatusSeqStore.getCount)
            .map(rv => ScChatStatus.fromJsonString(rv._2))
    }

    def getLastStatus:Option[ScChatStatus] = {
        scChatStatusSeqStore.getStream(None, None, 1)
            .map(rv => ScChatStatus.fromJsonString(rv._2))
            .toList.headOption
    }

    def addStatus(status:ScChatStatus) = {
        scChatStatusSeqStore.insert(status.getId, status.toJsonString)

        this
    }

    def setTyping(user: User) = {
        Digaku.engine.eventStream.emit(ScChatTypingEvent(this, user, "..."))
    }

    def deactivateHistory(users:User*) = {
        for (user <- users){
            getChatHistoryFor(user).foreach { ch =>
                ch.setActive(false)
                userHistoryStore.update(user.getId, ch.toJsonString)
            }
        }
    }

    /**
     * Set timout untuk ScChat ini.
     * ScChat akan di-timeout-kan ketika status masih open
     */
    def setTimeout() = {

        debug("try to closing ScChat: " + this)

        getFirstUnrepliedMessage.orElse(getLastMessageMediaOrText).foreach { lastMessage =>
            val nowMillis = Digaku.engine.dateUtils.nowMilis

            val isOpenScChat = getLastStatus.exists(x => x.status == ScChatStatusType.OPEN)
            val isAgentTimeout = lastMessage.getInitiatorId == this.getInitiatorId
            val status = if (isAgentTimeout) ScChatStatusType.AGENT_TIMEOUT else ScChatStatusType.CUSTOMER_TIMEOUT

            // Check apakah open status
            if (isOpenScChat) {

                // waktu untuk timeout
                val toTimeout = if (isAgentTimeout) {
                    lastMessage.getTimestamp + ScChatSettings.agentTimeout
                } else {
                    lastMessage.getTimestamp + ScChatSettings.initiatorTimeout
                }

                // Cek waktu terakhir message
                if (toTimeout <= nowMillis) {
                    if (getAgentId > 0L) {
                        val sccStatus = ScChatStatus(status)
                            .setAgentId(getAgentId)
                            .setScChatId(getId)
                            .setId(genMessageId)
                            .setCreationTime(Digaku.engine.dateUtils.nowMilis)

                        val agent = getAgent

                        val propEvent = this.getInitiator.flatMap { chatInitiator =>
                            agent.map { agentUser =>
                                if (isAgentTimeout) {
                                    (agentUser, chatInitiator, MessageEventText.AGENT_TIMEOUT)
                                } else {
                                    (chatInitiator, agentUser, MessageEventText.CUSTOMER_TIMEOUT)
                                }
                            }
                        }

                        if (isAgentTimeout) {
                            this.agentId = 0L

                            agent.foreach { user =>
                                // Deactive chat history agent
                                getChatHistoryFor(user).foreach { ch =>
                                    ch.setActive(false)

                                    deactivateHistory(user)
                                }

                                // Suspend agent
                                user.suspendAgent()
                            }
                        }

                        this.addStatus(sccStatus)

                        this.update()

                        // Kirim Message Event
                        propEvent.foreach { prop =>
                            prop._3 match {
                                case MessageEventText.CUSTOMER_TIMEOUT =>
                                    addEventMessage(prop._1, prop._3, prop._2.getId, Map.empty[String, String])
                                case _ =>
                                    addEventMessage(prop._1, prop._3, prop._2.getId, Map.empty[String, String], prop._1, prop._2)
                            }
                        }

                        debug("ScChat closed: %s = %s".format(this, sccStatus))

                        ScChat.openScChatRegistry.delete(getId)

                        // Swap scchat yang statusnya masih open harus paling atas
                        agent.foreach { agent =>
                            agent.getAgentScChats(None, None, 5).reverse.foreach { sc =>
                                sc.getLastStatus.foreach { st =>
                                    st.status match {
                                        case ScChatStatusType.OPEN =>
                                            sc.update()
                                        case _ =>
                                    }
                                }
                            }
                        }

                        // Ketika timout agent akan di-redirect ke agent lain
                        // atau masuk ke antrian ketika agent tidak ada yang available
                        if (isAgentTimeout) {
                            getTopic.foreach(_.addScChat(this, true))
                        }
                    } else {
                        warn("Can't find agentId for open %s".format(this))

                        ScChat.openScChatRegistry.delete(getId)

                        getTopic.foreach(_.addScChat(this, true))
                    }
                } else {
                    setAutoTimeout(toTimeout)
                }
            } else {
                ScChat.openScChatRegistry.delete(getId)
            }
        }
    }

    def getThumbnailFor(user:User) = {
        // "http://www.gravatar.com/avatar/%s.png?d=identicon".format(getTitleFor(user).md5)
        lazy val defaultThumbnail = DigakuWebCore.fixHttpProtocol(WebConfig.BASE_URL + "/assets/img/default/profile/2_64.png")

        if (user.getId == this.initiatorId) {
            getAgent.filterNot(_.photoSmall.trim.isEmpty)
                .map(agent => ContentNormalizer.normalizeUrl(agent.photoSmall))
                .getOrElse(defaultThumbnail)
        } else {
            getInitiator.filterNot(_.photoSmall.trim.isEmpty)
                .map(initiator => ContentNormalizer.normalizeUrl(initiator.photoSmall))
                .getOrElse(defaultThumbnail)
        }
    }

    def getTitleFor(user:User) = {
        if (user.getId == this.initiatorId) {
            getAgent.map(agent => agent.getName)
                .getOrElse("-")
        } else {
            getInitiator.map(initiator => initiator.getName)
                .getOrElse("-")
        }
    }

    def getUnreadMessageCount(forUser:User): Long = {
        getChatHistoryFor(forUser).map(_.getUnreadMessageCount).getOrElse(0L)
    }

    def markAllMessagesRead(user:User) = {
        var marked = 0
        getChatHistoryFor(user).foreach { ch =>
            marked += ch.markAllMessagesRead()
        }
        marked
    }

    def addMessageFor(user:User, msg:Message) = {
        getChatHistoryFor(user).foreach { ch =>
            ch.addMessage(msg.addConsumersByIds(ch.userId))

            Digaku.engine.eventStream.emit(ScChatMessageSentEvent(msg, ch))
        }
    }

    def ackMessages(user:User, messageIds:List[Long]) = {
        getChatHistoryFor(user).foreach { ch =>
            ch.ackMessages(messageIds)
        }

        Digaku.engine.eventStream.emit(ScChatDeliveryStatusUpdateEvent(user, this, messageIds, MessageDeliveryStatus.Delivered))
    }

    def markMessagesRead(user: User, messageIds: List[Long]): Int = {
        getChatHistoryFor(user).map { ch =>
            ch.markMessagesRead(messageIds)
        }.getOrElse(0)
    }

    def addMessage(msg: Message) = {

        // ini harus duluan agar dipastikan sudah terdaftar di chat ini
        // sehingga chat.getMessageById akan dipastikan selalu dapat
        registerMessage(msg)

        val chatHistories = getParticipants.flatMap(getChatHistoryFor)

        update()

        if (!getFirstUnrepliedMessage.exists(_.getInitiatorId == msg.getInitiatorId)) {
            setFirstUnrepliedMessage(msg)

            // Set auto timeout ketika status open dan bukan message event
            if (!msg.isInstanceOf[MessageEvent] && getLastStatus.exists(_.status == ScChatStatusType.OPEN)) {
                val nowMillis = Digaku.engine.dateUtils.nowMilis
                val to = if (msg.getInitiatorId == getInitiatorId) {
                    nowMillis + ScChatSettings.agentTimeout
                } else {
                    nowMillis + ScChatSettings.initiatorTimeout
                }

                setAutoTimeout(to)
            }
        }

        for (ch <- chatHistories){

            // check apakah user ada di connected histories apa tidak?
            // karena ada kemungkinan user dari sisi lain menghapus chat
            // yang mana menghapus dari chatSeqStore user
            // nah ini perlu dibalikin di mode p2p, karena apabila target user-nya gak exists
            // kalau tidak maka getChats tidak akan pernah menampilkan chat ini lagi
            ch.userO match {
                case Some(targetUser) =>
                    ch.addMessage(msg.addConsumersByIds(ch.userId))
                    Digaku.engine.eventStream.emit(ScChatMessageSentEvent(msg, ch))

                case _ =>
            }
        }

        incrementMessageCount()

        msg
    }

    def genMessageId = {
        ScChat.engine.generateId()
    }

    def reindexSearchEngine(): Unit ={
        Digaku.engine.searchEngine match {
            case mts:ScChatElasticSearchEngine =>
                mts.indexScChat(this)

            case _ =>
        }
    }

    def isClosed:Boolean = {
        getLastStatus.exists { st =>
            st.status == ScChatStatusType.SOLVED || st.status == ScChatStatusType.MARK_AS_SOLVED ||
                st.status == ScChatStatusType.CUSTOMER_TIMEOUT || st.status == ScChatStatusType.UNSOLVED
        }
    }

    /**
     * Get last message untuk media ata text
     * @return
     */
    def getLastMessageMediaOrText:Option[Message] = {
        var lastMessage:Option[Message] = None

        chatMessageSeqStore.paging(None, None){ case (idx, b) =>
            val msg = Message.fromJsonString(b)

            if (!msg.isInstanceOf[MessageEvent] && !lastMessage.isDefined) {
                lastMessage = Some(msg)
            }

            !lastMessage.isDefined
        }

        lastMessage
    }

    /**
     * Set auto timeout untuk ScChat ini
     * @param millis waktu timeout
     */
    def setAutoTimeout(millis:Long): Unit = {
        ScChat.openScChatRegistry.insert(getId, millis.toString)
        Digaku.engine.interNodeComm.sendPrefix("*", "set-timeout=%s:%s".format(getId, millis).getBytes)
    }
}

object ScChat extends ChatComponentConfig with Slf4jLogger {

    val messagesText = "sc-messages"

    lazy val scChatRegistry: SeqStore[java.lang.Long] = ScChatHistory.seqStore.buildCrypted("global-sc-chat-store",
        ()=> engine.generateId())

    // Penyimpanan untuk open ScChat
    lazy val openScChatRegistry: SeqStore[java.lang.Long] = ScChatHistory.seqStore.build("global-open-sc-chat-store",
        ()=> engine.generateId())

    def createChat(initiator:User, topicId: Long, title: String, question:String) = {
        build(ScChat(title)
            .setQuestion(question)
            .setInitiator(initiator)
            .setTopicId(topicId))
    }

    def build(scChat:ScChat) = {
        require(scChat.getId == 0L, "id already defined, prevent overwrite operation.")
        require(scChat.getInitiatorId != 0L, "initiator must set")

        val id = engine.generateId()

        val nowMillis = Digaku.engine.dateUtils.nowMilis

        scChat.setId(id)
            .setCreationTime(nowMillis)
            .setLastUpdated(nowMillis)

        scChatRegistry.insert(id, scChat.toJsonString)

        scChat
    }

    def getById(chatId:Long): Option[ScChat] = {
        scChatRegistry.get(chatId).map(fromJsonString)
    }


    def fromJsonString(jsonText: String):ScChat = {
        try {

            val json = parse(jsonText)

            val id = json \ "id" match {
                case JInt(_id) => _id.longValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting ScChat id. source: $jsonText")
            }

            val title = json \ "title" match {
                case JString(_title) => _title
                case data => throw new DigakuException(s"unexpected data: $data, expecting ScChat title. source: $jsonText")
            }

            val question = json \ "question" match {
                case JString(_question) => _question
                case data => throw new DigakuException(s"unexpected data: $data, expecting ScChat title. source: $jsonText")
            }

            val initiatorId = json \ "initiatorId" match {
                case JInt(_id) => _id.longValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting ScChat initiator id.")
            }

            val topicId = json \ "topicId" match {
                case JInt(_topicId) => _topicId.longValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting ScChat topic id.")
            }

            val agentId = json \ "agentId" match {
                case JInt(_agentId) => _agentId.longValue()
                case _ => 0L
            }

            val creationTime = json \ "creationTime" match {
                case JInt(_cr) => _cr.longValue()
                case data =>  throw new DigakuException(s"unexpected data: $data, expecting ScChat creation time.")
            }

            val lastUpdated = json \ "lastUpdated" match {
                case JInt(_id) => _id.longValue()
                case data => throw new DigakuException(s"unexpected data: $data, expecting ScChat last updated time.")
            }

            val tags = json \ "tags" match {
                case JString(_tags) => _tags
                case data => ""
            }

            val closedQuestion = json \ "closedQuestion" match {
                case JString(_cQuestion) => _cQuestion
                case _ => ""
            }

            val categoryId = json \ "categoryId" match {
                case JInt(num) => num.longValue()
                case _ => 0L
            }

            ScChat(title).setId(id)
                .setInitiatorId(initiatorId)
                .setQuestion(question)
                .setTopicId(topicId)
                .setAgentId(agentId)
                .setCreationTime(creationTime)
                .setLastUpdated(lastUpdated)
                .setTags(tags)
                .setClosedQuestion(closedQuestion)
                .setCategoryId(categoryId)

        } catch {
            case e:net.liftweb.json.JsonParser.ParseException =>
                error("error parsing: " + jsonText)
                e.printStackTrace()
                throw e
        }
    }
}