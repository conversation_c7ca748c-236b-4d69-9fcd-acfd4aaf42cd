/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import java.util.concurrent.{Executors, TimeUnit}
import java.{lang, util}

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types.TransactionalGraphType
import com.ansvia.digaku.exc._
import com.ansvia.digaku.lib.WebEngineWithConfig
import com.ansvia.digaku.messaging.ImplicitChatUser._
import com.ansvia.digaku.messaging.js.{MediaType, MessageDeliveryStatus, ScChatStatusType, ScTopicJs}
import com.ansvia.digaku.model.Topic
import com.ansvia.digaku.persistence.MapDb
import com.ansvia.digaku.thrift.messaging.{Chat => ChatThrift, Message => MessageThrift, MessageStatus => MessageStatusThrift, ScChat => ScChatThrift, ScChatStatus => ScChatStatusThrift, Topic => TopicThrift, User => UserThrift, UserTyping => UserTypingThrift, _}
import com.ansvia.digaku.util.ContentNormalizer
import com.ansvia.digaku.web.{DigakuApp, DigakuWebCore, WebConfig}
import com.ansvia.digaku.{Digaku, model}
import com.google.common.cache.CacheBuilder
import net.liftweb.util.BasicTypesHelpers.AsLong
import org.apache.commons.io.FilenameUtils
import org.apache.thrift.async.AsyncMethodCallback

import scala.collection.JavaConversions
import scala.collection.mutable.ListBuffer
import scala.concurrent._
import com.ansvia.digaku.helpers.TypeHelpers._
import com.ansvia.digaku.utils.RichString._
import com.ansvia.graph.BlueprintsWrapper._

import scala.concurrent.duration._
import JavaConversions._
import com.ansvia.digaku.utils.UserSettings._
import net.liftweb.json._
import com.ansvia.digaku.database.GraphCompat._
import com.ansvia.digaku.web.statistic.CreateMessageLogStore


// scalastyle:off number.of.methods

/**
 * Author: robin (<EMAIL>)
 */

private[messaging] trait UserCredentialAccess {
    this: Slf4jLogger =>


    // instance untuk dapat mengakses userId dan clienId melalui key accessToken
    // untuk melakukan mapping dari accessToken ke user dan app.
    private lazy val authRegistry = Digaku.engine.kvStoreProvider.build("digaku-oauth-registry")

    protected def getUserByApiKey(apiKey: String):Option[model.User] = {
        authRegistry.getOption[String](apiKey).flatMap { at =>
            if (apiKey.startsWith("access_token:")){
                at.split(':').toList match {
                    case AsLong(clientId) :: AsLong(userId) :: scope :: Nil =>
                        com.ansvia.digaku.model.User.getById(userId)
                    case _ =>
                        None
                }
            }else if (apiKey.startsWith("private_key:")){
                at match {
                    case AsLong(_id) => com.ansvia.digaku.model.User.getById(_id)
                    case _ => None
                }
            }else{
                None
            }
        }
    }

    protected def getAppByApiKey(apiKey: String):Option[model.App] = {
        authRegistry.getOption[String](apiKey).flatMap { at =>
            if (apiKey.startsWith("access_token:")){
                at.split(':').toList match {
                    case AsLong(clientId) :: AsLong(userId) :: scope :: Nil =>
                        com.ansvia.digaku.model.App.getById(clientId)
                    case _ =>
                        None
                }
            }else if (apiKey.startsWith("private_key:")){
                at match {
                    case AsLong(_id) => com.ansvia.digaku.model.App.getById(_id)
                    case _ => None
                }
            }else{
                None
            }
        }
    }

    protected def getUserByApiKeyOrError(apiKey: String) = {
//        debug("get user by api key: " + apiKey)
        getUserByApiKey(apiKey).getOrElse {
            throw PermissionDeniedException("invalid session or session expired")
        }
    }

}

private[messaging] trait ThriftHelper {
    private def getMessageStatusCode(mds:MessageDeliveryStatus): Byte = {
        if (mds == MessageDeliveryStatus.Delivered) {
            MessageDeliveryStatus.Delivered.getCode
        } else if (mds == MessageDeliveryStatus.Read) {
            MessageDeliveryStatus.Read.getCode
        } else {
            MessageDeliveryStatus.Sent.getCode
        }
    }

    protected def toThrift(msg:Message, currentUser:model.User):MessageThrift = {

        // Sent accumuate sent status hanya untuk inisiatornya saja
        // ketika bukan initiator default message delivery Sent
        val statusCode = {
            if (msg.getInitiatorId == currentUser.getId) {
                if (msg.getChatType == ChatType.CHAT) {
                    msg.getTargetChat match {
                        case Some(chat) if chat.kind == ChatKind.PersonToPerson =>
                            chat.getParticipants.filterNot(_.getId == msg.getInitiatorId).headOption.map { u =>
                                msg.getDeliveryStatusInfoFor(u).status.getCode
                            }.getOrElse(MessageDeliveryStatus.Sent.getCode)
                        case Some(chat) if chat.kind == ChatKind.Group =>
                            getMessageStatusCode(msg.accumulateStatus())
                        case _ =>
                            MessageDeliveryStatus.Sent.getCode
                    }
                } else {
                    getMessageStatusCode(msg.accumulateStatus())
                }
            } else {
                MessageDeliveryStatus.Sent.getCode
            }
        }

        val msgThrift = new MessageThrift(msg.getId, statusCode, msg.getInitiatorId,
            msg.getInitiator.map(_.getName).getOrElse("???"), msg.getKind, msg.getTimestamp, msg.getChatType)

        msg match {
            case m:MessageText =>
                msgThrift.setText(m.getText)
            case m:MessageEvent =>
                msgThrift.setText(m.eventText)
                if (msg.getChatType == ChatType.SCCHAT) {
                    ScChat.getById(msg.getTargetChatId).foreach { scChat =>
                        msgThrift.setTextRendered(currentUser.renderEventText(scChat, m))
                    }
                }
            case m:MessageMedia =>
                msgThrift.setMedia(m.getMedia)
                msgThrift.setMediaType(m.getMediaType.toByte)

        }

        msgThrift
    }


    protected def toThrift(user:model.User):UserThrift = {
        val thumb = if (user.photoSmall.nonEmpty) {
            ContentNormalizer.normalizeUrl(user.photoSmall)
        } else {
            DigakuWebCore.fixHttpProtocol(WebConfig.BASE_URL + "/assets/img/default/profile/2_64.png")
        }

        new UserThrift(user.getId, user.getName, user.meStatus, user.isAgentTopic, thumb)
    }

    protected def toThrift(chat:Chat, currentUser:model.User, withAllParticipant:Boolean):ChatThrift = {
        val initiator = chat.getInitiator.map(toThrift).getOrElse {
            throw new DigakuException("User initiator didn't exists for chat " + chat)
        }

        val chO = chat.getChatHistoryFor(currentUser)
        val active = chO.exists(_.isActive)

        var chatTitle = ""

        val participants = if (withAllParticipant) {
                chat.getActiveParticipants
            } else {
                // Participant hanya untuk chat P2P
                // Participant Group chat bisa di ambil dari getParticipant
                if (chat.kind == ChatKind.Group) {
                    chatTitle = chat.title

                    Seq.empty[model.User]
                } else {
                    val part = chat.getParticipants
                    chatTitle = part.find(u => u.getId != currentUser.getId)
                        .map(_.getName)
                        .getOrElse("")

                    part
                }
            }

        val chatThrift = new ChatThrift(chat.getId,
            chatTitle,
            initiator,
            JavaConversions.setAsJavaSet(participants.map(toThrift).toSet),
            active, chat.getThumbnailFor(currentUser),
            chat.kind,
            chat.lastUpdated
        )

        chO.map(_.getMessages(None, None, 1))
            .foreach(msgs => msgs.map(msg => toThrift(msg, currentUser)).foreach(chatThrift.setLatestMessage))

        chatThrift
    }

    protected def toThrift(topic: Topic):TopicThrift = {
        new TopicThrift(topic.getId, topic.name, topic.archived, topic.archivedReason,
            topic.archivedMessage, topic.archivedTime)
    }

    protected def toThrift(scChatStatus: ScChatStatus):ScChatStatusThrift = {
        val newSccStatus = new ScChatStatusThrift(scChatStatus.getId, scChatStatus.status, scChatStatus.getCreationTime,
            scChatStatus.getReason)

        scChatStatus.getAgent.foreach { agent =>
            newSccStatus.setAgent(toThrift(agent))
        }

        newSccStatus
    }

    protected def toThrift(scChat: ScChat, currentUser:model.User, db:TransactionalGraphType):ScChatThrift = {

        val initiator = scChat.getInitiator.map(toThrift).getOrElse {
            throw new DigakuException("User initiator didn't exists for chat " + scChat)
        }

        val quetionMessage = scChat.getQuestionMessage.getOrElse {
            throw new DigakuException("Question message didn't exists for chat " + scChat)
        }

        val topic = {
            val v = db.getVertex(scChat.getTopicId)
            if (v != null) {
                v.toCC[Topic].map(toThrift).getOrElse(throw new DigakuException("Topic didn't exists for chat " + scChat))
            } else {
                throw new DigakuException("Topic didn't exists for chat " + scChat)
            }
        }

        val status = scChat.getLastStatus.getOrElse {
            ScChatStatus(ScChatStatusType.QUEUE)
        }

        val sccThrift = new ScChatThrift(scChat.getId, initiator, toThrift(status), toThrift(quetionMessage, currentUser), scChat.creationTime,
            scChat.getLastUpdatedTime().getMillis, topic, scChat.getTags)

        scChat.getAgent.foreach(rv => sccThrift.setAgent(toThrift(rv)))

        sccThrift
    }

    protected def errorSuccess = new Error(200, "success")
}

trait DigakuThriftService extends UserCredentialAccess with ThriftHelper with Slf4jLogger


object DigakuMessagingService {

    // ini yg versi lama masih pake MapDb
    // for throttle
    //    private val throttleBuff = MapDb.compressed.createHashSet("messaging-service-throttle")
    //        .expireAfterWrite(2, TimeUnit.SECONDS)
    //        .make[String]()


    private lazy val recentlyProcessed = CacheBuilder.newBuilder()
//        .concurrencyLevel(2)
//        .weakKeys()
        .maximumSize(5000)
        .expireAfterWrite(2, TimeUnit.SECONDS)
        .build[String, Array[Byte]]()

    private def throttle(apiKey:String, prefix:String) = {
        val key = prefix + ":" + apiKey
        //        if (throttleBuff.contains(key)){
        //            throw new IgnoredException(throttledErrorMsgStr)
        //        }
        //        throttleBuff.add(key)

//        println("recentlyProcessed.getIfPresent(key): " + recentlyProcessed.getIfPresent(key))
        if (recentlyProcessed.getIfPresent(key) != null){
            println(s"throttled: $key")
            throw new IgnoredException(texts.throttledErrorMsgStr)
        }
        println(s"hit: $key")
        recentlyProcessed.put(key, Array(0x01.toByte))
    }

    object texts {
        val unauthorizedText = "unauthorized"
        val chatDidntExistsText = "chat didn't exists"
        val throttledErrorMsgStr = "Invalid state (throttled): too fast API call"
    }

}


class DigakuMessagingService extends MessagingService.Iface with DigakuThriftService {
    import ImplicitChatUser._

    import scala.collection.JavaConversions._

    import DigakuMessagingService.texts._
    import DigakuMessagingService.throttle

    private val IMAGE_RE = """(?i)^!(https?\://[a-zA-Z0-9][a-zA-Z0-9\.\-_/\?%#@~\(\)\+;&=,!\:]+(p?jpe?g|(?:x\-)?png))!$""".r

    private def safety[T](errorResult: String => T)(wrappedFunc: => T):T = {
        try {
            wrappedFunc
        }catch{
            case e:IgnoredException =>
                warn(e.getMessage)
                errorResult(e.getMessage)
            case e:DigakuException =>
                error(e.getMessage)
                errorResult(e.getMessage)
            case e:Exception =>
                error(e.getMessage)
                e.printStackTrace()
                errorResult(e.getMessage)
        }
    }

    private def defaultErrorHandler(errStr:String) = {
        if (errStr == throttledErrorMsgStr)
            errorSuccess // throttle anggap saja sukses
        else
            new Error(500, errStr)
    }

    private def defaultResultErrorHandler(errStr:String) = {
        if (errStr == throttledErrorMsgStr)
            Result.error(errorSuccess) // throttle anggap saja sukses
        else
            Result.error(new Error(500, errStr))
    }



    override def createChatSingle(apiKey: String, oppositeUserId: Long): Result = safety(defaultResultErrorHandler){

        throttle(apiKey, s"create-chat-p2p") // per user

        val currentUser = getUserByApiKeyOrError(apiKey)

        model.User.getById(oppositeUserId) match {
            case Some(oppositeUser) =>

                if (currentUser.isLocked) {
                    throw PermissionDeniedException("Anda tidak bisa create chat, Anda masih berada dalam status Suspended.")
                }

                val chat = currentUser.createP2PChat(oppositeUser)

                debug("chat created: " + chat)

                val chatThrift = {
                    val initiator = chat.getInitiator.map(toThrift).getOrElse {
                        throw new DigakuException("User initiator didn't exists for chat " + chat)
                    }

                    val chO = chat.getChatHistoryFor(currentUser)
                    val active = chO.exists(_.isActive)

                    val chatThrift = new ChatThrift(chat.getId,
                        chat.getTitleFor(currentUser),
                        initiator,
                        JavaConversions.setAsJavaSet(chat.getParticipants.map(toThrift).toSet),
                        active, chat.getThumbnailFor(currentUser),
                        chat.kind,
                        chat.lastUpdated
                    )

                    chO.map(_.getMessages(None, None, 1))
                        .foreach(msgs => msgs.map(msg => toThrift(msg, currentUser)).foreach(chatThrift.setLatestMessage))

                    chatThrift
                }

                val chatInfo = new ChatInfo(chatThrift, chat.getMessageCount, chat.getParticipantCount)

                Result.chatInfo(chatInfo)
            case _ =>
                throw NotExistsException("opposite user with id %s not exists".format(oppositeUserId))
        }
    }

    override def createChatGroup(apiKey: String, title: String, participantIds: util.List[lang.Long], logoUrl: String): Result = safety(defaultResultErrorHandler){
        throttle(apiKey, "create-chat-group-" + title.urlize)

        val currentUser = getUserByApiKeyOrError(apiKey)

        if (currentUser.isLocked) {
            throw PermissionDeniedException("Anda tidak bisa create chat, Anda masih berada dalam status Suspended.")
        }

        if (participantIds.isEmpty) {
            throw InvalidParameterException("Empty participantIds ?")
        }

        val participants = participantIds.flatMap(id => model.User.getById(Long.box(id))).toSet

        val chat = currentUser.createGroupChat(participants, title, logoUrl)

        debug("chat created: " + chat)

        val chatInfo = new ChatInfo(toThrift(chat, currentUser, false), chat.getMessageCount, chat.getParticipantCount)

        Result.chatInfo(chatInfo)
    }

    override def updateChat(apiKey: String, chatId: Long, req: ChatUpdateRequest): Error = safety(defaultErrorHandler){
        val currentUser = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) =>

                if (chat.getInitiatorId != currentUser.getId)
                    throw PermissionDeniedException("Unauthorized")

                chat.setTitle(req.getTitle)
                chat.setThumbnail(req.getLogoUrl)
                Chat.update(chat)

                errorSuccess
            case _ =>
                new Error(404, "No chat with id " + chatId)
        }
    }

    override def clearHistory(apiKey: String, chatId: Long): Error = safety(defaultErrorHandler){
        val currentUser = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) =>
                chat.getChatHistoryFor(currentUser) match {
                    case Some(ch) =>
                        ch.clear()
                        errorSuccess
                    case _ =>
                        new Error(404, "No chat history for " + currentUser.getName)
                }
            case _ =>
                new Error(404, "No chat with id " + chatId)
        }
    }

//
//    override def createChatSingle(apiKey: String, title: String, participants: util.List[java.lang.Long], logoUrl:String): Long = safety(_ => 0L){
//        val user = getUserByApiKeyOrError(apiKey)
//
//        val kind = if (participants.length == 1){
//            ChatKind.PersonToPerson
//        }else{
//            ChatKind.Group
//        }
//
//        val chat = user.createChat(participants.flatMap(userId => model.User.getById(userId)).toSet, title, kind)
//
//        debug("chat created: " + chat)
//
//        chat.getId
//    }


    override def dropChat(apiKey: String, chatId: Long): Error = safety(defaultErrorHandler){

        throttle(apiKey, "drop-chat")

        val currentUser = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) if chat.isParticipant(currentUser) =>


                chat.kind match {
                    case ChatKind.Group =>

                        if (chat.getInitiatorId == currentUser.getId) {

                            // remove permanently
                            Chat.drop(chat)

                            errorSuccess
                        }else{

                            // remove only from the current user
                            currentUser.removeChat(chat.getId)

                            errorSuccess
                        }

                    case ChatKind.PersonToPerson =>

                        // drop only from local user registry (ChatHistory)
                        currentUser.removeChat(chatId)

                        errorSuccess

                }

//                chat.getChatHistoryFor(user) match {
//                    case Some(ch) =>
//                        chat.removeUserHistory(ch)
//                        errorSuccess
//                    case _ =>
//                        new Error(500, "internal server error")
//                }
//



            case _ =>
                new Error(404, chatDidntExistsText)

        }
    }



    override def getMessageInfo(apiKey: String, chatId: Long, msgId: Long): Result = safety(defaultResultErrorHandler){

        val user = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) =>
                if (chat.isParticipant(user)) {
                    chat.getChatHistoryFor(user) match {
                        case Some(ch) =>

                            ch.getMessage(msgId) match {
                                case Some(msg) =>
                                    val messageStatuses = msg.getDeliveryStatuses
                                        .map(x => new MessageStatusThrift(toThrift(x._1), x._2.status.getCode, x._2.time))
                                    val msgInfo = new MessageInfo(toThrift(msg, user), messageStatuses)
                                    Result.messageInfo(msgInfo)
                                case _ =>
                                    Result.error(new Error(404, "no message with id " + msgId))
                            }

                        case _ =>
                            Result.error(new Error(403, "failed to get chat history"))
                    }
                } else {
                    Result.error(new Error(403, unauthorizedText))
                }
            case _ =>
                Result.error(new Error(404, chatDidntExistsText))
        }
    }

    override def getContactList(apiKey: String): Result = {

        val user = getUserByApiKeyOrError(apiKey)

        Result.users(user.getContacts().toList.map(toThrift))

    }


    override def getChatParticipants(apiKey: String, chatId: Long): Result = safety(defaultResultErrorHandler){
        val user = getUserByApiKeyOrError(apiKey)
        Chat.getById(chatId) match {
            case Some(chat) =>
                if (chat.isParticipant(user)) {
                    Result.users(chat.getActiveParticipants.map(toThrift))
                } else {
                    Result.error(new Error(403, unauthorizedText))
                }
            case _ =>
                Result.error(new Error(404, chatDidntExistsText))
        }
    }

    override def getUserInfo(apiKey: String, userId: Long, fieldsToSelect: util.List[String]): Result = safety(defaultResultErrorHandler){

        // hanya digunakan untuk authorization saja
        val _ = getUserByApiKeyOrError(apiKey)

        model.User.getById(userId).map(toThrift) match {
            case Some(ut: UserThrift) =>
                Result.userInfo(ut)
            case _ => Result.error(new Error(404, "user not exists with id " + userId))
        }


    }

    override def getMessages(apiKey: String, chatId: Long, fromId: Long, toId: Long, limit: Int): Result = safety(defaultResultErrorHandler){
        val user = getUserByApiKeyOrError(apiKey)
        Chat.getById(chatId) match {
            case Some(chat) =>
                if (chat.isParticipant(user)) {
                    chat.getChatHistoryFor(user) match {
                        case Some(ch) =>
                            val _fromId = if (fromId > 0) Some(fromId) else None
                            val _toId = if (toId > 0) Some(toId) else None
                            val _limit = limit match {
                                case n if n > 1000 => 100
                                case n if n < 1 => 1
                                case n => n
                            }
                            Result.messages(ch.getMessages(_fromId, _toId, _limit).map(m => toThrift(m, user)).reverse)
                        case _ =>
                            Result.error(new Error(403, "failed to get chat history"))
                    }
                } else {
                    Result.error(new Error(403, unauthorizedText))
                }
            case _ =>
                Result.error(new Error(404, chatDidntExistsText))
        }
    }

    override def getChats(apiKey: String, fromIdx: Long, toIdx: Long, limit: Int): Result = safety(defaultResultErrorHandler){
        val user = getUserByApiKeyOrError(apiKey)
        val fromIdxO = if (fromIdx > 0) Some(fromIdx) else None
        val toIdxO = if (fromIdx > 0) Some(fromIdx) else None
        Result.chats(user.getChats(fromIdxO, toIdxO, limit).map(c => toThrift(c, user, false)))
    }


//    override def sendMessages(apiKey: String, userId: Long, messages: util.List[MessageThrift]): Error = {
//
//        val user = getUserByStringOrError(apiKey)
//
//        user.getChatByTargetUserId(targetUserId = userId) match {
//            case Some(chat) =>
//                val msgs = messages.flatMap(x => toNewMessage(chat, x)).toList
//                user.sendMessages(chat, msgs)
//                errorSuccess
//            case _ =>
//                new Error(404, chatDidntExistsText)
//        }
//
//    }
//
//
//    override def sendGroupMessages(apiKey: String, chatId: Long, messages: util.List[MessageThrift]): Error = {
//        val user = getUserByStringOrError(apiKey)
//
//        Chat.getById(chatId) match {
//            case Some(chat) =>
//                val msgs = messages.flatMap(x => toNewMessage(chat, x)).toList
//                user.sendMessages(chat, msgs)
//                errorSuccess
//            case _ =>
//                new Error(404, chatDidntExistsText)
//        }
//    }

    override def sendMessages(apiKey: String, chatId: Long, messages: util.List[java.lang.String]): Error = safety(defaultErrorHandler){
        throttle(apiKey, "send-message-" + messages.map(_.crc32).sum)

        val user = getUserByApiKeyOrError(apiKey)
        val apps = getAppByApiKey(apiKey)

        if (user.isLocked) {
            throw PermissionDeniedException("Anda tidak bisa mengirim message, Anda masih berada dalam status Suspended.")
        }

        Chat.getById(chatId) match {
            case Some(chat) =>
                val msgs:List[Message] = messages.map {
                    // Ketika text message merupakan textile image, akan dijadikan MessageMedia
                    // ex: !http://s3-mc2.ansvia.com/img/2018/08/02/1aKSC5swy5S7ANZ.jpg!
                    case text@IMAGE_RE(url, ext) =>
                        try {
                            new MessageMedia(0L, user.getId, chat.getId, url,
                                MediaType.getFromFileName(url), FilenameUtils.getName(url), Digaku.engine.dateUtils.nowMilis)
                        } catch {
                            case e:Exception =>
                                e.printStackTrace()
                                new MessageText(text)
                        }

                    case text =>
                        new MessageText(text)
                }.toList

                val newMsgs = user.sendMessages(chat, msgs)

                apps.foreach { app =>
                    newMsgs.foreach { cm =>
                        CreateMessageLogStore.insertData(user.getId, cm.getId, cm.getTimestamp, app.name)
                    }
                }

                errorSuccess
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }

    override def addChatUsers(apiKey: String, chatId: Long, userIds: util.List[java.lang.Long]): Error = safety(defaultErrorHandler){

        val user = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) =>
                val participants = chat.getParticipants
                if (participants.exists(_.getId == user.getId)) {
                    chat.addParticipants(userIds.map(Long.unbox).toSet.flatMap { id: Long =>
                        model.User.getById(id)
                    }.toList: _*)
                    errorSuccess
                } else {
                    new Error(403, unauthorizedText)
                }
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }

    override def removeChatUsers(apiKey: String, chatId: Long, userIds: util.List[java.lang.Long]): Error = safety(defaultErrorHandler){
        throttle(apiKey, "remove-chat-" + chatId)

        val user = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) =>

                if (chat.getInitiator.map(_.getId) == Some(user.getId)) {

                    // untuk initiator yang ingin menghapus / kick user lain

                    chat.removeParticipants(userIds.map(Long.unbox).flatMap(id => model.User.getById(id)).toList: _*)
                    errorSuccess

                }else if (chat.getParticipants.map(_.getId).contains(user.getId)){

                    // untuk current user yang ingin keluar dari chat

                    val targetUser = userIds.map(Long.unbox).find(_ == user.getId)
                        .flatMap(id => model.User.getById(id))

                    if (targetUser.isDefined){
                        chat.removeParticipants(targetUser.get)
                        errorSuccess
                    }else{
                        new Error(403, "unauthorized, you cannot kick another user, you are not the owner of the group")
                    }

                }else{
                    new Error(403, "unauthorized, only owner of the group can do this")
                }
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }



    override def inviteUsers(apiKey: String, chatId: Long, userIds: util.List[lang.Long]): Error = {
        val user = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) =>

                if (chat.isActiveParticipant(user)){

                    for (u <- userIds.flatMap(_id => model.User.getById(_id))){
                        if (chat.isParticipant(u)){
                            chat.activateHistory(u)
                        }else{

                            if (!chat.isParticipant(u)){
                                chat.addParticipants(u)
                            }else{
                                new Error(412, u.getName +  " already participant on this chat")
                            }

                        }
                        Digaku.engine.eventStream.emit(ChatInviteUserEvent(user, u, chat))
                    }

                    errorSuccess
                }else{
                    new Error(403, unauthorizedText)
                }
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }

    override def kickUsers(apiKey: String, chatId: Long, userIds: util.List[lang.Long]): Error = {
        val currentUser = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) =>

                if (chat.getInitiatorId != currentUser.getId)
                    throw PermissionDeniedException("Cannot kick peoples, you are not authorized to doing this")

                if (chat.isActiveParticipant(currentUser)){

                    for (u <- userIds.flatMap(_id => model.User.getById(_id))){
                        chat.deactivateHistory(u)
                        Digaku.engine.eventStream.emit(RemoveParticipantEvent(currentUser, chat, u))
                    }

                    errorSuccess
                }else{
                    new Error(403, unauthorizedText)
                }
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }


    override def leaveChat(apiKey: String, chatId: Long): Error = {
        val cu = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) =>

                if (chat.isActiveParticipant(cu)){

                    if (chat.getInitiatorId != cu.getId){

                        chat.deactivateHistory(cu)
                        Digaku.engine.eventStream.emit(ChatLeaveGroupEvent(cu, chat))

                        // add message event for this user only
                        val msg = new MessageEvent(chat.genMessageId, "you left this group")
                            .setInitiator(cu)

                        msg.metaData = Map("activateChat" -> "false")

                        chat.registerMessage(msg)
                        chat.getChatHistoryFor(cu)
                            .foreach(_.addMessage(msg))

                        Digaku.engine.asInstanceOf[WebEngineWithConfig].notifGateway ! PublishChatMessage(cu.getId, cu.getId, chat.getId, msg.getId)

                        errorSuccess

                    }else{
                        new Error(400, "Initiator can only drop, not leaving")
                    }

                    errorSuccess
                }else{
                    new Error(403, unauthorizedText)
                }
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }

    override def getLatestMessages(apiKey: String, chatId: Long): Result = safety(defaultResultErrorHandler){
        val user = getUserByApiKeyOrError(apiKey)
        Chat.getById(chatId) match {
            case Some(chat) =>
                if (chat.isParticipant(user)) {
                    chat.getChatHistoryFor(user) match {
                        case Some(ch) =>
                            Result.messages(ch.getLastMessages.map(m => toThrift(m, user)).reverse)
                        case _ =>
                            Result.error(new Error(403, "failed to get chat history"))
                    }
                } else {
                    Result.error(new Error(403, unauthorizedText))
                }
            case _ =>
                Result.error(new Error(404, chatDidntExistsText))
        }
    }


    override def getMessageDeliveryStatus(apiKey: String, chatId: Long, messageIds: util.List[lang.Long]): Result = {
        val user = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) if chat.isParticipant(user) =>


                chat.getChatHistoryFor(user) match {
                    case Some(ch) =>
                        val _deliveryStatuses = new ListBuffer[DeliveryStatus]

                        for (msgId <- messageIds) {
                            ch.getMessage(msgId) match {
                                case Some(msg) =>

                                    val ds = msg.getDeliveryStatusInfoFor(user)
                                    _deliveryStatuses.append(new DeliveryStatus(chatId, msg.getId,
                                        ds.status.getCode))

                                case _ =>
                                    Result.error(new Error(404, "no message with id " + msgId))
                            }
                        }
                        Result.deliveryStatuses(_deliveryStatuses.result())
                    case _ =>
                        Result.error(new Error(404, "no chat history for " + user.getName))
                }
            case _ =>
                Result.error(new Error(404, chatDidntExistsText))
        }
    }

    override def setTyping(apiKey: String, chatId: Long): Error = {
        val user = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) if chat.isParticipant(user) =>
                chat.setTyping(user)
                errorSuccess
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }

    override def ackMessages(apiKey: String, chatId:Long, messageIds: util.List[lang.Long]): Error = {
        val user = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) if chat.isParticipant(user) =>
                chat.ackMessages(user, messageIds.map(Long.unbox).toList)
                errorSuccess
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }


    override def markAllMessagesRead(apiKey: String, chatId: Long): Error = safety(defaultErrorHandler){
        val user = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) if chat.isParticipant(user) =>
                chat.markAllMessagesRead(user)
                errorSuccess
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }

    override def markMessagesRead(apiKey: String, chatId: Long, messageIds: util.List[lang.Long]): Error = safety(defaultErrorHandler){
        val user = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) if chat.isParticipant(user) =>
                chat.markMessagesRead(user, messageIds.toList.map(Long.unbox))
                errorSuccess
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }

    override def getOnlineUsersIds(apiKey: String): util.List[lang.Long] = {
        val _ = getUserByApiKeyOrError(apiKey)

        ChatSessionStore.onlineUsers.getAll
            .toList
            .map { case (idStr, _) => idStr.toLong }
            .map(Long.box)
    }

    override def getChatInfo(apiKey: String, chatId: Long): Result = safety(defaultResultErrorHandler){
        val user = getUserByApiKeyOrError(apiKey)

        Chat.getById(chatId) match {
            case Some(chat) if chat.isParticipant(user) =>

                val chatInfo = new ChatInfo(toThrift(chat, user, false), chat.getMessageCount,
                    chat.getParticipantCount)

                Result.chatInfo(chatInfo)
            case _ =>
                throw NotExistsException(chatDidntExistsText)
        }
    }

    override def getServiceInfo(apiKey: String): Result = {
        val _ = getUserByApiKeyOrError(apiKey)

        val fileConf = Digaku.engine.asInstanceOf[WebEngineWithConfig].fileConf

        Result.serviceInfo(new ServiceInfo(
                Messaging.VERSION,
                WebConfig.NODE_ID,
                fileConf[String]("database.machine-id"),
                DigakuApp.VERSION,
                DigakuApp.GIT_REV,
                DigakuApp.GIT_BRANCH
            ))
    }

    override def login(loginId: String, password: String): Result = {
        Result.error(errorSuccess)
    }

    override def getPersonalTimeline(apiKey: String, UserID: Long, toIdx: Long, limit: Int): SocialResult = {
        SocialResult.error(errorSuccess)
    }

    override def getNetworkTimeline(apiKey: String, toIdx: Long, limit: Int): SocialResult = SocialResult.error(errorSuccess)

    override def getUserPersonalInfo(apiKey: String, userId: Long): SocialResult = SocialResult.error(errorSuccess)

    override def postStatus(apiKey: String, text: String): SocialResult = SocialResult.error(errorSuccess)

    /* SCCHAT */

    override def setTypingScChat(apiKey: String): Error = {
        val user = getUserByApiKeyOrError(apiKey)

        user.getActiveScChat match {
            case Some(chat) if chat.isParticipant(user) =>
                chat.setTyping(user)
                errorSuccess
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }

    override def getMessageInfoScChat(apiKey: String, msgId: Long): Result = {
        val user = getUserByApiKeyOrError(apiKey)

        user.getActiveScChat match {
            case Some(chat) =>
                if (chat.isParticipant(user)) {
                    chat.getChatHistoryFor(user) match {
                        case Some(ch) =>

                            ch.getMessage(msgId) match {
                                case Some(msg) =>
                                    val messageStatuses = msg.getDeliveryStatuses
                                        .map(x => new MessageStatusThrift(toThrift(x._1), x._2.status.getCode, x._2.time))
                                    val msgInfo = new MessageInfo(toThrift(msg, user), messageStatuses)
                                    Result.messageInfo(msgInfo)
                                case _ =>
                                    Result.error(new Error(404, "no message with id " + msgId))
                            }

                        case _ =>
                            Result.error(new Error(403, "failed to get chat history"))
                    }
                } else {
                    Result.error(new Error(403, unauthorizedText))
                }
            case _ =>
                Result.error(new Error(404, chatDidntExistsText))
        }
    }

    override def ackMessagesScChat(apiKey: String, messageIds: util.List[lang.Long]): Error = {
        val user = getUserByApiKeyOrError(apiKey)

        user.getActiveScChat match {
            case Some(chat) if chat.isParticipant(user) =>
                chat.ackMessages(user, messageIds.map(Long.unbox).toList)
                errorSuccess
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }

    override def sendMessagesScChat(apiKey: String, messages: String): Result = safety(defaultResultErrorHandler){
        throttle(apiKey, "send-message-" + messages.crc32)

        val user = getUserByApiKeyOrError(apiKey)

        if (user.isLocked) {
            throw PermissionDeniedException("Anda tidak bisa mengirim message, Anda masih berada dalam status Suspended.")
        }

        user.getActiveScChat match {
            case Some(chat) =>
                tx { t =>
                    val _topic = getFromTx[Topic](chat.getTopicId, t)

                    if (_topic.archived) {
                        throw PermissionDeniedException("Topic telah diarsipkan")
                    }

                    val msg:Message = messages match {
                        // Ketika text message merupakan textile image, akan dijadikan MessageMedia
                        // ex: !http://s3-mc2.ansvia.com/img/2018/08/02/1aKSC5swy5S7ANZ.jpg!
                        case text@IMAGE_RE(url, ext) =>
                            try {
                                new MessageMedia(0L, user.getId, chat.getId, url,
                                    MediaType.getFromFileName(url), FilenameUtils.getName(url), Digaku.engine.dateUtils.nowMilis)
                            } catch {
                                case e:Exception =>
                                    e.printStackTrace()
                                    new MessageText(text)
                            }

                        case text =>
                            new MessageText(text)
                    }

                    val newMessage = user.sendScMessage(chat, msg)
                    Result.message(toThrift(newMessage, user))
                }
            case _ =>
                Result.error(new Error(404, chatDidntExistsText))
        }
    }

    override def createScChat(apiKey: String, topicId: Long, title: String, question: String): Result = {
        try {
            val user = getUserByApiKeyOrError(apiKey)

            if (user.isLocked) {
                throw PermissionDeniedException("Anda tidak bisa mengirim pertanyaan, Anda masih berada dalam status Suspended.")
            }

            if (user.getActiveScChat.isDefined) {
                throw PermissionDeniedException("Already have active chat")
            }

            if (user.isAgentTopic) {
                throw PermissionDeniedException("Agent can't create ask question")
            }

            tx { t =>
                val v = t.getVertex(topicId)
                if (v != null) {
                    v.toCC[Topic].map { topic =>
                        if (topic.archived) {
                            throw PermissionDeniedException("Topic telah diarsipkan")
                        }
                    }.getOrElse(throw InvalidParameterException("Topic doesnt exist"))
                } else {
                    throw InvalidParameterException("Topic doesnt exist")
                }

                val scChat = user.createScChat(topicId, title, question)

                Result.scChat(toThrift(scChat, user, t))
            }
        } catch {
            case e:Exception =>
                e.printStackTrace()
                Result.error(new Error(403, e.getMessage))
        }

    }

    override def markAllMessagesReadScChat(apiKey: String): Error = {
        val user = getUserByApiKeyOrError(apiKey)

        user.getActiveScChat match {
            case Some(chat) if chat.isParticipant(user) =>
                chat.markAllMessagesRead(user)
                errorSuccess
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }

    override def closeActiveScChat(apiKey: String): Error = {
        val user = getUserByApiKeyOrError(apiKey)

        try {
            user.getActiveScChat match {
                case Some(chat) =>
                    user.removeActiveScChat()
                    errorSuccess
                case _ =>
                    new Error(404, chatDidntExistsText)
            }
        } catch {
            case e:Exception =>
                e.printStackTrace()
                new Error(403, e.getMessage)
        }
    }

    override def getActiveScChat(apiKey: String): Result = {
        val user = getUserByApiKeyOrError(apiKey)

        user.getActiveScChat match {
            case Some(chat) =>
                chat.markAllMessagesRead(user)
                tx(t => Result.scChat(toThrift(chat, user, t)))
            case _ =>
                Result.error(new Error(404, chatDidntExistsText))
        }
    }

    override def getMessagesScChat(apiKey: String, fromId: Long, toId: Long, limit: Int): Result = {
        val user = getUserByApiKeyOrError(apiKey)
        user.getActiveScChat match {
            case Some(chat) =>
                if (chat.isParticipant(user)) {
                    chat.getChatHistoryFor(user) match {
                        case Some(ch) =>
                            val _fromId = if (fromId > 0) Some(fromId) else None
                            val _toId = if (toId > 0) Some(toId) else None
                            val _limit = limit match {
                                case n if n > 1000 => 100
                                case n if n < 1 => 1
                                case n => n
                            }
                            Result.messages(ch.getMessages(_fromId, _toId, _limit).map(m => toThrift(m, user)).reverse)
                        case _ =>
                            Result.error(new Error(403, "failed to get chat history"))
                    }
                } else {
                    Result.error(new Error(403, unauthorizedText))
                }
            case _ =>
                Result.error(new Error(404, chatDidntExistsText))
        }
    }

    override def markMessagesReadScChat(apiKey: String, messageIds: util.List[lang.Long]): Error = {
        val user = getUserByApiKeyOrError(apiKey)

        user.getActiveScChat match {
            case Some(chat) if chat.isParticipant(user) =>
                chat.markMessagesRead(user, messageIds.toList.map(Long.unbox))
                errorSuccess
            case _ =>
                new Error(404, chatDidntExistsText)
        }
    }

    override def isOnlineTime(apiKey: String): Boolean = {
        getUserByApiKeyOrError(apiKey)
        ScChatSettings.isOnlineTime
    }
}

/**
 * Class yang digunakan untuk user yang listen
 * @param apiKey apiKey User yang saat login
 * @param user user yang listen
 * @param iter Jumlah iterasi yang sudah dilakukan sehingga ketika iterasi sudah mencapai maximum
 *             akan di returnkan ke resultHandler see [[com.ansvia.digaku.messaging.DigakuPushMessage#maxIter()]]
 * @param resultHandler AsyncMethodCallback
 */
case class UserListen(apiKey:String, user:model.User, var iter:Int, resultHandler: AsyncMethodCallback[PushResult])

class DigakuPushMessage extends PushMessage.AsyncIface with DigakuThriftService {

    // Create ExecutionContext newFixedThreadPool
    implicit val ec:ExecutionContext = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(5))

    implicit val f = DefaultFormats

    // digunakan untuk offsetting
    private val lastFetchedIdKey = "notif-store-last-fetch-id"

    private lazy val currentListenedClients = MapDb.compressed.createHashSet("messaging-listened-clients")
        .expireAfterWrite(30, TimeUnit.SECONDS)
        .make[String]()

    val maxIter = 13

    /**
     * Membuat scheduler untuk User yang listen.
     * recursive function ini akan mengiterasikan scheduleOnce selama 1 second sampai 13 kali
     * ketika user tidak memiliki list notif-store dan mengembalikan return ke AsyncMethodCallback
     * atau akan langsung mengembalikan return saat user sudah memiliki list notif-store
     *
     * @param ul User listen
     */
    def createSchedulerFor(ul:UserListen): Unit = {
        Digaku.engine.actorSystem.scheduler.scheduleOnce(1.seconds) {
            val apiKey = ul.apiKey
            val resultHandler = ul.resultHandler
            val user = ul.user

            // mark current user as online
            ChatSessionStore.onlineUsers.set(user.getId.toString, "1", Some(30)) // 30 detik

            try {
                var done = false

                val result = new PushResult()

                var lastFetchedId = user.config.get(lastFetchedIdKey, 0L)

                val dataStrSeq = ChatSessionStore.get(user).getStream(None,
                    if (lastFetchedId > 0L) Some(lastFetchedId) else None, 100).toList
                    .filterNot(_._1 == lastFetchedId) // exclusive

                var otherEvents = new ListBuffer[String]
                var newMessages = new ListBuffer[Message]

                if (dataStrSeq.nonEmpty) {
                    lastFetchedId = dataStrSeq.head._1
                }


                for ((id, dataStr) <- dataStrSeq) {
                    dataStr match {
                        case d if d.startsWith("|msg|:") =>
                            newMessages += Message.fromJsonString(d.substring(6))


                        case d if d.startsWith("|typing|:") =>

                            val s = d.split("\\|\\:")
                            if (s.length == 2) {
                                val ss = s(1).trim().split("\\:")
                                if (ss.length > 3) {
                                    val chatId = ss(0).toLongOr(0)
                                    val userId = ss(1).toLongOr(0)
                                    val chatType = ss(2).toIntOr(0)
                                    val userName = ss(3).trim()

                                    if (chatId > 0 && userId > 0 && userName.length > 0) {
                                        if (userName.toLowerCase != user.getName.toLowerCase) {
                                            result.setUserTyping(new UserTypingThrift(chatId, chatType, userId, userName))
                                            done = true
                                        }
                                    }
                                }
                            }
                        case d if d.startsWith("|msg-status|:") =>

                            val s = d.split("\\:")

                            if (s.length > 4) {

                                val chatId = s(1).toLongOr(0L)
                                val chatType = s(2).toIntOr(0)
                                val messageIds = s(3).split(",").toList.map(_.toLongOr(0L)).filter(_ > 0)
                                //                                        val status = s(3).toByteOr(0x00)

                                if (chatId > 0L && messageIds.nonEmpty) {

                                    val chat:Option[ChatBase] = if (chatType == ChatType.CHAT) {
                                        Chat.getById(chatId)
                                    } else {
                                        ScChat.getById(chatId)
                                    }

                                    chat match {
                                        case Some(chat) =>

                                            // ambil message yang terbesar id-nya
                                            // dan ambil status dari situ, message terakhir mewakili message-message sebelumnya
                                            // karena kalau yang terbesar udah "read" maka yang terkecil pasti
                                            // dan harusnya sudah "read" juga
                                            val largeId = messageIds.sorted.last

                                            chat.getMessageById(largeId) match {
                                                case Some(msg) =>
                                                    val deliveryStatus = new DeliverStatusBatch(chatId, chatType, messageIds.map(Long.box), msg.accumulateStatus().getCode)
                                                    result.setDeliverStatusBatch(deliveryStatus)
                                                    done = true
                                                case _ =>
                                            }
                                        case _ =>
                                    }
                                }

                            }

                        case d if d.startsWith("|online-time|:") =>
                            val s = d.split("\\:")
                            if (s.length > 1) {
                                result.setIsOnlineTime(s(1).toBooleanOr(false))
                                done = true
                            }

                        case d if d.startsWith("|archive-topic|:") =>
                            try {
                                val topic = parse(d.substring(16)).extract[ScTopicJs]
                                val topicThrift = new TopicThrift(topic.id, topic.name, topic.archived, topic.archivedReason,
                                    topic.archivedMessage, topic.archivedTime)

                                result.setArchiveTopic(topicThrift)
                                done = true
                            } catch {
                                case e:Exception =>
                                    e.printStackTrace()
                            }


                        case d =>
                            warn("[%s] got unexpected: %s".format(user.getName, d))
                            otherEvents += d
                            done = true
                    }
                }

                user.config.set(lastFetchedIdKey, lastFetchedId)

                if (newMessages.nonEmpty) {

                    val listedMessages = newMessages.result()
                        .filter(m => m.getInitiatorId != user.getId || m.getChatType == ChatType.SCCHAT)
                        .map(msg => new ChatMessagePair(Long.box(msg.getTargetChatId), toThrift(msg, user)))

                    if (listedMessages.nonEmpty) {
                        val _newMessages = new NewMessages(listedMessages.length, listedMessages)

                        result.setNewMessages(_newMessages)

                        done = true
                    }
                }

                if (done || ul.iter >= maxIter) {
                    currentListenedClients.remove(apiKey)
                    resultHandler.onComplete(result)

                    debug("[%s] listen end.".format(user.getName))

                } else {
                    ul.iter = ul.iter + 1
                    createSchedulerFor(ul)
                }
            } catch {
                case e: Throwable =>
                    error(e.getMessage)
                    e.printStackTrace()
            }
        }(ec)
    }

    override def listen(apiKey: String, resultHandler: AsyncMethodCallback[PushResult]){

        try {
            if (currentListenedClients.contains(apiKey)){
                throw AlreadyExistsException("already listen")
            }

            val user = getUserByApiKeyOrError(apiKey)

            debug("[%s] listen called.".format(user.getName))

            currentListenedClients.add(apiKey)

            Future {
                createSchedulerFor(UserListen(apiKey, user, 0, resultHandler))
            }(ec)

        }catch{
            case e:AlreadyExistsException =>
                currentListenedClients.remove(apiKey)
                resultHandler.onComplete(new PushResult().setError(new Error(409, e.getMessage)))

            case e:PermissionDeniedException =>
                currentListenedClients.remove(apiKey)
                resultHandler.onComplete(new PushResult().setError(new Error(403, e.getMessage)))

            case e:DigakuException =>
                currentListenedClients.remove(apiKey)
                resultHandler.onComplete(new PushResult().setError(new Error(500, e.getMessage)))
        }
    }

}
