/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.model.User
import com.ansvia.digaku.notifications.NonPersistentNotification
import com.ansvia.digaku.utils.RichString._

/**
 * Author: fajr (<EMAIL>)
 */

abstract class ChatPushNotificationBase(chat:Chat, userSource:User, userTargetO:Option[User], message:String,
                               hiddenMessage:String, messageIdsO:Option[List[Long]], notifType:String)
    extends NonPersistentNotification(102) {

    private val id = Digaku.engine.idFactory.createId().asInstanceOf[IDType]

    override def getId: IDType = id

    override lazy val hash: String = "%s:%s:%s:%s:%s:%s:%s".format(chat.getId, userSource.getId,
        userTargetO.map(_.getId).getOrElse(0L), message, hiddenMessage, messageIdsO.map(_.mkString(",")).getOrElse(""),
        notifType).md5

    override def getDefaultReceivers: Seq[User] = {

        userTargetO.map(ut => Seq(ut)).getOrElse {
            chat.kind match {
                case ChatKind.Group =>
                    chat.getActiveParticipants.filterNot(_.getId == userSource)
                case ChatKind.PersonToPerson =>
                    chat.getParticipants.filterNot(_.getId == userSource)
            }
        }
    }

    override def isValid:Boolean = chat.getActiveParticipants.nonEmpty

    override def getCreationTime:IDType = Digaku.engine.dateUtils.nowMilis

    override def renderFor(user:User):String = message
}
