package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.messaging.js.MessageDeliveryStatus
import com.ansvia.digaku.model.{Topic, User}
import com.ansvia.perf.PerfTiming
import org.specs2.specification.Scope

/**
 * Author: nadir (<EMAIL>)
 */

class ScChatHistorySpec extends ChatTest with PerfTiming {

    import ImplicitChatUser._

    "Sc Chat History" should {

        "get unread message count" in new Ctx2 {
            val ch1 = chat.getChatHistoryFor(u1).get
            val ch2 = chat.getChatHistoryFor(u2).get

            ch1.getUnreadMessageCount must_== 21

            val msgs2 = (for (i <- 1 to 20) yield genMessage(u1)).toList

            msgs2.foreach { msg =>
                chat.addMessage(msg)
            }

            // termasuk chat event
            ch2.getUnreadMessageCount must_== 22
        }

        "mark all as read" in new Ctx2 {
            val ch1 = chat.getChatHistoryFor(u1).get

            ch1.getUnreadMessageCount must_== 21
            ch1.markAllMessagesRead() must_== 21
            ch1.getUnreadMessageCount must_== 0

            toIds(ch1.getMessages(None, None, 21).filter(_.getDeliveryStatusInfoFor(u1).status == MessageDeliveryStatus.Read)) must containAllOf(toIds(messages))

            val msg2 = genMessage(u2)
            chat.addMessage(msg2)
            ch1.getUnreadMessageCount must_== 1
            val convs = ch1.getMessages(None, None, limit = 22)
            println("convs: " + convs)
            convs.head.getId must_== msg2.getId
            msg2.getDeliveryStatusInfoFor(u2).status must_!= MessageDeliveryStatus.Read

            toIds(convs) must containAllOf(toIds(messages))
        }

        "get unread messages" in new Ctx2 {
            val ch = chat.getChatHistoryFor(u1).get

            val revMessages = messages.reverse

            // latest message should not seen after this mark as read
            ch.markMessagesRead(List(revMessages.head.getId))

            val msgs5 = ch.getUnreadMessages(None, None, 5)
            msgs5.length must_== 5
            msgs5.map(_.getId).contains(revMessages.head.getId) must beFalse

            val msgs5b = ch.getUnreadMessages(Some(msgs5.last.getId), None, 6).slice(1, 6) // di-slice untuk exclusive
            msgs5.length must_== 5

            println("revMessages: " + revMessages.map(_.getId))
            println("msgs5: " + msgs5.toSeq.map(_.getId))
            println("msgs5b: " + msgs5b.toSeq.map(_.getId))


            // add +1 because the first one already marked as read on previous action
            revMessages.slice(0+1, 5+1).map(_.getId) must containAllOf(msgs5.toSeq.map(_.getId))
            revMessages.slice(5+1, 10+1).map(_.getId) must containAllOf(msgs5b.toSeq.map(_.getId))
        }

        "get all unread messages for initiator and agent" in new Ctx2 {
            val ch1 = chat.getChatHistoryFor(u1).get
            val ch2 = chat.getChatHistoryFor(u2).get

            ch1.getUnreadMessages(None, None, 100).length must_== 21

            val msgs2 = (for (i <- 1 to 20) yield genMessage(u1)).toList

            msgs2.foreach { msg =>
                chat.addMessage(msg)
            }

            // termasuk chat event
            ch2.getUnreadMessages(None, None, 100).length must_== 22
        }

    }

    class Ctx extends Scope {
        val u1 = genUser
        val u2 = genUser
        val u3 = genUser

        val topic = Topic.create("Credit Card", "Tentang Credit Card")

        topic.addUserAgents(u1)

        val ts = Digaku.engine.dateUtils.nowMilis

        val chat = u2.createScChat(topic.getId, "Title Message", "Question Message")

        def genMessage(user:User) = {
            new MessageText(genRandomString)
                .setId(chat.genMessageId)
                .setChatType(ChatType.SCCHAT)
                .setTimestamp(ts)
                .setInitiator(user)
        }
    }

    class Ctx2 extends Ctx {

        val messages = (for (i <- 1 to 20) yield genMessage(u2)).toList

        messages.foreach { msg =>
            chat.addMessage(msg)
        }

        def toId(msg:Message) = msg.getId
        def toIds(msgs:List[Message]) = msgs.map(toId)
    }
}
