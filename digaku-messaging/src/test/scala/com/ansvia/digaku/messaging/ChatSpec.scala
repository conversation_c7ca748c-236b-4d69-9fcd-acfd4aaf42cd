/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.exc.{InvalidParameterException, LimitationReachedException}
import com.ansvia.digaku.messaging.js.MessageDeliveryStatus
import com.ansvia.digaku.model.User
import com.ansvia.perf.PerfTiming
import org.specs2.specification.Scope


class ChatSpec extends ChatTest with PerfTiming {

    import ImplicitChatUser._

    "Chat" should {
        "get chat by id" in new Ctx2 {

            val _chat = Chat.getById(chat.getId)

            _chat must_== Some(chat)

        }
        "able to remove participants" in new Ctx2 {

            chat.getParticipants must contain(u3)

            chat.removeParticipants(u3)

//            chat.reload()

            chat.getParticipants.contains(u3) must beFalse

            // should not in user.getChats also
            u3.getChats(None, None, 10).contains(chat) must beFalse
        }
        "max participants 150" in new Ctx2 {

            for (i <- 1 to (150 - 3)){ // -3 karena sudah ada user sebelumnya
                chat.addParticipants(genUser)
            }

            chat.addParticipants(genUser) must throwAn[LimitationReachedException]
        }
        "publish chat message event" in new Ctx2 {
            val text = "add zufar as participant"
            val msgEvent = chat.publishEvent(u1, text)

            val ch1 = chat.getChatHistoryFor(u1).get
            val ch2 = chat.getChatHistoryFor(u2).get
            val ch3 = chat.getChatHistoryFor(u3).get

            ch1.getMessages(None, None, 1).must(contain(msgEvent))
            ch2.getMessages(None, None, 1).must(contain(msgEvent))
            ch3.getMessages(None, None, 1).must(contain(msgEvent))
        }
        "publish event synch count" in new Ctx2 {

            // satu karena first creation chat group pasti ada satu message event "created"
            chat.getMessageCount must_== 1

            chat.publishEvent(u1, "hello")
            chat.getMessageCount must_== 2

            chat.publishEvent(u1, "hello 2")
            chat.getMessageCount must_== 3

        }
        "event and text message ordered correctly" in new Ctx2 {
            val text = "add zufar as participant"
            val msgEvent = chat.publishEvent(u1, text)

            val messages = u1.sendMessagesText(chat, List("hello #1", "hello #2"))

            messages.head.setDeliveryStatusFor(u2, MessageDeliveryStatus.Read)

            val ch1 = chat.getChatHistoryFor(u1).get
            val ch2 = chat.getChatHistoryFor(u2).get
            val ch3 = chat.getChatHistoryFor(u3).get

            val expectedResult = (List(msgEvent) ++ messages).reverse.map(toId)

            toIds(ch1.getMessages(None, None, 3)).must(containAllOf(expectedResult).inOrder)
            toIds(ch2.getMessages(None, None, 3)).must(containAllOf(expectedResult).inOrder)
            toIds(ch3.getMessages(None, None, 3)).must(containAllOf(expectedResult).inOrder)
        }
        "get active participants" in new Ctx2 {
            chat.getActiveParticipants must contain(u2, u3)

            // no remove participant
            // just deactivate it
            chat.deactivateHistory(u3)

            chat.getActiveParticipants must contain(u1, u2).only
        }
        "is active participant" in new Ctx2 {
            chat.isActiveParticipant(u3) must_== true
            chat.deactivateHistory(u3)
            chat.isActiveParticipant(u3) must_== false
        }
        "deactivate history should not remove ref in user chat list" in new Ctx2 {
            chat.deactivateHistory(u2)
            u2.getChats(None, None, 10).map(_.getId) must contain(chat.getId)
            chat.isActiveParticipant(u2) must_== false
        }
        "activate history" in new Ctx2 {
            chat.deactivateHistory(u2)
            chat.isActiveParticipant(u2) must_== false
            chat.activateHistory(u2)
            chat.isActiveParticipant(u2) must_== true
        }
        "only add message to active participants" in new Ctx2 {
            chat.deactivateHistory(u2)
            val msg = genMessage(u1)
            chat.addMessage(msg)
            chat.getChatHistoryFor(u2).map(_.getLastMessages).get.map(_.getId).contains(msg.getId) must beFalse
            chat.getChatHistoryFor(u3).map(_.getLastMessages).get.map(_.getId) must contain(msg.getId)
        }
        "get message by id" in new Ctx2 {
            val msg = genMessage(u1)
            chat.addMessage(msg)
            chat.getMessageById(msg.getId).map(_.getId) must_== Some(msg.getId)
        }
        "update" in new Ctx2 {
            val newTitle = "hola dora"

            chat.title must_== "my first chat"
            chat.setTitle(newTitle)
            Chat.update(chat)
            Chat.getById(chat.getId).map(_.title) must_== Some(newTitle)

            // check also in user participants
            for (u <- chat.getParticipants){
                for ( chat <- u.getChats(None, None, 10) ){
                    chat.title must_== newTitle
                }
            }
        }
        "cannot add participant to non group chat mode" in new Ctx2 {
            val p2pChat = u1.createP2PChat(u2)

            p2pChat.addParticipants(u3) must throwAn[InvalidParameterException]
        }
    }

    class Ctx extends Scope {
        val u1 = genUser
        val u2 = genUser
        val u3 = genUser
    }

    class Ctx2 extends Ctx {
        def ts = Digaku.engine.dateUtils.nowMilis

        val chat = u1.createGroupChat(Set(u2, u3), "my first chat", "")

        def genMessage(user:User) = {
            new MessageText(genRandomString)
                .setId(chat.genMessageId)
                .setTimestamp(ts)
                .setInitiator(user)
        }

        def toId(msg:Message) = msg.getId
        def toIds(msgs:List[Message]) = msgs.map(toId)

    }

}
