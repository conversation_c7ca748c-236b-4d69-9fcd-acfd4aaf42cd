/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import java.util.concurrent.atomic.AtomicLong

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.persistence.CassandraDriver
import com.ansvia.digaku.{BeforeAllAfterAllMutable, Digaku, DigakuTest, Global}
import org.specs2.mutable.Specification

/**
 * Author: robin (<EMAIL>)
 */
trait ChatTest extends Specification with DigakuTest with BeforeAllAfterAllMutable {

    protected def beforeAll(): Unit ={

        ChatTest.cleanUpDb

        // index messaging related indices
        DatabaseUtil.index()
    }

    protected def afterAll(): Unit = ()

//    override def setUp(): Unit = {
//
//        super.setUp()
//
//
//    }
}

object ChatTest extends Slf4jLogger {

    // setup custom chat engine
    ChatComponentConfig.setEngine(new StandardEngine {
        private val atomicLong = new AtomicLong(0)
        override def generateId(): Long = {
            atomicLong.incrementAndGet()
        }
    })

    lazy val cleanUpDb = {
        if (!Digaku.config.mainDatabase.keyspaceName.endsWith("_test")){
            throw new DigakuException("for test please name the keyspace with postfix `_test`")
        }

        val c = Digaku.engine.config.mainDatabase

        val csd = CassandraDriver.getContext(c.clusterName,
            c.keyspaceName, c.hostName, c.replStrategy, c.replStrategyOpts)

//        info("clean up database for testing...")
        csd.clearColumnFamily(Global.STANDARD_CF_NAME)
        csd.clearColumnFamily("chat_store")
        csd.clearColumnFamily("sc_chat_store")
        csd.clearColumnFamily("queue_sc_chat_store")
        Thread.sleep(13000)
//        info("clean up done.")
    }
}
