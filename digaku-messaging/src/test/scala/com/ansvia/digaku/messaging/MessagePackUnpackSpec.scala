/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.perf.PerfTiming
import org.specs2.specification.Scope

/**
 * Author: robin (<EMAIL>)
 */

class MessagePackUnpackSpec extends ChatTest with PerfTiming {

    "Message encoder/decoder" should {
//        "can do basic message pack/unpack" in new Ctx {
//            val data = msg.toMessagePack
//            println(data.toList.map(x => "0x%02x".format(x)))
//            val storeData = Message.fromMessagePack(data)
//            storeData must anInstanceOf[MessageText]
//        }
        "json encode/decode" in new Ctx {

            timing("json encode/decode"){
                for (i <- 1 to 5000){
                    val data = msg.toJsonString
                    val storeData = Message.fromJsonString(data)
                    storeData must anInstanceOf[MessageText]
                }
            }

        }
//        "msgpack encode/decode" in new Ctx {
//            timing("msgpack encode/decode"){
//                for (i <- 1 to 5000){
//                    val data = msg.toMessagePack
//                    val storeData = Message.fromMessagePack(data)
//                    storeData must anInstanceOf[MessageText]
//                }
//            }
//        }
    }


    class Ctx extends Scope {
        val u1 = genUser
        val msg = new MessageText("hello :)")
            .setId(123)
            .setInitiator(u1)
            .setTimestamp(Digaku.engine.dateUtils.nowMilis)
    }
}
