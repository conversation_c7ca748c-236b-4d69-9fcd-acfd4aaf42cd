/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.exc.AlreadyExistsException
import com.ansvia.digaku.model.User
import com.ansvia.perf.PerfTiming
import org.specs2.specification.Scope


class RemoveChatFunctionalitySpec extends ChatTest with PerfTiming {

    import ImplicitChatUser._


    class Ctx extends Scope {
        val u1 = genUser
        val u2 = genUser
        val u3 = genUser
    }

    class Ctx2 extends Ctx {
        val ts = Digaku.engine.dateUtils.nowMilis

        val chat = u1.createGroupChat(Set(u2, u3), "my first chat", "")

        def genMessage(user:User) = {
            val msg = new MessageText(genRandomString)
            msg.setInitiator(user)
            msg
        }

        def toId(msg:Message) = msg.getId
        def toIds(msgs:List[Message]) = msgs.map(toId)

    }

    class CtxRemove extends Ctx {
        val chat = u1.createP2PChat(u2)

        Chat.getById(chat.getId).isDefined must beTrue

        u2.removeChat(chat.getId)
    }

    "Remove chat functionality" should {
        "remove chat" in new Ctx2 {
            u1.getChatsCount must_== 1

            u1.removeChat(chat.getId)

            u1.getChatsCount must_== 0
            u1.getChats(None, None, 100) must_== Nil
        }
        "should marking to inactive history for user who removes" in new CtxRemove {
            chat.getChatHistoryFor(u1).map(_.isActive) must_== Some(true)
            chat.getChatHistoryFor(u2).map(_.isActive) must_== Some(false)
        }
        "send message to opposite user after opposite user drop the chat should not result in duplicated ref to chat" in new Ctx {
            val p2pChat = u1.createP2PChat(u2)

            u1.sendMessagesText(p2pChat, List("hai"))

            u2.getChatsCount must_== 1
            u2.getChats(None, None, 10) must contain(p2pChat)

            u2.removeChat(p2pChat.getId)

            u2.getChatsCount must_== 0
            u2.getChats(None, None, 10) must_== Nil

            u1.sendMessagesText(p2pChat, List("hallo"))

            // harusnya muncul lagi di u2
            u2.getChatsCount must_== 1
            u2.getChats(None, None, 10) must contain(p2pChat).only
            //            u2.getChats(None, None, 10) must_== Nil
        }
        "remove only from user ref should occurr on initiator only" in new CtxRemove {
            Chat.getById(chat.getId).isDefined must beTrue

            // harus ada di u1 tapi tidak di u2
            u1.getChats(None, None, 2).map(_.getId) must contain(chat.getId)
            u2.getChats(None, None, 2).map(_.getId) must beEmpty

        }
        "when both removed should not exists in both" in new CtxRemove {

            u1.removeChat(chat.getId)

            // harus tidak ada di keduanya
            u1.getChats(None, None, 2).map(_.getId) must beEmpty
            u2.getChats(None, None, 2).map(_.getId) must beEmpty

        }
        "creation after both remove should return old object ref to initiator but not opposite" in new CtxRemove {
            u1.removeChat(chat.getId)

            u1.getChats(None, None, 2).map(_.getId) must beEmpty
            u2.getChats(None, None, 2).map(_.getId) must beEmpty

            u1.createP2PChat(u2)

            println("u1.getChats(None, None, 2).map(_.getId): " + u1.getChats(None, None, 2).map(_.getId))
            println("u2.getChats(None, None, 2).map(_.getId): " + u2.getChats(None, None, 2).map(_.getId))

            u1.getChats(None, None, 2).map(_.getId) must contain(chat.getId)
            u2.getChats(None, None, 2).map(_.getId) must beEmpty

        }
        "when both do the creation for the first time bidirectional should get the same object to the chat" in new CtxRemove {
            u1.removeChat(chat.getId)

            val chat1 = u1.createP2PChat(u2)
            val chat2 = u2.createP2PChat(u1)

            chat1.getId must_== chat2.getId

            u1.getChats(None, None, 2).map(_.getId) must contain(chat.getId)
            u2.getChats(None, None, 2).map(_.getId) must contain(chat.getId)
        }
    }


}
