/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.messaging.js.MessageDeliveryStatus
import com.ansvia.digaku.model.User
import com.ansvia.perf.PerfTiming
import org.specs2.specification.Scope

/**
 * Author: robin (<EMAIL>)
 */

class ChatHistorySpec extends ChatTest with PerfTiming {

    import ImplicitChatUser._

    "Chat history helpers" should {
        "get last conversations" in new Ctx2 {
            val ch = chat.getChatHistoryFor(u1).get
            ch.getTotalMessageCount must_== 20
//            println(messages)
            toIds(ch.getLastMessages) must contain(messages.last.getId)
        }
        "mark all messages as read" in new Ctx2 {
            val ch_u2 = chat.getChatHistoryFor(u2).get
            ch_u2.getUnreadMessageCount must_== 20
            ch_u2.markAllMessagesRead() must_== 20
            ch_u2.getUnreadMessageCount must_== 0

            // semua messages harusnya sudah di-marked as read
            // 21 karena tidak termasuk message event
            toIds(ch_u2.getMessages(None, None, 21).filter(_.getDeliveryStatusInfoFor(u2).status == MessageDeliveryStatus.Read)) must containAllOf(toIds(messages))

            // setelah mark read kalo ada tambahan message
            // maka itu jadi satu-satunya yang unread
            val msg2 = genMessage(u1)
            u1.sendMessages(chat, List(msg2))
            ch_u2.getUnreadMessageCount must_== 1
            val convs = ch_u2.getMessages(None, None, limit = 22)
            println("convs: " + convs)
            convs.head.getId must_== msg2.getId
            msg2.getDeliveryStatusInfoFor(u2).status must_!= MessageDeliveryStatus.Read

            // tapi juga harus berisi yang belum dibaca
            toIds(convs) must containAllOf(toIds(messages))
        }
        "mark messages read partially using ids" in new Ctx2 {
            val ch = chat.getChatHistoryFor(u1).get
            ch.getTotalMessageCount must_== 20
            ch.getUnreadMessageCount must_== 0 // post dari user sendiri gak termasuk
            ch.markMessagesRead(messages.slice(0, 5).map(_.getId))
            ch.getTotalMessageCount must_== 20
            ch.getUnreadMessageCount must_== 0
        }
        "get unread messages" in new Ctx2 {
            val ch = chat.getChatHistoryFor(u1).get

            val revMessages = messages.reverse

            // latest message should not seen after this mark as read
            ch.markMessagesRead(List(revMessages.head.getId))

            val msgs5 = ch.getUnreadMessages(None, None, 5)
            msgs5.length must_== 5
            msgs5.map(_.getId).contains(revMessages.head.getId) must beFalse

            val msgs5b = ch.getUnreadMessages(Some(msgs5.last.getId), None, 6).slice(1, 6) // di-slice untuk exclusive
            msgs5.length must_== 5

            println("revMessages: " + revMessages.map(_.getId))
            println("msgs5: " + msgs5.toSeq.map(_.getId))
            println("msgs5b: " + msgs5b.toSeq.map(_.getId))


            // add +1 because the first one already marked as read on previous action
            revMessages.slice(0+1, 5+1).map(_.getId) must containAllOf(msgs5.toSeq.map(_.getId))
            revMessages.slice(5+1, 10+1).map(_.getId) must containAllOf(msgs5b.toSeq.map(_.getId))

        }
        "clear history" in new Ctx2 {
            val ch = chat.getChatHistoryFor(u1).get
            val ch_u2 = chat.getChatHistoryFor(u2).get
            ch.getTotalMessageCount must_== 20
            ch_u2.getUnreadMessageCount must_== 20
            ch.clear()
            ch.getTotalMessageCount must_== 0
            ch.getUnreadMessageCount must_== 0
        }
    }

    "Mark messages & consumers functionality" should {
        "get message and check for it consumers" in new Ctx3 {
            msgInCh1.getConsumers must contain(u1.getId)
        }
        "mark some message should affect to other consumers" in new Ctx3 {

            msgInCh1.getDeliveryStatusInfoFor(u2).status must_== MessageDeliveryStatus.Sent

            msgInCh2.setDeliveryStatusFor(u2, MessageDeliveryStatus.Delivered)

            msgInCh1.getDeliveryStatusInfoFor(u2).status must_== MessageDeliveryStatus.Delivered

            var count = ch1.markMessagesRead(List(msgInCh1.getId))

            count must_== 1

            msgInCh2.getDeliveryStatusInfoFor(u1).status must_== MessageDeliveryStatus.Read
        }
        "delivery stages" in new Ctx2 {
            val msg1 = genMessage(u2)
            val msg2 = genMessage(u2)

            // user u2 mengirim 2 message ke chat yg berisi u1 dan u3

            u2.sendMessages(chat, List(msg1, msg2))

            msg1.getDeliveryStatusInfoFor(u1).status must_== MessageDeliveryStatus.Sent
            // untuk pengirimnya sendiri status-nya harusnya sudah `Read`
            msg1.getDeliveryStatusInfoFor(u2).status must_== MessageDeliveryStatus.Read
            msg1.getDeliveryStatusInfoFor(u3).status must_== MessageDeliveryStatus.Sent

            // server menandai telah di-deliver ke participants
            msg1.setDeliveryStatusFor(u3, MessageDeliveryStatus.Delivered)
            msg2.setDeliveryStatusFor(u3, MessageDeliveryStatus.Delivered)
            msg1.setDeliveryStatusFor(u1, MessageDeliveryStatus.Delivered)
            msg2.setDeliveryStatusFor(u1, MessageDeliveryStatus.Delivered)

            msg1.getDeliveryStatusInfoFor(u3).status must_== MessageDeliveryStatus.Delivered
            msg2.getDeliveryStatusInfoFor(u3).status must_== MessageDeliveryStatus.Delivered
            msg1.getDeliveryStatusInfoFor(u1).status must_== MessageDeliveryStatus.Delivered
            msg2.getDeliveryStatusInfoFor(u1).status must_== MessageDeliveryStatus.Delivered

            // user u3 me-mark chat-nya as read semua
            val ch = chat.getChatHistoryFor(u3).get
            ch.markAllMessagesRead()

            msg1.getDeliveryStatusInfoFor(u3).status must_== MessageDeliveryStatus.Read
            msg2.getDeliveryStatusInfoFor(u3).status must_== MessageDeliveryStatus.Read
            msg1.getDeliveryStatusInfoFor(u1).status must_== MessageDeliveryStatus.Delivered
            msg2.getDeliveryStatusInfoFor(u1).status must_== MessageDeliveryStatus.Delivered

        }
    }

    class Ctx extends Scope {
        val u1 = genUser
        val u2 = genUser
        val u3 = genUser
    }

    class Ctx2 extends Ctx {
        val ts = Digaku.engine.dateUtils.nowMilis

        val chat = u1.createGroupChat(Set(u2, u3), "my first chat", "")

        val messages = (for (i <- 1 to 20) yield genMessage(u1)).toList

        u1.sendMessages(chat, messages)

        def genMessage(user:User) = {
            new MessageText(genRandomString)
        }

        def toId(msg:Message) = msg.getId
        def toIds(msgs:List[Message]) = msgs.map(toId)
    }

    class Ctx3 extends Ctx2 {
        val ch1 = chat.getChatHistoryFor(u1).get
        val ch2 = chat.getChatHistoryFor(u2).get

        val msgInCh1 = ch1.getMessage(messages.head.getId).get
        val msgInCh2 = ch2.getMessage(messages.head.getId).get
    }

}
