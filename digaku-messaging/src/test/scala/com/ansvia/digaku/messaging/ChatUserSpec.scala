/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.exc.AlreadyExistsException
import com.ansvia.digaku.model.User
import com.ansvia.perf.PerfTiming
import org.specs2.specification.Scope



class ChatUserSpec extends ChatTest with PerfTiming {

    import ImplicitChatUser._


    class Ctx extends Scope {
        val u1 = genUser
        val u2 = genUser
        val u3 = genUser
    }

    class Ctx2 extends Ctx {
        val ts = Digaku.engine.dateUtils.nowMilis

        val chat = u1.createGroupChat(Set(u2, u3), "my first chat", "")

        def genMessage(user:User) = {
            val msg = new MessageText(genRandomString)
            msg.setInitiator(user)
            msg
        }

        def toId(msg:Message) = msg.getId
        def toIds(msgs:List[Message]) = msgs.map(toId)

    }


    "Chat user helpers" should {
        "create chat to one user" in new Ctx {
            u1.createP2PChat(u2)
        }
        "create chat to multiple users" in new Ctx {
            val chat = u1.createGroupChat(Set(u2, u3), "my first chat", "")

            println("chat.getParticipants: \n" + chat.getParticipants)
            chat.getParticipants must contain(u1, u2, u3).only
        }
        "add another user to a chat" in new Ctx2 {
            val u4 = genUser
            u1.addChatUsers(chat, Seq(u4))

            chat.getParticipants must contain(u1, u4).only
        }
        "send message" in new Ctx2 {
            val _msg1 = new MessageText("hello :)")
            val _msg2 = new MessageText("wow")

            val histoU1 = chat.getChatHistoryFor(u1).get
            val histoU2 = chat.getChatHistoryFor(u2).get
            val histoU3 = chat.getChatHistoryFor(u3).get

            var msg1: Message = null

            {
                val msgs = u1.sendMessage(chat, _msg1)
                msg1 = msgs.head
                println("msg1: " + msg1)

                toIds(histoU1.getLastMessages) must contain(msg1.getId).only
                toIds(histoU2.getLastMessages) must contain(msg1.getId).only
                toIds(histoU3.getLastMessages) must contain(msg1.getId).only
                toIds(histoU3.getLastMessages).contains(_msg2.getId) must beFalse
            }

            {
                val msgs = u3.sendMessage(chat, _msg2)
                val msg2 = msgs.head
                toIds(histoU1.getLastMessages) must contain(msg1.getId, msg2.getId)
                toIds(histoU2.getLastMessages) must contain(msg1.getId, msg2.getId)
                toIds(histoU3.getLastMessages) must contain(msg1.getId, msg2.getId)
            }

        }
        "get chat list" in new Ctx2 {
            // u1 dimasukkin lagi sebagai participants, harusnya ke-distinct
            val chat2 = u1.createGroupChat(Set(u2, u1, u3), "my seconds chat", "'")
            val chat3 = u1.createP2PChat(u2)

            u1.reload()
            u2.reload()
            u3.reload()

            u1.getChats(None, None, 10) must contain(chat3, chat2, chat).only.inOrder
            // chat3 (p2p) tidak termasuk di u2 karena sifat inactivity chat history pada p2p
            u2.getChats(None, None, 10) must contain(chat2, chat).only.inOrder
            u3.getChats(None, None, 10) must contain(chat, chat2).only

            // latest update chat must ordered on top
            chat.update()

            println("u1.getChats(None, None, 10): " + u1.getChats(None, None, 10))
            u1.getChats(None, None, 10) must contain(chat, chat3, chat2).only.inOrder
        }
        "get contacts" in new Ctx {

            u1.createP2PChat(u2)
            u1.createP2PChat(u3)

            u1.reload()
            u2.reload()
            u3.reload()
            u1.getContacts().map(_.getId) must contain(u2.getId, u3.getId).only
        }
        "cannot assign already participant as participant again" in new Ctx2 {
            u1.addChatUsers(chat, Seq(u3)) must throwAn[AlreadyExistsException]

            val u4 = genUser
//            chat.reload()
            u4.reload()

            u1.addChatUsers(chat, Seq(u4)) // must not throw
            chat.getParticipants.map(_.getId) must contain(u4.getId)
        }
        "find chat by user" in new Ctx {
            val chat2 = u1.createP2PChat(u2)
            val chat3 = u1.createP2PChat(u3)

            u1.findP2PChatByUserId(u2.getId) must_== Some(chat2)
            u1.findP2PChatByUserId(u3.getId) must_== Some(chat3)
        }
        "contacts only visible by initiator" in new Ctx {
            val chat = u1.createP2PChat(u2)

            u1.getContacts().toList must contain(u2)

            u2.getContacts().toList.contains(u1) must beFalse
        }
        "get the same chat object bidirectional for the first time creation" in new Ctx {
            val chat1 = u1.createP2PChat(u2)
            val chat2 = u2.createP2PChat(u1)

            chat1.getId must_== chat2.getId

            // and all history should active
            chat1.getChatHistoryFor(u1).map(_.isActive) must_== Some(true)
            chat1.getChatHistoryFor(u2).map(_.isActive) must_== Some(true)
        }
        "create p2p chat must return in 2 participants immediatelly" in new Ctx {
            val chat = u1.createP2PChat(u2)

            val _chat = Chat.getById(chat.getId).get

            _chat.getParticipantCount must_== 2
            _chat.getParticipants.map(_.getId).toList must contain(u1.getId)
            _chat.getParticipants.map(_.getId).toList must contain(u2.getId)

        }
    }


    "Chat P2P inactivity mechanism" should {
        "opposite user must not have chat list immediately after initiated" in new Ctx {
            val chat = u1.createP2PChat(u2)

            u2.getChats(None, None, 10).contains(chat) must beFalse
            chat.getChatHistoryFor(u2).map(_.isActive) must_== Some(false)
        }
        "opposite user only have when initiator send for the first message" in new Ctx {
            val chat = u1.createP2PChat(u2)

            u2.getChats(None, None, 10).contains(chat) must beFalse
            chat.getChatHistoryFor(u2).map(_.isActive) must_== Some(false)

            // but must have when u1 send message for the first time
            u1.sendMessagesText(chat, List("hallo"))

            u2.getChats(None, None, 10) must contain(chat)

            chat.getChatHistoryFor(u2).map(_.isActive) must_== Some(true)

        }
        "or when opposite user make a first message" in new Ctx {
            val chat = u1.createP2PChat(u2)

            u2.getChats(None, None, 10).contains(chat) must beFalse
            chat.getChatHistoryFor(u2).map(_.isActive) must_== Some(false)

            // but must have when u2 send message for the first time
            u2.sendMessagesText(chat, List("hallo"))

            u2.getChats(None, None, 10) must contain(chat)

            chat.getChatHistoryFor(u2).map(_.isActive) must_== Some(true)

        }
    }

}
