/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.model.User
import com.ansvia.perf.PerfTiming
import org.specs2.specification.Scope


class PerformanceSpec extends ChatTest with PerfTiming {

    import ImplicitChatUser._


    class Ctx extends Scope {
        val u1 = genUser
        val u2 = genUser
        val u3 = genUser
    }

    class Ctx2 extends Ctx {
        val ts = Digaku.engine.dateUtils.nowMilis

        val chat = u1.createGroupChat(Set(u2, u3), "my first chat", "")

        def genMessage(user:User) = {
            val msg = new MessageText(genRandomString)
            msg.setInitiator(user)
            msg
        }

        def toId(msg:Message) = msg.getId
        def toIds(msgs:List[Message]) = msgs.map(toId)

    }
//
//    performance of "Chat" in new Ctx2 {
//        measure method "insert" in {
//            withSize upTo 10 withSetup { size =>
//                (1 to size).toList
//            } run { col =>
//                col.foreach { x =>
//                    println("u1: " + u1)
//                    u1.sendMessage(chat, genMessage(u1))
//                }
//            }
//        }
//    }

    val test = this

    "Performance" should {
        "test" in new Ctx2 {

//            val context = new DefaultRunContext(new File("target/sperformance", test.name), test.name)
//            test.runTest(context)
//
//            context.generateResultsPage()

            val userChat = new ChatUser(u1)

            timing("test insert"){
                for (i <- 0 to 300){
                    userChat.sendMessage(chat, genMessage(u1))
                }
            }

        }
    }

//
//    "Chat user helpers" should {
//        "insert performance" in new Ctx2 {
//            timing("insert performance"){
//                for (i <- 1 to 500){
//                    u1.sendMessage(chat, genMessage(u1))
//                }
//            }
//        }
//    }

}
