package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.messaging.js.ScChatStatusType
import com.ansvia.digaku.model.{Topic, User}
import com.ansvia.perf.PerfTiming
import org.specs2.specification.Scope

/**
* Author: nadir (<EMAIL>)
*/

class ScChatSpec extends ChatTest with PerfTiming {

    import ImplicitChatUser._

    "Chat" should {
        "get sc chat by id" in new Ctx2 {
            val _chat = ScChat.getById(chat.getId)

            _chat must_== Some(chat)
        }

        "get agent sc chat" in new Ctx2 {
            chat.getAgent must_== Some(u1)
        }

        "get message sc chat" in new Ctx2 {
            chat.getAgent must_== Some(u1)

            val initiatorMsg = chat.getChatHistoryFor(u2).get.getMessages(None, None, 100)
            val agentMsg = chat.getChatHistoryFor(u1).get.getMessages(None, None, 100)

            initiatorMsg.length mustEqual 3
            initiatorMsg.lastOption.exists(_.asInstanceOf[MessageText].getText == questionContent) must beTrue

            agentMsg.length mustEqual 1
            agentMsg.headOption.exists(_.asInstanceOf[MessageText].getText == questionContent) must beTrue
        }

        "distribution chat for agents in one topic" in {
            val topic = Topic.create("Credit Card", "Tentang Credit Card")
            val u1 = genUser
            val u2 = genUser
            val u3 = genUser
            val u4 = genUser
            val u5 = genUser
            val u6 = genUser
            val u7 = genUser
            val u8 = genUser
            val u9 = genUser
            val u10 = genUser
            val u11 = genUser
            val u12 = genUser
            val u13 = genUser

            topic.addUserAgents(u1)
            topic.addUserAgents(u2)
            topic.addUserAgents(u3)

            u4.createScChat(topic.getId, "Title Chat 1", "Content Chat 1")
            u5.createScChat(topic.getId, "Title Chat 2", "Content Chat 2")
            u6.createScChat(topic.getId, "Title Chat 3", "Content Chat 3")
            u7.createScChat(topic.getId, "Title Chat 4", "Content Chat 4")
            u8.createScChat(topic.getId, "Title Chat 5", "Content Chat 5")
            u9.createScChat(topic.getId, "Title Chat 6", "Content Chat 6")
            u10.createScChat(topic.getId, "Title Chat 7", "Content Chat 7")
            u11.createScChat(topic.getId, "Title Chat 8", "Content Chat 8")
            u12.createScChat(topic.getId, "Title Chat 9", "Content Chat 9")
            u13.createScChat(topic.getId, "Title Chat 10", "Content Chat 10")

            val a = u1.getAgentScChats(None, None, 100)
            val b = u2.getAgentScChats(None, None, 100)
            val c = u3.getAgentScChats(None, None, 100)

            a.length mustEqual 3
            b.length mustEqual 3
            c.length mustEqual 3
        }

        "close sc chat" in new Ctx2 {

            val u4 = genUser
            val u5 = genUser
            val u6 = genUser
            val u7 = genUser
            val u8 = genUser
            val u9 = genUser

            val chat2 = u3.createScChat(topicId, "Chat 2", "Question Chat 2")
            val chat3 = u4.createScChat(topicId, "Chat 3", "Question Chat 3")

            u1.getAgentScChats(None, None, 100).must(containAllOf(Seq(chat3, chat2, chat)).inOrder)

            u1.closeScChat(chat2.getId, ScChatStatusType.SOLVED, "tags,chat", "Chat Solved", "Question", 0L)

            ScChat.getById(chat2.getId).exists(_.getLastStatus.exists(_.status == ScChatStatusType.SOLVED)) must beTrue

            u1.getAgentScChats(None, None, 100).must(containAllOf(Seq(chat3, chat, chat2)).inOrder)

            u1.closeScChat(chat3.getId, ScChatStatusType.SOLVED, "tags,chat", "Chat Solved", "Question", 0L)

            u1.getAgentScChats(None, None, 100).must(containAllOf(Seq(chat, chat3, chat2)).inOrder)

            val chat4 = u5.createScChat(topicId, "Chat 4", "Question Chat 4")
            val chat5 = u6.createScChat(topicId, "Chat 5", "Question Chat 5")

            u1.getAgentScChats(None, None, 100).must(containAllOf(Seq(chat5, chat4, chat, chat3, chat2)).inOrder)

            u1.closeScChat(chat4.getId, ScChatStatusType.SOLVED, "tags,chat", "Chat Solved", "Question", 0L)
            u1.getAgentScChats(None, None, 100).must(containAllOf(Seq(chat5, chat, chat4, chat3, chat2)).inOrder)


            u1.closeScChat(chat5.getId, ScChatStatusType.SOLVED, "tags,chat", "Chat Solved", "Question", 0L)
            u1.getAgentScChats(None, None, 100).must(containAllOf(Seq(chat, chat5, chat4, chat3, chat2)).inOrder)

            u1.closeScChat(chat.getId, ScChatStatusType.SOLVED, "tags,chat", "Chat Solved", "Question", 0L)
            u1.getAgentScChats(None, None, 100).must(containAllOf(Seq(chat, chat5, chat4, chat3, chat2)).inOrder)
        }

        "Queue Sc Chat" in new Ctx {
            val u4 = genUser
            val u5 = genUser
            val u6 = genUser
            val u7 = genUser
            val u8 = genUser
            val u9 = genUser
            val u10 = genUser
            val u11 = genUser
            val u12 = genUser
            val u13 = genUser
            val u14 = genUser

            val topic = Topic.create("Credit Card", "Tentang Credit Card")
            val topicId = topic.getId

            topic.addUserAgents(u1)
            topic.addUserAgents(u2)

            val chat1 = u3.createScChat(topic.getId, "Title Chat 0", "Content Chat 0")
            val chat2 = u4.createScChat(topic.getId, "Title Chat 1", "Content Chat 1")
            val chat3 = u5.createScChat(topic.getId, "Title Chat 2", "Content Chat 2")
            val chat4 = u6.createScChat(topic.getId, "Title Chat 3", "Content Chat 3")
            val chat5 = u7.createScChat(topic.getId, "Title Chat 4", "Content Chat 4")
            val chat6 = u8.createScChat(topic.getId, "Title Chat 5", "Content Chat 5")
            val chat7 = u9.createScChat(topic.getId, "Title Chat 6", "Content Chat 6")
            val chat8 = u10.createScChat(topic.getId, "Title Chat 7", "Content Chat 7")
            val chat9 = u11.createScChat(topic.getId, "Title Chat 8", "Content Chat 8")
            val chat10 = u12.createScChat(topic.getId, "Title Chat 9", "Content Chat 9")
            val chat11 = u13.createScChat(topic.getId, "Title Chat 10", "Content Chat 10")
            val chat12 = u14.createScChat(topic.getId, "Title Chat 12", "Content Chat 12")

            val u1ScChat = Seq(chat5, chat3, chat1)
            val u2ScChat = Seq(chat6, chat4, chat2)

            val inQueue = Seq(chat7, chat8, chat9, chat10, chat11, chat12)

            u1ScChat.count(_.getAgent.exists(_ == u1)) must_== u1ScChat.length
            u2ScChat.count(_.getAgent.exists(_ == u2)) must_== u2ScChat.length

            u1.getAgentScChats(None, None, 100).must(containAllOf(u1ScChat).only.inOrder)
            u2.getAgentScChats(None, None, 100).must(containAllOf(u2ScChat).only.inOrder)

            inQueue.count(_.getAgent.isEmpty) must_== inQueue.length

            u1.closeScChat(chat3.getId, ScChatStatusType.SOLVED, "tags,chat", "", "Question", 0L)
            u1.closeScChat(chat1.getId, ScChatStatusType.SOLVED, "tags,chat", "", "Question", 0L)
            u1.getAgentScChats(None, None, 100).must(containAllOf(Seq(chat8, chat7, chat5, chat1, chat3)).only.inOrder)

            u2.closeScChat(chat6.getId, ScChatStatusType.SOLVED, "tags,chat", "", "Question", 0L)
            u2.closeScChat(chat2.getId, ScChatStatusType.SOLVED, "tags,chat", "", "Question", 0L)
            u2.getAgentScChats(None, None, 100).must(containAllOf(Seq(chat10, chat9, chat4, chat2, chat6)).only.inOrder)

            u1.closeScChat(chat7.getId, ScChatStatusType.SOLVED, "tags,chat", "", "Question", 0L)
            u2.closeScChat(chat9.getId, ScChatStatusType.SOLVED, "tags,chat", "", "Question", 0L)
            u1.getAgentScChats(None, None, 100).must(containAllOf(Seq(chat11, chat8, chat5, chat7, chat1, chat3)).only.inOrder)
            u2.getAgentScChats(None, None, 100).must(containAllOf(Seq(chat12, chat10, chat4, chat9, chat2, chat6)).only.inOrder)

        }

        "get active chat initiator message" in new Ctx2 {
            u2.getActiveScChat.get must equalTo(chat)
        }
    }

    class Ctx extends Scope {
        val u1 = genUser
        val u2 = genUser
        val u3 = genUser
    }

    class Ctx2 extends Ctx {

        def ts = Digaku.engine.dateUtils.nowMilis

        val topic = Topic.create("Credit Card", "Tentang Credit Card")

        val topicId = topic.getId

        topic.addUserAgents(u1)

        val questionContent = "Question Content"

        val chat = u2.createScChat(topic.getId, "Question Title", questionContent)

        def genMessage(user:User) = {
            new MessageText(genRandomString)
                .setId(chat.genMessageId)
                .setChatType(ChatType.SCCHAT)
                .setTimestamp(ts)
                .setInitiator(user)
        }

        def toId(msg:Message) = msg.getId
        def toIds(msgs:List[Message]) = msgs.map(toId)

    }
}
