/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.messaging

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.messaging.js.MessageDeliveryStatus
import com.ansvia.digaku.model.User
import org.specs2.specification.Scope

/**
 * Author: robin (<EMAIL>)
 */

class MessageSpec extends ChatTest {

    import ImplicitChatUser._


    "Chat message" should {
        "accumulate status" in new Ctx2 {
            val msg1 = genMessage(u1)
            u1.sendMessages(chat, List(msg1))

            msg1.getDeliveryStatusInfoFor(u2).status must_== MessageDeliveryStatus.Sent
            msg1.getDeliveryStatusInfoFor(u3).status must_== MessageDeliveryStatus.Sent

            msg1.accumulateStatus() must_== MessageDeliveryStatus.Sent

            msg1.setDeliveryStatusFor(u2, MessageDeliveryStatus.Delivered)

            msg1.accumulateStatus() must_== MessageDeliveryStatus.Sent

            msg1.setDeliveryStatusFor(u2, MessageDeliveryStatus.Delivered)
            msg1.setDeliveryStatusFor(u3, MessageDeliveryStatus.Delivered)

            msg1.accumulateStatus() must_== MessageDeliveryStatus.Delivered
        }
        "message event" in new Ctx2 {
            val text = "add zufar as participant"
            val msgEvent = chat.publishEvent(u1, text)

            msgEvent.getId must_!= 0L
            msgEvent must anInstanceOf[MessageEvent]
            msgEvent.asInstanceOf[MessageEvent].eventText must_== text
        }
        "message event with metadata" in new Ctx2 {
            val text = "add zufar as participant"
            val msgEvent = chat.publishEvent(u1, text)
            val data = Map("hello" -> "cool")
            msgEvent.setMetaData(data)
            chat.updateMessage(msgEvent)

            chat.getMessageById(msgEvent.getId) match {
                case Some(x:MessageEvent) => x.metaData must_== data
                case _ => failure("no message event in db")
            }
        }
        "get delivery status info" in new Ctx2 {
            val messages = u1.sendMessagesText(chat, List("oi oi"))

            messages.head.getDeliveryStatusInfoFor(u1).status.getCode must_== MessageDeliveryStatus.Read.getCode
            messages.head.getDeliveryStatusInfoFor(u2).status.getCode must_== MessageDeliveryStatus.Sent.getCode

            messages.head.getDeliveryStatusInfoFor(u1).time must greaterThan(0L)
            messages.head.getDeliveryStatusInfoFor(u2).time must greaterThan(0L)

            chat.ackMessages(u2, messages.map(_.getId))

            val messages2 = chat.getChatHistoryFor(u2).get.getLastMessages.filter(_.getId == messages.head.getId)
            messages2.head.getDeliveryStatusInfoFor(u2).status.getCode must_== MessageDeliveryStatus.Delivered.getCode

            chat.markAllMessagesRead(u2)

            val messages3 = chat.getChatHistoryFor(u2).get.getLastMessages.filter(_.getId == messages.head.getId)
            messages3.head.getDeliveryStatusInfoFor(u2).status.getCode must_== MessageDeliveryStatus.Read.getCode

            // ditandai lagi sebagai delivered seharusnya tidak ngefek
            chat.ackMessages(u2, messages.map(_.getId))
            val messages4 = chat.getChatHistoryFor(u2).get.getLastMessages.filter(_.getId == messages.head.getId)
            messages4.head.getDeliveryStatusInfoFor(u2).status.getCode must_== MessageDeliveryStatus.Read.getCode
        }
    }


    class Ctx extends Scope {
        val u1 = genUser
        val u2 = genUser
        val u3 = genUser
    }

    class Ctx2 extends Ctx {
        val ts = Digaku.engine.dateUtils.nowMilis

        val chat = u1.createGroupChat(Set(u2, u3), "my first chat", "")

        def genMessage(user:User) = {
            val msg = new MessageText(genRandomString)
            msg.setInitiator(user)
            msg
        }

        def toId(msg:Message) = msg.getId
        def toIds(msgs:List[Message]) = msgs.map(toId)

    }


}
