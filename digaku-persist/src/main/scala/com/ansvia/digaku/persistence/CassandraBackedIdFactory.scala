///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.persistence
//
//import com.tinkerpop.blueprints.util.wrappers.id.IdGraph.IdFactory
//import com.tinkerpop.blueprints.Graph
//import com.ansvia.commons.logging.Slf4jLogger
//import scala.collection.JavaConversions._
//import com.netflix.astyanax.serializers.ComparatorType
//
///**
// * Author: robin
// *
// */
//class CassandraBackedIdFactory(clientId:Long,
//                               context:CassandraDriver.Context,
//                               val collisionDetector:Boolean=true) extends IdFactory with Slf4jLogger {
//
//
//
//    private def ensureColumnFamilyExists(cf: String, ks: String){
//        val ctx = context.cluster.getClient
//
//        val ksDef = ctx.describeKeyspace(ks)
//
//        var found = false
//        if (ksDef != null){
//            for (cdef <- ksDef.getColumnFamilyList){
//                found |= cdef.getName.equals(cf)
//            }
//        }
//
//        if (!found){
//            val cfDef = ctx.makeColumnFamilyDefinition()
//                .setName(cf)
//                .setKeyspace(ks)
//                .setGcGraceSeconds(0)
//                .setComparatorType(ComparatorType.BYTESTYPE.getClassName)
//                .setKeyValidationClass(ComparatorType.UTF8TYPE.getClassName)
//                .setDefaultValidationClass(ComparatorType.COUNTERTYPE.getClassName)
//                .setCompressionOptions(Map("sstable_compression" -> ""))
//            ctx.addColumnFamily(cfDef)
//        }
//    }
//
//    val csid:IdBatchGenerator = {
//        val columnFamilyName = "idgen_lock"
//        val colName = "idgen"
//        val rv = CassandraId.stripedGenerator(context.keyspace,
//            columnFamilyName, colName, 1, 1, CassandraBackedIdFactory.BATCH_LENGTH, clientId)
//        ensureColumnFamilyExists(columnFamilyName, context.keyspace.getKeyspaceName)
////        context.ensureColumnFamilyExists(colName, context.keyspace.getKeyspaceName)
//        rv
//    }
//
//
//    var db:Graph = _
//
//    def createId() = {
//        if (collisionDetector){
//            assert(db != null, "db not defined, please set it first before can creating id with collision detector")
//            var id = csid.generate().asInstanceOf[AnyRef]
//            var v = db.getVertex(id)
//            var collision = false
//            var failCounter = 0
//            while(v != null){
//                warn("id %s already used by %s in internal db, regenerating new one".format(id, v))
//                id = csid.generate().asInstanceOf[AnyRef]
//                v = db.getVertex(id)
//                collision = true
//                failCounter = failCounter + 1
//                if (failCounter % 10 == 0){
//                    csid.setNextBatch((id.asInstanceOf[Long] + 1000).toString.substring(1).toLong)
//                }
//            }
//            if (collision){
//                info("good id generated: " + id)
//            }
//            id
//        }else{
//            csid.generate().asInstanceOf[AnyRef]
//        }
//    }
//
//    override def toString = "CassandraIdFactory(" + clientId + ")"
//}
//
//object CassandraBackedIdFactory {
//    val BATCH_LENGTH = 20
//}