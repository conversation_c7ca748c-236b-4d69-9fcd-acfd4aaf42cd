/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.persistence

import com.netflix.astyanax.connectionpool.NodeDiscoveryType
import com.netflix.astyanax.connectionpool.exceptions.NotFoundException
import com.netflix.astyanax.connectionpool.impl.ConnectionPoolType
import com.netflix.astyanax.model.ColumnFamily
import com.netflix.astyanax.serializers.{ObjectSerializer, StringSerializer}


/**
 * Author: robin
 *
 */
class CassandraBackedKVStoreFactory(cfName:String,
                                      keyspace:String, clusterName:String,
                                      seeds:String, replicationStrategy:String,
                                      replicationStrategyOpts:String,
                                      discoveryType:NodeDiscoveryType=NodeDiscoveryType.RING_DESCRIBE,
                                      connectionPoolType:ConnectionPoolType=ConnectionPoolType.ROUND_ROBIN){

    require(replicationStrategyOpts.contains(":"), "Invalid strategyOpts: `" + replicationStrategyOpts + "`")

    lazy val CF = new ColumnFamily[String, String](
        cfName,
        StringSerializer.get(),
        StringSerializer.get(),
        ObjectSerializer.get())

    lazy val csh = new CassandraDriver[String, String](keyspace, CF,
        clusterName=clusterName, seeds=seeds, strategyClass=replicationStrategy,
        strategyOptsStr=replicationStrategyOpts,
        discoveryType=discoveryType,
        connectionPoolType=connectionPoolType)


    def build(baseKey:String) = {
        new CassandraBackedKVStore(baseKey, this)
    }


}


class CassandraBackedKVStore(baseKey:String, backend:CassandraBackedKVStoreFactory) extends BackedKVStoreIface {
    private val csh = backend.csh

    def get(key:String, defVal:Byte):Byte = {
        get(key, defVal.asInstanceOf[AnyRef]).asInstanceOf[Byte]
    }

    def get(key:String, defVal:Int):Int = {
        get(key, defVal.asInstanceOf[AnyRef]).asInstanceOf[Int]
    }

    def get(key:String, defVal:Long):Long = {
        get(key, defVal.asInstanceOf[AnyRef]).asInstanceOf[Long]
    }

    def get(key:String, defVal:Double):Double = {
        val v = get(key, defVal.asInstanceOf[AnyRef])
        v match {
            case _: java.lang.Double => Double.unbox(v)
            case _ => 0.0D
        }
    }

    def get(key:String, defVal:Boolean):Boolean = {
        val v = get(key, defVal.asInstanceOf[AnyRef])
        v match {
            case _: java.lang.Boolean => Boolean.unbox(v)
            case _ => false
        }
    }

    def get(key:String, defVal:String):String = {
        get(key, defVal.asInstanceOf[AnyRef]).asInstanceOf[String]
    }

    /**
     * Get value by key.
     * @param key key.
     * @param defVal default value when key not exists.
     * @return
     */
    def get(key:String, defVal:AnyRef) = {
        csh.usingQuery { q =>
            try {
                q.getKey(baseKey).getColumn(key).execute().getResult
                    .getValue(ObjectSerializer.get())
            } catch {
                case e:NotFoundException =>
                    defVal
            }
        }
    }

    /**
     * Check apakah key exists.
     * @param key key untuk dicheck.
     * @return
     */
    def exists(key:String) = {
        csh.usingQuery { q =>
            try {
                q.getKey(baseKey).getColumn(key).execute().getResult.hasValue
            } catch {
                case e:NotFoundException =>
                    false
            }
        }
    }

    /**
     * Get value by key return Option.
     * @param key key.
     * @return
     */
    def getOption[T <: AnyRef](key:String):Option[T] = {
        csh.usingQuery { q =>
            try {
                val value = q.getKey(baseKey).getColumn(key).execute()
                    .getResult
                if (value != null)
                    Some(value.getValue(ObjectSerializer.get()).asInstanceOf[T])
                else
                    None

            } catch {
                case e:NotFoundException =>
                    None
            }
        }
    }


    /**
     * Get all key + value.
     * @return
     */
    def getAll:Iterator[(String, AnyRef)] = {
        csh.usingQuery { q =>
            val columns = q.getKey(baseKey).execute().getResult
            var rv = Array.empty[(String, Object)]
            for ( i <- 0 to columns.size()-1 ){
                val col = columns.getColumnByIndex(i)
                //                rv :+= (col.getName, col.getLongValue)

                rv :+= (col.getName, col.getValue[Object](ObjectSerializer.get()))

                //                val value = manifest[T].erasure.toString match {
                //                    case "class java.lang.String" => col.getStringValue
                //                    case "class java.lang.Integer" | "int" => col.getIntegerValue
                //                    case "class java.lang.Long" | "long" => col.getLongValue
                //                    case "boolean" =>
                //                        col.getStringValue match {
                //                            case "\u0001" => Some(true.asInstanceOf[T])
                //                            case _ => Some(false.asInstanceOf[T])
                //                        }
                //                    case "class java.lang.Boolean" =>
                //                        Some(col.getBooleanValue.asInstanceOf[T])
                //                    case "class java.lang.Object" => col.getStringValue
                //                }
                //
                //                rv :+= (col.getName, value.asInstanceOf[T])
            }
            rv.toIterator
        }
    }

    /**
     * Get count all records inside.
     * @return
     */
    def getCount = {
        csh.usingQuery { q =>
            q.getKey(baseKey).getCount.execute().getResult
        }
    }



    /**
     * Set value to storage.
     * @param key key
     * @param value value
     */
    def set(key: String, value:AnyRef) {
        set(key, value, None)
    }

    def set(key: String, value:Byte) {
        set(key, value, None)
    }

    def set(key: String, value:Int) {
        set(key, value, None)
    }

    def set(key: String, value:Long) {
        set(key, value.asInstanceOf[AnyRef], None)
    }

    def set(key: String, value:Boolean) {
        set(key, value.asInstanceOf[AnyRef], None)
    }

    def set(key: String, value:Double) {
        set(key, value.asInstanceOf[AnyRef], None)
    }

    def set(key:String, value:Byte, expiration:Option[Int]){
        set(key, value.asInstanceOf[AnyRef], expiration)
    }

    def set(key:String, value:Int, expiration:Option[Int]){
        set(key, value.asInstanceOf[AnyRef], expiration)
    }

    def set(key:String, value:Long, expiration:Option[Int]){
        set(key, value.asInstanceOf[AnyRef], expiration)
    }

    def set(key:String, value:Double, expiration:Option[Int]){
        set(key, value.asInstanceOf[AnyRef], expiration)
    }

    def set(key:String, value:Boolean, expiration:Option[Int]){
        set(key, value.asInstanceOf[AnyRef], expiration)
    }

    /**
     * Just like #set but since
     * Cassandra >= 0.7 support TTL we implement parameter
     * for setting ttl.
     * @param key ke
     * @param value value
     * @param expiration expiration TTL in seconds.
     */
    def set(key:String, value:AnyRef, expiration:Option[Int]=None){
        csh.usingMutator { (cf, m) =>

            val row = m.withRow(cf, baseKey)

            val exp:java.lang.Integer = if (expiration.isDefined)
                expiration.get
            else
                null

            row.putColumn(key, value, ObjectSerializer.get(), exp)
        }
    }

    /**
     * Delete record by key.
     * @param key object key to delete.
     */
    def delete(key:String){
        csh.usingMutator { (cf, m) =>
            m.withRow(cf, baseKey).deleteColumn(key)
        }
    }

    /**
     * copy storage from to.
     * @param to target storage to write.
     */
    def copyTo(to: CassandraBackedKVStore) {
        import scala.collection.JavaConversions._
        csh.usingQuery { q =>
            val rv = q.getKey(baseKey).execute().getResult
            csh.usingMutator { (cf, m) =>
                rv.getColumnNames.foreach { colName =>
                    val col = rv.getColumnByName(colName)
                    to.set(colName, col.getByteArrayValue)
                }
            }
        }
    }
}
