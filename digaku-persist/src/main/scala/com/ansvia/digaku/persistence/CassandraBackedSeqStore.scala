/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 *
 */

package com.ansvia.digaku.persistence


import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

import com.netflix.astyanax.connectionpool.NodeDiscoveryType
import com.netflix.astyanax.connectionpool.exceptions.NotFoundException
import com.netflix.astyanax.connectionpool.impl.ConnectionPoolType
import com.netflix.astyanax.model.{ColumnFamily, ConsistencyLevel}
import com.netflix.astyanax.util.RangeBuilder
import org.apache.commons.codec.digest.DigestUtils

import scala.collection.JavaConversions._
import scala.collection.mutable.ListBuffer
import scala.util.control.Breaks._

/**
 * Author: robin
 *
 */
class CassandraBackedSeqStore[B](val cf:ColumnFamily[java.lang.String, B],
                                      keyspace:String, clusterName:String,
                                      seeds:String, replicationStrategy:String,
                                      replicationStrategyOpts:String,
                                      discoveryType:NodeDiscoveryType=NodeDiscoveryType.RING_DESCRIBE,
                                      connectionPoolType:ConnectionPoolType=ConnectionPoolType.ROUND_ROBIN) {

    require(replicationStrategyOpts.contains(":"), "Invalid strategyOpts: `" + replicationStrategyOpts + "`")

    private var comparatorType = "org.apache.cassandra.db.marshal.BytesType"
    private var caching = "keys_only"

//    val cfLock = new ColumnFamily[String, String]("seq_store_lock_", StringSerializer.get(), StringSerializer.get())

    lazy val csh = {
        val _csh = new CassandraDriver[String, B](keyspace, cf,
            clusterName = clusterName, seeds = seeds, strategyClass = replicationStrategy,
            strategyOptsStr = replicationStrategyOpts,
            discoveryType = discoveryType,
            connectionPoolType = connectionPoolType)

        _csh.comparatorType = comparatorType
        _csh.caching = caching

        val ctx = _csh.cluster.getClient

        if (!_csh.columnFamilyExists(cf.getName)){

            val cfDef = ctx.makeColumnFamilyDefinition()
                .setName(cf.getName)
                .setKeyspace(keyspace)
                .setComparatorType(comparatorType)
                .setKeyValidationClass("org.apache.cassandra.db.marshal.UTF8Type")
                .setCaching(caching)
                .setCompressionOptions(Map("sstable_compression" -> "org.apache.cassandra.io.compress.SnappyCompressor",
                    "chunk_length_kb" -> "64"))
                .setCaching("keys_only")
                .setReadRepairChance(0.5)
                .setReplicateOnWrite(true)

            cfDef.setFields(Map("speculative_retry" -> "99percentile"))

            ctx.addColumnFamily(cfDef)

        }

//        if (!_csh.columnFamilyExists(cfLock.getName)){
//
//            val cfDef = ctx.makeColumnFamilyDefinition()
//                .setName(cfLock.getName)
//                .setKeyspace(keyspace)
//                .setKeyValidationClass("org.apache.cassandra.db.marshal.UTF8Type")
//                .setDefaultValidationClass("org.apache.cassandra.db.marshal.LongType")
//                .setComparatorType("org.apache.cassandra.db.marshal.UTF8Type")
//                .setCaching("NONE")
//                .setReadRepairChance(0.5)
//                .setReplicateOnWrite(true)
//
//            cfDef.setFields(Map("speculative_retry" -> "99percentile"))
//
//            ctx.addColumnFamily(cfDef)
//
//        }

        _csh
    }

    def setComparatorType(compType:String) = {
        comparatorType = compType
        this
    }

    def setCaching(caching:String) = {
        this.caching = caching
        this
    }


    def build(baseKey:String, idGenerator: () => B):SeqStore[B] = {
        new SeqStore[B](baseKey, this){
            def genId = idGenerator()
        }
    }

    def buildCrypted(baseKey:String, idGenerator: () => B):SeqStore[B] = {
        new SeqStore[B](baseKey, this){
            def genId = idGenerator()

            // @TODO(robin): make secret key configurable
            override protected val encoder: Encoder = new AESEncoder("who owns the youth, gains the future")
        }
    }
}


trait Encoder {
    def encode(data:Array[Byte]): Array[Byte] = {
        data
    }
    def decode(data:Array[Byte]): Array[Byte] = {
        data
    }
}


class AESEncoder(secret:String) extends Encoder {

    private val codecPackStr = "AES/ECB/PKCS5Padding"
    private val secretKey = new SecretKeySpec(DigestUtils.sha512Hex(secret).substring(0,16).getBytes, "AES")

    override def encode(data: Array[Byte]): Array[Byte] = {
        val encipher = Cipher.getInstance(codecPackStr)
        encipher.init(Cipher.ENCRYPT_MODE, secretKey)
        encipher.doFinal(data)
    }

    override def decode(data: Array[Byte]): Array[Byte] = {
        val encipher = Cipher.getInstance(codecPackStr)
        encipher.init(Cipher.DECRYPT_MODE, secretKey)
        encipher.doFinal(data)
    }
}

abstract class SeqStore[T](baseKey:String, backend:CassandraBackedSeqStore[T]){

    private lazy val csh = backend.csh

    protected val encoder:Encoder = new Encoder {}

    def genId:T

    def insert(value:Array[Byte]):T = {
        val id = genId
        csh.usingMutator { (cf, mb) =>
            mb.setConsistencyLevel(ConsistencyLevel.CL_QUORUM)
            mb.withRow(cf, baseKey).putColumn(id, encoder.encode(value))
        }
        id
    }

    def insert(value:String, ttl:Int):T = {
        val id = genId
        csh.usingMutator { (cf, mb) =>
            mb.setConsistencyLevel(ConsistencyLevel.CL_QUORUM)
            if (ttl > 0){
                mb.withRow(cf, baseKey).putColumn(id, encoder.encode(value.getBytes), ttl)
            }else{
                mb.withRow(cf, baseKey).putColumn(id, encoder.encode(value.getBytes))
            }
        }
        id
    }

    def insert(value:String):T = {
        insert(value, 0)
    }

    def insert(id:T, value:String, ttl:Int){
        csh.usingMutator { (cf, mb) =>
            mb.setConsistencyLevel(ConsistencyLevel.CL_QUORUM)
            if (ttl > 0){
                mb.withRow(cf, baseKey).putColumn(id, encoder.encode(value.getBytes), ttl)
            }else{
                mb.withRow(cf, baseKey).putColumn(id, encoder.encode(value.getBytes))
            }
        }
    }

    def insert(id:T, value:String){
        insert(id, value, 0)
    }

    def update(id:T, value: String){
//        csh.usingMutator { (cf, mb) =>
//            mb.withRow(cf, baseKey).putColumn(id, value)
//        }
        insert(id, value, 0) // sama saja
    }

    def get(id:T):Option[String] = {
        csh.usingQuery { q =>
            try {
                val rowQuery = q.setConsistencyLevel(ConsistencyLevel.CL_QUORUM)
                    .getKey(baseKey)

                val rv = rowQuery.getColumn(id).execute()

                val data = encoder.decode(rv.getResult.getByteArrayValue)

                Some(new String(data))
            }catch{
                case e:NotFoundException =>
                    None
            }
        }
    }


    /**
     * Get all key + value.
     * @return
     */
    def getStream(fromId:Option[java.lang.Long], toId:Option[java.lang.Long], limit:Int):Iterator[(T, String)] = {
        csh.usingQuery { q =>

            val rowQuery = q.setConsistencyLevel(ConsistencyLevel.CL_QUORUM)
                .getKey(baseKey)

            val rowQuery2 =
                if (fromId.isDefined && toId.isEmpty){
                    val range = new RangeBuilder()
                        .setStart(fromId.get)
//                        .setReversed(true) // tidak perlu set reversed = true lagi, karena schema comparator-nya sudah diset reversed
                        .setLimit(limit)
                        .build()

                    rowQuery.withColumnRange(range)

                }else if (fromId.isEmpty && toId.isDefined){

                    val range = new RangeBuilder()
                        .setEnd(toId.get)
//                        .setReversed(true) // tidak perlu set reversed = true lagi, karena schema comparator-nya sudah diset reversed
                        .setLimit(limit)
                        .build()

                    rowQuery.withColumnRange(range)
                }else if (fromId.isDefined && toId.isDefined){
                    val range = new RangeBuilder()
                        .setStart(fromId.get)
                        .setEnd(toId.get)
//                        .setReversed(true) // tidak perlu set reversed = true lagi, karena schema comparator-nya sudah diset reversed
                        .setLimit(limit)
                        .build()

                    rowQuery.withColumnRange(range)
                }else{
                    val range = new RangeBuilder()
//                        .setReversed(true) // tidak perlu set reversed = true lagi, karena schema comparator-nya sudah diset reversed
                        .setLimit(limit)
                        .build()
                    rowQuery.withColumnRange(range)
                }

            val columns = rowQuery2.execute().getResult

            var rv = new ListBuffer[(T, String)]
            for ( i <- 0 to columns.size()-1 ){
                val col = columns.getColumnByIndex(i)

                val data = encoder.decode(col.getByteArrayValue)

                rv += ((col.getName, new String(data)))

            }
            rv.toIterator
        }
    }

    /**
     * Get all key + value.
     * @return
     */
    def paging(fromId:Option[java.lang.Long], toId:Option[java.lang.Long])(func : (T, String) => Boolean) = {

        csh.usingQuery { q =>

            val rowQuery = q.setConsistencyLevel(ConsistencyLevel.CL_QUORUM)
                .getKey(baseKey)

            val rowQuery2 =
                if (fromId.isDefined && toId.isEmpty){
                    val range = new RangeBuilder()
                        .setStart(fromId.get)
//                        .setReversed(true) // tidak perlu set reversed = true lagi, karena schema comparator-nya sudah diset reversed
                        .setLimit(1000)
                        .build()

                    rowQuery.autoPaginate(true)
                        .withColumnRange(range)

                }else if (fromId.isEmpty && toId.isDefined){

                    val range = new RangeBuilder()
                        .setEnd(toId.get)
//                        .setReversed(true) // tidak perlu set reversed = true lagi, karena schema comparator-nya sudah diset reversed
                        .setLimit(1000)
                        .build()

                    rowQuery.autoPaginate(true).withColumnRange(range)
                }else if (fromId.isDefined && toId.isDefined){
                    val range = new RangeBuilder()
                        .setStart(fromId.get)
                        .setEnd(toId.get)
//                        .setReversed(true) // tidak perlu set reversed = true lagi, karena schema comparator-nya sudah diset reversed
                        .setLimit(1000)
                        .build()

                    rowQuery.autoPaginate(true).withColumnRange(range)
                }else{
                    val range = new RangeBuilder()
//                        .setReversed(true) // tidak perlu set reversed = true lagi, karena schema comparator-nya sudah diset reversed
                        .setLimit(1000)
                        .build()
                    rowQuery.autoPaginate(true).withColumnRange(range)
                }

            breakable {
                var columns = rowQuery2.execute().getResult

                while (!columns.isEmpty){
                    for (i <- 0 to columns.size() - 1) {
                        val col = columns.getColumnByIndex(i)

                        val data = encoder.decode(col.getByteArrayValue)

                        if (!func(col.getName, new String(data))) {
                            break()
                        }

                    }
                    columns = rowQuery2.execute().getResult
                }
            }
        }
    }

    /**
     * Get count all records inside.
     * @return
     */
    def getCount = {
        csh.usingQuery { q =>
            q.setConsistencyLevel(ConsistencyLevel.CL_QUORUM)
                .getKey(baseKey).getCount.execute().getResult
        }
    }


    /**
     * Delete record by key.
     * @param key object key to delete.
     */
    def delete(key:T){
        csh.usingMutator { (cf, m) =>
            m.setConsistencyLevel(ConsistencyLevel.CL_QUORUM)
            m.withRow(cf, baseKey).deleteColumn(key)
        }
    }


    def clear(){
        csh.usingMutator { (cf, mb) =>
            mb.setConsistencyLevel(ConsistencyLevel.CL_QUORUM)
            mb.withRow(cf, baseKey).delete()
        }
    }
}
