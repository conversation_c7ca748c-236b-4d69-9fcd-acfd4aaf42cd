/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.persistence

import com.tinkerpop.blueprints.util.wrappers.id.IdGraph.IdFactory
import java.util.Date

//import java.security.SecureRandom
import java.util.concurrent.atomic.AtomicInteger


/**
 * Author: robin
 *
 *
 */

trait IdFactoryComponent {

    val idFactory:IdFactory
}


/**
 * ID generator factory based on Twitter Snowflake Algorithm
 * see https://github.com/twitter/snowflake/
 */
object SnowFlakeIdFactory {
    val EPOCH = 1288834974657L

    case class SnowFlakeId(timestamp:Long, machineId:Int, inc:Int)

    def build(machineId:Int) = new SnowFlakeIdFactory(machineId)

    def parse(id:Long) = {
        val ts = (id >> 22) + EPOCH
        val machineId = (id >> 12) & (1024 - 1)
        val incr = id & (1024 - 1)
        SnowFlakeId(ts, machineId.toInt, incr.toInt)
    }
}

// sebenarnya ini sama dengan yang di digaku.idgen library, hanya saja
// ini dispesialisasikan dengan engine digaku yang menggunakan IdFactory
class SnowFlakeIdFactory(machineId:Int) extends IdFactory {
    assert(machineId < 1024, "Maximum machine id number is 1023")

    private val incr = new AtomicInteger(0)
    private var lastId:Long = 0L

    def createId(): AnyRef = synchronized {
        val ts = new Date().getTime - SnowFlakeIdFactory.EPOCH
        if (incr.get() >= (1024 - 2 /*just in case*/)){
            // reset incr
            incr.set(0)
        }
        lastId = ((ts << 22) | (machineId << 12) | incr.incrementAndGet())
        lastId.asInstanceOf[AnyRef]
    }

    override def toString: String = "SnowFlakeIdFactory(machine-id=%d)".format(machineId)

    def getLastGeneratedId = lastId
}
