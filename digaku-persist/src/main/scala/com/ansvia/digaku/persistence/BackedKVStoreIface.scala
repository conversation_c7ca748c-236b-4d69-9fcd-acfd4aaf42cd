/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.persistence

/**
 * Author: robin
 *
 */
trait BackedKVStoreIface {
    def set(key:String, value:AnyRef)
    def set(key:String, value:Int)
    def set(key:String, value:Long)
    def set(key:String, value:Double)
    def set(key:String, value:Boolean)
    def get(key:String, defVal:AnyRef):AnyRef
    def get(key:String, defVal:Int):Int
    def get(key:String, defVal:Long):Long
    def get(key:String, defVal:Double):Double
    def get(key:String, defVal:String):String
    def get(key:String, defVal:Boolean):Boolean
    def getAll:Iterator[(String, AnyRef)]

    /**
     * Get count all records inside.
     * @return
     */
    def getCount:Int
    def exists(key:String):<PERSON>olean
    def getOption[T <: AnyRef](key:String):Option[T]
    def delete(key:String)
}
