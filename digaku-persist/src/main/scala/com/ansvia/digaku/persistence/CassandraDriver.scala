/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.persistence

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.persistence.CassandraDriver.Context
import com.netflix.astyanax.AstyanaxContext.Builder
import com.netflix.astyanax._
import com.netflix.astyanax.connectionpool.exceptions.{BadRequestException, NotFoundException, ConnectionException}
import com.netflix.astyanax.connectionpool.impl.{ConnectionPoolConfigurationImpl, ConnectionPoolType, CountingConnectionPoolMonitor}
import com.netflix.astyanax.connectionpool.{Host, HostConnectionPool, NodeDiscoveryType}
import com.netflix.astyanax.impl.AstyanaxConfigurationImpl
import com.netflix.astyanax.model.{Column, ColumnFamily}
import com.netflix.astyanax.query.ColumnFamilyQuery
import com.netflix.astyanax.thrift.ThriftFamilyFactory

import scala.collection.JavaConversions._
import scala.collection.mutable

/**
 * Author: robin
 *
 */
class CassandraDriver[K, V](keyspaceName:String, columnFamily:ColumnFamily[K, V],
                      clusterName:String, seeds:String,
                      strategyClass:String, strategyOptsStr:String,
                      discoveryType:NodeDiscoveryType=NodeDiscoveryType.RING_DESCRIBE,
                      connectionPoolType:ConnectionPoolType=ConnectionPoolType.ROUND_ROBIN)
    extends Slf4jLogger {


    private val strategyOpts = strategyOptsStr.split(",").map(_.split(':')).map(a => (a(0), a(1))).toMap

    lazy val context = CassandraDriver.getContext(clusterName, keyspaceName,
            seeds, strategyClass, strategyOpts,
            discoveryType, connectionPoolType)

    lazy val cluster = context.cluster

    lazy val keyspace:Keyspace = context.keyspace.getClient


    def usingMutator(func: (ColumnFamily[K, V],MutationBatch) => Unit){
        ensureKeyspaceExists
        ensureColumnFamilyExists

        val m = keyspace.prepareMutationBatch()

        func.apply(columnFamily, m)

        try {
            m.execute()
        }catch{
            case e:ConnectionException =>
                error(e.getMessage)
                e.printStackTrace()
        }
    }

    def usingKeySpace(func: Keyspace => Unit){
        ensureKeyspaceExists
        ensureColumnFamilyExists
        func.apply(keyspace)
    }

    def usingQuery[T](func: ColumnFamilyQuery[K, V] => T):T = {
        ensureKeyspaceExists
        ensureColumnFamilyExists
        func.apply(keyspace.prepareQuery(columnFamily))
    }

    lazy val ensureKeyspaceExists = {
        CassandraDriver.ensureKeyspaceExists(cluster, keyspaceName, strategyClass, strategyOpts)
    }

    var comparatorType = "org.apache.cassandra.db.marshal.BytesType"
    var caching = "keys_only"

    lazy val ensureColumnFamilyExists = {
        val ctx = cluster.getClient

        val ksDef = ctx.describeKeyspace(keyspaceName)

        var found = false
        if (ksDef != null){
            for (cdef <- ksDef.getColumnFamilyList){
                found |= cdef.getName.equals(columnFamily.getName)
            }
        }

        if (!found){
            val cfDef = ctx.makeColumnFamilyDefinition()
                .setName(columnFamily.getName)
                .setKeyspace(keyspaceName)
                .setComparatorType(comparatorType)
                .setCaching(caching)
                .setCompressionOptions(Map("sstable_compression" -> "org.apache.cassandra.io.compress.SnappyCompressor",
                    "chunk_length_kb" -> "64"))
            ctx.addColumnFamily(cfDef)
        }

    }

    def columnFamilyExists(name:String) = {
        val ctx = cluster.getClient

        val ksDef = ctx.describeKeyspace(keyspaceName)

        var found = false
        if (ksDef != null){
            for (cdef <- ksDef.getColumnFamilyList){
                found |= cdef.getName.equals(name)
            }
        }

        found
    }

    lazy val driverContext = {
        Context(context.keyspace, cluster, strategyClass, strategyOpts)
    }
}


object CassandraDriver extends Slf4jLogger {

    case class Context(keyspace:AstyanaxContext[Keyspace],
                       cluster:AstyanaxContext[Cluster],
                       strategyClass:String, strategyOpts:Map[String,String]){
        private lazy val ensureStarted = {
            cluster.start()
            keyspace.start()
        }
        def start(){
            ensureStarted
        }
        def shutdown(){
            keyspace.shutdown()
            cluster.shutdown()
        }
        def ensureKeyspaceExists(keyspaceName:String){
            CassandraDriver.ensureKeyspaceExists(cluster, keyspaceName, strategyClass, strategyOpts)
        }
        def ensureColumnFamilyExists(name:String, keyspaceName:String,
                                     comparatorType:String="org.apache.cassandra.db.marshal.BytesType"){
            CassandraDriver.ensureColumnFamilyExists(name, cluster, keyspaceName, comparatorType)
        }

        def clearColumnFamily(cf:String){
            try {
                keyspace.getClient.truncateColumnFamily(cf)
            }catch{
                case e:BadRequestException if e.getMessage.contains("unconfigured") =>
                    // if column family not exists yet, just ignore it
            }
        }

    }

    private val contextCache = new mutable.HashMap[Int, Context]()
        with mutable.SynchronizedMap[Int, Context]
    private val clusterCache = new mutable.HashMap[Int, AstyanaxContext[Cluster]]()
        with mutable.SynchronizedMap[Int, AstyanaxContext[Cluster]]

    private lazy val connPoolMonitor = new CountingConnectionPoolMonitor(){
        override def onHostAdded(host: Host, pool: HostConnectionPool[_]){
//            super.onHostAdded(host, pool)
            discoService.addHost(host)
        }

        override def onHostRemoved(host: Host){
            discoService.removeHost(host)
        }
    }

    import com.google.common.base.Supplier
    import scala.collection.JavaConversions._

    private object discoService extends Supplier[java.util.List[Host]] with Slf4jLogger {

        private var availableHosts = List.empty[Host]

        def addHost(host:Host){
            synchronized {
                if (!availableHosts.contains(host)){
                    info("discoService: got up host " + host)
                    availableHosts :+= host
                }
            }
        }

        def removeHost(host:Host) = {
            synchronized {
                if (availableHosts.contains(host)){
                    info("discoService: host removed " + host)
                    availableHosts = availableHosts.filterNot(_ == host)
                }
            }
        }


        def get(): java.util.List[Host] = {
            availableHosts
        }
    }

    def createBuilder(clusterName:String, keyspaceName:String, seeds:String="127.0.0.1:9160",
                      discoveryType:NodeDiscoveryType=NodeDiscoveryType.RING_DESCRIBE,
                      connectionPoolType:ConnectionPoolType=ConnectionPoolType.ROUND_ROBIN): Builder = {
        val port = {
            val s = seeds.split(":")
            if (s.length > 1)
                s(1).toInt
            else
                9160
        }

//        println("    discoveryType: " + discoveryType)
//        println("    connectionPoolType: " + connectionPoolType)
//        Thread.dumpStack()

        new AstyanaxContext.Builder()
            .forCluster(clusterName)
            .forKeyspace(keyspaceName)
            .withAstyanaxConfiguration(new AstyanaxConfigurationImpl()
                .setDiscoveryType(discoveryType)
                .setConnectionPoolType(connectionPoolType)
            )
            .withConnectionPoolConfiguration(new ConnectionPoolConfigurationImpl("dgweb-conn-pool")
                .setPort(port)
                .setMaxConnsPerHost(3)
                .setMaxConns(100)
                .setSeeds(seeds)
            )
            .withConnectionPoolMonitor(connPoolMonitor)
            .withHostSupplier(discoService)

    }

    def getContext(clusterName:String, keyspaceName:String,
                   seeds:String, strategyClass:String, strategyOpts:String,
                   discoveryType:NodeDiscoveryType=NodeDiscoveryType.RING_DESCRIBE,
                   connectionPoolType:ConnectionPoolType=ConnectionPoolType.ROUND_ROBIN): Context = {
        require(strategyOpts.contains(":"), "Invalid strategyOpts: `" + strategyOpts + "`")

        val strategyOptsMap = strategyOpts.split(',').map(_.split(':')).map(a => (a(0), a(1))).toMap

        getContext(clusterName, keyspaceName, seeds, strategyClass, strategyOptsMap,
            discoveryType, connectionPoolType)
    }

    def getContext(clusterName:String, keyspaceName:String,
                   seeds:String, strategyClass:String, strategyOpts:Map[String, String],
                   discoveryType:NodeDiscoveryType,
                   connectionPoolType:ConnectionPoolType): Context = {

        val key = (clusterName + keyspaceName + seeds).hashCode

        val cb = createBuilder(clusterName, keyspaceName, seeds, discoveryType, connectionPoolType)

        val cluster = clusterCache.getOrElseUpdate((clusterName + seeds + discoveryType.toString + connectionPoolType.toString).hashCode, {
            val cluster = cb.buildCluster(ThriftFamilyFactory.getInstance())
            cluster.start()
            cluster
        })

        contextCache.getOrElseUpdate(key, {
            val ks = cb.buildKeyspace(ThriftFamilyFactory.getInstance())
            ks.start()
            ensureKeyspaceExists(cluster, keyspaceName, strategyClass, strategyOpts)
            Context(ks, cluster, strategyClass, strategyOpts)
        })
    }

    def refresh(seeds:String, clusterName:String, keyspaceName:String){
        clusterCache.remove((clusterName + seeds).hashCode)
        contextCache.remove((clusterName + keyspaceName + seeds).hashCode)
    }


    /**
     * Memastikan keyspace exists/ada di database.
     * @param cluster cluster yang digunakan.
     * @param keyspaceName nama keyspace.
     * @param strategyClass jenis strategy yang digunakan, misal SimpleStrategy
     *                      atau NetworkTopologyStrategy
     * @param strategyOpts strategy options berdasarkan :strategyClass yang digunakan.
     */
    def ensureKeyspaceExists(cluster:AstyanaxContext[Cluster],
                             keyspaceName:String, strategyClass:String, strategyOpts:Map[String,String]){

        // ensure keyspace exists
        val ctx = cluster.getClient

        var ksDef = ctx.describeKeyspace(keyspaceName)

        if (ksDef == null){
            warn("Keyspace " + keyspaceName + " didn't exists, creating first.")

            ksDef = ctx.makeKeyspaceDefinition()
                .setName(keyspaceName)
                .setStrategyClass(strategyClass)
                .setStrategyOptions(strategyOpts)
            ctx.addKeyspace(ksDef)
            info("keyspace created " + keyspaceName)
        }
    }


    def ensureColumnFamilyExists(name:String,cluster:AstyanaxContext[Cluster],
                                 keyspaceName:String,
                                 comparatorType:String="org.apache.cassandra.db.marshal.BytesType",
                                 compressionOpts:Map[String,String]=Map("sstable_compression" ->
                                     "org.apache.cassandra.io.compress.SnappyCompressor")){
        val ctx = cluster.getClient

        val ksDef = ctx.describeKeyspace(keyspaceName)

        var found = false
        if (ksDef != null){
            for (cdef <- ksDef.getColumnFamilyList){
                found |= cdef.getName.equals(name)
            }
        }

        if (!found){
            val cfDef = ctx.makeColumnFamilyDefinition()
                .setName(name)
                .setKeyspace(keyspaceName)
                .setComparatorType(comparatorType)
                .setCompressionOptions(compressionOpts)
            ctx.addColumnFamily(cfDef)
        }

    }
}


/**
 * Merupakan implicit helpers untuk mempermudah mengoperasikan
 * cassandra semudah set dan get saja, dimana parameter-parameter
 * laiinya sudah predefined di sini.
 *
 */
object NamespacedStringCassandraDriverHelpers extends Slf4jLogger {
    implicit class CassandraDriverWrapper(cassandraDriver: CassandraDriver[String, String]){

        /**
         * Set key baru ke db.
         * @param namespace ini digunakan sebagai row key.
         * @param k key.
         * @param v value.
         * @param ttl time to live.
         */
        def set(namespace:String, k:String, v:String, ttl:Int) = {
            cassandraDriver.usingMutator { case (cf, mb) =>
                    mb.withRow(cf, namespace).putColumn(k, v, ttl)
            }
        }

        def set(namespace:String, k:String, v:java.lang.Boolean, ttl:Int) = {
            cassandraDriver.usingMutator { case (cf, mb) =>
                    mb.withRow(cf, namespace).putColumn(k, v, ttl)
            }
        }

        def set(namespace:String, k:String, v:java.lang.Integer, ttl:Int) = {
            cassandraDriver.usingMutator { case (cf, mb) =>
                    mb.withRow(cf, namespace).putColumn(k, v, ttl)
            }
        }

        def set(namespace:String, k:String, v:java.lang.Long, ttl:Int) = {
            cassandraDriver.usingMutator { case (cf, mb) =>
                    mb.withRow(cf, namespace).putColumn(k, v, ttl)
            }
        }

        /**
         * Mendapatkan konten dari db berdasarkan key-nya.
         * @param namespace ini digunakan sebagai row key.
         * @param k key.
         * @return Option string.
         */
        def getString(namespace:String, k:String):Option[String] = {
            get(namespace, k).map(_.getStringValue)
        }

        def getBool(namespace:String, key:String) = {
            get(namespace, key).exists(_.getBooleanValue)
        }

        def getInt(namespace:String, key:String) = {
            get(namespace, key).map(_.getIntegerValue)
        }

        def getLong(namespace:String, key:String) = {
            get(namespace, key).map(_.getLongValue)
        }


        /**
         * Mendapatkan konten dari db berdasarkan key-nya.
         * @param namespace ini digunakan sebagai row key.
         * @param k key.
         * @return Option Column[String].
         */
        def get(namespace:String, k:String): Option[Column[String]] = {
            try {
                cassandraDriver.usingQuery { q =>
                    Some(q.getRow(namespace).getColumn(k).execute().getResult)
                }
            }catch{
                case e:NotFoundException => None
                case e:Exception =>
                    error(e.getMessage)
                    None
            }
        }

    }
}


