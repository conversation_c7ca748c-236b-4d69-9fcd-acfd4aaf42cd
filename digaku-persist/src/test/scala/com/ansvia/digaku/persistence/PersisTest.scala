/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 *
 */

package com.ansvia.digaku.persistence

import java.util.Random
import java.util.concurrent.atomic.AtomicLong

import com.ansvia.util.idgen.TokenIdGenerator
import com.netflix.astyanax.connectionpool.exceptions.PoolTimeoutException
import com.netflix.astyanax.model.ColumnFamily
import com.netflix.astyanax.serializers.{LongSerializer, StringSerializer}
import org.specs2.specification.{Fragments, Step}

/**
 * Author: robin (<EMAIL>)
 */

trait PersistTest {
    val csh = {

        // digunakan untuk test koneksi
        // kalo gagal maka skip aja
        try {
            val CF_STANDARD1 = new ColumnFamily[String, java.lang.Long](
                "Standard1", StringSerializer.get(), LongSerializer.get())

            new CassandraDriver("digaku_test", CF_STANDARD1, "digaku", "127.0.0.1:9160",
                "SimpleStrategy", "replication_factor:1")
        }catch{
            case e:PoolTimeoutException =>
                skipIt
                null
        }
    }

    def skipIt
}

trait RandomStringGeneratorTestHelper {
    private val atomicIncr = new AtomicLong(0)
    private val rnd = new Random()

    /**
     * helper for generating random number.
     * @param maxLength number max length.
     * @return
     */
    def genRandomNumber(maxLength:Int=5) = {
        System.currentTimeMillis().toString.grouped(maxLength).toSeq.reverse.head.toInt +
            rnd.nextLong() +
            atomicIncr.getAndIncrement
    }

    private object randomStringGenerator extends TokenIdGenerator

    /**
     * Helper untuk menggenerasikan random string.
     * @return
     */
    def genRandomString = {
        randomStringGenerator.nextId()
    }

}

trait PersisTestImmutable extends org.specs2.Specification with PersistTest with BeforeAllAfterAllImmutable
    with RandomStringGeneratorTestHelper {

    override protected def setUp(): Unit = ()

    override protected def beforeAll(): Unit = ()

    override protected def afterAll(): Unit = ()

    def skipIt{
        skipAll
    }
}
trait PersisTestMutable extends org.specs2.mutable.Specification with PersistTest with BeforeAllAfterAllMutable
    with RandomStringGeneratorTestHelper {

    override protected def setUp(): Unit = ()

    override protected def beforeAll(): Unit = ()

    override protected def afterAll(): Unit = ()


    def skipIt{
        skipAll
    }
}


trait BeforeAllAfterAll {
    protected def setUp()
    protected def beforeAll()
    protected def afterAll()
}

trait BeforeAllAfterAllImmutable extends org.specs2.Specification with BeforeAllAfterAll {
    // see http://bit.ly/11I9kFM (specs2 User Guide)
    override def map(fragments: =>Fragments) =
        Step(beforeAll) ^ fragments ^ Step(afterAll)

    protected def setUp()
    protected def beforeAll()
    protected def afterAll()
}

trait BeforeAllAfterAllMutable extends org.specs2.mutable.Specification with BeforeAllAfterAll {
    // see http://bit.ly/11I9kFM (specs2 User Guide)
    override def map(fragments: =>Fragments) =
        Step(beforeAll) ^ fragments ^ Step(afterAll)

    protected def setUp()
    protected def beforeAll()
    protected def afterAll()
}


