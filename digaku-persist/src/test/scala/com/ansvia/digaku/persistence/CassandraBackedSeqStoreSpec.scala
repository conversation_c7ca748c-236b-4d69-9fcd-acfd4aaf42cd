/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 *
 */

package com.ansvia.digaku.persistence

import java.util.concurrent.atomic.AtomicLong

import com.netflix.astyanax.model.ColumnFamily
import com.netflix.astyanax.serializers.{LongSerializer, ObjectSerializer, StringSerializer}
import org.specs2.mutable.Specification
import org.specs2.specification.Scope

/**
 * Author: robin (<EMAIL>)
 */

class CassandraBackedSeqStoreSpec extends Specification with PersisTestMutable {

    "Cassandra backed sequence store" should {
        "able to insert" in new Ctx {
            val id = seqStore.insert("hello #1")
            id must_== 1 // sudah pasti 1 ini
        }
        "able to stream" in new Ctx {
            seqStore.insert("hello #1")
            seqStore.insert("hello #2")

            val rv = seqStore.getStream(None, None, 10).toList

//            println("rv: " + rv)

            rv.map(_._2) must contain("hello #2", "hello #1").inOrder
        }
        "able to handle many records" in new Ctx {
            val ss = for (i <- 0 to 1000-1) yield "msg #" + i

            for (s <- ss){
                seqStore.insert(s)
            }

            seqStore.getCount must_== 1000
            seqStore.getStream(None, None, 5000).toList.map(_._2) must containAllOf(ss.toSeq)
        }
        "basic paging" in new CtxPaging {

            totalToInsert = 1000
            init()

            var count = 0
            seqStore.paging(None, None){ (id, content) =>
                count += 1
                true
            }

            count must_== 1000
        }
        "paging within range" in new CtxPaging {
            totalToInsert = 500
            init()

            // paging must stop within range
            var count = 0
            seqStore.paging(Some(500L), Some(400L)){ (id, content) =>
                count += 1
//                println("%s. %s %s".format(count, id, content))
                true
            }
            count must_== 101 // ini 101 karena inclusive dengan content id 400 bukan hanya sampai 401
        }
        "paging must stop when callback return false" in new CtxPaging {
            init()
            var count = 0
            seqStore.paging(None, None){ (id, content) =>
                count += 1
                count < 300
            }
            count must_== 300
        }
    }


    class Ctx extends Scope {
        val idgen = new AtomicLong(0)

        val CF = new ColumnFamily[String, java.lang.Long](
            "chat_message",
            StringSerializer.get(),
            LongSerializer.get(),
            ObjectSerializer.get())

        val backend = new CassandraBackedSeqStore[java.lang.Long](CF, "digaku_test",
            "digaku", "127.0.0.1:9160", "SimpleStrategy", "replication_factor:1")
//        val seqStore = new TimeUUIDSeqStore("a-key", backend)
        val seqStore = backend.build("seqstore-test-key-" + genRandomString, () => idgen.incrementAndGet())
    }

    class CtxPaging extends Ctx {
        var totalToInsert = 1000
        def init(): Unit ={
            val ss = for (i <- 1 to totalToInsert) yield "msg #" + i

            for (s <- ss){
                seqStore.insert(s)
            }

            seqStore.getCount must_== totalToInsert
        }
    }
}
