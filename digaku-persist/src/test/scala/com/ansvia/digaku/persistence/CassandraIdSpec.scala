///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.persistence
//
//import com.netflix.astyanax.model.ColumnFamily
//import org.specs2.mutable.Specification
//import com.netflix.astyanax.connectionpool.exceptions.PoolTimeoutException
//import com.netflix.astyanax.serializers.{LongSerializer, StringSerializer, ComparatorType}
//import scala.collection.JavaConversions._
//
///**
// * Author: robin
// *
// */
//class CassandraIdSpec extends Specification {
//
//    private val csh = {
//        try {
//            val CF = new ColumnFamily[String, java.lang.Long](
//                "Standard1", StringSerializer.get(), LongSerializer.get())
//
//            new CassandraDriver("digaku_test", CF, "digaku", "127.0.0.1:9160",
//                "SimpleStrategy", "replication_factor:1")
//        }catch{
//            case e:PoolTimeoutException =>
//                skipAll
//                null
//        }
//    }
//
////    private lazy val idFactory:CassandraBackedIdFactory = new CassandraBackedIdFactory(2L, csh.context)
//
////    csh.keyspace.dropKeyspace()
//    csh.ensureKeyspaceExists
//
//    if (csh.columnFamilyExists("idgen_lock"))
//        csh.keyspace.dropColumnFamily("idgen_lock")
//
//    private def ensureColumnFamilyExists(cf: String, ks: String){
//        val ctx = csh.cluster.getClient
//
//        val ksDef = ctx.describeKeyspace(ks)
//
//        val found = ksDef.getColumnFamilyList.exists(_.getName == cf)
//
//        if (!found){
//            val cfDef = ctx.makeColumnFamilyDefinition()
//                .setName(cf)
//                .setKeyspace(ks)
//                .setGcGraceSeconds(0)
//                .setComparatorType(ComparatorType.BYTESTYPE.getClassName)
//                .setKeyValidationClass(ComparatorType.UTF8TYPE.getClassName)
//                .setDefaultValidationClass(ComparatorType.COUNTERTYPE.getClassName)
//                .setCompressionOptions(Map("sstable_compression" -> ""))
//            ctx.addColumnFamily(cfDef)
//        }
//    }
//
//    ensureColumnFamilyExists("idgen_lock", csh.keyspace.getKeyspaceName)
//
//    val csid = CassandraId.stripedGenerator(csh.context.keyspace, "idgen_lock", "idgen", 1, 2, 10, 2)
//
//    sequential
//
//    "Cassandra id factory" should {
//
//        "bisa generate id" in {
//            csid.setNextBatch(10L)
//            val id = csid.generate()
//            println("id: %s".format(id))
//            id must not equalTo(0)
//        }
//        "get current next batch" in {
//            val currentNextBatch = csid.getCurrentBatch
//            for(i <- 1 to (10/2)){ // dibagi 2 karena incremental-nya 2
//                val xid = csid.generate()
//                println("id -> " + xid)
//            }
//            println("-------------")
//            val currentNextBatch2 = csid.getCurrentBatch
//            for(i <- 1 to (10/2)){ // dibagi 2 karena incremental-nya 2
//                val xid = csid.generate()
//                println("id -> " + xid)
//            }
//            val currentNextBatch3 = csid.getCurrentBatch
//            println("currentNextBatch: " + currentNextBatch)
//            println("currentNextBatch2: " + currentNextBatch2)
//            println("currentNextBatch3: " + currentNextBatch3)
//
//            (currentNextBatch must beEqualTo(10L)) //and
//                (currentNextBatch2 must beEqualTo(20L)) //and
//                (currentNextBatch3 must beEqualTo(30L))
//        }
//        "set next batch" in {
//            csid.setNextBatch(42L)
//            csid.getCurrentBatch must beEqualTo(42L)
//        }
//    }
//}
