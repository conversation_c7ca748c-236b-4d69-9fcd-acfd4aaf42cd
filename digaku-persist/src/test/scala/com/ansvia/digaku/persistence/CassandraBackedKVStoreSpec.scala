/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.persistence


/**
 * Author: robin
 *
 */
class CassandraBackedKVStoreSpec extends PersisTestImmutable {


    def is = "Cassandra backed key value store should" ^
        sequential ^
//        Step {setup()} ^
        "can set" ! trees.canSet ^
        "can get" ! trees.canGet ^
        "can using integer" ! trees.canUsingInt ^
        "can getAll" ! trees.canGetAll ^
        "can remove" ! trees.rem ^
    end

    object trees {


        object kvsFactory extends CassandraBackedKVStoreFactory("Standard1", "digaku_test",
            "digaku", "127.0.0.1:9160", "SimpleStrategy", "replication_factor:1")

        val kvs = kvsFactory. build("config")


        def canSet = {
            kvs.set("key1","val1").must(not(throwA[Exception]()))
        }

        def canGet = {
            kvs.get("key1", "xxx") must_== "val1"
        }

        def canUsingInt = {
            kvs.set("key2", 2)
            kvs.get("key2", 0) must_== 2
        }


        def canGetAll = {
            kvs.getAll.map { case (k, v) =>
                println("%s -> %s (%s)".format(k, v, v.getClass.getSimpleName))
                v
            }.toList must be contain("val1", Int.box(2)) only
        }

        def rem = {
            kvs.delete("key1")
            kvs.get("key1", "xxx") must_== "xxx"
        }

    }

}
