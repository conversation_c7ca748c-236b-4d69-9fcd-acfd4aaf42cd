/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

//package com.ansvia.digaku.persistence
//
//import org.specs2.mutable.Specification
//
///**
// * Author: robin
// * Date: 11/26/13
// * Time: 7:42 PM
// *
// */
//class SimpleFlakeIdFactorySpec extends Specification {
//
//    "SimpleFlake id factory" should {
//        "generate unique id in K-ordered manner" in {
//            val idFactory = new SimpleFlakeIdFactory(2)
//            var idsb = Seq.newBuilder[(Int, Long)]
//
//            for (i <- 1 to 1000) {
//                idsb.+=((i, idFactory.createId().asInstanceOf[Long]))
//            }
////            val ids = idsb.result().sortBy(_._2)
//            val ids = idsb.result()
//            ids.slice(0, 10).foreach(println)
//
//            ids.map(_._2).distinct.length must beEqualTo(1000)
//
//            for (i <- 2 to 1000){
//                ids(i-1)._1 must beGreaterThan(ids(i-2)._1)
//            }
//
//        }
//    }
//
//}
