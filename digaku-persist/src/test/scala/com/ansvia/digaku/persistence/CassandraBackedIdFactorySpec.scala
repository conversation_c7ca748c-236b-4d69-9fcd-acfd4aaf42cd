///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.persistence
//
//import com.netflix.astyanax.model.ColumnFamily
//import com.netflix.astyanax.serializers.{StringSerializer, LongSerializer}
//import org.specs2.Specification
//import com.netflix.astyanax.connectionpool.exceptions.PoolTimeoutException
//import com.tinkerpop.blueprints.util.wrappers.id.IdGraph
//import org.apache.commons.configuration.BaseConfiguration
//import com.thinkaurelius.titan.core.TitanFactory
//import java.io.File
//import org.apache.cassandra.io.util.FileUtils
//
//
///**
// * Author: robin
// *
// */
//class CassandraBackedIdFactorySpec extends Specification {
//
//    private val csh = {
//        try {
//            val CF_STANDARD1 = new ColumnFamily[String, java.lang.Long](
//                    "Standard1", StringSerializer.get(), LongSerializer.get())
//
//            new CassandraDriver("digaku_test", CF_STANDARD1, "digaku", "127.0.0.1:9160",
//                "SimpleStrategy", "replication_factor:1")
//        }catch{
//            case e:PoolTimeoutException =>
//                skipAll
//                null
//        }
//    }
//
//    //    private lazy val idFactory:CassandraBackedIdFactory = new CassandraBackedIdFactory(2L, csh.context)
//
//    //    csh.keyspace.dropKeyspace()
//    csh.ensureKeyspaceExists
//
//    if (csh.columnFamilyExists("Standard1"))
//        csh.keyspace.dropColumnFamily("Standard1")
//
//    csh.ensureColumnFamilyExists
//    val csid = CassandraId.stripedGenerator(csh.context.keyspace, "Standard1", "idgen", 1, 2, 10, 2)
//
//    def is = "CassandraBackedIdFactory should" ^
//        sequential ^
//        p ^
//        "create id" ! trees.createId ^
//        "no collision" ! trees.noCollision ^
//        "add many vertex without collision" ! trees.addVertexNoCol ^
//        end
//
//    lazy val (cbif, db) = {
//        val cbif = new CassandraBackedIdFactory(1, csh.context)
//
//        val dbDir = "/tmp/digaku-persist-test-db"
//
//        val fDir = new File(dbDir)
//        if (fDir.exists())
//            FileUtils.deleteRecursive(fDir)
//
//        val conf = new BaseConfiguration()
//        conf.setProperty("storage.backend", "berkeleyje")
//        conf.setProperty("storage.directory", dbDir)
//        conf.setProperty("storage.transactions", false)
//        val idGraph = new IdGraph(TitanFactory.open(conf), true, false)
//        idGraph.setVertexIdFactory(cbif)
//        cbif.db = idGraph
//        (cbif, idGraph)
//    }
//
//
//    object trees {
//
//
//        def createId = {
//            cbif.createId() must not equalTo(null)
//        }
//
//        def noCollision = {
//            val ids = for(i <- (1 to 100)) yield cbif.createId()
//            println("no collision, real count: " + ids.toList.length)
//            ids.toList.distinct.length must beEqualTo(100)
//        }
//
//        def addVertexNoCol = {
//            val vx = for(i <- (1 to 1000)) yield {
//                db.addVertex(null)
//            }
//            vx.toList.map(_.getId).distinct.length must beEqualTo(1000)
//        }
//    }
//}
