/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.persistence

import org.specs2.mutable.Specification
import java.util.Date
import java.text.SimpleDateFormat

// scalastyle:off magic.number

/**
 * Author: robin
 *
 */
class SnowFlakeIdFactorySpec extends Specification {

    "SnowFlake id factory" should {
        "generate positive number" in {
            val idFactory = SnowFlakeIdFactory.build(123)
            idFactory.createId().asInstanceOf[Long] must beGreaterThan(0L)
        }
        "generate unique id in K-ordered manner" in {
            val idFactory = SnowFlakeIdFactory.build(123)
            var idsb = Seq.newBuilder[(Int, Long)]

            for (i <- 1 to 5000) {
                idsb.+=((i, idFactory.createId().asInstanceOf[Long]))
                Thread.sleep(1) // give generator a chance
            }
            val ids = idsb.result().sortBy(_._2)
            ids.slice(0, 10).foreach(println)

            for (i <- 1 to 5000){
//                println("%s should be %s".format(ids(i-1), i))
                ids(i-1)._1 must_== i
            }

            ids.distinct.length must_== 5000

        }
        "can extract data info from id" in {

//            val cal = Calendar.getInstance()
            val sf = new SimpleDateFormat("dd/MM/yyyy hh:mm")

            val idf = SnowFlakeIdFactory.build(123)
            val id = idf.createId()

            val rv = SnowFlakeIdFactory.parse(id.asInstanceOf[Long])

            val d1 = new Date(rv.timestamp)
            val d2 = new Date()
            val d2f = sf.format(d2)

//            println("d2f: " + d2f + " == " + sf.format(d1))

            sf.format(d1) must_== d2f
            rv.machineId must_== 123
            rv.inc must_== 1

        }
        "generated id should be in 18 digit" in {
            val idFactory = SnowFlakeIdFactory.build(200)

            val id = idFactory.createId()

            id.toString.length must_== 18
        }
    }

}
