/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.mailhandler

import org.specs2.mutable.Specification

/**
 * Author: robin
 * Date: 5/14/14
 * Time: 2:28 PM
 *
 */
class EmailMessageContentNormalizerSpec extends Specification {

    val regsPlain = Seq(
        ("""
           |hello ini email message
           |
           |> ini quote-nya
           |> quote
         """.stripMargin.trim, """hello ini email message""".trim),
        ("""
           |hello ini email message
           |
           |>
           |> ini quote-nya
           |> quote
         """.stripMargin.trim, """hello ini email message""".trim),
        ("""
           |hello ini email message
           |
           |augustus
           |>
           |> ini quote-nya
           |> quote
         """.stripMargin.trim, """hello ini email message
                                          |
                                          |augustus""".stripMargin.trim)
    )
    val regsHtml = Seq(
        ("""
           |hello ini email message
           |<br><br>
         """.stripMargin.trim, """hello ini email message""".stripMargin.trim)
    )

    "EmailMessageContentNormalizer" should {
        regsPlain.zipWithIndex.foreach { case ((input, expected), idx) =>
            "normalize simple text #%d".format(idx + 1) in {
                EmailMessageContentNormalizer.normalize(input).trim must beEqualTo(expected)
            }
        }
        regsHtml.zipWithIndex.foreach { case ((input, expected), idx) =>
            "normalize html text #%d".format(idx + 1) in {
                EmailMessageContentNormalizer.normalizeHtml(input).trim must beEqualTo(expected)
            }
        }
    }

}
