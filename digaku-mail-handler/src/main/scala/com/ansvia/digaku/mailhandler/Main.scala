/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.mailhandler

import java.io.FileNotFoundException

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.exc.NotExistsException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.lib.WebEngineWithConfig
import com.ansvia.digaku.model.builder.OriginBuilder
import com.ansvia.digaku.notifications.{PersistentNotification, NotificationSender}
import com.ansvia.digaku.web.notification.{NotificationWebHandlerBase, SendToEmail}
import com.ansvia.digaku.web.{DigakuWebCore, MailSettings, OstrichReporter, WebConfig}
import com.ansvia.digaku.{Digaku, model}
import net.liftweb.http.LiftRules
import org.streum.configrity.Configuration


object Main extends DbAccess with Slf4jLogger {

    val VERSION = "0.2.0" // autogenerated, don't edit this by hand

    def main(args: Array[String]) {

        val configFile = if (args.length > 0){
            args(0)
        }else{
            throw NotExistsException("Usage: mailhandler [DIGAKU-CONFIG-FILE] [SMTP-PORT]")
        }

        val port = if (args.length > 1)
            args(1).toInt
        else
            25

        setupDigakuEngine(configFile)

        // get email app if not exists create it!
        val admin = model.User.getByName("admin").getOrElse {
            throw NotExistsException("No admin user, is not installed yet?")
        }
        val emailApp = {
            val emailAppId = Digaku.systemConfig.get("email-app-id", 0L)
            if (emailAppId != 0L){
                model.App.getById(emailAppId).getOrElse {
                    throw NotExistsException("No email app with id " + emailAppId +
                        ", app id is retrieved from configurator `email-app-id`, please fix it if " +
                        "id referred to non existent app")
                }
            }else{
                // apabila belum ada buat dulu.
                info("email app not exists, creating first...")
                val _emailApp = model.App.create("Email", "Email App", "0.1", WebConfig.BASE_URL, admin, internal = true)
                _emailApp.pictureSmall = "http://dmcd6hvaqrxz0.cloudfront.net/img/apps/8522eb9af9d9283b02c13eddbe8931e9.png"
                _emailApp.pictureMedium = "http://dmcd6hvaqrxz0.cloudfront.net/img/apps/efcd726daa16c5036ad1261ddebf93df.png"
                _emailApp.pictureLarge = "http://dmcd6hvaqrxz0.cloudfront.net/img/apps/77667015629c17c1bfe127113b0d2925.png"
                _emailApp.save()
                db.commit()
                Digaku.systemConfig.set("email-app-id", _emailApp.getId)
                _emailApp
            }
        }


        println("using email app: " + emailApp)

        processor.emailApp = emailApp

        val smtp = SMTPServer(port, "Digaku Engine v2 " + VERSION)
        smtp.start()
    }



    /**
     * setup database.
     */
    private def setupDigakuEngine(configFile:String){

        debug("loading configuration...")

        val conf = Configuration.load(configFile)

        try {
            info("Using config file: " + configFile)

            WebConfig.conf = conf
            MailSettings.conf = conf

        }catch{
            case e:FileNotFoundException =>
                error(e.getMessage)
                e.printStackTrace()
                System.exit(4)
        }

//
////        val CLIENT_ID = conf("client_id", 1L)
//        val MACHINE_ID = conf[Int]("database.machine-id", 0)
//        val CASSANDRA_HOST = conf[String]("database.cassandra.host", "unset")
//        val CASSANDRA_KEYSPACE = conf[String]("database.cassandra.keyspace", "titan")
//        val CASSANDRA_REPLICATION_STRATEGY = conf[String]("database.cassandra.replication-strategy-class", "SimpleStrategy")
//        val CASSANDRA_REPLICATION_STRATEGY_OPTS = conf[String]("database.cassandra.replication-strategy-options", "replication_factor:1")
////        val CASSANDRA_CLUSTER_NAME = conf[String]("database.cassandra.cluster-name", "digaku")
////        val CASSANDRA_CONTEXT_KEYSPACE = conf[String]("database.cassandra.context-keyspace", "digaku_ctx")
//        val CASSANDRA_WRITE_CONSISTENCY_LEVEL = conf[String]("database.cassandra.write-consistency-level", "QUORUM")
//        val CASSANDRA_READ_CONSISTENCY_LEVEL = conf[String]("database.cassandra.read-consistency-level", "QUORUM")
//
//
//        debug("setup digaku engine...")
//
//        info("using cassandra: " + CASSANDRA_HOST + ", repl strategy: " + CASSANDRA_REPLICATION_STRATEGY +
//            ", repl strategy opts: " + CASSANDRA_REPLICATION_STRATEGY_OPTS +
//            ", keyspace: " + CASSANDRA_KEYSPACE +
//            ", machine-id: " + MACHINE_ID +
//            ", write-consistency-level: " + CASSANDRA_WRITE_CONSISTENCY_LEVEL +
//            ", read-consistency-level: " + CASSANDRA_READ_CONSISTENCY_LEVEL)
//
//
//        /**
//         * Set db config using TitanConfig.
//         */
////        lazy val cassandraContext = {
////            CassandraDriver.getContext(CASSANDRA_CLUSTER_NAME,
////                CASSANDRA_CONTEXT_KEYSPACE, CASSANDRA_HOST,
////                CASSANDRA_REPLICATION_STRATEGY,
////                CASSANDRA_REPLICATION_STRATEGY_OPTS)
////        }
////        val idFactory = new CassandraBackedIdFactory(CLIENT_ID, cassandraContext,
////            collisionDetector = true)
//
//        val idFactory = new SnowFlakeIdFactory(MACHINE_ID)
//
//
//        val dbConf = WebConfig.getPreconfiguredDBConfig(conf)
//        CoreConfig.dbConf = dbConf
//        CoreConfig.idFactory = idFactory
//
//        /**
//         * Setup search engine provider.
//         */
//        CoreConfig.searchEngineProvider = Digaku.systemConfig.get("search.engine", "embedded-elastic-search")
//        CoreConfig.esIndexDir = Digaku.systemConfig.get("search.index-dir", "/tmp/digaku-index")
//        CoreConfig.withAnalEventStreamListener = WebConfig.conf("engine.anal-event-stream-listener", true)
//        CoreConfig.useBlockingEventStream = WebConfig.conf("engine.use-blocking-event-stream", false)
//        CoreConfig.useBlockingEventStreamListener = WebConfig.conf("engine.use-blocking-event-stream-listener", false)
//        CoreConfig.useBlockingAnalEventStreamListener = WebConfig.conf("engine.use-blocking-anal-event-stream-listener", false)
//        CoreConfig.useBlockingNotifierSender = WebConfig.conf("engine.use-blocking-notifier-sender", false)
//        CoreConfig.counterProvider = CassandraCounterProvider.setup(WebConfig.CASSANDRA_HOST,
//            WebConfig.CASSANDRA_CLUSTER_NAME,
//            WebConfig.CASSANDRA_KEYSPACE, WebConfig.CASSANDRA_REPLICATION_STRATEGY,
//            WebConfig.CASSANDRA_REPLICATION_STRATEGY_OPTS)
//
//        //        CoreConfig.idFactory = new CassandraBackedIdFactory(WebConfig.CLIENT_ID, WebConfig.CASSANDRA_CONTEXT)
//        //        WebConfig.SEARCH_INDEX_DIR = CoreConfig.esIndexDir
//
//        debug("using search engine provider: " + CoreConfig.searchEngineProvider)
//        debug("using search index dir: " + CoreConfig.esIndexDir)
//        debug("using blocking AESL: " + CoreConfig.useBlockingAnalEventStreamListener)


        Digaku.engine = new WebEngineWithConfig(conf){
            override val notifGateway: EWCNotifGateway = null
        }

        /**
         * Dispatch Digaku engine initialization
         * procedure.
         */
        Digaku.init()

        Digaku.engine.print()

        /**
         * Ensure indices.
         */
        try {
            Digaku.engine.database.index()
        }
        catch {
            case e:Throwable =>
        }


//
//        if (WebConfig.IS_SUPPORT_ANDROID_PUSH_NOTIF) {
//            val androidGCMHandler = AndroidGCMPushNotificationSendHandler(WebConfig.GCM_API_KEY)
//            val androidC2DMHandler = AndroidC2DMPushNotificationSendHandler(WebConfig.C2DM_APP_EMAIL,
//                WebConfig.C2DM_APP_EMAIL_PASSWORD, WebConfig.C2DM_APP_SOURCE)
//
////            PersistentNotificationHandler.extendedHandlers :+= androidGCMHandler
////            PersistentNotificationHandler.extendedHandlers :+= androidC2DMHandler
//
//            NotificationSender.addHandlers(androidGCMHandler, androidC2DMHandler)
//
//        }
//
//        if(WebConfig.IS_SUPPORT_APPLE_PUSH_NOTIF) {
//            val apnsHandler = ApplePushNotificationServiceHandler(WebConfig.APPLE_CERT_FILE, WebConfig.APPLE_CERT_PASSWORD)
////            PersistentNotificationHandler.extendedHandlers :+= apnsHandler
//
//            NotificationSender.addHandlers(apnsHandler)
//        }
//
//        if (WebConfig.IS_SUPPORT_BB_PUSH_NOTIF) {
//            val blackberryPushHandler = BlackberryPushNotificationSendHandler(WebConfig.BB_APP_ID, WebConfig.BB_APP_PASSWORD, WebConfig.BB_PUSH_URL)
////            PersistentNotificationHandler.extendedHandlers :+= blackberryPushHandler
//
//            NotificationSender.addHandlers(blackberryPushHandler)
//        }
//
//        if(WebConfig.IS_SUPPORT_MICROSOFT_PUSH_NOTIF) {
//            val mpnsHandler = MicrosoftPushNotificationServiceHandler()
////            PersistentNotificationHandler.extendedHandlers :+= mpnsHandler
//
//            NotificationSender.addHandlers(mpnsHandler)
//
//        }

        //        EditArticleNotification.extendedReceivers = { ean:EditArticleNotification =>
        //            import com.ansvia.digaku.model
        //            ean.article.map(_.userCanEditList).getOrElse(Seq.empty[model.User])
        //        }

        //        object afterSendCalled extends RequestMemoize[String,Boolean]
        //        val throttler = RateLimiter.create(1.0, 5, TimeUnit.SECONDS)

        OriginBuilder.CHANNEL_URL_PREFIX = WebConfig.CHANNEL_URL_PREFIX
        OriginBuilder.USER_URL_PREFIX = WebConfig.USER_URL_PREFIX


        // inisialisasi digaku-web-core module
        DigakuWebCore.init()

//        setupNotificationGateway()

        // extend persistent notification handler
        // dengan notification web handler
        // agar dapat menghandle event untuk notifikasi, jadi nanti
        // kalau ada user yang balas lewat email akan di-dispatch ke notification gateway
        // via NotificationWebHandler ini
//        PersistentNotificationHandler.extendedHandlers :+= new NotificationWebHandlerBase(Digaku.engine.asInstanceOf[WebEngineWithConfig].notifGateway){
//            protected def sendEmail(ntf: NotificationBase){
//                SendToEmail.send(ntf)
//            }
//        }

        object notifToEmailHandler extends NotificationWebHandlerBase(Digaku.engine.asInstanceOf[WebEngineWithConfig].notifGateway){
            protected def sendEmail(ntf: PersistentNotification){
                SendToEmail.send(ntf)
            }
        }

        NotificationSender.addHandlers(notifToEmailHandler)

        LiftRules.unloadHooks.append { ()=>
            shutdown()
        }

        debug("setup digaku engine done.")
    }

//    def setupNotificationGateway(){
//        info("setup notification gateway")
//        val DIGAKUWEB_NSQ_TOPIC = List("send-for", "send-invite-for", "message-for")
//        val gtw = NsqNotificationGatewayV2(WebConfig.NSQ_PUBLISHER_HOST,DIGAKUWEB_NSQ_TOPIC)
//        info("using " + gtw)
////        NotificationGateway.setGateway(gtw)
//    }

    def shutdown() {
        debug("Unload all...")
        //        PluginManager.close()
        //        invalidNotifSweeper.shutdown()
        Digaku.shutdown()
        //        EventReminderLive.stop()
        //        AutoUserReleaseLive.stop()

        OstrichReporter.cassandraCollections.foreach(_._2.shutdown())
        //        SessionMaster.sessionCheckFuncs = SessionMaster.sessionCheckFuncs.filterNot(_ == SessionChecker)
        //        SessionMaster.sessionWatchers = SessionMaster.sessionWatchers.filterNot(_ == SessionInfoDumper)
    }
}
