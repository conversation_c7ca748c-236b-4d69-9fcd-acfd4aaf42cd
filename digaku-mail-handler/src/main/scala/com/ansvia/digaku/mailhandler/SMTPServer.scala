/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.mailhandler

import java.io.InputStream
import com.ansvia.commons.logging.Slf4jLogger
import org.subethamail.smtp.{MessageHandler, MessageContext, MessageHandlerFactory}
import javax.mail.{Part, Session}
import java.util.Properties
import javax.mail.internet.{InternetAddress, MimeMultipart, MimeMessage}
import com.ansvia.digaku.notifications.EmailSession
import com.tinkerpop.blueprints.Vertex
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.digaku.model._
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.Types.GraphType
import com.ansvia.digaku.notifications.impl.{ResponseNotification, SummonNotification}
import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException, IgnoredException}
import com.ansvia.digaku.web.{MailSettings, WebConfig}
import java.util.regex.Pattern
import org.jsoup.Jsoup
import com.ansvia.digaku.validator.EmailValidator
import com.ansvia.digaku.utils.MailSender

import com.ansvia.digaku.utils.ForumSettings._

/**
 * Author: robin
 *
 * Main smtp server that handles all incoming message from outside.
 */
case class SMTPServer(port:Int=25, softwareName:String) {

    private lazy val smtpInternal = {
        val rv = new org.subethamail.smtp.server.SMTPServer(new DigakuMailHandlerFactory())
        rv.setSoftwareName(softwareName)
        rv.setPort(port)
        rv
    }

    def getSmtpInternal = smtpInternal

    def start(){
        smtpInternal.start()
    }

}

class DigakuMailHandlerFactory() extends MessageHandlerFactory with Slf4jLogger with DbAccess {

    import processor._

    private val mailTargetRe = "^(([a-zA-Z0-9\\s]*)?\\<.*?@%s\\>|.*?@%s)$".format(Pattern.quote(WebConfig.NOTIF_EMAIL_DOMAIN),
        Pattern.quote(WebConfig.NOTIF_EMAIL_DOMAIN)).r

    private val emailSender = new MailSender(MailSettings.SMTP_HOST,MailSettings.SMTP_PORT)

    def create(ctx: MessageContext) = new MessageHandler {
        def from(from: String) {
            debug("got email from: " + from)
        }

        def recipient(recipient: String) {
            debug("got recipient: " + recipient)
        }


        /**
         * digunakan untuk mengirimkan informasi error ke user yang coba reply tapi terjadi kegagalan.
         * @param errorStr error information.
         * @param targetEmail target user email address.
         */
        def sendErrorMessage(errorStr: String, targetEmail:String){
            if (targetEmail.trim.length > 0){
                if (EmailValidator.isValid(targetEmail, noMxLookup = false)){
                    emailSender.sendByAddress("<EMAIL>",
                        "Cannot process email reply", errorStr, "text/plain", targetEmail)
                }
            }
        }

        def data(data: InputStream) {

            var senderEmail = ""

            // <<EMAIL>>
            try {

                val s = Session.getDefaultInstance(new Properties())
                val message = new MimeMessage(s, data)


                // validasi message data
                if (message.getFrom == null)
                    throw InvalidParameterException("No sender data, got null")

                val sender = message.getFrom.head.toString
                val target = message.getAllRecipients.head
                val subject = message.getSubject
                val contentType = message.getContentType

                println("sender: " + sender)
                println("target: " + target.toString)
                println("subject: " + subject)
                println("content-type: " + contentType)


                // validasi email target
                if (!mailTargetRe.pattern.matcher(target.toString).matches()) {
                    throw InvalidParameterException("Invalid email target " + target)
                }

//                // get email session from target
//                val sl = target.toString.split("\\+")
//
//                val emailSessionIdO =
//                    if (sl.length > 1) {
//                        val sl2 = sl(1).split("@")
//                        Some(sl2(0))
//                    } else
//                        None

                val sid = getEmailSession(message).getOrElse {
                    throw InvalidParameterException("no session")
                }

                debug("processing email sid: " + sid)

                senderEmail = message.getFrom.head match {
                    case ia:InternetAddress =>
                        ia.getAddress
                    case x =>
                        x.toString
                }

                EmailSession.get(sid).map { ntfV =>
                    val cls = ntfV.getOrElse("_class_", "")

                    debug("targetEmail: " + senderEmail)
                    User.getByEmailLogin(senderEmail) foreach { uSender =>
                        try {
                            processEmailSession(PipeMailData(cls, message, ntfV, uSender))

                            // uncomment 5 lines berikut apabila session ingin sekali pakai
                            // artinya reply kedua ke email ini tidak akan berhasil
                            //                                            // hapus email session
                            //                                            db.commit()
                            //                                            val _v = db.getVertex(ntfV.getId)
                            //                                            _v.removeProperty[String]("email.session.v2")
                            //                                            db.commit()
                        }
                        catch {
                            case e: IgnoredException =>
                                warn(e.getMessage)
                        }
                    }
                }.getOrElse {
                    warn("unknown sid: " + sid)
                }

            }
            catch {
                case e:IgnoredException =>
                    sendErrorMessage(e.getMessage, senderEmail)
                case e:InvalidParameterException =>
//                    warn(e.getMessage)
//                    e.printStackTrace()
                case e:DigakuException =>
                    warn(e.getMessage)
                    sendErrorMessage(e.getMessage, senderEmail)
                case e:Exception =>
                    e.printStackTrace()
                    sendErrorMessage("Something wrong when processing your email reply, your reply may not sent",
                        senderEmail)
            }

            //            val writer = new StringWriter()
            //            IOUtils.copy(data, writer)
            //            debug("data: " + writer.toString)
            //            writer.close()
        }


        private def getEmailSession(mailMsg: MimeMessage):Option[String] = {
            import scala.collection.JavaConversions._

            val text = EmailHelper.getText(mailMsg, false)

//            debug("text after EmailHelper.getText: " + text)

            val doc = Jsoup.parse(text)


            doc.select("a").map(_.attr("href")).filter { href =>
                href.contains("?ref=email&sid") || href.contains("?ref=email&amp;sid")
            }.flatMap { href =>
                val s = href.split("sid\\=|&ntf\\=|&amp;ntf\\=")
                if (s.length == 3)
                    Some(s(1))
                else
                    None
            }.headOption


        }

        def done() {}


        private def processEmailSession:PF = (responseProcessor orElse summonProcessor
            /*orElse pmCreateProcessor orElse pmResponseProcessor*/)


    }
}

object EmailMessageContentNormalizer {
    def normalize(txt:String) = {
        txt.indexOf("\n>") match {
            case idx if idx > -1 =>
                txt.substring(0, idx)
            case _ =>
                txt
        }
    }

    def normalizeHtml(html:String) = {
        import scala.collection.JavaConversions._

        val _text = html.replaceAll("(?i)<br[^>]*>", "{nlbr}")

        val doc = Jsoup.parse(_text)

        // normalize gmail extra data if any
        for (elm <- doc.select(".gmail_extra").iterator()){
            elm.remove()
        }
        for (elm <- doc.select("blockquote").iterator()){
            elm.remove()
        }

        doc.text().replaceAll("\\{nlbr\\}","\n")
    }

}

object EmailHelper extends Slf4jLogger {

    def getText(part:Part, normalize:Boolean):String = {
        if (part.isMimeType("multipart/alternative")){
            val mp = part.getContent.asInstanceOf[MimeMultipart]
            val sb = new StringBuilder
            //            for (i <- 1 to mp.getCount){
            for (i <- 1 to 1){ // hanya ambil satu part aja
            val p = mp.getBodyPart(i)
                if (p.isMimeType("text/plain")){
                    sb.append(p.toString)
                }else if (p.isMimeType("text/html")){
                    if (normalize){
                        debug("part " + i + " (before norm html): " + getText(p, normalize))
                        val s = EmailMessageContentNormalizer.normalizeHtml(getText(p, normalize))
                        debug("part " + i + " (after norm html): " + s)
                        sb.append(s)
                    }else{
                        sb.append(getText(p, normalize))
                    }
                }else{
                    sb.append(getText(p, normalize))
                }
            }
            sb.result()
        }else if (part.isMimeType("multipart/*")){
            val mp = part.getContent.asInstanceOf[MimeMultipart]
            val sb = new StringBuilder
            //            for (i <- 0 to mp.getCount-1){
            for (i <- 1 to 1){ // hanya ambil satu part aja
            val p = mp.getBodyPart(i)
                val s = getText(p, normalize)
                sb.append(s)
            }
            sb.result()
        }else if (part.isMimeType("text/*")){
            part.getContent.toString
        }else{
            ""
        }
    }
}

case class PipeMailData(cls:String, message:MimeMessage, v:Vertex, user:User)

package object processor extends DbAccess with Slf4jLogger {


    type PF = PartialFunction[PipeMailData,Unit]

    var emailApp:App = _

    private def getContent(mm:MimeMessage): String = {
        val txt = EmailHelper.getText(mm, normalize = true)
        //debug("txt after getText: " + txt)
        EmailMessageContentNormalizer.normalize(txt)
    }

    /**
     * Untuk memproses email response terhadap notification.
     * @return
     */
    def responseProcessor:PF = {
        case PipeMailData(cls, msg, ntfV, user) if cls == "com.ansvia.digaku.notifications.impl.ResponseNotification" =>

            ntfV.toCC[ResponseNotification].map { ntf =>
                ntf.respondedObject.map { ro =>

                // permission checking.
                    ro match {
                        case ho:HasOrigin[GraphType] => {
                            ho.origin match {
                                case ch:Forum => {
                                    if (!ch.userCanResponse_?(user))
                                        throw IgnoredException(("user %s ignored to response via email because %s " +
                                            "is not allowed by permission setting of group #%s").format(user.name,
                                            user.name, ch.name))
                                }
                                case u:User => {
                                    if (u.isBlocked(user))
                                        throw IgnoredException(("user %s ignored to response via email because %s " +
                                            "is blocked by %s").format(user.name, user.name, u.name))
                                }
                            }
                        }
                    }

                    //                val content = msg.getContent.toString
                    val resp = ro.addResponse(user, getContent(msg))
                    resp.reload()
                    emailApp.reload()
                    resp.setPostVia(emailApp)
                    db.commit()
                }
            }


    }

    private def addResponse(user:User, msg:MimeMessage, responable:Responable) = {
        val resp = responable.addResponse(user, getContent(msg))
        resp.reload()
        emailApp.reload()
        resp.setPostVia(emailApp)
        db.commit()
        resp
    }


    /**
     * Untuk memproses email summon terhadap notification.
     * @return
     */
    def summonProcessor:PF = {
        case PipeMailData(cls, msg, ntfV, user) if cls == "com.ansvia.digaku.notifications.impl.SummonNotification" =>

            ntfV.toCC[SummonNotification].map { ntf =>

                ntf.obj.map { ro =>

                // permission checking.
                    ro match {
                        case ho:HasOrigin[GraphType] => {
                            ho.origin match {
                                case ch:Forum => {
                                    if (!ch.userCanResponse_?(user))
                                        throw IgnoredException(("user %s ignored to response via email because %s " +
                                            "is not allowed by permission setting of group #%s").format(user.name,
                                            user.name, ch.name))
                                }
                                case u:User => {
                                    if (u.isBlocked(user))
                                        throw IgnoredException(("user %s ignored to response via email because %s " +
                                            "is blocked by %s").format(user.name, user.name, u.name))
                                }
                            }
                        }
                    }

                    //                val content = msg.getContent.toString

                    ro match {
                        case responable:Responable => {
                            addResponse(user, msg, responable)
                        }
                        case response:Response => {
                            response.getRespondedObject.map { responable =>
                                addResponse(user, msg, responable)
                            }
                        }
                        case x => {
                            warn("don't know how to handle this object: " + x)
                        }
                    }


                }
            }


    }

//    /**
//     * Untuk memproses email PrivateMessageNotification (pm create)
//     * @return
//     */
//    def pmCreateProcessor:PF = {
//        case PipeMailData(cls, msg, ntfV, user)
//            if cls == "com.ansvia.digaku.notifications.impl.PrivateMessageNotification" =>
//
//            ntfV.toCC[PrivateMessageNotification].map { ntf =>
//
//                ntf.privateMessage.map { pm =>
//
//                // permission checking
//                    if (!pm.getParticipants.contains(user))
//                        throw IgnoredException(("user %s ignored to reply pm via email because %s " +
//                            "is not participants of pm id %s").format(user.name, user.name, pm.getId))
//
//                    pm.reload()
//                    user.reload()
//                    /*val resp = */pm.addResponse(user, getContent(msg))
//                    db.commit()
//                }
//
//            }
//
//    }
//
//    /**
//     * Untuk memproses email PrivateMessageNotification (pm create)
//     * @return
//     */
//    def pmResponseProcessor:PF = {
//        case PipeMailData(cls, msg, ntfV, user)
//            if cls == "com.ansvia.digaku.notifications.impl.MessageResponseNotification" =>
//
//            ntfV.toCC[MessageResponseNotification].map { ntf =>
//
//                ntf.privateMessageObject.map { pm =>
//
//                // permission checking
//                    if (!pm.getParticipants.contains(user))
//                        throw IgnoredException(("user %s ignored to reply pm via email because %s " +
//                            "is not participants of pm id %s").format(user.name, user.name, pm.getId))
//
//                    if (!user.isActivated)
//                        throw IgnoredException(s"user ${user.name} unable to reply PM via email because email is not confirmed yet.")
//
//                    pm.reload()
//                    user.reload()
//                    /*val resp = */pm.addResponse(user, getContent(msg))
//                    db.commit()
//                }
//
//            }
//
//    }



}

