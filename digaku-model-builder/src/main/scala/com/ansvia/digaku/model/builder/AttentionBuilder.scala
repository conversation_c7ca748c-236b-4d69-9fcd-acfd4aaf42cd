/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import scala.xml.{Text, Unparsed, NodeSeq}
import com.ansvia.digaku.notifications.{Attention, PersistentNotification, AcceptRejectAttention}
import com.ansvia.digaku.{Digaku, model}
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.Types._

/**
 * Author: robin
 *
 */

trait AttentionNodeSeqWrapperIface {
    /**
     * Untuk build attention notification (magic box) di atas stream user.
     * @param template -- digunakan untuk membedakan antara template mobile dan web.
     * @param user --
     * @return
     */
    def toNodeSeq(template: String)(implicit user:model.User):NodeSeq
}

trait AttentionBuilder extends Slf4jLogger {

    abstract class AcceptRejectAttentionNodeSeqWrap(att:PersistentNotification with AcceptRejectAttention)
        extends AttentionNodeSeqWrapperIface {

        def toNodeSeq(template: String)(implicit user:model.User):NodeSeq
    }


}

object AttentionBuilder extends AttentionBuilder

