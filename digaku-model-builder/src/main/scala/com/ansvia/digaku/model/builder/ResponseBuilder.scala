/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.digaku.{Di<PERSON>ku, model}
import com.ansvia.digaku.Types._

import scala.xml.NodeSeq
import com.ansvia.digaku.model._
import com.ansvia.digaku.exc.{NotExistsException, NotImplementedException, UnsupportedException}
import com.ansvia.digaku.model.builder.ContentDeletableBuilder.ContentDeletableNodeSeqWrapperBase

/**
 * Author: robin
 *
 */
trait ResponseBuilder {

    abstract class ResponseNodeSeqWrapperBase(resp:model.Response){

        protected implicit def userAutoCenverter(user:model.User):UserBuilder.UserWrapperBase
        protected implicit def originAutoConvert(origin:model.Origin[GraphType]):OriginBuilder.OriginWrapperBase
        protected implicit def postConverter(p:model.Post):PostBuilder.PostNodeSeqWrapperBase
//        protected implicit def eventConverter(event:model.Event):EventBuilder.EventNodeSeqWrapperBase
        protected implicit def pictConverter(pict:model.PictureBase):PictureBuilder.PictureWrapperBase
        protected implicit def contentDeletableAutoConverter(deletable:model.Deletable):ContentDeletableBuilder.ContentDeletableNodeSeqWrapperBase

        /**
         * Jumlah reply dalam satu halaman.
         * @return
         */
        protected def replyPerPage:Int

        def isLoggedIn:Boolean

        def currentUser:Option[model.User]

        def likeClickResponse:NodeSeq

        def responseRemoverButton:NodeSeq

        def buildReportButton:NodeSeq



        def cuIsAdmin = {
            currentUser.exists { cu =>
                val admin = (cu.role == model.UserRole.ADMIN || cu.role == model.UserRole.SUPER_ADMIN)
                val adminChannel = {
                    model.Forum.getById(resp.origin.getId).exists(ch =>
                        ch.getStaffAbility(cu).contains(Ability.DELETE_POST) || ch.isOwner(cu))
                }

                admin || adminChannel

            }//.getOrElse(false)
        }




        def canQuoteResponse:Boolean

        /**
         * Ditambahkan caption karena btn quote pada mobile
         * tidak menggunakan caption Quote melainkan hanya tanda kutip
         * @param caption
         * @return
         */
        def quoter(caption:String = "Quote"):NodeSeq = {
            if (isLoggedIn && canQuoteResponse) {
                resp.getRespondedObject.filter(_.isInstanceOf[model.Lockable]).map(_.asInstanceOf[model.Lockable]).filter(_.isLocked == false).map { p =>
                    <button class="btn btn-small btn-quoter icon-quote-left pull-right" rel="tooltip" data-click-tooltip="false" data-toggle="tooltip" title="Quote" data-placement="bottom"
                             user-name={resp.creator.getName} text-data={resp.getId.toString}>{caption}</button>
                        :NodeSeq
                }.getOrElse(NodeSeq.Empty)
            } else
                NodeSeq.Empty
        }

        /**
         * Mendapatkan url response berdasarkan highlight response
         * Menyesuaikan jika single view pada posisi embed (popup)
         *
         * @param embedMode
         * @param paginationMode Int see [[com.ansvia.digaku.model.ResponsePaginationMode]]
         * @return
         */
        def url(embedMode:Boolean, paginationMode:Int = ResponsePaginationMode.PAGE_NUMBER) = {
            val post = resp.getRespondedObject.getOrElse {
                throw NotExistsException("This response not bound to any responable object like post etc")
            }

            val joinStr = if (embedMode) "&" else "?"

            val pageItem = if( paginationMode == ResponsePaginationMode.PAGE_NUMBER ) {
                    if (post.getIndexOfResponse(resp) >= replyPerPage) {
                        "%soffset=%s&rid=%s".format(joinStr, (post.getIndexOfResponse(resp) / replyPerPage) * replyPerPage, resp.getId)
                    } else
                        "%srid=%s".format(joinStr, resp.getId)
                } else
                    ("%srid=%s".format(joinStr, resp.getId))

            post match {
                case p:com.ansvia.digaku.model.Post =>
                    p.url(embedMode) + pageItem
//                case p:com.ansvia.digaku.model.Event =>
//                    p.url(embedMode) + pageItem
                case p:com.ansvia.digaku.model.PictureBase =>
                    p.url(embedMode) + pageItem
                case respObj:model.Response =>
                    val pageItem = if (respObj.getIndexOfResponse(resp) >= replyPerPage) {
                            "%soffset=%s&rid=%s".format(joinStr, (respObj.getIndexOfResponse(resp) / replyPerPage) * replyPerPage, resp.getId)
                        } else {
                            "%srid=%s".format(joinStr, resp.getId)
                        }

                    singleUrl(embedMode) + pageItem
            }
        }

        private def singleUrlMatcher(rObj:model.Responable, refResp:model.Response, embedMode:Boolean):String = {
            rObj match {
                case p:com.ansvia.digaku.model.Post =>
                    p.url(embedMode) + "/reply/" + refResp.getId
                case p:com.ansvia.digaku.model.PictureBase =>
                    p.url(embedMode) + "/reply/" + refResp.getId
            }
        }

        def singleUrl(embedMode:Boolean): String ={
            val rObj = resp.getRespondedObject.getOrElse {
                throw NotExistsException("This response not bound to any responable object like post etc")
            }

            rObj match {
                case r:Response =>
                    val rObj2 = r.getRespondedObject.getOrElse {
                        throw NotExistsException("This response not bound to any responable object like post etc")
                    }

                    singleUrlMatcher(rObj2, r, embedMode)

                case x:Responable =>
                    singleUrlMatcher(x, resp, embedMode)
            }

        }
    }
}

object ResponseBuilder extends ResponseBuilder {
    class UnimplementedResponseNodeSeqWrapper(resp:model.Response) extends ResponseNodeSeqWrapperBase(resp) {


        protected implicit def userAutoCenverter(user: User) = new UserBuilder.UnimplementedUserWrapper(user)
        protected implicit def postConverter(p: Post) = new PostBuilder.UnimplementedPostNodeSeqWrapper(p)
//        protected implicit def eventConverter(event: Event) = new EventBuilder.UnimplementedEventBuilderWrapper(event)
        protected implicit def pictConverter(pict: PictureBase) = new PictureBuilder.UnimplementedPictureWrapperBase(pict)

        protected implicit def originAutoConvert(origin: Origin[GraphType]) =
            new OriginBuilder.UnimplementedOriginWrapper(origin)

        def isLoggedIn = {
            throw NotImplementedException("isLoggedIn belum diset, mohon override HtmlDgCode.picBuilder " +
                "dengan implementasi mu sendiri.")
        }

        def currentUser = None

        def buildLikeButton(resp: Response) = {
            throw NotImplementedException("buildLikeButton belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        def likeClickResponse = {
            throw NotImplementedException("likeClickResponse belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        def responseRemoverButton = {
            throw NotImplementedException("responseRemoverButton belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        def canQuoteResponse = {
            throw NotImplementedException("canQuoteResponse belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        def likersClick: NodeSeq = null

        protected implicit def contentDeletableAutoConverter(app: Deletable): ContentDeletableNodeSeqWrapperBase = {
            throw NotImplementedException("contentDeletableAutoConverter belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri")
        }

        def buildReportButton: NodeSeq = {
            throw NotImplementedException("buildReportButton belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri")
        }

        override protected def replyPerPage: Int = {
            throw NotImplementedException("buildReportButton belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri")
        }
    }

}