///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model.jsonbuilder
//
//import net.liftweb._
//import json._
//import JsonDSL._
//import com.ansvia.digaku.model.{MessageResponse, PrivateMessage,User}
////import com.ansvia.digaku.restapi.SimpleTime
//
///**
// * Author: robin
// *
// */
//trait PrivateMessageBuilder {
//
//    protected class PMWrapper(pm:PrivateMessage) {
//
////        import UserBuilder._
//
//        def toSimpleJson(userRef:User):JValue = {
//            import UserBuilder._
//            ("id" -> pm.getId) ~
//                ("subject" -> pm.subject) ~
//                ("message" -> pm.content) ~
//                ("has_read" -> pm.isParticipantHasRead(userRef)) ~
//                ("reply_count" -> pm.getResponseCountByUser(userRef)) ~
//                ("creator" -> buildUserInfoSimple(pm.creator)) ~
//                ("participants" -> pm.getParticipants.map(UserBuilder.buildUserInfoSimple)) ~
//                ("creation_time" -> (pm.creationTime / 1000))
//        }
//    }
//
//    protected class PMResponseWrapper(pmr:MessageResponse) {
//        def toJson = {
//            ("id" -> pmr.getId) ~
//                ("content" -> pmr.content) ~
//                ("creator" -> UserBuilder.buildUserInfoSimple(pmr.creator)) ~
//                ("creation_time" -> (pmr.creationTime / 1000))
//        }
//    }
//
//    implicit def implicitPMWrapper(pm:PrivateMessage) = new PMWrapper(pm)
//    implicit def implicitPMToJValue(pm:PrivateMessage)(implicit userRef:User):JValue = new PMWrapper(pm).toSimpleJson(userRef)
//    implicit def implicitPMsWrapper(pms:Iterable[PrivateMessage])(implicit userRef:User):JValue = {
//        pms.map(_.toSimpleJson(userRef))
//    }
//    implicit def implicitPMsWrapper(pms:TraversableOnce[PrivateMessage])(implicit userRef:User):JValue = {
//        implicitPMsWrapper(pms.toIterable)
//    }
//    implicit def implicitPMRWrapper(pmr:MessageResponse) = {
//        new PMResponseWrapper(pmr)
//    }
//    implicit def implicitPMRWrapperToJValue(pmr:MessageResponse):JValue = {
//        new PMResponseWrapper(pmr).toJson
//    }
//    implicit def implicitPMRsWrapper(pmrs:Iterable[MessageResponse]):JValue = {
//        pmrs.map(_.toJson)
//    }
//
//}
//
//object PrivateMessageBuilder extends PrivateMessageBuilder
