///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model.jsonbuilder
//
//import com.ansvia.digaku.model._
//import net.liftweb.json.JsonDSL._
//import net.liftweb.json._
//
///**
// * Author: robin (<EMAIL>)
// */
//
//trait PictureGroupBuilder {
//
//
//
//
//    abstract class PictureGroupBuilder(post:PictureGroup) {
//
//        def toJson(userRef:Option[User], overwriteId:Option[Long], templateMode:Boolean, addParams:(String, String)*): JValue
//
//    }
//
//    abstract class JsonPictureGroupWrapper(picGroup:PictureGroup) extends PictureGroupBuilder(picGroup) /*with BasicContentBuilder*/ /*with App<PERSON><PERSON><PERSON>*/ {
//
//        implicit protected def picGroupAutoConverter(_picGroup:PictureGroup):com.ansvia.digaku.model.builder.PictureBuilder.PictureWrapperBase
//        implicit protected def basicContentConverter(_bc:BasicContentBuilder.BasicContentType):com.ansvia.digaku.model.jsonbuilder.BasicContentBuilder.JsonBasicContentWrapper
//        implicit protected def implJsonPictureWrapper(pic:Picture):PictureBuilder.JsonPictureWrapper
//
//        protected val SHORT_URL:String
//
//        protected val urlShorter:(String) => String
//
//
//        def toJson(userRef:Option[User], overwriteId:Option[Long], templateMode:Boolean,
//                   addParams:(String, String)*): JValue = {
//
//            val shortUrl = if (!picGroup.originIsPrivate) {
//                SHORT_URL + urlShorter(picGroup.url(false))
//            } else {
//                ""
//            }
//
//            var rv = picGroup.basicContent(userRef, overwriteId, templateMode) ~ ("kind" -> "picture_group") ~
//                ("content" -> picGroup.getTitle) ~
//                ("pictures_count" -> picGroup.pictures.length) ~
//                ("picture" -> picGroup.headPic.map(_.toJsonPicUrlOnly)) ~
//                ("pictures" -> picGroup.pictures.map(_.toJsonSimpleWithNoCreator)) ~
//                ("via" -> picGroup.getPostVia.map { app =>
//                    ("id" -> app.getId) ~
//                        ("name" -> app.name)
//                }) ~
//                ("share_url" -> shortUrl)
//
//            if (userRef.isDefined) {
//                if (picGroup.creator == userRef.get && picGroup.getVertex.getProperty[Boolean]("publish")) {
//                    rv = rv ~ ("publish" -> "true")
//                } else {
//                    rv = rv ~ ("publish" -> "false")
//                }
//            }
//
//            rv
//
//        }
//
//
//    }
//
//
//}
//
//object PictureGroupBuilder extends PictureGroupBuilder
//
