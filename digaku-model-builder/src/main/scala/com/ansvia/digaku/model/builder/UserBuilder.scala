/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.digaku.Types._
import com.ansvia.digaku.exc.NotImplementedException
import com.ansvia.digaku.model.{Origin, ProfilePicDefault}
import com.ansvia.digaku.{Digaku, Types, model}
import org.apache.commons.codec.digest.DigestUtils

import scala.xml.NodeSeq


/**
 * Author: robin
 *
 */
trait UserBuilder {


    abstract class UserWrapperBase(user:model.User){


        // tidak pake with DbAccess karena ini menghindari ambiguous ketika import
        // karena with DbAccess pakenya bukan private tapi protected.
        private implicit def db:GraphType = Digaku.engine.database.getRaw


        implicit def originAutoConvert(origin:model.Origin[GraphType]):OriginBuilder.OriginWrapperBase

        val httpProtocol:String

        /**
         * Http protocol yang digunakan oleh phitos atau aws
         */
        val awsHttpProtocol:String
        val baseUrl:String

        // url to profile page
        val profileUrl:String

        private def fixHttpProtocol(url:String) = {
            url.replace("http://", httpProtocol + "://")
                .replace("https://", httpProtocol + "://") // butuh bolak-balik dua-dua-nya
        }

        /**
         * Rewrite http schema pada url menggunakan value dari [[awsHttpProtocol]]
         */
        private def fixS3HttpProtocol(url:String):String = {
            url.replace("http://", awsHttpProtocol + "://")
                .replace("https://", awsHttpProtocol + "://")
        }

        /**
         * Set banner User dengan default banner BCA MC2
         * @return
         */
        private def defaultBanner() = {
            // val num = user.name.map(_.toInt).sum % 3
            fixHttpProtocol(baseUrl + "/assets/img/default/banner/user.jpg")
        }


        /**
         * Mendapatkan url small size profile picture user
         *
         * Jika belum ke set di user pakai profile pic default system
         * Jika profile picture default system blom ada pake gravatar
         *
         * @return
         */
        def normalizedPhotoSmall = {
            if (user.photoSmall.length > 0)
                fixS3HttpProtocol(user.photoSmall)
            else {
                val profilePicCount = ProfilePicDefault.getCount
                if (profilePicCount > 0){
//                    var pic = ""
                    val num =
                        if (profilePicCount > 1)
                            (user.name.map(_.toInt).sum % profilePicCount).toInt
                        else
                            0

                    val avatarList = ProfilePicDefault.getListRight(None, None, 100).toList
                    if (avatarList.length > num)
                        fixHttpProtocol(avatarList(num).smallUrl)
                    else
                        avatar(32)
                } else
                    avatar(32)
            }
        }

        /**
         * Mendapatkan url medium size profile picture user
         *
         * Jika belum ke set di user pakai profile pic default system
         * Jika profile picture default system blom ada pake gravatar
         * @return
         */
        def normalizedPhotoMedium = {
            if (user.photoMedium.length > 0)
                fixS3HttpProtocol(user.photoMedium)
            else {
                val profilePicCount = ProfilePicDefault.getCount
                if (profilePicCount > 0){
//                    var pic = ""
                    val num =
                        if (profilePicCount > 1)
                            (user.name.map(_.toInt).sum % profilePicCount).toInt
                        else
                            0

                    val avatarList = ProfilePicDefault.getListRight(None, None, 100).toList
                    if (avatarList.length > num)
                        fixHttpProtocol(avatarList(num).mediumUrl)
                    else
                        avatar(64)
                } else
                    avatar(64)
            }
        }

        /**
         * Mendapatkan url large size profile picture user
         *
         * Jika belum ke set di user pakai profile pic default system
         * Jika profile picture default system blom ada pake gravatar
         *
         * @return
         */
        def normalizedPhotoLarge = {
            if (user.photoLarge.length > 0)
                fixS3HttpProtocol(user.photoLarge)
            else {
                val profilePicCount = ProfilePicDefault.getCount
                if (profilePicCount > 0){
//                    var pic = ""
                    val num =
                        if (profilePicCount > 1)
                            (user.name.map(_.toInt).sum % profilePicCount).toInt
                        else
                            0

                    val avatarList = ProfilePicDefault.getListRight(None, None, 100).toList
                    if (avatarList.length > num)
                        fixHttpProtocol(avatarList(num).originalUrl)
                    else
                        avatar(128)
                } else
                    avatar(128)
            }
        }

        def normalizeBannerPicUrl = {
            if (user.bannerPicUrl.length>0)
                fixS3HttpProtocol(user.bannerPicUrl)
            else
                fixHttpProtocol(defaultBanner())
//                "https://dmcd6hvaqrxz0.cloudfront.net/2013/04/23/415a9395c40a5d1cefbf8f8c2d50f892.jpg"
        }

        // Comment out MC2 tidak menggunakan cloudinary.
//        /**
//         * Sama seperti normalizedBannerPicUrl, bedanya ini akan mentransformasi
//         * ukurannya sesuai parameter yang diberikan. Menggunakan Cloudinary
//         * sebagai transformer service-nya.
//         * @param width lebar.
//         * @param height tinggi.
//         * @return
//         */
//        def normalizeBannerPicUrlWithSize(width:Int,height:Int) = {
//            if (user.bannerPicUrl.length>0)
//                "https://res.cloudinary.com/ansvia/image/fetch/w_" + width + ",h_" + height +
//                    ",c_fill/" + user.bannerPicUrl
//            else
////                "https://res.cloudinary.com/ansvia/image/fetch/w_" + width + ",h_" + height +
////                    ",c_fill/http://dmcd6hvaqrxz0.cloudfront.net/2013/04/23/415a9395c40a5d1cefbf8f8c2d50f892.jpg"
//                "https://res.cloudinary.com/ansvia/image/fetch/w_" + width + ",h_" + height +
//                    ",c_fill/" + defaultBanner()
//        }

        // Comment out MC2 tidak menggunakan gravatar.
//        def gravatar(size:Int) = {
//            val hash = DigestUtils.md5Hex(user.emailLogin)
//            "https://secure.gravatar.com/avatar/" + hash +  "?s=" + size + "&d=mm"
//        }

        private def avatar(size:Int) = {
            // val num = user.name.map(_.toInt).sum % 2
            baseUrl + "/assets/img/default/profile/2_" + size + ".png"
        }

        def bannerPicElm:NodeSeq =
            <div class="banner-box">
                <img src={normalizeBannerPicUrl} />
            </div>

        /**
         * thumbnail photo profile dengan icon badges yang terakhir
         * yang didapatkan oleh user.
         * @return
         */
        def thumbnailElmWithBadges:NodeSeq = {
            val latestBadges = user.getTrophies(0, 1).toSeq.headOption
            val className = if (latestBadges.isDefined) "img-group" else ""

            <div class="thumbnail with-badges"
                 data-hc-kind="user"
                 data-hc-username={user.name}
                 data-hc-userid={user.getId.toString}>
                <a href={user.url}
                   data-hc-kind="user"
                   data-hc-username={user.name}
                   data-hc-userid={user.getId.toString} class={className}>
                    <img class="user-thumbnail img-fluid rounded-circle" src={normalizedPhotoLarge} alt={user.name} />
                    {
                    latestBadges.map { trophy =>
                        <img class="badges-icon visible-xs-inline-block visible-sm-inline-block visible-md-inline-block
                        visible-lg-inline-block" src={fixS3HttpProtocol(trophy.icons)} alt={trophy.getName} />
                    }.getOrElse(NodeSeq.Empty)
                    }
                </a>
            </div>
        }


        def thumbnailElmSmall:NodeSeq =
            <div class="thumbnail thumb-small"
                data-hc-kind="user"
                data-hc-username={user.name}
                data-hc-userid={user.getId.toString}
                >
                <a href={user.url}
                   data-hc-kind="user"
                   data-hc-username={user.name}
                   data-hc-userid={user.getId.toString}
                >
                    <img src={normalizedPhotoSmall} alt={user.name} style="width: 40px; height: 40px;" />
                </a>
            </div>

        def thumbnailElmSmallNoLink:NodeSeq =
            <div class="thumbnail thumb-small"
                data-hc-kind="user"
                data-hc-username={user.name}
                data-hc-userid={user.getId.toString}>
                <img src={normalizedPhotoSmall} alt={user.name} style="width: 40px; height: 40px;" />
            </div>

        def thumbnailElmMedium:NodeSeq =
            <div class="thumbnail thumb-medium"
                 data-hc-kind="user"
                 data-hc-username={user.name}
                 data-hc-userid={user.getId.toString}>
                <img src={normalizedPhotoMedium} alt={user.name} class="img-circle" />
            </div>

        def thumbnailElmMediumWithLink:NodeSeq =
            <a href={user.url} title={user.funkyName}>{thumbnailElmMedium}</a>

        def thumbnailElmLarge:NodeSeq =
            <div class="thumbnail thumb-large"
                data-hc-kind="user"
                data-hc-username={user.name}
                data-hc-userid={user.getId.toString}>
                <a href={baseUrl + "/settings/profile"}>
                    <img src={normalizedPhotoLarge} alt={user.name} />
                </a>
            </div>

        def listItemSeq:NodeSeq = {
            <div class="clearfix">
                <a href={user.url}>{thumbnailElmSmall}</a>
                <trong><a href={user.url}>{user.name}</a></trong>
                {
                if (user.fullName.trim.length > 0)
                    <span>({user.fullName})</span>
                }
            </div>
        }

        def listItemNameOnlySeq:NodeSeq = {
            <xml:group>
                <span class={"user-list-item-" + user.getId}>
                    <trong><a href={user.url}>{user.name}</a></trong>
                    {
                    if (user.fullName.trim.length > 0)
                        <span>({user.fullName})</span>
                    }
                </span>
            </xml:group>
        }

        def normalizedEmailLogin:String = {
            if(user.emailLogin.endsWith("mc2.bca.co.id") || user.emailLogin.trim.isEmpty) {
                "-"
            } else {
                user.emailLogin
            }
        }
    }

//    protected final class UserWrapper(user:model.User) extends UserWrapperBase(user){
//
//    }
//
//    implicit def userToUserWrapper(user:model.User) = new UserWrapper(user)

}


object UserBuilder extends UserBuilder {
    class UnimplementedUserWrapper(u:model.User) extends UserWrapperBase(u){
        implicit def originAutoConvert(origin: Origin[Types.GraphType]) =
            new OriginBuilder.UnimplementedOriginWrapper(origin)
        val httpProtocol = "http"
        val baseUrl = throw NotImplementedException("baseUrl belum diset, mohon override fungsi ini " +
            "dengan implementasi mu sendiri")
        lazy val profileUrl: String = throw NotImplementedException("profileUrl belum diset, mohon override variable ini " +
            "dengan implementasi mu sendiri")
        /**
         * Http protocol yang digunakan oleh phitos atau aws
         */
        override val awsHttpProtocol: String = "http"
    }

}
