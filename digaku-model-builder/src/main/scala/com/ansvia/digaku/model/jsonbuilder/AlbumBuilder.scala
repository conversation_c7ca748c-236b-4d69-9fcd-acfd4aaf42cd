///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model.jsonbuilder
//
//import com.ansvia.digaku.exc.NotExistsException
//import com.ansvia.digaku.model.{Album, User}
//import net.liftweb.json.JsonAST.JObject
//import net.liftweb.json.JsonDSL._
//import net.liftweb.json._
//
///**
// * Author: fajrhf
// *
// */
//trait AlbumBuilder {
//
//    import com.ansvia.digaku.model.jsonbuilder.ChannelBuilder._
//
//    implicit def implicitAlbumToJObject(album:Album)(implicit cu:Option[User]):JObject = {
//
//        val parentChannel = album.group.getOrElse {
//            throw NotExistsException("Group not found")
//        }
//
//        ("id" -> album.getId) ~
//            ("name" -> album.name) ~
//            ("desc" -> album.desc) ~
//            ("cover_pic" -> album.coverPicUrl) ~
//            ("picture_count" -> album.getMediaCount) ~
//            ("parent_channel" -> parentChannel)
//
//    }
//
//    implicit def implicitAlbumstoJValue(albums:Iterator[Album])(implicit cu:Option[User]):JValue = {
//        albums.toSeq.map(x => x:JValue)
//    }
//    implicit def implicitAlbumstoJValue(albums:List[Album])(implicit cu:Option[User]):JValue = {
//        albums.toSeq.map(x => x:JValue)
//    }
//
//}
//
//object AlbumBuilder extends AlbumBuilder
