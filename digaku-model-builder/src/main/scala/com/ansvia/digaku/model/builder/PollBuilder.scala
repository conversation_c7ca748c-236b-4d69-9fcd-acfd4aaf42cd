/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.digaku.Types._
import com.ansvia.digaku.{Digaku, model}

import scala.xml.NodeSeq

/**
 * Author: robin
 * Date: 3/10/13
 * Time: 6:44 PM
 *
 */
trait PollBuilder {


    import com.ansvia.commons.StringTemplate._
    import com.ansvia.digaku.utils.RichString._

    abstract class PollWrapperBase(poll:model.Polling){

        // tidak pake with DbAccess karena ini menghindari ambiguous ketika import
        // karena with DbAccess pakenya bukan private tapi protected.
        private implicit def db:GraphType = Digaku.engine.database.getRaw


        protected implicit def originAutoConvert(origin:model.Origin[GraphType]):OriginBuilder.OriginWrapperBase =
            new OriginBuilder.UnimplementedOriginWrapper(origin)

        def currentUser:Option[model.User]
        def buildChoiceNs(choice:model.PollingChoice):NodeSeq

        def url = {
            if(poll.reload().origin == null)
                "null"
            else
                poll.reload().origin.url + "/poll/" + titleUrl + ".html"
        }
        def titleUrl = {
            (poll.title.urlize + "-" + poll.getId.toString).toLowerCase
        }

        def choicerActionNodeSeq:NodeSeq = {
            currentUser.map { cu =>

                if(poll.canChoice_?(cu)){

                    val actions = poll.getChoices.map { c => buildChoiceNs(c)
                        /*SHtml.a(()=>{
                            try {
                                c.reload().addChoicer(cu.reload())
                                S.redirectTo(url,()=>S.notice("Your choice is "+c.reload().title))
                            } catch {
                                case e:AlreadyExistsException =>
                                    S.redirectTo(url,()=>S.error("You have already voted"))
                            }

                        },Text(c.title)):NodeSeq*/

                    }.map(x => (<li>{x}</li>:NodeSeq)).reduceLeftOption(_ ++ _).getOrElse(NodeSeq.Empty)

                    <div class="well"><strong>Make your choice</strong> <em class="post-attributes">cilck to vote</em>: <div><ul>{actions}</ul></div></div>


                }else
                    NodeSeq.Empty

                :NodeSeq
            }.getOrElse(NodeSeq.Empty)
        }

        def drawChart:NodeSeq = {

            val choices = poll.getChoices.filter(x => x.chooserCount > 0)
                .map(x => """['%s',%s]""".format(x.title, x.chooserCount))
                .reduceLeftOption(_ + "," + _).getOrElse("")

            val jsRaw =
                """
                  |function rerenderPoll(datas){
                  | var data = google.visualization.arrayToDataTable(datas);
                  |
                  |  var options = {
                  |      title: '%{title}'
                  |  };
                  |
                  |  var chart = new google.visualization.PieChart(document.getElementById('ChartPlaceholder'));
                  |  chart.draw(data, options);
                  |}
                  |google.setOnLoadCallback(function(){
                  |  rerenderPoll([
                  |         ['Choice', 'People'],
                  |         %{choices}
                  |     ]);
                  |});
                  |
                """.stripMargin.render(Map("title" -> poll.title, "choices" -> choices))

            <script type="text/javascript">{xml.Unparsed("\n//<![CDATA[\n%s\n//]]>\n".format(jsRaw))}</script>

        }
    }

//    sealed class PollWrapper(poll:model.Polling) extends PollWrapperBase(poll){
//        def currentUser = {
//
//        }
//
//        def buildChoiceNs(choice: PollingChoice) = null
//    }
//
//    implicit def pollToPollWrapper(poll:model.Polling) = new PollWrapper(poll)
}


object PollBuilder extends PollBuilder
