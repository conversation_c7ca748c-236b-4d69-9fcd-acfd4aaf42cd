/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.jsonbuilder

import com.ansvia.digaku.model
import com.ansvia.digaku.utils.TextCompiler
import net.liftweb._
import json._
import JsonDSL._
import com.ansvia.digaku.model._
import UserGroupBuilder._
//import com.ansvia.digaku.restapi.SimpleTime

/**
 * Author: robin
 *
 */
trait ResponseBuilder {

    abstract class JsonResponseWrapper(response:Response) {

        implicit protected def userAutoConverter(_u:User):com.ansvia.digaku.model.builder.UserBuilder.UserWrapperBase

        /**
         * Get response content
         * @return
         */
        def getContent:String = {
            if(response.isDeleted) {

                val deleterName = model.User.getById(response.deletedByUserId).map(_.getName).getOrElse("")

                if (response.deletedRole == DeletedRole.POST_CREATOR) {
                    "This content has been deleted by %s".format(deleterName)
                } else {
                    "This content has been deleted by %s, (%s). Reason: %s".format(deleterName, response.deletedRole, response.deletedReason)
                }
            } else {
                TextCompiler.compileLink(response.content)
            }
        }

        def toSimpleJson(userRef:Option[User]=None):JValue = {

            val lastUpdatedTime = if (response.lastEditTime == response.creationTime) 0 else response.lastEditTime / 1000
            val creator = response.creator

            var rv = ("id" -> response.getId) ~
                ("content" -> getContent) ~
                ("creator" -> UserBuilder.buildUserInfoSimple(creator)) ~
                ("creation_time" -> (response.creationTime / 1000)) ~
                ("creation_time_age" -> response.getCreationAge) ~
                ("last_edited_time" -> lastUpdatedTime) ~
                ("response_count" -> response.getResponseCount) ~
                ("via" -> response.getPostVia.map { app =>
                    ("id" -> app.getId) ~
                        ("name" -> app.name)
                }) ~
                ("is_can_delete" -> canDelete(userRef, response)) ~
                ("is_can_give_reputation" -> userRef.exists(cu => cu.userCanGiveReputation(response))) ~
                ("is_given_reputation" -> userRef.exists(cu => cu.inLastGivenReputationList(creator)))

            //show this info if the the response is non-deleted response
            if(!response.isDeleted) {
                rv = rv ~ ("cools_count" -> response.getLikesCount) ~
                    ("coolers" -> response.getLikers(0,20).map(u => ("id" -> u.getId) ~ ("name" -> u.getName):JValue).toList)

                //add is cooled by me
                if (userRef.isDefined) {
                    rv = rv ~ ("is_cooled_by_me" -> response.isLiker(userRef.get))
                }
            } else {
                rv = rv ~
                    ("is_deleted" -> response.isDeleted) ~
                    ("deleted_reason" -> response.deletedReason) ~
                    ("deleted_time" -> response.deletedTime) ~
                    ("deleted_by" -> {
                        response.deletedRole match {
                            case model.DeletedRole.ADMIN_CHANNEL =>
                                "Moderator"
                            case model.DeletedRole.POST_CREATOR =>
                                "Creator"
                            case model.DeletedRole.ADMIN_SYSTEM =>
                                "Admin"
                        }
                    }) ~
                    ("deleter_id" -> response.deletedByUserId)
            }

            response.getPostAs.foreach { ug =>
                rv = rv ~ ("post_as_group" -> {
                    val isAdmin = userRef.exists(u => u.role == UserRole.ADMIN || u.role == UserRole.SUPER_ADMIN)
                    val isMember = userRef.exists(u => ug.isMember(u))
                    val hideCreator = isAdmin || isMember
                    ug.toJson() ~
                        ("show_creator" -> hideCreator)
                })
            }

            rv = rv ~ ("embedded_objects" ->
                    ("file" -> response.getEmbeddedObjects.filter(_.isInstanceOf[EmbeddedFile]).collect {
                        case file:EmbeddedFile =>
                            val respId = response.getId
                            val fileId = file.getId
                            val fileUrl = s"${creator.baseUrl}/post/response/$respId/download-attachment/$fileId?via=app"
                            ("kind" -> "file") ~
                                ("name" -> file.name) ~
                                ("url" -> fileUrl) ~
                                ("md5_sum" -> file.md5) ~
                                ("size" -> file.size) ~
                                ("readable_size" -> file.prettySize)
                    })
                )

            rv
        }

        def toJson(userRef:Option[User]=None,
                   likersSmallThumbnail:Boolean=false):JValue = {

            var rv = ("id" -> response.getId) ~
                ("content" -> response.content) ~
                ("cools_count" -> response.getLikesCount) ~
                ("coolers" -> response.getLikers(0,20).toList.map { u =>
                        ("id" -> u.getId) ~
                        ("name" -> u.getName) ~
                        ("photo_small" -> u.normalizedPhotoSmall)
                }) ~
                ("response_count" -> response.getResponseCount) ~
                ("creator" -> UserBuilder.buildUserInfoSimple(response.creator)) ~
                ("creation_time" -> (response.creationTime / 1000)) ~
                ("creation_time_age" -> response.getCreationAge)~
                ("via" -> response.getPostVia.map { app =>
                    ("id" -> app.getId) ~
                        ("name" -> app.name)
                }) ~
                ("is_can_delete" -> canDelete(userRef, response)) ~
                ("is_can_give_reputation" -> userRef.exists(cu => cu.userCanGiveReputation(response))) ~
                ("is_given_reputation" -> userRef.exists(cu => cu.inLastGivenReputationList(response.creator)))

            response.getPostAs.foreach { ug =>
                rv = rv ~ ("post_as_group" -> {
                    val isAdmin = userRef.exists(u => u.role == UserRole.ADMIN || u.role == UserRole.SUPER_ADMIN)
                    val isMember = userRef.exists(u => ug.isMember(u))
                    val hideCreator = isAdmin || isMember
                    ug.toJson() ~
                        ("show_creator" -> hideCreator)
                })
            }

            if (userRef.isDefined) {
                rv = rv ~ ("is_cooled_by_me" -> response.isLiker(userRef.get))
            }

            rv
        }

        /**
         * Berguna untuk menangani apabila ada perubahan kemampuan delete di rule
         * mengacu pada com.ansvia.digaku.restapi.PostRest di bagian delete post
         * @return Boolean
         */
        def canDelete(userO:Option[User], content:Response):Boolean = {

            import com.ansvia.digaku.utils.UserSettings._

            if (userO.isDefined) {

                val cu = userO.get
                val p = content
                val superAdmin = cu.role == UserRole.SUPER_ADMIN
                val adminStaff = cu.role == UserRole.ADMIN
                val owner = p.creator == cu

                val channelOwner = {
                    p.origin match {
                        case ch:model.Forum => ch.isOwner(cu)
                        case user:model.User =>
                            false
                    }
                }

                val adminStaffCanDelete = {
                    cu.getAbilities.contains(Ability.DELETE_POST)
                }

                val adminChannelCanDelete = {
                    p.origin match {
                        case ch:model.Forum =>
                            ch.getStaffAbility(cu).contains(Ability.DELETE_POST)
                        case _ => false
                    }
                }

                if (owner || superAdmin || channelOwner || adminStaff ||
                    adminStaffCanDelete || adminChannelCanDelete) {
                    true
                } else {
                    //current user don't have enough permission
                    false
                }

            } else false
        }

    }

//    implicit def implicitResponseWrapper(rsp:Response) = new JsonResponseWrapper(rsp)
//    implicit def implicitResponsesWrapper(rsps:Iterable[Response]):JValue = {
//        rsps.map( r => r.toSimpleJson() )
//    }
//    implicit def implicitResponsesWrapper(rsps:Seq[Response]):JValue = {
//        rsps.map( r => r.toSimpleJson() )
//    }


}

object ResponseBuilder extends ResponseBuilder

