/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.jsonbuilder

import com.ansvia.digaku.model._
import com.ansvia.digaku.utils.TextCompiler
import com.ansvia.perf.PerfTiming
import net.liftweb.json._
import JsonDSL._
import UserGroupBuilder._

/**
 * Author: robin (<EMAIL>)
 */

trait PostBuilder {


    abstract class PostBuilder(post:PostBase) {

        def toJson(userRef:Option[User], overwriteId:Option[Long],
                   templateMode:Boolean, attributes:List[String]=Nil,
                   addParams:Map[String, String]=Map.empty): JValue

    }


//    abstract class SimplePostBuilder(p:SimplePost) extends PostBuilder(p) with PerfTiming with AppBuilder {
//
//        implicit protected def postAutoConverter(p:SimplePost):com.ansvia.digaku.model.builder.PostBuilder.PostNodeSeqWrapperBase
//        implicit protected def basicContentConverter(_bc:BasicContentBuilder.BasicContentType):com.ansvia.digaku.model.jsonbuilder.BasicContentBuilder.JsonBasicContentWrapper
//
//        protected val oembedHelper:OembedHelper
//        protected val SHORT_URL:String
//
//        protected val urlShorter:(String) => String
//
//        def toJson(userRef:Option[User], overwriteId:Option[Long], templateMode:Boolean,
//                   attributes:List[String]=Nil, addParams:Map[String, String]=Map.empty): JValue = {
//
//            /**
//             * 2014 May 17
//             * Untuk data migrasi, simple post yang mengandung link gambar, akan dikembalikan oleh API
//             * sebagai post embedded link picture
//             */
//
//            val shareUrl = if (!p.originIsPrivate) {
//                SHORT_URL + urlShorter(p.url(embedMode = false))
//            } else {
//                ""
//            }
//
//            var rv = timing("simplePost.basicContent") {
//                p.basicContent(userRef, overwriteId, templateMode, attributes) ~
//                    ("content" -> p.content) ~
//                    ("share_url" -> shareUrl)
//            }
//
//
//            val embeddedObjects = timing("p.getEmbeddedObjects") {
//                p.getEmbeddedObjects
//            }
//
//            if(embeddedObjects.exists {
//                case el: EmbeddedLink if el.kind == LinkKind.VIDEO && el.thumbnail != "" => true
//                case _ => false
//            }) {
//                rv = rv ~ ("kind" -> "video")
//            } else {
//                rv = rv ~ ("kind" -> "simple_post")
//            }
//
//
//            if (embeddedObjects.exists {
//                case el:EmbeddedLink if el.kind == LinkKind.VIDEO && el.thumbnail != "" => true
//                case el:EmbeddedLink if el.kind == LinkKind.PIC => true
//                case el:EmbeddedLink if el.thumbnail != "" => true
//                case el:EmbeddedLink if el.thumbnail == "" => true
//                case _ => false
//            }) {
//                rv = rv ~ ("embedded_objects" -> embeddedObjects.distinct.collect {
//                    case el:EmbeddedLink if el.kind == LinkKind.VIDEO && el.thumbnail != "" =>
//
//                        val html = timing("getOembedData(el)"){
//                            oembedHelper.getOembedData(el)
//                        }
//
//                        ("title" -> el.title) ~
//                            ("description" -> el.desc) ~
//                            ("thumbnail_url" -> el.thumbnail) ~
//                            ("html" -> html) ~
//                            ("url" -> el.url)~
//                            ("kind" -> "embedded_video_link")
//                    case el:EmbeddedLink if el.kind == LinkKind.PIC =>
//                        //untuk menyesuaikan dengan content hasil migrasi
//                        ("title" -> el.title) ~
//                            ("desc" -> el.desc) ~
//                            ("url" -> el.url) ~
//                            ("kind" -> "embedded_pic")
//                    case el:EmbeddedLink if el.thumbnail != "" =>
//                        ("title" -> el.title) ~
//                            ("desc" -> el.desc) ~
//                            ("thumbnail_url" -> el.thumbnail) ~
//                            ("url" -> el.url) ~
//                            ("kind" -> "embedded_link")
//                    case el:EmbeddedLink if el.thumbnail == "" =>
//                        ("title" -> el.title) ~
//                            ("desc" -> el.desc) ~
//                            ("url" -> el.url) ~
//                            ("kind" -> "embedded_link")
//                    case _ =>
//                        List.empty:JObject
//                })
//            } else {
//                rv
//            }
//
//            buildViaFor(p).map(_ ~ rv).getOrElse(rv)
//        }
//    }

    abstract class ArticlePostBuilder(p:Article) extends PostBuilder(p) with PerfTiming with AppBuilder {

        implicit protected def postAutoConverter(_p:Article):com.ansvia.digaku.model.builder.PostBuilder.PostNodeSeqWrapperBase
        implicit protected def basicContentConverter(_bc:BasicContentBuilder.BasicContentType):com.ansvia.digaku.model.jsonbuilder.BasicContentBuilder.JsonBasicContentWrapper

        protected val oembedHelper:OembedHelper

        /**
         * Jumlah response yang ditampilkan perhalaman
         */
        protected val responsePerPage:Int

        def toJson(userRef:Option[User], overwriteId:Option[Long],
                   templateMode:Boolean,
                   attributes:List[String] = Nil,
                   addParams:Map[String, String] = Map.empty):JValue = {

            var rv = p.basicContent(userRef, overwriteId, templateMode) ~ ("kind" -> "article") ~
                ("title" -> p.title) ~
                ("short_desc" -> p.shortDesc) ~
                ("preview_image" -> p.thumbUrl) ~
                ("tags" -> p.tags) ~
                ("last_edited_time" -> p.lastEditTime / 1000) ~
                ("rate" -> p.getRatingAverage) ~
                ("rater_count" -> p.getLikesCount) ~
                ("is_rated" -> userRef.exists(cu => p.getLikeRate(cu) > 0)) ~
                ("is_can_give_reputation" -> userRef.exists(cu => cu.userCanGiveReputation(p))) ~
                ("is_given_reputation" -> userRef.exists(cu => cu.inLastGivenReputationList(p.creator))) ~
                ("rate_from_me" -> userRef.map(cu => p.getLikeRate(cu)).getOrElse(0)) ~
                ("post_marks" -> p.mark.map { postMark =>
                    ("color" -> postMark.color) ~
                        ("title" -> postMark.title)
                }) ~
                ("embedded_objects" ->
                    ("event" -> p.getEmbeddedObjects.filter(_.isInstanceOf[Event]).collect {
                        case event:Event =>

                            ("kind" -> "event") ~
                                ("title" -> event.title) ~
                                ("content" -> event.content) ~
                                ("location" -> event.location) ~
                                ("start_date_long" -> event.startDate / 1000) ~
                                ("finish_date_long" -> event.finishDate / 1000) ~
                                ("is_maybe_attending" -> userRef.exists(cu => event.isAttenders(AttenderKind.MAYBE, cu))) ~
                                ("is_positive_attending" -> userRef.exists(cu => event.isAttenders(AttenderKind.POSITIVE, cu))) ~
                                ("is_expired" -> event.expired) ~
                                ("is_ongoing" -> event.isOnGoing) ~
                                ("attenders" ->
                                    (AttenderKind.KindIntToStr(AttenderKind.POSITIVE) -> {
                                        "count" -> event.getAttendersCount(AttenderKind.POSITIVE)
                                    }) ~
                                        (AttenderKind.KindIntToStr(AttenderKind.MAYBE) -> {
                                            "count" -> event.getAttendersCount(AttenderKind.MAYBE)
                                        }))
                    }) ~
                        ("polling" -> p.getEmbeddedObjects.filter(_.isInstanceOf[Polling]).collect {
                            case polling:Polling =>
                                ("kind" -> "polling") ~
                                    ("title" -> polling.title) ~
                                    ("description" -> polling.desc) ~
                                    ("choices" -> polling.getChoices.toList.zipWithIndex.map { case (choice, i) =>
                                        ("choice_index" -> i) ~
                                            ("choice" -> choice.title) ~
                                            ("choice_count" -> choice.chooserCount)
                                    }) ~
                                    ("has_vote" -> userRef.exists(polling.isChooser)) ~
                                    ("selected_vote_index" -> userRef.flatMap(userX => polling.getChoices.toList.zipWithIndex.filter(_._1.isChooser(userX)).map(_._2).headOption).getOrElse(-1)) ~
                                    ("total_voter" -> polling.getChooserCount) ~
                                    ("expiration_time" -> (polling.expired / 1000))
                        }) ~
                        ("file" -> p.getEmbeddedObjects.filter(_.isInstanceOf[EmbeddedFile]).collect {
                            case file:EmbeddedFile =>
                                val articleId = p.getId
                                val fileId = file.getId
                                val fileUrl = s"${p.baseUrl}/post/article/$articleId/download-attachment/$fileId?via=app"
                                ("kind" -> "file") ~
                                    ("name" -> file.name) ~
                                    ("url" -> fileUrl) ~
                                    ("md5_sum" -> file.md5) ~
                                    ("size" -> file.size) ~
                                    ("readable_size" -> file.prettySize)
                        })
                ) ~
                ( "isMonitored" -> userRef.exists(cu => p.isMonitorist(cu))) ~
                ("response_per_page" -> responsePerPage)

            p.getPostAs.foreach { ug =>
                rv = rv ~ ("post_as_group" -> {
                    val isAdmin = userRef.exists(u => u.role == UserRole.ADMIN || u.role == UserRole.SUPER_ADMIN)
                    val isMember = userRef.exists(u => ug.isMember(u))
                    val hideCreator = isAdmin || isMember
                    ug.toJson() ~
                        ("show_creator" -> hideCreator)
                })
            }


//            if (p.getEmbeddedObjects.exists {
//                case event:Event => true
//                case polling: Polling => true
//                case picture:Picture => true
//                case el:EmbeddedLink if el.kind == LinkKind.VIDEO && el.thumbnail != "" => true
//                case el:EmbeddedLink if el.thumbnail != "" => true
//                case _ => false
//            }) {
//                rv = rv ~ ("embedded_objects" -> p.getEmbeddedObjects.collect {
//                    case event:Event =>
//
//                        def attendersToJson(kind:Int):JValue = {
//                            ("count" -> event.getAttendersCount(kind)) ~
//                                ("entries" -> event.getAttenders(kind).map(UserBuilder.buildUserInfoSimple))
//                        }
//
//                        ("kind" -> "event") ~
//                            ("title" -> event.title) ~
//                            ("content" -> event.content) ~
//                            ("location" -> event.location) ~
//                            ("start_date_long" -> event.startDate) ~
//                            ("finish_date_long" -> event.finishDate) ~
//                            ("is_expired" -> event.expired) ~
//                            ("attenders" ->
//                                (AttenderKind.KindIntToStr(AttenderKind.POSITIVE) -> attendersToJson(AttenderKind.POSITIVE)) ~
//                                    (AttenderKind.KindIntToStr(AttenderKind.MAYBE) -> attendersToJson(AttenderKind.MAYBE)))
//
//                    case polling:Polling =>
//                        ("kind" -> "polling") ~
//                            ("title" -> polling.title) ~
//                            ("description" -> polling.desc) ~
//                            ("choices" -> polling.getChoices.toList.zipWithIndex.map { case (choice, i) =>
//                                ("choice_index" -> i) ~
//                                    ("choice" -> choice.title) ~
//                                    ("choice_count" -> choice.chooserCount) ~
//                                    ("choice_voter" -> choice.getChooser.toList.map { u =>
//                                        ("id" -> u.getId) ~
//                                            ("name" -> u.getName)
//                                    })
//                            }) ~
//                            ("has_vote" -> userRef.map(polling.isChooser).getOrElse(false)) ~
//                            ("selected_vote_index" -> userRef.flatMap(userX => polling.getChoices.toList.zipWithIndex.filter(_._1.isChooser(userX)).map(_._2).headOption).getOrElse(-1)) ~
//                            ("total_voter" -> polling.getChooserCount) ~
//                            ("expiration_time" -> (polling.expired) / 1000)
//                    case file:EmbeddedFile =>
//                        ("kind" -> "file") ~
//                            ("name" -> file.name) ~
//                            ("url" -> file.url) ~
//                            ("md5_sum" -> file.md5) ~
//                            ("size" -> file.size) ~
//                            ("readable_size" -> file.prettySize)
//                    case picture:Picture =>
//                        ("thumbnail_url" -> picture.smallUrl) ~
//                            ("medium" -> picture.mediumUrl) ~
//                            ("original" -> picture.largeUrl) ~
//                            ("kind" -> "embedded_picture")
//                    case el:EmbeddedLink if el.kind == LinkKind.VIDEO && el.thumbnail != "" =>
//
//                        val html = oembedHelper.getOembedData(el)
//
//                        ("kind" -> "embedded_video_link") ~
//                            ("title" -> el.title) ~
//                            ("description" -> el.desc) ~
//                            ("thumbnail_url" -> el.thumbnail) ~
//                            ("html" -> html) ~
//                            ("url" -> el.url)
//                    case el:EmbeddedLink if el.thumbnail != "" =>
//                        ("title" -> el.title) ~
//                            ("desc" -> el.desc) ~
//                            ("thumbnail_url" -> el.thumbnail) ~
//                            ("url" -> el.url) ~
//                            ("kind" -> "embedded_link")
//                    case _ =>
//                        List.empty:JObject
//                })
//            }

            rv = buildViaFor(p).map(_ ~ rv).getOrElse(rv)

            if (addParams.exists(_._1=="withContent")){
                rv = rv ~ ("content" -> formattedContent(addParams.find(_._1=="contentFormat").map(_._2).getOrElse("html")))
            }

            rv
        }

        /**
         * Mengambil post content berdasarkan format-nya
         * see [[com.ansvia.digaku.model.Article#getContentFormat(java.lang.String)]]
         * @param format
         * @return
         */
        protected def formattedContent(format:String):String = {
            val content =
                if (format=="html") {
                    val contentHtml = p.getContentFormat("html")

                    if (contentHtml.isEmpty) {
                        p.content
                    } else {
                        contentHtml
                    }
                } else {
                    p.content
                }

            TextCompiler.compileLink(content)
        }

    }
}

object PostBuilder extends PostBuilder

