/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.digaku.model
import scala.xml.{Null, UnprefixedAttribute}

/**
 * Author: robin
 *
 */
trait AppBuilder {

    abstract class AppWrapperBase(app:model.App){

        val baseUrl:String

        def normalizedPicSmall = {
            if (app.pictureSmall.trim.length == 0)
                AppBuilder.defaultSmallPicture(baseUrl)
            else
                app.pictureSmall
        }

        def normalizedPicMedium = {
            if (app.pictureMedium.trim.length == 0)
                AppBuilder.defaultMediumPicture(baseUrl)
            else
                app.pictureMedium
        }

        def normalizedPicLarge = {
            if (app.pictureLarge.trim.length == 0)
                AppBuilder.defaultLargePicture(baseUrl)
            else
                app.pictureLarge
        }

        def editUrl = baseUrl + "/apps/manager/edit/" + app.getId.toString

        def summaryUrl = baseUrl + "/apps/manager/" + app.getId.toString

        /**
         * Berbeda dengan canvasUrl
         * ini adalah untuk mem-view canvas-nya bukan url iframe ke canvas-nya.
         * @return
         */
        def canvasViewUrl = baseUrl + "/apps/" + app.getId.toString

        def listItemView(selected:Boolean=false) = {
            val rv = <li><a href={summaryUrl}><span class="icon-adn"></span> {app.name}</a></li>
            if (selected)
                rv % new UnprefixedAttribute("class","active",Null)
            else
                rv
        }


        
        
        
    }


}

object AppBuilder extends AppBuilder {

    def defaultSmallPicture(baseUrl:String) = {
        baseUrl + "/assets/img/app/app-small.png"
    }

    def defaultMediumPicture(baseUrl:String) = {
        baseUrl + "/assets/img/app/app-medium.png"
    }

    def defaultLargePicture(baseUrl:String) = {
        baseUrl + "/assets/img/app/app-large.png"
    }
}

