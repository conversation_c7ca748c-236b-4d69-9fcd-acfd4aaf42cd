///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model.jsonbuilder
//
//import net.liftweb._
//import json._
//import JsonDSL._
//import com.ansvia.digaku.model.Tips
//
///**
//* Author: fajrhf
//*
//*/
//trait TipsBuilder {
//    protected class TipsWrapper(tips:Tips) {
//        def toJson:JValue = {
//            ("id" -> tips.getId) ~
//            ("title" -> tips.title) ~
////            ("snippet" -> tips.snip) ~
//            ("content" -> tips.content)
//        }
//    }
//
//    implicit def implicitTipWrapper(tips:Tips) = new TipsWrapper(tips)
//    implicit def implicitTipsWrapper(tipsList:List[Tips]):JValue = {
//        tipsList.map(_.toJson)
//    }
//}
//
//object TipsBuilder extends TipsBuilder
