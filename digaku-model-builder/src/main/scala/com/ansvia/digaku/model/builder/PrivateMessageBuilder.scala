///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model.builder
//
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.exc.NotImplementedException
//import com.ansvia.digaku.model._
//import com.ansvia.digaku.model.builder.OriginBuilder.OriginWrapperBase
//import com.ansvia.digaku.model.builder.UserBuilder.UserWrapperBase
//import com.ansvia.digaku.utils.TextCompiler
//import com.ansvia.digaku.{Types, model}
//
//import scala.xml.{Elem, NodeSeq, Unparsed}
//
///**
// * Author: nadir, robin
// *
// */
//trait PrivateMessageBuilder extends Slf4jLogger {
//
//    import com.ansvia.digaku.utils.RichString._
//
//    abstract class MessageNodeSeqWrapperBase(pm:model.PrivateMessage) extends EmbeddedObjectBuilder {
//
//        protected implicit def userAutoCenverter(user:model.User):UserBuilder.UserWrapperBase = new UserBuilder.UnimplementedUserWrapper(user)
//        protected implicit def originAutoConvert(origin:model.Origin[GraphType]):OriginBuilder.OriginWrapperBase =
//            new OriginBuilder.UnimplementedOriginWrapper(origin)
//
//        def currentUser:Option[model.User]
//
//        val baseUrl:String
//
//        def toStreamNodeSeq(selected:Boolean=false):NodeSeq = {
//
//            val isRead = if (!pm.isParticipantHasRead(currentUser.get)) " unread" else " read"
//            val className = "pm-item-" + pm.getId.toString + " pm-stream-item" + { if (selected) " selected" else "" } + isRead
//            <div class={className}>
//                {previewContent}
//            </div>
//
//
//        }
//
//        def previewContent: Elem = {
//            <div class="pm-stream-content">
//                {pm.creator.thumbnailElmSmallNoLink}
//                <div class="media-body" >
//                    <span class="msg-subject">
//                        {pm.subject.truncate(30)}
//                    </span>
//                    <!-- p>{Unparsed(ArticleUtil.shortDesc(pm.content))}</p -->
//                    {
//                        val participantCounts = if (pm.getParticipantCount > 1) participantCount
//                        <strong>
//                            <span class={"user-name" + (if (pm.creator.isInactive) " text-inactive-user " else "")} >
//                                {pm.creator.name}{participantCounts}</span>
//                            {
//                            if (pm.creator.isInactive) {
//                                <div class="mt-icon-inactive-small" title="Inactive User"></div>
//                            }
//                            }
//                        </strong>
//                        <div class="pm-attribute">{messageAttributesNoLink}</div>
//                    }
//                </div>
//            </div>
//        }
//
//
//        def showRemoverButton:Boolean
//
//        protected def buildRemoverButton(p:model.PrivateMessage):NodeSeq
//
//        def creationAge =
//            <xml:group>
//                <span class="icon-time"></span>
//                <cite>{pm.getCreationAge}</cite>
//            </xml:group>
//
//        def replyCount =
//            <xml:group>
//                {pm.getResponseCountByUser(currentUser.get)} <span class="icon-reply"></span>
//            </xml:group>
//
//        def participantCount =
//            <xml:group>
//                , +{pm.getParticipantCount}
//            </xml:group>
//
//        def messageAttributes(embedMode:Boolean) = {
//            <span class="post-attributes">
//                {creationAge}
//                -
//                <span><a href={url(embedMode)} data-embed-url={url(true)}>{replyCount}</a></span>
//            </span>
//        }
//
//        def messageAttributesNoLink = {
//            <span class="post-attributes">
//                {creationAge}
//            </span>
//        }
//
//        def titleUrl:String = {
//            val rv = pm.content.urlize + "-" + pm.getId
//            rv.toLowerCase
//        }
//
//        def url(embedMode:Boolean) = {
//            if (embedMode)
//                "%s/messages/%s?embed=1".format(baseUrl, pm.getId)
//            else
//                "%s/messages/%s".format(baseUrl, pm.getId)
//        }
//
//        def snipLink(embedMode:Boolean):NodeSeq = {
//            <a href={url(embedMode)} data-embed-url={url(true)}>{pm.content.truncate(20)}</a>
//        }
//
//    }
//
//    abstract class ResponseNodeSeqWrapperBase(messageResp:model.MessageResponse) {
//
//        protected implicit def userAutoCenverter(user:model.User):UserBuilder.UserWrapperBase
//        protected implicit def privateConverter(p:model.PrivateMessage):PrivateMessageBuilder.MessageNodeSeqWrapperBase
//        protected implicit def originAutoConvert(origin:model.Origin[GraphType]):OriginBuilder.OriginWrapperBase
//
//        def isLoggedIn:Boolean
//        def currentUser:Option[model.User]
//
//        def responseRemoverButton:NodeSeq
//
//        def canQuoteResponse:Boolean
//
//        def quoter:NodeSeq = {
//            if (isLoggedIn && canQuoteResponse) {
//                messageResp.getPrivateMessageObject.map { message =>
//                    <button class="btn btn-small btn-quoter icon-quote-right pull-right" rel="tooltip" data-click-tooltip="false" data-toggle="tooltip" title="Quote" data-placement="bottom"
//                            user-name={messageResp.creator.name} text-data={messageResp.getId.toString}></button>
//                        :NodeSeq
//                }.getOrElse(NodeSeq.Empty)
//            } else
//                NodeSeq.Empty
//        }
//
//        def toNodeSeq:NodeSeq = {
//
//            val id = messageResp.getId.toString
//            val leaveItemClass = if (messageResp.responseKind == model.MessageResponseKind.LEAVE) " response-leave-item" else ""
//            val respItemClass = "response-item response-item-" + id + leaveItemClass
//
//            <div id={"Response-" + id} class={respItemClass}>
//                <div class="clearfix">
//                    {
//                        if (messageResp.responseKind == model.MessageResponseKind.LEAVE) {
//                            <div class="response-leave" id={"ResponseText-" + id}>
//                                <label>{messageResp.creator.name}</label> left the conversation
//                                <div class="reason">"{messageResp.leaveReason}"</div>
//                            </div>
//                        } else {
//                            <div class="response-item-inner">
//                                {messageResp.creator.thumbnailElmSmall}
//                                <div style="float: left; margin-right: 10px">
//                                    <strong class={(if (messageResp.creator.isInactive) "text-inactive-user " else "" )}>
//                                        <a data-hc-kind="user" class="user-name" href={messageResp.creator.url}>{messageResp.creator.name}</a>
//                                    </strong>
//                                    {
//                                    if (messageResp.creator.isInactive) {
//                                        <div class="mt-icon-inactive-small" title="Inactive User"></div>
//                                    }
//                                    }
//                                </div>
//                                {attributes}
//                                <div class="response-text" id={"ResponseText-" + id}>
//                                    {Unparsed(TextCompiler.compileMessage(messageResp.content))}
//                                </div>
//                                {
//                                currentUser.filter(_ == messageResp.creator)
//                                    .map( u => responseRemoverButton ).getOrElse(Nil)
//                                }
//                                {quoter}
//                            </div>
//                        }
//                    }
//                </div>
//            </div>
//        }
//
//
//        def attributes = {
//            <span class="post-attributes">
//                <span class="icon-time"></span>
//                {messageResp.getCreationAge}
//            </span>
//        }
//
//        def snipLink(embedMode:Boolean):NodeSeq = {
//            messageResp.getPrivateMessageObject.map{ pm =>
//                <a href={pm.url(embedMode)} data-embed-url={pm.url(true)}>{messageResp.content.truncate(20)}</a>
//            }.getOrElse(NodeSeq.Empty)
//        }
//    }
//
//}
//
//object PrivateMessageBuilder extends PrivateMessageBuilder {
//    class UnimplementedPrivateMessageNodeSeqWrapper(pm:model.PrivateMessage) extends MessageNodeSeqWrapperBase(pm) {
//
//
//        protected lazy val hasEmbeddedObject: HasEmbeddedObject[Types.IDType] = pm.asInstanceOf[model.PrivateMessage]
//
//        protected def safeUrl(url:String):String = "xxx"
//
//        val baseUrl: String = "base-url-not-set"
//
//        def currentUser: Option[User] = {
//            throw NotImplementedException("currentUser belum di set, mohon override fungsi ini " +
//                "dengan implementasi mu sendiri.")
//        }
//
//        def showRemoverButton: Boolean = {
//            throw NotImplementedException("showRemoverButton belum di set, mohon override fungsi ini " +
//                "dengan implementasi mu sendiri.")
//        }
//
//        protected def buildRemoverButton(p: PrivateMessage): NodeSeq = {
//            throw NotImplementedException("buildRemoverButton belum di set, mohon override fungsi ini " +
//                "dengan implementasi mu sendiri.")
//        }
//    }
//
//    class UnimplementedResponseNodeSeqWrapper(mr:model.MessageResponse) extends ResponseNodeSeqWrapperBase(mr) {
//        protected implicit def userAutoCenverter(user: User): UserWrapperBase = new UserBuilder.UnimplementedUserWrapper(user)
//        protected implicit def privateConverter(pm: PrivateMessage): MessageNodeSeqWrapperBase = new PrivateMessageBuilder.UnimplementedPrivateMessageNodeSeqWrapper(pm)
//        protected implicit def originAutoConvert(origin: Origin[GraphType]): OriginWrapperBase =
//            new OriginBuilder.UnimplementedOriginWrapper(origin)
//
//        def isLoggedIn: Boolean = throw NotImplementedException("isLoggedIn belum di set, mohon override fungsi ini " +
//            "dengan implementasi mu sendiri.")
//
//        def currentUser: Option[User] = throw NotImplementedException("currentUser belum di set, mohon override fungsi ini " +
//            "dengan implementasi mu sendiri.")
//
//        def responseRemoverButton: NodeSeq = throw NotImplementedException("responseRemoverButton belum di set, mohon override fungsi ini " +
//            "dengan implementasi mu sendiri.")
//
//        def canQuoteResponse: Boolean = throw NotImplementedException("responseRemoverButton belum di set, mohon override fungsi ini " +
//            "dengan implementasi mu sendiri.")
//    }
//
//}
