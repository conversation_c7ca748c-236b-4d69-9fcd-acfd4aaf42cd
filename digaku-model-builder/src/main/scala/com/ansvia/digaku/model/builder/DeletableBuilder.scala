/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.digaku.model
import model.User
import xml.NodeSeq
import com.ansvia.digaku.exc.NotImplementedException

/**
 * Author: nadir
 * Date: 10/2/13
 * Time: 10:38 AM
 * 
 */
trait ContentDeletableBuilder {
    abstract class ContentDeletableNodeSeqWrapperBase(p:model.Deletable){
        def currentUser:Option[model.User]

        def deleteAsCreatorButton(removedClass:String=""):NodeSeq

        def deleteAsAdminButton(removedClass:String=""):NodeSeq

        def deleteAsAdminChannelButton(removedClass:String=""):NodeSeq

    }


}

object ContentDeletableBuilder extends ContentDeletableBuilder {
    class UnimplementedContentDeletableNodeSeqWrapper(p:model.Deletable) extends ContentDeletableNodeSeqWrapperBase(p) {
        def currentUser: Option[User] = None

        def deleteAsCreatorButton(removedClass: String): NodeSeq = {
            throw NotImplementedException("deleteAsCreatorButton belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        def deleteAsAdminButton(removedClass: String): NodeSeq = {
            throw NotImplementedException("deleteAsAdminButton belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        def deleteAsAdminChannelButton(removedClass: String): NodeSeq = {
            throw NotImplementedException("deleteAsAdminChannelButton belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }
    }
}
