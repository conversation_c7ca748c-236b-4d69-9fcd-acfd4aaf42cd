/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.digaku.model
import xml.NodeSeq
import com.ansvia.digaku.Types._

/**
* Author: nadir
* Date: 12/11/13
* Time: 6:10 PM
*
*/
trait TrophyBuilder {
    abstract class TrophyWrapperBase(trophy:model.Trophy){

        /**
         * Http protocol yang digunakan oleh phitos atau aws
         */
        val awsHttpProtocol:String

        /**
         * Rewrite http schema pada url menggunakan value dari [[awsHttpProtocol]]
         */
        private def fixS3HttpProtocol(url:String):String = {
            url.replace("http://", awsHttpProtocol + "://")
                .replace("https://", awsHttpProtocol + "://")
        }

        def iconElmSmall:NodeSeq =
                <div class="trophy small left" style={"background: url(\"" + fixS3HttpProtocol(trophy.icons) + "\");"} ></div>

        def iconElmLarge:NodeSeq =
            <div class="trophy large left" style={"background: url(\"" + fixS3HttpProtocol(trophy.icons) + "\");"} ></div>
    }
}

object TrophyBuilder extends TrophyBuilder {
    class UnimplementedUserWrapper(trophy:model.Trophy) extends TrophyWrapperBase(trophy){

        /**
         * Http protocol yang digunakan oleh phitos atau aws
         */
        override val awsHttpProtocol: String = "http"
    }

}