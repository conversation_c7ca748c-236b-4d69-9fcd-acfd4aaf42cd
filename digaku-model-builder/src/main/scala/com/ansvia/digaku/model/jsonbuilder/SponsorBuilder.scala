///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model.jsonbuilder
//
//import net.liftweb._
//import json._
//import JsonDSL._
//import com.ansvia.digaku.model.{User, SponsorPost}
////import com.ansvia.digaku.restapi.SimpleTime
//
///**
// * Author: robin
// *
// */
//trait SponsorBuilder {
//
//    protected class SponsorWrapper(sp:SponsorPost) {
//        def toSimpleJson(userRef:Option[User]=None):JValue = {
//            ("id" -> sp.getId) ~
//                ("kind" -> "sponsor") ~
//                ("title" -> sp.name) ~
//                ("content" ->
//                    ("small" -> sp.bannerPicture) ~
//                    ("medium" -> sp.bannerPicture) ~
//                    ("large" -> sp.bannerPicture)
//                    ) ~
//                ("creation_time" -> (sp.creationTime / 1000)) ~
//                ("view_count" -> sp.viewsCount)
//        }
//
//    }
//
//    implicit def implicitSponsorsWrapper(sp:SponsorPost) = new SponsorWrapper(sp)
//    implicit def implicitSponsorsWrapper(sps:Seq[SponsorPost]):JValue = {
//        sps.map(_.toSimpleJson(None))
//    }
//}
//
//object SponsorBuilder extends SponsorBuilder
