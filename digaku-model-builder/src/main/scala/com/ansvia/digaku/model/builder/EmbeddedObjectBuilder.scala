/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.digaku.model.{LinkKind, HasEmbeddedObject}
import com.ansvia.digaku.Types.IDType
import scala.xml.NodeSeq
import com.ansvia.digaku.model


/**
 * Author: robin
 *
 * Helper untuk builder agar bisa
 * menggunakan builder untuk embedded object.
 */
trait EmbeddedObjectBuilder {

    protected val hasEmbeddedObject:HasEmbeddedObject[IDType]

    def embeddedObject:NodeSeq = {
        hasEmbeddedObject match {
            case he:HasEmbeddedObject[IDType] =>
                he.getEmbeddedObjects.headOption.map {
                    case el:model.EmbeddedLink if el.kind == LinkKind.PIC =>
                        <div class="embedded-object">
                            <img src={el.url} />
                        </div>
                    case el:model.EmbeddedLink =>
                        val safedUrl = safeUrl(el.url)
                        val noFollow = if (!isLocalUrl(el.url)) "nofollow" else ""
                        <div class="embedded-object">
                            <table border="0">
                                <tr>
                                    <td>
                                        <div class="thumbnail">
                                            <a href={safedUrl} rel={noFollow} target="_blank">
                                                <img src={el.normalizedThumbnail}/>
                                            </a>
                                        </div>
                                    </td>
                                    <td style="padding-left: 10px;">
                                        <a href={safedUrl} rel={noFollow} target="_blank">
                                            <strong>
                                                {el.title}
                                            </strong>
                                        </a>
                                        <a href={safedUrl} rel={noFollow} target="_blank">
                                            <p class="desc">
                                                {el.desc}
                                            </p>
                                        </a>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    case _ =>
                        NodeSeq.Empty
                }.getOrElse(NodeSeq.Empty)
            case _ =>
                NodeSeq.Empty
        }
    }

    /**
     * Mendapatkan title link dari embedded object
     * link ke tab baru
     * @return
     */
    def embeddedObjectTitle:NodeSeq = {
        hasEmbeddedObject match {
            case he:HasEmbeddedObject[IDType] =>
                he.getEmbeddedObjects.headOption.map {
                    case el:model.EmbeddedLink if el.kind == LinkKind.PIC =>
                        val noFollow = if (!isLocalUrl(el.url)) "nofollow" else ""
                        <a href={el.url} rel={noFollow} target="_blank">
                            {el.title}
                        </a>
                    case el:model.EmbeddedLink =>
                        val safedUrl = safeUrl(el.url)
                        val noFollow = if (!isLocalUrl(el.url)) "nofollow" else ""
                        <a href={safedUrl} rel={noFollow} target="_blank">
                            {el.title}
                        </a>
                    case _ =>
                        NodeSeq.Empty
                }.getOrElse(NodeSeq.Empty)
            case _ =>
                NodeSeq.Empty
        }
    }

    protected def safeUrl(url:String):String

    protected def isLocalUrl(url:String):Boolean = false
}
