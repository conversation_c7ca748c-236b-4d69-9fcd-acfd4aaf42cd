/*
 * Copyright (c) 2013-2016 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.jsonbuilder

import com.ansvia.digaku.model.builder.Normalizer
import com.ansvia.digaku.model.{User, Trophy}
import net.liftweb._
import json._
import JsonDSL._

/**
 * Author: fajr (<EMAIL>)
 */
trait TrophyBuilder {

    implicit def implicitTrophyToJValue(trophy:Trophy)(implicit user:User):JObject = {
        ("name" -> trophy.getName) ~
            ("unlocked" -> trophy.userHasTrophy(user)) ~
            ("trophy_icons" -> Normalizer.normalizeUrl(trophy.icons))
    }
    implicit def implicitTrophiesToJValue(trophies:Iterator[Trophy])(implicit user:User):JValue = {
        trophies.toSeq.map(trophy => trophy:JValue)
    }
}

object TrophyBuilder extends TrophyBuilder


