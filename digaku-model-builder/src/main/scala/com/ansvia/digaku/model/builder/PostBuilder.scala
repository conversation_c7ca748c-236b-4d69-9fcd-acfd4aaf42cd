/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import xml.{Unparsed, NodeSeq}
import com.ansvia.digaku.{Digaku, Types, model}
import com.ansvia.digaku.model._
import builder.ContentDeletableBuilder.ContentDeletableNodeSeqWrapperBase
import builder.ContentMovableBuilder.ContentMovableNodeSeqWrapperBase
import com.ansvia.digaku.utils.{ArticleUtil, TextCompiler}
import com.ansvia.digaku.Types._
import scala.xml.Text
import com.ansvia.digaku.exc.NotImplementedException
import scala.collection.JavaConversions._
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.digaku.helpers.GremlinHelpers._
import net.liftweb.util.Helpers._

/**
 * Author: robin
 *
 */
trait PostBuilder {

    import com.ansvia.digaku.utils.RichString._
    import PostKind._
    import com.ansvia.digaku.utils.UserSettings._

    sealed case class TagableWrapper(p:Tagable){
        def tagsNodeSeq:NodeSeq = {

            p.tagsArray.map(tag =>
                if ( !tag.isEmpty ) {
                    <span>
                        <a href={"/search/result/tag/" + urlEncode(tag.toLowerCase)}>{tag}</a>
                    </span>:NodeSeq
                } else
                    NodeSeq.Empty
                ).toSeq.reduceOption(_ ++ _).getOrElse(Nil)

        }

    }

    implicit def implicitTagable(p:Tagable) = TagableWrapper(p)

    abstract class PostNodeSeqWrapperBase(p:model.Post) extends StreamView with EmbeddedObjectBuilder {

        import com.ansvia.digaku.model.Label.POST_VIA

        // tidak pake with DbAccess karena ini menghindari ambiguous ketika import
        // karena with DbAccess pakenya bukan private tapi protected.
        implicit private def db:GraphType = Digaku.engine.database.getRaw

        protected implicit def userAutoCenverter(user:model.User):UserBuilder.UserWrapperBase = new UserBuilder.UnimplementedUserWrapper(user)
        protected implicit def originAutoConvert(origin:model.Origin[GraphType]):OriginBuilder.OriginWrapperBase =
            new OriginBuilder.UnimplementedOriginWrapper(origin)
        protected implicit def pollAutoConverter(poll:model.Polling):PollBuilder.PollWrapperBase
        protected implicit def appAutoConverter(app:model.App):AppBuilder.AppWrapperBase
        protected implicit def contentDeletableAutoConverter(deletable:model.Deletable):ContentDeletableBuilder.ContentDeletableNodeSeqWrapperBase
        protected implicit def contentMovableAutoConverter(movable:model.Movable):ContentMovableBuilder.ContentMovableNodeSeqWrapperBase

        val httpProtocol:String
        val baseUrl:String

        def currentUser:Option[model.User]

        def isLoggedIn:Boolean

        def snipLink(embedMode:Boolean):NodeSeq = {
            p.kind match {
                case SIMPLE_POST =>
                    <a href={url(embedMode)}>{p.getContent.truncate(20)}</a>
                case ARTICLE =>
                    <a href={url(embedMode)}>{getTitle(20)}</a>
                case QUESTION =>
                    <a href={url(embedMode)}>{getTitle(20)}</a>
                case DEAL =>
                    <a href={url(embedMode)}>{getTitle(20)}</a>
            }
        }

        def snipLinkNoSign(embedMode:Boolean):NodeSeq = {
            p.kind match {
                case SIMPLE_POST =>
                    <a href={url(embedMode)} onclick={"dg.Util.showSinglePost(this, event, " + p.getId + ");"}>post</a>
                case ARTICLE =>
                    <a href={url(embedMode)} onclick={"dg.Util.showSinglePost(this, event, " + p.getId + ");"}>post</a>
                case QUESTION =>
                    <a href={url(embedMode)} onclick={"dg.Util.showSinglePost(this, event, " + p.getId + ");"}>post</a>
                case DEAL =>
                    <a href={url(embedMode)} onclick={"dg.Util.showSinglePost(this, event, " + p.getId + ");"}>post</a>
            }
        }
//
        def likeClick:NodeSeq

        def creatorNs:NodeSeq = {
            <div class="clearfix p-creator">
                {p.creator.thumbnailElmSmall}
                <strong><a href={p.creator.url}>{p.creator.getName}</a></strong>
                {
                if(p.creator.isInactive)
                    <div class="icon-eye-close" title="User is inactive"></div>
                else {
                    p.origin match {
                        case ch:Forum =>
                            <span> from {ch.snipLink}</span>
                        case _ =>
                            NodeSeq.Empty
                    }
                }
                }
            </div>:NodeSeq
        }

        protected def safeUrl(url:String):String

        /**
         * Untuk build tampilan shout di post.
         * @param streamInfo meta info untuk object stream.
         * @return
         */
        def shoutInfo(streamInfo:model.StreamObject) = {
            import com.ansvia.digaku.utils.RichDate._
            if (streamInfo != null) {
                streamInfo match {
                    case si:model.ShoutStreamObject =>
                        <div class="shout word-preview">
                            <span class="icon-retweet"></span>
                            {si.shouter.snipLinkNoSign}: {si.message}
                            <small title={si.time.toStdFormatWithTime}> - {si.time.toRelative()}</small>
                        </div>
                    case _ => NodeSeq.Empty
                }
            }else
                NodeSeq.Empty
        }


//        /**
//         * Untuk build tampilan retalk di post.
//         * @param streamInfo meta info untuk object stream.
//         * @return
//         */
//        def retalkLable(streamInfo:model.StreamObject) = {
//                // lable untuk retalk
//                if (streamInfo != null) {
//                    streamInfo match {
//                        case si:model.RetalkStreamObject =>
//                            <span class="label label-default">retalked</span>
//                        case _ => NodeSeq.Empty
//                    }
//                }else
//                    NodeSeq.Empty
//        }

        protected def buildPostMark(p:Post, mark:PostMark) = {
            <span class="label post-mark-label" style={"background-color: " + mark.color + ";"}
                  onclick={"window.location.href='" + p.origin.url + "/post/mark/" + mark.title.toLowerCase  + "';"}>
                {mark.title}
            </span>
        }


        def shortDesc:String = ArticleUtil.shortDesc(p.getContent)


        def participants:NodeSeq = {

            // MENAMPILKAN POST PARTICIPANTS (responder)

            if (p.getResponseCount > 0){
                <lift:lazy-load template="_chunk_silent_loading">
                    <div class="lift:PostSnippet.participants"></div>
                </lift:lazy-load>
            }else
                NodeSeq.Empty

        }

        def toSingleViewNodeSeq(embedMode:Boolean, settingMenus:NodeSeq, adminMenus:NodeSeq):NodeSeq

        def attachedFiles(at:model.FileAttachable):NodeSeq

        def showRemoverButton:Boolean

        protected def buildRemoverButton(p:model.Post):NodeSeq
//        protected def buildShoutButton():NodeSeq
//        protected def buildRetalkButton():NodeSeq
        protected def buildReportButton():NodeSeq
//        protected def buildShareButton(embedMode: Boolean):NodeSeq
        protected def buildLockPostButton(embedMode: Boolean):NodeSeq
        protected def buildClosePostButton(embedMode: Boolean):NodeSeq

        protected def removerButton:NodeSeq = {

            if(showRemoverButton){
                currentUser.map { cu =>
                    if(cu == p.creator || cu.getAbilities.contains(model.Ability.DELETE_POST)){
                        buildRemoverButton(p)
                    }else
                        NodeSeq.Empty
                }.getOrElse(NodeSeq.Empty)
            }
            else
                NodeSeq.Empty

        }

        def choicerAction:NodeSeq = {
            currentUser.map { cu =>

                p match {
                    case article:model.Article =>
                        article.getPolls.headOption.map{poll =>
                            if(poll.canChoice_?(cu)){
                                poll.reload().choicerActionNodeSeq
                            }else NodeSeq.Empty
                        }.getOrElse(NodeSeq.Empty)
                }

            }.getOrElse(NodeSeq.Empty)
        }

        def creationAge =
            <span class="p-attr time">
                <span class="icon-time"></span>
                <cite>{p.getCreationAge}</cite>
            </span>

        def responseCount = {
            <span class="count">
                {p.getResponseCount}
            </span>
            <span class="icon icon-comments" data-toggle="tooltip" title="Response" data-placement="bottom" rel="tooltip" ></span>
        }

        def viewsCount = {
            <span>
                {
                    if (p.viewsCount > 0)
                        <span rel="tooltip"  data-toggle="tooltip" title="Viewed" data-placement="bottom" style="color: #F09432;"> {p.viewsCount} <span class="icon-eye-open"></span></span>
                    else {
                        <span rel="tooltip"  data-toggle="tooltip" title="Viewed" data-placement="bottom"> {p.viewsCount} <span class="icon-eye-open"></span></span>
                    }
                }
            </span>
        }

        def postLikers:NodeSeq

//        @deprecated("gak akan dipakai lagi, gunakan inline", "20 jan 2014")
//        def postAttributes(embedMode:Boolean, withShareButton:Boolean=true) = {
//
//            val classLikeCount = "like-count-" + p.reload().getId
//            <div class="post-attributes">
//                {creationAge}
//                {
//                via map { app =>
//                    <span class="p-attr">via <img src={app.normalizedPicSmall} title={app.name} alt={app.name} /> </span>
//                } getOrElse NodeSeq.Empty
//                }
//                <span class="p-attr"><span class={classLikeCount}>{postLikers}</span> <span>{likeClick}</span></span>
//                <span  class="p-attr"><a href={url(embedMode)}>{responseCount}</a></span>
//                <span class="p-attr" title={"This post has been viewed " + p.viewsCount + " times"}>{viewsCount}</span>
//                {
//                if (p.originIsPrivate || !withShareButton)
//                    NodeSeq.Empty
//                else
//                    buildShareButton(embedMode)
//                }
//            </div>
//        }

        def via = {
            gremlin(p.getVertex).out(POST_VIA).headOption.flatMap(_.toCC[model.App])
        }

//        def postAttributesNoLink(withSharedButton:Boolean = true, mobile:Boolean = false) = {
//            val classLikeCount = "like-count-" + p.reload().getId
//            val featured = {
//                if (p.isPromoted)
//                    <span rel="tooltip" data-toggle="tooltip" title="Featured" data-placement="bottom" class="featured-icon mt-icon-medal"></span>
//                else
//                    NodeSeq.Empty
//            }
//
//            <div class="post-attributes clearfix">
//                {
//                if (mobile) {
//                    <div class="pull-left">
//                        {creationAge}
//                    </div>
//                    <div class="pull-right">
//                        <span class="p-attr views" title={"This post has been viewed " + p.viewsCount + " times"}>{viewsCount}</span>
//                        <span class="p-attr responses">{responseCount}</span>
//                        <span class="p-attr stars"><span class={classLikeCount + " count"} >{postLikers}</span></span>
//                        {featured}
//                    </div>
//                } else {
//                    <div class="pull-left share-menu">
//                        {
//                        if (p.originIsPrivate || !withSharedButton)
//                            NodeSeq.Empty
//                        else
//                            buildShareButton(true)
//                        }
//                    </div>
//
//                    <div class="pull-right">
//                        {creationAge}
//                        <span class="p-attr views" title={"This post has been viewed " + p.viewsCount + " times"}>{viewsCount}</span>
//                        <span class="p-attr responses">{responseCount}</span>
//                        <span class="p-attr stars"><span class={classLikeCount + " count"} >{postLikers} </span></span>
//                        {featured}
//                    </div>
//                }
//                }
//            </div>
//        }

        def url(embedMode:Boolean, addParams:(String, String)*):String = {

            val url =
                if(p.origin!=null){
                    "/post/%s".format(p.getId)
                }else{
                    if (p.creator!=null)
                        baseUrl + "/u/%s/post/%s.html".format(p.creator.getName.toLowerCase,titleUrl)
                    else
                        baseUrl + "/null/post/%s.html".format(titleUrl)
                }

//            if (embedMode){
//                url + "?embed=1"
//            }else{
//                url
//            }

            val addParamsEncoded = addParams.map {case (k,v) => s"$k=$v" }.mkString("&")

            if (embedMode){
                url + "?embed=1" + (if (addParams.nonEmpty) "&" else "") + addParamsEncoded
            }else{
                url + (if (addParams.nonEmpty) "?" else "") + addParamsEncoded
            }
        }


        /**
         * Cek apakah current user termasuk dalam :
         * - admin/super admin system atau
         * - owner/staff(punya abilty delete) group
         * @return
         */
        def cuIsAdmin = {
            currentUser.exists { cu =>
                val admin = cu.role == model.UserRole.ADMIN || cu.role == model.UserRole.SUPER_ADMIN
                val adminChannel = {
                    model.Forum.getById(p.origin.getId).exists { ch =>
                        ch.getStaffAbility(cu).contains(Ability.DELETE_POST) || ch.isOwner(cu)
                    }
                }

                admin || adminChannel

            }
        }

        /**
         * Cek apakah current user adalah :
         * admin/super admin system
         * @return
         */
        def cuIsSuperAdmin = {
            currentUser.exists(cu =>
                cu.role == model.UserRole.ADMIN ||
                    cu.role == model.UserRole.SUPER_ADMIN)
        }

        /**
         * Cek apakah current user merupakan :
         * owner/staff(punya abilty delete) group
         * @return
         */
        def cuIsAdminChannel = {
            currentUser.exists ( cu =>
                model.Forum.getById(p.origin.getId).exists { ch =>
                    ch.getStaffAbility(cu).contains(Ability.DELETE_POST) || ch.isOwner(cu)
                }
            )
        }



        def  deletedPreviewNodeSeq: NodeSeq = {
            if (cuIsAdmin) {

                p.kind match {
                    case SIMPLE_POST =>
//                        <div class="post-box col-md-9 clearfix">
                            <div class="clearfix single-post-view simple-post alert alert-warning" style="margin-bottom: 0;">
                                <div class="clearfix">
                                    {p.creator.thumbnailElmSmall}
                                    <strong><a href={p.creator.url}>{p.creator.getName}</a></strong>
                                </div>

                                <div class="post-content no-embed">
                                    {Unparsed(TextCompiler.compileMessage(p.getContent))}
                                </div>
                                {embeddedObject}
                                {/*postAttributesNoLink()*/}

                                <!--<div>{Unparsed(TextCompiler.compileMessage(p.getContent))}</div>
                                {embeddedObject}
                                {postAttributesNoLink()} -->
                                <!-- {participants} -->
                            </div>
//                        </div>
//                        <div class="col-md-3 clearfix">
//                            <div>
//                                {p.creator.thumbnailElmSmall}
//                                <strong><a href={p.creator.url}>{p.creator.name}</a></strong>
//                            </div>
//                        </div>

                    case ARTICLE =>
                        p.getVertex.toCC[model.Article] map { article =>
                            <div class="single-post-view article alert-warning" style="margin-bottom: 0;">
                                <div class="clearfix">
                                    <h1>{if (article.hasPolling) <span class="icon-bar-chart" style="color: #AB4CE6;"></span> else NodeSeq.Empty} {article.title}</h1>

                                    <div class="clearfix">
                                        {article.creator.thumbnailElmSmall}
                                        {
                                        article.origin match {
                                            case ch:model.Forum =>
                                                if(ch.isOwner(article.creator))
                                                    <strong><a href={article.creator.url} style="color: #cF0809;">
                                                        {article.creator.getName}</a></strong>
                                                else if(ch.isStaff(article.creator))
                                                    <strong><a href={article.creator.url} style="color: #8E1AED;">
                                                        {article.creator.getName}</a></strong>
                                                        //<em class="post-attributes" style="margin-left: 10px;">{ch.getStaffAttribute(article.creator).title}</em>
                                                else
                                                    <strong><a href={article.creator.url}>{article.creator.getName}</a></strong>
                                            case _ =>
                                                <strong><a href={article.creator.url}>{article.creator.getName}</a></strong>
                                        }
                                        } <span>from {p.origin.snipLink}</span>
                                    </div>

                                    <div class="article-content">{Unparsed(TextCompiler.compileMessage(article.getContent))}</div>

                                    {if(article.hasPolling){

                                        <hr />

                                        <div data-lift="Poll.ifSupportPolling?show-error=true">
                                            <div class="clearfix">
                                                <div id="ChartPlaceholder"></div>
                                                <a class="pull-right" href={article.getPolls
                                                    .headOption.map(_.url).getOrElse("/")}><span class="icon-search"></span> show detail</a>
                                                <div id="ChoicerAction">{choicerAction}</div>
                                            </div>
                                        </div>

                                            <hr />
                                } else
                                    NodeSeq.Empty
                                    }

                                    <div>{article.tagsNodeSeq}</div>
                                    {/*postAttributesNoLink()*/}

                                </div>

                                {
                                if (article.lastEditTime > 0){
                                    <div class="post-attributes" style="margin-top: 15px;">
                                        <em>

                                            {
//                                            if(article.revision > 0){
//                                                <span>revision {article.revision}</span><br />
//                                            }else NodeSeq.Empty
                                            }

                                            {

                                            (article.lastEditor map { ue =>

                                                Text("last edited by ") ++ ue.snipLinkNoSign

                                            } getOrElse Text("last edited ")) ++ Text(" at " + article.getLastEditTimeStd +
                                                " (" + article.getLastEditTimeAge + ")") ++
                                                {
                                                    if (article.editReason.trim.length > 1) Text(" reason: " + article.editReason) else NodeSeq.Empty
                                                }


                                            }

                                        </em>
                                    </div>
                                } else
                                    NodeSeq.Empty
                                }
                                {attachedFiles(article)}
                            </div>
//                            ++
//                            participants
                        } getOrElse
                            NodeSeq.Empty
                }
            } else
                NodeSeq.Empty
        }


        def relativeUrl = {

            if(p.origin!=null){
                "%s/post/%s.html".format(p.origin.relativeUrl, titleUrl)
            }else{
                if (p.creator!=null)
                    "u/%s/post/%s.html".format(p.creator.getName.toLowerCase,titleUrl)
                else
                    "null/post/%s.html".format(titleUrl)
            }
        }


        /**
         * Ini title yang di-url-kan, bukan URL itu sendiri
         * jangan gunakan ini untuk dapetin url.
         * @return
         */
        def titleUrl:String = {
            val rv = p match {
                case art:model.Article =>
                    art.title.urlize + "-" + art.getId
//                case simple:model.SimplePost =>
//                    simple.getContent.urlize + "-" + simple.getId
//                case question:model.Question =>
//                    question.title.urlize + "-" + question.getId
//                case deal:model.Deal =>
//                    deal.name.urlize + "-" + deal.getId
            }
            rv.toLowerCase
        }

        /**
         * Get normalized title.
         * @param truncate truncated by.
         * @return
         */
        def getTitle(truncate:Int=0):String = {
            val rv = p match {
                case art:model.Article =>
                    if (truncate > 0)
                        art.title.truncate(truncate)
                    else
                        art.title

//                case simple:model.SimplePost =>
//
//                    /**
//                     * Penanganan untuk simple post agak beda
//                     * apabila ada embedded object link
//                     * maka title-nya akan diambil dari title link
//                     * tersebut.
//                     */
//
//                    val eos = simple.getEmbeddedObjects
//
//                    val rv =
//                        if (eos.length > 0){
//                            eos(0) match {
//                                case el:EmbeddedLink => {
//                                    if (el.title.trim.length > 2)
//                                        el.title
//                                    else
//                                        simple.getContent
//                                }
//                                case _ =>
//                                    simple.getContent
//                            }
//                        }else {
//                            simple.getContent
//                        }
//
//                    if (truncate > 0)
//                        rv.truncate(truncate)
//                    else
//                        rv
//
//                case question:model.Question =>
//                    question.title.truncate(truncate)
//
//                case deal:model.Deal =>
//                    deal.name.truncate(truncate)
            }
            rv
        }

        /**
         * @deprecated since we don't know the base domain yet.
         * @return
         */
        def absoluteUrl(embedMode:Boolean):String = {
            url(embedMode)
        }

        def editUrl(embedMode:Boolean) = {
            var rv = if (p.origin != null) {
                "%s/post/%s/edit".format(p.origin.url, p.getId)
            } else {
                if (p.creator != null)
                    "/u/%s/post/%s/edit".format(p.creator.getName.toLowerCase, p.getId)
                else
                    "/null/post/%s/edit".format(titleUrl)
            }
            if (embedMode)
                rv = rv + "?embed=1"
            rv
        }

        // @TODO(robin): add to unittest
        /**
         * access control untuk ngecek apakah user yang bersangkutan
         * bisa edit post ini ato gak.
         * @param user target user.
         * @return
         */
        def canEdit(user:model.User):Boolean = {
            p match {
                case art:model.Article =>
                    if (art.editable){  // pastikan post editable
                        checkUserAccess(user)  || staffCanEditPost(user)
                    }else
                        false
                case _ =>
                    checkUserAccess(user) || staffCanEditPost(user)
            }
        }

        import com.ansvia.digaku.model.Ability._

        def staffCanEditPost(user:User) = {
            p.origin match {
                case ch:Forum =>
                    ch.hasAbility(user, EDIT_POST)

                case _ =>
                    false
            }
        }

        private def checkUserAccess(user:model.User) = {
            if (user.role == UserRole.SUPER_ADMIN)
                true
            else
                p.reload().userCanEdit(user)
        }

        // @TODO(ubai): add to unittest
        /**
         * Make user can edit this post.
         * @param user target user.
         */
        def makeCanEdit(user:model.User){
            p.reload().mekeUserCanEdit(user)
        }

        /**
         * untuk mendapatkan daftar user
         * yang bisa nge-dit post ini.
         * @return
         */
        def userCanEditList:Seq[model.User] = p.reload().userCanEditList

        def collabUrl = {
            if(p.origin!=null){
                "%s/post/%s/collab".format(p.origin.url, p.getId)
            }else{
                if (p.creator!=null)
                    "/u/%s/post/%s/collab".format(p.creator.getName.toLowerCase, p.getId)
                else
                    "/null/post/%s/collab".format(titleUrl)
            }
        }

    }



}

object PostBuilder extends PostBuilder {


    class UnimplementedPostNodeSeqWrapper(p:model.Post) extends PostNodeSeqWrapperBase(p) {


        val httpProtocol = "http"
        val baseUrl = "base-url-not-set"

        def currentUser = None

        protected def safeUrl(url:String) = url

        protected def buildRemoverButton(p: model.Post) = {
            throw NotImplementedException("buildRemoverButton belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        def isLoggedIn = {
            throw NotImplementedException("isLoggedIn belum diset, mohon override HtmlDgCode.picBuilder " +
                "dengan implementasi mu sendiri.")
        }

        def likeClick = {
            throw NotImplementedException("likeClick belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        def showRemoverButton = {
            throw NotImplementedException("showRemoverButton belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        protected implicit def pollAutoConverter(poll: model.Polling) = {
            throw NotImplementedException("pollAutoConverter belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        protected implicit def appAutoConverter(poll: App): AppBuilder.AppWrapperBase = {
            throw NotImplementedException("appAutoConverter belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri")
        }

        protected implicit def contentDeletableAutoConverter(app: Deletable): ContentDeletableNodeSeqWrapperBase = {
            throw NotImplementedException("contentDeletableAutoConverter belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri")
        }

        protected implicit def contentMovableAutoConverter(app: Movable): ContentMovableNodeSeqWrapperBase = {
            throw NotImplementedException("contentMovableAutoConverter belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri")
        }

        def postLikers: NodeSeq = {
            throw NotImplementedException("postLikers belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }


        def toStreamNodeSeq(embedMode: Boolean, streamInfo: StreamObject, withShoutButton: Boolean,
                            withRetalkButton: Boolean, withRemoverButton: Boolean, withReportButton: Boolean,
                            withLockPostButton: Boolean, withClosePostButton: Boolean): NodeSeq = NodeSeq.Empty


        def toSingleViewNodeSeq(embedMode: Boolean, settingMenus:NodeSeq, adminMenus: NodeSeq): NodeSeq = {
            throw NotImplementedException("toSingleViewNodeSeq belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri")
        }

        protected lazy val hasEmbeddedObject: HasEmbeddedObject[Types.IDType] = p.asInstanceOf[HasEmbeddedObject[IDType]]

        def attachedFiles(at: FileAttachable) = NodeSeq.Empty
        protected def buildShoutButton() = NodeSeq.Empty
//        protected def buildRetalkButton() = NodeSeq.Empty
        protected def buildReportButton() = NodeSeq.Empty
//        protected def buildShareButton(embedMode: Boolean) = NodeSeq.Empty
        protected def buildLockPostButton(embedMode: Boolean) = NodeSeq.Empty
        protected def buildClosePostButton(embedMode: Boolean) = NodeSeq.Empty

        def toStreamNodeSeq(embedMode: Boolean): NodeSeq = NodeSeq.Empty

//        def toHomeStreamNodeSeq(embedMode: Boolean, perspective:String): NodeSeq = NodeSeq.Empty

    }


}


