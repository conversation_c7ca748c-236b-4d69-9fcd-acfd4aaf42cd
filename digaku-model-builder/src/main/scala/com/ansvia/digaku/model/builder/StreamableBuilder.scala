/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.digaku.model.Streamable
import com.ansvia.digaku.Types.IDType
import scala.xml.NodeSeq
import com.ansvia.digaku.exc.NotImplementedException

/**
 * Author: robin
 *
 */
trait StreamableBuilder {

    abstract class AbstractStreamableWrapper(s:Streamable[IDType]){
        def toStreamNodeSeq(embedMode:Boolean):NodeSeq

//        /**
//         * Untuk build stream di halaman home
//         * digunakan di stream bookmark group dan all stream pada home stream
//         * story: https://mindtalk.atlassian.net/browse/MM-119
//         * @param embedMode cek apakah post di embed
//         * @param perspective Ini digunakan untuk membedakan stream all stream dan bookmark stream
//         * @return
//         */
//        def toHomeStreamNodeSeq(embedMode:<PERSON><PERSON><PERSON>, perspective:String):NodeSeq
    }
}

//object StreamableBuilder extends StreamableBuilder {
//
//    class UnimplementedStreamableWrapper(s:Streamable[IDType]) extends AbstractStreamableWrapper(s){
//        def toStreamNodeSeq(embedMode: Boolean): NodeSeq = {
//            throw NotImplementedException("please implement this toStreamNodeSeq")
//        }
//    }
//}
