/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.jsonbuilder

import net.liftweb.json._
import JsonDSL._
import com.ansvia.digaku.model.PostBase
import com.ansvia.digaku.model.App

/**
 * Author: robin (<EMAIL>)
 */

trait AppBuilder {

//    import com.ansvia.digaku.web.builder.MyAppBuilder._

    implicit protected def postAutoConverter(_app:App):com.ansvia.digaku.model.builder.AppBuilder.AppWrapperBase

    def buildViaFor(p:PostBase) = {
        p.getPostVia.map { via =>
            var appJson = ("id" -> via.getId) ~
                ("name" -> via.name) ~
                ("icon" -> via.normalizedPicSmall)

            if (via.supportCanvas)
                appJson = appJson ~ ("canvas_url" -> via.canvasViewUrl)

            ("via" -> appJson)
        }
    }


}
