/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types._
import com.ansvia.digaku.exc.NotImplementedException
import com.ansvia.digaku.model._
import com.ansvia.digaku.model.builder.ContentDeletableBuilder.ContentDeletableNodeSeqWrapperBase
import com.ansvia.digaku.model.builder.ContentMovableBuilder.ContentMovableNodeSeqWrapperBase
import com.ansvia.digaku.{Digaku, model}

import scala.xml.NodeSeq


/**
* Author: robin
* Date: 2/3/13
* Time: 4:10 PM
*
*/
trait PictureBuilder extends Slf4jLogger {



    import com.ansvia.digaku.utils.RichString._


    abstract class PictureWrapperBase(pic:model.PictureBase) extends StreamView {

        // tidak pake with DbAccess karena ini menghindari ambiguous ketika import
        // karena with DbAccess pakenya bukan private tapi protected.
        private implicit def db:GraphType = Digaku.engine.database.getRaw


        protected implicit def originAutoConvert(origin:model.Origin[GraphType]):OriginBuilder.OriginWrapperBase =
            new OriginBuilder.UnimplementedOriginWrapper(origin)
        protected implicit def userAutoCenverter(user:model.User):UserBuilder.UserWrapperBase = new UserBuilder.UnimplementedUserWrapper(user)
        protected implicit def contentDeletableAutoConverter(deletable:model.Deletable):ContentDeletableBuilder.ContentDeletableNodeSeqWrapperBase
        protected implicit def contentMovableAutoConverter(movable:model.Movable):ContentMovableBuilder.ContentMovableNodeSeqWrapperBase

        val httpProtocol:String

        def currentUser:Option[model.User]
        def isLoggedIn:Boolean

        def snipLink(embedMode:Boolean):NodeSeq = {
            <span>
                <a href={url(embedMode)} data-embed-url={url(embedMode = true)} data-mfp-class="mfp-radius mfp-single-photo">{pic.getTitle.truncate(20)}</a>
            </span>
        }

        def url(embedMode:Boolean, addParams:(String, String)*) = {
            val title = if (titleUrl == "")  pic.creator.name else titleUrl

            val url =
                if(pic.origin!=null){
                    "%s/pic/%s/%s".format(pic.origin.url, pic.getId,title)
                }else{
                    if (pic.creator!=null)
                        "/u/%s/pic/%s/%s".format(pic.creator.getName.toLowerCase,pic.getId, title)
                    else
                        "/null/pic/%s/%s".format(pic.getId, title)
                }


            val addParamsEncoded = addParams.map {case (k,v) => s"$k=$v" }.mkString("&")

            if (embedMode){
                url + "?embed=1" + (if (addParams.length > 0) "&" else "") + addParamsEncoded
            }else{
                url + (if (addParams.length > 0) "?" else "") + addParamsEncoded
            }
        }

        def mediumPreviewUrl:String
        def smallPreviewUrl:String

        def relativeUrl = {
            if(pic.origin!=null){
                "forum/%s/pic/%s/%s".format(pic.origin.getName.toLowerCase, pic.getId,titleUrl)
            }else{
                if (pic.creator!=null)
                    "/u/%s/pic/%s/%s".format(pic.creator.getName.toLowerCase,pic.getId, titleUrl)
                else
                    "/null/pic/%s/%s".format(pic.getId, titleUrl)
            }
        }
//
        def titleUrl = {
            val titleUrlize = pic.getTitle.urlize.toLowerCase
            if (titleUrlize == "") pic.creator.name else titleUrlize
        }

    }
}

object PictureBuilder extends PictureBuilder {
    class UnimplementedPictureWrapperBase(p:model.PictureBase) extends PictureWrapperBase(p) {

        val httpProtocol = "http"

        def currentUser = None

        protected def buildReportButton() = NodeSeq.Empty
        protected def buildShareButton(embedMode: Boolean) : NodeSeq = NodeSeq.Empty
        protected def buildRetalkButton(embedMode: Boolean) : NodeSeq = NodeSeq.Empty
        protected def buildLockPostButton(embedMode: Boolean) : NodeSeq = NodeSeq.Empty
        protected def buildClosePostButton(embedMode: Boolean) : NodeSeq = NodeSeq.Empty

        def supportUnsupportButton(srcUser: User, targetUser: User, ns: NodeSeq) = {
            throw NotImplementedException("supportUnsupportButton belum di set, mohon override HtmlDgCode.picBuilder " +
                "dengan implementasi mu sendiri.")
        }

        def likeClick = {
            throw NotImplementedException("likeClick belum di set, mohon override HtmlDgCode.picBuilder " +
                "dengan implementasi mu sendiri.")
        }

        def removerButton = {
            throw NotImplementedException("removerButton belum di set, mohon override HtmlDgCode.picBuilder " +
                "dengan implementasi mu sendiri.")
        }

        def isLoggedIn = {
            throw NotImplementedException("isLoggedIn belum di set, mohon override HtmlDgCode.picBuilder " +
                "dengan implementasi mu sendiri.")
        }

        def pictLikers: NodeSeq = {
            throw NotImplementedException("eventLikers belum di set, mohon override HtmlDgCode.picBuilder " +
                "dengan implementasi mu sendiri.")
        }

        protected implicit def contentDeletableAutoConverter(app: Deletable): ContentDeletableNodeSeqWrapperBase = {
            throw NotImplementedException("contentDeletableAutoConverter belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri")
        }

        protected implicit def contentMovableAutoConverter(app: Movable): ContentMovableNodeSeqWrapperBase = {
            throw NotImplementedException("contentDeletableAutoConverter belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri")
        }

        def mediumPreviewUrl = {
            throw NotImplementedException("contentDeletableAutoConverter belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri")
        }

        def smallPreviewUrl = {
            throw NotImplementedException("contentDeletableAutoConverter belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri")
        }

        def toStreamNodeSeq(embedMode: Boolean): NodeSeq = NodeSeq.Empty

//        def toHomeStreamNodeSeq(embedMode: Boolean, perspective:String): NodeSeq = NodeSeq.Empty

    }
}

