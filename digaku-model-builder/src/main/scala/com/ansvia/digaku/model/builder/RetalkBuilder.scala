///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model.builder
//
//import com.ansvia.digaku.model
//import xml.NodeSeq
//
///**
// * Author: ubai
// *
// */
//
///**
// * digunakan untuk build stream RetalkWrapper ke NodeSeq
// */
//trait RetalkBuilder {
//    abstract class RetalkNodeSeqWrapperBase(p:model.RetalkWrapper) extends StreamView {
//
//    }
//}
//
//object RetalkBuilder extends RetalkBuilder {
//    class UnimplementedRetalkNodeSeqWrapperBase(p:model.RetalkWrapper) extends RetalkNodeSeqWrapperBase(p) {
//
//        def toStreamNodeSeq(embedMode: <PERSON>olean): NodeSeq = NodeSeq.Empty
//
////        def toHomeStreamNodeSeq(embedMode: Bo<PERSON><PERSON>, perspective: String): NodeSeq = NodeSeq.Empty
//    }
//}