/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

/**
 * Author: robin (<EMAIL>)
 */

object Normalizer {
    val PHITOS_HTTPS_REWRITE = """(http)\://(s3\.mc2\.bca\.co\.id/.[^\s]*)""".r

    /**
     * Untuk normalisasi single url.
     * @param url url yang akan di-normalisasi.
     * @return
     */
    def normalizeUrl(url:String) = {
        PHITOS_HTTPS_REWRITE.replaceFirstIn(url, "https://$2")
    }
}
