/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types._
import com.ansvia.digaku.exc.NotImplementedException
import com.ansvia.digaku.model.Origin
import com.ansvia.digaku.{Digaku, Types, model}
import com.ansvia.digaku.utils.ForumSettings._

import scala.xml.{NodeSeq, Text}

/**
 * Author: nadir, robin
 *
 */
trait SubForumBuilder extends DefaultSubForumBuilder with Slf4jLogger {

    abstract class SubForumWrapperBase(group:model.Forum){

        // tidak pake with DbAccess karena ini menghindari ambiguous ketika import
        // karena with DbAccess pakenya bukan private tapi protected.
        private implicit def db:GraphType = Digaku.engine.database.getRaw


        implicit def originAutoConvert(origin:model.Origin[GraphType]):OriginBuilder.OriginWrapperBase

        val httpProtocol:String

        /**
         * Http protocol yang digunakan oleh phitos atau aws
         */
        val awsHttpProtocol:String
        val baseUrl:String

        val logoDefault = "http://i.imgur.com/VCFbjJa.jpg"

        private def fixHttpProtocol(url:String) = {
            url.replace("http://", httpProtocol + "://")
        }

        /**
         * Rewrite http schema pada url menggunakan value dari [[awsHttpProtocol]]
         */
        private def fixAwsHttpProtocol(url:String):String = {
            url.replace("http://", awsHttpProtocol + "://")
                .replace("https://", awsHttpProtocol + "://")
        }

        def normalizedLogoSmall = {
            if (group.smallLogoUrl.length > 0)
                fixAwsHttpProtocol(group.smallLogoUrl)
            else {
//                val chLogoCount = ChannelLogoDefault.getCount
//
//                if (chLogoCount > 0) {
//                    val num =
//                        if (chLogoCount > 1)
//                            (group.name.map(_.toInt).sum % chLogoCount).toInt
//                        else
//                            0
//
//                    val chLogoList = ChannelLogoDefault.getListLogoDefault.toList
//                    if (chLogoList.length > num) {
//                        fixHttpProtocol(chLogoList(num).logoDefaultSmallUrl)
//                    } else {
//                        defaultLogo(baseUrl, group, 32)
//                    }
//                } else {
                fixHttpProtocol(defaultLogo(baseUrl, group, 32))
//                }
            }
        }

        def normalizedLogoMedium = {
            if (group.mediumLogoUrl.length > 0)
                fixAwsHttpProtocol(group.mediumLogoUrl)
            else {
//                val chLogoCount = ChannelLogoDefault.getCount
//
//                if (chLogoCount > 0) {
//                    val num =
//                        if (chLogoCount > 1)
//                            (group.name.map(_.toInt).sum % chLogoCount).toInt
//                        else
//                            0
//
//                    val chLogoList = ChannelLogoDefault.getListLogoDefault.toList
//                    if (chLogoList.length > num) {
//                        fixHttpProtocol(chLogoList(num).logoDefaultMediumUrl)
//                    } else {
//                        defaultLogo(baseUrl, group, 64)
//                    }
//                } else {
                    fixHttpProtocol(defaultLogo(baseUrl, group, 64))
//                }
            }
        }

        def normalizedLogoLarge = {
            if (group.largeLogoUrl.length > 0)
                fixAwsHttpProtocol(group.largeLogoUrl)
            else {
//                val chLogoCount = ChannelLogoDefault.getCount
//
//                if (chLogoCount > 0) {
//                    val num =
//                        if (chLogoCount > 1)
//                            (group.name.map(_.toInt).sum % chLogoCount).toInt
//                        else
//                            0
//
//                    val chLogoList = ChannelLogoDefault.getListLogoDefault.toList
//                    if (chLogoList.length > num) {
//                        fixHttpProtocol(chLogoList(num).logoOriginalUrl)
//                    } else {
//                        defaultLogo(baseUrl, group, 128)
//                    }
//                } else {
                    fixHttpProtocol(defaultLogo(baseUrl, group, 128))
//                }
            }
        }

        def normalizeBannerPicUrl = {
            if (group.bannerPicUrl.length > 0) {
                fixAwsHttpProtocol(group.bannerPicUrl)
            } else {
                fixHttpProtocol(defaultBanner(baseUrl, group))
                // "https://dmcd6hvaqrxz0.cloudfront.net/2013/04/23/415a9395c40a5d1cefbf8f8c2d50f892.jpg"
            }
        }

        def bannerPicElm:NodeSeq =
            <div>
                <img class="img-rounded" src={normalizeBannerPicUrl}>
                </img>
            </div>

        def thumbnailLogoSmall:NodeSeq =
            <div class="thumbnail" style="width: 32px; height: 32px; float: left; margin-right: 10px;">
                <a href={group.url} title={group.name}><img src={normalizedLogoSmall} alt={group.name} /></a>
            </div>

        def thumbnailLogoMedium:NodeSeq =
            <div class="thumbnail" style="width: 64px; height: 64px; float: left; margin-right: 10px;">
                <img src={normalizedLogoMedium} alt={group.name} />
            </div>

        def thumbnailLogoLarge:NodeSeq =
            <div class="thumbnail" style="width: 128px; height: 128px; float: left; margin-right: 10px;">
                <img src={normalizedLogoLarge} alt={group.name} />
            </div>

        // @TODO(robin): periksa ini apakah masih dipakai atau tidak, kalau tidak remove aja.
        /**
         * Dipakai di post creator box jika dibuka dari group page
         *
         * Kasi keterangan feature post kind apa saja yang nonaktif di group tersebut
         *
         * @param u
         * @return
         */
        def notSupportedStatus(u:model.User):NodeSeq = {
            val notSupported = Array.empty[String]
            var notSupportedFatures = Array.empty[String]

//            // untuk who can post
//            if (!group.userCanCreateVideo_?(u))
//                notSupported +:= "Video post"
//
//            if (!group.userCanCreatePicture_?(u))
//                notSupported +:= "Photo post"
//
//            if (!group.userCanCreateSimplePost_?(u))
//                notSupported +:= "Text post"
//
            // untuk group features
//            if (!group.hasFeature(ForumFeatures.VIDEO))
//                notSupportedFatures +:= "Video Post"

//            if (!group.hasFeature(ForumFeatures.PHOTO))
//                notSupportedFatures +:= "Photo post"

//            if (!group.hasFeature(ForumFeatures.SIMPLE_POST))
//                notSupportedFatures +:= "Text post"

            if (!notSupported.isEmpty || !notSupportedFatures.isEmpty){
                <div class="text-center not-support">
                    {
                    if (!notSupported.isEmpty) {
                        Text("Group's policy makes ") ++ notSupported.map { s => <strong>{s}</strong> :NodeSeq}.reduceLeft(_ ++ Text(", ") ++ _) ++ Text(" not available for you.")
                    } else {
                        notSupportedFatures.map{s => <strong>{s}</strong> :NodeSeq}.reduceLeft(_ ++ Text(" and ") ++ _) ++ Text(" are not allowed in this Channels.")
                    }
                    }
                </div>
            } else
                NodeSeq.Empty
        }
    }

}

object SubForumBuilder extends SubForumBuilder {
    class UnimplementedUserWrapper(ch:model.Forum) extends SubForumWrapperBase(ch){
        implicit def originAutoConvert(origin: Origin[Types.GraphType]) =
            new OriginBuilder.UnimplementedOriginWrapper(origin)

        val httpProtocol: String = "http"
        val baseUrl: String = throw NotImplementedException("baseUrl belum diset, mohon override fungsi ini " +
            "dengan implementasi mu sendiri")

        /**
         * Http protocol yang digunakan oleh phitos atau aws
         */
        override val awsHttpProtocol: String = "http"
    }

}

trait DefaultSubForumBuilder {
    /**
     * Set Group banner dengan default banner BCA MC2
     * @return
     */
    def defaultBanner(baseUrl:String, forum:model.Forum) = {
        // val num = group.name.map(_.toInt).sum % 2
        val kind = if (forum.hasFeature(model.ForumFeatures.ARTICLE)) {
            "subforum"
        } else {
            "forum"
        }
        baseUrl + "/assets/img/default/banner/" + kind + ".jpg"
    }

    /**
     * Set default logo for group
     * @param baseUrl
     * @param forum
     * @param size
     * @return
     */
    def defaultLogo(baseUrl:String, forum:model.Forum, size:Int) = {
        // val num = group.name.map(_.toInt).sum % 8
        val kind = if (forum.hasFeature(model.ForumFeatures.ARTICLE)) {
            "sf_"
        } else {
            "forum_"
        }
        baseUrl + "/assets/img/default/logo/" + kind + size + ".png"
    }
}
