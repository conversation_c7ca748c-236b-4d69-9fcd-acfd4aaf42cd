package com.ansvia.digaku.model.jsonbuilder

import com.ansvia.digaku.model.UserGroup
import net.liftweb.json.JsonAST.{JObject, JValue}
import net.liftweb.json.JsonDSL._

/**
  * Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
  */

trait UserGroupBuilder {
    class JsonUserGroupWrapper(userGroup:UserGroup) {
        def toJson(): JObject = {
            ("id" -> userGroup.getId) ~
                ("name" -> userGroup.name) ~
                ("description" -> userGroup.description) ~
                ("logo" -> userGroup.logo)
        }
    }

    implicit def implicitUserGroupToJValue(ug:UserGroup) = new JsonUserGroupWrapper(ug)
}

object UserGroupBuilder extends UserGroupBuilder