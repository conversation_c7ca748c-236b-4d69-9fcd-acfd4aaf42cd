/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.digaku.exc.NotImplementedException
import com.ansvia.digaku.model

import scala.xml.NodeSeq

/**
 * Author: nadir (<EMAIL>)
 *
 * digunakan untuk build list thread ke NodeSeq dalam bentuk table
 *
 */
trait ThreadListBuilder {
    abstract class ThreadListWrapperBase(postList:Seq[model.Post]) {

        /**
         * digunakan untuk menjadikan list thread ke NodeSeq table
         * @param pinnedIcon tampilkan icon pin ketika suatu post di pin,
         *                   false ketika tidak ingin di tampilkan.
         * @return
         */
        def threadListNodeSeq(pinnedIcon: Boolean, emptyNs:NodeSeq = NodeSeq.Empty): NodeSeq

        /**
         * hanya build NodeSeq <tr> table
         * untuk satu object post
         * @param post see [[com.ansvia.digaku.model.Post]]
         * @param pinnedIcon tampilkan icon pin ketika suatu post di pin,
         *                   false ketika tidak ingin di tampilkan.
         * @return
         */
        protected def threadTableItemNodeSeq(post:model.Post, pinnedIcon:Boolean):NodeSeq
    }
}

object ThreadListBuilder extends ThreadListBuilder {
    class UnimplementedUserWrapper(postList:Seq[model.Post]) extends ThreadListWrapperBase(postList){
        def threadListNodeSeq(pinnedIcon: Boolean, emptyNs:NodeSeq = NodeSeq.Empty): NodeSeq = {
            throw NotImplementedException("threadListNodeSeq belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        override protected def threadTableItemNodeSeq(post: model.Post, pinnedIcon:Boolean): NodeSeq = {
            throw NotImplementedException("threadTrTableNodeSeq belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }
    }

}
