/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.jsonbuilder

import com.ansvia.digaku.model.builder.Normalizer
import com.ansvia.digaku.model.{Trophy, User}
import net.liftweb.json.JsonDSL._
import net.liftweb.json._
import com.ansvia.digaku.utils.UserSettings._

import scala.math._

/**
 * Author: robin
 *
 */
trait UserBuilder {


    abstract class JsonUserWrapper(u:User) {


        implicit protected def userAutoConverter(_u:User):com.ansvia.digaku.model.builder.UserBuilder.UserWrapperBase

        def toSimpleJson(userRef:Option[User] = None):JValue = {
            var rv = UserBuilder.buildUserInfoSimple(u)
            userRef.foreach { uref =>
                rv = rv ~ ("is_supported_by_me" -> uref.isSupport(u)) ~
                    ("is_supported_me" -> u.isSupport(uref))
            }

            rv
        }

        /**
         * Return simple user info
         * @return
         */
        def infoSimple:JsonAST.JObject = {
            UserBuilder.buildUserInfoSimple(u)
        }

        /**
         * Digunakan untuk autocomplete
         * @return
         */
        def toSimple:JValue = {
            ("id" -> u.getId) ~
                ("name" -> u.getName)
        }

        /**
         * Digunakan untuk pengambilan list supporter dan supporting
         * @return
         */
        def toSimpleList:JValue = {

            ("id" -> u.getId) ~
                ("name" -> u.getName) ~
                ("full_name" -> u.fullName) ~
                ("photo" -> List(u.normalizedPhotoSmall, u.normalizedPhotoMedium, u.normalizedPhotoLarge))
        }

        /**
         * Digunakan untuk saat memerlukan tambahan informasi seperti email dan birth date
         * @param userRef user reference
         * @param attributes list of attribute requested
         * @return
         */
        def toJson(userRef:Option[User] = None, attributes:List[String] = List.empty[String]):JValue = {
            var rv = UserBuilder.buildUserInfo(u, attribute = attributes)
            userRef.foreach { uref =>
                rv = rv ~ ("is_supported_by_me", uref.isSupport(u)) ~
                    ("is_supported_me" -> u.isSupport(uref))
            }
            rv
        }
    }

}


object UserBuilder extends UserBuilder {

    def buildUserInfoSimple(user:User) = {
        val displayedEmail = if (user.emailLogin.endsWith("mc2.bca.co.id")) {
            "-"
        } else {
            user.emailLogin
        }

        ("id" -> user.getId) ~
            ("name" -> user.name) ~
            ("full_name" -> user.fullName) ~
            ("email" -> displayedEmail) ~
            ("experience" -> user.getExperience) ~
            ("user_rank" -> user.getRank.map(_.name).getOrElse("")) ~
            ("reputation_count" -> user.getCalcReputationBar) ~
            ("reputation_point" -> user.getReputationCount) ~
            ("department" -> user.department) ~
            ("job_title" -> user.title) ~
            ("photo_small" -> Normalizer.normalizeUrl(user.photoSmall)) ~
            ("photo_medium" -> Normalizer.normalizeUrl(user.photoMedium)) ~
            ("photo_large" -> Normalizer.normalizeUrl(user.photoLarge)) ~
            ("trophy" -> user.getTrophies(0, 1).toSeq.headOption.map { trophy =>
                ("name" -> trophy.getName) ~
                    ("unlocked" -> trophy.userHasTrophy(user)) ~
                    ("trophy_icons" -> Normalizer.normalizeUrl(trophy.icons))
            })
    }

    def buildUserInfo(implicit user:User, attribute:List[String] = List.empty[String]) = {

        var rv = buildUserInfoSimple(user)

        attribute.foreach {
            case "birthDate" => rv = rv ~ ("birth_date" -> user.birthDate)
            case "activationInfo" => rv = rv ~ ("activated" -> user.activated)
            case _ =>
        }

        rv = rv ~ ("experience" -> user.getExperience) ~
            ("user_rank" -> user.getRank.map(_.name).getOrElse("")) ~
            ("level" -> user.level) ~
            ("supporters_count" -> user.supportersCount) ~
            ("supporting_count" -> user.supportingCount) ~
            ("has_get_started" -> user.hasGetStarted) ~
            ("location" -> user.location) ~
            ("self_description" -> user.description) ~
            ("gender" -> user.sexStr) ~
            ("join_date" -> user.joinTime / 1000) ~
            ("join_date_str" -> User.JOIN_DATE_FORMAT.print(user.joinTime)) ~
            ("last_seen" -> user.settings.getOption("lastSeen").getOrElse(0L) /1000 ) ~
            ("timezone" -> user.timezone) ~
            ("banner" -> Normalizer.normalizeUrl(user.bannerPicUrl)) ~
            ("post_count" -> user.getPostCount)

        val badgesCount = user.getTrophies
        if (badgesCount.nonEmpty) {

            import com.ansvia.digaku.model.jsonbuilder.TrophyBuilder._

            rv = rv ~ ("unlocked_trophies" -> badgesCount.length) ~
                ("trophies" -> user.getTrophies(0, 3))
        }

        rv
    }
}

