/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types._
import com.ansvia.digaku.model.{Origin, OriginKind}
import com.ansvia.digaku.{Digaku, model}

import scala.xml.NodeSeq
import com.ansvia.digaku.utils.RichString.stringToRichString

/**
 * Author: robin
 *
 */
trait OriginBuilder extends Slf4jLogger {


    import com.ansvia.digaku.model.builder.OriginBuilder.{CHANNEL_URL_PREFIX, USER_URL_PREFIX}

    abstract class OriginWrapperBase(origin:Origin[GraphType]){

        // tidak pake with DbAccess karena ini menghindari ambiguous ketika import
        // karena with DbAccess pakenya bukan private tapi protected.
        private implicit def db:GraphType = Digaku.engine.database.getRaw

        assert(origin != null, "origin is null!")

        private lazy val lowerName = origin.getName.toLowerCase

        def url = {
            absoluteUrl + "/" + relativeUrl
        }

        def relativeUrl = {
            origin match {
                case forum:model.Forum =>
                    CHANNEL_URL_PREFIX + "/" + forum.getId
                case user:model.User =>
                    USER_URL_PREFIX + "/" + user.getId
            }
        }



        def absoluteUrl:String /*= {
            BASE_URL + url
        }*/

        def funkyName = {
            origin match {
                case u:model.User =>
                    if(u.fullName.trim.length > 0)
                        u.name + " (" + u.fullName + ")"
                    else
                        u.name
            }
        }

        def settingsGeneralUrl = {
            origin.kind match {
                case OriginKind.SUB_FORUM =>
                    absoluteUrl + "/" + CHANNEL_URL_PREFIX + "/" + lowerName + "/settings/general"
            }
        }

        def settingsUrl = {
            origin.kind match {
                case OriginKind.SUB_FORUM =>
                    absoluteUrl + "/" + CHANNEL_URL_PREFIX + "/" + origin.getId + "/settings"
            }
        }

        def memberUrl = {
            origin.kind match {
                case OriginKind.SUB_FORUM =>
                    absoluteUrl + "/" + CHANNEL_URL_PREFIX + "/" + lowerName + "/members"
            }
        }

        def snipLink:NodeSeq = {
            origin.kind match {
                case OriginKind.SUB_FORUM => <a href={url}>#{origin.getName}</a>
                case OriginKind.USER => <a href={url}>@{origin.getName}</a>
            }
        }

        def snipLinkNoSign:NodeSeq = {
            val title = origin match {
                case user:model.User =>
                    if (user.fullName.isEmpty)
                        user.name
                    else
                        user.fullName.stripPrefix("@")
                case _ =>
                    ""
            }
            origin.kind match {
                case OriginKind.SUB_FORUM => <a href={url}>{origin.getName}</a>
                case OriginKind.USER => <a href={url} title={title}>{origin.getName}</a>
            }
        }

        def snipLinkFullName(truncate:Int = 0):NodeSeq = {
            origin match {
                case user:model.User =>
                    val fullName = user.getName.stripPrefix("@")
                    val name = if (truncate > 0) {
                        fullName.truncate(truncate)
                    } else {
                        fullName
                    }

                    <a href={url} title={fullName}>{name}</a>
            }
        }
    }

}

object OriginBuilder extends OriginBuilder {
    var CHANNEL_URL_PREFIX = "notset"
    var USER_URL_PREFIX = "notset"

    class UnimplementedOriginWrapper(origin:Origin[GraphType]) extends OriginWrapperBase(origin) {
        def absoluteUrl = "/absolute-url-notset/"
    }

}
