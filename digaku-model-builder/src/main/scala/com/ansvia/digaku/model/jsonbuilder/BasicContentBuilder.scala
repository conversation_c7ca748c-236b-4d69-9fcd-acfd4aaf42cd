/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.jsonbuilder


import com.ansvia.digaku.Types.IDType
import com.ansvia.digaku.exc.UnsupportedException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model._
import com.ansvia.digaku.database.GraphCompat._
import net.liftweb.json.JsonDSL._
import net.liftweb.json._

/**
 * Author: fajr, robin
 *
 */

trait BasicContentBuilder extends DbAccess {

//    protected val SHORT_URL:String // = WebConfig.DIGAKU_SHORT_URL + "/"

    /**
     * Digunakan untuk membentuk json basic untuk publishable content
     * @param content kontent yang bisa di-publish.
     * @return
     */
    abstract class JsonBasicContentWrapper(content:BasicContentBuilder.BasicContentType){

        import ChannelBuilder._

        implicit protected def userBuilder(user:User):com.ansvia.digaku.model.jsonbuilder.UserBuilder.JsonUserWrapper

        def basicContent(userRef:Option[User] = None, overwriteId:Option[Long] = None,
                         templateMode:Boolean = false, attributes:List[String] = Nil) = {

            val originJson = content.origin match {
                case ch:Forum =>
                    ch.toJson(userRef, templateMode, attributes)
                case user:User =>
                    user.toJson()
                case _ => throw UnsupportedException("Unsupported origin type")
            }

            val origin = content.origin match {
                case ch:Forum => ch
                case user:User => user
                case _ => throw UnsupportedException("Unsupported origin type")
            }

            var rv = {
                val _content = content.reload()
                val _id = overwriteId.getOrElse(content.getId)

                // stickyInfo bernilai berbeda antara post dan retalk post
                val stickyInfo = if (overwriteId.isEmpty) {
                    _content.isSticked
                } else {
                    false
                }

                val totalRespCount = {
                    content match {
                        case p:Post =>
                            p.getAllResponseCount
                        case _ =>
                            _content.getResponseCount
                    }
                }

                val canDeleteJ:JValue = if (templateMode)
                    "%{is_can_delete}"
                else
                    if (BasicContentBuilder.canDelete(userRef, content)) true else false

                ("id" -> _id) ~
                    ("response_count" -> _content.getResponseCount) ~
                    ("all_response_count" -> totalRespCount) ~ // total response level 1 dan level 2
                    ("cools_count" -> _content.getLikesCount) ~
                    ("creator" -> tx { t =>
                        val creator = getFromTx(_content.creator, t)
                        UserBuilder.buildUserInfoSimple(creator)
                    }) ~
                    ("creation_time" -> (_content.creationTime / 1000)) ~
                    ("origin" -> originJson) ~
                    ("is_sticky" -> stickyInfo) ~
                    ("is_locked" -> _content.isLocked) ~
                    ("is_closed" -> _content.isClosed) ~
                    ("is_featured" -> content.isPromoted) ~
                    ("is_can_delete" -> canDeleteJ) ~
                    ("moved" -> contentMoved)
            }

            if (templateMode){
                rv = rv ~ ("view_count" -> "%{view_count}")
            }else{
                rv = rv ~ ("view_count" -> content.viewsCount)
            }

            if (userRef.isDefined) {

                val cu = userRef.get

                // tidak dapat di-move dan di-retalk jika origin berupa user
                val isCanMove = origin match {
                    case chOrigin: Forum =>
                        if (chOrigin.hasAbility(cu, Ability.MOVE_CONTENT)
                            && cu.getChannelsCountToMovePost(chOrigin, "") > 0
                            || cu.role == UserRole.ADMIN) {

                            content match {
                                // post jenis picture group tidak dapat di move
//                                case picGroup: PictureGroup =>
//                                    false
                                // penanganan apabila picture merupakan bagian dari picture group yang tidak dapat di move
                                case picture: Picture =>
                                    val pic = Picture.getById(picture.getId).get

                                    if (overwriteId.isEmpty) {
                                        if (pic.isEmbeddedPicture) {
                                            false
                                        } else {
                                            true
                                        }
                                    } else {
                                        false
                                    }
                                case post: Post =>
                                    if (overwriteId.isEmpty) {
                                        true
                                    } else {
                                        false
                                    }
                            }
                        } else {
                            false
                        }
                    case _ =>
                        false
                }

//                val isCanRetalk = origin match {
//                    case chOrigin: Forum =>
//                        (chOrigin.isMember(cu)
//                            && cu.getChannelsCountToRetalkPost(chOrigin, "") > 0
////                            && chOrigin.isSupportRetalk
//                            && !chOrigin.getPrivated
//                            && overwriteId.isEmpty)
//                    case _ =>
//                        false
//                }

                val isCanSticky = origin match {
                    case chOrigin: Forum =>
                        if (chOrigin.isOwner(cu) || chOrigin.isStaff(cu)) {
                            content match {
                                // post jenis picture group = false karena picture group tidak dapat di sticky
//                                case picGroup: PictureGroup =>
//                                    false
                                // penanganan apabila picture merupakan bagian dari picture group
                                case picture: Picture =>
                                    val pic = Picture.getById(picture.getId).get

                                    if (overwriteId.isEmpty) {
                                        if (pic.isEmbeddedPicture) {
                                            false
                                        } else {
                                            true
                                        }
                                    } else {
                                        false
                                    }
                                case post: Post =>
                                    if (overwriteId.isEmpty) {
                                        true
                                    } else {
                                        false
                                    }
                            }
                        } else {
                            false
                        }
                    case _ =>
                        false
                }

                rv = rv ~ ("is_cooled_by_me" -> content.isLiker(cu)) ~
//                    ("is_subscribed_by_me" -> content.userCanUnwatch(cu)) ~
                    ("is_can_move" -> isCanMove) ~
                    ("is_can_sticky" -> isCanSticky)
            }

            rv
        }

        private def contentMoved:Option[JObject] = {
            if(content.isMoved) {
                val rv = ("is_moved" -> "true") ~
                    ("channel_origin" -> Forum.getById(content.movedForumOriginId).map {
                        ch =>
                            ("id" -> ch.getId) ~
                                ("name" -> ch.name) ~
                                ("logo_small" -> ch.smallLogoUrl)
                    })
                Some(rv)
            } else {
                None
            }
        }
    }

//    implicit def implicitBasicContentWrapper(content:PublishableContent with Responable with Likable with Stickable) =
//        new BasicBuilderJsonable(content)
}

object BasicContentBuilder extends BasicContentBuilder {
    type BasicContentType = PublishableContent with Responable with Likable with Stickable

    import com.ansvia.digaku.utils.UserSettings._

    /**
     * Berguna untuk menangani apabila ada perubahan kemampuan delete di rule
     * mengacu pada com.ansvia.digaku.restapi.PostRest di bagian delete post
     * @return Boolean
     */
    def canDelete(userO:Option[User], content:BasicContentType):Boolean = {
        if (userO.isDefined) {

            val cu = userO.get
            val p = content
//            val superAdmin = cu.role == UserRole.SUPER_ADMIN
//            val adminStaff = cu.role == UserRole.ADMIN
            val owner = p.creator == cu
//
//            val channelOwner = {
//                p.origin match {
//                    case ch:model.Forum => ch.isOwner(cu)
//                    case user:model.User =>
//                        false
//                }
//            }
//
//            val adminStaffCanDelete = {
//                cu.getAbilities.contains(Ability.DELETE_POST)
//            }
//
//            val adminChannelCanDelete = {
//                p.origin match {
//                    case ch:model.Forum =>
//                        ch.getStaffAbility(cu).contains(Ability.DELETE_POST)
//                    case _ => false
//                }k
//            }

            // di-comment out karena di api tidak menggunakan fungsi administrasi
//            if (!p.isLocked && (owner || superAdmin || channelOwner || adminStaff || adminStaffCanDelete || adminChannelCanDelete)) {
            if (!p.isLocked && owner) {
                true
            } else {
                //current user don't have enough permission
                false
            }

        } else false
    }

    def canDelete(userO:Option[User], content:Streamable[IDType]):Boolean = {
        canDelete(userO, content.asInstanceOf[BasicContentType])
    }
}
