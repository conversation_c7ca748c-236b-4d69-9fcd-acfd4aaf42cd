/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import com.ansvia.digaku.model
import model.User
import xml.NodeSeq
import com.ansvia.digaku.exc.NotImplementedException

/**
 * Author: temon
 * Date: 10/25/13
 * Time: 1:38 PM
 * 
 */
trait ContentMovableBuilder {
    abstract class ContentMovableNodeSeqWrapperBase(p:model.Movable){
        def currentUser:Option[model.User]
        def moveAsAdminChannelButton():NodeSeq
        def moveAsAdminButton():NodeSeq
        def movedChannelSniplink:NodeSeq
    }
}

object ContentMovableBuilder extends ContentMovableBuilder {
    class UnimplementedContentMovableNodeSeqWrapper(p:model.Movable) extends ContentMovableNodeSeqWrapperBase(p) {
        def currentUser: Option[User] = None

        def moveAsAdminChannelButton(): NodeSeq = {
            throw NotImplementedException("deleteAsCreatorButton belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        def moveAsAdminButton(): NodeSeq = {
            throw NotImplementedException("deleteAsCreatorButton belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }

        def movedChannelSniplink: NodeSeq = {
            throw NotImplementedException("deleteAsCreatorButton belum diset, mohon override fungsi ini " +
                "dengan implementasi mu sendiri.")
        }


    }
}
