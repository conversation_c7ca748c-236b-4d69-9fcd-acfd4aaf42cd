/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import java.util.Date

import com.ansvia.digaku.Types._
import com.ansvia.digaku.exc.NotImplementedException
import com.ansvia.digaku.model
import com.ansvia.digaku.model.builder.ContentDeletableBuilder.ContentDeletableNodeSeqWrapperBase
import com.ansvia.digaku.model.builder.ContentMovableBuilder.ContentMovableNodeSeqWrapperBase
import com.ansvia.digaku.model.{Ability, Deletable, Movable, OriginKind, User}
import com.ansvia.digaku.utils.RichDate._
import com.ansvia.digaku.utils.TextCompiler
import org.ocpsoft.prettytime.PrettyTime

import scala.xml.{NodeSeq, Text, Unparsed}

/**
 * Author: robin
 * Date: 2/15/13
 * Time: 11:02 AM
 *
 */
trait EventBuilder {

    import com.ansvia.digaku.utils.RichString._
    import com.ansvia.graph.BlueprintsWrapper._


//    abstract class EventNodeSeqWrapperBase(event:model.Event) extends StreamView {
//
//        protected implicit def userAutoCenverter(user:model.User):UserBuilder.UserWrapperBase = new UserBuilder.UnimplementedUserWrapper(user)
//        protected implicit def originAutoConvert(origin:model.Origin[GraphType]):OriginBuilder.OriginWrapperBase =
//            new OriginBuilder.UnimplementedOriginWrapper(origin)
//
//        protected implicit def contentDeletableAutoConverter(deletable:model.Deletable):ContentDeletableBuilder.ContentDeletableNodeSeqWrapperBase
//        protected implicit def contentMovableAutoConverter(movable:model.Movable):ContentMovableBuilder.ContentMovableNodeSeqWrapperBase
//
//        def buildReportButton():NodeSeq
//        protected def buildShareButton(embedMode: Boolean):NodeSeq
//        protected def buildRetalkButton(embedMode: Boolean):NodeSeq
//        protected def buildLockEventButton():NodeSeq
//        protected def buildCloseEventButton():NodeSeq
//
//        def currentUser:Option[model.User]
//        def isLoggedIn:Boolean
//
//        def likeClick:NodeSeq
//
//        def creatorNs:NodeSeq = {
//            <div class="clearfix p-creator">
//                {event.creator.thumbnailElmSmall}
//                <strong><a href={event.creator.url}>{event.creator.name}</a>:</strong>
//                {
//                if(event.creator.isInactive)
//                    <div class="icon-eye-close" title="User is inactive"></div>
//                }
//            </div>:NodeSeq
//        }
//
//        /**
//         * Untuk build tampilan retalk di post.
//         * @param streamInfo meta info untuk object stream.
//         * @return
//         */
//        def retalkLable(streamInfo:model.StreamObject) = {
//            // lable untuk retalk
//            if (streamInfo != null) {
//                streamInfo match {
//                    case si:model.RetalkStreamObject =>
//                        <span class="label label-default">retalked</span>
//                    case _ => NodeSeq.Empty
//                }
//            }else
//                NodeSeq.Empty
//        }
//
//        /**
//         * untuk build event item stream di stream
//         * @return
//         */
//        def toStreamNodeSeq(embedMode:Boolean):NodeSeq = {
//
//            // @TODO(*): implement ini seperti di PostBuilder dan PicBuilder
//
//            <div></div>
//
////            import com.ansvia.digaku.utils.RichDate._
////
////            val alertClass = {
////                event.deletedType match {
////                    case model.DeletedType.HARD =>
////                        " alert alert-danger"
////
////                    case model.DeletedType.SOFT =>
////                        " alert alert-warning"
////
////                    case _ =>
////                        ""
////                }
////            }
////
////            val className = "clearfix stream-item post-item-" + event.getId
////
////
////            event.deletedType match {
////                case model.DeletedType.HARD =>
////                    NodeSeq.Empty
////                case model.DeletedType.SOFT =>
////                    <div class={className + alertClass}>
////                        {creatorNs}
////                        {retalkLable(streamInfo)}
////                        <div class="alert alert-warning"><span class="icon-info-sign"></span>
////                            <em>
////                                This content has been deleted by
////                                {
////                                if (event.deletedRole == model.DeletedRole.ADMIN_SYSTEM){
////                                    "Admin"
////                                } else {
////                                    model.User.getByName(event.deletedByUserName).map(_.snipLink).getOrElse(NodeSeq.Empty) ++
////                                        Text("(" + event.deletedRole.replaceAll("-", " ").capitalize + ")")
////                                }
////                                }
////                                <br />
////                                Reason :
////                                <br />
////                                {Unparsed(TextCompiler.compileMessage(event.deletedReason))}
////                                {
////                                if (cuIsAdmin){
////                                    <a href={deletedUrl} target="_blank">Preview this deleted content</a>
////                                }
////                                }
////                            </em>
////                            <div class="stream-item-bottom">{eventAttributes(false)}</div>
////                        </div>
////                    </div>
////
////                case _ =>
////                    if(event.moved) {
////                            <div class={className + alertClass}>
////                                { if (isLoggedIn && (!event.isLocked || cuIsSuperAdmin)) {
////                                <div class="pull-right space">
////                                    <a class="dropdown-toggle" data-target="#" data-toggle="dropdown" href="#">
////                                        <span class="icon-gear"></span>
////                                    </a>
////                                    <ul class="dropdown-menu ">
////                                        <div style="padding: 10px;">
////                                            <ul class="nav nav-list">
////                                                <li>{event.moveButton()}</li>
////                                                <li>{buildReportButton()}</li>
////                                                <li>{buildLockEventButton()}</li>
////                                                <li>{buildCloseEventButton()}</li>
////                                                <li>{event.deleteAsAdminChannelButton()}</li>
////                                                <li>{event.deleteAsAdminButton("")}</li>
////                                                <li>{event.deleteAsCreatorButton("post-item-"+event.getId)}</li>
////                                            </ul>
////                                        </div>
////                                    </ul>
////                                </div>
////                                } else
////                                    NodeSeq.Empty
////                                }
////                                {
////                                if(event.isSticked)
////                                    <span class="icon-pushpin pull-right"
////                                          title="event is sticked"
////                                          style="color: #5A56c3; font-size: 15px;"> </span>
////                                else NodeSeq.Empty
////                                }
////                                {
////                                if(event.locked)
////                                    <span class="icon-lock pull-right"
////                                          title="event is locked"
////                                          style="color: #993300; font-size: 15px;"> </span>
////                                else NodeSeq.Empty
////                                }
////                                {
////                                if(event.closed)
////                                    <span class="icon-lock action-close pull-right"
////                                          title="post is closed"
////                                          style="font-size: 15px;"> </span>
////                                else NodeSeq.Empty
////                                }
////                                <div class="clearfix p-creator">
////                                    {event.creator.thumbnailElmSmall}
////                                    {
////                                    if (event.creator.isInactive)
////                                        <div class="icon-eye-close" title="User is inactive"></div>
////                                    }
////
////                                    <span class="label label-info">event</span>
////                                    {retalkLable(streamInfo)}
////                                    {
////                                    if (!renderAtTargetChannel)
////                                        <span class="label label-warning">MOVED</span>
////                                    }
////                                    <strong><a href={url(false)} data-embed-url={url(true)}><strong>{event.title}</strong></a></strong> -
////                                    <span>{event.getDueDate.toRelative}</span><br />
////                                    <span>
////                                        Prepared by <strong><a href={event.creator.url}>{event.creator.name}</a></strong>
////                                        in <strong>{if(renderAtTargetChannel) event.origin.snipLink else event.movedChannelSniplink}</strong> group
////                                        {
////                                        if (renderAtTargetChannel)
////                                            <span> origin from <strong>{event.movedChannelSniplink}</strong></span>
////                                        }
////                                    </span>
////                                </div>
////
////                                <div style="margin-left: 50px;">
////                                    <div class="clearfix">
////                                        {
////                                        val attenders = event.getAttenders(model.AttenderKind.POSITIVE)
////
////                                        <div>
////                                            {
////                                            if (attenders.length < 3){
////                                                attenders.map(_.thumbnailElmSmall)
////                                                    .reduceLeftOption(_ ++ _).getOrElse(NodeSeq.Empty)
////                                            }else{
////                                                attenders.slice(0, 2).map(_.thumbnailElmSmall)
////                                                    .reduceLeftOption(_ ++ _).getOrElse(NodeSeq.Empty)
////                                            }
////                                            }
////                                        </div> ++
////                                            (if (attenders.length > 0){
////                                                <div>
////
////                                                    {
////                                                    attenders.length match {
////                                                        case 1 =>
////                                                            <span>{attenders(0).snipLinkNoSign} join this event</span>
////                                                        case 2 =>
////                                                            <span>{attenders.map(_.snipLinkNoSign).reduceLeft(_ ++ (Text(", and "):NodeSeq) ++ _)} join this event</span>
////                                                        case 3 =>
////                                                            <span>{attenders.slice(0, 2).map(_.snipLinkNoSign).reduceLeft(_ ++ (Text(", "):NodeSeq) ++ _)} and {attenders(2).snipLinkNoSign} join this event</span>
////                                                        case x if x > 3 =>
////                                                            val more = x - 2
////                                                            <span>{attenders.slice(0,2).map(_.snipLinkNoSign).reduceLeft(_ ++ (Text(", "):NodeSeq) ++ _)} and <a href={url(false)} data-embed-url={url(true)}>{more} others</a> join this event</span>
////                                                        case 0 =>
////                                                            NodeSeq.Empty
////                                                    }
////                                                    }
////
////                                                </div>
////                                            }else
////                                                NodeSeq.Empty)
////
////                                        }
////
////                                    </div>
////                                    <div><a href={url(false)} data-embed-url={url(true)}><span class="icon-zoom-in"></span> view more</a>
////                                        {buildShareButton(false)}
////                                    </div>
////
////                                </div>
////                                <div>
////                                    {eventAttributes(false)}
////                                </div>
////                            </div>
////
////                    } else {
////                            <div class={className + alertClass}>
////                                { if (isLoggedIn && (!event.isLocked || cuIsSuperAdmin)) {
////                                <div class="pull-right space">
////                                    <a class="dropdown-toggle" data-target="#" data-toggle="dropdown" href="#">
////                                        <span class="icon-gear"></span>
////                                    </a>
////                                    <ul class="dropdown-menu ">
////                                        <div style="padding: 10px;">
////                                            <ul class="nav nav-list">
////                                                <li>{event.moveButton()}</li>
////                                                <li>{buildRetalkButton(false)}</li>
////                                                <li>{buildReportButton()}</li>
////                                                <li>{buildLockEventButton()}</li>
////                                                <li>{buildCloseEventButton()}</li>
////                                                <li>{event.deleteAsAdminChannelButton()}</li>
////                                                <li>{event.deleteAsAdminButton("")}</li>
////                                                <li>{event.deleteAsCreatorButton("post-item-"+event.getId)}</li>
////                                            </ul>
////                                        </div>
////                                    </ul>
////                                </div>
////                                } else
////                                    NodeSeq.Empty
////                                }
////                                {
////                                if(event.isSticked)
////                                    <span class="icon-pushpin pull-right"
////                                          title="event is sticked"
////                                          style="color: #5A56c3; font-size: 15px;"> </span>
////                                else NodeSeq.Empty
////                                }
////                                {
////                                if(event.locked)
////                                    <span class="icon-lock pull-right"
////                                          title="event is locked"
////                                          style="color: #993300; font-size: 15px;"> </span>
////                                else NodeSeq.Empty
////                                }
////                                {
////                                if(event.closed)
////                                    <span class="icon-lock action-close pull-right"
////                                          title="post is closed"
////                                          style="font-size: 15px;"> </span>
////                                else NodeSeq.Empty
////                                }
////                                <div class="clearfix p-creator">
////                                    {event.creator.thumbnailElmSmall}
////                                    {
////                                    if (event.creator.isInactive)
////                                        <div class="icon-eye-close" title="User is inactive"></div>
////                                    }
////
////                                    <span class="label label-info">event</span>
////                                    {retalkLable(streamInfo)}
////                                    <strong><a href={url(false)} data-embed-url={url(true)}><strong>{event.title}</strong></a></strong> -
////                                    <span>{event.getDueDate.toRelative}</span><br />
////                                    <span>
////                                        Prepared by <strong><a href={event.creator.url}>{event.creator.name}</a></strong>
////                                        in <strong>{event.origin.snipLinkNoSign}</strong> group
////                                    </span>
////                                </div>
////
////                                <div style="margin-left: 50px;">
////                                    <div class="clearfix">
////                                        {
////                                        val attenders = event.getAttenders(model.AttenderKind.POSITIVE)
////
////                                        <div>
////                                            {
////                                            if (attenders.length < 3){
////                                                attenders.map(_.thumbnailElmSmall)
////                                                    .reduceLeftOption(_ ++ _).getOrElse(NodeSeq.Empty)
////                                            }else{
////                                                attenders.slice(0, 2).map(_.thumbnailElmSmall)
////                                                    .reduceLeftOption(_ ++ _).getOrElse(NodeSeq.Empty)
////                                            }
////                                            }
////                                        </div> ++
////                                            (if (attenders.length > 0){
////                                                <div>
////
////                                                    {
////
////                                                    attenders.length match {
////                                                        case 1 =>
////                                                            <span>{attenders(0).snipLinkNoSign} join this event</span>
////                                                        case 2 =>
////                                                            <span>{attenders.map(_.snipLinkNoSign).reduceLeft(_ ++ (Text(", and "):NodeSeq) ++ _)} join this event</span>
////                                                        case 3 =>
////                                                            <span>{attenders.slice(0, 2).map(_.snipLinkNoSign).reduceLeft(_ ++ (Text(", "):NodeSeq) ++ _)} and {attenders(2).snipLinkNoSign} join this event</span>
////                                                        case x if x > 3 =>
////                                                            val more = x - 2
////                                                            <span>{attenders.slice(0,2).map(_.snipLinkNoSign).reduceLeft(_ ++ (Text(", "):NodeSeq) ++ _)} and <a href={url(false)} data-embed-url={url(true)}>{more} others</a> join this event</span>
////                                                        case 0 =>
////                                                            NodeSeq.Empty
////                                                    }
////
////
////                                                    }
////
////                                                </div>
////                                            }else
////                                                NodeSeq.Empty)
////
////                                        }
////
////                                    </div>
////                                    <div><a href={url(false)} data-embed-url={url(true)}><span class="icon-zoom-in"></span> view more</a>
////                                        {buildShareButton(false)}
////                                    </div>
////
////                                </div>
////                                <div>
////                                    {eventAttributes(false)}
////                                </div>
////
////                            </div>
////                    }
////            }
//        }
//
//        def toSingleViewEvent:NodeSeq = {
//            event.getVertex.toCC[model.Event] map { rvEvent =>
//                <div class="clearfix single-event-view">
//                    This event published in {rvEvent.origin.snipLink} group
//                    <div>
//                        <table class="table table-striped">
//                            <tr class="word-preview">
//                                <td>Event</td><td>:</td><td>{rvEvent.title}</td>
//                            </tr>
//                            <tr  class="word-preview">
//                                <td>Location</td><td>:</td><td>{rvEvent.location.truncate(1000)}</td>
//                            </tr>
//                            <tr>
//                                <td>When</td><td>:</td>
//                                <td>
//                                    {
//                                    if (rvEvent.expired){
//                                        <span class="label label-important">EXPIRED</span>
//                                    }else
//                                        NodeSeq.Empty
//                                    }
//                                    {new PrettyTime(new Date()).format(new Date(rvEvent.date))} - {new Date(rvEvent.date).toStdFormatWithTime}
//                                </td>
//                            </tr>
//                            <tr>
//                                <td>Prepared by</td><td>:</td>
//                                <td>
//                                    {rvEvent.creator.thumbnailElmSmall}
//                                    {
//                                    rvEvent.origin match {
//                                        case ch:model.Forum =>
//                                            if(ch.isOwner(rvEvent.creator))
//                                                <strong><a href={rvEvent.creator.url} style="color: #cF0809;">
//                                                    {rvEvent.creator.name}</a></strong>
//                                            else if(ch.isStaff(rvEvent.creator))
//                                                <strong><a href={rvEvent.creator.url} style="color: #8E1AED;">
//                                                    {rvEvent.creator.name}</a></strong>
////                                                    <em class="post-attributes" style="margin-left: 10px;">{ch.getStaffAttribute(rvEvent.creator).title}</em>
//                                            else
//                                                <strong><a href={rvEvent.creator.url}>{rvEvent.creator.name}</a></strong>
//                                        case _ =>
//                                            <strong><a href={rvEvent.creator.url}>{rvEvent.creator.name}</a></strong>
//                                    }
//                                    }
//                                    {
//                                    if (rvEvent.creator.isInactive)
//                                        <div class="icon-eye-close" title="User is inactive"></div>
//                                    }
//                                    <div class="post-attributes">{creationAge}</div>
//                                </td>
//                            </tr>
//                            <tr class="word-preview">
//                                <td>Detail</td><td>:</td><td><p>{Unparsed(TextCompiler.compileMessage(rvEvent.content))}</p></td>
//                            </tr>
//                            <tr>
//                                <td>Attenders</td><td>:</td>
//                                <td id="AttendersContainer">
//                                    {attendersNodeSeq}
//                                </td>
//                            </tr>
//                        </table>
//
//
//                    </div>
//                    <div>
//                        {eventAttributesNoLink}
//                    </div>
//                    {
//                    if (rvEvent.lastEditTime > 0){
//                        <div class="post-attributes" style="margin-top: 15px;">
//                            <em>
//
//                                {
////                                if(rvEvent.revision > 0){
////                                    <span>revision {rvEvent.revision}</span><br />
////                                }else NodeSeq.Empty
//                                }
//
//                                {
//
//                                (rvEvent.lastEditor map { ue =>
//
//                                    Text("last edited by ") ++ ue.snipLinkNoSign
//
//                                } getOrElse(Text("last edited "))) ++ Text(" at " + rvEvent.getLastEditTimeStd +
//                                    " (" + rvEvent.getLastEditTimeAge + ")") ++
//                                    ({if(rvEvent.editReason.trim.length > 1) Text(" reason: " + rvEvent.editReason) else NodeSeq.Empty})
//
//
//                                }
//
//                            </em>
//                        </div>
//                    }else NodeSeq.Empty
//                    }
//                </div>
//            } getOrElse
//                NodeSeq.Empty
//        }
//
//        def deletedUrl:String = {
//            "/preview/deleted/content/%s".format(event.getId)
//        }
//
//        def cuIsAdmin = {
//            currentUser.map { cu =>
//                val admin = (cu.role == model.UserRole.ADMIN || cu.role == model.UserRole.SUPER_ADMIN)
//                val adminChannel = {
//                    model.Forum.getById(event.origin.getId).map { ch =>
//                        ch.getStaffAbility(cu).contains(Ability.DELETE_POST) || ch.isOwner(cu)
//                    }.getOrElse(false)
//                }
//
//                admin || adminChannel
//
//            }.getOrElse(false)
//        }
//
//        def cuIsSuperAdmin = {
//            currentUser.map { cu =>
//                cu.role == model.UserRole.ADMIN || cu.role == model.UserRole.SUPER_ADMIN
//            }.getOrElse(false)
//        }
//
//        def deletedPreviewNodeSeq:NodeSeq = {
//            if (cuIsAdmin) {
//                <div class={"clearfix single-event-view alert alert-warning"}>
//                    This event published in {event.origin.snipLink} group
//                    <div>
//                        <table class="table table-striped">
//                            <tr class="word-preview">
//                                <td>Event</td><td>:</td><td>{event.title}</td>
//                            </tr>
//                            <tr class="word-preview">
//                                <td>Location</td><td>:</td><td>{event.location.truncate(1000)}</td>
//                            </tr>
//                            <tr>
//                                <td>When</td><td>:</td>
//                                <td>
//                                    {
//                                    if (event.expired){
//                                        <span class="label label-important">EXPIRED</span>
//                                    }else
//                                        NodeSeq.Empty
//                                    }
//                                    {new PrettyTime(new Date()).format(new Date(event.date))} - {new Date(event.date).toStdFormatWithTime}
//                                </td>
//                            </tr>
//                            <tr>
//                                <td>Prepared by</td><td>:</td>
//                                <td>
//                                    {event.creator.thumbnailElmSmall}
//                                    {
//                                    event.origin match {
//                                        case ch:model.Forum =>
//                                            if(ch.isOwner(event.creator))
//                                                <strong><a href={event.creator.url} style="color: #cF0809;">
//                                                    {event.creator.name}</a></strong>
//                                            else if(ch.isStaff(event.creator))
//                                                <strong><a href={event.creator.url} style="color: #8E1AED;">
//                                                    {event.creator.name}</a></strong>
////                                                    <em class="post-attributes" style="margin-left: 10px;">{ch.getStaffAttribute(event.creator).title}</em>
//                                            else
//                                                <strong><a href={event.creator.url}>{event.creator.name}</a></strong>
//                                        case _ =>
//                                            <strong><a href={event.creator.url}>{event.creator.name}</a></strong>
//                                    }
//                                    }
//                                    {
//                                    if (event.creator.isInactive)
//                                        <div class="icon-eye-close" title="User is inactive"></div>
//                                    }
//                                    <div class="post-attributes">{creationAge}</div>
//                                </td>
//                            </tr>
//                            <tr class="word-preview">
//                                <td>Detail</td><td>:</td><td><p>{Unparsed(TextCompiler.compileMessage(event.content))}</p></td>
//                            </tr>
//                            <tr>
//                                <td>Attenders</td><td>:</td>
//                                <td id="AttendersContainer">
//                                    {attendersNodeSeq}
//                                </td>
//                            </tr>
//                        </table>
//
//
//                    </div>
//                    <div>
//                        {eventAttributesNoLink}
//                    </div>
//                    {
//                    if (event.lastEditTime > 0){
//                        <div class="post-attributes" style="margin-top: 15px;">
//                            <em>
//
//                                {
////                                if(event.revision > 0){
////                                    <span>revision {event.revision}</span><br />
////                                }else NodeSeq.Empty
//                                }
//
//                                {
//
//                                (event.lastEditor map { ue =>
//
//                                    Text("last edited by ") ++ ue.snipLinkNoSign
//
//                                } getOrElse(Text("last edited "))) ++ Text(" at " + event.getLastEditTimeStd +
//                                    " (" + event.getLastEditTimeAge + ")") ++
//                                    ({if(event.editReason.trim.length > 1) Text(" reason: " + event.editReason) else NodeSeq.Empty})
//
//
//                                }
//
//                            </em>
//                        </div>
//                    }else NodeSeq.Empty
//                    }
//                </div>
//            } else
//                NodeSeq.Empty
//        }
//
//        def deletedSnipLink:NodeSeq={
//            if (cuIsAdmin) {
//                 <a href={deletedUrl} target="_blank">{event.title.truncate(20)}</a>
//            } else
//                <a href=""> ---- </a>
//        }
//
//        def attendersNodeSeq:NodeSeq = {
//            <xml:group>
//                Positive ({event.getAttendersCount(model.AttenderKind.POSITIVE)}): <br />
//                <p style="margin-left: 10px;">
//                    {
//                    event.getAttenders(model.AttenderKind.POSITIVE).map { u =>
//                        if(isLoggedIn && currentUser.get == u)
//                            <span id="attend-id-you">you</span>:NodeSeq
//                        else
//                            <a href={u.url}>{u.name}</a>:NodeSeq
//                    }.reduceLeftOption(_ ++ Text(", ").asInstanceOf[NodeSeq] ++  _).getOrElse(NodeSeq.Empty)
//                    }
//                </p>
//                <br />
//
//                Maybe ({event.getAttendersCount(model.AttenderKind.MAYBE)}): <br />
//
//                <p style="margin-left: 10px;">
//                    {
//                    event.getAttenders(model.AttenderKind.MAYBE).map { u =>
//                        if(isLoggedIn && currentUser.get == u)
//                            <span id="attend-id-you">you</span>:NodeSeq
//                        else
//                            <a href={u.url}>{u.name}</a>:NodeSeq
//                    }.reduceLeftOption(_ ++ Text(", ").asInstanceOf[NodeSeq] ++  _).getOrElse(NodeSeq.Empty)
//                    }
//                </p>
//            </xml:group>
//        }
//
//
//        def creationAge =
//            <span class="p-attr">
//                <span class="icon-time"></span>
//                <cite>{event.getCreationAge}</cite>
//            </span>
//
//        def responseCount =
//            <xml:group>
//                {event.getResponseCount} <span class="icon-comments" data-toggle="tooltip" title="Response" data-placement="bottom" rel="tooltip" ></span>
//            </xml:group>
//
//        def titleUrl:String = {
//            val rv = event.title.urlize + "-" + event.getId
//            rv.toLowerCase
//        }
//
//        def eventAttributes(embedMode:Boolean) = {
//            val classLikeCount = "like-count-"+event.getId
//            <div class="post-attributes">
//                {creationAge}
//                -
//                <span class="p-attr"><span class={classLikeCount} >{eventLikers}</span> <span>{likeClick}</span></span>
//                -
//                <span class="p-attr"><a href={url(embedMode)} data-embed-url={url(embedMode = false)}>{responseCount}</a></span>
//            </div>
//        }
//
//        def eventAttributesNoLink = {
//            val classLikeCount = "like-count-"+event.getId
//            <div class="post-attributes">
//                {creationAge}
//                -
//                <span class="p-attr"><span class={classLikeCount} >{eventLikers}</span> <span>{likeClick}</span></span>
//                -
//                <span class="p-attr">{responseCount}</span>
//            </div>
//        }
//
//        def url(embedMode:Boolean):String = {
//            val rv =
//            if(event.origin!=null){
//                if(event.origin.kind == OriginKind.SUB_FORUM)
//                    "%s/event/%s.html".format(event.origin.url, titleUrl)
//                else
//                    "/u/%s/event/%s.html".format(event.origin.getName, titleUrl)
//            }else{
//                if (event.creator!=null)
//                    "/u/%s/event/%s.html".format(event.creator.getName,titleUrl)
//                else
//                    "/null/event/%s.html".format(titleUrl)
//            }
//
//            if (embedMode)
//                rv + "?embed=1"
//            else
//                rv
//        }
//
//        def relativeUrl = {
//            if(event.origin!=null){
//                if(event.origin.kind == OriginKind.SUB_FORUM)
//                    "forum/%s/event/%s.html".format(event.origin.getName.toLowerCase, titleUrl)
//                else
//                    "u/%s/event/%s.html".format(event.origin.getName.toLowerCase, titleUrl)
//            }else{
//                if (event.creator!=null)
//                    "u/%s/event/%s.html".format(event.creator.getName.toLowerCase,titleUrl)
//                else
//                    "null/event/%s.html".format(titleUrl)
//            }
//        }
//
//
//        def getTitle(truncate:Int=0):String = {
//            val rv = event match {
//                case e:model.Event =>
//                    e.title.truncate(truncate)
//            }
//            rv
//        }
//
//
//        def editUrl = {
//            if(event.origin!=null){
//                "%s/event/%s/edit".format(event.origin.url, event.getId)
//            }else{
//                if (event.creator!=null)
//                    "/u/%s/event/%s/edit".format(event.creator.getName.toLowerCase, event.getId)
//                else
//                    "/null/event/%s/edit".format(titleUrl)
//            }
//        }
//
//        /**
//         * @deprecated we don't know the base domain for absolute url yet.
//         * @return
//         */
//        def absoluteUrl:String = {
//            url(embedMode = false)
//        }
//
//        def snipLink = {
//            <a href={url(embedMode = false)} data-embed-url={url(embedMode = true)}>{event.title.truncate(20)}</a>
//        }
//
//        def eventLikers: NodeSeq
//
//        def supportUnsupportButton(srcUser:model.User, targetUser:model.User, ns:NodeSeq):NodeSeq
//
////        def toHomeStreamNodeSeq(embedMode: Boolean, perspective:String): NodeSeq = {
////
////            // @TODO(*): implement ini seperti di PostBuilder dan PicBuilder
////
////            <div></div>
////        }
//    }
}

object EventBuilder extends EventBuilder {
//    class UnimplementedEventBuilderWrapper(ev:model.Event) extends EventNodeSeqWrapperBase(ev) {
//        def currentUser = None
//
//        def likeClick = {
//            throw NotImplementedException("likeClick belum di set, mohon override fungsi ini " +
//                "dengan implementasi mu sendiri.")
//        }
//
//        def isLoggedIn = {
//            throw NotImplementedException("isLoggedIn belum di set, mohon override fungsi ini" +
//                "dengan implementasi mu sendiri.")
//        }
//
//        def supportUnsupportButton(srcUser: User, targetUser: User, ns: NodeSeq) = {
//            throw NotImplementedException("supportUnsupportButton belum di set, mohon override fungsi ini " +
//                "dengan implementasi mu sendiri.")
//        }
//
//        def eventLikers: NodeSeq = {
//            throw NotImplementedException("eventLikers belum di set, mohon override fungsi ini " +
//                "dengan implementasi mu sendiri.")
//        }
//
//        protected implicit def contentDeletableAutoConverter(deletable: Deletable): ContentDeletableNodeSeqWrapperBase = {
//            throw NotImplementedException("contentDeletableAutoConverter belum di set, mohon override fungsi ini " +
//                "dengan implementasi mu sendiri.")
//        }
//
//        protected implicit def contentMovableAutoConverter(movable: Movable): ContentMovableNodeSeqWrapperBase = {
//            throw NotImplementedException("contentDeletableAutoConverter belum di set, mohon override fungsi ini " +
//                "dengan implementasi mu sendiri.")
//        }
//
//        def buildReportButton(): NodeSeq = NodeSeq.Empty
//        protected def buildShareButton(embedMode: Boolean) : NodeSeq = NodeSeq.Empty
//        protected def buildRetalkButton(embedMode: Boolean) : NodeSeq = NodeSeq.Empty
//        protected def buildLockEventButton() : NodeSeq = NodeSeq.Empty
//        protected def buildCloseEventButton() : NodeSeq = NodeSeq.Empty
//    }
}
