///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model.jsonbuilder
//
//import net.liftweb._
//import json._
//import JsonDSL._
//import com.ansvia.digaku.model._
////import com.ansvia.digaku.restapi.SimpleTime
//
///**
// * Author: hani (<EMAIL>)
// *
// */
//trait StatusBuilder {
//
//    protected class StatusWrapper(status:UserStatus) {
//
//       def toJson(userRef:Option[User]=None):JValue = {
//
//            ("id" -> status.getId) ~
//            ("content" -> status.content) ~
//            ("creator" -> UserBuilder.buildUserInfoSimple(status.creator)) ~
//            ("creation_time" -> (status.creationTime / 1000)) ~
//            ("creation_time_age" -> status.getCreationAge)
//
//        }
//
//    }
//
//    implicit def implicitStatusWrapper(status:UserStatus) = new StatusWrapper(status)
//
//}
//
//object StatusBuilder extends StatusBuilder
