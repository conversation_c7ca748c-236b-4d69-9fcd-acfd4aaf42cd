/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.builder

import scala.xml.NodeSeq
import com.ansvia.digaku.model.StreamObject

/**
 * Author: robin
 *
 */
trait StreamObjectBuilder {

    protected abstract class StreamObjectBuilderWrapper(so:StreamObject) extends StreamView {

    }

}


abstract class StreamView {
    def toStreamNodeSeq(embedMode:Boolean):NodeSeq

//    def toHomeStreamNodeSeq(embedMode:<PERSON>ole<PERSON>, perspective:String):NodeSeq
}
