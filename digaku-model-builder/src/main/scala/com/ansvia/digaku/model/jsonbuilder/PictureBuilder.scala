/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.jsonbuilder

import com.ansvia.digaku.model._
import com.ansvia.perf.PerfTiming
import net.liftweb.json.JsonDSL._
import net.liftweb.json._

/**
 * Author: robin (<EMAIL>)
 */

trait PictureBuilder {




    abstract class PictureBuilder(post:PictureBase) {

        def toJson(userRef:Option[User], overwriteId:Option[Long], templateMode:Boolean, addParams:(String, String)*): JValue
        
    }

    
    abstract class JsonPictureWrapper(pic:Picture) extends PictureBuilder(pic) with PerfTiming /*with AppBuilder*/ {

        implicit protected def postAutoConverter(_pic:Picture):com.ansvia.digaku.model.builder.PictureBuilder.PictureWrapperBase
//        protected val oembedHelper:OembedHelper
        implicit protected def basicContentConverter(_bc:BasicContentBuilder.BasicContentType):com.ansvia.digaku.model.jsonbuilder.BasicContentBuilder.JsonBasicContentWrapper


        protected val SHORT_URL:String

        protected val urlShorter:(String) => String

        val picSmall = if (pic.smallScaledUrl.isEmpty) {
            pic.smallUrl
        } else {
            pic.smallScaledUrl
        }

        val picMedium = if (pic.mediumScaledUrl.isEmpty) {
            pic.mediumUrl
        } else {
            pic.mediumScaledUrl
        }


        def toJson(userRef:Option[User], overwriteId:Option[Long], templateMode:Boolean,
                   addParams:(String, String)*): JValue = {

            val shortUrl =
                if (!pic.originIsPrivate) {
                    SHORT_URL + urlShorter(pic.url(false))
                } else {
                    ""
                }


            pic.basicContent(userRef, overwriteId, templateMode) ~ ("kind" -> "picture") ~
                ("content" -> pic.title) ~
                ("picture" ->
                    ("small" -> picSmall) ~
                        ("medium" -> picMedium) ~
                        ("large" -> pic.largeUrl)
                    ) ~
                ("via" -> timing("get post via") { pic.getPostVia.map { app =>
                    ("id" -> app.getId) ~
                        ("name" -> app.name)
                }}) ~
                ("share_url" -> shortUrl)

        }


        /**
         * to be used for displaying picture info only, without creator
         */
        def toJsonSimpleWithNoCreator:JValue = {
            ("id" -> pic.getId) ~
                ("content" -> pic.title) ~
                ("picture" ->
                    ("small" -> picSmall) ~
                        ("medium" -> picMedium) ~
                        ("large" -> pic.largeUrl)
                    ) ~
                ("response_count" -> pic.getResponseCount) ~
                ("cools_count" -> pic.getLikesCount)
        }

        /**
         * only to return picture value
         * @return
         */
        def toJsonPicUrlOnly:JValue = {
            ("small" -> picSmall) ~
                ("medium" -> picMedium) ~
                ("large" -> pic.largeUrl)
        }
    }


}


object PictureBuilder extends PictureBuilder
