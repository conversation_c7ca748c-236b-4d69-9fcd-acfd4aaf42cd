/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.jsonbuilder

import com.ansvia.digaku.Types.IDType
import com.ansvia.digaku.model
import com.ansvia.digaku.notifications.{NotificationBase, PersistentNotification}
import net.liftweb._
import json._
import JsonDSL._
import com.ansvia.digaku.model.User
//import com.ansvia.digaku.restapi.SimpleTime
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.notifications.impl._
import com.ansvia.digaku.exc._

/**
 * Author: robin
 *
 */
trait NotificationBuilder {

    abstract class NotificationBuilder(ntf:PersistentNotification) {
        def notificationBaseToJValue(cu:User, withPostReplacer:<PERSON>olean = true, withMicros:Boolean = false):JObject
    }
}

object NotificationBuilder extends NotificationBuilder {
    val RESPONSE_STR = "response"
    val POST_STR = "post"
    val USER_STR = "user"
    val FORUM_STR = "forum"
}
