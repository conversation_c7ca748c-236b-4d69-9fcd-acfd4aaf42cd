///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model.builder
//
//import scala.xml.NodeSeq
//import com.ansvia.digaku.model
//import com.tinkerpop.blueprints.Edge
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.model.{PictureBase, Picture}
//
///**
// * Author: robin
// *
// */
//trait ActivityBuilder {
//
//
//    abstract class ActivityNodeSeq(edge:Edge) extends model.ActivityBase[NodeSeq](edge) {
//
//        implicit def eventAutoConvert(event:model.Event):EventBuilder.EventNodeSeqWrapperBase
//        implicit def postAutoConvert(post:model.Post):PostBuilder.PostNodeSeqWrapperBase
//        implicit def originAutoConvert(origin:model.Origin[GraphType]): OriginBuilder.OriginWrapperBase =
//            new OriginBuilder.UnimplementedOriginWrapper(origin)
//        implicit def picAutoConvert(pic: PictureBase) =
//            new com.ansvia.digaku.model.builder.PictureBuilder.UnimplementedPictureWrapperBase(pic)
//
//        // current user
//        // implement this
//        val currentUser:Option[model.User]
//
//        def compileJoin(user: model.User, group: model.Group) = {
//            <span>{
//                    if (user == currentUser.get ){
//                        "you"
//                    }else {
//                        <a href={user.url}>{user.name}</a>
//                    }
//                    } join {group.snipLink}</span>
//        }
//
//        def compileSupport(userA: model.User, userB: model.User) = {
//            <span>{
//                    if (userA == currentUser.get ){
//                        "you"
//                    }else {
//                        <a href={userA.url}>{userA.name}</a>
//                    }
//                } support {userB.snipLink}</span>
//        }
//
//        def compileResponse(user: model.User, post: model.Post) = {
//            <span>{
//                    if (user == currentUser.get ){
//                        "you"
//                    }else {
//                        <a href={user.url}>{user.name}</a>
//                    }
//
//                } response to post {post.snipLink(false)}</span>
//        }
//
//        def compilePost(user: model.User, post: model.Post) = {
//            <span>{
//                    if (user == currentUser.get ){
//                        "you"
//                    }else {
//                        <a href={user.url}>{user.name}</a>
//                    }
//                } wrote post {post.snipLink(false)}</span>
//        }
//
//        def compileEvent(user: model.User, event: model.Event) = {
//            <span>{
//                    if (user == currentUser.get ){
//                        "you"
//                    }else {
//                        <a href={user.url}>{user.name}</a>
//                    }
//                } create event <a href={event.url(false)} data-embed-url={event.url(true)}>{event.title}</a></span>
//        }
//
//        def compilePicture(user: model.User, picture: model.Picture) = {
//            <span>{
//                if (user == currentUser.get ){
//                    "you"
//                }else {
//                    <a href={user.url}>{user.name}</a>
//                }
//                } upload photo {picture.snipLink(false)}</span>
//        }
//    }
//
////    private class ActivityWrapper(edge:Edge){
////        def toActivity = new ActivityNodeSeq(edge).compile
////    }
////
////    implicit def implicitActivityWrapper(edge:Edge) = new ActivityWrapper(edge)
//
//}
//
//object ActivityBuilder extends ActivityBuilder
