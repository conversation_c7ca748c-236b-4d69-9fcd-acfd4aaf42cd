/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model.jsonbuilder


import com.ansvia.digaku.model.builder.Normalizer
import net.liftweb._
import json._
import JsonDSL._
import com.ansvia.digaku.model.{StickyState, Forum, User}
import com.ansvia.digaku.database.GraphCompat.tx
import com.ansvia.digaku.utils.ForumSettings._
import com.ansvia.digaku.model.UserRole

/**
 * Author: robin
 *
 */
trait ChannelBuilder {

    class JsonChannelWrapper(_ch:Forum){
        def toJson(cu:Option[User], templateMode:Boolean, attributes:List[String]=Nil):JObject = {

            tx { t =>

                val ch = _ch.reload()(t)

                var rv = ("id" -> ch.getId) ~
                    ("name" -> ch.name) ~
                    ("desc" -> ch.desc) ~
                    ("parent_id" -> ch.parentForumId) ~
                    ("parent_name" -> ch.getParentForum.map(_.name).getOrElse("")) ~
                    ("is_private" -> ch.privated) ~
                    ("label" -> ch.getPostMarks(0, 10).map(pm => pm.title)) ~
                    ("is_followed_by_me" -> cu.exists(ch.isFollower))


                if (attributes.contains("member_count") || attributes.contains("*"))
                    rv = rv ~ ("member_count" -> ch.getMemberCount())

                if (attributes.contains("follower_count") || attributes.contains("*"))
                    rv = rv ~ ("follower_count" -> ch.getFollowerCount())

                if (attributes.contains("tags") || attributes.contains("*"))
                    rv = rv ~ ("tags" -> ch.tags)

                if (attributes.contains("logo") || attributes.contains("*")) {
                    rv = rv ~ ("logo" -> Normalizer.normalizeUrl(ch.mediumLogoUrl)) ~
                        ("logo_small" -> Normalizer.normalizeUrl(ch.smallLogoUrl)) ~
                        ("logo_large" -> Normalizer.normalizeUrl(ch.largeLogoUrl))
                }


                if (attributes.contains("creator") || attributes.contains("*"))
                    rv = rv ~ ("creator" -> ch.getOwner.map(UserBuilder.buildUserInfoSimple))


                if (attributes.contains("banner") || attributes.contains("*"))
                    rv = rv ~ ("banner" -> Normalizer.normalizeUrl(ch.bannerPicUrl))


                if (templateMode) {
                    rv = rv ~ ("is_staff" -> "%{is_staff}") ~
                        ("is_joined_by_me" -> "%{is_joined_by_me}") ~
                        ("is_bookmarked_by_me" -> "%{is_bookmarked_by_me}")
                }else{
                    rv = rv ~ ("is_staff" -> cu.map(currentUser => ch.isStaff(currentUser))) ~
                        ("is_joined_by_me" -> cu.map(currentUser => ch.isMember(currentUser)))
                }

                rv = rv ~ ("description" -> ch.desc) ~
                    ("tags" -> ch.tags) ~
//                    ("category" -> ch.getGroupCategory.map(_.categoryName)) ~
                    ("features" -> ch.getFeatures.filterNot(_ == "event").toList) ~ //event belum disupport
                    ("size" -> ch.getMaximumFileSizeAllowed) ~
                    ("mime_types" -> ch.getAllowedMimeType) ~
                    ("file_types" -> ch.getAllowedFileTypeList)

                    if (attributes.contains("content_stats") || attributes.contains("*")) {
                        rv = rv ~ ("sticky_post_count" ->
                            ("total" -> ch.stickyPostCount) ~
//                                ("simple_post" -> ch.stickySimplePostCount) ~
                                ("article" -> ch.stickyArticleCount) ~
                                ("picture" -> ch.stickyPictureCount)) ~
                            ("post_count" ->
                                ("total" -> ch.getContentCount) ~
//                                    ("simple_post" -> ch.getSimplePostCount) ~
                                    ("article" -> ch.getArticleCount(StickyState.ALL)) ~
                                    ("picture" -> ch.getPictureCount))
                    }

                    if (attributes.contains("staffs") || attributes.contains("*"))
                        rv = rv ~ ("staffs" -> ch.getStaffs(0, 6).toSeq.map(staff => UserBuilder.buildUserInfoSimple(staff.user))) // last 6 staffs

                    if (attributes.contains("members") || attributes.contains("*"))
                        rv = rv ~ ("members" -> ch.getMembers(0,6).map(UserBuilder.buildUserInfoSimple)) // last 6 members

                    if (attributes.contains("subforums") || attributes.contains("*")) {

                        cu.map { currentUser =>

                            /**
                             * Rule nya adalah:
                             * 1. Semua user bisa melihat forum (tidak ada closed subforum, semuanya open)
                             * 2. Super admin bisa melihat semua subforum
                             * 3. Admin bisa melihat semua subforum
                             * 4. Moderator forum bisa melihat semua subforum di bawah forum yg di moderasinya
                             * 5. Moderator subforum bisa melihat semua subforum yg di moderasi
                             * 6. Member bisa melihat semua forum yg dijoin
                             * 7. User hanya bisa melihat open subforum
                             */

                            val x = ch
                            val f2 = x.getForumArrangementForUser(currentUser, asAdmin = false)
                            val subForumList = if (f2.nonEmpty) f2 else x.getSubForumForUser(currentUser, asAdmin = false).toList

                            rv = rv ~ ("subforums" -> subForumList.map { sf =>
                                ("id" -> sf.getId) ~
                                    ("name" -> sf.name) ~
                                    ("logo" -> Normalizer.normalizeUrl(sf.mediumLogoUrl)) ~
                                    ("logo_small" -> Normalizer.normalizeUrl(sf.smallLogoUrl)) ~
                                    ("logo_large" -> Normalizer.normalizeUrl(sf.largeLogoUrl)) ~
                                    ("description" -> sf.desc) ~
                                    ("label" -> sf.getPostMarks(0, 10).map(pm => pm.title)) ~
                                    ("size" -> sf.getMaximumFileSizeAllowed) ~
                                    ("mime_types" -> sf.getAllowedMimeType) ~
                                    ("file_types" -> sf.getAllowedFileTypeList)
                            })
                        }.getOrElse {
                            rv = rv ~ ("subforums" -> List.empty[String])
                        }
                    }

                if(attributes.contains("announcement") || attributes.contains("*")) {
                    rv = rv ~ ("announcement" -> ch.announcementText)
                }

                rv
            }

        }

        /**
         * Mengembalikan group dengan informasi minimal
         * salah satunya digunakan untuk auto complete
         * @param cu
         * @return
         */
        def toJsonMinimal(cu:Option[User]=None, withSubforum:Boolean=false):JObject = {
            tx { t =>
                val ch = _ch.reload()(t)

                if (withSubforum) {
                    ("id" -> ch.getId) ~
                        ("name" -> ch.name) ~
                        ("subforums" -> ch.getSubForums.toList.map { sf =>
                            ("id" -> sf.getId) ~
                                ("name" -> sf.name)
                        })
                } else {
                    ("id" -> ch.getId) ~
                        ("name" -> ch.name)
                }
            }
        }
    }

    implicit def implicitChannelToJObject(_ch:Forum)(implicit cu:Option[User]):JObject = {
//        tx { t =>
//
//            val ch = _ch.reload()(t)
//
//            var rv =
//                ("id" -> ch.getId) ~
//                    ("name" -> ch.name) ~
//                    ("desc" -> ch.desc) ~
//                    ("member_count" -> ch.getMemberCount()) ~
//                    ("tags" -> ch.tags) ~
//                    ("logo" -> ch.mediumLogoUrl) ~
//                    ("logo_small" -> ch.smallLogoUrl) ~
//                    ("logo_large" -> ch.largeLogoUrl) ~
//                    ("creator" -> ch.getOwner.map(buildUserInfoSimple))
//
//            if (cu != null){
//                rv = rv ~ ("is_joined_by_me" -> ch.isMember(cu))
//            }
//
//            rv
//        }

        new JsonChannelWrapper(_ch).toJson(cu, false, List("member_count", "tags", "logo", "creator"))

    }

    implicit def implicitChannelsToJValue(chs:Iterator[Forum])(implicit cu:Option[User]):JValue = {
        chs.toSeq.map(x => x:JObject) //.reduceLeftOption(_ ~ _).getOrElse(JArray(List.empty[JValue])):JValue
    }
    implicit def implicitChannelsToJValue(chs:Seq[Forum])(implicit cu:Option[User]):JValue = {
        implicitChannelsToJValue(chs.toIterator)
    }

    implicit def implicitChannelInfoToJValue(ch:Forum) = new JsonChannelWrapper(ch)
}

object ChannelBuilder extends ChannelBuilder

