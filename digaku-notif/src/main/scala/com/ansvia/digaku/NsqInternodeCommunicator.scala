/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.cluster.InternodeCommunicator
import com.google.common.io.BaseEncoding
import ly.bit.nsq.lookupd.BasicLookupd
import ly.bit.nsq.syncresponse.{SyncResponse<PERSON><PERSON><PERSON>, SyncResponseReader}
import ly.bit.nsq.{Message, NSQProducer}

/**
 * Author: robin (<EMAIL>)
 */

/**
 * Internode communicator implementation using NSQ as the backend.
 * @param initiator this app id, used as address by other nodes.
 * @param publisherHost nsq publisher host.
 * @param topic nsq topic
 */
abstract class NsqInternodeCommunicator(initiator:String, publisherHost:String, topic:String)
    extends InternodeCommunicator with SyncResponseHandler with Slf4jLogger {

    import net.liftweb.json._

    private implicit val formats = Serialization.formats(NoTypeHints)

    require(publisherHost != null, "publisherHost is null")
    require(publisherHost != "", "publisherHost not set")

    private lazy val nsqProducer = new NSQProducer("http://" + publisherHost, topic)
    private lazy val nsqReader = new SyncResponseReader(topic, initiator, this)

    nsqReader.addLookupd(new BasicLookupd("http://" + Digaku.config.nsqLookupHost))

    /**
     * Send payload to specific destination node using address.
     * @param addr node address where payload should send to,
     *             this is node id set in configuration file (id).
     *
     * @param payload payload to send in bytes.
     */
    override def send(addr: String, payload: Array[Byte]){
        nsqProducer.putAsync(toJson(addr, payload))
    }

    /**
     * Send payload to specific group or all nodes.
     * @param prefix node id prefix, eg: "api-",
     *               or for broadcast to all nodes use wildcard "*" as the prefix.
     *
     *
     * @param payload payload to send in bytes.
     */
    override def sendPrefix(prefix: String, payload: Array[Byte]){
        send("prefix:" + prefix, payload)
    }


    override def handleMessage(msg: Message): Boolean = {

        val str = new String(msg.getBody)
        val json = parse(new String(msg.getBody))

        debug("got data (before encoded): " + str)

        val initiator = try {
            (json \ "initiator").extract[String]
        } catch {
            case e:Exception =>
                println("Cannot extract source for data: " + str + " (to get initiator info)")
                throw e
        }

        val destAddr = try {
            (json \ "dest").extract[String]
        } catch {
            case e:Exception =>
                println("Cannot extract source for data: " + str + " (to get dest address info)")
                throw e
        }

        if (destAddr == initiator || destAddr == "*" || destAddr == "prefix:*"){
            // accept

            val payloadb64 = try {
                (json \ "payload").extract[String]
            } catch {
                case e:Exception =>
                    println("Cannot extract source for data: " + str + " (to get dest address info)")
                    throw e
            }

            val payload = BaseEncoding.base64().decode(payloadb64)

//            val payloadUncompressed = Snappy.uncompress(payloadCompressed)

            debug(s"got data from `$initiator` : ${new String(payload)}")

            receive(initiator, payload)

        }else
            true

    }

    private val template = """
                             |{"initiator":"%s",
                             |"dest":"%s",
                             |"payload":"%s"}
                           """.stripMargin

    private def toJson(addr:String, payload:Array[Byte]) = {
        val b64Payload = BaseEncoding.base64().encode(payload)
        template.format(initiator, addr, b64Payload)
    }




    override def close(): Unit ={
        nsqReader.shutdown()
    }
}

