/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.nsq

/**
 * Author: robin
 * Date: 5/30/14
 * Time: 5:47 PM
 *
 */
class NsqDataCompressor(data:String) {

    // {"id":1,"cl":"com.ansvia.digaku.web.notification.NewPostArrived","userId":2311552,"postId":211958000,"postClass":"com.ansvia.digaku.model.SimplePost"}

    def compress() = {
        NsqDataCompressor.compress(data)
    }

    def decompress() = {
        NsqDataCompressor.decompress(data)
    }


}

object NsqDataCompressor {

    private val dDict: Seq[(String, String)] = Seq(
        ("""{"id":""",  "\29"),
        ("\"cl\":\"com.ansvia.digaku",  "\12"),
        (",\"userId\":","\28"),
        (",\"postId\":","\14"),
        (",\"postClass\":\"com.ansvia.digaku.model.Picture","\15"),
        (",\"postClass\":\"com.ansvia.digaku.model","\16"),
        ("com.ansvia.digaku.notifications.impl","\30"),
        ("NewPostArrived", "\17"),
        ("SendFor", "\18"),
        ("com.ansvia.digaku.model",  "\19"),
        ("web.notification.",  "\20"),
        (".SimplePost",  "\21"),
        ("{\"",  "\22"),
        ("\"}",  "\23"),
        ("notifClass\":",  "\24"),
        ("notifId\":",  "\25"),
        ("kind\":",  "\26"),
        ("\"UpdateNotifCount\":",  "\27"),
        ("\"LatestNewsFor\":",  "\28")
    )

    def compress(data:String) = {

        var rv = data

        dDict.foreach { case (k,v) =>
//            println("k,v: " + k + ", " + v)
            rv = rv.replace(k, v)
        }

        rv

    }

    def decompress(data:String) = {

        var rv = data

        dDict.foreach { case (k,v) =>
//            println("k,v: " + k + ", " + v)
            rv = rv.replace(v, k)
        }

        rv

    }

}