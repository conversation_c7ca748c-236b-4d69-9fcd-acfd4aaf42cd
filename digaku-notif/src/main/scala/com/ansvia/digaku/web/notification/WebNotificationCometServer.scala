/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.web.notification

import net.liftweb.actor.LiftActor
import net.liftweb.http.ListenerManager
import com.ansvia.digaku.notifications.{NotificationBase, PersistentNotification}
import com.ansvia.digaku.model.{User, NotificationState}
import com.ansvia.digaku.helpers.DbAccess

/**
 * Author: robin
 *
 */
abstract class WebNotificationCometServerBase extends LiftActor with ListenerManager with DbAccess {

    protected def currentUser:Option[User]

    def notifs:List[NotificationBase] = currentUser.map { u =>
        u.getNotifications(0, 19, NotificationState.ALL)
    }.getOrElse(Nil)

    protected def createUpdate = notifs

    override protected def lowPriority: PartialFunction[Any, Unit] = {
        case ntf:TestNotif =>
            sendListenersMessage(ntf)

        case ntf:PersistentNotification =>
//            notifs ::= ntf
            sendListenersMessage()

        case sf:SendFor =>
            sendListenersMessage(sf)

//        case mf:MessageFor =>
//            sendListenersMessage(mf)

        case npa:NewPostArrived =>
            sendListenersMessage(npa)

        case npa:DeactiveUser =>
            sendListenersMessage(npa)

        case sif:SendInviteFor =>
            sendListenersMessage(sif)

        case ntf:UpdateNotifCount =>
            sendListenersMessage(ntf)

        case ums:UpdateMtSession =>
            sendListenersMessage(ums)

        case ntf:ErrorMessage =>
            sendListenersMessage(ntf)

        case ntf:KillOther =>
            sendListenersMessage(ntf)

//        case ntf:UpdateUnreadMessageCount =>
//            sendListenersMessage(ntf)


    }


}


object SystemInternalCometServer extends LiftActor with ListenerManager with DbAccess {
    protected def createUpdate: Any = None

    override protected def lowPriority: PartialFunction[Any, Unit] = {
        case ie:IndexingStatus => sendListenersMessage(ie)
        case ess:ExportStatisticState => sendListenersMessage(ess)
        case esp:ExportStatisticProgress => sendListenersMessage(esp)
        case sdd:SetDwhDirectoryStatus => sendListenersMessage(sdd)
    }
}



