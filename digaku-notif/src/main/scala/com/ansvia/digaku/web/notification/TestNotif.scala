/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.web.notification

import com.ansvia.digaku.notifications.{NotificationSendHandler, PersistentNotification}
import com.ansvia.digaku.model.User
import com.ansvia.digaku.notifications.impl.{NotifKind, NotifPartition}

/**
 * Author: robin
 *
 */
case class TestNotif(msg:String) extends PersistentNotification(NotifKind.DUMMY) {

    val partition = NotifPartition.ANY

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable = false

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    def renderFor(user: User) = "%s %s".format(msg, user.name)

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler) = np.name == "web"

    def isValid = true

    def userAccept(user: User) = true
}
