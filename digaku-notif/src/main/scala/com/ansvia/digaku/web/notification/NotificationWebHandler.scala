/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.web.notification

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types._
import com.ansvia.digaku.event.impl.IndexEvent
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.{Streamable, User}
import com.ansvia.digaku.notifications.impl._
import com.ansvia.digaku.notifications._
import com.ansvia.perf.Throttle
import com.ansvia.util.duration._
import scala.language.reflectiveCalls


/**
 * Author: robin
 *
 * Untuk dispatch notifikasi ke web
 * menggunakan comet untuk push notification-nya.
 */

/**
 * Web actor message digunakan untuk cross node communication.
 * @param name message name.
 */
abstract class WebActorMessage(val name:String) {
    var initiator = "0"
}


trait CustomImplementation {
    this: WebActorMessage =>

    def toNsqMessage:String
}


case class SendFor(user:User, notif:NotificationBase) extends WebActorMessage("send-for")
case class SendInviteFor(user:User, notif:NotificationBase) extends WebActorMessage("send-invite-for")

case class LatestNewsFor(news: NewsNotifications)extends WebActorMessage("latest-news-for")

/**
 * web actor message ini digunakan untuk notifikasi apabila ada new post.
 * @param user yang akan menerima notifikasi.
 * @param streamable content object/post yang baru arrive.
 */
case class NewPostArrived(user:User, streamable:Streamable[IDType]) extends WebActorMessage("new-post-arrived")

case class UpdateNotifCount(user:User) extends WebActorMessage("update-notif-count")

case class SendAnnouncement(notif:NotificationBase) extends WebActorMessage("update-notif-count")

case class DeactiveUser(user:User) extends WebActorMessage("deactive-user")

// digunakan untuk logout yang lain ketika login dengan user yang sama
case class KillOther(user: User, accessTime:Long, ref:String) extends WebActorMessage("kill-other-user")

/**
 * digunakan untuk update MtSessionVar ini digunakan ketika suspend user
 * @param user user to update it session.
 */
case class UpdateMtSession(user:User) extends WebActorMessage("update-mt-session")

case class IndexingStatus(ie:IndexEvent) extends WebActorMessage("indexing-status")
case class ErrorMessage(message:String, targetUser:User) extends WebActorMessage("error-message")

/**
 * Digunakan untuk mengirim notifikasi session timeout
 * @param jSessionId JSESSIONID cookie value
 */
case class SessionTimeout(jSessionId: String) extends WebActorMessage("session-timeout")

/**
  * Digunakan untuk mengirim notification bahwa export statistic sedang dijalankan
  * @param kind
  * @param path
  * @param fromDate
  * @param toDate
  * @param creationTime
  */
case class ExportStatisticState(kind:Int, path:String, fromDate:String, toDate:String, creationTime:Long)
    extends  WebActorMessage("export-statistic-state")

/**
  * Notification untk progress export statistic yang sedang berjalan
  * @param actionLogId
  * @param kind
  * @param path
  * @param fromDate
  * @param toDate
  * @param desc
  * @param progress
  */
case class ExportStatisticProgress(actionLogId:Long, kind:Int, path:String, fromDate:String, toDate:String, desc:String, progress:Double)
    extends  WebActorMessage("export-statistic-progress")

/**
  * Notification untuk status setelah pengubahan directory DWH
  * @param path
  * @param desc
  * @param success
  */
case class SetDwhDirectoryStatus(path:String, desc:String, success:Boolean) extends  WebActorMessage("export-statistic-progress")

/**
 * Notification handler di layer web.
 * Ini bertugas mengirimkan notifikasi via web comet
 * sehingga notifikasi bisa realtime di web.
 * Juga mengirimkan notifikasi ke email pada jenis-jenis notifikasi tertentu.
 */
abstract class NotificationWebHandlerBase(gateway:{def !(data:Any)})
    extends NotificationSendHandler with DbAccess with Throttle with Slf4jLogger {

    val name = "web"

    // throttle clean up drop duration
    override val dropDuration = 1.days

    // how long duplicated notification will be avoided
    protected val throttleDuration = 12.hours

    debug("up")

    def dispatch:PartialFunction[NotificationBase, Unit] = {
        case d:PersistentNotification =>
            if (d.hash != "") {
                throttle(d.hash, 1, throttleDuration) {
                    asyncDispatchInternal.apply(d)
                }
            } else {
                asyncDispatchInternal.apply(d)
            }
        case d:NonPersistentNotification =>
            asyncDispatchInternal.apply(d)

    }

    protected def sendEmail(ntf:PersistentNotification)

    protected def gatewaySend(recvr: User, ntf:NotificationBase) {
        gateway ! SendFor(recvr, ntf)
    }

    private def asyncDispatchInternal:PartialFunction[NotificationBase, Unit] = {
        case d:JoinNotification =>
            d.reload()
            if (d.userAccept(d.user.get))
                gateway ! SendFor(d.user.get, d)

        case d:RankNotification =>
            d.reload()
            if (d.userAccept(d.user.get))
                gateway ! SendFor(d.user.get, d)

        case d:GlobalAnnouncementNotification =>
            d.reload()
            gateway ! SendAnnouncement(d)

        case d:ForumAnnouncementNotification =>
            d.reload()
            gateway ! SendAnnouncement(d)

        case d:GiveReputationNotification =>
            d.reload()
            if (d.userAccept(d.userTarget.get))
                gateway ! SendFor(d.userTarget.get, d)

        case d:AddStaffNotification =>
            d.reload()
            if (d.userAccept(d.user.get))
                gateway ! SendFor(d.user.get, d)
        case d:PromotedThreadNotification =>
            d.reload()
            if (d.userAccept(d.creatorObject.get))
                gateway ! SendFor(d.creatorObject.get, d)

        case d:SupportNotification =>
            d.reload()
            if (d.userAccept(d.userB.get))
                gateway ! SendFor(d.userB.get, d)

            sendEmail(d)

        case d:ResponseNotification =>
            d.reload()
            for ( recvr <- d.getReceivers; if d.userAccept(recvr) ) {
                gateway ! SendFor(recvr, d)
            }

        case d:ShoutNotification =>
            d.reload()
            for ( recvr <- d.getReceivers; if d.userAccept(recvr) ) {
                gateway ! SendFor(recvr, d)
            }

            sendEmail(d)

        case d:SummonNotification =>
            d.reload()
            for ( recvr <- d.getReceivers; if d.userAccept(recvr) )
                gateway ! SendFor(recvr, d)

            sendEmail(d)

        case d:LikeNotification =>
            d.reload()
            for ( recvr <- d.getReceivers; if d.userAccept(recvr) )
                gateway ! SendFor(recvr, d)

        case d:AddPostCollaborationNotification =>
            d.reload()
            for (recvr <- d.getReceivers; if d.userAccept(recvr))
                gateway ! SendFor(recvr, d)

            sendEmail(d)

        case d:AttenderEventNotification =>
            d.reload()
            for (recvr <- d.getReceivers; if d.userAccept(recvr))
                gateway ! SendFor(recvr, d)

            sendEmail(d)

        case d:EditEventNotification =>
            d.reload()
            for (recvr <- d.getReceivers; if d.userAccept(recvr))
                gateway ! SendFor(recvr, d)

            sendEmail(d)

        case d:ConnectWithFacebookNotification =>
            d.reload()
            for(recvr <- d.getReceivers; if d.userAccept(recvr)) {
                gateway ! SendFor(recvr, d)
            }

        case d:ConnectWithTwitterNotification =>
            d.reload()
            for(recvr <- d.getReceivers; if d.userAccept(recvr)) {
                gateway ! SendFor(recvr, d)
            }

        case d:UserHasTrophyNotification =>
            d.reload()
            for(recvr <- d.getReceivers; if d.userAccept(recvr)) {
                gateway ! SendFor(recvr, d)
            }

        case d:QuoteNotification =>
            d.reload()
            for ( recvr <- d.getReceivers; if d.userAccept(recvr) ) {
                gateway ! SendFor(recvr, d)
            }

        case d:EndorsementNotification =>
            d.reload()
            for (recvr <- d.getReceivers; if d.userAccept(recvr))
                gateway ! SendInviteFor(recvr, d)

            sendEmail(d)

        case d:NewsNotifications =>
                gateway ! LatestNewsFor(d)

        /**
         * FOR TEST PURPOSE ONLY.
         */
        case x:TestNotif =>
            gateway ! x

        case _ =>
    }
}
