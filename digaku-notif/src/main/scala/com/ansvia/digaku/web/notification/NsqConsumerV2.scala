/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.web.notification

import ly.bit.nsq.syncresponse.{SyncResponseReader, SyncResponseHandler}
import com.ansvia.commons.logging.Slf4jLogger
import ly.bit.nsq.{NSQProducer, Message}
import com.ansvia.digaku.nsq.NsqDataCompressor
import com.ansvia.digaku.exc.DigakuException
import net.liftweb.json.{NoTypeHints, Serialization}
import ly.bit.nsq.lookupd.BasicLookupd
import java.io.IOException

/**
 * Author: Fajr (<EMAIL>)
 *
 */
class NsqConsumerV2 extends SyncResponseHandler with Slf4jLogger {
    import NotificationNsqBuilder._

    private implicit val formats = Serialization.formats(NoTypeHints)
    private var reader:SyncResponseReader = _
    var webNotifCometServer:WebNotificationCometServerBase = _
    
    /**
     * Setiap message yang datang dari NSQ akan masuk ke sini.
     * @param msg
     * @return
     */
    def handleMessage(msg: Message) = {
        assert(webNotifCometServer != null, "webNotifCometServer is null, it's seem you are " +
            "not set webNotifCometServer, please set first")

        //        println("before compressed: " + new String(msg.getBody))

        val data = NsqDataCompressor.decompress(new String(msg.getBody))

        debug("got: " + data)

        try {
            data.toWebActorMessage map { am =>
            //                debug("data converted to " + am)
                webNotifCometServer ! am
            } getOrElse {
                warn("cannot convert nsq message to web actor message: \n" + data)
            }
        }
        catch {
            case e:DigakuException =>
                throw e
            case e:Exception =>
                error(e.getStackTraceString)
        }

        true
    }

    def setup(nsqLookupHost:String, nsqTopic:String, nsqAppId:String){

        debug("setup...")

        reader = new SyncResponseReader(nsqTopic, nsqAppId, this)
        try {
            reader.addLookupd(new BasicLookupd("http://" + nsqLookupHost))    
        } catch {
            case e:IOException =>
                //topic not exist, create it first
                val nsqProducer = new NSQProducer("http://" + nsqLookupHost, nsqTopic)
                reader.addLookupd(new BasicLookupd("http://" + nsqLookupHost))
                
        }
        

    }

    def shutdown(){
        debug("shutdown...")
        if (reader != null)
            reader.shutdown()
    }
}
