/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.web.notification
//
//import ly.bit.nsq.NSQProducer
//import com.ansvia.digaku.exc.DigakuException
//import com.ansvia.digaku.nsq.NsqDataCompressor
//
///**
// * Author: robin
// *
// */
//case class NsqNotificationGateway(publisherHost:String, topic:String) extends NotificationGateway {
//
//    require(publisherHost != null, "publisherHost is null")
//    require(publisherHost != "", "publisherHost not set")
//
//    private lazy val nsqProducer = new NSQProducer("http://" + publisherHost, topic)
//
//
//
//    def pipe(data: Any){
//        sendToNsqServer(data)
//    }
//
//
//
//
//    /**
//     * Ini akan mengirimkan message ke Nsq server
//     * sebelumnya message aka di-encode dulu ke JSON.
//     * @param data msg yang akan di-encode.
//     */
//    private def sendToNsqServer(data: Any){
//
//        import com.ansvia.digaku.web.notification.NotificationNsqBuilder._
//
//        data match {
//
//            case am:WebActorMessage =>
//                nsqProducer.putAsync(NsqDataCompressor.compress(am.toNsqMessage))
//
//            case x =>
//                throw new DigakuException("Unknown to handle " + x)
//        }
//
//    }
//
//    def close(){
//        nsqProducer.shutdown()
//    }
//
//}
