/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.web.notification

import com.ansvia.digaku.nsq.NsqDataCompressor
import com.ansvia.digaku.exc.DigakuException
import ly.bit.nsq.NSQProducer
import org.slf4j.LoggerFactory
import com.ansvia.commons.logging.Slf4jLogger

/**
 * Author: Fajr (<EMAIL>)
 *
 */
case class NsqNotificationGatewayV2(publisherHost:String, topics:List[String]) extends NotificationGateway 
    with Slf4jLogger {

    require(publisherHost != null, "publisherHost is null")
    require(publisherHost != "", "publisherHost not set")

    private lazy val nsqProducers = topics.map { topic =>
        new NSQProducer("http://" + publisherHost, topic)
    }

    def pipe(data: Any){
        sendToNsqServer(data)
    }

    /**
     * Ini akan mengiri<PERSON>kan message ke Nsq server
     * sebelumnya message aka di-encode dulu ke JSON.
     * @param data msg yang akan di-encode.
     */
    private def sendToNsqServer(data: Any){

        import com.ansvia.digaku.web.notification.NotificationNsqBuilder._

        data match {

            case am:WebActorMessage =>
                nsqProducers.foreach { nsqProducer =>
                    if (nsqProducer.getTopic == am.name) {
                        debug(s"send NSQ topic ${nsqProducer.getTopic}")
                        nsqProducer.putAsync(NsqDataCompressor.compress(am.toNsqMessage))    
                    }   
                }

            case x =>
                throw new DigakuException("Unknown to handle " + x)
        }

    }

    def close(){
        nsqProducers.foreach { nsqProducer =>
            nsqProducer.shutdown()
        }
    }
    
}
