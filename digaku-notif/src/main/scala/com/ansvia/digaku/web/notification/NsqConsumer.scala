/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

//package com.ansvia.digaku.web.notification
//
//
//import java.util.concurrent.TimeUnit
//
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.persistence.MapDb
//import ly.bit.nsq.syncresponse.{SyncResponseReader, SyncResponseHandler}
//import ly.bit.nsq.Message
//import ly.bit.nsq.lookupd.BasicLookupd
//import net.liftweb.json._
//import com.ansvia.digaku.exc.DigakuException
//import com.ansvia.digaku.nsq.NsqDataCompressor
//
//
///**
// * Author: robin
// *
// */
//
//
//object NsqConsumer extends SyncResponseHandler with <PERSON>lf4j<PERSON><PERSON><PERSON> {
//
//
//    private implicit val formats = Serialization.formats(NoTypeHints)
//
//
//    private lazy val recentlyReceived = MapDb.compressed.createHashSet("recently_received")
//        .expireAfterAccess(15, TimeUnit.MINUTES)
//        .expireAfterWrite(30, TimeUnit.MINUTES)
//        .expireMaxSize(5000)
//        .make[Array[Byte]]()
//
//
//    private var reader:SyncResponseReader = _
//
//    import NotificationNsqBuilder._
//
//    var webNotifCometServer:WebNotificationCometServerBase = _
//
//
//
//    /**
//     * Setiap message yang datang dari NSQ akan masuk ke sini.
//     * @param msg
//     * @return
//     */
//    def handleMessage(msg: Message):Boolean = {
//        assert(webNotifCometServer != null, "webNotifCometServer is null, it's seem you are " +
//            "not set webNotifCometServer, please set first")
//
////        println("before compressed: " + new String(msg.getBody))
//
//        // ini digunakan agar jangan memproses message yg sama
//        // karena NSQ memungkinkan message sama dikirimkan lebih dari sekali
//        if (recentlyReceived.contains(msg.getId))
//            return true
//
//        recentlyReceived.add(msg.getId)
//
//        val data = NsqDataCompressor.decompress(new String(msg.getBody))
//
//        debug("got: " + data)
//
//        try {
//            data.toWebActorMessage map { am =>
//                webNotifCometServer ! am
//            } getOrElse {
//                warn("cannot convert nsq message to web actor message: \n" + data)
//            }
//        }
//        catch {
//            case e:DigakuException =>
//                throw e
//            case e:Exception =>
//                error(e.getStackTraceString)
//        }
//
//        return true
//    }
//
//    def setup(nsqLookupHost:String, nsqTopics:List[String], nsqAppId:String){
//
//        debug("setup...")
//
//        nsqTopics.foreach { nsqTopic =>
//            reader = new SyncResponseReader(nsqTopic, nsqAppId, this)
//
//            reader.addLookupd(new BasicLookupd("http://" + nsqLookupHost))
//        }
//
//    }
//
//    def shutdown(){
//        debug("shutdown...")
//        if (reader != null)
//            reader.shutdown()
//    }
//}
//
//
