/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.web.notification

/**
 * Author: robin
 *
 */


/**
 * Notification gateway ini digunakan untuk jembatan dari comet client
 * ke comet server, dalam hal ini comet server adalah [[com.ansvia.digaku.web.notification.WebNotificationCometServerBase]]
 *
 * Gateway ini akan memilih jalan yang telah ditentukan, apabila
 * tidak mendukung NSQ maka gateway akan langsung mengirimkan notification
 * ke yang implementasi [[com.ansvia.digaku.web.notification.WebNotificationCometServerBase]]
 *
 * Tetapi apabila mendukung NSQ maka gateway akan mengirimkan notification ke NSQ server
 * lalu NSQ server yang mem-broadcast ke Digaku cluster, apabila digambarkan seperti berikut:
 *
 *
 *      comet client --> gateway --> NSQ --> digaku app (in cluster) --> web notif comet server
 *
 * Kelebihannya apabila menggunakan NSQ maka antar digaku app node di cluster bisa
 * mendapatkan notifikasi secara real-time walaupun user X dan Y online di server dan web app yang berbeda.
 *
 */

abstract class NotificationGateway {
    def pipe(data:Any)
    def !(data:Any) = pipe(data)
    def close()
}
//
//object LocalNotificationGateway extends NotificationGateway {
//
//    private var webNotifCometServer:WebNotificationCometServerBase = _
//
//    def pipe(data: Any){
//        assert(webNotifCometServer != null, "webNotifCommetServer not set, please set it first before you can use")
//        webNotifCometServer ! data
//    }
//
//    def setWebNotifCometServer(cs:WebNotificationCometServerBase) = {
//        webNotifCometServer = cs
//        this
//    }
//
//    def close(){}
//}

trait NotificationGatewayComponent {
    val notifGateway:NotificationGateway
}

//object NotificationGateway {
//
//    /**
//     * Kirimkan data ke gateway.
//     * @param data yang akan dikirimkan.
//     */
//    def !(data:Any){
//        if (gateway == null)
//            throw new DigakuException("gateway not set, please set it first using `setGateway`")
//
//        gateway pipe data
//    }
//
//    private var gateway:NotificationGateway = null
//
//
//    /**
//     * Set default gateway yang akan digunakana.
//     * @param gateway gateway.
//     * @return
//     */
//    def setGateway(gateway:NotificationGateway) = {
//        this.gateway = gateway
//        this
//    }
//
//    /**
//     * Dapatkan gateway yang digunakan.
//     * @return
//     */
//    def getGateway = gateway
//
//    /**
//     * Tutup gateway.
//     */
//    def close(){
//        if (gateway != null){
//            gateway.close()
////            actor ! PoisonPill
//        }
//    }
//
//}

