/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.web.notification

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types.IDType
import com.ansvia.digaku.exc.{IgnoredException, DigakuException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.{Forum, Streamable, User}
import com.ansvia.digaku.notifications.impl.{LatestPublicNewsNotification, LatestForumNewsNotification}
import com.ansvia.digaku.notifications.{NewsNotifications, NotificationBase, PersistentNotification}
import com.ansvia.util.idgen.IncrementalNumIdGenerator
import net.liftweb.json.JsonAST.JValue
import net.liftweb.json.JsonDSL._
import net.liftweb.json._

/**
 * Author: robin
 *
 */
object NotificationNsqBuilder {

    var initiator:String = "0"

    class WebActorMessageWrapper(am:WebActorMessage) extends IncrementalNumIdGenerator(0L) with Slf4jLogger with DbAccess {
        implicit val formats = Serialization.formats(NoTypeHints)

        /**
         * Convert this notification to json string
         * @return
         */
        def toNsqMessage:String = {
            compact(render(toJson))
        }

        private def buildUserNotifPairMessage(user:User, notif:NotificationBase):JObject = {
            ("id" -> nextId()) ~
            ("cl" -> am.getClass.getCanonicalName) ~
                ("userId" -> user.getId) ~
                ("notifId" -> notif.getId) ~
                ("notifClass" -> notif.getClass.getCanonicalName)
        }


        def toJson:JObject = {

            val rv:JObject =
            am match {
                case SendFor(user, notif) => {
                    buildUserNotifPairMessage(user, notif)
                }
                case SendInviteFor(user, notif) => {
                    buildUserNotifPairMessage(user, notif)
                }
//                case MessageFor(user, notif) => {
//                    buildUserNotifPairMessage(user, notif)
//                }
                case NewPostArrived(user, post) => {
                    ("id" -> nextId()) ~
                    ("cl" -> am.getClass.getCanonicalName) ~
                        ("userId" -> user.getId) ~
                        ("postId" -> post.getId) ~
                        ("postClass" -> post.getClass.getCanonicalName)
                }

                case LatestNewsFor(newsNotification:NewsNotifications) =>{
                    ("id" -> nextId()) ~
                        ("cl" -> am.getClass.getCanonicalName) ~
                        ("newsNotifId" -> newsNotification.getId) ~
                        ("newsNotifClass" -> newsNotification.getClass.getCanonicalName)~
                        ("article" -> newsNotification.getArticleId)

                }

                case UpdateMtSession(user) => {
                    ("id" -> nextId()) ~
                        ("kind" -> "UpdateMtSession") ~
                        ("userId" -> user.getId) ~
                        ("cl" -> am.getClass.getCanonicalName)
                }
                case UpdateNotifCount(user) => {
                    ("id" -> nextId()) ~
                    ("kind" -> "UpdateNotifCount") ~
                        ("userId" -> user.getId) ~
                        ("cl" -> am.getClass.getCanonicalName)
                }
                case IndexingStatus(ie) => {
                    "id" -> nextId()
                }

                case KillOther(user, accessTime, ref) => {
                    ("id" -> nextId()) ~
                        ("kind" -> "KillOther") ~
                        ("userId" -> user.getId) ~
                        ("accessTime" -> accessTime) ~
                        ("ref" -> ref) ~
                        ("cl" -> am.getClass.getCanonicalName)
                }

                case DeactiveUser(user) => {
                    ("id" -> nextId()) ~
                        ("cl" -> am.getClass.getCanonicalName) ~
                        ("userId" -> user.getId) ~
                        ("postClass" -> am.getClass.getCanonicalName)
                }

                case SendAnnouncement(notif) => {
                    ("id" -> nextId()) ~
                        ("cl" -> am.getClass.getCanonicalName) ~
                        ("notifId" -> notif.getId) ~
                        ("notifClass" -> notif.getClass.getCanonicalName)
                }

                case SessionTimeout(jSessionId) => {
                    ("id" -> nextId()) ~
                        ("cl" -> am.getClass.getCanonicalName) ~
                        ("jSessionId" -> jSessionId)
                }

                case x =>
                    error(x + " not handled yet, please handle it")
                    "id" -> 0L
            }

            ("initiator" -> initiator) ~ rv

        }

    }

    implicit def webActorMessageToNsqMessageWrapper(am:WebActorMessage) = {
        new WebActorMessageWrapper(am)
    }

    class MessageWrapper(ntf:PersistentNotification) {
        import net.liftweb.json.Serialization.write
        implicit val formats = Serialization.formats(NoTypeHints)

        /**
         * Convert this notification to json string
         * @return
         */
        def toNsqMessage:String = {
            compact(render(toJson))
        }

        def toJson:JObject = {
            val ser = write(ntf)
            val json = parse(ser)
            json match {
                case jo:JObject =>
                    jo ~ ("id" -> ntf.getId) ~
                        ("creationTime" -> ntf.creationTime) ~
                        ("sr" -> true) ~
                        ("cl" -> ntf.getClass.getCanonicalName)
                case x =>
                    throw new DigakuException("cannot deserialize %s to json".format(ntf))
            }
        }

    }

    implicit def notifToNsqMessageWrapper(ntf:PersistentNotification) = {
        new MessageWrapper(ntf)
    }

    var customWebActorConverter: PartialFunction[JValue, Option[WebActorMessage]] =
        { case JNothing => None }

    class StrToObjectWrapper(str:String) extends DbAccess with Slf4jLogger {
        import com.ansvia.graph.BlueprintsWrapper._
        implicit val formats = Serialization.formats(NoTypeHints)

        def toNotif:Option[PersistentNotification] = {
            val json = parse(str)

            val clazzName = (json \ "cl").extract[String]

            val id = (json \ "id").extract[IDType]

            val vertex = db.getVertex(id)

            if (vertex != null){
                val clazz = Class.forName(clazzName)

                // DO THE MAGIC!!
                val m = Manifest.classType(clazz)
                vertex.toCC(m)
            }else{
                None
            }
        }

        private def extractUserAndNotif(json:JValue)(func: (User, PersistentNotification) => WebActorMessage):Option[WebActorMessage] = {
            val JInt(userId) = json \ "userId"
            val JInt(ntfId) = json \ "notifId"

            try {
                User.getById(userId.toLong).flatMap { u =>
                    db.getVertex(ntfId.toLong).toCC[PersistentNotification].map { ntf =>
                        func(u, ntf)
                    }
                }
            } catch {
                case e:Throwable => throw new DigakuException(e.getMessage)
            }
        }



        def toWebActorMessage:Option[WebActorMessage] = {
            if (str == "ping"){
                None
            }else{
                parseData()
            }
        }

        private def parseData() = {
            val json = try {
                parse(str)
            }catch{
                case e:JsonParser.ParseException =>
                    warn(s"Cannot parse as json: $str")
                    throw e
            }

            val clazzName = try {
                (json \ "cl").extract[String]
            }catch{
                case e:Exception =>
                    println("Cannot extract data: " + str)
                    //                    throw new DigakuException("Cannot extract data: " + str)
                    throw e
            }

            val initiator = try {
                (json \ "initiator").extract[String]
            } catch {
                case e:Exception =>
                    println("Cannot extract source for data: " + str + " (to get initiator info)")
                    throw e
            }

            db.rollback()

            val rv =
                clazzName match {
                    case "com.ansvia.digaku.web.notification.SendFor" =>

                        extractUserAndNotif(json){ (user, notif) =>
                            SendFor(user, notif)
                        }

                    case "com.ansvia.digaku.web.notification.SendInviteFor" =>

                        extractUserAndNotif(json){ (user, notif) =>
                            SendInviteFor(user, notif)
                        }

                    case "com.ansvia.digaku.web.notification.SendAnnouncement" =>

                        val JInt(ntfId) = json \ "notifId"

                        db.getVertex(ntfId.toLong).toCC[PersistentNotification].map { ntf =>
                            SendAnnouncement(ntf)
                        }


                    //                case "com.ansvia.digaku.web.notification.MessageFor" =>
                    //
                    //                    extractUserAndNotif(json){ (user, notif) =>
                    //                        MessageFor(user, notif)
                    //                    }

                    case "com.ansvia.digaku.web.notification.NewPostArrived" =>

                        val JInt(userId) = json \ "userId"
                        val JInt(postId) = json \ "postId"


                        User.getById(userId.toLong).flatMap { u =>
                            db.getVertex(postId.toLong).toCC[Streamable[IDType]].map { post =>
                                NewPostArrived(u, post)
                            }
                        }

                    //                case "com.ansvia.digaku.web.notification.DeactiveUser" =>
                    //
                    //                    val JInt(userId) = (json \ "userId")
                    //
                    //
                    //                    User.getById(userId.toLong).map { u =>
                    //                        DeactiveUser(u)
                    //                    }
                    case "com.ansvia.digaku.web.notification.UpdateMtSession" =>
                        val JInt(userId) = json \ "userId"
                        User.getById(userId.toLong).map { u =>
                            UpdateMtSession(u)
                        }

                    case "com.ansvia.digaku.web.notification.UpdateNotifCount" =>

                        val JInt(userId) = json \ "userId"
                        User.getById(userId.toLong).map { u =>
                            UpdateNotifCount(u)
                        }

                    case "com.ansvia.digaku.web.notification.KillOther" =>
                        val JInt(userId) = json \ "userId"
                        val JInt(lastAccess) = json \ "accessTime"
                        val JString(ref) = json \ "ref"
                        User.getById(userId.toLong).map { u =>
                            KillOther(u, lastAccess.toLong, ref)
                        }

                    case "com.ansvia.digaku.web.notification.LatestNewsFor" =>
                        val JInt(article) = json \ "article"
                        val JString(notifClass) = json \ "newsNotifClass"
                        val newsNotif = notifClass match {
                            case "com.ansvia.digaku.notifications.impl.LatestForumNewsNotification" =>
                                LatestForumNewsNotification( article.toLong)
                            case "com.ansvia.digaku.notifications.impl.LatestPublicNewsNotification" =>
                                LatestPublicNewsNotification(article.toLong)
                            case _ =>
                                new NewsNotifications(0, 0) {
                                    override val hash: String = ""
                                }
                        }

                        Some(LatestNewsFor(newsNotif))

                    case "com.ansvia.digaku.web.notification.SessionTimeout" =>
                        val JString(jSessionId) = json \ "jSessionId"

                        Some(SessionTimeout(jSessionId))


                    case x =>

                        customWebActorConverter.applyOrElse(json, (_:JValue) => {
                            throw new IgnoredException("web actor %s not handled yet, ".format(x) +
                                "please handle it")
                        })

                }

            rv.foreach(_.initiator = initiator)

            rv
        }

    }

    implicit def strToNotif(str:String) = {
        new StrToObjectWrapper(str)
    }


}
