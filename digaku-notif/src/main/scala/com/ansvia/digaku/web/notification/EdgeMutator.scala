/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.web.notification

import com.ansvia.digaku.notifications.{PersistentNotificationHandlerDbMutator}
import com.ansvia.digaku.notifications.impl.ResponseNotification
import com.tinkerpop.blueprints.Edge
import com.ansvia.commons.logging.Slf4jLogger

/**
 * Author: robin
 *
 */

// @TODO(robin): make this usable
//               kedepannya untuk set readTriggerEvent yang saat ini masih dihandle di PersistentNotificationHandler

/**
 * Edge mutator ini digunakan untuk mutator edge
 * terutama untuk extends operasi di [[com.ansvia.digaku.notifications.PersistentNotificationHandlerDbMutator]]
 */
object EdgeMutator extends Slf4jLogger {

//    import PersistentNotificationHandler.EdgeMutatorParam

    def bind(){
        PersistentNotificationHandlerDbMutator.edgeMutators.append({
            case PersistentNotificationHandlerDbMutator.EdgeMutatorParam(respNtf:ResponseNotification, edge:Edge) =>
                debug("edge.readTriggerEvent: " + respNtf.getId + " -> " + edge.getProperty[String]("readTriggerEvent"))
        })
    }

}
