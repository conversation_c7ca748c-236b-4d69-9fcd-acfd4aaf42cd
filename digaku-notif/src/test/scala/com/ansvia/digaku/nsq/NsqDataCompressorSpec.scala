/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.nsq

import org.specs2.mutable.Specification
import com.ansvia.perf.PerfTiming

/**
 * Author: robin
 * Date: 5/30/14
 * Time: 6:05 PM
 *
 */
class NsqDataCompressorSpec extends Specification with PerfTiming {

    "NSQ data compressor" should {

        val datas = Seq(
            """{"id":1,"cl":"com.ansvia.digaku.web.notification.NewPostArrived","userId":2311552,"postId":211958000,"postClass":"com.ansvia.digaku.model.SimplePost"}""",
            """{"id":100,"cl":"com.ansvia.digaku.web.notification.SendFor","userId":2311552,"postId":211958000,"postClass":"com.ansvia.digaku.model.SimplePost"}""",
            """{"id":510,"cl":"com.ansvia.digaku.web.notification.SendInviteFor","userId":2311552,"postId":211958000,"kind":"UpdateNotifCount"}""",
            """{"id":1,"cl":"com.ansvia.digaku.web.notification.NewPostArrived","userId":2122066,"postId":2412057108,"postClass":"com.ansvia.digaku.model.Picture"}""",
            """{"id":1,"cl":"com.ansvia.digaku.web.notification.SendFor","userId":2256532,"notifId":211958170,"notifClass":"com.ansvia.digaku.notifications.impl.SummonNotification"}"""
        )

        datas.zipWithIndex.foreach { case (d, i) =>
            "compress/decompress #%d".format(i+1) in {

                val ndc = new NsqDataCompressor(d)
                val cd = ndc.compress()
                println("%s -> %s".format(d, cd))
                println("before compress: %d, after: %d".format(d.length, cd.length))
                val ndc2 = new NsqDataCompressor(cd)
                val cd2 = ndc2.decompress()
                println("before decompress: %d, after: %d".format(cd.length, cd2.length))

                (cd.length must beLessThan(d.length))

                    (cd2.length must beGreaterThan(cd.length))

                    (cd2 must beEqualTo(d))
            }
        }


        // test performance
        timing("performance test"){
            for (i <- 1 to 5000){
                datas.zipWithIndex.foreach { case (d, i) =>

                    val ndc = new NsqDataCompressor(d)
                    val cd = ndc.compress()

                    val ndc2 = new NsqDataCompressor(cd)
                    val cd2 = ndc2.decompress()

                    print(".")

                }
            }
        }

    }
}
