/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package bootstrap.liftweb

import java.io.{File, FileNotFoundException}
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.event.listener.{AsyncForumFollowerStreamListener, AsyncDeleteChannelEventStreamListener}
import com.ansvia.digaku.lib.WebEngineWithConfig
import com.ansvia.digaku.message.compiler.HTMLLinkTransformer
import com.ansvia.digaku.minion.MinionStreamBuilder
import com.ansvia.digaku.model.builder.OriginBuilder
import com.ansvia.digaku.persistence.CassandraDriver
import com.ansvia.digaku.restapi.notification.WebNotificationCometServer
import com.ansvia.digaku.restapi.{Config => RestConfig, _}
import com.ansvia.digaku.stream.{CassandraBackedDuplicateDetector, StreamBuilder}
import com.ansvia.digaku.system.StreamBuilderExtension
import com.ansvia.digaku.utils.emo.impl.MindtalkEmoticonProcessor
import com.ansvia.digaku.validator.EmailValidator
import com.ansvia.digaku.web._
import com.ansvia.digaku.web.event.listener.AsyncAnnouncementEventListener
import com.ansvia.digaku.web.notification._
import com.twitter.ostrich.admin.ServiceTracker
import com.twitter.ostrich.stats.{GraphiteStatsLogger, Stats, StatsListener}
import net.liftweb.http.{RewriteRequest, _}
import org.streum.configrity.Configuration
import scala.collection.mutable.ListBuffer


/**
 * A class that's instantiated early and run.  It allows the application
 * to modify lift's environment
 */
class Boot extends Slf4jLogger {

    import bootstrap.liftweb.Boot._

    private val versionGetterRe = "/v(\\d+)/.+".r

    def boot {

        LiftRules.earlyInStateful.append(MtSession.testCookieEarlyInStateful)

        LiftRules.dispatch.append(Authorization)
        LiftRules.statelessDispatch.append(UserRest)
        LiftRules.statelessDispatch.append(PostRest)
        LiftRules.statelessDispatch.append(ChannelRest)
        LiftRules.statelessDispatch.append(EmbedableRest)
        LiftRules.statelessDispatch.append(SearchRest)
        LiftRules.statelessDispatch.append(UserRestMeBlockUnblock)
        LiftRules.statelessDispatch.append(UserRestMe)
        LiftRules.statelessDispatch.append(PostResponseRestCools)
        LiftRules.statelessDispatch.append(PostResponseRestCools2)
        LiftRules.statelessDispatch.append(PostRestCools)
        LiftRules.statelessDispatch.append(PostRestCools2)
        LiftRules.statelessDispatch.append(PostRestArticle)
        LiftRules.statelessDispatch.append(PostRecommenderRest)
        LiftRules.statelessDispatch.append(SystemUtilRest)
        LiftRules.statelessDispatch.append(MiscRest)
        LiftRules.statelessDispatch.append(ResponseRest)
        LiftRules.statelessDispatch.append(UserRestMeConnect)
        LiftRules.statelessDispatch.append(ChannelInfoRest)
        LiftRules.statelessDispatch.append(PostPopularRest)
        LiftRules.statelessDispatch.append(UserRestMeNotification)
        LiftRules.statelessDispatch.append(PostModerationRest)
        LiftRules.statelessDispatch.append(UserManagerRest)
        LiftRules.statelessDispatch.append(ShortenUrlRest)
        LiftRules.statelessDispatch.append(FAQRest)

        /**
         * Rewrite ini digunakan untuk mengextract informasi versi
         * dari url-nya, dan menambahkan  parameter v di request-nya
         * hal ini untuk memudahkan tracking versi yang digunakan
         * di level api controller-nya, sebagai contoh di bagian
         * Authorization yang menggunakan fungsi satu shared
         * antara v1 dan v2, di sana kita perlu tau mana yang dari v1
         * dan mana yang dari v2, sehingga kita bisa memberikan response
         * yang tepat.
         */
        LiftRules.statelessRewrite.append {
            case RewriteRequest(path,reqType,httpReq) => {
                val v = if (httpReq.uri.startsWith("/v1/"))
                    httpReq.uri match {
                        case versionGetterRe(version) => version
                        case x => "2"
                    }
                else
                    "2"
                RewriteResponse(path,Map("v" -> v), stopRewriting = true)
            }
        }

        val conf = parseConfig()

        setupDigakuEngine(conf)

        setupEmoticon()

        // setup ostrich services.
        setupOstrich()

        info("ready.")
    }

    /**
     * setup database.
     */
    private def setupDigakuEngine(conf: Configuration) {

        debug("setup digaku engine...")

        /**
         * Set db config using TitanConfig.
         */
        EmailValidator.mxRecordCheck = RestConfig.conf("mail.validator.mx-check", true)

        HTMLLinkTransformer.BASE_URL = WebConfig.BASE_URL

        if (!EmailValidator.mxRecordCheck) {
            warn("email validator MX record checking is disabled")
        }

        Digaku.engine = new WebEngineWithConfig(conf) {
            // setup notification gateway
            override lazy val notifGateway: EWCNotifGateway = {
                if (config.supportNsq) {
                    new EWCNsqNotifGateway {
                        override var webNotifCometServer: WebNotificationCometServerBase = WebNotificationCometServer
                    }
                } else {
                    new EWCLocalNotifGateway() {
                        override var webNotifCometServer: WebNotificationCometServerBase = WebNotificationCometServer
                    }
                }
            }
        }

        NotificationNsqBuilder.initiator = WebConfig.NODE_ID

        /**
         * Dispatch Digaku engine initialization
         * procedure.
         */
        Digaku.init()

        Digaku.engine.print()

        /**
         * Ensure indices.
         */
        try {
            Digaku.engine.database.index()
        } catch {
            case e:Throwable =>
                error(e.getMessage)
                e.printStackTrace()
        }

        /**
         * Add event stream listener di layer digaku-web.
         */
        Digaku.engine.eventStream.addListeners(com.ansvia.digaku.restapi.event.RestEventStreamListener)
        Digaku.engine.eventStream.addListeners(AsyncDeleteChannelEventStreamListener)
        Digaku.engine.eventStream.addListeners(com.ansvia.digaku.web.JsonObjectMapper)
        Digaku.engine.eventStream.addListeners(AsyncForumFollowerStreamListener)
        Digaku.engine.eventStream.addListeners(AsyncAnnouncementEventListener)

        // custom external stream builder
        if (WebConfig.conf("engine.use-minion-stream-builder", false)) {
            println("use external stream builder: minion stream builder with nsq publisher host: " +
                WebConfig.NSQ_PUBLISHER_HOST)
            Digaku.engine.eventStream.addListeners(new MinionStreamBuilder(WebConfig.NSQ_PUBLISHER_HOST))
        }

        OriginBuilder.CHANNEL_URL_PREFIX = WebConfig.CHANNEL_URL_PREFIX
        OriginBuilder.USER_URL_PREFIX = WebConfig.USER_URL_PREFIX

	    // setup node util
        WebConfig.nodeUtil.registerMe()
        WebConfig.nodeUtil.keepMeAlive()

        // inisialisasi digaku-web-core module
        DigakuWebCore.init()

        StreamBuilderExtension.setup()

        LiftRules.unloadHooks.append { () =>
            shutdown()
        }

        debug("setup digaku engine done.")
    }

    private def setupEmoticon() {
        WebConfig.supportedEmos = (if (WebConfig.supportedEmos != null) WebConfig.supportedEmos else
            Seq(new MindtalkEmoticonProcessor))
    }

    /**
     * Setup ostrich stats/monitoring service.
     */
    private def setupOstrich() {

        import com.twitter.conversions.time._

        val conf = WebConfig.conf.detach("database.analytics.cassandra")
        val cassandraHost = conf("host", "127.0.0.1:9160")
        val cassandraClusterName = conf("cluster-name", "digaku")
        val cassandraKeyspaceName = conf("keyspace", "ostrich_digaku")
        val cassandraReplicationStrategy = conf("replication-strategy", "SimpleStrategy")
        val cassandraReplicationStrategyOpts = conf("replication-strategy-opts", "replication_factor:1")

        info("ostrich cassandra using host: " + cassandraHost +
            ", cluster name: " + cassandraClusterName +
            ", keyspace: " + cassandraKeyspaceName +
            ", repl strategy: " + cassandraReplicationStrategy +
            ", repl strategy opts: " + cassandraReplicationStrategyOpts)

        StatsListener(1.minute, Stats, Nil)

        // graphite reporter implementation
        WebConfig.conf.get[String]("graphite.host").foreach { hostNPort =>
            val host = hostNPort.split(":").apply(0)
            val port = hostNPort.split(":").apply(1).toInt
            info("graphite host: " + host + ":" + port)
            val cbs3 = new GraphiteStatsLogger(host, port,1.minute, WebConfig.BRAND_NAME.toLowerCase,
                Some(WebConfig.NODE_ID), Stats)
            ServiceTracker.register(cbs3)
            cbs3.start()
        }
    }

    def shutdown() {
        debug("Unload all...")

        Digaku.shutdown()

        OstrichReporter.cassandraCollections.foreach(_._2.shutdown())

	    WebConfig.nodeUtil.shutdown()

        if (WebConfig.conf("engine.use-minion-stream-builder", false)) {
            println("shutting down duplicate detector")

            StreamBuilder.duplicateDetector match {
                case cbdd:CassandraBackedDuplicateDetector =>
                    cbdd.close()
                case _ =>
            }
        }
    }
}


object Boot extends Slf4jLogger {

    lazy val currentDir = {
        new File(new File(new File((new File(getClass.getProtectionDomain.getCodeSource.getLocation.getPath))
            .getParent).getParent).getParent)
    }

    lazy val digakuConfFile = {
        System.getProperty("digaku.configFile", {
            val curDirF = currentDir + "/digaku.conf"
            if (new File(curDirF).exists()) {
                curDirF
            } else {
                // for development let's look up to parent dir
                currentDir.getParent + "/digaku.conf"
            }
        })
    }

    def parseConfig() = {
        try {
            info("Using config file: " + digakuConfFile)
            val conf = Configuration.load(digakuConfFile)
            RestConfig.parse(conf)
            WebConfig.conf = conf
            MailSettings.conf = conf
            conf
        } catch {
            case e: FileNotFoundException =>
                error(e.getMessage)
                e.printStackTrace()
                System.exit(4)
                throw e
        }
    }
}
