///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.restapi.builder.compat
//
//import com.ansvia.digaku.model._
//import net.liftweb._
//import http._
//import rest._
//import json._
//import JsonDSL._
//import com.ansvia.graph.BlueprintsWrapper._
//import java.text.SimpleDateFormat
//import com.ansvia.digaku.exc.BadRequestException
//import UserBuilderCompat._
//
///**
// * Author: fajrhf
// *
// */
//object ChannelBuilderCompat {
//
//    protected class ChannelWrapper(ch:Forum) {
//        val sdf = new SimpleDateFormat("yyyy/MM/dd")
//
//        //recreate toJSON based on group builder
//        def toJSON():JsonAST.JObject = {
//            val founder = ch.getOwner.getOrElse{
//                throw BadRequestException("founder not found")
//            }
//
//            val creationTime:Long = ch.getVertex.getOrElse("creationTime", 0L)
//
//            ("id" -> ch.getId.toString) ~
//                ("name" -> ch.name) ~
//                ("desc" -> ch.desc) ~
//                ("logo" -> ch.smallLogoUrl) ~
//                ("logo_small" -> ch.smallLogoUrl) ~
//                ("logo_large" -> ch.largeLogoUrl) ~
//                ("founder" -> founder.toJSON(attr = List[String]("photo", "supporting_count", "supporters_count", "channel_joined_count"))) ~
//                ("keywords" -> ch.tags.split(" ").toList) ~
//                ("members_count" -> ch.getMemberCount()) ~
//                ("creation_time" -> creationTime)
//        }
//
//        def toSimpleJSON():JsonAST.JObject = {
//            ("id" -> ch.getId.toString) ~
//                ("name" -> ch.name) ~
//                ("logo_small" -> ch.smallLogoUrl) ~
//                ("logo_large" -> ch.largeLogoUrl)
//        }
//
//        def baseToJSON(founder:Boolean = false) = {
//            val creationTime:Long = ch.getVertex.getOrElse("creationTime", 0L)
//            val memberCount = ch.getMemberCount()
//
//            ("id" -> ch.getId.toString) ~
//                ("name" -> ch.name) ~
//                ("desc" -> ch.desc) ~
//                ("logo" -> ch.mediumLogoUrl) ~
//                ("logo_small" -> ch.smallLogoUrl) ~
//                ("logo_large" -> ch.largeLogoUrl) ~
//                ("keywords" ->  ch.tags.split(" ").toList) ~
//                ("count" -> memberCount) ~
//                //digunakan pada v1/forum/newest
//                ("members_count" -> memberCount)~
//                // @TODO(alam): unix time, not yet implemented
//                ("creation_time" -> creationTime)
//               // ("creation_time" -> ch.getVertex.getProperty("creationTime").toString)
//
//        }
//
//        def infoToJSON(u:User=null) = {
//            baseToJSON() ~
//                ("current_user_is_member" -> (if(u == null) false else ch.isMember(u)))~
//                ("founder" -> ch.getOwner.map(chf => List(
//                    chf.getId.toString,
//                    chf.name,
//                    chf.photoSmall
//                )).getOrElse(List.empty[String]))~
//                ("statistic" ->
//                    ("questions"-> 0)~
//                        ("minds" -> ch.getSimplePostCount)~
//                        ("articles" -> ch.getArticleCount(StickyState.ALL))~
//                        ("total" -> (ch.getContentCount))
//                    ) ~
//                ("members" ->
//                    ("count" -> ch.getMemberCount())~
//                        ("users" -> ch.getMembers(0,10).map(chm => List(
//                            chm.getId.toString,
//                            chm.name
//                        )))
//                    )~
//                // @deprecated
//                ("likes" ->
//                    ("count "-> 0)~
//                        ("users"-> List.empty[String])
//                    )~
//                ("tags" -> ch.tags.split(" ").toList) ~
//                // @TODO(alam): active_streams, not yet implemented
//                ("active_streams" -> List.empty[String])
//
//        }
//        def membersToJSON(offset:Int=0, limit:Int=10) = {
//            ("count" -> ch.getMemberCount())~
//                ("members" ->
//                    (ch.getMembers(offset, (if(limit>100) 100 else limit)).map { u =>
//                        ("id" -> u.getId.toString)~
//                            ("name" -> u.getName)~
//                            ("full_name" -> u.fullName)~
//                            ("photos" -> List(
//                                u.photoSmall,
//                                u.photoMedium,
//                                u.photoLarge
//                            ))~
//                            ("level" -> u.level) ~
//                            ("points" -> u.point)~
//                            ("supporting_count" -> u.supportingCount)~
//                            ("supporters_count" -> u.supportersCount)
//                    })
//                    )
//        }
//    }
//
//    implicit def implicitChannelJsonable(ch:Forum) = new ChannelWrapper(ch)
//    implicit def implicitChannelJsonable(channels:List[Forum]):JValue =
//        channels.map (_.toJSON())
//    implicit def implicitChannelsJsonable(channels:Iterable[Forum]):JValue = {
//        channels.map (_.toJSON())
//    }
//}
