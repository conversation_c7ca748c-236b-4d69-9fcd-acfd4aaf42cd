/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.restapi.builder.compat

import com.ansvia.digaku.model._
import net.liftweb._
import json._
import JsonDSL._
import scala.collection.JavaConversions._
import com.ansvia.graph.BlueprintsWrapper._
import com.tinkerpop.blueprints.Direction
import java.util.Date
import com.ansvia.digaku.exc.InvalidParameterException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.restapi.builder.compat.UserBuilderCompat._

/**
 * Author: fajrhf
 *
 */
object PictureBuilderCompat extends DbAccess {

    protected class PictureWrapper(picture:Picture) {
        def toJSON(attr:List[(String)] = List.empty,
                   preview:Boolean = false,
                   format:String = "markdown",
                   currentUser:Option[User]=None
                      ):JsonAST.JObject = {



            if (!List("plain", "html", "markdown").contains(format)) {
                throw InvalidParameterException("format should be one of: plain, html or markdown")
            }

            val isLiked = if (currentUser.isDefined) {
                picture.isLiker(currentUser.get)
            } else {
                false
            }

            val postOriginKind = picture.origin.kind match {
                case OriginKind.SUB_FORUM => "Group"
                case OriginKind.USER => "User"
                case _ => "None"
            }

            val shoutObject = picture.getVertex.pipe.inE(Label.SHOUT).iterator().map { ed =>
                val message = ed.getOrElse("message", "")
                val time = ed.getOrElse("timeOrder", 0L).abs
                val userShout = ed.getVertex(Direction.OUT).toCC[User].get
                ShoutStreamObject(userShout, message,picture, new Date(time))
            }.toList

            //build the picture attachment
            val pictJSONized:JObject = ("id" -> picture.getId.toString) ~
                ("thumbnail" -> picture.smallUrl) ~
                ("medium" -> picture.mediumUrl) ~
                ("original" -> picture.largeUrl) ~
                ("creation_time" -> picture.creationTime) ~
//                ("kind" -> picture.kind)
                ("kind" -> "EmbedPic")

            var postJSONized:JObject = ("id" -> picture.getId.toString) ~
                ("creator" -> picture.creator.toJSON(photo = true)) ~
                ("message" -> picture.title) ~
                ("origin" ->
                    ("id" -> picture.origin.getId.toString) ~
                        ("name" -> picture.origin.getName) ~
                        ("kind" -> postOriginKind)
                    ) ~
                ("response_count" -> picture.getResponseCount) ~
                ("liked" -> isLiked ) ~
                ("likes_count" -> picture.getLikesCount) ~
                ("creation_time" -> picture.creationTime) ~
                ("kind" -> "Mind") ~
                ("attachments" -> List(pictJSONized))

            if (picture.getVertex.pipe.in(Label.SHOUT).count > 0) {
                postJSONized =
                shoutObject.headOption.map { sO =>
                    postJSONized ~
                        ("shout" -> true) ~
                        ("shout_user_id" -> sO.shouter.getId) ~
                        ("shout_user_name" -> sO.shouter.name) ~
                        ("shout_message" -> sO.message) ~
                        ("parent_id" -> picture.getId.toString)
                }.getOrElse {
                    postJSONized ~
                        ("shout" -> false) ~
                        ("shout_user_id" -> "") ~
                        ("shout_user_name" -> "") ~
                        ("shout_message" -> "") ~
                        ("parent_id" -> "")
                }
            }

            postJSONized
        }


    }

    implicit def implicitArticleJsonable(picture:Picture) = new PictureWrapper(picture)
    implicit def implicitArticleJsonable(picture:Seq[Picture]):JValue = {
        picture.map(_.toJSON())
    }
}
