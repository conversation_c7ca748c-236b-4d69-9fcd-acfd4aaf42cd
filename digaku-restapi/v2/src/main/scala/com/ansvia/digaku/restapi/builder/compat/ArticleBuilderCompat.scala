///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.restapi.builder.compat
//
//import com.ansvia.digaku.model._
//import com.ansvia.digaku.restapi.UserRestCompat
//import net.liftweb._
//import json._
//import JsonDSL._
//import scala.collection.JavaConversions._
//import com.ansvia.graph.BlueprintsWrapper._
//import com.tinkerpop.blueprints.Direction
//import java.util.Date
//import com.ansvia.digaku.exc.InvalidParameterException
//import com.ansvia.digaku.helpers.DbAccess
//import UserBuilderCompat._
//
///**
// * Author: fajrhf
// *
// */
//object ArticleBuilderCompat extends DbAccess {
//
//    protected class ArticleWrapper(article:Article) {
//        def toJSON(attr:List[(String)] = List.empty,
//                   preview:Boolean = false,
//                   format:String = "markdown"
//                      ):JsonAST.JObject = {
//
//
//
//            if (!List("plain", "html", "markdown").contains(format)) {
//                throw InvalidParameterException("format should be one of: plain, html or markdown")
//            }
//
//            val isLiked = article.getLikesCount > 0
//
//            val message = if (preview) {
//                article.shortDesc
//            } else {
//                article.getContent
//            }
//
//            val postKind = article.origin.kind match {
//                case OriginKind.SUB_FORUM => "Group"
//                case OriginKind.USER => "User"
//                case _ => "None"
//            }
//
//            val shoutObject = article.getVertex.pipe.inE(Label.SHOUT).iterator().map { ed =>
//                val message = ed.getOrElse("message", "")
//                val time = ed.getOrElse("timeOrder", 0L).abs
//                val userShout = ed.getVertex(Direction.OUT).toCC[User].get
//                ShoutStreamObject(userShout, message,article, new Date(time))
//            }.toList
//
//            var postJSONized:JObject = ("id" -> article.getId.toString) ~
//                ("creator" -> article.creator.toJSON(photo = true)) ~
//                ("title" -> article.title) ~
//                ("message" -> message) ~
//                ("origin" ->
//                    ("id" -> article.origin.getId) ~
//                        ("name" -> article.origin.getName) ~
//                        ("kind" -> postKind)
//                    ) ~
//                ("keywords" -> article.tags) ~
//                ("thumbnail_url" -> article.thumbUrl) ~
//                ("response_count" -> article.getResponseCount) ~
//                ("likes_count" -> article.getLikesCount) ~
//                ("creation_time" -> article.creationTime) ~
//                ("kind" -> article.kind)
////                ("kind" -> article.kind) ~
////                ("attachments" -> "") // @TODO(fajr): add attachment
//
//            if (article.getVertex.pipe.in(Label.SHOUT).count > 0) {
//                shoutObject.headOption.map { sO =>
//                    postJSONized = postJSONized ~
//                        ("shout" -> true) ~
//                        ("shout_user_id" -> sO.shouter.getId) ~
//                        ("shout_user_name" -> sO.shouter.name) ~
//                        ("shout_message" -> sO.message) ~
//                        ("parent_id" -> article.getId.toString)
//                }.getOrElse {
//                    postJSONized = postJSONized ~
//                        ("shout" -> false) ~
//                        ("shout_user_id" -> "") ~
//                        ("shout_user_name" -> "") ~
//                        ("shout_message" -> "") ~
//                        ("parent_id" -> "")
//                }
//            }
//
//            postJSONized
//        }
//
//        /**
//         * get shorted description of message
//         * @param message message dari article
//         * @param compiled apakah akan dicompile atau tidak
//         * @param width panjang message dari return
//         * @return
//         */
//        def getShortDesc(message:String, compiled:Boolean = false, width:Int = 32) = {
//            val shortedMessage = if (compiled) {
//                // @TODO(fajr): make message compiler here
//                message
//            } else {
//                message
//            }
//
//            if (shortedMessage.length > width) {
//                shortedMessage.substring(0, width).concat("...")
//            } else {
//                shortedMessage
//            }
//        }
//
//
//    }
//
//    implicit def implicitArticleJsonable(article:Article) = new ArticleWrapper(article)
//    implicit def implicitArticleJsonable(article:Seq[Article]):JValue = {
//        article.map(_.toJSON())
//    }
//}
