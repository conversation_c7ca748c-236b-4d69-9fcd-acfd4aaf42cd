/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.restapi.builder.compat

import java.text.SimpleDateFormat

import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model._
import com.ansvia.digaku.web.DigakuWebCore
import com.ansvia.graph.BlueprintsWrapper._
import net.liftweb.json.JsonDSL._
import net.liftweb.json._

/**
 * Author: fajrhf
 *
 */
object UserBuilderCompat extends DbAccess {

    protected class UserWrapper(u:User) {
        val sdf = new SimpleDateFormat("yyyy/MM/dd")

        def toJSON(photo:Boolean = false, supporting:Boolean = false, supporters:Boolean = false,
                   attr:List[(String)] = List.empty):JsonAST.JObject = {
            var attributes = attr

            var userJSONized = ("id" -> u.getId.toString) ~
                ("name" -> u.name) ~
                ("full_name" ->  u.fullName) ~
                ("sex" -> u.sex) ~
                ("location" -> u.location) ~
                ("self_descs" -> u.selfDescs.split(",").map(_.trim).toList) ~
                ("level" -> u.level) ~
                ("point" -> u.point) ~
                ("creation_time" -> u.joinTime / 1000) ~
                ("timezone" -> u.timezone) ~
                ("verified" -> u.activated) ~
                ("locale" -> u.locale)


            if (photo) {
                attributes = "photo" :: attributes
            }

            if (supporting) {
                attributes = "supporting_count" :: attributes
            }

            if (supporters) {
                attributes = "supporters_count" :: attributes
            }

            val dummyTrophies = ("id" -> "4eae53a15ceadd7547000024") ~
            ("name" -> "Meteora") ~
            ("desc" -> "for reaching level 1.") ~
            ("trophy_class" -> "meteora") ~
                ("origin" -> null) ~
            ("external_icon" -> "None")

            attributes.map {
                case "activated" => userJSONized = userJSONized ~ ("activated" -> u.activated)
                case "email" => userJSONized = userJSONized ~ ("email" -> u.emailLogin)
                case "post_count" => userJSONized = userJSONized ~ ("post_count" -> u.getVertex.pipe.out(Label.COLLECTION_VERTEX)
                    .has("kind", CollectionVertex.Kind.PUBLISH_CONTENT).out(Label.PUBLISH_CONTENT).count())
                case "channel_joined_count" => userJSONized = userJSONized ~ ("channel_joined_count" -> u.getJoinedChannelCount(true))
                case "channel_founded_count" => userJSONized = userJSONized ~ ("channel_founded_count" -> u.getOwnedChannelCount)
                case "trophies" => userJSONized = userJSONized ~ ("thropies" -> List(dummyTrophies)) // @TODO(fajr): tambahin ini setelah ada trophi
                case "photo" => userJSONized = userJSONized ~
                    ("photos" -> List(
                        DigakuWebCore.fixHttpProtocol(u.photoSmall),
                        DigakuWebCore.fixHttpProtocol(u.photoMedium),
                        DigakuWebCore.fixHttpProtocol(u.photoLarge)
                    ))
                case "supporting_count" => userJSONized = userJSONized ~ ("supporting_count" -> u.supportingCount)
                case "supporters_count" => userJSONized = userJSONized ~ ("supporters_count" -> u.supportersCount)
                case _ =>
            }

            userJSONized
        }
    }

    implicit def implicitUserJsonable(user:User) = new UserWrapper(user)
    implicit def implicitUsersToJvalue(users:Iterable[User]):JValue =
        users.map {_.toJSON()}


}
