///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.restapi.builder.compat
//
//import com.ansvia.digaku.model._
//import com.ansvia.digaku.util.MyOembedHelper
//import net.liftweb._
//import json._
//import JsonDSL._
//import scala.collection.JavaConversions._
//import com.ansvia.graph.BlueprintsWrapper._
//import com.tinkerpop.blueprints.Direction
//import java.util.{UUID, Date}
//import com.ansvia.digaku.exc.InvalidParameterException
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.restapi.builder.compat.UserBuilderCompat._
//import com.ansvia.digaku.restapi.V1Time
//import com.ansvia.digaku.utils.TextCompiler
//
///**
// * Author: fajrhf
// *
// */
//object PostBuilderCompat extends DbAccess with V1Time /*with OembedSimple*/ {
//
//    private val SAVED_CODE_TAG = "[saved-code-%s]"
//    private def nextId() = UUID.randomUUID().toString
//
//    protected class PostWrapper(postBase:Post) {
//
//        def genSavedCode() = SAVED_CODE_TAG.format(nextId())
//
//        private def toMarkdown(message:String):String = {
//            var preCode = Map[String, String]()
//
//            val EMPHASIS_RE = """(_)(.*?)\1""".r
//            val ITALIC_RE = """(__)(.*?)\1""".r
//            val STRONG_RE = """(\*)(.*?)\1""".r
////            val BOLD_RE = """(\*\*)(.*?)\1""".r
////            val IMAGE_RE = """(?:^|([\s(>])|\[|\{)!(.*?)(?:\((.*)\))?!(?::(\S+))?(?:$|([\s)])|\]|\})""".r
//
//            var rv = message
//
//            //leave image as it is
////            rv = IMAGE_RE.replaceAllIn(rv, { re =>
////                val id = genSavedCode()
////                if(Option[String](re.group(3)).isDefined) {
////                    preCode += id -> """![%s](%s)""".format(re.group(3), re.group(2))
////                } else {
////                    preCode += id -> """![](%s)""".format(re.group(2))
////                }
////                id
////            })
//
//            rv = ITALIC_RE.replaceAllIn(rv, {re =>
//                val id = genSavedCode()
//                preCode += id -> """*%s*""".format(re.group(2))
//
//                id
//            })
//
//            rv = STRONG_RE.replaceAllIn(rv, {re =>
//                val id = genSavedCode()
//                if (re.group(2) != "") {
//                    preCode += id -> """**%s**""".format(re.group(2))
//                } else {
//                    preCode += id -> re.group(0)
//                }
//                id
//            })
//
//            // leave double wildcard as it is
//            // rv = BOLD_RE.replaceAllIn(rv, {re =>
//            //     val id = genSavedCode()
//            //     preCode += id -> """**%s**""".format(re.group(2))
//            //     id
//            // })
//
//            rv = EMPHASIS_RE.replaceAllIn(rv, {re =>
//                val id = genSavedCode()
//                if (re.group(2) != "") {
//                    preCode += id -> """*%s*""".format(re.group(2))
//                } else {
//                    preCode += id -> re.group(0)
//                }
//                id
//            })
//
//            for( (k,v) <- preCode ){
//                rv = rv.replace(k, v)
//            }
//
//            rv
//        }
//
//        def toJSON(attr:List[String] = List.empty,
//                   format:String = "markdown",
//                   currentUser:Option[User]=None
//                      ):JsonAST.JObject = {
//
//            val post = postBase match {
//                case sp:SimplePost => sp
//                case art:Article => art
//            }
//
//            if (!List("plain", "html", "markdown").contains(format)) {
//                throw InvalidParameterException("format should be one of: plain, html or markdown")
//            }
//
//            val isLiked = if (currentUser.isDefined) {
//                post.isLiker(currentUser.get)
//            } else {
//                false
//            }
//
//            val preview = attr.contains("preview")
//
//            val postOriginKind = post.origin.kind match {
//                case OriginKind.SUB_FORUM => "Group"
//                case OriginKind.USER => "User"
//                case _ => "None"
//            }
//
//            val shoutObject = post.getVertex.pipe.inE(Label.SHOUT).iterator().map { ed =>
//                val message = ed.getOrElse("message", "")
//                val time = ed.getOrElse("timeOrder", 0L).abs
//                val userShout = ed.getVertex(Direction.OUT).toCC[User].get
//                ShoutStreamObject(userShout, message,post, new Date(time))
//            }.toList
//
//            val attachment:JObject = {
//                val embs = post.getEmbeddedObjects
//                if (embs.length > 0) {
//                    embs.headOption.map {
//                        case picture:Picture =>
//                            ("id" -> picture.getId.toString) ~
//                                ("thumbnail" -> picture.smallUrl) ~
//                                ("medium" -> picture.mediumUrl) ~
//                                ("original" -> picture.largeUrl) ~
//                                ("creation_time" -> picture.creationTime.toV1) ~
//                                ("kind" -> "EmbedPic")
//                        case el:EmbeddedLink if el.kind == LinkKind.VIDEO && el.thumbnail != "" =>
//
//                            val html = MyOembedHelper.getOembedData(el)
//
//                            ("id" -> post.getId.toString) ~
//                                ("kind" -> "EmbedVideoLink") ~
//                                ("title" -> el.title) ~
//                                ("description" -> el.desc) ~
//                                ("thumbnail_url" -> el.thumbnail) ~
//                                ("html" -> html) ~
//                                ("url" -> el.url)
//                        case el:EmbeddedLink if el.thumbnail != "" =>
//                            ("id" -> post.getId.toString) ~
//                                ("title" -> el.title) ~
//                                ("desc" -> el.desc) ~
//                                ("thumbnail" -> el.thumbnail) ~
//                                ("url" -> el.url) ~
//                                ("creation_time" -> post.creationTime.toV1) ~
//                                ("kind" -> "EmbedLink")
//                        case _ =>
//                            List.empty:JObject
//                    }.getOrElse(List.empty:JObject)
//                } else {
//                    List.empty:JObject
//                }
//            }
//
//            var postJSONized:JObject = {
//                post match {
//                    case sp:SimplePost => {
//                        ("id" -> sp.getId.toString) ~
//                            ("creator" -> sp.creator.toJSON(photo = true)) ~
//                            ("message" -> sp.getContent) ~
//                            ("origin" ->
//                                ("id" -> sp.origin.getId.toString) ~
//                                    ("name" -> post.origin.getName) ~
//                                    ("kind" -> postOriginKind)
//                                ) ~
//                            ("response_count" -> sp.getResponseCount) ~
//                            ("liked" -> isLiked ) ~
//                            ("likes_count" -> sp.getLikesCount) ~
//                            ("creation_time" -> sp.creationTime.toV1) ~
//                            ("kind" -> "Mind")
//                    }
//                    case article:Article => {
//
//                        val message = if (preview) {
//                            //perlu menggunakan getShortDesc ini, tidak bisa menggunakan
//                            // article.shortDesc karena outputnya berbeda
//                            getShortDesc(article.getContent, compiled=true, width=128)
//                        } else {
//                            article.getContent
//                        }
//
//                        val formattedContent = format match {
//                            case "html" => TextCompiler.compileMessage(message)
//                            case "markdown" => toMarkdown(message)
//                            case _ => message
//                        }
//
//                        ("id" -> article.getId.toString) ~
//                            ("creator" -> article.creator.toJSON(photo = true)) ~
//                            ("title" -> article.title) ~
//                            ("message" -> formattedContent) ~
//                            ("origin" ->
//                                ("id" -> article.origin.getId.toString) ~
//                                    ("name" -> article.origin.getName) ~
//                                    ("kind" -> postOriginKind)
//                                ) ~
//                            ("keywords" -> article.tags.split(",").toList) ~
//                            ("thumbnail_url" -> article.thumbUrl) ~
//                            ("response_count" -> article.getResponseCount) ~
//                            ("likes_count" -> article.getLikesCount) ~
//                            ("creation_time" -> article.creationTime.toV1) ~
//                            ("kind" -> "Article") ~
//                            ("view_count" -> article.viewsCount)
//                    }
//                }
//            }
//
//            //apabila ada attachment nya dimunculin, kalo tidak ada key attachment nggak dimunculin
//            if(attachment.obj.length > 1)
//                postJSONized = postJSONized ~ ("attachments" -> List(attachment))
//
//            if (post.getVertex.pipe.in(Label.SHOUT).count > 0) {
//                shoutObject.headOption.map { sO =>
//                    postJSONized = postJSONized ~
//                        ("shout" -> true) ~
//                        ("shout_user_id" -> sO.shouter.getId.toString) ~
//                        ("shout_user_name" -> sO.shouter.name) ~
//                        ("shout_message" -> sO.message) ~
//                        ("parent_id" -> post.getId.toString)
//                }.getOrElse {
//                    postJSONized = postJSONized ~
//                        ("shout" -> false) ~
//                        ("shout_user_id" -> "") ~
//                        ("shout_user_name" -> "") ~
//                        ("shout_message" -> "") ~
//                        ("parent_id" -> "")
//                }
//            } else {
//                postJSONized = postJSONized ~
//                    ("shout" -> false) ~
//                    ("shout_user_id" -> "") ~
//                    ("shout_user_name" -> "") ~
//                    ("shout_message" -> "") ~
//                    ("parent_id" -> "")
//            }
//
//            postJSONized
//        }
//
//        /**
//         * get shorted description of message
//         * @param message message dari article
//         * @param compiled apakah akan dicompile atau tidak
//         * @param width panjang message dari return
//         * @return
//         */
//        def getShortDesc(message:String, compiled:Boolean = false, width:Int = 32) = {
//            val shortedMessage = if (compiled) {
//                // @TODO(fajr): make message compiler here
//                message
//            } else {
//                message
//            }
//
//            if (shortedMessage.length > width) {
//                shortedMessage.substring(0, width).concat("...")
//            } else {
//                shortedMessage
//            }
//        }
//
//
//    }
//
//    implicit def implicitPostJsonable(p:Post) = new PostWrapper(p)
//    implicit def implicitPostsJsonable(p:Seq[Post]):JValue = {
//        p.map(_.toJSON())
//    }
//    implicit def implicitPostsJsonable(p:List[Post]):JValue = {
//        p.map(_.toJSON())
//    }
//}
