///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.restapi.builder.compat
//
//import com.ansvia.digaku.model._
//import com.ansvia.digaku.restapi.builder.compat.UserBuilderCompat._
//import net.liftweb.json.JsonDSL._
//import net.liftweb.json._
//
///**
// * Author: fajrhf
// *
// */
//object PrivateMessagetBuilderCompat {
//
//    protected class PMWrapper(pm:PrivateMessage) {
//        def toJSON(last2Responses:Boolean = false,
//                   readUser:User=null,
//                   attr:List[(String)] = List.empty):JsonAST.JObject = {
//
//            var postJSONized:JObject = ("id" -> pm.getId.toString) ~
//                ("creation_time" -> pm.creationTime) ~
//                ("creator" -> pm.creator.toJSON(photo = true)) ~
//                ("message" -> pm.content) ~
//                ("participants" -> pm.getParticipants.toList.map { participant =>
//                    participant.toJSON(attr=List("photo"))
//                }) ~
//                ("response_count" -> pm.getResponseCountByUser(readUser)) ~
//                ("creation_time" -> pm.creationTime)
//
//            if(readUser != null) {
//                postJSONized = postJSONized ~ ("read" -> pm.isParticipantHasRead(readUser))
//            }
//
//            if (pm.getResponseCountByUser(readUser) > 0 && last2Responses) {
//                postJSONized = postJSONized ~ ("last_2_responses" -> pm.getResponses(readUser, 0, 2).toList.map { resp =>
//                    implicitPMRespJsonable(resp).respToJSON()
//                })
//            }
//
//            postJSONized
//        }
//    }
//
//    protected class PMResponseWrapper(pmr:MessageResponse) {
//        def respToJSON():JsonAST.JObject = {
//            ("id" -> pmr.getId) ~
//            ("creator" -> pmr.creator.toJSON(photo = true)) ~
//            ("message" -> pmr.content) ~
//            ("deleted" -> pmr.deleted) ~
//            ("creation_time" -> pmr.creationTime)
//        }
//    }
//
//    implicit def implicitPMJsonable(pm:PrivateMessage) = new PMWrapper(pm)
//    implicit def implicitPMsJsonable(pms:Iterable[PrivateMessage]):JValue = {
//        pms.map(_.toJSON())
//    }
//
//    implicit def implicitPMRespJsonable(pmr:MessageResponse) = new PMResponseWrapper(pmr)
//    implicit def implicitPMRespsJsonable(pmrs:Iterable[MessageResponse]):JValue = {
//        pmrs.map(_.respToJSON())
//    }
//}
