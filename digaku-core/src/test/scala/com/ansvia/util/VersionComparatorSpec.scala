/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.util

/**
 * Author: robin
 * Date: 1/2/14
 * Time: 5:25 PM
 *
 */


 import com.ansvia.digaku.DigakuTest
import org.specs2.mutable.Specification
import org.specs2.specification.Scope


class VersionComparatorSpec extends Specification with DigakuTest {

    class Ctx extends Scope {
        val vc = new VersionComparator()
    }

    "VersionComparator" should {
        "0.1 lebih kecil dari 0.2" in new Ctx { vc.compare("0.1", "0.2") must_== -1 }
        "0.1.3 lebih kecil dari 0.1.4" in new Ctx { vc.compare("0.1.3", "0.1.4") must_== -1 }
        "0.2.3 lebih besar dari 0.1.4" in new Ctx { vc.compare("0.2.3", "0.1.4") must_== 1 }
        "1.0.3-SNAPSHOT lebih besar dari 1.0.2-SNAPSHOT" in new Ctx { vc.compare("1.0.3-SNAPSHOT", "1.0.2-SNAPSHOT") must_== 1 }
        "1.0.2-SNAPSHOT sama dengan 1.0.2-SNAPSHOT" in new Ctx { vc.compare("1.0.2-SNAPSHOT", "1.0.2-SNAPSHOT") must_== 0 }
    }


}

