///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//
///**
// * Author: andrie
// * Date: 2/12/14
// * Time: 3:30 PM
// *
// */
//class ChannelLogoDefaultSpec extends Specification with DigakuTest {
//    def is = "Group Logo Default should" ^
//        sequential ^
//        p ^
//        "create group logo" ! trees.create ^
//        "get group logo by name" ! trees.getByName ^
//        "logo is exist" ! trees.logoExist ^
//        "get list logo group default" ! trees.logoGetList ^
//    end
//
//    object trees {
//
//        val logo1 = ChannelLogoDefault.create("http://dmcd6hvaqrxz0.cloudfront.net/844fa43bfba500d219f758b3e6f67634.jpg", "logosatu")
//        val logo2 = ChannelLogoDefault.create("http://dmcd6hvaqrxz0.cloudfront.net/844fa43bfba500d219f758b3e6f67634.jpg", "logodua")
//
//        def create = {
//            logo1 must beAnInstanceOf[ChannelLogoDefault]
//        }
//
//        def getByName = {
//            ChannelLogoDefault.getByName(logo1.logoDefaultName).isDefined must beTrue
//        }
//
//        def logoExist = {
//            ChannelLogoDefault.existByName(logo1.logoDefaultName) must beTrue
//        }
//
//        def logoGetList = {
//            ChannelLogoDefault.getListLogoDefault.toList must contain(logo1, logo2).only
//        }
//
//    }
//
//}
