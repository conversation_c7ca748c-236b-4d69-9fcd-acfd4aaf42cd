/*
* Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
*
* This file is part of Digaku project.
*
* Unauthorized copying of this file, via any medium is strictly prohibited
* Proprietary and confidential.
*/

package com.ansvia.digaku.model

import com.ansvia.digaku.exc.PermissionDeniedException
import org.specs2.Specification
import com.ansvia.digaku.DigakuTest
import org.specs2.specification.{Step, Fragments}

/**
* Author: nadir
*
*/
class ReportableSpec extends Specification with DigakuTest  {
    def is: Fragments = {
        sequential ^
            "Reportable should" ^
            p ^
                "add report for content" ! trees.reportContent ^
                "get report from content" ! trees.getReportFromContent ^
                "get reporting count from content" ! trees.getReportCount ^
                "User tidak bisa report dua kali untuk satu post" ! trees.reportTwice ^
                "User tidak bisa report post sendiri" ! trees.reportOwnPost ^
                "delete reports from content" ! trees.deleteAllReport ^
            end
    }

    object trees {
        val user1 = genUser
        val user2 = genUser
        val user3 = genUser

        val channel1 = genForumWithOwner(user1)
        val channel2 = genForumWithOwner(user2)

//        channel1.setAllPermission(ForumPermission.ALL)
//        channel2.setAllPermission(ForumPermission.ALL)

        db.commit()

        user2.support(user1)
        user1.support(user2)
        user2.support(user3)
        user3.support(user2)
        user1.support(user3)
        user3.support(user1)

        val article1 = Article.create(user2, "ini artikel dari user2 ke channel1", "content dari artikel", "tags1, tags2", channel1)

        val resp2 = article1.addResponse(user1, "user1 response article1")
        val reportArticle1 = article1.addReport(user3, "artikel mengandung konten berbau porno")
        val report2Article1 = article1.addReport(user1, "artikel mengandung konten berbau porno")

        def reportContent = {
                (reportArticle1 must beAnInstanceOf[AbuseReport])
        }

        def getReportFromContent = {
            article1.getReports(0, 10) must be contain(report2Article1, reportArticle1)
        }

        def getReportCount = {
            article1.getReportCounts must beEqualTo(2)
        }

        def reportTwice = {
            article1.addReport(user3, "aku ngereport lagi") must throwAn[PermissionDeniedException]
        }

        def reportOwnPost = {
            article1.addReport(user2, "aku ngereport post ku sendiri") must throwA[PermissionDeniedException]
        }

        def deleteAllReport = {
            article1.deleteReports()
            article1.getReports(0, 10) must beEmpty
        }

    }

}
