package com.ansvia.digaku.model

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.exc.PermissionDeniedException
import org.specs2.Specification
import org.specs2.specification.Step

/**
 * Author: fajr
 *
 */
class MoveableSpec extends Specification with DigakuTest {
    def is= {
        sequential ^
          "Response should" ^
          p ^
          "move thread to other subforum" ! trees.moveArticleToOtherSubforum ^
          "moderator can move thread" ! trees.mutualModeratorCanMoveArticle ^
          "administrator can move thread" ! trees.adminMoveToArticle ^
          "user cannot move thread" ! trees.userCantMoveArticle ^
          "add auto reply post after move an article" ! trees.addReplyPost ^
          end
    }

    object trees {

        val article = Article("article title", "this Article content")
        val endUser1 = genUser

        val userMod1 = genUser

        //user admin
        val userAdm1 = genUser
        userAdm1.setRole(UserRole.ADMIN)

        val forum1 = genForumWithOwner(userMod1)
        val subForum1 = genForumWithOwner(userMod1)
        val subForum2 = genForumWithOwner(userMod1)
        val subForum3 = genForumWithOwner(userMod1)
        forum1.addSubForum(subForum1)
        forum1.addSubForum(subForum2)
        forum1.addSubForum(subForum3)

        subForum1.addMembers(userAdm1, endUser1)
        subForum2.addMembers(userAdm1, endUser1)
        subForum3.addMembers(userAdm1, endUser1)

        val reason = "this content don't belong in ForumX"

        val article1 = Article.create(userMod1, "article title", "this Article content", "tags", subForum1)

        def moveArticleToOtherSubforum = {
            article1.setMoved(MovedType.SOFT, MovedRole.ADMIN_CHANNEL, userMod1, subForum2.getId, reason)
            val art1 = Post.getById(article1.getId).get
            art1.origin must beEqualTo(subForum2)
        }

        def mutualModeratorCanMoveArticle = {
            article1.setMoved(MovedType.SOFT, MovedRole.ADMIN_CHANNEL, userMod1, subForum3.getId, reason)
            val art1 = Post.getById(article1.getId).get
            art1.origin must beEqualTo(subForum3)
        }

        def adminMoveToArticle = {
            val article2 = Article.create(userAdm1, "article title1", "this Article content", "tags", subForum1)
            article2.setMoved(MovedType.SOFT, MovedRole.ADMIN_SYSTEM, userAdm1, subForum2.getId, reason)
            val art2 = Post.getById(article2.getId).get

            art2.origin must beEqualTo(subForum2)
        }

        def userCantMoveArticle = {
            val article3 = Article.create(endUser1, "article title1", "this Article content", "tags", subForum1)
            article3.setMoved(MovedType.SOFT, MovedRole.ADMIN_SYSTEM, endUser1, subForum2.getId, reason) must throwAn[PermissionDeniedException]
        }

        def addReplyPost = {
            val article2 = Article.create(userAdm1, "article title1", "this Article content", "tags", subForum1)
            article2.setMoved(MovedType.SOFT, MovedRole.ADMIN_SYSTEM, userAdm1, subForum2.getId, reason)
            val art2 = Post.getById(article2.getId).get

            val lastResp = art2.getLastResponse.map(_.content).getOrElse("")

            lastResp must beEqualTo(s"moved from #${subForum1.name} to #${subForum2.name} because $reason")
        }
    }
}
