/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import org.specs2.Specification
import com.ansvia.digaku.DigakuTest
import java.util.{Date, Calendar}
import com.ansvia.digaku.utils.DateUtils
import org.joda.time.DateTime

/**
 * Author: robin
 *
 */
class EventReminderSpec extends Specification with DigakuTest {

    def is = "EventReminder should" ^
        sequential ^
        p ^
            "be able to create new reminder" ! trees.create ^
            "user has reminder when attend the event" ! trees.userHasReminderWhenAttend ^
            "get list" ! trees.getList ^
            "get list with afterDate" ! trees.getListAfterDate ^
            "get list coming" ! trees.getListComing ^
        end


    object trees {

        val user1 = genUser
        val user2 = genUser
        val ch = genForum
//        ch.setAllPermission(ForumPermission.ALL)
        val event = Event.create(user1, "event title", "event content", "location", ch, dueDate, finishDate)

        /**
         * untuk menggenerasikan due date
         * +1 hari dari hari ini.
         * @return
         */
        private def dueDate = {
            val cal = Calendar.getInstance()
            cal.setTime(new Date())
            cal.add(Calendar.DATE, 1)
            cal.getTime
        }

        private def finishDate = {
            val cal = Calendar.getInstance()
            cal.setTime(new Date())
            cal.add(Calendar.DATE, 2)
            cal.getTime
        }

        def create = {
            event.reload()
            user1.reload()

            val er = EventReminder.create(event, user1)

            (er.title must beEqualTo(event.title))
            (event.getReminders(0, 1).toList.headOption.isDefined must beTrue) and
                (event.getReminders(0, 1).toList.head.title must beEqualTo(event.title))
        }

        def userHasReminderWhenAttend = {
            event.reload()
            user2.reload()

            event.addAttenders(AttenderKind.POSITIVE, user2)

            event.reload()

            // user1 ada dua karena yang pertama dibuat di #create secara manual via EventReminder.create
            event.getReminders(0, 2).toList.flatMap(_.user) must be contain(user1, user1, user2) only
        }

        def getList = {

            Thread.sleep(1000L)
            val user1 = genUser

            val event = Event.create(user1, "event title", "event content", "location", ch, dueDate, finishDate)
            val user2 = genUser

            Thread.sleep(1000L)

            event.reload()
            event.addAttenders(AttenderKind.POSITIVE, user2)

            val user3 = genUser

            Thread.sleep(1000L)

            event.reload()
            event.addAttenders(AttenderKind.POSITIVE, user3)

            val lst = EventReminder.getList(0, 10).toList

            (lst.flatMap(_.user) must be contain(user3, user2, user1) inOrder) and
                (lst.flatMap(_.user).toList must be contain(user2, user3, user1) only)
        }

        def getListAfterDate = {
            Thread.sleep(1000L)

            import com.ansvia.graph.BlueprintsWrapper._

            // clear up db for events
            Event.rootVertex.pipe.out(Event.rootVertexLabel).remove()
            EventReminder.rootVertex.pipe.out(EventReminder.rootVertexLabel).remove()
            db.commit()

            val dd = dueDate
            val cal = Calendar.getInstance()
            cal.setTime(dd)
            cal.add(Calendar.DAY_OF_MONTH, -2)

            val user1 = genUser
            val event = Event.create(user1, "event title afterDate", "event content", "location", ch, dd, finishDate)
            val user2 = genUser
            Thread.sleep(1000L)

            event.reload()
            event.addAttenders(AttenderKind.POSITIVE, user2)

            val user3 = genUser

            Thread.sleep(1000L)

            event.reload()
            event.addAttenders(AttenderKind.POSITIVE, user3)

            Thread.sleep(1000L)

            val lstEmpty = EventReminder.getList(dd, 0, 0, 10).toList
            val lstEmpty2 = EventReminder.getList(dd, 1, 0, 10).toList
            val lstContain = EventReminder.getList(cal.getTime, 0, 0, 10).toList

            lstContain.foreach(println)

            (lstEmpty must beEmpty) and
            (lstEmpty2 must beEmpty) and
                (lstContain.length must beEqualTo(3))
        }

        def getListComing = {
            EventReminder.clear()

            val ch = genForum
//            ch.setAllPermission(ForumPermission.ALL)

            Thread.sleep(1000L)

            val dd = new DateTime().plusDays(3).toDate
            val fd = new DateTime().plusDays(4).toDate

            val user1 = genUser
            val event = Event.create(user1, "event title coming", "event content", "location", ch, dd, fd)
            val user2 = genUser

            Thread.sleep(1000L)

            event.reload()
            event.addAttenders(AttenderKind.POSITIVE, user2)

            val user3 = genUser

            Thread.sleep(1000L)

            event.reload()
            event.addAttenders(AttenderKind.POSITIVE, user3)

            Thread.sleep(1000L)

            val lstEmpty = EventReminder.getListComing(2, 0, 0, 10).toList
            val lstContain = EventReminder.getListComing(3, 0, 0, 10).toList

            (lstEmpty must beEmpty) and
                (lstContain.length must beEqualTo(3))
        }
    }
}
