/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.io.{BufferedReader, File, FileReader}

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.utils.MailSender
import org.apache.commons.codec.binary.Base64
import org.specs2.Specification

/**
 * Author: robin
 *
 */
class EmailDraftSpec extends Specification with DigakuTest {
    def is = "EmailDraft harusnya" ^
        sequential ^
        p ^
        "bisa create email draft" ! trees.create ^
        p ^
        "bisa compile berdasarkan jenis:" ^
            p ^
            "plain" ! trees.compilePlain ^
            "html" ! trees.compileHtml ^
            "textile" ! trees.compileTextile ^
        p ^
        "bisa menggunakan email transport" ! trees.useMailTransport ^
        "bisa mengirimkan email" ! trees.sendEmail ^
        "get email draft list" ! trees.getList ^
        p ^
        "untuk localization" ^
        p ^
            "by default locale ketika create adalah en_US" ! locale.localeDefault ^
            "bisa en_US" ! locale.localeEn ^
            "bisa id_ID" ! locale.localeID ^
            "email yang didapat user harus sama dengan locale-nya" ! locale.receiverLocale ^
            "bisa dapetin available locales" ! locale.availableLocales ^
        end

    val mtc = new MailTemplateCompilerIface {
        def compile(text: String) = text
        override def compileContent(text: String): String = text
    }

    object trees {

        def create = {

            val emailDraft = EmailDraft.create("ubai","<EMAIL>", "hello", "content", EmailKind.PLAIN)

            emailDraft must beAnInstanceOf[EmailDraft]
        }

        def compilePlain = {
            val emd = EmailDraft.create("ubai","<EMAIL>", "hello", "content plain", EmailKind.PLAIN)
            emd.compile(Locale.DEFAULT_LOCALE) must beEqualTo("content plain")
        }

        def compileHtml = {
            val emd = EmailDraft.create("ubai","<EMAIL>", "hello", "<p>content plain</p>", EmailKind.HTML)
            (emd.compile(Locale.DEFAULT_LOCALE) must beEqualTo("<p>content plain</p>")) and
                (emd.contentType must beEqualTo("text/html"))
        }

        def compileTextile = {
            val emd = EmailDraft.create("ubai","<EMAIL>", "hello", "**content** plain", EmailKind.TEXTILE)
            (emd.compile(Locale.DEFAULT_LOCALE) must beEqualTo("<p><b>content</b> plain</p>")) and
                (emd.contentType must beEqualTo("text/html"))
        }

        def useMailTransport = {
            val emd = EmailDraft.create("ubai","<EMAIL>", "hello", "**content** plain", EmailKind.TEXTILE)
            val u1 = genUser
            val u2 = genUser

            emd.setReceivers(u1, u2) must beAnInstanceOf[EmailTransport]
        }

        def sendEmail = {
            val emd = EmailDraft.create("ubai","<EMAIL>", "hello", "**content** plain", EmailKind.TEXTILE)
            val u1 = genUser
            val u2 = genUser

            val outPath = "/tmp/digaku-mail-debug.html"
            val f = new File(outPath)

            if (f.exists())
                f.delete()

            MailSender.debug = true

            emd.setReceivers(u1, u2)
                .setMailSender(MailSender("dummy", 123))
                .setTemplateCompiler(mtc)
                .setDuplicateDetectorEnabled(false)
                .send()

            Thread.sleep(2000)

            // get last line
            val br = new BufferedReader(new FileReader(f))

            var lastLine = ""
            var line = br.readLine()
            var kindHtml = false

            while (line != null){
                line = br.readLine()

                if (line != null && line.trim.contains("Content-Type: text/html"))
                    kindHtml = true

                if (line!=null)
                    lastLine = line
            }

            br.close()

            (f.exists() must beTrue) and
            (kindHtml must beTrue) and
            (lastLine must beEqualTo("<p><b>content</b> plain</p>"))
        }

        def getList = {
            EmailDraft.getList(0, 10).length mustNotEqual(0)
        }

    }

    object locale {


        val emd = EmailDraft.create("robin", "<EMAIL>", "hello", "kitty", EmailKind.PLAIN)
        emd.addContent(Locale.id_ID, "halo", "anak kucing")

        def localeDefault = {
            emd.getContent(Locale.en_US).map(_.locale).getOrElse("none") must beEqualTo("en_US")
        }

        def localeEn = {
            (emd.getContent(Locale.en_US).map(_.subject).getOrElse("none") must beEqualTo("hello")) and
            (emd.getContent(Locale.en_US).map(_.content).getOrElse("none") must beEqualTo("kitty"))
        }

        def localeID = {
            (emd.getContent(Locale.id_ID).map(_.subject).getOrElse("none") must beEqualTo("halo")) and
                (emd.getContent(Locale.id_ID).map(_.content).getOrElse("none") must beEqualTo("anak kucing"))
        }

        def receiverLocale = {
            val emd = EmailDraft.create("ubai","<EMAIL>", "hello", "<strong>what happened?</strong>", EmailKind.HTML)

            emd.addContent(Locale.id_ID, "halo", "<strong>Ada apa?</strong>")
            emd.reload()
            emd.addContent(Locale.ja_JP, "こんにちは", "<strong>何が起こったのか？?</strong>")

            val u2 = genUser

            u2.locale = Locale.ja_JP
            u2.save()
            db.commit()

            val outPath = "/tmp/digaku-mail-debug.html"
            val f = new File(outPath)

            if (f.exists())
                f.delete()

            MailSender.debug = true

            emd.setReceivers(u2)
                .setMailSender(MailSender("dummy", 123))
                .setTemplateCompiler(mtc)
                .setDuplicateDetectorEnabled(false)
                .send()

            Thread.sleep(2000)

            // get last line
            val br = new BufferedReader(new FileReader(f))

            var lastLine = ""
            var line = br.readLine()
            var kindHtml = false

            while (line != null){
                line = br.readLine()

                if (line != null && line.trim.contains("Content-Type: text/html"))
                    kindHtml = true

                if (line!=null)
                    lastLine = line
            }

            if (lastLine != null){
                lastLine = new String(Base64.decodeBase64(lastLine), "UTF-8")
            }

            br.close()

            (f.exists() must beTrue) and
                (kindHtml must beTrue) and
                (lastLine must beEqualTo("<strong>何が起こったのか？?</strong>"))
        }

        def availableLocales = {
            val emd = EmailDraft.create("ubai","<EMAIL>", "hello", "<strong>what happened?</strong>", EmailKind.HTML)

            emd.addContent(Locale.id_ID, "halo", "<strong>Ada apa?</strong>")
            emd.reload()
            emd.addContent(Locale.ja_JP, "こんにちは", "<strong>何が起こったのか？?</strong>")

            emd.availableLocales.toList.map(_.locale) must be contain(Locale.id_ID, Locale.ja_JP)
        }
    }

}
