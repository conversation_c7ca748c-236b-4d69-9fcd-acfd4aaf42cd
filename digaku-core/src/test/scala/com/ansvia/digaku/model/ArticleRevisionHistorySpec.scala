///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//
///**
// * Author: robin
// *
// */
//class ArticleRevisionHistorySpec extends Specification with DigakuTest {
//
//    def is = "ArticleRevisionHistory harusnya" ^
//        sequential ^
//        p ^
//            "bisa update dan dapetin history-nya" ! trees.updateAndGetHistory ^
//            "bisa dapetin daftar history-nya" ! trees.getHistories ^
//            "bisa revert ke history-history sebelumnya" ! trees.revertTo ^
//            "bisa diff antar satu history dengan history lainnya" ! trees.diff ^
//            "revert dan update harusnya buat revision baru yang number-nya incremental dari latest" ! trees.revertAndUpdate ^
//        end
//
//    object trees {
//
//        val u1 = genUser
//        val ch1 = genChannelWithOwner(u1)
//        val article = Article.create(u1, "title1", "content1", "tags,ini,itu", ch1)
//
//
//        def updateAndGetHistory = {
//
//            article.update("title2", "content2", "content2","apa ya?", u1, "anstile") must beAnInstanceOf[ArticleRevisionHistory]
//
//        }
//
//        def getHistories = {
//
//            article.update("title3", "content3", "content3", "anu 3", u1, "anstile")
//
//            val rhs = article.getRevisionHistories(0, 2).toList
//
//            (rhs.map(_.title) must be contain("title3", "title2") inOrder) and
//                (rhs.map(_.content) must be contain("content3", "content2") inOrder) and
//                (article.getVertex.getProperty[Long]("revision") must beEqualTo(2))
//        }
//
//        def revertTo = {
//
//            article.revertToRevision(1)
//            article.reload()
//            val artV1 = article.getVertex
//            val title1 = artV1.getProperty[String]("title")
//            val content1 = artV1.getProperty[String]("content")
//            val rev1 = artV1.getProperty[Long]("revision")
//
//            article.reload()
//            article.revertToRevision(2)
//            article.reload()
//            val artV2 = article.getVertex
//            val title2 = artV2.getProperty[String]("title")
//            val content2 = artV2.getProperty[String]("content")
//            val rev2 = artV2.getProperty[Long]("revision")
//
//            article.reload()
//
//            (title1 must beEqualTo("title2")) and
//                (content1 must beEqualTo("content2")) and
//                (rev1 must beEqualTo(1)) and
//                (title2 must beEqualTo("title3")) and
//                (content2 must beEqualTo("content3")) and
//                (rev2 must beEqualTo(2)) and
//                (article.latestRevision must beEqualTo(2))
//
//        }
//
//        def diff = {
//
//            val rhs = article.getRevisionHistories(0, 2).toList
//
//            (rhs(0).diffToHtml(rhs(1)).title must beEqualTo("""<span>title</span><del style="background:#ffe6e6;">2</del><ins style="background:#e6ffe6;">3</ins>""")) and
//                (rhs(0).diffToHtml(rhs(1)).content must beEqualTo("""<span>content</span><del style="background:#ffe6e6;">2</del><ins style="background:#e6ffe6;">3</ins>"""))
//
//        }
//
//        def revertAndUpdate = {
//
//            article.revertToRevision(1)
//            article.reload()
//            val artV1 = article.getVertex
//            val title1 = artV1.getProperty[String]("title")
//            val content1 = artV1.getProperty[String]("content")
//            val rev1 = artV1.getProperty[Long]("revision")
//
//            article.update("title4", "content4", "content4", "coba update lagi", u1, "anstile")
//
//            article.reload()
//
//            (article.getVertex.getProperty[Long]("revision") must beEqualTo(3L)) and
//                (title1 must beEqualTo("title2")) and
//                (content1 must beEqualTo("content2")) and
//                (rev1 must beEqualTo(1))
//
//        }
//
//
//
//    }
//}
