///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.specification.Fragments
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.exc.UnsupportedException
//
//
///**
// * Author: nadir
// * Date: 11/26/13
// * Time: 4:15 PM
// *
// */
//class Connectable3rdSiteSpec extends Specification with DigakuTest {
//    def is: Fragments = {
//        "Connactable should" ^
//         p ^
//            sequential^
//            "user connect to facebook correctly" ! trees.connectToFacebookInfoCorrectly1 ^
//            "user harus memiliki property fb.id dulu untuk dikoneksikan dengan user yang lainnya" ! trees.connectToFacebookInfoCorrectly2 ^
//            "get friends facebook lists" ! trees.getFriendsFacebookList ^
//            "search friends facebook" ! trees.searchFriendsFacebook ^
//            "user connect to twitter correctly" ! trees.connectToTwitterInfoCorrectly1 ^
//            "user harus memiliki property tw.id dulu untuk dikoneksikan dengan user yang lainnya" ! trees.connectToTwitterInfoCorrectly2 ^
//            "get friends twitter list" ! trees.getFriendsTwitterList ^
//            "search friends twitter" ! trees.searchFriendsTwitter ^
//         end
//    }
//
//
//    object trees {
//
//        val user1 = genUser
//        val user2 = genUser
//        val user3 = genUser
//        val user4 = genUser
//        val user5 = genUser
//        val user6 = genUser
//
//        user1.getVertex.setProperty("fb.id", "111")
//        user3.getVertex.setProperty("fb.id", "333")
//        user4.getVertex.setProperty("fb.id", "444")
//        user5.getVertex.setProperty("fb.id", "555")
//        user6.getVertex.setProperty("fb.id", "666")
//
//        user1.getVertex.setProperty("tw.id", "111")
//        user3.getVertex.setProperty("tw.id", "333")
//        user4.getVertex.setProperty("tw.id", "444")
//        user5.getVertex.setProperty("tw.id", "555")
//        user6.getVertex.setProperty("tw.id", "666")
//        db.commit()
//
//        val facebookInfo1 = user1.connectFacebookInfo()
//        val facebookInfo3 = user3.connectFacebookInfo()
//        val facebookInfo4 = user4.connectFacebookInfo()
//        val facebookInfo5 = user5.connectFacebookInfo()
//        val facebookInfo6 = user6.connectFacebookInfo()
//
//        val twitterInfo1 = user1.connectTwitterInfo()
//        val twitterInfo3 = user3.connectTwitterInfo()
//        val twitterInfo4 = user4.connectTwitterInfo()
//        val twitterInfo5 = user5.connectTwitterInfo()
//        val twitterInfo6 = user6.connectTwitterInfo()
//
//        facebookInfo1.addFriends(facebookInfo3, facebookInfo4, facebookInfo5)
//        facebookInfo3.addFriends(facebookInfo1, facebookInfo4, facebookInfo6)
//
//        twitterInfo5.addFriends(twitterInfo3, twitterInfo6)
//        twitterInfo6.addFriends(twitterInfo1, twitterInfo3, twitterInfo4, twitterInfo5)
//
//        def connectToFacebookInfoCorrectly1 = {
//            user1.getFacebookInfo.get must beEqualTo(facebookInfo1)
//        }
//
//        def connectToFacebookInfoCorrectly2 = {
//            user2.connectFacebookInfo() must throwAn[UnsupportedException]
//        }
//
//        def getFriendsFacebookList = {
//            (user1.getFriendsFacebook(0, 10).toList must contain(user3, user4, user5).only) and
//                (user3.getFriendsFacebook(0,10).toList must contain(user1, user4, user6).only)
//        }
//
//        def searchFriendsFacebook = {
//            user1.getFriendsFacebookByName(user5.name, 0, 10).toList must contain(user5).only
//        }
//
//        def connectToTwitterInfoCorrectly1 = {
//            user1.getTwitterInfo.get must beEqualTo(twitterInfo1)
//        }
//
//        def connectToTwitterInfoCorrectly2 = {
//            user2.connectTwitterInfo() must throwAn[UnsupportedException]
//        }
//
//        def getFriendsTwitterList = {
//            (user5.getFriendsTwitter(0, 10).toList must contain(user3, user6).only) and
//                (user6.getFriendsTwitter(0, 10).toList must contain(user1, user3, user4, user5))
//        }
//
//        def searchFriendsTwitter = {
//            user5.getFriendsTwitterByName(user3.name, 0, 10).toList must contain(user3).only
//        }
//
//    }
//}
