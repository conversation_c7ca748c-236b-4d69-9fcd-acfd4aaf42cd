///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import org.specs2.specification.Fragments
//import com.ansvia.digaku.exc.InvalidParameterException
//import org.ocpsoft.prettytime.PrettyTime
//import java.util.Date
//
///**
// * Author: nadir
// * Date: 1/3/14
// * Time: 4:52 PM
// *
// */
//class UserStatusSpec extends Specification with DigakuTest {
//
//    def is: Fragments = {
//        sequential ^
//            "User status should" ^
//            p ^
//            "create user status" ! trees.createUserStatus ^
//            "create user status content length must > 3 character" ! trees.contentLengthLeesThen3 ^
//            "getting creation age" ! trees.getCreationAge ^
//            "get creator user status" ! trees.getCreatorStatus ^
//            "user status soft deleted by user creator" ! trees.removeUserStatus ^
//        end
//    }
//
//
//    object trees {
//
//        val user = genUser
//        val userStatus1 = UserStatus.create(user, "aku adalah anak gembala1")
//        val time = new PrettyTime(new Date()).format(new Date(userStatus1.creationTime))
//
//        def createUserStatus = {
//            UserStatus.getById(userStatus1.getId).isDefined must beTrue
//        }
//
//        def contentLengthLeesThen3 = {
//            UserStatus.create(user, "as") must throwAn[InvalidParameterException]
//        }
//
//        def getCreationAge = {
//            userStatus1.getCreationAge must beEqualTo(time)
//        }
//
//        def getCreatorStatus = {
//            userStatus1.creator must beEqualTo(user)
//        }
//
//        def removeUserStatus = {
//            userStatus1.setDeleted(DeletedType.SOFT, DeletedRole.POST_CREATOR, user, "Remove User Status")
//            userStatus1.isSoftDeleted must beTrue
//        }
//
//    }
//
//}