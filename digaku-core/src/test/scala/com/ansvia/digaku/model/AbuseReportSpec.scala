// @TODO(robin): benerin ini

///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import org.specs2.specification.{Step, Fragments}
//import com.ansvia.digaku.database.GraphCompat._
//import java.util.{Date, Calendar}
//
///**
// * Author: nadir
// * Date: 9/25/13
// * Time: 12:13 PM
// *
// */
//class AbuseReportSpec extends Specification with DigakuTest {
//    def is: Fragments = {
//        sequential ^
//            "Question should" ^
//            p ^
//            Step(cleanUp()) ^
//            "save to database" ! trees.saveToDatabase ^
//            "saved data correcly for content" ! trees.savedMessageCorrectly ^
//            "get all abuse report" ! trees.getAbuseReports ^
//            "get user reporter by abuse report" ! trees.getReporterByAbuseReport ^
//            "get content/report object" ! trees.getReportObject ^
//            "delete abuse report by Id" ! trees.deleteAbuseReportById ^
//            "delete abuse report by model" ! trees.deleteAbuseReportByModel ^
//            Step(tearDown()) ^
//            end
//    }
//
//    object trees {
//
//        import com.ansvia.graph.BlueprintsWrapper._
//
//        val report = AbuseReport("ini report")
//        var id = 0L
//
//        val user1 = genUser
//        val user2 = genUser
//        val user3 = genUser
//
//        val channel1 = genChannelWithOwner(user1)
//        val channel2 = genChannelWithOwner(user2)
//
//
//        channel1.setAllPermission(SubForumPermission.ALL)
//        channel2.setAllPermission(SubForumPermission.ALL)
//
//
//        val simplePost11 = Post.createSimple(user1, "user1 post post1", user1)
//        val article1 = Article.create(user2, "ini artikel dari user2 ke channel1", "content dari artikel", "tags1, tags2", channel1)
//
//        val resp1 = simplePost11.addResponse(user2, "user2 response simple post")
//        val resp2 = article1.addResponse(user1, "user1 response article1")
//
//        val reportSimplePost = simplePost11.addReport(user2, "content mengandung sara")
//        val reportArticle1 = article1.addReport(user3, "artikel mengandung konten berbau porno")
//        val report2Article1 = article1.addReport(user2, "artikel mengandung konten berbau porno")
//        val reportResp = resp1.addReport(resp1.addReport(user1, "response tidak sopan"))
//
//
//        def saveToDatabase = {
//            val rp = report.save().toCC[AbuseReport]
//            id = rp.get.getId
//            rp.isDefined
//        }
//
//        def savedMessageCorrectly = {
//            val rep = AbuseReport.getById(id)
//            rep.get.message must beEqualTo("ini report")
//        }
//
//        def getAbuseReports = {
//            AbuseReport.getListRight(None, None, 10).toList must be contain(reportSimplePost, reportArticle1,
//                report2Article1, reportResp)
//        }
//
//        def getReporterByAbuseReport = {
//            (report2Article1.reporter must beEqualTo(user2)) and
//                (reportSimplePost.reporter must beEqualTo(user2)) and
//                    (reportArticle1.reporter must beEqualTo(user3)) and
//                        (reportResp.reporter must beEqualTo(user1))
//        }
//
//        def getReportObject = {
//            (report2Article1.getReportedObject.get.asInstanceOf[Article] must beEqualTo(article1)) and
//                (reportSimplePost.getReportedObject.get.asInstanceOf[SimplePost] must beEqualTo(simplePost11)) and
//                    (reportResp.getReportedObject.get.asInstanceOf[Response] must beEqualTo(resp1))
//        }
//
//        def deleteAbuseReportById = {
//            AbuseReport.deleteById(reportSimplePost.getId)
//            simplePost11.getReports(0, 10) must beEmpty
//        }
//
//        def deleteAbuseReportByModel = {
//            AbuseReport.delete(reportResp)
//            resp1.getReports(0, 10) must beEmpty
//        }
//
//    }
//
//
//}
