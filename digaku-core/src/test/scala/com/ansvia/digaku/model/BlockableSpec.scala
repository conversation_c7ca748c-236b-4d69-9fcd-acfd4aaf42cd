// @TODO(robin): benerin ini setelah major refactor
///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
///**
// * Author: robin
// *
// */
//
// import com.ansvia.digaku.DigakuTest
// import org.specs2.Specification
//
// class BlockableSpec extends Specification with DigakuTest {
//
//     def is = "Blockable model should" ^
//         sequential ^
//         "can block" ! trees.block ^
//         "can unblock" ! trees.unblock  ^
//     end
//
//     object trees {
//
//         val u1 = genUser
//         val u2 = genUser
//         val ch1 = genChannelWithOwner(u1)
//
//         // untuk sample menggunakan Post, karena Post implement Blockable.
//         val p1 = Post.createSimple(u1, genRandomString, ch1)
//
//         def block = {
//             p1.setBlocked(block = true, u2).save()
//             db.commit()
//
//             // reload
//
//             val p2 = p1.reload()
//
//             (p2.blocked must beTrue) and
//                 (p2.blockUnblockByUserName must_== u2.name) and
//                 (p2.blockUnblockByUserId must_== u2.getId) and
//                 (p2.blockUnblockTime must beGreaterThan(0L))
//
//
//         }
//
//         def unblock = {
//             p1.setBlocked(block = false, u2).save()
//             db.commit()
//
//             val p2 = p1.reload()
//
//             (p2.blocked must beFalse) and
//                 (p2.blockUnblockByUserName must_== u2.getName) and
//                 (p2.blockUnblockByUserId must_== u2.getId) and
//                 (p2.blockUnblockTime must beGreaterThan(0L))
//         }
//
//
//     }
//
// }
//
