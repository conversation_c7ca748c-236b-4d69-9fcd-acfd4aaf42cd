///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import org.specs2.specification.Step
//
///**
// * Author: fajrhf
// * Date: 28/05/14
// * Time: 14:29
// *
// */
//class RetalkedSpec extends Specification with DigakuTest {
//
//    def is = {
//        sequential ^
//        "RetalkWrapper model should" ^
//        p ^
//            Step(cleanUp()) ^
//            "save data to database" ! trees.saveToDataBase ^
//            "retalk wrapper harus bisa dapatkan object yang di retalk" ! trees.getRetalkedObj ^
//            "ketika post yang diretalk dihapus, maka retalk wrapper juga ikut terhapus" ! trees.deletedRetalkWrapper ^
//            Step(tearDown()) ^
//        end
//
//    }
//
//    object trees {
//
//        val u1 = genUser
//        val u2 = genUser
//        val ch1 = genChannelWithOwner(u1)
//        val ch2 = genChannelWithOwner(u1)
//
//        val p1 = Article.create(u1, "title", "conten", "tags", ch1)
//        val p2 = Article.create(u1, "title2", "conten", "tags", ch1)
//        val p3 = Article.create(u1, "title3", "conten", "tags", ch1)
//
//        def saveToDataBase = {
//            val a = p1.retalk(ch2, u1)
//
//            a.isInstanceOf[RetalkWrapper] must beTrue
//        }
//
//        def getRetalkedObj = {
//            val b = p2.retalk(ch2, u1)
//
//            b.retalkedObject mustEqual p2
//        }
//
//        def deletedRetalkWrapper = {
//            val c = p3.retalk(ch2, u1)
//
//            p3.setDeleted(DeletedType.SOFT, DeletedRole.POST_CREATOR, u1, "")
//
//            RetalkedContent.getById(c.getId) mustEqual None
//
//        }
//    }
//
//}
