/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
// * Copyright (c) 2013. Ansvia Inc.
// * Author: robin
// * Created: 3/24/13 2:04 AM
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//
///**
// * Author: robin
// * Date: 3/24/13
// * Time: 2:04 AM
// *
// */
//class ChannelHandOverSpec extends Specification {
//
//    def is = "Group hand over should" ^ end
//
//    // @TODO(*): code here
//
//    /**
//     * Buat agar group bisa dipindahkan kepemilikannya.
//     *
//     *  h2. Spesifikasi
//     *
//     * * UI Action hand over dilakukan dari group admin / settings / system / change ownership (unittest di digaku-web).
//     * * Hanya bisa dilakukan oleh current owner (tidak ada staff dengan akses ini) (unittest di digaku-web).
//     * * Notifikasi user calon owner dengan menggunakan notifikasi `AcceptRejectAttention`.
//     * * Proses perpindahan dari mulai action -> accept/reject akan dinotifikasikan ke semua member-nya via general notif dan email notif.
//     * * Mantan owner akan kehilangan seluruh hak dan akses-nya begitu calon owner lain meng-accept attention notif-nya,
//     *      kecuali owner kasih check `will be staff after` (dilakukan sebelum action), atau next owner men-set sebagai staff atau di pindah tangankan lagi setelahnya.
//     * * Current owner bisa meng-abort proses pemindahan tangan selama calon owner belum meng-accept-nya.
//     * * Owner tidak bisa melakukan pemindahan tangan lagi selama rentang waktu 1 minggu dari terakhir perpindahan tangan terjadi.
//     */
//
//
//}
