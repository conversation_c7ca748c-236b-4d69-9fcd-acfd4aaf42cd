///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
///**
// * Author: robin
// * Date: 12/17/13
// * Time: 1:39 PM
// *
// */
//
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.exc.LimitationReachedException
//
//
//class PictureGroupSpec extends Specification with DigakuTest {
//     def is = "PictureGroup should" ^
//         sequential ^
//         "create" ! trees.create ^
//         "add pics" ! trees.addPics ^
//         "get all pics from `pictures`" ! trees.getPictures ^
//         "add pics max 5" ! trees.addPicsMax5 ^
//     end
//
//     object trees {
//
//         val u1 = genUser
//         val ch1 = genChannelWithOwner(u1)
//
//         var pg: PictureGroup = _
//
//         val pic1 = Picture.create(u1, "pic1", ch1)
//         val pic2 = Picture.create(u1, "pic2", ch1)
//         val pic3 = Picture.create(u1, "pic3", ch1)
//         val pic4 = Picture.create(u1, "pic4", ch1)
//         val pic5 = Picture.create(u1, "pic5", ch1)
//         val pic6 = Picture.create(u1, "pic6", ch1)
//
//
//         def create = {
//             pg = PictureGroup.create("ini picture group", u1, ch1)
//             true must beTrue
//         }
//
//         def addPics = {
//             pg.addPictures(pic1, pic2)
//             pg.getPictures.toList must contain(pic1, pic2).only
//         }
//
//         def getPictures = {
//             val _pg = pg.reload()
//             _pg.pictures must contain(pic1, pic2)
//         }
//
//
//         def addPicsMax5 = {
//             val _pg = pg.reload()
//             _pg.addPictures(pic3, pic4, pic5, pic6) must throwAn[LimitationReachedException]
//         }
//
//
//     }
//
// }
//
