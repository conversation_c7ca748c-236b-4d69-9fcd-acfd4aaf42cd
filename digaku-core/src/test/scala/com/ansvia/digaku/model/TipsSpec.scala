///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//
///**
// * Author: robin
// * Date: 8/6/13
// * Time: 4:36 PM
// *
// */
//class TipsSpec extends Specification with DigakuTest {
//
//    import com.ansvia.digaku.model.Tips.category._
//    def is = "Tips model harusnya" ^
//        sequential ^
//        p ^
//        "bisa add tips kind: text" ! trees.createText ^
//        "bisa add tips kind: link" ! trees.createLink ^
//        "get list by category " ! trees.createLink ^
//        end
//
//    object trees {
//        def createText = {
//            Tips.createText("tips text", "ini content-nya", TIPS) must beAnInstanceOf[Tips]
//        }
//        def createLink = {
//            Tips.createLink("tips link", "http://www.tips.com/link/nya.html", TIPS) must beAnInstanceOf[Tips]
//        }
//        def getListByCategory = {
//            Tips.createLink("tips link", "http://www.tips.com/link/nya.html", JOB)
//            Tips.createText("tips link", "http://www.tips.com/link/nya.html", JOB)
//            Tips.createText("tips link", "http://www.tips.com/link/nya.html", JOB)
//
//            Tips.getByCategory(JOB, None, None, 10).length mustEqual(3)
//        }
//    }
//
//}
