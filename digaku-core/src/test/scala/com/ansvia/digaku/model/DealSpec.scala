///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import org.specs2.specification.Step
//import com.ansvia.digaku.DigakuTest
//
///**
// * Author: surya
// * Date: 1/22/13
// * Time: 1:37 PM
// *
// */
//class DealSpec extends Specification with DigakuTest {
//    def is = {
//        sequential ^
//        "Deal Model Should be" ^
//        p ^
//            Step(cleanUp()) ^
//            "save to database" ! tress.saveDatabase ^
//            "saved data read moid correctly" ! tress.saveMoid ^
//            "saved data read containsGreeting correctly" !tress.saveContainGreeting ^
//            "saved data read containsLink correctly" ! tress.saveContaintLink ^
//            "saved data read containsPic correctly" ! tress.saveContainPic ^
//            "saved data read containsVideoLink correctly" ! tress.saveContainVideoLink ^
//            "saved data read name deal correctly" ! tress.saveNameDeal ^
//            "saved data read price correctly" ! tress.savePrice ^
//            "saved data read deal desv correctly" ! tress.saveDesc ^
//            "saved data read currecny correctly" ! tress.saveCurrency ^
//            "saved data read locale correctly" ! tress.saveLocale ^
//            "saved data read condition correctly" ! tress.saveCondition ^
//            "saved data read location correctly" ! tress.saveLocation ^
//            "saved data read kind correctly" ! tress.saveKind ^
//            "saved data read photos correctly" ! tress.savePhotos ^
//            Step(tearDown()) ^
//        end
//    }
//
//    lazy val tress = new {
//        import com.ansvia.graph.BlueprintsWrapper._
//
//        val deal = Deal("test", 2000.34, "deal desc", Currency.IDR, "locale", "condition", "location")
//        deal.moid = "moid"
//        deal.containsLink = true
//        deal.containsGreeting = true
//        deal.containsPic = true
//        deal.containsVideoLink = false
//
//        var id = 0L
//
//        def saveDatabase = {
//            val deals = deal.save().toCC[Deal]
//            id = deals.get.getId
//            deals.isDefined
//        }
//
//        def saveMoid = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.moid must beEqualTo("moid")
//        }
//
//        def saveContainGreeting = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.containsGreeting must beTrue
//        }
//
//        def saveContaintLink = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.containsLink must beTrue
//        }
//
//        def saveContainPic = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.containsPic must beTrue
//        }
//
//        def saveContainVideoLink = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.containsVideoLink must beFalse
//        }
//
//        def saveNameDeal = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.name must beEqualTo("test")
//        }
//
//        def savePrice = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.price must beEqualTo(2000.34)
//        }
//
//        def saveDesc = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.desc must beEqualTo("deal desc")
//        }
//
//        def saveCurrency = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.currency must beEqualTo(Currency.IDR)
//        }
//
//        def saveLocale = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.locale must beEqualTo("locale")
//        }
//
//        def saveCondition = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.condition must beEqualTo("condition")
//        }
//
//        def saveLocation = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.location must beEqualTo("location")
//        }
//
//        def saveKind = {
//            val deals = Post.getPostById(id).get.asInstanceOf[Deal]
//            deals.kind must beEqualTo(PostKind.DEAL)
//        }
//
//        def savePhotos = {
//            var deal = Post.getPostById(id).get.asInstanceOf[Deal]
//            val photos = Array("http://www.com/img1.jpg",
//                "http://www.com/img2.jpg",
//                "http://www.com/img3.jpg")
//
//            deal.photos = photos
//            deal.save()
//            db.commit()
//
//            deal = Post.getPostById(id).get.asInstanceOf[Deal]
//            deal.photos must beEqualTo(photos)
//        }
//    }
//}
