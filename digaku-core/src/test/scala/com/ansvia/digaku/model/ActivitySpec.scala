/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
// * Copyright (c) 2013. Ansvia Inc.
// * Author: robin
// * Created: 2/21/13 4:28 PM
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import java.util.{Date, Calendar}
//
///**
// * Author: robin
// * Date: 2/21/13
// * Time: 4:28 PM
// *
// */
//class ActivitySpec extends Specification with DigakuTest {
//
//    def is =
//        sequential ^
//        "Activity String should" ^
////        Step(cleanUp()) ^
//        p ^
//            "Generate join edge" ! trees.generateJoinEdge ^
//            "Generate wrote edge" ! trees.generateWroteEdge ^
//            "Generate create edge" ! trees.generateCreateEdge ^
//            "Generate response edge" ! trees.generateResponseEdge ^
//            "Generate support edge" ! trees.generateSupportEdge ^
////            Step(tearDown()) ^
//        end
//
//
//    object trees {
//
//        import com.ansvia.graph.BlueprintsWrapper._
//        import Label._
//
//        private def dueDate = {
//            val cal = Calendar.getInstance()
//            cal.setTime(new Date())
//            cal.add(Calendar.DATE, 1)
//            cal.getTime
//        }
//
////        val ch1name = ChannelNameGenerator.nextName
//
//        val u1 = genUser
//        val u2 = genUser
//        val ch1 = genChannelWithOwner(u1)
//        ch1.setAllPermission(ChannelPermissionGroup.ALL)
//        val post1 = Article.create(u1, "title",  "hello :)", "", ch1)
//
//
//        def generateJoinEdge = {
//
//            val u1x = User.getByName(u1.name).get
//
//            ch1.addMembers(u1x)
//
//            val edge = u1x.getVertex.pipe.outE(JOIN).next()
//
//            ActivityString(edge).compile.get must beEqualTo("%s join %s".format(u1.name, ch1.name))
//        }
//        def generateWroteEdge = {
//            val u1x = User.getByName(u1.name).get
//            val edge = u1x.getVertex.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT).outE(PUBLISH_CONTENT).next()
//            ActivityString(edge).compile.get must beEqualTo("%s wrote post %s".format(u1.name, post1.title))
//        }
//        def generateCreateEdge = {
//            val u1x = User.getByName(u1.name).get
//            val event = Event.create(u1,"title event", "content", "loc", ch1, dueDate)
//            val edge = u1x.getVertex.pipe.outE(CREATE).next()
//            ActivityString(edge).compile.get must beEqualTo("%s create event %s".format(u1.name, event.title))
//        }
//        def generateResponseEdge = {
//            val u2x = User.getByName(u2.name).get
//            val res1 = post1.addResponse(u2x, "hello juga")
//            val edge = u2x.getVertex.pipe.outE(RESPONSE_WITH).next()
//            ActivityString(edge).compile.get must beEqualTo("%s response to post %s".format(u2.name, post1.title))
//        }
//        def generateSupportEdge = {
//            val u1x = User.getByName(u1.name).get
//            val u2x = User.getByName(u2.name).get
//            u1x.support(u2x)
//            val edge = u1x.getVertex.pipe.outE(SUPPORT).next()
//            ActivityString(edge).compile.get must beEqualTo("%s support %s".format(u1.name, u2.name))
//        }
//
//    }
//
//}
