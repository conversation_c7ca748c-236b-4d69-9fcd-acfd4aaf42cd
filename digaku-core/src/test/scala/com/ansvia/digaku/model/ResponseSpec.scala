/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import org.specs2.specification.Step

/**
 * Author: nadir, robin
 *
 */
class ResponseSpec extends Specification with DigakuTest {
    def is= {
        sequential ^
        "Response should" ^
         p ^
            "save to database" ! trees.saveToDatabase ^
            "saved data correcly for content" ! trees.savedContentCorrectly ^
         end

    }

    object trees {

        import com.ansvia.graph.BlueprintsWrapper._

        val response = Response("hallo")
        var id = 0L

        def saveToDatabase = {
            val res = response.save().toCC[Response]
            id = res.get.getId
            res.isDefined
        }

        def savedContentCorrectly = {
            val res = Response.getById(id)
            res.get.content must beEqualTo("hallo")
        }
    }
}
