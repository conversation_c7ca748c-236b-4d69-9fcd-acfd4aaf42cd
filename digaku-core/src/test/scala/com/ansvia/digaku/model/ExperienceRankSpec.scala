/*
* Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
*
* This file is part of Digaku project.
*
* Unauthorized copying of this file, via any medium is strictly prohibited
* Proprietary and confidential.
*/

package com.ansvia.digaku.model

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import org.specs2.specification.{Step, Fragments}

/**
* Author: nadir (<EMAIL>)
*
*/
class ExperienceRankSpec extends Specification with DigakuTest {
    override def is: Fragments = {
        sequential ^
            "ExperienceRank should" ^
            p ^
            "create 10 rank" ! trees.createRank ^
            "get rank berdasarkan experience valunya" ! trees.getRank ^
            "kalkulasi rank berdasarkan jumlah experience" ! trees.calcRank ^
            "update rank experiance value berdasarkan rank experience tertentu" ! trees.updateRankExperience ^
            "update rank name berdasarkan rank experience nya" ! trees.updateRankName ^
            "remove rank" ! trees.removeRank ^
            end
    }

    object trees {

        def createRank = {

            ExperienceRank.removeAllRank()

            ExperienceRank.createRank(0, "Rank 1")
            ExperienceRank.createRank(100, "Rank 2")
            ExperienceRank.createRank(200, "Rank 3")
            ExperienceRank.createRank(500, "Rank 4")
            ExperienceRank.createRank(750, "Rank 5")
            ExperienceRank.createRank(1000, "Rank 6")
            ExperienceRank.createRank(4000, "Rank 7")
            ExperienceRank.createRank(10000, "Rank 8")
            ExperienceRank.createRank(25000, "Rank 9")
            ExperienceRank.createRank(50000, "Rank 10")

            ExperienceRank.getRanks.size must beEqualTo(10)
        }

        def getRank = {
            ExperienceRank.getRank(100) must beEqualTo(Some(Rank(100, "Rank 2"))) and (
                ExperienceRank.getRank(600) must beNone)
        }

        def calcRank = {
            ExperienceRank.calcRank(99) must beEqualTo(Some(Rank(0, "Rank 1"))) and (
                ExperienceRank.calcRank(100) must beEqualTo(Some(Rank(100, "Rank 2")))) and (
                ExperienceRank.calcRank(150) must beEqualTo(Some(Rank(100, "Rank 2")))) and (
                ExperienceRank.calcRank(400) must beEqualTo(Some(Rank(200, "Rank 3")))) and (
                ExperienceRank.calcRank(4000) must beEqualTo(Some(Rank(4000, "Rank 7"))))and (
                ExperienceRank.calcRank(70000) must beEqualTo(Some(Rank(50000, "Rank 10"))))
        }

        def updateRankExperience = {
            ExperienceRank.updateRankExp(0, 50)

            ExperienceRank.getRank(50).isEmpty must beFalse and (
                ExperienceRank.getRank(0).isEmpty must beTrue)
        }

        def updateRankName = {
            ExperienceRank.updateRankName(50, "Rank 11")
            ExperienceRank.getRank(50).get.name must beEqualTo("Rank 11")
        }

        def removeRank = {
            ExperienceRank.removeRank(50)
            ExperienceRank.getRank(50).isEmpty must beTrue
        }

    }

}
