package com.ansvia.digaku.model

import java.util.concurrent.{Executors, TimeUnit}

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import org.specs2.specification.Fragments
import com.ansvia.graph.BlueprintsWrapper._

import scala.concurrent.duration.Duration
import scala.concurrent._
import com.ansvia.digaku.database.GraphCompat._

/**
  * Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
  */

object LeaderboardSpec extends Specification with DigakuTest {
    override def is:Fragments = "Leaderboard" ^
        sequential ^
            p ^
                "Leaderboard harusnya" ^
                p ^
                    "Menambah Point" ! trees.addPoint ^
                    "Get Users harus sesuai urutan point tertinggi" ! trees.getList ^
                    "Mendapatkan rank dari user" ! trees.getRank ^
                    "Point user bisa minus" ! trees.minusUser ^
                    "User dengan point < 1 tidak muncul di leaderboard" ! trees.notContainsMinusUser ^
                    "Increment point secara asynchronous" ! trees.syncEx ^
                    "User yang di exclude harusnya tidak muncul di list" ! trees.excludedUser ^
        end

    object trees {

        implicit val ec:ExecutionContext = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(5))
        val ec1:ExecutionContext = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(5))
        val ec2:ExecutionContext = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(5))
        val ec3:ExecutionContext = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(5))

        // for support/unsupport testing
        private def createUser(name:String):User = {
            val u = User.create(name, name + "@mail.com", SexType.MALE, "01/05/1986", "123456")
            u.setActivated(true)
            u.reload()
            u
        }

        val u1 = createUser("userlb1")
        val u2 = createUser("userlb2")
        val u3 = createUser("userlb3")
        val u4 = createUser("userlb4")
        val u5 = createUser("userlb5")
        val u6 = createUser("userlb6")
        val u7 = createUser("userlb7")

        def addPoint = {
            UserLeaderboard.incrementPoint(u6, 5)
            UserLeaderboard.incrementPoint(u6, 3)

            Thread.sleep(1000)

            UserLeaderboard.incrementPoint(u2, 3)
            UserLeaderboard.incrementPoint(u2, 5)

            Thread.sleep(1000)

            UserLeaderboard.incrementPoint(u3, 5)
            UserLeaderboard.incrementPoint(u3, 3)

            UserLeaderboard.incrementPoint(u4, 20)
            UserLeaderboard.incrementPoint(u4, 30)

            UserLeaderboard.incrementPoint(u5, 3)
            UserLeaderboard.incrementPoint(u5, 11)

            UserLeaderboard.incrementPoint(u1, 10)
            UserLeaderboard.incrementPoint(u1, 20)
            UserLeaderboard.incrementPoint(u1, 3)
            UserLeaderboard.incrementPoint(u1, 4)

            UserLeaderboard.getUserPoint(u1) must beEqualTo(37) and (
                UserLeaderboard.getUserPoint(u2) must beEqualTo(8)
                ) and (
                UserLeaderboard.getUserPoint(u3) must beEqualTo(8)
                ) and (
                UserLeaderboard.getUserPoint(u4) must beEqualTo(50)
                ) and (
                UserLeaderboard.getUserPoint(u5) must beEqualTo(14)
                )
        }

        def getList = tx { t =>
            val list = UserLeaderboard.getList(0, 100, _db = t)

            list.map(_._1) must be contain(u4, u1, u5, u6, u2, u3) inOrder
        }

        def getRank = tx { t =>
            val list = UserLeaderboard.getList(0, 100, _db = t)

            (UserLeaderboard.getRank(u4, t) must_== Some(1)) and (
                UserLeaderboard.getRank(u1, t) must_== Some(2)
                ) and (
                UserLeaderboard.getRank(u5, t) must_== Some(3)
                ) and (
                UserLeaderboard.getRank(u6, t) must_== Some(4)
                ) and (
                UserLeaderboard.getRank(u2, t) must_== Some(5)
                ) and (
                UserLeaderboard.getRank(u3, t) must_== Some(6)
                ) and (
                UserLeaderboard.getRank(u7, t) must_== None
                )
        }

        def minusUser = {

            UserLeaderboard.incrementPoint(u7, 5)
            UserLeaderboard.incrementPoint(u7, -10)

            UserLeaderboard.getUserPoint(u7) must beEqualTo(-5)
        }

        def notContainsMinusUser = tx { t =>
            val list = UserLeaderboard.getList(0, 100, _db = t)

            println(list)

            list.map(_._1) must be contain(u4, u1, u5, u6, u2, u3) only
        }

        def syncEx = {
            val u8 = createUser("userlb8")
            val u9 = createUser("userlb9")
            val u10 = createUser("userlb10")
            val u11 = createUser("userlb11")

            val a = (0 to 100).toList.map(x => 5)

            val b = for (cat <- a) yield future {
                UserLeaderboard.incrementPoint(u8, cat)
                1
            }(ec)

            val c = for (cat <- a) yield future {
                UserLeaderboard.incrementPoint(u9, cat)
                1
            }(ec1)

            val d = for (cat <- a) yield future {
                UserLeaderboard.incrementPoint(u10, cat)
                1
            }(ec2)

            val e = for (cat <- a) yield future {
                UserLeaderboard.incrementPoint(u11, cat)
                1
            }(ec3)

            val aggregated: Future[Seq[Int]] = Future.sequence(b)
            val aggregated2: Future[Seq[Int]] = Future.sequence(c)
            val aggregated3: Future[Seq[Int]] = Future.sequence(d)
            val aggregated4: Future[Seq[Int]] = Future.sequence(e)

            val f = Await.result(aggregated, Duration(10, TimeUnit.HOURS))
            val g = Await.result(aggregated2, Duration(10, TimeUnit.HOURS))
            val h = Await.result(aggregated3, Duration(10, TimeUnit.HOURS))
            val i = Await.result(aggregated4, Duration(10, TimeUnit.HOURS))

            tx { t =>
                UserLeaderboard.getUserPoint(u8) must beEqualTo(a.sum) and (
                    UserLeaderboard.getUserPoint(u9) must beEqualTo(a.sum)
                ) and (
                    UserLeaderboard.getUserPoint(u10) must beEqualTo(a.sum)
                ) and (
                    UserLeaderboard.getUserPoint(u11) must beEqualTo(a.sum)
                )
            }
        }

        def excludedUser = {

            val u12 = createUser("userlb12")

            UserLeaderboard.incrementPoint(u12, 100)

            UserLeaderboard.excludeUser(u12, true)

            UserLeaderboard.getList(0, 100).map(_._1) must not contain(u12)

        }
    }
}
