///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException}
//import org.specs2.specification.Fragments
//import com.ansvia.digaku.validator.PostMarkValidator
//
///**
// * Author: alamybs
// *
// */
//class PhoneNumberSpec extends Specification {
//    def is: Fragments = "Phone number scpec should" ^
//        p ^
//        "validasi  listCountryCallingCodes harus true semua" ! trees.listCountryCorrectly ^
//        "validasi dengan 896546898 harus throw exceptions" ! trees.validateCorrectly1 ^
//        "validasi dengan 0857466545 harus throw exceptions" ! trees.validateCorrectly3 ^
//        "validasi dengan -6568987554 harus throw exceptions" ! trees.validateCorrectly4 ^
//        "validasi dengan +62 harus throw exceptions" ! trees.validateCorrectly5 ^
//        "validasi dengan +62889898989898989898989 harus throw exceptions" ! trees.validateCorrectly6 ^
//        "ketika memasukan phone number yang sesuai harus dapat di ambil calling code-nya" ! trees.getPhoneAreaNumberCorrectly ^
//        "memasukan huruf harus throw exceptions" ! trees.validateCorrectly7
//        "get Country Colling Codes By CountryCode ID harus menghasilkan code negara, nama negara, calling code nya" ! trees.getCountryCollingCodesByCountryCodeCorrectly1 ^
//        end
//
//
//    object trees {
//
//        def listCountryCorrectly = {
//            var rv = true
//            for (listCountryCorrectly <- PhoneNumber.listCountryCallingCodes.map(_._3)) {
//                rv = rv && PhoneNumber.validPhoneNumber(listCountryCorrectly+"85743434343")
//            }
//
//            rv must beTrue
//
//        }
//
//        def validateCorrectly1 = {
//            PhoneNumber.validate("896546898") must throwAn[DigakuException]
//        }
//
//        def validateCorrectly3 = {
//            PhoneNumber.validate("0857466545") must throwAn[DigakuException]
//        }
//
//        def validateCorrectly4 = {
//            PhoneNumber.validate("-6568987554") must throwAn[DigakuException]
//        }
//
//        def validateCorrectly5 = {
//            PhoneNumber.validate("+62") must throwAn[DigakuException]
//        }
//
//        def validateCorrectly6 = {
//            PhoneNumber.validate("+62889898989898989898989") must throwAn[DigakuException]
//        }
//
//        def validateCorrectly7 = {
//            PhoneNumber.validate("dsfjhjdskfhkjds") must throwAn[DigakuException]
//        }
//
//        def getPhoneAreaNumberCorrectly = {
//            val listPhoneNumber = PhoneNumber.listCountryCallingCodes.map(_._3).toList
//            var rv = List.empty[String]
//
//            for (listCountryCorrectly <- listPhoneNumber) {
//                rv :+= "+" + PhoneNumber.getPhoneAreaNumber(listCountryCorrectly+"85743434343")
//            }
//
//            rv must  be equalTo(listPhoneNumber)
//        }
//
//        def getCountryCollingCodesByCountryCodeCorrectly1 = {
//            PhoneNumber.getCountryCollingCodesByCountryCode("ID") must beEqualTo(Some("ID", "Indonesia", "+62"))
//        }
//
//    }
//}
