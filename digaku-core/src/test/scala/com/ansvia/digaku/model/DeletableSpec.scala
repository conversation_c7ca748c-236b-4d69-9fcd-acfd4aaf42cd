/*
* Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
*
* This file is part of Digaku project.
*
* Unauthorized copying of this file, via any medium is strictly prohibited
* Proprietary and confidential.
*/

package com.ansvia.digaku.model

/**
* Author: robin
*
*/

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import com.ansvia.digaku.exc.InvalidParameterException
import org.specs2.specification.Step

class DeletableSpec extends Specification with DigakuTest {

    import com.ansvia.graph.BlueprintsWrapper._

    def is = "Deletable harusnya bisa" ^
        sequential ^
        Step {
            // clean up
            Deletable.rootVertex.pipe.out(Deletable.rootVertexLabel).remove()
            db.commit()
        } ^
        "koleksi deleted object ketika di-delete" ! trees.collectWhenDelete ^
        "mendapatkan daftar deleted objects" ! trees.getDeletedObjects ^
        "mendapatkan daftar streamable PostStreamObject (SimplePost, Article, Picture, PictureGroup)" ! trees.getDeletedStreamable ^
        "mengembalikan object yang telah di-delete" ! trees.restoreDeletedObjects ^
        "gak bisa restore object yang emang gak ada dikoleksi" ! trees.cantRestoreIfNotExists ^
        end

    object trees {

        val u1 = genUser
        val u2 = genUser
        val ch1 = genForumWithOwner(u2)
        val ch2 = genForumWithOwner(u1)
        val ch3 = genForumWithOwner(u1)
        ch2.addMembers(u2)
        ch2.addStaff(u2, "", Array("delete"))
        val article = Article.create(u2, "article title", "this Article content", "", ch1)
        val article2 = Article.create(u2, "article title dua", "this Article content dua", "", ch1)

        def collectWhenDelete = {
            article.setDeleted(DeletedRole.POST_CREATOR, u2, "nothing", true)
            ch2.setDeleted(DeletedRole.ADMIN_CHANNEL, u1, "nothing", true)

            (Deletable.isCollected(article) must beTrue) and
                (Deletable.isCollected(article2) must beFalse) and
                (Deletable.isCollected(ch2) must beTrue) and
                (Deletable.isCollected(ch3) must beFalse)
        }

        def getDeletedObjects = {
            Deletable.getDeletedObjects().toList must be contain(article, ch2) only
        }

        def getDeletedStreamable = {
            val streamPost = Deletable.getDeletedStream(ch1).map(_.content)
            val rv = streamPost.toList

            rv must be contain(article) inOrder
        }

        def restoreDeletedObjects = {
            val articleBefore = (Deletable.isCollected(article) must beTrue)
            val chBefore = (Deletable.isCollected(ch2) must beTrue)

            Deletable.restore(article)
            Deletable.restore(ch2)

            articleBefore and (Deletable.isCollected(article) must beFalse)
                chBefore and (Deletable.isCollected(ch2) must beFalse)
        }

        def cantRestoreIfNotExists = {
            Deletable.restore(article2) must throwAn[InvalidParameterException]
        }


    }

}

