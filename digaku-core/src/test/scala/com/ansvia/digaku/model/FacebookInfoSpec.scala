///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import org.specs2.specification.Fragments
//
///**
// * Author: nadir
// * Date: 11/26/13
// * Time: 3:47 PM
// *
// */
//class FacebookInfoSpec extends Specification with DigakuTest  {
//    def is: Fragments = {
//        "Facebook connect id should" ^
//            p ^
//            sequential^
//            "saved data  facebook connect id correctly" ! trees.saveToDatabase ^
//            "add friends correctly" ! trees.addFriends ^
//        end
//    }
//
//    object trees {
//
//        import com.ansvia.graph.BlueprintsWrapper._
//
//        val facebookInfo = FacebookInfo(genRandomNumber().toString)
//
//        def saveToDatabase = {
//            val fci = facebookInfo.save().toCC[FacebookInfo]
//            fci.isDefined
//        }
//
//        def addFriends = {
//            val fci1 = FacebookInfo.create(genRandomNumber().toString)
//            val fci2 = FacebookInfo.create(genRandomNumber().toString)
//            val fci3 = FacebookInfo.create(genRandomNumber().toString)
//
//            fci1.addFriends(fci2, fci3)
//
//            (fci1.isFriend(fci2) must beTrue) and (fci1.isFriend(fci3) must beTrue)
//
//        }
//
//    }
//}
