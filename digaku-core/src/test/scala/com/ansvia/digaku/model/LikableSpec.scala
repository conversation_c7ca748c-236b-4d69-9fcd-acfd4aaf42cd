/*
* Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
*
* This file is part of Digaku project.
*
* Unauthorized copying of this file, via any medium is strictly prohibited
* Proprietary and confidential.
*/

package com.ansvia.digaku.model

import com.ansvia.digaku.exc.DigakuException
import org.specs2.Specification
import com.ansvia.digaku.DigakuTest
import com.ansvia.graph.BlueprintsWrapper._

/**
* Author: nadir
*
*/
class LikableSpec extends Specification with DigakuTest {
    def is = {
        "Post dao simple creation with likable should" ^
        p ^
            sequential^
            "add user for like post" ! postLikable.addLikePost ^
            "get like rate user" ! postLikable.getLikeRateUser ^
            "get experiece count setelah postnya di like" ! postLikable.creatorExperience ^
            "like dengan rate 0 harusnya di unlike dan experience creator berkurang" ! postLikable.likeWithZeroRate ^
            "experience creator tidak berubah ketika di like oleh creatornya sendiri" ! postLikable.creatorLikeWithRate ^
            "get all likers post" ! postLikable.getLikersPost ^
            "remove likers post by user id" ! postLikable.removeLikePost ^
            "get count of likers post" ! postLikable.getLikersCountPost ^
            "get likers using sinceTime and maxTime" ! postLikable.getLikersSinceId ^
        p ^
        "response dao with likable should" ^
        p ^
            sequential^
            "add user for like response" ! responseLikable.addLikeResponse ^
            "get for likers response" ! responseLikable.getLikersResponse ^
            "remove likers response by user id" ! responseLikable.removeLikeresponse ^
            "get count of likers response" ! responseLikable.getLikesCountResopnse ^
        end
    }

    object postLikable {
        val u5 = genUser
        val u6 = genUser
        val u7 = genUser
        val ch5 = genForumWithOwner(u7).addMembers(u5, u6)
        val p5 = Article.create(u5, "article title 5", "this Article content 5", "", ch5)
        val res = Response.create(u5, "hello juga")
        val resId = res.getId
        val u6Id = u6.getId

        def addLikePost = {
            p5.addLikeWithRate(u6, 5)
            (p5.getLikesCount must beEqualTo(1)) and (p5.addLike(u7) must throwA[DigakuException])
        }

        def getLikeRateUser = {
            val prevLike = p5.getLikeRate(u6) must beEqualTo(5)
            p5.addLikeWithRate(u6, 4)
            prevLike and(p5.getLikeRate(u6) must beEqualTo(4))
        }

        def creatorExperience = {
            val p6 = Article.create(u6, "article title 6", "this Article content 6", "", ch5)

            // like rate 10
            p6.addLikeWithRate(u5, 5)
            p6.addLikeWithRate(u7, 5)

            val prevExp = u6.getExperience must beEqualTo(10 * 5)

            // ubah like rate jadi 7
            p6.addLikeWithRate(u7, 4)
            p6.addLikeWithRate(u5, 3)

            val afterExp = u6.getExperience must beEqualTo(7 * 5)

            prevExp and afterExp
        }

        def getLikersPost = {
            p5.getLikers(0, 10).toArray.apply(0) must beEqualTo(u6)
        }

        def removeLikePost = {
            p5.removeLike(u6Id)
            p5.getLikesCount == 0 must beTrue
        }

        def getLikersCountPost = {
            p5.addLikeWithRate(u5, 5)
            p5.getLikesCount must beEqualTo(1)
        }

        def likeWithZeroRate = {
            val u11 = genUser
            ch5.addMembers(u11)
            val p7 = Article.create(u11, "article title 7", "this Article content 7", "", ch5)

            p7.addLikeWithRate(u5, 5)
            val prevExperience = u11.getExperience must beEqualTo(5 * 5)

            p7.addLikeWithRate(u5, 0)
            val afterExperience = u11.getExperience must beEqualTo(0)

            val unlike = p7.isLiker(u5) must beFalse

            prevExperience and afterExperience and unlike
        }

        def creatorLikeWithRate = {
            val user = genUser
            ch5.addMembers(user)
            val post = Article.create(user, "article title 7", "this Article content 7", "", ch5)

            post.addLikeWithRate(u5, 2)
            post.addLikeWithRate(user, 5)
            val experience = user.getExperience must beEqualTo(10)

            post.removeLike(user)
            val experience2 = user.getExperience must beEqualTo(10)

            experience and experience2
        }

        def getLikersSinceId = {
            val p = Article.create(u5, "article title 8", "this Article content 8", "", ch5)
            val u8 = genUser
            val u9 = genUser
            val u10 = genUser
            val ed1 = p.addLikeWithRate(u8, 5)
            val ed2 = p.addLikeWithRate(u9, 5)
            val ed3 = p.addLikeWithRate(u10, 5)

            val likers = p.getLikers(0, 10, sinceTime=Some(p.getLikeEdge(u9).map(_.getOrElse("creationTime",0L)).getOrElse(0L))).toList
            val likers2 = p.getLikers(0, 10, maxTime=Some(p.getLikeEdge(u9).map(_.getOrElse("creationTime",0L)).getOrElse(0L))).toList

            (likers must contain(u10).only) and
                (likers2 must contain(u8).only)
        }

    }

    object responseLikable {
        val u7 = genUser
        val userRespCreator = genUser
        val userLiker = genUser
        val ch7 = genForumWithOwner(u7)
        val p7 = Article.create(u7, "article title 9", "this Article content 9", "", ch7)
        val res1 = Response.create(userRespCreator, "waaww")
        val userLikerId = userLiker.getId

        def addLikeResponse = {
            val initExp = userRespCreator.getExperience
            p7.addResponse(res1)
            res1.addLike(userLiker)
            val deltaExp = userRespCreator.getExperience - initExp
            (res1.getLikesCount must beEqualTo(1)) and (deltaExp must beEqualTo(1))
        }
        def getLikersResponse = {
            res1.getLikers(0, 10).toArray.head must beEqualTo(userLiker)
        }
        def removeLikeresponse = {
            val initExp = userRespCreator.getExperience
            res1.removeLike(userLikerId)
            val deltaExp = userRespCreator  .getExperience - initExp
            (res1.getLikesCount==0 must beTrue) and (deltaExp must beEqualTo(-1))
        }
        def getLikesCountResopnse = {
            res1.addLike(u7)
            res1.getLikesCount>0 must beTrue
        }

    }


}
