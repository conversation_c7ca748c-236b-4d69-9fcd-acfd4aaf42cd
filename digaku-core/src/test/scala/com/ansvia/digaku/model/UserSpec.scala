/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.exc.PermissionDeniedException
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.graph.IdGraphTitanDbWrapper._
import org.specs2.Specification
import org.specs2.specification.Step


/**
 * Author: nadir
 *
 */
class UserSpec extends Specification with DigakuTest {
    def is = {
        sequential ^
            "user model should" ^
            p ^
            "saved to database" ! trees.savedToDatabase ^
            "saved data read role correctly" ! trees.savedDataCorrectly ^
//            "saved data read abilities correctly" ! trees.savedDataCorrectly2 ^
            "saved data read photoMedium correctly" ! trees.savedDataCorrectly3 ^
            "saved data read kind correctly" ! trees.savedDataCorrectly4 ^
            "saved data read timezone correctly" ! trees.savedDataCorrectly5 ^
            "saved data read verified correctly" ! trees.savedDataCorrectly6 ^
            "saved data read title correctly" ! trees.savedDataCorrectly7 ^
            "saved data read meStatus correctly" ! trees.savedDataCorrectly8 ^
            "saved data read hasToured correctly" ! trees.savedDataCorrectly9 ^
            "saved data read ability correctly from getAbilities" ! trees.getAbilityUser ^
            "saved data set ability correctly" ! trees.correctlySetAbility ^
            "saved data add ability correctly" ! trees.correctlyAddAbilityUser ^
            "saved data remove ability correctly" ! trees.correctlyRemoveAbility ^
            "saved data get group by user correctly" ! trees.correctlyGetChannel ^
            "saved data get active user correctly" ! trees.getActiveUserCorrectly ^
            "saved data set active user correctly" ! trees.correctlysetActivateUser ^
//            "support approval" ! trees.testSupportApproval ^
//            "support approval Reject" ! trees.testRejectSupportApproval ^
//            "support approval Accept" ! trees.testAcceptSupportApproval ^
            "support/unsupport" ! trees.testSupportUnsupport ^
            "support affect supporters/ing(count)" ! trees.testSupportAffectCount ^
            "unsupport affect supporters/ing(count)" ! trees.testUnsupportAffectCount ^
            "getting recommendation" ! trees.testRecommendation ^
            "getting supporters" ! trees.testGetSupporters ^
            "getting supporting" ! trees.testGetSupporting ^
            "getting mutual supporters" ! trees.testMutalSupporters ^
            "getting mutual supportings" ! trees.testMutalSupportings ^
            "getting mutual supporters count" ! trees.testMutalSupportersCount ^
            "getting mutual supportings count" ! trees.testMutalSupportingsCount ^
            "getting mutual supports" ! trees.testMutualSupport ^
            "getting group recommendation" ! trees.testChannelRecomendation ^
            "get notification list" ! trees.notificationList ^
//            "get unread notifications" ! trees.getUnreadNotif ^
//            "get stream basic" ! trees.getPostStream ^
            ("get stream tidak termasuk post dari group privat " +
                "apabila user bukan member dari group di mana post itu berada") ! trees.getPostStreamCorrectly ^
            "get stream dengan menggunakan sinceId dan maxId" ! trees.getPostWithSinceAndMax ^
            "get owned group count" ! trees.testGetOwnedChannelCount ^
            "get joined group count" ! trees.testGetJoinedChannelCount ^
            "set role user" ! trees.testSetRole ^
//            "mark all dialogue notif as read" ! trees.markAllDialogueNotifAsRead ^
            "ignored user don't show in recommendation" ! trees.ignoredRecomm ^
            "connecting apps user and check it" ! trees.connectApps ^
            "get list of connected apps" ! trees.listConnectApps ^
//            "bookmark group" ! trees.bookmarkChannel ^
//            "bookmark channels" ! trees.bookmarkChannels ^
//            "get bookmarked channels" ! trees.getBookmarkedChannels ^
//            "check is bookmarked group" ! trees.checkBookmarkedChannel ^
//            "unbookmark group" ! trees.unbookmarkChannel ^
//            "create status" ! trees.createStatus ^
//            "get list status" ! trees.getStatusLists ^
//            "get current status user" ! trees.getCurrentStatus ^
//            "getting status user count" ! trees.getStatusCount ^
//            "status user count affetced when status deleted" ! trees.getStatusCountAffectedAfterDelete ^
            "create profile picture" ! trees.createProfilePicture ^
            "get historical photo profile" ! trees.changeProfilePicture ^
            "getting channels by ability" ! trees.testChannelByAbility ^
            "getting channels by ability count" ! trees.testChannelByAbilityCount ^
            "get joined channels tidak termasuk privat group by default" ! trees.testGetJoinedChannelsNoPrivate ^
            "get joined owned channels tidak termasuk privat group by default" ! trees.testGetJoinedOwnedChannelsNoPrivate ^
            "make sure get channels without duplicate" ! trees.testGetChannels ^
            "getting blocked users" ! trees.testGetBlockedUsers ^
            "getting blocked users with query" ! trees.testGetBlockedUsersByQuery ^
            "set activated" ! trees.testSetActivated ^
//            "set email activated" ! trees.testSetEmailActivated ^
            end
    }

    object trees {

        val user1Name = genUserName
        val user2Name = genUserName
        val user3Name = genUserName
        val user4Name = genUserName
        val user5Name = genUserName
        val user6Name = genUserName
        val user7Name = genUserName
        val user8Name = genUserName


        val user1 = User(user1Name, "user full name", user1Name + "@yahoo.com", SexType.MALE, "Location", "desc", "13/12/1999")
        user1.activated = true
        user1.role = UserRole.USER
//        user1.abilities = Array("user", "test")
        user1.timezone = 7
        user1.verified = true
        user1.title = "test"
        user1.meStatus = "status test"
        user1.hasToured = true
        user1.hasGetStarted = true
//        user1.channelQuota = 1
        user1.photoSmall = "small"
        user1.photoLarge = "large"
        user1.photoMedium = "medium"
        user1.level = 1
        user1.point = 1
        user1.bannerPicUrl = "banner"
        user1.theme = ""
        user1.registerProvider = ""
        user1.registerId = ""
        user1.passHash = ""
        user1.authMechanism = ""
        user1.passV = 1
        user1.locale = "en_US"
        user1.activated = true
        user1.supportersCount = 0
        user1.supportingCount = 0
        user1.kind = OriginKind.USER

        // for support/unsupport testing
        private def createUser(name:String):User = {
            val u = User.create(name, name + "@mail.com", SexType.MALE, "01/05/1986", "123456")
            u.setActivated(true)
            u.reload()
            u
        }

        var user2:User = null
        var user3:User = null
        var user4:User = null
        var user5:User = null
        var user6:User = null
        var user7:User = null
        var user8:User = null

        var channel1:Forum = null
        var channel2:Forum = null
        var channel3:Forum = null
        var channel4:Forum = null
        var channel5:Forum = null
        var channel6:Forum = null


        user1.saveWithLabel(VertexLabels.USER)
        db.commit()

        user2 = createUser(user2Name)
//        user2.channelQuota = 20
        user3 = createUser(user3Name)
        user4 = createUser(user4Name)
        user5 = createUser(user5Name)
        user6 = createUser(user6Name)
        user7 = createUser(user7Name)
        user8 = createUser(user8Name)

        //create group
        channel1 = genForumWithOwner(user2)
        channel2 = genForumWithOwner(user2)
        channel3 = genForum
        channel4 = genForum
        channel5 = genForum
        channel6 = genForum



        def savedToDatabase = {
            val user = User.getByName(user1Name).get
            user.reload().getId must not equalTo(0)
        }

        def savedDataCorrectly = {
            val user = User.getByName(user1Name).get
            user.role must beEqualTo(UserRole.USER)
        }

//        def savedDataCorrectly2 = {
//            val user = User.getByName(user1Name).get
//            user.abilities must beEqualTo(Array("user","test"))
//        }

        def savedDataCorrectly3 = {
            val user = User.getByName(user1Name).get
            user.photoMedium must beEqualTo("medium")
        }

        def savedDataCorrectly4 = {
            val user = User.getByName(user1Name).get
            user.kind must beEqualTo(OriginKind.USER)
        }
        def savedDataCorrectly5 = {
            val user = User.getByName(user1Name).get
            user.timezone must beEqualTo(7)
        }
        def savedDataCorrectly6 = {
            val user = User.getByName(user1Name).get
            user.verified must beTrue
        }
        def savedDataCorrectly7 = {
            val user = User.getByName(user1Name).get
            user.title must beEqualTo("test")
        }
        def savedDataCorrectly8 = {
            val user = User.getByName(user1Name).get
            user.meStatus must beEqualTo("status test")
        }
        def savedDataCorrectly9 = {
            val user = User.getByName(user1Name).get
            user.hasToured must beTrue
        }
        def getAbilityUser = {
            skipped
//            val user = User.getByName(user1Name).get
//            user.getAbilities must beEqualTo(Array("user", "test"))
        }
        def correctlySetAbility = {
            skipped
//            val user = User.getByName(user1Name).get
//            user.setAbilities(Array("abilities", "set"))
//            user.getAbilities must beEqualTo(Array("abilities", "set"))
        }
        def correctlyAddAbilityUser = {
            skipped
//            val user = User.getByName(user1Name).get
//            user.addAbility("add1")
//            user.abilities must beEqualTo(Array("add1","user", "test"))
        }
        def correctlyRemoveAbility = {
            skipped
//            val user = User.getByName((user1Name)).get
//            user.removeAbility("test")
//            user.abilities must beEqualTo(Array("user"))
        }
        def correctlyGetChannel = {
//            val user = User.getByName(user1Name).get
            val user = genUser
            val user2 = genUser
            val ch = genForumWithOwner(user)
            genForumWithOwner(user)
            ch.addMembers(user2)
            ch.addStaff(user2, "project manager", Array.empty[String])
            (user.getChannels(SubForumStateFlags.STAFF).length>0 must beTrue) and
                (user.getChannels(SubForumStateFlags.JOINED).length == 2 must beTrue)
        }
        def getActiveUserCorrectly = {
            val user = User.getByName(user1Name).get
            user.isActivated must beEqualTo(true)
        }
        def correctlysetActivateUser = {
            val user = User.getByName(user1Name).get
            user.setActivated(state = false)
            user.isActivated must beEqualTo(false)
        }
        def testSupportUnsupport = {
            val user1 = User.getByName(user1Name).get
//            user1.isSupport(user2) must beTrue
//            user2.isSupport(user3) must beTrue
            user1.unsupport(user2)
//            user1.isSupport(user2) must beFalse
            user1.support(user2)
//            user1.isSupport(user2) must beTrue
            user1.support(user7)
            user2.support(user3)
            user4.support(user3)
            user7.support(user3)
            user4.support(user5)
            user2.support(user6)
            user7.support(user6)
            user2.support(user4)
            user1.support(user4)
            (user1.isSupport(user4) must beTrue) and
                (user2.isSupport(user3) must beTrue) and
                (user2.isSupport(user4) must beTrue) and
                (user1.support(user1) must throwAn[PermissionDeniedException])
        }

//        /**
//         * test
//         * setting user 4 support nya harus melalui approval
//         * user4 -> support -> user3
//         */
//        def testSupportApproval = {
//            val privacys:Array[String] = Array(PrivacyRole.SUPPORT_APPROVAL)
//            user3.setPrivacys(privacys)
//            user3.save()
//            user4.support(user3) must throwAn[PermissionDeniedException] and
//                (user4.isSupport(user3) must beFalse) and
//                (user4.isInvited(user3, InvitationKind.BECOME_SUPPORTED) must beTrue)
//        }
//
//        def testRejectSupportApproval = {
//            val att = user3.getAttentionNotifications(0,1).toSeq.head.asInstanceOf[AcceptRejectAttention]
//
//            att.reject()
//
//            (user4.isSupport(user3) must beFalse) and
//                (user4.isInvited(user3, InvitationKind.BECOME_SUPPORTED) must beFalse)
//        }
//
//        def testAcceptSupportApproval = {
//            user4 support user3 must throwAn[AlreadyExistsException]
//
//            val att = user3.getAttentionNotifications(0,1).toSeq.head.asInstanceOf[AcceptRejectAttention]
//
//            att.accept()
//
//            val privacys:Array[String] = Array.empty
//            user3.setPrivacys(privacys)
//            user3.save()
//
//            (user4.isSupport(user3) must beTrue) and
//                (user4.isInvited(user3, InvitationKind.BECOME_SUPPORTED) must beFalse)
//        }



        def testSupportAffectCount = {
            val userx1 = genUser
            val userx2 = genUser

            userx1.support(userx2)

            val userx1r = User.getById(userx1.getId)
            val userx2r = User.getById(userx2.getId)

            (userx1r.get.supportingCount must beEqualTo(1)) and
            (userx2r.get.supportersCount must beEqualTo(1))
        }

        def testUnsupportAffectCount = {
            val userx1 = genUser
            val userx2 = genUser
            val userx3 = genUser


            userx1.reload()
            userx3.reload()
            userx1.support(userx3)
            userx3.reload()
            userx2.support(userx3)
            println(userx3.supportersCount)
            val supportersCountBefore = userx3.getVertex.getOrElse("supportersCount", 0)
            userx2.reload()
            userx3.reload()
            userx2.unsupport(userx3)
            val supportersCountAfter = userx3.getVertex.getOrElse("supportersCount", 0)

            val userx1r = User.getById(userx1.getId)
            val userx3r = User.getById(userx3.getId)

            (supportersCountBefore must beEqualTo(2)) and
            (supportersCountAfter must beEqualTo(1)) and
            (userx1r.get.supportingCount must beEqualTo(1)) and
            (userx3r.get.supportersCount must beEqualTo(1))
        }

        /**
         * Keterangan:
         *
         * user1 -> support -> user2
         * user1 -> support -> user4
         * user1 -> support -> user7
         *
         * untuk user3 memiliki 3 supporter dari supporter yang disupport user1
         * user2 -> support -> user3
         * user7 -> support -> user3
         * user4 -> support -> user3
         *
         * untuk user6 memiliki 2 supporter dari supporter yang disupport user1
         * user7 -> support -> user6
         * user2 -> support -> user6
         *
         * untuk user5 memiliki 1 supporter dari supporter yang disupport user1
         * user4 -> support -> user5
         *
         * user2 -> support -> user4
         *
         * get user1 recommendation harusnya dapat user3, user6, user5 dan disesuaikan dengan
         * urutan dari banyaknya supporter yang disupport oleh user1
         * tidak termasuk user4, karena user4 sudah di-support user1.
         */
        def testRecommendation = {
            val user1 = User.getByName(user1Name).get
            val recms = user1.getRecommendation(0, 10).toList
            recms.length must beGreaterThan(0)
            val recmNames = recms.map(_.name).toList
//            println("recmNames: " + recmNames)
            recmNames must be not contain(user2Name) and not contain(user4Name) and contain(user3Name, user6Name, user5Name).inOrder
        }

        def testGetSupporters = {
            user3.getSupporters(0, 10).toList must be contain(user2, user7, user4)
        }

        def testGetSupporting = {
            val user1 = User.getByName(user1Name).get
            user1.getSupporting(0, 10).toList must be contain(user2, user4, user7)
        }

        /**
         * Get mutual supporter antara user6 dan user3 harus dapet user2 dan user7 karena
         * user2 dan user7 support user6 dan user3
         *
         * user2 --> support --> user6
         * user2 --> support --> user3
         * user7 --> support --> user6
         * user7 --> support --> user3
         *
         * @return
         */
        def testMutalSupporters = {
            val user6 = User.getByName(user6Name).get
            val x = user6.getMutualSupportersTo(user3, 0, 10).toList
            x must contain(user2, user7).only
        }

        /**
         * Get mutual supporting antara user2 dan user7 harus dapet user6 dan user3 karena
         * user3 dan user6 di support user 2 dan user 7
         *
         * user6 <-- support <-- user2
         * user3 <-- support <-- user2
         * user6 <-- support <-- user7
         * user3 <-- support <-- user7
         *
         * @return
         */
        def testMutalSupportings = {
            val user2 = User.getByName(user2Name).get
            val x = user2.getMutualSupportingsTo(user7, 0, 10).toList
            x must contain(user3, user6).only
        }

        /**
         * Get jumlah mutual supporter antara user3 dan user6 harus dapet 2 (user2 dan user7)
         * @return
         */
        def testMutalSupportersCount = {
//            val user6 = User.getByName(user6Name).get
//            val x = user6.getMutualSupportersCount(user3)
//            x must beEqualTo(2)
            skipped // diskip dulu karena ini harus menjalankan pymk script dulu
        }

        /**
         * Get jumlah mutual supporter antara user2 dan user7 harus dapet 2 (user3 dan user6)
         * @return
         */
        def testMutalSupportingsCount = {
//            val user2 = User.getByName(user2Name).get
//            val x = user2.getMutualSupportingsCount(user7)
//            x must beEqualTo(2)
            skipped // diskip dulu karena ini harus menjalankan pymk script dulu
        }

        def testMutualSupport = {

            val user2 = User.getByName(user2Name).get
            user3.support(user2)
            user6.support(user2)
            val x = user2.getMutualSupport(0, 10).toList
            println(x)
            x must contain(user3, user6).only
        }

        /**
         * keterangan :
         * user1 suport : user7, user2, user4
         * channel1 memiliki 3 member : user7, user2, user4
         * channel2 memiliki 2 member : user2, user4
         * channel3 memiliki 1 member : user4
         * channel4 memiliki 2 member : user1, user7
         *
         * get group recommendation untuk user1 harusnya dapat : channel3, channel2, channel1
         * group sesuai dengan urutan dari banyaknya member yang disupport oleh user1
         * channel4 harus tidak termasuk dalam group recommendation karena user1 telah menjadi member
         * dalam group tersebut
         * @return
         */
        def testChannelRecomendation = {

            skipped

            // sementara di-skip dulu untuk keperluan snapshot
//            val user1 = User.getByName(user1Name).get
//            channel1.addMembers(user7,user2, user4)
//            channel2.addMembers(user2,user4)
//            channel3.addMembers(user4)
//            channel4.addMembers(user1,user7)
//
//            val recmch = user1.getChannelRecommendation(0, 10).toList
////            recmch.length must beGreaterThan(0)
//            val recmNames = recmch.map(_.name).toList
////            recmNames.apply(0) must beEqualTo(ch1Name)
////            recmNames.apply(1) must beEqualTo(ch2Name)
////            recmNames.apply(2) must beEqualTo(ch3Name)
////
//            recmch.length must beGreaterThan(0) and(recmNames must be contain(channel1.name,channel2.name,channel3.name) inOrder)
////            recmNames must be not contain(ch4Name)
        }


        /**
         * keterangan :
         * user2 adalah owner dari channel1, channel2, channel3, channel4
         * (channel1, channel2) memiliki staff (ability "retalk-post") : user1
         * (channel3, channel4) memiliki staff (ability "edit-post")   : user1
         *
         * user1 mempunyai ability retalk-post di                      : channel1, channel2
         *
         * get group by ability "retalk-post" yang diberikan ke user1 yang merupakan staff
         * di channel1, channel2, channel3, channel4 (valid            : channel1, channel2)
         */
        def testChannelByAbility = {
            val user1 = User.getByName(user1Name).get

            channel1.addMembers(user1)
            channel2.addMembers(user1)
            channel3.addMembers(user1)
            channel4.addMembers(user1)
            channel5.addMembers(user1)

            channel1.addStaff(user1, "staff", Array("retalk-post"))
            channel2.addStaff(user1, "staff", Array("retalk-post"))
            channel3.addStaff(user1, "staff", Array("edit-post"))
            channel4.addStaff(user1, "staff", Array("edit-post"))
            channel5.addStaff(user1, "staff", Array("edit-post"))

            val ownedChannels = user1.searchChannel(com.ansvia.digaku.model.SubForumStateFlags.OWNER, 0, 10, "").toList
            val staffWithAbilityretalkChannels = List(channel1, channel2)

            // yang di dapatkan adalah group dimana user1 staff dengan ability retalk-post dan group own user1
            val withAbilityretalkChannels:List[Forum] = (ownedChannels ++ staffWithAbilityretalkChannels).sortBy(_.getId)

            val channels:List[Forum] = user1.getChannelsByAbility("retalk-post", 0, 10, "").toList.sortBy(_.getId)

            channels mustEqual withAbilityretalkChannels
        }

        /**
         * Mendapatkan jumlah channels berdasarkan ability edit-post dari user1
         *
         * valid : 3 channels (channel3, channel4, channel5)
         */
        def testChannelByAbilityCount = {
            val channelsCount = user1.getChannelsByAbilityCount("edit-post", "")

            // 3 didapatkan dari com.ansvia.digaku.model.UserSpec.trees.testChannelByAbility, kita telah set ability edit-post user1 ke tiga group
            // 3 ditambahkan dengan group yang dia punyai

            channelsCount must beEqualTo(3+user1.getOwnedChannelCount)
        }

        def notificationList = {
            skipped

//            val user1 = User.getByName(user1Name).get
//
//            val userx = User.create(genUserName, genUserName + "@mail.com", SexType.MALE, "01/01/1986", "123456")
//
//            userx.support(user1)
//
//            Thread.sleep(1000)
//
//            user1.getNotifications(0, 10).length must beGreaterThan(0)
        }




//        def getPostStream = {
//
//            val u1 = User.getByName(user1Name).get
//
//            channel1.addMembers(u1, user7)
//            channel2.addMembers(u1)
//
//            val post1 = Post.createSimple(u1, "simple post1", channel1)
//            val post2 = Article.create(user2, "title article1", "content article1", "hallo, article1", channel1)
//            val post3 = Article.create(user4, "title article2", "content article2", "hallo, article2", channel2)
//            val post4 = Post.createSimple(user7, "simple post2", channel1)
//
//            // harus di-sleep karena StreamBuilder berjalan secara asynchronous
//            Thread.sleep(5000)
//
//            u1.getStream(0,10).toList.map(_.content) must be contain(post1, post2, post3, post4)
//
//        }

        /**
         * keterangan :
         * pada function sebelumnya user1 suppport : user7, user2, user4
         * user7, user2, user4 member pada private group (channel5)
         * user1 bukan member dari channel5
         * user7, user2 dan user4 membuat article di private group (channel5) yaitu post5, post6, post7
         *
         * user1, user7, user2, user4 member pada private group (channel6)
         * user1 merupakan member dari channel6
         * user7, user2 dan user4 membuat article di private group (channel6) yaitu post8, post 9, dan post10
         *
         * getStream untuk user1 harus :
         * tidak mendapatkan post5, post6, post7 karena user1 bukan member dari private group pada channel5
         * mendapatkan post8, post 9, dan post10 karena user1 merupakan member dari private group pada channel6
         *
         */
        def getPostStreamCorrectly = {

            val u1 = User.getByName(user1Name).get
            channel5.setPrivate(true)
            channel5.save()

            channel5.addMembers(user7, user2, user4)

            channel6.setPrivate(true)
            channel6.save()

            channel6.addMembers(u1,user7, user2, user4)

            val post5 = Article.create(user7, "title article3", "content article3", "hallo, article3", channel5)
            val post6 = Article.create(user2, "title article4", "content article4", "hallo, article4", channel5)
            val post7 = Article.create(user4, "title article5", "content article5", "hallo, article5", channel5)
            val post8 = Article.create(user7, "title article3", "content article3", "hallo, article3", channel6)
            val post9 = Article.create(user2, "title article4", "content article4", "hallo, article4", channel6)
            val post10 = Article.create(user4, "title article5", "content article5", "hallo, article5", channel6)

            (u1.getStream(0,30).toList.map(_.content.getId) must not contain(post5.getId, post6.getId, post7.getId)) and
                (u1.getStream(0,30).toList.map(_.content.getId) must be contain(post8.getId, post9.getId, post10.getId))
        }

        def getPostWithSinceAndMax = {
            val u1 = User.getByName(user1Name).get
            val post11 = Article.create(user2, "title article 11", "content article 11", "some, tags", channel6)
            val post12 = Article.create(user2, "title article 12", "content article 12", "some, tags", channel6)
            val post13 = Article.create(user2, "title article 13", "content article 13", "some, tags", channel6)

            //test sinceId
            (u1.getStream(0, 30, Some(post11.getId)).map(_.content.getId) must not contain(post11.getId)) and
                (u1.getStream(0, 30, Some(post11.getId)).map(_.content.getId) must be contain(post12.getId, post13.getId))
            //test maxId
            (u1.getStream(0, 30, None, Some(post12.getId)).map(_.content.getId) must not contain(post13.getId)) and
                (u1.getStream(0, 30, None,  Some(post12.getId)).map(_.content.getId) must be contain(post11.getId, post12.getId))
            //test sinceId and maxId
            (u1.getStream(0, 30, Some(post11.getId), Some(post12.getId)).map(_.content.getId) must not contain(post11.getId)) and
                (u1.getStream(0, 30, Some(post11.getId),  Some(post12.getId)).map(_.content.getId) must be contain(post12.getId, post13.getId))
        }

        def testGetOwnedChannelCount = {
//            channel1.setOwner(user2)
//            channel2.setOwner(user2)

            user2.getOwnedChannelCount must beEqualTo(2L)
        }

        def testGetJoinedChannelCount = {

            val userx = genUser

            val chx1 = genForum
            val chx2 = genForum
            val chx3 = genForum
            val chx4 = genForumWithOwner(userx)

            chx1.addMembers(userx)
            chx2.addMembers(userx)
            chx3.addMembers(userx)

            println("userx.getOwnedChannelCount: " + userx.getOwnedChannelCount)

            (userx.getJoinedChannelCount(includeOwned = true) must beEqualTo(4)) and
                (userx.getJoinedChannelCount(includeOwned = false) must beEqualTo(3))
        }

        def testSetRole = {
            user2.setRole(UserRole.SUPER_ADMIN)
            user3.setRole(UserRole.ADMIN)

            (user2.role must beEqualTo(UserRole.SUPER_ADMIN)) and
                (user3.role must beEqualTo(UserRole.ADMIN))
        }


//        def markAllDialogueNotifAsRead = {
//            val u1 = User.getByName(user1Name).get
//            val u2 = User.getByName(user2Name).get
//
//            val post = Post.createSimple(u1, "hello", u1)
//            post.addResponse(u2, "pem pem")
//
//            post.addLike(u2)
//
//            val before = u1.getDialogue(0, 5, NotificationState.UNREAD).toList.length must beGreaterThan(0)
//            val before2 = u1.getNotifications(0, 5, NotificationState.UNREAD,
//                NotifPartition.ALL_NOTICE_LEVEL).toList.length must beGreaterThan(0)
//
//            // seharusnya ini gak mempengaruhi standard / all notice level.
//            u1.markAllDialogueAsRead()
//
//            val after = u1.getDialogue(0, 5, NotificationState.UNREAD).toList.length must_== 0
//
//            // ini harusnya tetep / gak terpengaruh, karena normal notif
//            val after2 = u1.getNotifications(0, 5, NotificationState.UNREAD,
//                NotifPartition.ALL_NOTICE_LEVEL).length must beGreaterThan(0)
//
//            before and
//                after and
//                before2 and
//                after2
//        }

        def ignoredRecomm = {

//            println("user2: " + user2)
//            println("user3: " + user3)
//            println("user4: " + user4)
//            println("user5: " + user5)

//            user2.unsupport(user4)
//            user2.support(user3)
//            user3.support(user4)
//
//            val recm1 = user2.getRecommendation(0, 10).toList
//
//            user2.ignoreUserRecommendation(user4)
//
//            val recm2 = user2.getRecommendation(0, 10).toList
//
//            (recm1 must contain(user4)) and
//                (recm2 must not contain(user4))

            // @TODO(robin): code here
            skipped // diskip dulu karena ini harus menjalankan pymk script dulu

        }

        def connectApps = {
            val regId = "123123"
            val provider = "android"
            val userConnect = UserConnect(regId, provider)
            user1.connectApp(userConnect)


            val connected = user1.getConnectedApp(provider).get

            (connected.uid must beEqualTo(regId)) and
                (connected.via must beEqualTo(provider))

        }

        def listConnectApps = {
            val regId = "123123"
            val provider = "google"
            val userConnect1 = UserConnect(regId, provider)
            user1.connectApp(userConnect1)


            val connected = user1.getConnectedApp.toList

            (connected must contain(userConnect1))
        }


//        def bookmarkChannel = {
//            (user1.bookmarkChannel(channel1) must be not throwAn[Exception]) and
//                (user2.bookmarkChannel(channel5) must be not throwAn[Exception])
//        }
//
//        def bookmarkChannels = {
//            val chs = Seq(channel2, channel3, channel5)
//
//            val before = (user4.bookmarkChannel(chs) must be not throwAn[Exception])
//            db.commit()
//            val after = (user4.getBookmarkedChannelCount must beEqualTo(3))
//
//            before and
//                after
//
//        }
//
//        def getBookmarkedChannels = {
//            user1.getBookmarkedChannels(0, 10).toList.map(_.group) must contain(channel1)
//        }
//
//        def unbookmarkChannel = {
//            user1.unbookmarkChannel(channel1)
//            user1.getBookmarkedChannels(0, 10).toList.map(_.group) must not contain(channel1)
//        }
//
//        def checkBookmarkedChannel = {
//            user2.isBoorkmarked(channel5) must beTrue
//        }

//        def createStatus = {
//            user1.createStatus("user1 create status").content must beEqualTo("user1 create status")
//        }

//        def getStatusLists = {
//            val status2 = user1.createStatus("user1 create status 2")
//            val status3 = user1.createStatus("user1 create status 3")
//            val status4 = user1.createStatus("user1 create status 4")
//            val status5 = user1.createStatus("user1 create status 5")
//            val status6 = user1.createStatus("user1 create status 6")
//            user1.getStatusLists(0, 10).toSeq must be contain(status6, status5, status4, status3, status2) inOrder
//        }
//
//        def getCurrentStatus = {
//            val curr = user1.createStatus("user1 create status 7")
//
//            user1.getCurrentStatus.get must beEqualTo(curr)
//        }
//
//        def getStatusCount = {
//            user1.getStatusCount.must(beEqualTo(7L))
//                .and(user1.getCounter.get("status").must(beEqualTo(7L))) // via counter provider)
//        }
//
//        def getStatusCountAffectedAfterDelete = {
//
//            user1.getCurrentStatus.map(_.setDeleted(DeletedType.HARD, DeletedRole.ADMIN_SYSTEM, user1))
//
//            user1.getStatusCount.must(beEqualTo(6L))
//                .and(user1.getCounter.get("status").must(beEqualTo(6L))) // via counter provider)
//        }

        def createProfilePicture = {

            val userCreatePic = genUser

            val profPict = userCreatePic.createProfilePicture("http://image.com/img.jpg")

            val currentProfPic = userCreatePic.getActiveProfilePic.get

            profPict must beEqualTo(currentProfPic)
        }

        def changeProfilePicture = {

            val userCreatePic2 = genUser

            val pp1 = userCreatePic2.createProfilePicture("http://image.com/img1.jpg")
            val pp2 = userCreatePic2.createProfilePicture("http://image.com/img2.jpg")
            val pp3 = userCreatePic2.createProfilePicture("http://image.com/img3.jpg")

            userCreatePic2.changeProfilePic(pp2)

            val currentProfPic = userCreatePic2.getActiveHistoryProfilePic.get

            pp2 must beEqualTo(currentProfPic.picture)
        }

        def getHistoricalPhotoProfile = {

            val userGetPP = genUser

            val pp1 = userGetPP.createProfilePicture("http://image.com/img1.jpg")
            val pp2 = userGetPP.createProfilePicture("http://image.com/img2.jpg")
            val currentPP = userGetPP.createProfilePicture("http://image.com/img3.jpg")

            val historicalPP = userGetPP.getHistoricalProfilePic(false)

            historicalPP.toSeq must be contain(pp1, pp2) inOrder
        }

        def testGetJoinedChannelsNoPrivate = {
            //            val user = User.getByName(user1Name).get
            val user = genUser
            val user2 = genUser
            val ch = genForumWithOwner(user)
            val ch2 = genForumWithOwner(user)
            ch.addMembers(user2)
            ch2.addMembers(user2)
            ch2.setPrivate(true)
            (user2.getChannels(SubForumStateFlags.JOINED, withPrivate=true).length must beEqualTo(2)) and
                (user2.getChannels(SubForumStateFlags.JOINED).length must beEqualTo(1))
        }

        def testGetJoinedOwnedChannelsNoPrivate = {
            val user1 = genUser
            val user2 = genUser

            val ch = genForumWithOwner(user1)
            val ch2 = genForumWithOwner(user1)
            val ch3 = genForumWithOwner(user1)

            ch.addMembers(user2)
            ch2.addMembers(user2)
            ch3.addMembers(user2)
            ch3.setPrivate(true)

            (user2.getChannels(SubForumStateFlags.JOINED_AND_OWNED, 0, 3, withPrivate=true).length must beEqualTo(3)) and
                (user2.getChannels(SubForumStateFlags.JOINED_AND_OWNED, 0, 3).length must beEqualTo(2))

        }

        def testGetChannels = {
            val user1 = genUser
            val user2 = genUser
            val user3 = genUser

            val ch1 = genForumWithOwner(user1)
            val ch2 = genForumWithOwner(user2)
            ch2.addMembers(user1)
            ch2.addStaff(user1, "programmer", Array(Ability.ALL))
            val ch3 = genForumWithOwner(user3)
            ch3.addMembers(user1)
            ch3.addStaff(user1, "programmer", Array(Ability.ALL))
            ch3.setPrivate(true)

            val flags = SubForumStateFlags.OWNER | SubForumStateFlags.STAFF | SubForumStateFlags.JOINED

            (user1.getChannels(flags, 0, 10, withPrivate = true).length must beEqualTo(3)) and
                (user1.getChannels(flags, 0, 10, withPrivate = false).length must beEqualTo(2))

        }

        def testGetBlockedUsers = {
            val user1 = genUser
            val user2 = genUser
            val user3 = genUser

            user1.blockUser(user2)
            user1.blockUser(user3)

            user1.getBlockedUsers(0, 10).toList must be contain(user3, user2)

        }

        def testGetBlockedUsersByQuery = {
            val user1 = genUser
            val user2Name = genUserName + ".blocked"
            val user3Name = genUserName + ".blocked"
            val user2 = User.create(user2Name, user2Name + "@mail.com", SexType.FEMALE, "01/05/1992", "123")
            val user3 = User.create(user3Name, user3Name + "@mail.com", SexType.MALE, "01/05/1990", "123")
            val user4 = genUser
            val user5 = genUser
            val user6 = genUser

            user1.blockUser(user2)
            user1.blockUser(user3)
            user1.blockUser(user4)
            user1.blockUser(user5)
            user1.blockUser(user6)

            user1.getBlockedUsers("blocked", 0, 10).toList must contain(user3, user2).only

        }

        def testSetActivated = {
            user1.activated = false
            val a = user1.isActivated must beFalse
            user1.setActivated(true)
            val b = user1.isActivated must beTrue

            a and b
        }

//        def testSetEmailActivated = {
//
//            val user1 = genUser
//
//            val a = user1.isActivatedEmail must beFalse
//            user1.setEmailActivated(true)
//            val b = user1.reload().isActivatedEmail must beTrue
//
//            a and b
//        }



    }
}

