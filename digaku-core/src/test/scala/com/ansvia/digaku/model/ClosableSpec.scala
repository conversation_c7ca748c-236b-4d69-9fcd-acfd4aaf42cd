// @TODO(robin): benerin ini setelah major refactor
///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//
///**
// * Author: andrie
// * Date: 2/20/14
// * Time: 9:48 AM
// *
// */
//class ClosableSpec extends Specification with DigakuTest {
//    def is = "Closable model should" ^
//        sequential ^
//        "can closed" ! trees.closed ^
//        "can open" ! trees.open ^
//    end
//
//    object trees {
//        val u1 = genUser
//        val u2 = genUser
//        val ch1 = genChannelWithOwner(u1)
//        val reason = genRandomString
//
//        // untuk sample menggunakan Post, <PERSON>rena <PERSON> implement Closable.
//        val p1 = Post.createSimple(u1, genRandomString, ch1)
//
//        def closed = {
//            p1.setClosed(close = true, u2, reason).save()
//            db.commit()
//
//            // reload
//
//            val p2 = p1.reload()
//
//            (p2.isClosed must beTrue) and
//                (p2.closeOpenByUserName must_== u2.name) and
//                (p2.closeOpenByUserId must_== u2.getId) and
//                (p2.reasonClosed must_== reason) and
//                (p2.closeOpenTime must beGreaterThan(0L))
//        }
//
//        def open = {
//            p1.setClosed(close = false, u2, "").save()
//            db.commit()
//
//            val p2 = p1.reload()
//
//            (p2.isClosed must beFalse) and
//                (p2.closeOpenByUserName must_== u2.getName) and
//                (p2.closeOpenByUserId must_== u2.getId) and
//                (p2.reasonClosed must_== "") and
//                (p2.closeOpenTime must beGreaterThan(0L))
//        }
//    }
//}
