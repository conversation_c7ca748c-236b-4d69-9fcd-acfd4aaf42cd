///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
///**
// * Author: temon
// * Date: 2/5/14
// * Time: 12:06 PM
// *
// */
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.exc.AlreadyExistsException
//import com.ansvia.digaku.utils.DateUtils
//
//
//class PromotedSpec extends Specification with DigakuTest {
//    def is = "Promoted should" ^
//        sequential ^
//        p ^
//        "able to create Promoted" ! trees.create ^
//        "able to create Promoted kind Post" ! trees.createFeatured ^
//        "tidak bisa di promote lg jika sudah pernah di promote" ! trees.promoteAgain ^
//        "get creator of promoted" ! trees.getCreator ^
//        "get prmoted by Id promoted obj" ! trees.getByPromotedId ^
//        "get list promoted obj by kind" ! trees.getKindChannel ^
//        "able to edit promoted" ! trees.editPromoted ^
//        "check if content has promoted" ! trees.checkIsPromoted ^
//        "check if content has promoted and set to displayed" ! trees.checkIsPromotedToDisplayed ^
//        "get creator headline and draft of promoted" ! trees.getHeadlineAndDraftCreator ^
//        "get list featured post" ! trees.getListPostPromoted ^
//        "able to delete promoted" ! trees.deletePromoted ^
//        end
//
//    object trees {
//
//        import Promoted.Kind._
//
//        val u1 = genUser
//        val u2 = genUser
//
//        u1.setRole(UserRole.SUPER_ADMIN)
//        u2.setRole(UserRole.SUPER_ADMIN)
//
//        val ch1 = genForum
//        val ch2 = genForum
//        val ch3 = genForum
//
//        ch1.addMembers(u1)
//
//        val p1 = Article.create(u1, "Title Article", "Ini content dari article", "tags article", ch1)
//        val p2 = Picture.create(u1, "picture title " + genRandomString, ch1)
//
//        val logo1 = "http://dmcd6hvaqrxz0.cloudfront.net/img/category_logo/4bf7c67e83e3e1e0c84608088df9947a"
//        val logo2 = "http://dmcd6hvaqrxz0.cloudfront.net/img/logo2/4bf7c67e83e3e1e0c84608088df9947a"
//
//        val promoted1 = Promoted.create(u2, p2, PICTURE, logo2, "headline picture")
//        val promoted2 = Promoted.create(u2, p1, POST, logo1, "headline post")
//
//        def create = {
//            Promoted.create(u1, ch1, GROUP, logo1) must beAnInstanceOf[Promoted]
//        }
//
//        def promoteAgain = {
//            Promoted.create(u1, ch1, GROUP, logo1) must throwAn[AlreadyExistsException]
//        }
//
//        def createFeatured = {
//            (promoted1 must beAnInstanceOf[Promoted]) and
//                (promoted2 must beAnInstanceOf[Promoted])
//        }
//
//        def getCreator = {
//            val promoted = Promoted.create(u2, ch2, GROUP, logo1)
//
//            (promoted.creator mustEqual(u2))
//        }
//
//        def getByPromotedId = {
//            (Promoted.getByPromoteId(ch1.getId, GROUP).isDefined must beTrue) and
//                (Promoted.getByPromoteId(p1.getId, POST).isDefined must beTrue)
//        }
//
//        def getKindChannel = {
//            Promoted.getPromotedObjects[Promotable](0, 10, GROUP).toList must contain(ch1)
//        }
//
//        def editPromoted = {
//            Promoted.update(ch1.getId, ch3, GROUP, logo2)
//
//            Promoted.getByPromoteId(ch3.getId, GROUP).get.logo mustEqual(logo2)
//        }
//
//        def checkIsPromoted = {
//            p1.isPromoted must beTrue
//        }
//
//        def checkIsPromotedToDisplayed = {
//            Promoted.setVisible(db.getVertex(p1.getId), POST, u1, true, Digaku.engine.dateUtils.nowMilis.toDouble)
//
//            (p1.isPromoted must beTrue) and
//                (p1.isDisplayed must beTrue)
//        }
//
//        def getHeadlineAndDraftCreator = {
//            Promoted.setVisible(db.getVertex(p2.getId), PICTURE, u2, true, Digaku.engine.dateUtils.nowMilis.toDouble)
//            Promoted.setVisible(db.getVertex(p2.getId), PICTURE, u1)
//
//            val promoted = Promoted.getByPromoteId(p2.getId, PICTURE).get
//
//            (promoted.creatorHeadline.get mustEqual(u2)) and
//                (promoted.creatorDraft.get mustEqual(u1))
//
//        }
//
//        def getListPostPromoted = {
//            (Promoted.getListPost(0, 10).map(_._1).toList must contain(promoted2, promoted1)) and
//                (Promoted.getListPost(0, 10, true).map(_._1).toList must contain(promoted2))
//        }
//
//        def deletePromoted = {
//            val idPromoted = Promoted.getByPromoteId(ch2.getId, GROUP).get.getId
//            val idPromoted2 = Promoted.getByPromoteId(p1.getId, POST).get.getId
//            val idPromoted3 = Promoted.getByPromoteId(p2.getId, PICTURE).get.getId
//
//            Promoted.deleteById(idPromoted)
//            Promoted.deleteById(idPromoted2)
//            Promoted.deleteById(idPromoted3)
//
//            (Promoted.isExists(ch2.getId, GROUP) must beFalse) and
//                (Promoted.isExists(p1.getId, POST) must beFalse) and
//                (Promoted.isExists(p2.getId, PICTURE) must beFalse)
//        }
//    }
//}
