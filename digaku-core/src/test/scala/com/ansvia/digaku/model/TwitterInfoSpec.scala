///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.specification.Fragments
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//
///**
// * Author: nadir
// * Date: 11/27/13
// * Time: 2:57 PM
// *
// */
//class TwitterInfoSpec extends Specification with DigakuTest {
//    def is: Fragments = {
//        "Twitter info should" ^
//            p ^
//            sequential^
//            "saved data  twitter info correctly" ! trees.saveToDatabase ^
//            "add friends correctly" ! trees.addFriends ^
//            end
//    }
//
//    object trees {
//
//        import com.ansvia.graph.BlueprintsWrapper._
//
//        val twitterInfo = TwitterInfo(genRandomNumber().toString)
//
//        def saveToDatabase = {
//            val fci = twitterInfo.save().toCC[TwitterInfo]
//            fci.isDefined
//        }
//
//        def addFriends = {
//            val fci1 = TwitterInfo.create(genRandomNumber().toString)
//            val fci2 = TwitterInfo.create(genRandomNumber().toString)
//            val fci3 = TwitterInfo.create(genRandomNumber().toString)
//
//            fci1.addFriends(fci2, fci3)
//
//            (fci1.isFriend(fci2) must beTrue) and (fci1.isFriend(fci3) must beTrue)
//
//        }
//
//    }
//}
