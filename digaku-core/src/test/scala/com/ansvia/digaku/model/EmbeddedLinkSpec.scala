///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//
//
///**
// * Author: robin
// *
// */
//class EmbeddedLinkSpec extends Specification with DigakuTest {
//
//    def is = "EmbeddedLink harusnya" ^
//        sequential ^
//        p ^
//        "bisa create" ! trees.create ^
//        "bisa diembed ke post" ! trees.embedToPost ^
//        "bisa diembed ke post (multiple)" ! trees.embedToPostMulti ^
//        end
//
//    object trees {
//        val u = genUser
//        val post = Post.createSimple(u, "hello", u)
//        val post2 = Post.createSimple(u, "hello 2", u)
//
//        var emb:EmbeddedLink = _
//
//        def create = {
//            emb = EmbeddedLink.create(
//                "http://digaku.ansvia.com/forum/mindtalk-dev/post/jadwal-ronda-standby-and-keep-care-mindtalk-225875.html",
//                "some title", LinkKind.TEXT,
//                "http://dmcd6hvaqrxz0.cloudfront.net/2013/06/26/a1fd101a6d30c45c8fb4f6bbde97afbf.jpg",
//                "Jadwal ronda")
//
//            emb must beAnInstanceOf[EmbeddedLink]
//        }
//
//
//        def embedToPost = {
//            post.addEmbeddedObject(emb)
//            post.getEmbeddedObjects.toList must be contain(emb) only
//        }
//
//        def embedToPostMulti = {
//            val emb2 = EmbeddedLink.create(
//                "http://digaku.ansvia.com/forum/mindtalk-dev/post/jadwal-ronda-standby-and-keep-care-mindtalk-225875.html",
//                "some title #2",
//                LinkKind.TEXT,
//                "http://dmcd6hvaqrxz0.cloudfront.net/2013/06/26/a1fd101a6d30c45c8fb4f6bbde97afbf.jpg",
//                "Jadwal ronda")
//            post2.addEmbeddedObjects(emb, emb2)
//            post2.getEmbeddedObjects.toList must be contain(emb, emb2) only
//        }
//
//    }
//}
