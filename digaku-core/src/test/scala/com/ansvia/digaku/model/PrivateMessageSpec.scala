///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.specification.Step
//
///**
// * Author: nadir, robin
// *
// */
//class PrivateMessageSpec extends Specification with DigakuTest {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//
//    def is = {
//        sequential ^
//            "Private message model should" ^
//            p ^
//            Step(cleanUp()) ^
//            "saved to database" ! treesUnit.saveToDataBase ^
//            "saved data read content message" ! treesUnit.savedDataCorrectly1 ^
//            "saved data read creation time" ! treesUnit.savedDataCorrectly2 ^
//            "get user participant" ! treesAccept.getParticipant ^
//            "get all of user participant" ! treesAccept.getAllParticipant ^
//            "add participant private message" ! treesAccept.addParticipant ^
//            "get participant count" ! treesAccept.getParticipantCount ^
//            "add reply private message" ! treesAccept.addReplyMessage ^
//            "get response count" ! treesAccept.getResponseCount ^
//            "get response count by user" ! treesAccept.getResponseCountByUser ^
//            "kicked participant and get kicked participant" ! treesAccept.kickedAndGetKickedParticipant ^
//            "get reply message" ! treesAccept.getReplyMessage ^
//            "def get creator private message" ! treesAccept.getCreatorPrivateMessage ^
//            "kicked user has no stream for that message anymore" ! treesAccept.kickedUserHasNoStream ^
//            "leaved user has no stream for that message anymore" ! treesAccept.leavedUserHasNoStream ^
//            "added participant has stream for that message" ! treesAccept.addedParticipantHasStream ^
//            "user can gen ordered message stream" ! treesAccept.userGetOrderedStream ^
//            Step(tearDown()) ^
//            end
//    }
//
//    object treesUnit {
//
//        val date = System.currentTimeMillis()
//        val privateMessage1 = PrivateMessage(genRandomString, "private message test")
//        privateMessage1.creationTime = date
//
//        def saveToDataBase = {
//            privateMessage1.save().toCC[PrivateMessage].isDefined must beTrue
//        }
//
//        def savedDataCorrectly1 = {
//            privateMessage1.content must beEqualTo("private message test")
//        }
//
//        def savedDataCorrectly2 = {
//            privateMessage1.creationTime must beEqualTo(date)
//        }
//
//    }
//
//    object treesAccept {
//        val user1 = genUser
//        val user2 = genUser
//        val user3 = genUser
//        val user4 = genUser
//        val user5 = genUser
//        val user6 = genUser
//        val user7 = genUser
//
//        user1.support(user2)
//        user2.support(user1)
//        user1.support(user3)
//        user3.support(user1)
//        user1.support(user4)
//        user4.support(user1)
//
//        user7.support(user3)
//        user3.support(user7)
//        user7.support(user2)
//        user2.support(user7)
//        user7.support(user4)
//        user4.support(user7)
//
//        val pM1 = PrivateMessage.create(user1, genRandomString, "send to user2, user3, user4, user5",Array(user2, user3, user4, user5))
//
//        pM1.leaveConversation(user5, "Because of you")
//
//        var pmRes1:MessageResponse = null
//        var pmRes2:MessageResponse = null
//        var pmRes3:MessageResponse = null
//        var pmRes4:MessageResponse = null
//
//        def getParticipant = {
//            pM1.getParticipants must contain(user2, user3, user4)
//        }
//
//        def getAllParticipant = {
//            pM1.getParticipants must contain(user2, user3, user4)
//        }
//
//        def addParticipant = {
//            user1.support(user5)
//            user5.support(user1)
//            pM1.addParticipant(user5)
//
//            pM1.reload().getParticipants must contain(user5)
//        }
//
//        def getParticipantCount = {
//
//            user1.support(user6)
//            user6.support(user1)
//
//            pM1.addParticipant(user6)
//            pM1.getParticipantCount must beEqualTo(5)
//        }
//
//        def addReplyMessage = {
//            pmRes1 = pM1.addResponse(user2, "user2 reply")
//            pmRes2 = pM1.addResponse(user1, "user1 reply")
//            pmRes3 = pM1.addResponse(user3, "user3 reply")
//            pmRes4 = pM1.addResponse(user4, "user4 reply")
//
//            pM1.getResponses(user1,0,10) must contain(pmRes1, pmRes2, pmRes3, pmRes4).inOrder
//        }
//
//        def getResponseCount = {
//            pM1.getResponseCount must_== 4
//        }
//
//        def getResponseCountByUser = {
//            user1.support(user7)
//            user7.support(user1)
//            pM1.addParticipant(user7)
//
//            (pM1.getResponseCountByUser(user6) must beEqualTo(4)) and
//                (pM1.getResponseCountByUser(user7) must beEqualTo(0))
//        }
//
//        def kickedAndGetKickedParticipant = {
//            pM1.kickParticipant(user3)
//            pM1.kickParticipant(user4)
//
//            pM1.getKickedParticipant must contain(user3, user4)
//        }
//
//        def getReplyMessage = {
//            (pM1.getResponses(user1,0,10) must contain(pmRes1, pmRes2)) and
//                (pM1.getResponses(user1,0,10) must not contain(pmRes3, pmRes4))
//        }
//
//        def getCreatorPrivateMessage = {
//            pM1.creator must beEqualTo(user1)
//        }
//
//        def kickedUserHasNoStream = {
//
//            val pm1 = PrivateMessage.create(user1, genRandomString, "to user2 dan user3", Array(user2, user3))
//
//            val hasStreamBefore = user2.getMessageStream(0, 10).toList must be contain(pm1)
//            pm1.kickParticipant(user2)
//
//            val hasNoStreamAfter = user2.getMessageStream(0, 10).toList must not contain(pm1)
//
//            hasStreamBefore and
//                hasNoStreamAfter and
//                (user3.getMessageStream(0, 10).toList must contain(pm1))
//        }
//
//        def leavedUserHasNoStream = {
//
//            val pm1 = PrivateMessage.create(user1, genRandomString, "to user2 dan user3 again", Array(user2, user3))
//
//            val hasStreamBefore = user2.getMessageStream(0, 10).toList must be contain(pm1)
//
//            pm1.leaveConversation(user2, "I don't want talk about it anymore, bye...")
//
//            val hasStreamAfter = user2.getMessageStream(0, 10).toList must be contain(pm1)
//
//            val res1 = pm1.addResponse(user1, "Response after user2 left")
//
//            val hasNoResponseAfter = pm1.getResponses(user2, 0, 10) must be contain(res1)
//
//            hasStreamBefore and
//                hasStreamAfter and
//                hasNoResponseAfter and
//                (user3.getMessageStream(0, 10).toList must contain(pm1))
//        }
//
//
//        def addedParticipantHasStream = {
//            val pm1 = PrivateMessage.create(user1, genRandomString, "to user3 aja", Array(user3))
//
//            val hasNoStreamBefore = user2.getMessageStream(0, 10).toList must be not contain(pm1)
//
//            pm1.addParticipant(user2)
//
//            val hasStreamAfter = user2.getMessageStream(0, 10).toList must contain(pm1)
//
//            hasNoStreamBefore and
//                hasStreamAfter and
//                (user3.getMessageStream(0, 10).toList must contain(pm1))
//        }
//
//        def userGetOrderedStream = {
//            val pm1 = PrivateMessage.create(user7, genRandomString, genRandomString, Array(user3))
//            val pm2 = PrivateMessage.create(user7, genRandomString, genRandomString, Array(user2))
//            val pm3 = PrivateMessage.create(user7, genRandomString, genRandomString, Array(user4))
//
//            println(user7.getMessageStream(0, 10).toList)
//
//            val defaultStream = user7.getMessageStream(0, 10).toList must contain(pm3, pm2, pm1).inOrder
//
//            pm1.addResponse(user3, "reply1")
//            Thread sleep 10000
//            pm2.addResponse(user2, "reply2")
//
//            val newStream = user7.getMessageStream(0, 10).toList must contain(pm2, pm1, pm3).inOrder
//
//            defaultStream and
//                newStream
//        }
//
//
//
//    }
//
//}
