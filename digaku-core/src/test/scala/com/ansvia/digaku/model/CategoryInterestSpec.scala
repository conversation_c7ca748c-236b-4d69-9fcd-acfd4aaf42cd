// @TODO(robin): benerin ini setelah major refactor
///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.exc.InvalidParameterException
//
///**
// * Author: andrie
// *
// */
//class GroupCategorySpec extends Specification with DigakuTest {
//    def is = "Category Interest should" ^
//        sequential ^
//        p ^
//        "create to category" ! trees.create ^
//        "get category by name" ! trees.getByName ^
//        "create category interest name length must > 3 and < 25 character" ! trees.createMinMax ^
//        "add category to group" ! trees.addCategoryToChannel ^
//        "add category to user" ! trees.addCategoryToUser ^
//        "category is exist" ! trees.categoryExist ^
//        "get list category interest" ! trees.getCategoryList ^
//        "get list user dan group yang sama dengan category interest berdasarkan user/group" ! trees.getMutualByCategory ^
//        "get list user berdasarkan category interest" ! trees.getUserByCategory ^
//        "get list group berdasarkan category interest dan group ditampilkan berdasarkan member terbanyak" ! trees.getChannelByCategory ^
//        "get category interest dari user/group" ! trees.getNameCategoryOf ^
//        "set category interest di group" ! trees.setCategoryChannel ^
//        "promote category interest dan get semua list group yang sudah di promoted" ! trees.promotedAndGetCategoryChannel ^
//    end
//
//    object trees {
//
//        val ch1 = genChannel
//        val ch2 = genChannel
//        val ch3 = genChannel
//        val ch4 = genChannel
//        val u1 = genUser
//        val u2 = genUser
//        val u3 = genUser
//        val cat1 = SubForumCategory.create("Tech & Gadget", "http://dmcd6hvaqrxz0.cloudfront.net/img/category_logo/4bf7c67e83e3e1e0c84608088df9947a")
//        val cat2 = SubForumCategory.create("Sports", "http://dmcd6hvaqrxz0.cloudfront.net/img/category_logo/4bf7c67e83e3e1e0c84608088df9947a")
//        val cat3 = SubForumCategory.create("Movies", "http://dmcd6hvaqrxz0.cloudfront.net/img/category_logo/4bf7c67e83e3e1e0c84608088df9947a")
//        val cat4 = SubForumCategory.create("Woman", "http://dmcd6hvaqrxz0.cloudfront.net/img/category_logo/4bf7c67e83e3e1e0c84608088df9947a")
//        ch1.addGroupCategory(cat1)
//        ch2.addGroupCategory(cat2)
//        ch3.addGroupCategory(cat1)
//        ch4.addGroupCategory(cat2)
//        u1.addGroupCategory(cat1, cat2, cat3)
//        u2.addGroupCategory(cat2, cat3)
//        u3.addGroupCategory(cat2, cat3, cat1)
//
//        ch2.addMembers(u3)
//        ch4.addMembers(u1, u2, u3)
//
//        def create = {
//            cat1 must beAnInstanceOf[SubForumCategory]
//        }
//
//        def getByName = {
//            SubForumCategory.getByName("Tech & Gadget").isDefined must beTrue
//        }
//
//        def createMinMax = {
//            (SubForumCategory.create("ai", "") must throwAn[InvalidParameterException]) and
//                (SubForumCategory.create("Category Interest tidak boleh lebih dari dua puluh lima karakter", "") must throwAn[InvalidParameterException])
//        }
//
//        def addCategoryToChannel = {
//            ch1.hasGroupCategory(cat1) must beTrue
//        }
//
//        def addCategoryToUser = {
//            u1.hasGroupCategory(cat1) must beTrue
//            u1.hasGroupCategory(cat2) must beTrue
//        }
//
//        def categoryExist = {
//            SubForumCategory.isExistByName("Sports") must beTrue
//        }
//
//        def getCategoryList = {
//            SubForumCategory.getListRight(None, None, 10).toList must contain(cat3, cat2, cat1).only
//        }
//
//        def getMutualByCategory = {
//            (u1.getMutualGroupCategoryTo[User](0, 10) must contain(u2, u3)) and
//                (u2.getMutualGroupCategoryTo[Forum](0, 10) must contain(ch2)) and
//                    (ch1.getMutualGroupCategoryTo[Forum](0, 10) must contain(ch3)) and
//                        (ch2.getMutualGroupCategoryTo[User](0, 10) must contain(u1, u2, u3))
//        }
//
//        def getUserByCategory = {
//            cat2.getGroupCategory[User](0, 10) must contain(u1, u2, u3)
//        }
//
//        def getChannelByCategory = {
//            cat2.getGroupCategory[Forum](0, 10) must be contain(ch4, ch2) inOrder
//        }
//
//        def getNameCategoryOf = {
//            (ch2.getGroupCategory.get must beEqualTo(cat2)) and
//                (u3.getGroupCategoryLists(0, 10).toSeq must contain(cat2, cat3, cat1))
//        }
//
//        def setCategoryChannel = {
//            ch3.setGroupCategory("Sports")
//
//            (ch3.getGroupCategory.toList must contain(cat2).only) and
//                (ch3.hasGroupCategory(cat1) must beFalse)
//        }
//
//        def promotedAndGetCategoryChannel = {
//            ch3.addGroupCategory(cat4)
//            cat4.promotedToInterest(db.getVertex(ch2.getId), u1)
//            cat4.promotedToInterest(db.getVertex(ch1.getId), u2)
//
//            (ch1.hasGroupCategory(cat4) must beTrue) and
//                (ch2.hasGroupCategory(cat4) must beTrue) and
//                (cat4.getGroupCategory[Forum](0, 10, hasPromoted = true) must be contain(ch1, ch2) inOrder)
//        }
//    }
//}
