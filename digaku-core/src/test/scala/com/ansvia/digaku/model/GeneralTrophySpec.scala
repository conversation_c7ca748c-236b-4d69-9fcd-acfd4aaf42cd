/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import org.specs2.Specification
import com.ansvia.digaku.DigakuTest
import org.specs2.specification.Fragments
import com.ansvia.digaku.model.Label._

/**
 * Author: nadir
 * Date: 12/10/13
 * Time: 5:47 PM
 * 
 */
class GeneralTrophySpec extends Specification with DigakuTest {
    def is: Fragments = {
        "Trophy should" ^
            p ^
            sequential^
            "saved data  trophy correctly" ! trees.saveToDatabase ^
            "add trophy to user correctly" ! trees.addTrophy ^
            "remove trophy from user correctly" ! trees.removeTrophyFromUser ^
            "calculate trophy role untuk jumlah supporter" ! trees.calculateTrophySupporter ^
            "calculate trophy role untuk jumlah group yang dimiliki" ! trees.calculateTrophyOwnedChannel ^
            "calculate trophy role untuk jumlah level yang dimiliki" ! trees.calculateTrophyLevel ^
            "calculate trophy role untuk jumlah article creation" ! trees.calculateTrophyArticleCreation ^
            "calculate trophy role untuk post activity" ! trees.calculateTrophyPostActivity ^
            "calculate trophy role untuk post apreciated" ! trees.calculateTrophyPostAppreciated ^
            end
    }

    object trees {

        import com.ansvia.graph.BlueprintsWrapper._
        import scala.collection.JavaConversions._

        val trophy = GeneralTrophy("trophy1", "desc")
        val user = genUser

        val trop = Trophy.createGeneral("trophy2", "desc", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.ARTICLE)
        trop.giveTo(user)

        val user1 = genUser
        val user2 = genUser
        val user3 = genUser
        val user4 = genUser
        val user5 = genUser

        val userV = User.rootVertex.pipe.out(DAO_LIST)

        transact {
            user1.getVertex.setProperty("supportersCount", 50)
            user2.getVertex.setProperty("supportersCount", 100)
            user3.getVertex.setProperty("supportersCount", 508)
            user4.getVertex.setProperty("supportersCount", 1005)
            user5.getVertex.setProperty("supportersCount", 5000)
        }

        val trophy1 = Trophy.createGeneral("Prince", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.PERSONAL)
        val trophy2 = Trophy.createGeneral("King", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.PERSONAL)
        val trophy3 = Trophy.createGeneral("Emperor", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.PERSONAL)
        val trophy4 = Trophy.createGeneral("Spotlight", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.PERSONAL)
        val trophy5 = Trophy.createGeneral("Dominator", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.PERSONAL)

        def saveToDatabase = {
            val fci = trophy.save().toCC[GeneralTrophy]
            fci.isDefined must beTrue
        }

        def addTrophy = {
            trop.userHasTrophy(user) must beTrue
        }

        def removeTrophyFromUser = {
            trop.removeTrophyFromUser(user)

            trop.userHasTrophy(user) must beFalse

        }

        def calculateTrophySupporter = {
            trophy1.setAutoAssignWithInterval("supportersCount", 50, 100)
            trophy2.setAutoAssignWithInterval("supportersCount", 100, 500)
            trophy3.setAutoAssignWithInterval("supportersCount", 500, 1000)
            trophy4.setAutoAssignWithInterval("supportersCount", 1000, 2000)
            trophy5.setAutoAssignWithInterval("supportersCount", 2000, 0)

            User.rootVertex.pipe.out(User.rootVertexLabel).iterator().foreach { v =>
                val users = v.toCC[User]
                users.foreach { user =>
                    trophy1 giveTo user
                    trophy2 giveTo user
                    trophy3 giveTo user
                    trophy4 giveTo user
                    trophy5 giveTo user
                }
            }

            (trophy1.userHasTrophy(user1) must beTrue) and
                (trophy2.userHasTrophy(user2) must beTrue) and
                (trophy3.userHasTrophy(user3) must beTrue) and
                (trophy4.userHasTrophy(user4) must beTrue) and
                (trophy5.userHasTrophy(user5) must beTrue)

        }

        def calculateTrophyOwnedChannel = {

            val trophy6 = Trophy.createGeneral("Creator", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.GROUP)
            val trophy7 = Trophy.createGeneral("Developer", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.GROUP)
            val trophy8 = Trophy.createGeneral("Expander", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.GROUP)

            transact {
                user1.getVertex.setProperty("channelOwnedCount", 12)
                user2.getVertex.setProperty("channelOwnedCount", 20)
                user3.getVertex.setProperty("channelOwnedCount", 25)
            }

            trophy6.setAutoAssignWithInterval("channelOwnedCount", 1, 15)
            trophy7.setAutoAssignWithInterval("channelOwnedCount", 15, 25)
            trophy8.setAutoAssignWithInterval("channelOwnedCount", 25, 0)

            User.rootVertex.pipe.out(DAO_LIST).iterator().foreach { v =>
                val users = v.toCC[User]
                users.foreach { user =>
                    trophy6.giveTo(user)
                    trophy7.giveTo(user)
                    trophy8.giveTo(user)
                }
            }

            (trophy6.userHasTrophy(user1) must beTrue) and
                (trophy7.userHasTrophy(user2) must beTrue) and
                (trophy8.userHasTrophy(user3) must beTrue)

        }

        def calculateTrophyLevel = {
            val trophy9 = Trophy.createGeneral("Meteora", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.LEVEL)
            val trophy10 = Trophy.createGeneral("Interstella", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.LEVEL)
            val trophy11 = Trophy.createGeneral("Nebula", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.LEVEL)
            val trophy12 = Trophy.createGeneral("Galaxy", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.LEVEL)
            val trophy13 = Trophy.createGeneral("Andromeda", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.LEVEL)

            transact {
                user1.getVertex.setProperty("lavel", 9)
                user2.getVertex.setProperty("lavel", 10)
                user3.getVertex.setProperty("lavel", 26)
                user4.getVertex.setProperty("lavel", 59)
                user5.getVertex.setProperty("lavel", 100)
            }

            trophy9.setAutoAssignWithInterval("lavel", 1, 10)
            trophy10.setAutoAssignWithInterval("lavel", 10, 25)
            trophy11.setAutoAssignWithInterval("lavel", 25, 50)
            trophy12.setAutoAssignWithInterval("lavel", 50, 100)
            trophy13.setAutoAssignWithInterval("lavel", 100, 0)

            User.rootVertex.pipe.out(DAO_LIST).iterator().foreach { v =>
                val users = v.toCC[User]
                users.foreach { user =>
                    trophy9.giveTo(user)
                    trophy10.giveTo(user)
                    trophy11.giveTo(user)
                    trophy12.giveTo(user)
                    trophy13.giveTo(user)
                }
            }

            (trophy9.userHasTrophy(user1) must beTrue) and
                (trophy10.userHasTrophy(user2) must beTrue) and
                (trophy11.userHasTrophy(user3) must beTrue) and
                (trophy12.userHasTrophy(user4) must beTrue) and
                (trophy13.userHasTrophy(user5) must beTrue)

        }

        def calculateTrophyArticleCreation = {
            val trophy14 = Trophy.createGeneral("Writer", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.ARTICLE)
            val trophy15 = Trophy.createGeneral("Wordsmith", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.ARTICLE)
            val trophy16 = Trophy.createGeneral("Writegasm", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.ARTICLE)

            transact {
                user1.getVertex.setProperty("articleCreationCount", 50)
                user2.getVertex.setProperty("articleCreationCount", 103)
                user3.getVertex.setProperty("articleCreationCount", 506)
            }

            trophy14.setAutoAssignWithInterval("articleCreationCount", 50, 100)
            trophy15.setAutoAssignWithInterval("articleCreationCount", 100, 500)
            trophy16.setAutoAssignWithInterval("articleCreationCount", 500, 0)

            User.rootVertex.pipe.out(DAO_LIST).iterator().foreach { v =>
                val users = v.toCC[User]
                users.foreach { user =>
                    trophy14.giveTo(user)
                    trophy15.giveTo(user)
                    trophy16.giveTo(user)
                }
            }

            (trophy14.userHasTrophy(user1) must beTrue) and
                (trophy15.userHasTrophy(user2) must beTrue) and
                (trophy16.userHasTrophy(user3) must beTrue)
        }

        def calculateTrophyPostActivity = {
            val trophy17 = Trophy.createGeneral("Teleporter", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.ARTICLE)
            val trophy18 = Trophy.createGeneral("Spry", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.ARTICLE)
            val trophy19 = Trophy.createGeneral("Passionate", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.ARTICLE)
            val trophy20 = Trophy.createGeneral("Talkactive", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.ARTICLE)

            transact {
                user1.getVertex.setProperty("postUniqueChannel", 70)
                user2.getVertex.setProperty("teenPostADayOnWeek", true)
                user3.getVertex.setProperty("userPostCount", 506)
                user4.getVertex.setProperty("userPostCount", 2000)
            }

            trophy17.setAutoAssignWithInterval("postUniqueChannel", 70, 0)
            trophy18.setAutoAssignWithValue("teenPostADayOnWeek", "true")
            trophy19.setAutoAssignWithInterval("userPostCount", 500, 1000)
            trophy20.setAutoAssignWithInterval("userPostCount", 1000, 0)

            User.rootVertex.pipe.out(DAO_LIST).iterator().foreach { v =>
                val users = v.toCC[User]
                users.foreach { user =>
                    trophy17.giveTo(user)
                    trophy18.giveTo(user)
                    trophy19.giveTo(user)
                    trophy20.giveTo(user)
                }
            }

            (trophy17.userHasTrophy(user1) must beTrue) and
                (trophy18.userHasTrophy(user2) must beTrue) and
                (trophy19.userHasTrophy(user3) must beTrue) and
                (trophy20.userHasTrophy(user4) must beTrue)
        }

        def calculateTrophyPostAppreciated = {
            val trophy21 = Trophy.createGeneral("Cupidbomb", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.ARTICLE)
            val trophy22 = Trophy.createGeneral("Superpost", "", "http:digaku.com/icon_sprite_trophy.png", TrophyGroup.ARTICLE)

            transact {
                user1.getVertex.setProperty("cupidBombCount", 20)
                user2.getVertex.setProperty("userSuperpost", 25)
            }

            trophy21.setAutoAssignWithInterval("cupidBombCount", 10, 0)
            trophy22.setAutoAssignWithInterval("userSuperpost", 25, 0)

            User.rootVertex.pipe.out(DAO_LIST).iterator().foreach { v =>
                val users = v.toCC[User]
                users.foreach { user =>
                    trophy21.giveTo(user)
                    trophy22.giveTo(user)
                }
            }

            (trophy21.userHasTrophy(user1) must beTrue) and
                (trophy22.userHasTrophy(user2) must beTrue)

        }


    }

}
