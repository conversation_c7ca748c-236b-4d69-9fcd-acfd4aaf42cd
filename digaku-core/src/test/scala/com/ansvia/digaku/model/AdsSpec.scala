///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
///**
// * Author: Andrie (<EMAIL>)
// *
// */
//
//import com.ansvia.digaku._
//import com.ansvia.digaku.persistence.BackedKVStoreIface
//import com.ansvia.digaku.se.{DigakuSearchEngine, EmbeddedElasticSearchEngine}
//import org.specs2.Specification
//import org.specs2.specification.Step
//import scala.sys.process._
//
//class MicroAdSpec extends Specification with DigakuTest {
//
//    var origEngine:Engine = null
//
//    def is = {
//        sequential ^
//            "MicroAd should" ^
//            Step {
//                // kita butuh ini karena digunakan untuk getByTags
//
//                origEngine = Digaku.engine
//
////                Digaku.engine.searchEngine = new EmbeddedElasticSearchEngine("/tmp/digaku-es-index-test")
//
//                Digaku.engine.searchEngine match {
//                    case emb:EmbeddedElasticSearchEngine =>
//                        Seq("rm", "-rf", emb.storeDir).!
//                    case _ =>
//                }
//
//                Digaku.engine = new DigakuTestEngine {
//                    override val contentProcessor: ContentProcessor = null
//                    override val systemConfig: BackedKVStoreIface = null
//                    override lazy val searchEngine: DigakuSearchEngine = new EmbeddedElasticSearchEngine("/tmp/digaku-es-index-test")
//                }
//            } ^
//            "be able to create" ! trees.create ^
//            "be able to getting list" ! trees.getList ^
//            "be able to get by title" ! trees.getByTitle ^
//            "get by tags" ! trees.getByTags ^
//            "delete ad" ! trees.delete ^
//            Step {
//                Digaku.engine.searchEngine.close()
//                Thread.sleep(5000)
//                Digaku.engine = origEngine
////                Digaku.engine.searchEngine = NopSearchEngine
//            } ^
//        end
//    }
//
//    object trees {
//
//        val u = genUser
//
//        def create = {
//            val rv = MicroAd.create("iklan1",
//            "iklan satu", "http://www.mindtalkk.com", "mindtalk,iklan", u)
//            rv must beAnInstanceOf[MicroAd]
//        }
//
//        def getList = {
//            val rv = MicroAd.create("iklan2", "iklan dua",
//            "http://www.mindtalk.com", "mindtalk,iklan,cat", u)
//            MicroAd.getListRight(None, None, 5).toList must be contain(rv)
//        }
//
//        def getByTitle = {
//            val rv = MicroAd.getByTitle("iklan1")
//            rv must be not equalTo(None)
//        }
//
//        def getByTags = {
//            Thread.sleep(5000)
//            val rv = MicroAd.getByTags(Seq("mindtalk"))
//            val rv2 = MicroAd.getByTags(Seq("cat"))
//            (rv.map(_.title).toList must be contain("iklan1","iklan2")) and
//            (rv2.map(_.title).toList must be contain("iklan2") only)
//        }
//
//        def delete = {
//            val x = MicroAd.getByTags(Seq("mindtalk")).head
//            Ads.delete(x)
//            Ads.getList.toList must not contain(x)
//        }
//
//    }
//
// }
//
//
//class BannerAdSpec extends Specification with DigakuTest {
//
//    import java.text.SimpleDateFormat
//    import java.util.{Calendar, Date}
//
//import com.ansvia.digaku.model.BannerAd.ChannelAdBanner
//    import com.ansvia.digaku.utils.DateUtils
//
//
//    def is = {
//        sequential ^
//            "Banner Slider should" ^
//            "be able to create BannerSlider" ! trees.createBs ^
//            "get creator banner" ! trees.getCreatorBanner ^
//            "get banner slider active" ! trees.getListActive ^
//            p ^
//            "Banner Ad should" ^
//            "be able to create BannerAd" ! trees.createBa ^
//            "be able to set BannerAd to Group" ! trees.setBaToChannel ^
//            "group get BannerAd" ! trees.channelGetBannerAd ^
//            "Banner Ad get advertiser" ! trees.getAdvertiser ^
//            "Banner Ad is allowed" ! trees.isAllowed ^
//            "Group remove banner ad" ! trees.removeBannerAd ^
//        end
//    }
//
//    object trees {
//
//        val user1 = genUser
//        val user2 = genUser
//        val user3 = genUser
//
//        val ch1 = genChannel
//        val ch2 = genChannel
//        val ch3 = genChannel
//
//        private val timeFormatter = new SimpleDateFormat("dd/MM/yyyy")
//
//        val rvBanner1 = BannerSlider.create("Banner iklan satu", "https://www.mindtalk.com",
//            "https://dmcd6hvaqrxz0.cloudfront.net/user_profile_banner/db25f9b3fb62a1fe72d969c93e1043375924265f.jpg", user2)
//        val rvBanner2 = BannerSlider.create("Banner iklan dua", "https://www.mindtalk.com",
//            "https://dmcd6hvaqrxz0.cloudfront.net/user_profile_banner/db25f9b3fb62a1fe72d969c93e1043375924265f.jpg", user3)
//
//        val rvAdBanner1 = BannerAd.create("Banner group satu", "https://www.mindtalk.com",
//            "https://dmcd6hvaqrxz0.cloudfront.net/user_profile_banner/db25f9b3fb62a1fe72d969c93e1043375924265f.jpg", user1, BannerAd.Kind.GROUP)
//        val rvAdBanner2 = BannerAd.create("Banner group dua", "https://www.mindtalk.com",
//            "https://dmcd6hvaqrxz0.cloudfront.net/user_profile_banner/db25f9b3fb62a1fe72d969c93e1043375924265f.jpg", user2, BannerAd.Kind.GROUP)
//
//        def createBs = {
//            rvBanner1 must beAnInstanceOf[BannerSlider]
//        }
//
//        def getCreatorBanner = {
//            rvBanner1.creatorBanner must beEqualTo(user2)
//        }
//
//        def getListActive = {
//            rvBanner1.setClosed(false, user3, "").save()
//            rvBanner2.setClosed(true, user1, "").save()
//
//            (rvBanner1.isClosed must beFalse) and
//                (rvBanner2.isClosed must beTrue) and
//                (BannerSlider.getListActive(0, 5) must contain(rvBanner1))
//        }
//
//        def createBa = {
//            rvAdBanner1 must beAnInstanceOf[BannerAd]
//        }
//
//        def setBaToChannel = {
//            val now = Digaku.engine.dateUtils.now
//            val cal = Calendar.getInstance()
//            cal.setTime(new Date(now.getTime))
//            cal.add(Calendar.DATE, 1)
//            val st = timeFormatter.parse(timeFormatter.format(new Date(now.getTime)))
//            val et = timeFormatter.parse(timeFormatter.format(cal.getTime))
//
//            rvAdBanner2.addToChannel(user3, st, et, ch1)
//            rvAdBanner2.addToChannel(user2, st, et, ch2)
//            rvAdBanner2.addToChannel(user1, st, et, ch3)
//
//            Thread.sleep(2000)
//            (ch1.hasAdBanner must beTrue) and
//                (rvAdBanner2.getListChannel(0, 5) must contain(ch1, ch2, ch3))
//        }
//
//        def channelGetBannerAd = {
//            ch1.getBannerAd.get must beEqualTo(rvAdBanner2)
//        }
//
//        def getAdvertiser = {
//            rvAdBanner2.getAdvertiser must beEqualTo(user3)
//        }
//
//        def isAllowed = {
//            rvAdBanner2.isAllowed must beTrue
//        }
//
//        def removeBannerAd = {
//            ch1.removeBannerAd(rvAdBanner2.bannerAdPicture)
//
//            rvAdBanner2.getListChannel(0, 5) must not contain(ch1, ch2, ch3)
//        }
//    }
//
//}
