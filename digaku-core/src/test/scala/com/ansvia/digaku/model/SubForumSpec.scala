///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import org.specs2.specification.Step
//import com.ansvia.digaku.exc.{PermissionDeniedException, DigakuException}
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.notifications.impl.{BecomeOwnerInviteNotification, BecomeStaffInviteNotification}
//import com.ansvia.digaku.model.Ability._
//import java.util.{Date, Calendar}
//import com.ansvia.graph.IdGraphTitanDbWrapper._
//import com.ansvia.digaku.stream.StreamBuilder
//
//
///**
// * Author: robin
// *
// */
//class SubForumSpec extends Specification with DigakuTest {
//    def is =
//        sequential ^
//            "Group model should" ^
//            p ^
//                Step(cleanUp()) ^
//                "saved to database" ! trees.savedToDatabase ^
//                "saved data read rank correctly" ! trees.savedDataCorrectly ^
//                "saved data read largeLogoUrl correctly" ! trees.savedDataCorrectly2 ^
//                "saved data read smallLogoUrl correctly" ! trees.savedDataCorrectly3 ^
//                "saved data read origin kind correctly" ! trees.savedDataCorrectly4 ^
//                "saved data read origin deleted correctly" ! trees.savedDataCorrectly5 ^
//                "saved data read origin deletedReason correctly" ! trees.savedDataCorrectly6 ^
//                "saved data read origin deletedTime correctly" ! trees.savedDataCorrectly7 ^
//                "saved data read private correctly" ! trees.savedDataCorrectly8 ^
//            p ^
//            "Creation and ownership" ^
//                "first creation not make owner joined edge duplicate" ! trees.createNotDuplicateJoinState ^
//            p ^
//            "Add and get staff group" ^
//            p ^
//                "add staff group" ! addGetAndUnsetStaff.addStaff ^
//                "get staff Attribute" ! addGetAndUnsetStaff.getStaffAttribute ^
//                "get staff Ability" ! addGetAndUnsetStaff.getStaffAbility ^
//                "get staffs" ! addGetAndUnsetStaff.getStaffs ^
//                "remove staff group" ! addGetAndUnsetStaff.removeStaff ^
//            p ^
//            "Staff invitation" ^
//            p ^
//                "invite staff" ! staffInvitation.inviteStaff ^
//                "non owner/staff cannot invite other user" ! staffInvitation.nonOwnerInviteUser ^
//                "invited user got and can accept and become staff quickly" ! staffInvitation.userAccept ^
//            p ^
//            "Owner invitation" ^
//            p ^
//                "invite owner" ! ownerInvitation.inviteOwner ^
//                "non owner/staff cannot invite other user" ! ownerInvitation.nonOwnerInviteUser ^
//                "invited user got and can accept and become owner quickly" ! ownerInvitation.userAccept ^
//            p ^
//            "Get stream untuk group seharusnya" ^
//            p ^
//                "fungsi dasar get stream bisa dapetin stream dengan benar" ! getStreamPostChannel.basicStream ^
//                "advanced get stream bisa dapetin stream dengan benar" ! getStreamPostChannel.advancedStream ^
//                "get stream menggunakan super class Post harus mendapatkan urutan list object turunannya dengan benar" ! getStreamPostChannel.getBasicStreamPost ^
//                "popular post harus berurutan dari yang memiliki response terbanyak" ! getStreamPostChannel.getPopularDiscussionStreamCorrectly ^
//                "post yang tidak ada responnya tidak termasuk dalam popular post" ! getStreamPostChannel.getPopularDiscussionStreamCorrectly2 ^
//                "active post harus berurutan dari creationtime terbaru" ! getStreamPostChannel.getActivePostStreamCorrectly ^
//                "post yang tidak ada responsenya tidak termasuk dalam active post" ! getStreamPostChannel.getActivePostStreamCorrectly2 ^
//                "active post picture harus berurutan dari creation time terbaru" ! getStreamPostChannel.getActivePictureCorrectly ^
//                "post picture yang tidak ada responsenya tidak termasuk dalam active post picture" ! getStreamPostChannel.getActivePictureCorrectly2 ^
//                "getStreamObj tidak mengembalikan deleted post" ! getStreamPostChannel.getStreamNoReturnDeleted ^
//                "getStreamKindObj mengembalikan kind yang benar" ! getStreamPostChannel.getStreamWithKind ^
//                "getStream mendapatkan retalkedWrapper" ! getStreamPostChannel.getStreamWithRetalkWrapper ^
//            p ^
//            "Other features" ^
//            p ^
//                "save and get features" ! channelFeatures.saveAndGetFeatures ^
//                "add postMarks" ! channelFeatures.addPostMark ^
//                "get postMark list" ! channelFeatures.getPostMarkList ^
//                "remove postMarks" ! channelFeatures.removePostMark ^
//                "remove postMarks by object" ! channelFeatures.removePostMarkByObj ^
//                "post mark max 10 items" ! channelFeatures.postMarkMax10 ^
//            p ^
//            "Menghitung origin yang masuk ke group" ^
//            p ^
//                "count post at group" ! channelGetCount.postCount ^
//                "count picture at group" ! channelGetCount.pictureCount ^
//                "count event at group" ! channelGetCount.eventCount ^
//                "count deal at group" ! channelGetCount.dealCount ^
//            p ^
//                "count post at group" ! channelStickyCount.postCount ^
//                "count picture at group" ! channelStickyCount.pictureCount ^
//                "count event at group" ! channelStickyCount.eventCount ^
//                "count sticky post at group" ! channelStickyCount.stickyPostCount ^
//            p ^
//            "Post mark filter" ^
//            p ^
//                "get post by post mark filter" ! postMark.filterByPostMark ^
//            Step(tearDown()) ^
//            end
//
//    private def dueDate = {
//        val cal = Calendar.getInstance()
//        cal.setTime(new Date())
//        cal.add(Calendar.DATE, 1)
//        cal.getTime
//    }
//
//    object trees {
//
//        import com.ansvia.graph.BlueprintsWrapper._
////
////        Config.database = TitanConfig("memory:titan")
////        Config.autoCloseWhenDbNotUsed = true
////        Config.init()
////
////        implicit def db = Global.database.useAndGet[TitanGraph]()
//
//        val ch = Forum("ch", "ch desc")
//        ch.rank = 3
//        ch.largeLogoUrl = "large"
//        ch.smallLogoUrl = "small"
//        ch.memberCount = 700
//        ch.visibility = ForumVisibility.ON_SEARCH
//        ch.hashtagable = false
//        ch.adsPublisher = "ads"
//        ch.bannerPicUrl = "banner"
//        ch.kind = OriginKind.SUB_FORUM
//        ch.deleted = false
//        ch.deletedTime = 1L
//        ch.deletedReason = "none"
//        ch.privated = true
//        ch.setWhoCanCreateDeal(SubForumPermission.ALL)
//        ch.setWhoCanCreateArticle(SubForumPermission.ALL)
//        ch.setWhoCanResponses(SubForumPermission.ALL)
//        ch.setWhoCanCreatePictures(SubForumPermission.ALL)
//
////        def init() {
////
////            // clean up database
////            db.getVertices.foreach(db.removeVertex)
////            db.getEdges.foreach(db.removeEdge)
////        }
////
////        def close(){
////            //            Global.database.close()
////            Global.database.done()
////        }
//
//        def savedToDatabase = {
//            ch.saveWithLabel(VertexLabels.GROUP).toCC[Forum].isDefined
//        }
//
//        def savedDataCorrectly = {
//            val ch = Forum.getByName("ch").get
//            ch.rank must beEqualTo(3)
//        }
//
//        def savedDataCorrectly2 = {
//            val ch = Forum.getByName("ch").get
//            ch.largeLogoUrl must beEqualTo("large")
//        }
//
//        def savedDataCorrectly3 = {
//            val ch = Forum.getByName("ch").get
//            ch.smallLogoUrl must beEqualTo("small")
//        }
//
//        def savedDataCorrectly4 = {
//            val ch = Forum.getByName("ch").get
//            ch.kind must beEqualTo(OriginKind.SUB_FORUM)
//        }
//        def savedDataCorrectly5 = {
//            val ch = Forum.getByName("ch").get
//            ch.deleted must beFalse
//        }
//        def savedDataCorrectly6 = {
//            val ch = Forum.getByName("ch").get
//            ch.deletedReason must beEqualTo("none")
//        }
//        def savedDataCorrectly7 = {
//            val ch = Forum.getByName("ch").get
//            ch.deletedTime must beEqualTo(1L)
//        }
//
//        def savedDataCorrectly8 = {
//            val ch = Forum.getByName("ch").get
//            ch.getPrivated must beTrue
//        }
//
//        def createNotDuplicateJoinState = {
//            val u = genUser
//            u.increasePoints(15)
//
//            val ch = genChannelWithOwner(u)
//
//            ch.getMembers(0, 100).toList.length must_== 1
//        }
//
//    }
//
//
//    object staffInvitation extends DigakuTest {
//
//        val userA = genUser
//        userA.increasePoints(15)
//
//        val userB = genUser
//        val userC = genUser
//
//        val ch = Forum.create(genChannelName, "invite staff group description", userA, Array.empty[String])
//
//        def inviteStaff = {
//
//            val inv = ch.inviteToBecomeStaff(userA, userB, "admin", Array("kick"))
//
//            Thread.sleep(5000L)
//
//            val notifs = userB.reload().getAttentionNotifications(0, 2, NotificationState.UNREAD)
//
//            (ch.isInvitationCodeValid(inv.code) must beTrue) and
//                (ch.isInvited(userB, InvitationKind.BECOME_STAFF) must beTrue) and
//                (notifs.count(_.isInstanceOf[BecomeStaffInviteNotification]) must beGreaterThan(0))
//        }
//
//        def nonOwnerInviteUser = {
//            ch.inviteToBecomeStaff(userC, userB, "admin", Array("kick")) must throwAn[PermissionDeniedException]
//        }
//
//        def userAccept = {
//            val notifs = userB.reload().getAttentionNotifications(0, 2, NotificationState.UNREAD)
//
//            val notifInvit = notifs(0).asInstanceOf[BecomeStaffInviteNotification]
//
//            notifInvit.accept()
//
//            ch.reload()
//
//            (ch.isStaff(userB) must beTrue) and // harus jadi staff
//                (ch.isMember(userB) must beTrue) and // harus member juga
//                (ch.isInvited(userB, InvitationKind.BECOME_STAFF) must beFalse) and // invitation-nya harus hilang.
//                (ch.getStaffAttribute(userB).title must beEqualTo("admin")) and
//                (ch.getStaffAbility(userB).toList must be contain("kick"))
//        }
//    }
//
//    object ownerInvitation extends DigakuTest {
//
//        val userD = genUser
//        userD.increasePoints(15)
//
//        val user3 = genUser
//        val user4 = genUser
//
//        val ch = genChannelWithOwner(userD)
//        user3.setActivated(true)
//
//        def inviteOwner = {
//            val inv = ch.inviteToBecomeOwner(userD, user3)
//
//            Thread.sleep(5000L)
//
//            val notifs = user3.reload().getAttentionNotifications(0, 2, NotificationState.UNREAD)
//
//            (ch.isInvitationCodeValid(inv.code) must beTrue) and
//                (ch.isInvited(user3, InvitationKind.BECOME_OWNER) must beTrue) and
//                (notifs.count(_.isInstanceOf[BecomeOwnerInviteNotification]) must beGreaterThan(0))
//        }
//
//        def nonOwnerInviteUser = {
//            ch.inviteToBecomeOwner(user4, user3) must throwAn[PermissionDeniedException]
//        }
//
//        def userAccept = {
//            val notifs = user3.reload().getAttentionNotifications(0, 2, NotificationState.UNREAD)
//
//            val notifInvit = notifs(0).asInstanceOf[BecomeOwnerInviteNotification]
//
//            notifInvit.accept()
//
//            ch.reload()
//
//            (ch.getOwner must beSome(user3)) and // harus jadi owner
//                (ch.isMember(user3) must beTrue) and // harus member juga
//                (ch.isInvited(user3, InvitationKind.BECOME_OWNER) must beFalse) // invitation-nya harus hilang.
//        }
//    }
//
//    object getStreamPostChannel {
//
//        val u1 = genUser
//        u1.increasePoints(15)
//
//        val u2 = genUser
//        val ch1  = genChannelWithOwner(u1)
//        val ch2  = genChannelWithOwner(u1)
//        ch1.setAllPermission(SubForumPermission.ALL)
//        ch2.setAllPermission(SubForumPermission.ALL)
//
//        val post1 = Post.createSimple(u1, "abcd", ch1)
//        val res1Post1 = post1.addResponse(u2, "efgh")
//        val res2Post1 = post1.addResponse(u1, "ijkl")
//        val res3Post1 = post1.addResponse(u2, "mnop")
//
//        val post2 = Post.createSimple(u1, "141516", ch1)
//        val res1Post2 = post2.addResponse(u2, "171819")
//
//        val post3 = Post.createSimple(u1, "1234", ch1)
//        val res1Post3 = post3.addResponse(u2, "6789")
//        val res2Post3 = post3.addResponse(u1, "10111213")
//
//        val post4 = Post.createSimple(u1, "202122", ch1)
//
//        import scala.collection.JavaConversions._
//        import com.ansvia.graph.BlueprintsWrapper._
//
////        println("u1.name = " + u1.name)
//        u1.getVertex.pipe.out(Label.COLLECTION_VERTEX)
//            .out(Label.PUBLISH_CONTENT).iterator().foreach(x => print(x + ","))
////        println(".")
//
//        val pic1 = Picture.create(u1, "123", ch1)
//
//        val v = db.getVertex(pic1.getId)
////        println("v.id: " + v.getId)
//        val pic1Creator = v.pipe.in(Label.PUBLISH_CONTENT).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT)
//            .in(Label.COLLECTION_VERTEX).headOption.flatMap(_.toCC[User])
////        println("pic1 creator: " + pic1Creator + " = " + pic1.creatorOption)
////        println("v.pipe.in(Label.PUBLISH_CONTENT): " + v.pipe.in(Label.PUBLISH_CONTENT).toList)
//
//        val res1Pic1 = pic1.addResponse(u2, "any")
//        val res2Pic1 = pic1.addResponse(u1, "aoe")
//
//        val pic2 = Picture.create(u1, "456", ch1)
//        val pic3 = Picture.create(u1, "789", ch1)
//
//        val res1Pic3 = pic3.addResponse(u1, "123")
//        val res2Pic3 = pic3.addResponse(u2, "456")
//        val res3Pic3 = pic3.addResponse(u1, "789")
//
//        def basicStream = {
//            val article1 = Article.create(u1, "hello 1", "any content", "", ch1); Thread.sleep(100)
//            val article2 = Article.create(u1, "hello 2", "any content", "", ch1); Thread.sleep(100)
//            val article3 = Article.create(u1, "hello 3", "any content", "", ch1); Thread.sleep(100)
//            ch1.getStream[Article](0, 10).toList must be contain(article3, article2, article1) inOrder
//        }
//
//        def advancedStream = {
//            val article4 = Article.create(u1, "hello 4", "any content", "", ch1); Thread.sleep(100)
//            val article5 = Article.create(u1, "hello 5", "any content", "", ch1); Thread.sleep(100)
//            val article6 = Article.create(u1, "hello 6", "any content", "", ch1); Thread.sleep(100)
//
//            (ch1.getStream[Article](0, 10).toList must be contain(article4)) and
//            (ch1.getStream[Article](1, 2).toList must be contain(article5, article6)).only.inOrder
//        }
//
//        def getBasicStreamPost = {
//
//            val ch2Article1 = Article.create(u1, "hello 7", "any content", "", ch2); Thread.sleep(100)
//            val ch2Article2 = Article.create(u1, "hello 8", "any content", "", ch2); Thread.sleep(100)
//            val ch2Article3 = Article.create(u1, "hello 9", "any content", "", ch2); Thread.sleep(100)
//            val ch2Article4 = Article.create(u1, "hello 10", "any content", "", ch2); Thread.sleep(100)
//            val ch2Article5 = Article.create(u1, "hello 11", "any content", "", ch2); Thread.sleep(100)
//            val ch2Article6 = Article.create(u1, "hello 12", "any content", "", ch2); Thread.sleep(100)
//            val picture1 = Picture.create(u1, "pict title 1", ch2);  Thread.sleep(100)
//            val ch2Article7 = Article.create(u1, "hello 13", "any content", "", ch2); Thread.sleep(100)
//            val picture2 = Picture.create(u1, "pict title 1", ch2);  Thread.sleep(100)
//            val ch2Article8 = Article.create(u1, "hello 14", "any content", "", ch2); Thread.sleep(100)
//            val picture3 = Picture.create(u1, "pict title 2", ch2);  Thread.sleep(100)
//            val ch2Article9 = Article.create(u1, "hello 15", "any content", "", ch2); Thread.sleep(100)
//            val picture4 = Picture.create(u1, "pict title 3", ch2);  Thread.sleep(100)
//            val ch2Article10 = Article.create(u1, "hello 16", "any content", "", ch2); Thread.sleep(100)
//
//            (ch2.getStream[Post](0, 20).toList must be contain(ch2Article10, ch2Article9, ch2Article8, ch2Article7,
//                ch2Article6, ch2Article5, ch2Article4, ch2Article3, ch2Article2, ch2Article1)).only.inOrder
//        }
//
//        def getPopularDiscussionStreamCorrectly = {
//
//            val u3 = genUser
//
//            ch1.addMembers(u3)
//
//            val article1 = Article.create(u1, "pop discussion #1", "any content #1", "", ch1); Thread.sleep(100)
//            val article2 = Article.create(u1, "pop discussion #2", "any content #2", "", ch1); Thread.sleep(100)
//            val article3 = Article.create(u1, "pop discussion #3", "any content #3", "", ch1); Thread.sleep(100)
//
//            article2.addResponse(u2, "yuhuu")
//            article2.addResponse(u2, "yuhuu lagi")
//            article2.addResponse(u2, "yuhuu lagi-lagi")
//            article1.addResponse(u2, "yuhuu juga")
//            article1.addResponse(u3, "yuhuu apa deh?")
//            article1.addResponse(u3, "yuhuu juga deh")
//            article3.addResponse(u3, "yuhuu apa?")
//            article3.addResponse(u3, "yuhuu apa nih?")
//
//            val posts =  ch1.getPopularDiscussionStream[Article](0,10).map(item =>
//                item.getContent
//            )
//
//            posts must contain("any content #2", "any content #1", "any content #3").inOrder
//        }
//
//        def getPopularDiscussionStreamCorrectly2 = {
//
//            val post =  ch1.getPopularDiscussionStream[Post](0,10).map(item =>
//                item.getContent
//            )
//            post must not contain ("202122")
//        }
//
//        def getActivePostStreamCorrectly = {
//            val post =  ch1.getActivePostStream[Post](0,10).map(item =>
//                item.getContent
//            )
//            post.get(0) must beEqualTo("abcd")
//            post.get(2) must beEqualTo("1234")
//            post.get(1) must beEqualTo("any content #2")
//
//        }
//        def getActivePostStreamCorrectly2 = {
//            val post =  ch1.getActivePostStream[Post](0,10).map(item =>
//                item.getContent
//            )
//            post must not contain("202122")
//        }
//
//        def getActivePictureCorrectly = {
//            val pict = ch1.getActivePictureStream[Picture](0, 10).map(item =>
//                item.title
//            )
//            pict.get(1) must beEqualTo("123")
//            pict.get(0) must beEqualTo("789")
//        }
//
//        def getActivePictureCorrectly2 = {
//            val pict = ch1.getActivePictureStream[Picture](0, 2).map(item =>
//                item.title
//            )
//
//            pict must not contain("456")
//        }
//
//        def getStreamNoReturnDeleted = {
//            val u1 = genUser
//            u1.increasePoints(15)
//
//            val ch1 = genChannelWithOwner(u1)
//
//            u1.reload()
//
//            val article1 = Article.create(u1, "hello 1", "any content", "", ch1); Thread.sleep(100)
//            val article2 = Article.create(u1, "hello 2", "any content", "", ch1); Thread.sleep(100)
//            val article3 = Article.create(u1, "hello 3", "any content", "", ch1); Thread.sleep(100)
//
//            article2.setDeleted(DeletedType.HARD,DeletedRole.POST_CREATOR,u1)
//
//            // harusnya hanya return article1 dan article3
//            // article2 tidak karena article2 sudah di-mark as deleted
//            (ch1.getStreamObj[Article](0, 10).map(_.content).toList must be contain(article3, article1) only) and
//                (ch1.getStream[Article](0, 10).toList must be contain(article3, article1) only)
//        }
//
//        def getStreamWithKind= {
//            val u1 = genUser
//            u1.increasePoints(15)
//
//            val ch1 = genChannelWithOwner(u1)
//
//            u1.reload()
//
//            val article1 = Article.create(u1, "hello 1", "any content", "", ch1); Thread.sleep(100)
//            // ubah creation time nya jadi tahun 2013
//            article1.creationTime = 1386654059
//            val article2 = Article.create(u1, "hello 2", "any content", "", ch1); Thread.sleep(100)
//            val article3 = Article.create(u1, "hello 3", "any content", "", ch1); Thread.sleep(100)
//
//            StreamBuilder.addToStream(ch1, article1, true)
//
//            article2.setDeleted(DeletedType.HARD,DeletedRole.POST_CREATOR,u1)
//
//            // harusnya hanya return article1 dan article3
//            // article2 tidak karena article2 sudah di-mark as deleted
//            (ch1.getStreamKindObj(StreamKind.TEXT, 0, 10).map(_.content).toList must be contain(article3, article1, article1) only) and
//                (ch1.getStreamKindObj(StreamKind.TEXT, 0, 10, None, None, StickyState.ALL, Some(2013), Some(12)).map(_.content).toList must be contain(article1) only)
//
//        }
//
//        def getStreamWithRetalkWrapper = {
//            val u1 = genUser
//            val u2 = genUser
//
//            u1.increasePoints(15)
//            u2.increasePoints(15)
//
//            val chOrigin = genChannelWithOwner(u1)
//            val chTarget = genChannelWithOwner(u2)
//
//            chOrigin.addMembers(u2)
//            chTarget.addMembers(u1)
//
//            val p1 = Article.create(u1, "title", "conten", "tags", chOrigin)
//            val retalkWrapper = u2.retalk(p1, chTarget)
//
//            (chTarget.getStreamAll(offset = 0, limit = 10).map(_.content).toList must be contain retalkWrapper) and
//                (chTarget.getStreamObj(0, 10).map(_.content).toList must be contain retalkWrapper)
//        }
//
//
//    }
//
//    object channelFeatures {
//
//        val ch1Name = genChannelName
//        val user1 = genUser
//        user1.increasePoints(15)
//
//        val ch1 = Forum.create(ch1Name, ch1Name + " desc", user1)
//        ch1.supportedFeatures = Array.empty[String]
//        ch1.save()
//        db.commit()
//        ch1.reload()
//
//        val name1 = genRandomString
//        val name2 = genRandomString
//        val name3 = genRandomString
//        val name4 = genRandomString
//
//        val postmark1 = PostMark.create(name1, "#cacaca")
//        val postmark2 = PostMark.create(name2, "#cacaff")
//        val postmark3 = PostMark.create(name3, "#22caff")
//        ch1.addPostMark(postmark1.title)
//        ch1.addPostMark(postmark2.title)
//        ch1.addPostMark(postmark3.title)
//
//        def saveAndGetFeatures = {
//            val chOld = Forum.getById(ch1.getId).get // before save
//            ch1.supportedFeatures = Array(ForumFeatures.PHOTO, ForumFeatures.EVENT)
//            ch1.save()
//            val chx = Forum.getById(ch1.getId).get
//            (chOld.hasFeature(ForumFeatures.PHOTO) must beFalse) and
//              (chx.hasFeature(ForumFeatures.PHOTO) must beTrue) and
//              (chx.hasFeature(ForumFeatures.EVENT) must beTrue)
//
//        }
//
//
//
//        def addPostMark = {
//            (ch1.hasPostMark(name2) must beTrue) and
//                (ch1.hasPostMark(name3) must beTrue) and
//                (ch1.getPostMark(name1).get.color must beEqualTo("#cacaca"))
//        }
//
//        def getPostMarkList = {
//            val rv = ch1.getPostMarks(0, 2).map(_.title)
//
//            (rv must be contain(name1)) and
//                (rv must be contain(name3))
//        }
//
//        def removePostMark = {
//            ch1.removePostMark(name3)
//            ch1.hasPostMark(name3) must beFalse
//        }
//
//        def removePostMarkByObj = {
//            val pm = ch1.getPostMark(name2).get
//            ch1.removePostMark(pm)
//            ch1.hasPostMark(name2) must beFalse
//        }
//
//        def postMarkMax10 = {
//            import com.ansvia.digaku.model.Label._
//            import com.ansvia.graph.BlueprintsWrapper._
//
//            ch1.removePostMark(name1)
//            ch1.removePostMark(name4)
//            ch1.removePostMark(name3)
//            ch1.getVertex.pipe.outE(POST_MARK).remove()
//            db.commit()
//            for( i <- (0 to 10) ){
//                PostMark.create("post-mark-" + i, "#654321")
//                ch1.addPostMark("post-mark-" + i)
//                println("added " + i)
//            }
//            ch1.addPostMark("post-mark-11") must throwAn[DigakuException]
//        }
//    }
//
//    object channelGetCount {
//        val ch2Name = genChannelName
//
//        val user3 = genUser
//        user3.channelQuota = 3
//
//        val user4 = genUser
//        val channel1 = Forum.create(ch2Name, ch2Name + " desc", user3)
//        channel1.setAllPermission(SubForumPermission.ALL)
//
//        def postCount = {
//            Article.create(user3, "article1", "article1 desc","article, 1", channel1)
//            Article.create(user4, "article2", "article2 desc","article, 2", channel1)
//            Article.create(user3, "article3", "article3 desc","article, 3", channel1)
//
//            channel1.getArticleCount(StickyState.ALL) must beEqualTo(3)
//        }
//
//        def pictureCount = {
//            Picture.create(user3, "picture1", channel1)
//            Picture.create(user4, "picture2", channel1)
//            Picture.create(user3, "picture3", channel1)
//
//            channel1.getPictureCount must beEqualTo(3)
//        }
//
//        def eventCount = {
//            Event.create(user3, "event1", "event1 content", "jogja", channel1, dueDate)
//            Event.create(user4, "event2", "event2 content", "jogja", channel1, dueDate)
//            Event.create(user3, "event3", "event3 content", "jogja", channel1, dueDate)
//
//            channel1.getEventCount must beEqualTo(3)
//        }
//
//        def dealCount = {
//            Deal.create(user3, "deal1", 500, "deal desc" * 50, Currency.IDR, Locale.id_ID, "new", "jogja", channel1)
//
//            channel1.getDealCount must beEqualTo(1)
//        }
//
//    }
//
//    object channelStickyCount {
//        val ch2Name = genChannelName
//
//        val user5 = genUser
//        user5.increasePoints(15)
//
//        val user6 = genUser
//        val channel1 = Forum.create(ch2Name, ch2Name + " desc", user5)
//        channel1.setAllPermission(SubForumPermission.ALL)
//
//        def postCount = {
//            val art1 = Article.create(user5, "article1", "article1 desc","article, 1", channel1)
//            val art2 = Article.create(user6, "article2", "article2 desc","article, 2", channel1)
//            val art3 = Article.create(user5, "article3", "article3 desc","article, 3", channel1)
//
//            art1.setSticky(true)
//            art2.setSticky(true)
//            art3.setSticky(true)
//
//            channel1.stickyArticleCount must beEqualTo(3)
//        }
//
//        def pictureCount = {
//            val pic1 = Picture.create(user5, "picture1", channel1)
//            val pic2 = Picture.create(user6, "picture2", channel1)
//            val pic3 = Picture.create(user5, "picture3", channel1)
//
//            pic1.setSticky(true)
//            pic2.setSticky(true)
//            pic3.setSticky(true)
//
//            channel1.stickyPictureCount must beEqualTo(3)
//        }
//
//        def eventCount = {
//            val evt1 = Event.create(user5, "event1", "event1 content", "jogja", channel1, dueDate)
//            val evt2 = Event.create(user6, "event2", "event2 content", "jogja", channel1, dueDate)
//            val evt3 = Event.create(user5, "event3", "event3 content", "jogja", channel1, dueDate)
//
//            evt1.setSticky(true)
//            evt2.setSticky(true)
//            evt3.setSticky(true)
//
//            channel1.stickyEventCount must beEqualTo(3)
//        }
//
//        def stickyPostCount = {
//            channel1.stickyPostCount must beEqualTo(9)
//        }
//
//    }
//
//    object getInvitationChannel {
//        val ch2Name = genChannelName
//
//        val user7 = genUser
//        user7.channelQuota = 3
//
//        val user8 = genUser
//        val user9 = genUser
//
//        val channel1 = Forum.create(ch2Name, ch2Name + " desc", user7)
//
//        def addInviteUser = {
//            val invite = channel1.inviteUser(user7, user8)
//            channel1.getInvitation(user8).get must beEqualTo(invite)
//        }
//
//        def addInviteToBecomeStaff = {
//            val invite = channel1.inviteToBecomeStaff(user7, user8, "Staff", Array(GENERAL_SETTING_CHANNEL, EDIT_POST, DELETE_POST))
//            channel1.getInvitation(user8).get must beEqualTo(invite)
//        }
//        def addInviteToBecomeOwner() = {
//            user9.setActivated(true)
//            val invite = channel1.inviteToBecomeOwner(user7, user9)
//            channel1.getInvitation(user9).get must beEqualTo(invite)
//        }
//
//
//    }
//
//
//    object addGetAndUnsetStaff {
//        val channel1Name = genChannelName
//
//        val user10 = genUser
//        val user11 = genUser
//        val user12 = genUser
//        user12.increasePoints(15)
//
//        val channel1 = Forum.create(channel1Name, channel1Name + " desc", user12)
//
//        val ability = Array(EDIT_POST, GENERAL_SETTING_CHANNEL)
//        channel1.addMembers(user10)
//        channel1.addStaff(user10, "Staff", ability)
//
//        def addStaff = {
//            (channel1.getStaffByUserName(user10.name).get must beEqualTo(user10)) and
//                (channel1.isStaff(user10) must beTrue)
//        }
//
//        def getStaffAttribute = {
//            val attribute = channel1.getStaffAttribute(user10)
//            (attribute.abilities must beEqualTo(ability)) and
//                (attribute.title must beEqualTo("Staff"))
//        }
//
//        def getStaffAbility = {
//            channel1.getStaffAbility(user10) must beEqualTo(ability)
//        }
//
//        def getStaffs = {
//            channel1.getStaffs(0, 10).toList.map(_.user) must contain(user10)
//        }
//
//        def removeStaff = {
//            channel1.removeMembers(user10)
//            channel1.unsetStaff(user10)
//
//            (channel1.getMemberByUserName(Some(user10.name)) must beEmpty) and
//                (channel1.getStaffByUserName(user10.name) must beEmpty)
//        }
//
//    }
//
//    object postMark {
//        val u3 = genUser
//        u3.increasePoints(15)
//
//        val ch = genChannelWithOwner(u3)
//
//        val p1 = Article.create(u3, "test post mark", "ini content-nya", "", ch)
//
//        val pm = PostMark.create("pm1","#cacaca")
//        p1.setMark(pm)
//
//        def filterByPostMark = {
//
//            ch.reload()
//            ch.filterByPostMark("pm1",0,10).toList must be contain(p1)
//
//        }
//    }
//}
