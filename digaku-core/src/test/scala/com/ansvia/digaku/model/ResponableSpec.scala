// @TODO(robin): benerin ini setelah major refactor

///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.exc.DigakuException
//
///**
// * Author: nadir, robin, temon
// *
// */
//class ResponableSpec extends Specification with DigakuTest {
//
//    def is = {
//        sequential ^
//        "Post dao simple creation with responable should" ^
//            p ^
//            "add response post by user and content" ! postResponable.addResponsePost ^
//            "add response post by umodel" ! postResponable.addResponsePostByModel ^
//            "delete response post by id respose" ! postResponable.deleteResposePostById ^
//            "get responseCount correctly" ! postResponable.responseCountTest ^
//            "prevent double/duplicate response" ! postResponable.noDuplicates ^
//            "get last response correctly" ! postResponable.getLastResponse ^
//            "get index of response" ! postResponable.getIndexOfResponse ^
//            "get responders" ! postResponable.getResponders ^
//            "add response change lastResponseId" ! postResponable.addResponseChangeLastResponseId ^
//            "remove response change lastResponseId" ! postResponable.rmResponseChangeLastResponseId ^
//            "get reesponse mode stream exclude max dan since" ! postResponable.getResponseStreamModeExclusive ^
//            "get reesponse mode stream include max dan since" ! postResponable.getResponseStreamModeInclusive ^
//        end
//    }
//
//    object postResponable {
//
//        val ch1Name = genChannelName
//
//        val u5 = genUser
//        val u6 = genUser
//        val ch5 = genChannelWithOwner(u5)
//        ch5.setAllPermission(SubForumPermission.ALL)
//        val p5 = Post.createSimple(u5, "content article", ch5)
//        println("p5.getResponseCount first: " + p5.getResponseCount)
//        val p5Id = p5.getId
//        val res1 = p5.addResponse(u6, "hello :)")
//        val res1Id = res1.getId
//        val res2 = Response.create(u5, "hello juga")
//        val res2Id =res2.getId
//
//        def addResponsePost = {
//            p5.getResponses(0,10).size must beEqualTo(1)
//        }
//
//        def addResponsePostByModel = {
//            p5.addResponse(res2)
//            p5.getResponses(0,10).size must beEqualTo(2)
//        }
//
//        def deleteResposePostById = {
//            p5.removeResponse(res1Id)
//            p5.getResponses(0,10).size must beEqualTo(1)
//        }
//
//        def responseCountTest = {
//            println("p5.getResponseCount before: " + p5.getResponseCount)
//
//            val before = p5.getResponseCount must equalTo(1)
//            p5.removeResponse(res2Id)
//            val after = p5.getResponseCount must equalTo(0)
//
//            println("p5.getResponseCount after: " + p5.getResponseCount)
//
//            before and after
//        }
//
//        def noDuplicates ={
//            val res3 = Response.create(u5, "duplicates")
//            val res4 = Response.create(u5, "duplicates")
//            p5.addResponse(res3)
//            (p5.addResponse(res4) must throwAn[DigakuException])
//        }
//
//        val p6 = Post.createSimple(u5, "post p6", ch5)
//        val u7 = genUser
//
//        val resp = p6.addResponse(u7, genRandomString)
//        val resp2 = p6.addResponse(u7, genRandomString)
//
//        def getLastResponse = {
//            p6.getLastResponse must_== Some(resp2)
//        }
//
//        def getIndexOfResponse = {
//            (p6.getIndexOfResponse(resp) must_== 0) and
//                (p6.getIndexOfResponse(resp2) must_== 1)
//        }
//
//
//        def getResponders = {
//            p6.getResponders(0, 10).toList must contain(u7)
//        }
//
//
//        def addResponseChangeLastResponseId = {
//            val r = p6.addResponse(u7, genRandomString)
//            p6.getVertex.getProperty[Long]("lastResponseId") must_== r.getId
//        }
//
//        def rmResponseChangeLastResponseId = {
//            val r = p6.getLastResponse.get
//            p6.removeResponse(r)
//            p6.getVertex.getProperty[Long]("lastResponseId") must_== resp2.getId
//        }
//
//        def getResponseStreamModeExclusive = {
//
//            val user1 = genUser
//            val channel1 = genChannelWithOwner(user1)
//            channel1.setAllPermission(SubForumPermission.ALL)
//            val post1 = Post.createSimple(user1, "content article nya", channel1)
//
//            // create response
//            val res1 = post1.addResponse(user1, "hello 1 :)")
//            val res2 = post1.addResponse(user1, "hello 2 :)")
//            val res3 = post1.addResponse(user1, "hello 3 :)")
//            val res4 = post1.addResponse(user1, "hello 4 :)")
//            val res5 = post1.addResponse(user1, "hello 5 :)")
//            val res6 = post1.addResponse(user1, "hello 6 :)")
//            val res7 = post1.addResponse(user1, "hello 7 :)")
//            val res8 = post1.addResponse(user1, "hello 8 :)")
//            val res9 = post1.addResponse(user1, "hello 9 :)")
//            val res10 = post1.addResponse(user1, "hello 10 :)")
//
//
//            val responsesWithSinceId = post1.getResponses(Some(res3.getId), None, 4, inclusive = false)
//            val responsesWithMaxId = post1.getResponses(None, Some(res8.getId), 4, inclusive = false)
//
//            (responsesWithSinceId.toList must be not contain(res3)) and (responsesWithMaxId.toList must be not contain(res8))
//        }
//
//        def getResponseStreamModeInclusive = {
//            val user1 = genUser
//            val channel1 = genChannelWithOwner(user1)
//            channel1.setAllPermission(SubForumPermission.ALL)
//            val post1 = Post.createSimple(user1, "content article nya", channel1)
//
//            // create response
//            val res1 = post1.addResponse(user1, "hello 1 :)")
//            val res2 = post1.addResponse(user1, "hello 2 :)")
//            val res3 = post1.addResponse(user1, "hello 3 :)")
//            val res4 = post1.addResponse(user1, "hello 4 :)")
//            val res5 = post1.addResponse(user1, "hello 5 :)")
//            val res6 = post1.addResponse(user1, "hello 6 :)")
//            val res7 = post1.addResponse(user1, "hello 7 :)")
//            val res8 = post1.addResponse(user1, "hello 8 :)")
//            val res9 = post1.addResponse(user1, "hello 9 :)")
//            val res10 = post1.addResponse(user1, "hello 10 :)")
//
//
//            val responsesWithSinceId = post1.getResponses(None, Some(res3.getId), 4, inclusive = true)
//            val responsesWithMaxId = post1.getResponses(None, Some(res8.getId), 4, inclusive = true)
//
//            (responsesWithSinceId.toList must contain(res3)) and (responsesWithMaxId.toList must contain(res8))
//        }
//
//    }
//}
