/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types._
import org.specs2.Specification
import com.ansvia.digaku.DigakuTest
import java.util.{Date, Calendar}

/**
 * Author: nadir
 *
 */
class EventSpec extends Specification with DigakuTest {
    def is = {
        sequential ^
//            Step {cleanUp()} ^
            "add attender for event" ! addGetAndRemoveAttend.addEventAttend ^
            "get attender for event" ! addGetAndRemoveAttend.getEventAttend ^
            "remove attender event" ! addGetAndRemoveAttend.removeEventAttend ^
            "embed event in article" ! addGetAndRemoveAttend.embedToArticle ^
            "get poll parent object" ! addGetAndRemoveAttend.getPollParent ^
            "removed from HasEmbedded object" ! addGetAndRemoveAttend.removeFromHasEmbedded ^
//            Step(tearDown()) ^
            end
    }

    object addGetAndRemoveAttend {

        val u2 = genUser
        val u3 = genUser
        val ch2 = genForum
//        ch2.setAllPermission(ForumPermission.ALL)
        val e2 = Event.create(u2, "title Event2", "content Event2", "Jakarta", ch2, dueDate, finishDate)
        val e2Id = e2.getId
        val e3 = Event.create(u2, "title Event3", "content Event3", "Jakarta", ch2, dueDate, finishDate)
        val e3Id = e3.getId


        private def dueDate = {
            val cal = Calendar.getInstance()
            cal.setTime(new Date())
            cal.add(Calendar.DATE, 1)
            cal.getTime
        }

        private def finishDate = {
            val cal = Calendar.getInstance()
            cal.setTime(new Date())
            cal.add(Calendar.DATE, 2)
            cal.getTime
        }

        def addEventAttend = {
            //add attender untuk maybe
            e2.addAttenders(AttenderKind.MAYBE, u2)
            //update attender dari maybe ke positive
            e2.addAttenders(AttenderKind.POSITIVE, u2)

            //maybe harus 0
            e2.getAttenders(AttenderKind.MAYBE) must not contain (u2)
            //positif harus lebih dari 0 karena kind edge diset dari maybe jadi positive
            e2.getAttenders(AttenderKind.POSITIVE) must be contain(u2)
        }

        def getEventAttend = {
            e2.getAttenders(AttenderKind.POSITIVE) must be contain(u2)
        }

        def removeEventAttend = {
            e2.addAttenders(AttenderKind.MAYBE, u3)
            e2.removeAttenders(AttenderKind.MAYBE, u3)
            e2.getAttenders(AttenderKind.MAYBE).length>0 must beFalse
        }

        val article = Article.create(u2, "some article", "hello", "ada user", ch2)

        def embedToArticle = {
            article.reload()
            e2.reload()

            article.addEmbeddedObject(e2)
            article.getEmbeddedObjects.toList must be contain(e2)
        }

        def getPollParent = {
            e2.reload()
            (e2.getEmbedParentObject[HasEmbeddedObject[IDType]].isDefined must beTrue) and
                (e2.getEmbedParentObject[HasEmbeddedObject[IDType]] must beEqualTo(Some(article)))
        }

        def removeFromHasEmbedded = {
            article.removeEmbeddedObject(e2)
            article.getEmbeddedObjects.toList must be not contain(e2)
        }

    }

    object move {

        val u2 = genUser
        val u3 = genUser
        val ch = genForum
//        ch.setAllPermission(ForumPermission.ALL)
        val e2 = Event.create(u2, "title Event2", "content Event2", "Jakarta", ch, dueDate, finishDate)

        private def dueDate = {
            val cal = Calendar.getInstance()
            cal.setTime(new Date())
            cal.add(Calendar.DATE, 1)
            cal.getTime
        }

        private def finishDate = {
            val cal = Calendar.getInstance()
            cal.setTime(new Date())
            cal.add(Calendar.DATE, 2)
            cal.getTime
        }

    }
}
