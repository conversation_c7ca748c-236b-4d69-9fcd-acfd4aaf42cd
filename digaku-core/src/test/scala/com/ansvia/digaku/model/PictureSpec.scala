///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//
///**
// * Author: temon
// *
// */
//class PictureSpec extends Specification with DigakuTest {
//    def is = {
//        sequential ^
//            "move to another group soft" ! menuOption.moveSoft ^
//            "move to another group hard" ! menuOption.moveHard ^
//            end
//    }
//
//    object menuOption {
//        val u1 = genUser
//        val ch = genForumWithOwner(u1)
//        ch.setAllPermission(SubForumPermission.ALL)
//        val pic1 = Picture.create(u1, "picture title", ch)
//
//        def moveSoft  = {
//            val ch1 = genForumWithOwner(u1)
//            pic1.setMoved( 1, "admin-group", u1, ch1.getName)
//
//            (pic1.isMoved must beTrue) and
//                (pic1.movedChannelOrigin must beEqualTo(ch.getName)) and
//                (pic1.movedType must beEqualTo(1)) and
//                (ch1.getPictureStream(0, 10).toList must contain(pic1).inOrder) and
//                (ch.getPictureStream(0, 10).toList must not contain(pic1)) and
//                (pic1.reload().origin must beEqualTo(ch1))
//        }
//
//        def moveHard = {
//            val ch2 = genForumWithOwner(u1)
//            pic1.setMoved( 2, "admin-group", u1, ch2.getName)
//
//            (pic1.isMoved must beTrue) and
//                (pic1.movedChannelOrigin must beEqualTo(ch.getName)) and
//                (pic1.movedType must beEqualTo(2)) and
//                (ch2.getPictureStream(0, 10).toList must  contain(pic1).inOrder) and
//                (ch.getPictureStream(0, 10).toList must not contain(pic1)) and
//                (pic1.reload().origin must beEqualTo(ch2))
//        }
//    }
//}
