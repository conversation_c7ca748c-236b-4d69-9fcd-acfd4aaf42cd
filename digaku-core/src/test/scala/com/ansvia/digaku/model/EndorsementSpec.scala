/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

/**
 * Author: robin
 * Date: 10/16/13
 * Time: 7:25 PM
 *
 */


import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import com.ansvia.digaku.exc.AlreadyExistsException


class EndorsementSpec extends Specification with DigakuTest {
    def is = "Endorsement should" ^
        sequential ^
        p ^
        "able to create Endorsement" ! trees.create ^
        "get endorsement by label" ! trees.getByLabel ^
        "user bisa hapus endorsement-nya" ! trees.userRemoveEndorsement ^
        "bisa mendapatkan siapa saja yang meng-endorse-nya" ! trees.getEndorser ^
        "bisa check kalo dah ada" ! trees.checkExists ^
        "check label is valid" ! trees.checkValid ^
        ("user yang sama tidak bisa endorse ke user yang sama " +
            "dengan label yang sama lebih dari sekali") ! trees.doubleCase ^
        "bisa dapetin popular endorsement kind USER" ! trees.getPopUser ^
        "bisa create endorsement kind GROUP" ! trees.createEndorseKindChannel ^
        "bisa endorse GROUP" ! trees.endorseChannel ^
        "bisa menghapus siapa yang meng-endorse-nya" ! trees.removeEndorser ^
        "bisa get endorsement label list kind GROUP" ! trees.getLabelKindChannel ^
        "bisa dapetin popular endorsement kind GROUP" ! trees.getPopChan ^
        "bisa search endorsement kind GROUP" ! trees.searchChan ^
        "get search endorsement kind GROUP count" ! trees.searchChanCount ^
        "cek endorsement dan endorser sudah ada atau belum" ! trees.checkExistsEndorse ^
        "get count endorser dari endorsement" ! trees.getEndorserCount ^
    end

    object trees {

        import Endorsement.Kind._

        val u1 = genUser
        val u2 = genUser

        def create = {
            Endorsement.create("programmer-hardcore", USER) must beAnInstanceOf[Endorsement]
        }

        def getByLabel = {
            Endorsement.getByLabel("programmer-hardcore", USER).isDefined must beTrue
        }

        def userRemoveEndorsement = {
            val end = Endorsement.create("endorsement-test", USER)
            Endorsement.endorse(u1, end, u2, true)

            val before = u2.getEndorsements(0, 2).toList must contain(end).only

            u2.removeEndorsement(end)

            val after = u2.getEndorsements(0, 2).toList must be not contain(end)

            val after2 = Endorsement.getByLabel("endorsement-test", USER).isDefined must beTrue

            before and after and after2

        }

        def getEndorser = {
            val end = Endorsement.create("endorsement-test2", USER)
            val u3 = genUser

            Endorsement.endorse(u1, end, u2, true)
            Endorsement.endorse(u3, end, u2, true)

            implicit val userContext = u2

            val endors = u2.getEndorsements(0, 5).toList

            (endors must contain(end).only) and
                (endors(0).getEndorsers(0, 10).toList must contain(u1, u3).only)

        }

        def checkExists = {
            Endorsement.isExists("endorsement-test", USER) must beTrue
        }

        def checkValid = {
            (Endorsement.isValidLabel("Endorsement1") must beTrue) and
                (Endorsement.isValidLabel("endo_2") must beTrue) and
                (Endorsement.isValidLabel("1endo#") must beFalse) and
                (Endorsement.isValidLabel("endo&endo2") must beFalse)
        }

        def doubleCase = {
            val end = Endorsement.create("endorsement-test3", USER)
            (Endorsement.endorse(u1, end, u2) must be not throwAn[AlreadyExistsException]) and
                (Endorsement.endorse(u1, end, u2) must throwAn[AlreadyExistsException])
        }

        def getPopUser = {

            val name3 = "endor-1"
            val name4 = "endor.2"
            val name5 = "endor_3"

            val end3 = Endorsement.create(name3, USER)
            val end4 = Endorsement.create(name4, USER)
            val end5 = Endorsement.create(name5, USER)

            val tu1 = genUser
            val tu2 = genUser
            val tu4 = genUser

            val u3 = genUser
            val u4 = genUser

            Endorsement.endorse(u1, end3, tu1, true)
            Endorsement.endorse(u1, end4, tu2, true)
            Endorsement.endorse(u2, end3, tu2, true)
            Endorsement.endorse(u2, end4, tu2, true)
            Endorsement.endorse(u3, end4, tu2, true)
            Endorsement.endorse(u4, end4, tu2, true)
            Endorsement.endorse(u3, end3, tu2, true)
            Endorsement.endorse(u1, end5, tu4, true)
            Endorsement.endorse(u2, end5, tu4, true)

            val ends = Endorsement.getPopularEndorsements(USER, 0, 5).toList

//            println(ends)

            /**
             * harusnya end4 diurutkan lebih dulu baru end3
             * karena end4 lebih banyak digunakan.
             */
            ends must contain(end4, end3, end5).inOrder
        }


        private lazy val name1 = "endors1"
        private lazy val name2 = "endors2"
        private lazy val name3 = "superman"

        def createEndorseKindChannel = {
            Endorsement.create(name1, GROUP) must beAnInstanceOf[Endorsement]
        }

        def endorseChannel = {
            val endo = Endorsement.create(name2, GROUP)
            val ch = genForum
            Endorsement.endorse(u1, endo, ch, true)
            ch.reload()
            ch.getEndorsements(0, 10).toList must contain(endo)
        }

        def removeEndorser = {
            val end = Endorsement.create(name3, GROUP)
            val u4 = genUser
            val ch2 = genForum

            Endorsement.endorse(u4, end, ch2, true)

            implicit val channelContext = ch2
            ch2.removeEndorser(u4, end)

            ch2.hasEndorser(u4, end)  must beFalse

        }

        def getLabelKindChannel = {
            Endorsement.getList(0, 10, GROUP).toList.map(_.labelName) must contain(name1, name2, name3).only
        }

        def getPopChan = {

            val name3 = "endorsement1"
            val name4 = "endor_sement2"
            val name5 = "endor.sement3"

            val end3 = Endorsement.create(name3, GROUP)
            val end4 = Endorsement.create(name4, GROUP)
            val end5 = Endorsement.create(name5, GROUP)

            val ch1 = genForum
            val ch2 = genForum
            val ch3 = genForum

            val u3 = genUser
            val u4 = genUser

            Endorsement.endorse(u1, end3, ch1, true)
            Endorsement.endorse(u1, end4, ch2, true)
            Endorsement.endorse(u2, end3, ch2, true)
            Endorsement.endorse(u2, end4, ch2, true)
            Endorsement.endorse(u3, end4, ch2, true)
            Endorsement.endorse(u4, end4, ch2, true)
            Endorsement.endorse(u3, end3, ch2, true)
            Endorsement.endorse(u1, end5, ch3, true)
            Endorsement.endorse(u2, end5, ch3, true)

            val ends = Endorsement.getPopularEndorsements(GROUP, 0, 5).toList

//            println(ends)

            /**
             * harusnya end4 diurutkan lebih dulu baru end3
             * karena end4 lebih banyak digunakan.
             */
            ends must contain(end4, end3, end5).inOrder
        }

        def searchChan = {
            Endorsement.searchEndorsements("endors", 0, 10, GROUP).toList.map(_.labelName) must contain(name1, name2)
        }

        def searchChanCount = {
            val name6 = genRandomString
            val end90 = Endorsement.create(name6, GROUP)

            end90.setBlocked(true, u1).save()
            Endorsement.getEndorsementCount(name6, GROUP, true) must beEqualTo(1)
        }

        def checkExistsEndorse = {
            val name7 = genRandomString
            val end = Endorsement.create(name7, GROUP)
            val u3 = genUser
            val ch3 = genForum

            Endorsement.endorse(u3, end, ch3, true)

            implicit val channelContext = ch3
            (ch3.hasEndorsement(end) must beTrue) and
                (ch3.hasEndorser(u3, end) must beTrue)
        }

        def getEndorserCount = {
            val name8 = genRandomString
            val end91 = Endorsement.create(name8, GROUP)
            val u1 = genUser
            val u2 = genUser
            val ch1 = genForum

            Endorsement.endorse(u1, end91, ch1, true)
            Endorsement.endorse(u2, end91, ch1, true)

            implicit val channelContext = ch1
            ch1.getEndorserCount(end91) must beEqualTo(2)
        }

    }

}

