///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import org.specs2.specification.Step
//import com.ansvia.digaku.DigakuTest
//
///**
// * Author: nadir
// * Date: 1/17/13
// * Time: 2:20 PM
// *
// */
//class SimplePostSpec extends Specification with DigakuTest {
//    def is = {
//        sequential ^
//        "SimplePost model should" ^
//         p ^
//            Step(cleanUp()) ^
//            "saved to database" ! trees.saveToDataBase ^
//            "saved data read moid correctly" ! trees.saveToDataBase ^
//            "saved data read containsGreeting correctly" ! trees.savedDataCorrectly1 ^
//            "saved data read containsLink" ! trees.savedDataCorrectly2 ^
//            "saved data read containsPisc" ! trees.savedDataCorrectly3 ^
//            "saved data read containsVideo correctly" ! trees.savedDataCorrectly4 ^
//            "saved data read content correctly" ! trees.savedDataCorrectly5 ^
//            "saved data read kind correctly" ! trees.savedDataCorrectly6 ^
//            "be able to close" ! trees.closePost ^
//            "be able to unclose (open)" ! trees.openPost ^
//            "set containsLink automatically when EmbedLink kind text appended" ! trees.containsLink ^
//            "set containsVideo automatically when EmbedLink kind video appended" ! trees.containsVidLink ^
//            "set containsPic automatically when EmbedLink kind pic appended" ! trees.containsPicWhenEmbedPic ^
//            Step(tearDown()) ^
//        end
//
//    }
//
//    object trees {
//        import com.ansvia.graph.BlueprintsWrapper._
//
////        Config.database = TitanConfig("memory:titan")
////        Config.autoCloseWhenDbNotUsed = true
////        Config.init()
////
////        implicit val db = Global.database.useAndGet[TitanGraph]()
//
//        val u1 = genUser
//        val simplePost1 = SimplePost("hello 1")
//        simplePost1.moid = "test"
//        simplePost1.containsGreeting = true
//        simplePost1.containsLink = true
//        simplePost1.containsPic =true
//        simplePost1.containsVideoLink = true
//
//        var id = 0L
////        def init() {
////            db.getVertices.foreach(db.removeVertex)
////            db.getEdges.foreach(db.removeEdge)
////        }
////        def close() {
////            Global.database.done()
////        }
//        def saveToDataBase = {
//            val smpost = simplePost1.save().toCC[SimplePost]
//            id = smpost.get.getId
//            smpost.isDefined
//        }
//        def savedDataCorrectly = {
//            val simplepost = Post.getPostById(id).get.asInstanceOf[SimplePost]
//            simplepost.moid must beEqualTo("test")
//        }
//        def savedDataCorrectly1 = {
//            val simplepost = Post.getPostById(id).get.asInstanceOf[SimplePost]
//            simplepost.containsGreeting must beTrue
//        }
//        def savedDataCorrectly2 = {
//            val simplepost = Post.getPostById(id).get.asInstanceOf[SimplePost]
//            simplepost.containsLink must beTrue
//        }
//        def savedDataCorrectly3 = {
//            val simplepost = Post.getPostById(id).get.asInstanceOf[SimplePost]
//            simplepost.containsPic must beTrue
//        }
//        def savedDataCorrectly4 = {
//            val simplepost = Post.getPostById(id).get.asInstanceOf[SimplePost]
//            simplepost.containsVideoLink must beTrue
//        }
//        def savedDataCorrectly5 = {
//            val simplepost = Post.getPostById(id).get.asInstanceOf[SimplePost]
//            simplepost.content must beEqualTo("hello 1")
//        }
//        def savedDataCorrectly6 = {
//            val simplepost = Post.getPostById(id).get.asInstanceOf[SimplePost]
//            simplepost.kind must beEqualTo(PostKind.SIMPLE_POST)
//        }
//
//        def closePost = {
//            val sp = Post.getPostById(id).get.asInstanceOf[SimplePost]
//            val before = sp.isClosed
//            sp.setClosed(true, u1, genRandomString)
//            val sp2 = Post.getPostById(id).get.asInstanceOf[SimplePost]
//            (before must beFalse) and (sp.isClosed must beTrue) and (sp2.isClosed must beTrue)
//        }
//
//        def openPost = {
//            val sp = Post.getPostById(id).get.asInstanceOf[SimplePost]
//            sp.setClosed(false, u1, "")
//            val sp2 = Post.getPostById(id).get.asInstanceOf[SimplePost]
//            (sp.isClosed must beFalse) and (sp2.isClosed must beFalse)
//        }
//
//        def containsLink = {
//            simplePost1.containsLink = false
//            simplePost1.save()
//            db.commit()
//
//            val sp = Post.getPostById(id).get.asInstanceOf[SimplePost]
//
//            (sp.containsLink must beFalse) and
//                {
//                    EmbeddedLink.create("http://www.youtube.com/watch?v=DUBlUcek-EA","f-15 documentary",
//                        LinkKind.TEXT,"","desc",Some(sp))
//                    (sp.containsLink must beTrue) and
//                        {
//                            val _sp = Post.getPostById(id).get.asInstanceOf[SimplePost]
//                            _sp.containsLink must beTrue
//                        }
//                }
//        }
//
//
//        def containsVidLink = {
//            simplePost1.containsVideoLink = false
//            simplePost1.save()
//            db.commit()
//
//            val sp = Post.getPostById(id).get.asInstanceOf[SimplePost]
//
//            (sp.containsVideoLink must beFalse) and
//                {
//                    EmbeddedLink.create("http://www.youtube.com/watch?v=DUBlUcek-EA","f-15 documentary",
//                        LinkKind.VIDEO,"","desc",Some(sp))
//                    (sp.containsVideoLink must beTrue) and
//                        {
//                            val _sp = Post.getPostById(id).get.asInstanceOf[SimplePost]
//                            _sp.containsVideoLink must beTrue
//                        }
//                }
//        }
//
//
//        def containsPicWhenEmbedPic = {
//            simplePost1.containsPic = false
//            simplePost1.save()
//            db.commit()
//
//            val sp = Post.getPostById(id).get.asInstanceOf[SimplePost]
//
//            (sp.containsPic must beFalse) and
//                {
//                    EmbeddedLink.create("http://www.youtube.com/watch?v=DUBlUcek-EA","f-15 documentary",
//                        LinkKind.PIC,"","desc",Some(sp))
//                    (sp.containsPic must beTrue) and
//                        {
//                            val _sp = Post.getPostById(id).get.asInstanceOf[SimplePost]
//                            _sp.containsPic must beTrue
//                        }
//                }
//        }
//
//
//    }
//}
