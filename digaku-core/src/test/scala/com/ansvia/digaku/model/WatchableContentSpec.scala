// @TODO(robin): benerin ini setelah major refactor
///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import org.specs2.specification.{Step, Fragments}
//
///**
// * Author: ubai
// * Date: 4/29/14
// * Time: 5:39 PM
// *
// */
//class WatchableContentSpec extends Specification with DigakuTest {
//    def is: Fragments = {
//        "leaveable content should" ^
//            p ^
//            sequential ^
//            Step(cleanUp()) ^
//                "user yang unwatch/leave conversation tidak akan mendapakan notifikasi lagi" ! trees.leaveConversation ^
//                "user yang unwatch/leave conversation akan mendapatkan notifikasi lagi ketika dia response kembali" ! trees.unleaveConversation ^
//                "creator dan responder harusnya bisa unwatch content" ! trees.canLeaveContent ^
//            Step(tearDown()) ^
//            end
//    }
//
//
//
//    object trees {
//        val user1 = genUser
//        val user2 = genUser
//        val user3 = genUser
//        val user4 = genUser
//
//        val ch1 = genChannelWithOwner(user1)
//        ch1.addMembers(user2, user3, user4)
//
//        val post1 = Post.createSimple(user1, "ini adalah post 1", ch1)
//        post1.addResponse(user2, "aku rapopo")
//
//        def leaveConversation() = {
//            post1.unwatchContent(user2)
//
//            post1.addResponse(user1, "aku juga")
//
//            Thread.sleep(3000)
//
//            user2.getDialogue(0, 10).length must beEqualTo(0)
//
//        }
//
//        def unleaveConversation() = {
//
//            post1.addResponse(user2, "huasem")
//
//            post1.addResponse(user3, "abcd")
//            Thread.sleep(3000)
//            user2.getDialogue(0, 10).length > 0 must beTrue
//        }
//
//        def canLeaveContent() = {
//            val post3 = Post.createSimple(user3, "ini post 3", ch1)
//
//            post3.addResponse(user1, "iya tau")
//            post3.addResponse(user2, "ini juga response ke 2")
//            post3.unwatchContent(user2)
//
//            (post3.userCanUnwatch(user1) && post3.userCanUnwatch(user3) must beTrue) and
//                (post3.userCanUnwatch(user2) && post3.userCanUnwatch(user4) must beFalse)
//        }
//
//    }
//
//}
