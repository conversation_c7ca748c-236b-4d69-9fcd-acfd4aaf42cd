// @TODO(robin): benerin ini setelah major refactor

///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//
///**
// * Author: hani (<EMAIL>)
// *
// */
//class StickableSpec extends Specification with DigakuTest {
//    def is = {
//        "Stickable should" ^
//            p ^
//            sequential^
//            "set sticky a post" ! trees.setSticky ^
//            "get sticky state from a post" !trees.isSticked ^
//            end
//    }
//
//    object trees {
//        val u1 = genUser
//        val ch1 = genChannelWithOwner(u1)
//        val p1 = Post.createSimple(u1, "content of post", ch1)
//
//        val smallUrl1 = "http://image.com/image1.jpg"
//        val mediumUrl1 = "http://image.com/image2.jpg"
//        val originalUrl1 = "http://image.com/image3.jpg"
//        val pic1 = Picture.create(u1, "picture 1", ch1, smallUrl1, mediumUrl1, originalUrl1, true)
//
//        def setSticky = {
//            p1.setSticky(true)
//            p1.isSticked must beTrue
//        }
//
//        def isSticked = {
//            pic1.isSticked must beFalse
//        }
//    }
//
//}
