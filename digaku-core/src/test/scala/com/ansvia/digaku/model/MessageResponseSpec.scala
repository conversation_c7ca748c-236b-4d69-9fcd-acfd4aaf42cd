///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import org.specs2.specification.Step
//
///**
// * Author: nadir
// * Date: 4/26/13
// * Time: 5:31 PM
// *
// */
//class MessageResponseSpec extends Specification with DigakuTest {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    def is = {
//        sequential ^
//            "Private message model should" ^
//            p ^
////            Step(cleanUp()) ^
//            "saved to database" ! trees.saveToDataBase ^
//            "saved data read content message" ! trees.savedDataCorrectly1 ^
//            "saved data read creation time" ! trees.savedDataCorrectly2 ^
//            "get creator or participant message response" ! treesMethod.getCreatorOrParticipant ^
//            "get private message object" ! treesMethod.getPrivateMessageObject ^
////            Step(tearDown()) ^
//            end
//    }
//
//    object trees {
//        val date = System.currentTimeMillis()
//        val messageResponse1 = MessageResponse("message response test")
//        messageResponse1.creationTime = date
//
//
//        def saveToDataBase = {
//            messageResponse1.save().toCC[MessageResponse].isDefined must beTrue
//        }
//
//        def savedDataCorrectly1 = {
//            messageResponse1.content must beEqualTo("message response test")
//        }
//
//        def savedDataCorrectly2 = {
//            messageResponse1.creationTime must beEqualTo(date)
//        }
//    }
//
//    object treesMethod {
//
//        val user1 = genUser
//        val user2 = genUser
//        val user3 = genUser
//
//        user1.support(user2)
//        user2.support(user1)
//        user1.support(user3)
//        user3.support(user1)
//
//        val pM1 = PrivateMessage.create(user1, genRandomString, "send to user2, user3", Array(user2, user3))
//        val messageResponse1 = pM1.addResponse(user2, "user2 reply")
//
//        def getCreatorOrParticipant = {
//            messageResponse1.creator must beEqualTo(user2)
//        }
//
//        def getPrivateMessageObject = {
//            messageResponse1.getPrivateMessageObject.get must beEqualTo(pM1)
//        }
//
//    }
//
//}
