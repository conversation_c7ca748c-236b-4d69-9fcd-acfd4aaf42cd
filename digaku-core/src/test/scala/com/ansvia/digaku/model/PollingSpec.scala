/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.Types.IDType
import org.specs2.Specification

/**
 * Author: nadir
 *
 */
class PollingSpec extends Specification with DigakuTest {
    def is =
        sequential ^
        "Polling should" ^
//            Step(cleanUp()) ^
        p ^
            sequential ^
            "add polling choice" ! trees.addAndGetChoice ^
            "add and get chooser" ! trees.addAndGetChoicer ^
            "remove choicer by user model" ! trees.removeChoicerByModel ^
            "remove choicer by user id" ! trees.removeChoicerById ^
            "remove choice by model choice" ! trees.removeChoiceByModel ^
            "remove choice by id choice" ! trees.removeChoiceById ^
            "clear choice" ! trees.clearChoice ^
            "be able to embed to HasEmbedded object" ! trees.embedToArticle ^
            "get poll parent object" ! trees.getPollParent ^
            "removed from HasEmbedded object" ! trees.removeFromHasEmbedded ^
//            Step(tearDown()) ^
        end

    object trees {

        val u1 = genUser
        val u2 = genUser

        val poll1 = Polling.create("Polling 1!", "")
        val pollChoice1 = poll1.addChoice("Choice 1", "").reload()
        val pollChoice2 = poll1.addChoice("Choice 2", "'").reload()
        val pollChoice3 = poll1.addChoice("Choice 3", "'").reload()
        val pollChoice4 = poll1.addChoice("Choice 4", "").reload()

        def addAndGetChoice = {
            val pollChoice5 = poll1.addChoice("Choice 5")
            (poll1.getChoices.toList must be contain(pollChoice5)) and
                (poll1.getChoices.toList must be contain(pollChoice1,pollChoice2,pollChoice3, pollChoice4))
        }

        def addAndGetChoicer = {
            u1.reload()
            pollChoice1.reload()

            pollChoice1.addChooser(u1)

            pollChoice1.reload()
            u2.reload()

            pollChoice1.addChooser(u2)

            pollChoice1.reload()

            pollChoice1.getChooser.toList must be contain(u1)
        }


        def removeChoicerByModel = {
            pollChoice1.removeChooser(u1)
            pollChoice1.getChooser.toList must not contain(u1)
        }

        def removeChoicerById = {
            pollChoice1.removeChooserById(u2.getId)
            (pollChoice1.getChooser.toList must not contain(u2)) and (pollChoice1.chooserCount must beEqualTo(1))
        }

        def removeChoiceByModel = {
            poll1.removeChoice(pollChoice1)
            poll1.getChoices.toList must not contain(pollChoice1)
        }

        def removeChoiceById = {
            poll1.removeChoiceById(pollChoice2.getId)
            poll1.getChoices.toList must not contain(pollChoice2)
        }

        def clearChoice = {
            poll1.clearChoices()
            poll1.getChoices.isEmpty must beTrue
        }

        val article = Article.create(u1, "some article", "hello", "ada user", u1)

        def embedToArticle = {
            article.reload()
            poll1.reload()

            article.addEmbeddedObject(poll1)
            article.getEmbeddedObjects.toList must be contain(poll1)
        }

        def getPollParent = {
            poll1.reload()
            (poll1.getEmbedParentObject[HasEmbeddedObject[IDType]].isDefined must beTrue) and
                (poll1.getEmbedParentObject[HasEmbeddedObject[IDType]] must beEqualTo(Some(article)))
        }

        def removeFromHasEmbedded = {
            article.removeEmbeddedObject(poll1)
            article.getEmbeddedObjects.toList must be not contain(poll1)
        }

    }
}
