/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

/**
 * Author: robin
 * Date: 3/28/14
 * Time: 10:49 PM
 *
 */

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import com.ansvia.digaku.exc.NotSupportedException
import com.ansvia.digaku.Types.{GraphType, IDType}
import com.ansvia.digaku.dao.DaoBase

case class DummyModel() extends BaseModel[IDType]
object DummyModel extends DaoBase[GraphType,DummyModel]{
    val ROOT_VERTEX_CLASS: String = getClass.getCanonicalName + "RootVertex"
}

class BaseModelSpec extends Specification with DigakuTest {

     def is = "base model should" ^
         sequential ^
         "cannot delete directly" ^
         "can delete from Dao object" ^
     end

     object trees {
         import com.ansvia.graph.BlueprintsWrapper._

         def cantDeleteDirect = {
             val d = DummyModel().save().toCC[DummyModel].get

             d.delete() must throwA[NotSupportedException]
         }

         def canDeleteFromDao = {
             val d = DummyModel().save().toCC[DummyModel].get
             DummyModel.delete(d)
             DummyModel.getListLeft(Some(d.getId),None,10).toList must beEmpty
         }

     }

 }

