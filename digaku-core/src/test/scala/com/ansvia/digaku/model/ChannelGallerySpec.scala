///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
///**
// * Author: robin
// *
// */
//
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.exc.AlreadyExistsException
//import org.specs2.specification.Step
//
//
//class ChannelGallerySpec extends Specification with DigakuTest {
//     def is = "Group gallery should" ^
//        sequential ^
//        p ^
//         "set group" ! trees.setChannel ^
//         "add collection" ! trees.testAddCollection ^
//         "remove collection" ! trees.testRemoveCollection ^
//         "detach group" ! trees.detachChannel ^
//         "group generate gallery" ! trees.generateGallery ^
//         "group get album list" ! trees.getAlbumList ^
//         "group get album count" ! trees.getAlbumCount ^
//         "group get gallery" ! trees.getGalleryFromChannel ^
//         "group create album" ! trees.createAlbumFromChannel ^
//        p ^
//         "Gallery collection should" ^
//        p ^
//        Step(trees.step1()) ^
//         "get gallery" ! trees.getGallery ^
//         "get group" ! trees.getChannel ^
//         "add media" ! trees.testAddMedia ^
//         "get media count" ! trees.getMediaCount ^
//         "get picture stream" ! trees.getPictureStream ^
//         "move media to other collection" ! trees.moveToOtherCollection ^
//         "remove media from collection" ! trees.removeMedia ^
//        p ^
//         "Gallery album should" ^
//        p ^
//         "create album, tags normalized to max 5 words" ! treesAlbum.createTagsNorm ^
//         "get gallery" ! treesAlbum.getGallery ^
//         "get group" ! treesAlbum.getChannel ^
//         "add photo" ! treesAlbum.testAddMedia ^
//         "get photo stream" ! treesAlbum.getPictureStream ^
//         "move photo to other collection" ! treesAlbum.moveToOtherCollection ^
//         "remove photo from collection" ! treesAlbum.removeMedia ^
//         "delete album" ! treesAlbum.removeAlbum ^
//        end
//
//     object trees {
//
//         val u1 = genUser
//         val ch1 = genChannelWithOwner(u1)
//         val ch2 = genChannelWithOwner(u1)
//         val gal1 = ChannelGallery.create("photo gallery", ch1)
//         val collection1 = GalleryCollection.create("collection satu", "ini collection satu", "")
//         val collection2 = GalleryCollection.create("collection dua", "ini collection dua", "")
//         val pic = Picture.create(u1, "judul", ch2)
//
////         val gal2 = ChannelGallery.create(ch1)
//
//         def setChannel = {
//             gal1.setChannel(ch2) must throwAn[AlreadyExistsException]
//         }
//
//         def testAddCollection = {
//             gal1.addCollection(collection1)
//             gal1.getCollections(0, 1).toList must contain(collection1).only
//         }
//
//         def testRemoveCollection = {
//             gal1.removeCollection(collection1)
//
//             gal1.getCollections(0, 1).length must beEqualTo(0)
//         }
//
//         def detachChannel = {
//             gal1.detachChannel(ch1)
//             ch1.hasGallery("photo") must beFalse
//         }
//
//         def generateGallery = {
////             (ch1.generateGallery() must throwAn[AlreadyExistsException]) and
//                 (ch2.createGallery("photo") must anInstanceOf[ChannelGallery]) and
//                 (ch2.hasGallery("photo") must beTrue)
//         }
//
//
//         // Step
//         def step1(){
//             gal1.setChannel(ch1)
//             gal1.addCollection(collection1)
//         }
//
//
//         def getGallery = {
//             collection1.gallery must_== Some(gal1)
//         }
//
//         def getChannel = {
//             collection1.group must_== Some(ch1)
//         }
//
//         def testAddMedia = {
//             collection1.put(pic)
//             true must beTrue
//         }
//
//         def getMediaCount = {
//             collection1.getMediaCount must_== 1
//         }
//
//
//         def getPictureStream = {
//             collection1.getStream(-1, 0, 5).toList must contain(pic)
//         }
//
//         def moveToOtherCollection = {
//             collection1.moveTo(pic, collection2)
//             (collection1.hasMedia(pic) must beFalse) and
//                 (collection2.hasMedia(pic) must beTrue) and
//                 (collection2.getStream(-1, 0, 10).toList must contain(pic))
//         }
//
//         def removeMedia = {
//             collection2.remove(pic)
//             (collection2.hasMedia(pic) must beFalse) and
//                 (collection2.getStream(-1, 0, 10).toList must be not contain(pic))
//         }
//
//         def getAlbumList = {
//             val gal = ch1.createGallery("galxx")
//             val album = Album.create("album xxx", "album xxx", "", gal)
//             ch1.getAlbums(0, 10).toList.length must be greaterThan(0)
//         }
//
//         def getAlbumCount = {
//             ch1.getAlbumCount must be greaterThan(0)
//         }
//
//         def getGalleryFromChannel = {
//             ch1.createGallery("photo")
//             ch1.getGallery("photo") must not equalTo(None)
//         }
//
//         def createAlbumFromChannel = {
//             (ch1.createAlbum("my vacation", "my vacation desc", "") must beAnInstanceOf[Album]) and
//                 (ch1.createAlbum("my vacation", "my vacation desc", "") must throwAn[AlreadyExistsException]) and
//                 (ch1.createAlbum("my vacation 2", "my vacation desc", "") must beAnInstanceOf[Album]) and
//                 (ch2.createAlbum("my vacation", "my vacation desc", "") must beAnInstanceOf[Album]) and
//                 (ch2.createAlbum("my vacation", "my vacation desc", "") must throwAn[AlreadyExistsException])
//         }
//
//     }
//
//    object treesAlbum {
//        val u1 = genUser
//        val ch1 = genChannelWithOwner(u1)
//        val ch2 = genChannelWithOwner(u1)
//        val gal1 = ChannelGallery.create("photo gallery", ch1)
//        val album1 = Album.create("album satu", "ini album satu", "", gal1)
//        val album2 = Album.create("album dua", "ini album dua", "", gal1)
//        val pic = Picture.create(u1, "judul", ch2)
//
//        def createTagsNorm = {
//            val album3 = Album.create("album tiga", "ini album tiga", "abc,def,ghi,jkl,mno,pqr,stu", gal1)
//            (album3.tags.length must beEqualTo(5)) and
//                (album3.tags.toList must contain("abc","jkl","ghi")) and
//                (album3.tags.toList must not contain("stu","pqr"))
//        }
//
//        def getGallery = {
//            album1.gallery must_== Some(gal1)
//        }
//
//        def getChannel = {
//            album1.group must_== Some(ch1)
//        }
//
//        def testAddMedia = {
//            album1.put(pic)
//            true must beTrue
//        }
//
//        def getPictureStream = {
//            album1.getStream(-1, 0, 5).toList must contain(pic)
//        }
//
//        def moveToOtherCollection = {
//            album1.moveTo(pic, album2)
//            (album1.hasMedia(pic) must beFalse) and
//                (album2.hasMedia(pic) must beTrue) and
//                (album2.getStream(-1, 0, 10).toList must contain(pic))
//        }
//
//        def removeMedia = {
//            album2.remove(pic)
//            (album2.hasMedia(pic) must beFalse) and
//                (album2.getStream(-1, 0, 10).toList must be not contain(pic))
//        }
//
//        def removeAlbum = {
//            val albumCount = ch1.getAlbumCount
//            Album.remove(album2.getId, gal1)
//
//            ch1.getAlbumCount must beEqualTo(albumCount-1)
//        }
//
//    }
//
// }
//
