///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
///**
// * Author: robin
// * Date: 12/19/13
// * Time: 2:53 PM
// *
// */
//
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.utils.DateUtils
//import java.util.Calendar
//
//
//class SponsorPostSpec extends Specification with DigakuTest {
//     def is = "SponsorPost should" ^
//         sequential ^
//         "create" ! trees.create ^
//         "get list" ! trees.getList ^
//     end
//
//     object trees {
//
//         var sp:SponsorPost = _
//
//         def getTimeRange = {
//             val st = Digaku.engine.dateUtils.now
//             val cal = Calendar.getInstance()
//             cal.setTime(st)
//             cal.add(Calendar.DATE, 30)
//             val et = cal.getTime
//             (st, et)
//         }
//
//
//         def create = {
//             val (st, et) = getTimeRange
//             sp = SponsorPost.create("sponsor honda","my first sponsor!",
//                 "goto-url||http://www.mindtalk.com/ads", st, et, true)
//             true must beTrue
//         }
//
//         def getList = {
//             SponsorPost.getListRight(None, None, 10).toList must contain(sp)
//         }
//
//
//     }
//
// }
//
