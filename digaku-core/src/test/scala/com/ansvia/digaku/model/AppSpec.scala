/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import com.ansvia.digaku.validator.AppValidator

/**
 * Author: robin
 * Date: 8/28/13
 * Time: 1:54 PM
 *
 */
class AppSpec extends Specification with DigakuTest {

    def is = "App" ^
        sequential ^
        p ^
            "name validator harusnya" ^
            p ^
            "menganggap nama `aplikasi` sebagai valid" ! (AppValidator.validName("aplikasi") must beTrue) ^
            "menganggap nama `aplikasi1` sebagai valid" ! (AppValidator.validName("aplikasi1") must beTrue) ^
            "menganggap nama `aplikasi 2` sebagai valid" ! (AppValidator.validName("aplikasi 2") must beTrue) ^
            "menganggap nama `aplikasi @2` sebagai tidak valid" ! (AppValidator.validName("aplikasi @2") must beFalse) ^
            "menganggap nama `3keren` sebagai valid" ! (AppValidator.validName("3keren") must beTrue) ^
        p ^
            "Dao harusnya" ^
          p ^
            "bisa create app" ! trees.create ^
            "created app harus punya dev access token by default" ! trees.hasDevAccessToken ^
            "bisa get app list" ! trees.getList ^
            "clientId-nya sama dengan vertex id" ! trees.clientIdSameId ^
            "client secret-nya unik" ! trees.uniqueSecretKey ^
            "bisa get by name" ! trees.getByName ^
            "bisa get by dev access token" ! trees.getByAccessToken ^
        end



    object trees {
        import com.ansvia.graph.BlueprintsWrapper._

        // clean up db
        App.rootVertex.pipe.out(App.rootVertexLabel).remove()
        db.commit()

        val user = genUser
        val app = App.create("app1", "ini aplikasi #1", "0.0.1", "http://digakusite.com/callback", user)

        def create = {
            println("app.clientSecret: " + app.clientSecret)
            app must anInstanceOf[App]
        }

        def hasDevAccessToken = {
            app.devAccessToken mustNotEqual("123")
        }

        def getList = {
            App.getListRight(None, None, 5).toList must be contain(app) only
        }

        def clientIdSameId = {
            app.getId must beEqualTo(app.clientId)
        }

        def uniqueSecretKey = {
            val app2 = App.create("app2", "ini aplikasi #2", "0.0.1", "http://digakusite.com/callback", user)
            app.clientSecret must be not equalTo(app2.clientSecret)
        }

        def getByName = App.getByName("app1") must beEqualTo(Some(app))

        def getByAccessToken = {
            App.getByDevAccessToken(app.devAccessToken) must_==Some(app)
        }


    }
}
