///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import scala.collection.JavaConversions._
//import com.ansvia.digaku.DigakuTest
//
///**
// * Author: surya
// * Date: 1/17/13
// * Time: 5:44 PM
// *
// */
//class QuestionSpec extends Specification with DigakuTest  {
//    def is = {
//        sequential ^
//        "Question should" ^
//        p ^
////            Step(cleanUp()) ^
//            "save to database" ! trees.saveToDatabase ^
//            "saved data read moid correctly" ! trees.saveMoid ^
//            "saved data read containsGreeting correctly" ! trees.saveContainsGreeting ^
//            "saved data read containsLink correctly" ! trees.saveConteinsLink ^
//            "saved data read containsPic correctly" ! trees.saveCOntaintsPic ^
//            "saved data read containsVideoLinks correctly" ! trees.saveContaintsVideoLink ^
//            "saved data read title correctly" ! trees.saveTitle ^
//            "saved data read content correctly " ! trees.saveContent ^
//            "saved data read kind correctly" ! trees.saveKind ^
////            Step(tearDown()) ^
//        end
//    }
//
//    object trees {
//
//        import com.ansvia.graph.BlueprintsWrapper._
////
////        Config.dbConf = TitanConfig("memory:titan")
////        Config.autoCloseWhenDbNotUsed = true
////        Config.init()
//
////        implicit val db = Global.database.useAndGet[TitanGraph]()
//
//        val question = Question("Title", "Content question")
//        question.moid = "ask"
//        question.containsGreeting = true
//        question.containsLink = true
//        question.containsPic = true
//        question.containsVideoLink = true
//
//        var id = 0L
//        def init() {
//            db.getVertices.foreach(db.removeVertex)
//            db.getEdges.foreach(db.removeEdge)
//        }
//
//        def close() {
////            Global.database.done()
//        }
//
//        def saveToDatabase = {
//            val ask = question.save().toCC[Question]
//            id = ask.get.getId
//            ask.isDefined
//        }
//
//        def saveMoid = {
//            val ask1 = Post.getPostById(id).get.asInstanceOf[Question]
//            ask1.moid must beEqualTo("ask")
//        }
//
//        def saveContainsGreeting = {
//            val ask2 = Post.getPostById(id).get.asInstanceOf[Question]
//            ask2.containsGreeting must beTrue
//        }
//
//        def saveConteinsLink = {
//            val ask3 = Post.getPostById(id).get.asInstanceOf[Question]
//            ask3.containsLink must beTrue
//        }
//
//        def saveCOntaintsPic = {
//            val ask4 = Post.getPostById(id).get.asInstanceOf[Question]
//            ask4.containsPic must beTrue
//        }
//
//        def saveContaintsVideoLink = {
//            val ask5 = Post.getPostById(id).get.asInstanceOf[Question]
//            ask5.containsVideoLink must beTrue
//        }
//
//        def saveTitle = {
//            val title = Post.getPostById(id).get.asInstanceOf[Question]
//            title.title must beEqualTo("Title")
//        }
//
//        def saveContent = {
//            val content = Post.getPostById(id).get.asInstanceOf[Question]
//            content.content must beEqualTo("Content question")
//        }
//
//        def saveKind = {
//            val kind = Post.getPostById(id).get.asInstanceOf[Question]
//            kind.kind must beEqualTo(PostKind.QUESTION)
//        }
//    }
//}
