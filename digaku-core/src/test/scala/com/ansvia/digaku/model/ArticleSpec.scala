/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.exc.PermissionDeniedException
import org.specs2.Specification
import org.specs2.specification.Step
import com.ansvia.digaku.utils.ForumSettings._

/**
 * Author: nadir
 *
 */
class ArticleSpec extends Specification with DigakuTest {
    def is = {
        sequential ^
        "Article model should" ^
        p ^
            "save to database" ! trees.saveToDatabase ^
            "saved data read title correctly" ! trees.saveDataCorrectly ^
            "saved data read content correctly" ! trees.saveDataCorrectly1 ^
            "saved data read thumbUrl" ! trees.saveDataCorrectly2 ^
            "saved data read editReason correctly" ! trees.saveDataCorrectly3 ^
            "saved data read moid correctly" ! trees.saveDataCorrectly4 ^
            "saved data read containsGreeting correctly" ! trees.saveDataCorrectly5 ^
            "saved data read containsLink correctly" ! trees.saveDataCorrectly6 ^
            "saved data read containsPic correctly" ! trees.saveDataCorrectly7 ^
            "saved data read containsVideoLink" ! trees.saveDataCorrectly8 ^
            "using mark" ! trees.usingMark ^
            "using mark (setMark)" ! trees.usingMarkSetMark ^
            "using mark (clearMark)" ! trees.usingMarkClearMark ^
            "able to attach files" ! trees.attachFiles ^
            "able to remove attached files" ! trees.removeAttachedFiles ^
            "able to remove attached files via FileAttachable" ! trees.removeAttachedFiles2 ^
            "cannot add reply on closed thread" ! trees.cannotAddReplyPostOnClosedThread ^
            "cannot create thread on a forum without feature article" ! trees.cannotCreateThreadOnForumWithoutFeature ^
            "can create thread on a forum with feature article" ! trees.canCreateThreadOnForumWithFeature ^
            "user non member cannot add a reply" ! trees.endUserNonMemberCannotCreateAReply ^
            "article creator get 10 exp points" ! trees.articleCreatorGetPoints ^
            "get rating average" ! trees.getRatingAverage ^
//            "can update and creating revision history" ! trees.updateCreateRevHistory ^
//            "get latest revision" ! trees.getLatestRevision ^
//            "move to another group soft" ! trees.moveSoft ^
//            "move to another group hard" ! trees.moveHard ^
        end

    }

    object trees {
        import com.ansvia.graph.BlueprintsWrapper._

//        Config.database = TitanConfig("memory:titan")
//        Config.autoCloseWhenDbNotUsed = true
//        Config.init()
//
//        implicit val db = Global.database.useAndGet[TitanGraph]()

        val artc = Article("article title", "this Article content")
        artc.thumbUrl = "http://test.com"
        artc.editReason = "test"
        artc.moid = "article moid"
        artc.containsGreeting = true
        artc.containsLink = true
        artc.containsPic = true
        artc.containsVideoLink = true

        var id = 0L
//        def init() {
//            db.getVertices.foreach(db.removeVertex)
//            db.getEdges.foreach(db.removeEdge)
//        }
//        def close() {
//            Global.database.done()
//        }

        def saveToDatabase = {
            val art = artc.save().toCC[Article]
            id = art.get.getId
            art.isDefined
        }
        def saveDataCorrectly = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            article.title must beEqualTo("article title")
        }
        def saveDataCorrectly1 = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            article.content must beEqualTo("this Article content")
        }
        def saveDataCorrectly2 = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            article.thumbUrl must beEqualTo("http://test.com")
        }
        def saveDataCorrectly3 = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            article.editReason must beEqualTo("test")
        }
        def saveDataCorrectly4 = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            article.moid must beEqualTo("article moid")
        }
        def saveDataCorrectly5 = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            article.containsGreeting must beTrue
        }
        def saveDataCorrectly6 = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            article.containsLink must beTrue
        }
        def saveDataCorrectly7 = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            article.containsPic must beTrue
        }
        def saveDataCorrectly8 = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            article.containsVideoLink must beTrue
        }
        def usingMark = {

//            val ch = Group.create(genChannelName, genChannelName + " desc")

            val pm = PostMark("hero", "#cacaca").save().toCC[PostMark].get


            val article = Post.getPostById(id).get.asInstanceOf[Article]

            article --> Label.POST_MARK --> pm

            (article.mark.isDefined must beTrue) and
            (article.mark.get.title must beEqualTo("hero"))

        }

        def usingMarkSetMark = {
            val pm = PostMark("hero2", "#cacaca").save().toCC[PostMark].get

            val u1 = genUser
            val ch1 = genForumWithOwner(u1)

            val article = Article.create(u1,"title","content", "", ch1)

            article.setMark(pm)

            (article.mark.isDefined must beTrue) and
                (article.mark.get.title must beEqualTo("hero2"))

        }

        def usingMarkClearMark = {
            val pm = PostMark("hero2", "#cacaca").save().toCC[PostMark].get

            val u1 = genUser
            val ch1 = genForumWithOwner(u1)

            val article = Article.create(u1,"title","content", "", ch1)

            article.setMark(pm)

            val before = (article.mark.isDefined must beTrue) and
                (article.mark.get.title must beEqualTo("hero2"))

            article.clearMark()

            val after = (article.reload().mark must beEqualTo(None))

            before and after
        }



        def attachFiles = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            val file1 = EmbeddedFile("one.zip","http://file.com/one.zip","123",100).save().toCC[EmbeddedFile].get
            val file2 = EmbeddedFile("two.zip","http://file.com/two.zip","123",100).save().toCC[EmbeddedFile].get

            article.addEmbeddedObject(file1, true)
            article.addEmbeddedObject(file2, true)
            article.save()
            db.commit()

            val article2 = Post.getPostById(id).get.asInstanceOf[Article]
            (article2.attachedFiles.toList must be contain(file1, file2) only) and
                (article2.embeddedObjectCount must beEqualTo(2))
        }

        def removeAttachedFiles = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            val embO = article.attachedFiles.find(_.name == "one.zip").head
            article.removeEmbeddedObject(embO)
            (article.attachedFiles.map(_.name).toList must not contain("one.zip")) and
                (article.embeddedObjectCount must beEqualTo(1))
        }

        def removeAttachedFiles2 = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            val embO = article.attachedFiles.find(_.name == "two.zip").head
            article.removeAttachedFile(embO)
            (article.embeddedObjectCount must beEqualTo(0))
        }

        def cannotAddReplyPostOnClosedThread = {
            val article = Post.getPostById(id).get.asInstanceOf[Article]
            val userAdm1 = genUser
            userAdm1.setRole(UserRole.ADMIN)
            article.setClosed(true, userAdm1, "should not continue discussion")

            val article2 = Post.getPostById(id).get.asInstanceOf[Article]
            article2.addResponse(userAdm1, "my response") must throwAn[PermissionDeniedException]
        }

        def cannotCreateThreadOnForumWithoutFeature = {
            val userMod1 = genUser
            val forum1 = genForumWithOwner(userMod1)
            val subForum1 = genForumWithOwner(userMod1)
            val subForum2 = genForumWithOwner(userMod1)
            forum1.addSubForum(subForum1)
            subForum1.addFeatures(ForumFeatures.ARTICLE)
            forum1.addSubForum(subForum2)

            val article = Article.create(userMod1,"title","content", "", subForum1)

            article.setMoved(MovedType.SOFT, MovedRole.ADMIN_CHANNEL, userMod1, forum1.getId, "alasannya") must throwAn[PermissionDeniedException]
        }

        def canCreateThreadOnForumWithFeature = {
            val userMod1 = genUser
            val forum1 = genForumWithOwner(userMod1)
            val subForum1 = genForumWithOwner(userMod1)
            val subForum2 = genForumWithOwner(userMod1)
            forum1.addSubForum(subForum1)
            subForum1.addFeatures(ForumFeatures.ARTICLE)
            forum1.addFeatures(ForumFeatures.ARTICLE)
            forum1.addSubForum(subForum2)

            val article = Article.create(userMod1,"title","content", "", subForum1)

            article.setMoved(MovedType.SOFT, MovedRole.ADMIN_CHANNEL, userMod1, forum1.getId, "alasannya") must_!= throwAn[PermissionDeniedException]
        }

        def endUserNonMemberCannotCreateAReply = {
            val u1 = genUser
            val ch1 = genForumWithOwner(u1)
            ch1.setPrivate(true)
            val article = Article.create(u1,"title","content", "", ch1)

            val nonMember = genUser

            article.addResponse(nonMember, "reply reply") must throwAn[PermissionDeniedException]
        }

        def articleCreatorGetPoints = {
            val u1 = genUser
            val initExp = u1.getExperience
            val ch1 = genForumWithOwner(u1)
            ch1.setPrivate(true)
            val article = Article.create(u1,"title","content", "", ch1)
            val gotExp = u1.getExperience - initExp
            article.setDeleted(DeletedRole.POST_CREATOR, u1, "delete yuk")
            val expNow = u1.getExperience

            (10 == gotExp) and (expNow == initExp)
        }

        def getRatingAverage = {
            val u5 = genUser
            val u6 = genUser
            val u7 = genUser
            val ch5 = genForumWithOwner(u7).addMembers(u5, u6)
            val thread = Article.create(u5, "article title 1", "this Article content 1", "", ch5)

            thread.addLikeWithRate(u5, 5)
            thread.addLikeWithRate(u6, 1)
            thread.addLikeWithRate(u7, 3)

            // nilai rata-rata didapat dari total rate dibagi total like
            // diambil satu angka belakang koma
            val average = 9.toFloat / 3 // total rate = 9 dibagi total like = 3

            thread.getRatingAverage must beEqualTo("%.1f".format(average).toDouble)
        }


//        def updateCreateRevHistory = {
//
//            val u = genUser
//
//            val article = Post.getPostById(id).get.asInstanceOf[Article]
//            val x1 = article.update("update pertama", "isinya berubah #1", "isinya berubah #1", "update pertama", u, "anstile")
//            Thread.sleep(50)
//            article.reload()
//            val x2 = article.update("update kedua", "isinya berubah #2", "isinya berubah #2", "update kedua", u, "anstile")
//
//            println(x1)
//            println(x2.diffToHtml(x1))
//
//            article.reload()
//
//            (x1.title must beEqualTo("update pertama")) and
//                (x2.title must beEqualTo("update kedua")) and
//                (article.title must beEqualTo("update kedua")) and
//                (article.content must beEqualTo("isinya berubah #2")) and
//                (article.title must beEqualTo("update kedua")) and
//                (article.revision must beEqualTo(2)) and
//                (x1.revision must beEqualTo(1)) and
//                (x2.revision must beEqualTo(2)) and
//                (article.getRevisionHistories(0, 10).toList must contain(x2, x1).inOrder.only)
//        }


//        def getLatestRevision = {
//            val article = Post.getPostById(id).get.asInstanceOf[Article]
//            article.latestRevision must beEqualTo(2)
//        }

//
//        def moveSoft  = {
//            val owner = genUser
//            val ch1 = genForumWithOwner(owner)
//            val ch2 = genForumWithOwner(owner)
//            val article2 = Article.create(owner, "test title", "content", "test", ch1)
//            article2.setMoved(1, "admin-group", owner, ch2.getName)
//            Thread.sleep(3000)
//
//            (article2.isMoved must beTrue) and
//                (article2.movedChannelOrigin must beEqualTo(ch1.getName)) and
//                (article2.movedType must beEqualTo(1)) and
//                (ch1.getStream[Article](0, 10).toList must not contain(article2)) and
//                (ch2.getStream[Article](0, 10).toList must contain(article2)) and
//                (article2.reload().origin must beEqualTo(ch2))
//        }
//
//        def moveHard = {
//            val owner = genUser
//            val ch1 = genForumWithOwner(owner)
//            val ch2 = genForumWithOwner(owner)
//            val article = Article.create(owner, "test title 2", "content 22", "test2", ch2)
//            article.setMoved( 2, "admin-group", owner, ch1.getName)
//            Thread.sleep(3000)
//
//            (article.isMoved must beTrue) and
//                (article.movedChannelOrigin must beEqualTo(ch2.getName)) and
//                (article.movedType must beEqualTo(2)) and
//                (ch1.getStream[Article](0, 10).toList must  contain(article).inOrder) and
//                (ch2.getStream[Article](0, 10).toList must not contain(article) inOrder) and
//                (article.reload().origin must beEqualTo(ch1))
//        }


    }
}
