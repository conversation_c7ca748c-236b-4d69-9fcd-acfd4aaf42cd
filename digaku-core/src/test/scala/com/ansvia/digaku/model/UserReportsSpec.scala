// @TODO(robin): benerin ini setelah major refactor
///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import org.specs2.specification.Fragments
//import java.util.{Calendar, Date}
//
///**
// * Author: nadir
// * Date: 12/9/13
// * Time: 1:32 PM
// *
// */
//class UserReportsSpec extends Specification with DigakuTest {
//
//
//
//    def is: Fragments = {
//        "UserReport role should" ^
//            p ^
//            sequential ^
//            "kalkulasi group owned count untuk user" ! trees.channelCount ^
//            "kalkulasi tujuh post dalam satu minggu untuk user" ! trees.calTeenPostADayOnWeek ^
//            "kalkulasi artikel creation count untuk user" ! trees.calArticleCreationCount ^
//            "kalkulasi post unique group count untuk user" ! trees.calPostUniqueChannel ^
//            "kalkulasi user post count untuk user" ! trees.calUserPostCount ^
//            "kalkulasi cupid bomb count untuk user" ! trees.calCupidBombCount ^
//            "kalkulasi super post untuk user" ! trees.calUserSuperpost ^
//            end
//    }
//
//    object trees {
//
//        import com.ansvia.graph.BlueprintsWrapper._
//        import Label._
//        import scala.collection.JavaConversions._
//        import com.ansvia.digaku.Types._
//
//        val user1 = genUser
//
//        val channel1 = genChannelWithOwner(user1)
//
//        val channel2 = genChannelWithOwner(user1)
//
//        val channel3 = genChannelWithOwner(user1)
//
//        def channelCount = {
//            UserReports.calcChannelOwnerCount(user1.getVertex) must beEqualTo(3)
//        }
//
//        def calTeenPostADayOnWeek = {
//            for (m <- 0 to 7) {
//                for (n <- 0 to 9) {
//                    val art = Article.create(user1, "post " + n, "content " + n, "tags", channel1)
//                    val creationTime = art.creationTime
//                    val creationDate = new Date(creationTime)
//                    val cal = Calendar.getInstance()
//                    cal.setTime(creationDate)
//                    // substract 7 days
//                    cal.add(Calendar.DATE, -m)
//
//                    val newDate = cal.getTime.getTime
//
//                    val publishE = art.getVertex.pipe.inE(PUBLISH_CONTENT).as("ed").outV()
//                        .has("kind",CollectionVertex.Kind.PUBLISH_CONTENT).in(COLLECTION_VERTEX).has("id", user1.getId)
//                        .back("ed")
//                        .asInstanceOf[GremPipeEdge]
//                        .iterator()
//                        .toList
//                        .head
//
//                    art.getVertex.setProperty("creationTime", newDate)
//                    publishE.setProperty("timeOrder", newDate)
//                    db.commit()
//                }
//            }
//
//            UserReports.calcTeenPostADayOnWeek(user1.getVertex) must beTrue
//        }
//
//        def calArticleCreationCount = {
//            val user2 = genUser
//            val channel4 = genChannelWithOwner(user2)
//            Article.create(user2, "art1 ", "art1 content", "tags", channel4)
//            Article.create(user2, "art2 ", "art2 content", "tags", channel4)
//            Article.create(user2, "art3 ", "art3 content", "tags", channel4)
//            Post.createSimple(user2, "simple 1", user2)
//            Post.createSimple(user2, "simple 2", user2)
//
//            UserReports.calcArticleCreationCount(user2.reload().getVertex) must beEqualTo(3)
//        }
//
//        def calPostUniqueChannel = {
//            val user3 = genUser
//
//            val channel5 = genChannelWithOwner(user3); user3.reload()
//            val channel6 = genChannelWithOwner(user3); user3.reload()
//            val channel7 = genChannelWithOwner(user3); user3.reload()
//
//            for (n <- 0 to 9) {
//                Article.create(user3, "post " + n, "content " + n, "tags", channel5); user3.reload()
//                Article.create(user3, "post " + n, "content " + n, "tags", channel6); user3.reload()
//                Article.create(user3, "post " + n, "content " + n, "tags", channel7); user3.reload()
//            }
//
//            db.commit()
//            user3.reload()
//
//            UserReports.calcPostUniqueChannel(user3.getVertex) must beEqualTo(3)
//
//        }
//
//        def calUserPostCount = {
//            val user4 = genUser
//            val channel4 = genChannelWithOwner(user4)
//            Article.create(user4, "art1 ", "art1 content", "tags", channel4)
//            Article.create(user4, "art2 ", "art2 content", "tags", channel4)
//            Article.create(user4, "art3 ", "art3 content", "tags", channel4)
//            Post.createSimple(user4, "simple 1", user4)
//            Post.createSimple(user4, "simple 2", user4)
//
//            UserReports.calcUserPostCount(user4.getVertex) must beEqualTo(5)
//        }
//
//        def calCupidBombCount = {
//            val user5 = genUser
//            val user6 = genUser
//            val user7 = genUser
//            val user8 = genUser
//            val user9 = genUser
//            val user10 = genUser
//            val user11 = genUser
//            val channel4 = genChannelWithOwner(user5)
//            val post1 = Article.create(user5, "art1 ", "art1 content", "tags", channel4)
//            val post2 = Article.create(user5, "art2 ", "art2 content", "tags", channel4)
//            val post3 = Article.create(user5, "art3 ", "art3 content", "tags", channel4)
//
//            post1.addLike(user6)
//            post1.addLike(user7)
//            post1.addLike(user8)
//            post1.addLike(user9)
//            post1.addLike(user10)
//            post1.addLike(user11)
//
//            post2.addLike(user6)
//            post2.addLike(user7)
//            post2.addLike(user8)
//            post2.addLike(user9)
//            post2.addLike(user10)
//
//            post3.addLike(user6)
//            post3.addLike(user7)
//            post3.addLike(user8)
//            post3.addLike(user9)
//
//            UserReports.calcCupidBombCount(user5.getVertex) must beEqualTo(6)
//        }
//
//
//        def calUserSuperpost = {
//            val user5 = genUser
//            val user6 = genUser
//            val user7 = genUser
//            val user8 = genUser
//            val user9 = genUser
//            val user10 = genUser
//            val user11 = genUser
//
//            println(("user5: %s, user6: %s, user7: %s, user8: %s, user9: %s," +
//                " user 10: %s, user11: %s").format(user5, user6, user7, user8, user9, user10, user11))
//
//            val channel4 = genChannelWithOwner(user5).addMembers(user5, user6, user7, user8, user9, user10, user11)
//
//            val post1 = Article.create(user5, "art1 ", "art1 content", "tags", channel4)
//            val post2 = Article.create(user5, "art2 ", "art2 content", "tags", channel4)
//            val post3 = Article.create(user5, "art3 ", "art3 content", "tags", channel4)
//
//            post1.addResponse(user6, "res 1")
//            post1.addResponse(user7, "res 2")
//            post1.addResponse(user6, "res 3")
//            post1.addResponse(user8, "res 4")
//            post1.addResponse(user7, "res 5")
//            post1.addResponse(user9, "res 6")
//            post1.addResponse(user8, "res 7")
//            post1.addResponse(user10, "res 8")
//            post1.addResponse(user9, "res 9")
//            post1.addResponse(user11, "res 10")
//            post1.addResponse(user9, "res 11")
//            post1.addResponse(user8, "res 12")
//            post1.addResponse(user11, "res 13")
//
//            post2.addResponse(user6, "user6 res")
//            post2.addResponse(user7, "user7 res")
//            post2.addResponse(user8, "user8 res")
//            post2.addResponse(user9, "user9 res")
//            post2.addResponse(user10, "user10 res")
//
//            post3.addResponse(user6, "user6 res")
//            post3.addResponse(user7, "user7 res")
//            post3.addResponse(user8, "user8 res")
//            post3.addResponse(user9, "user9 res")
//
//            db.commit()
//
//            UserReports.calcUserSuperpost(user5.getVertex) must beEqualTo(6)
//
//        }
//
//    }
//}
