/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
//* Copyright (c) 2013. Ansvia Inc.
//* Author: robin
//* Created: 6/14/13 3:05 PM
//*/
//
//package com.ansvia.digaku.stats.model
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//
///**
//* Author: robin
//* Date: 6/14/13
//* Time: 3:05 PM
//*
//*/
//class CountStatsSpec extends Specification with DigakuTest {
//    import scala.collection.JavaConversions._
//    import com.ansvia.digaku.database.GraphCompat.transact
//
//    def is = "Stats should" ^
//        sequential ^
//        p ^
//        "can get" ! trees.get ^
//        "can get by name" ! trees.getByName ^
//        "only one" ! trees.only1 ^
//        p ^
//        "UserCountStats should" ^
//        p ^
//        "paired to user when get" ! trees.pairedUser ^
//        "only one" ! trees.userCountStatsOnly1 ^
//        end
//
//    object trees {
//        def get = {
//            Stats.getCountStats must beAnInstanceOf[CountStats]
//        }
//        def getByName = {
//            (Stats.getByName("count-stats") must beAnInstanceOf[Some[CountStats]]) and
//                (Stats.getByName("count-stats").get.asInstanceOf[CountStats].activeUserCount must beEqualTo(0))
//        }
//        def only1 = {
//            val cs = Stats.getCountStats
//            transact {
//                cs.femaleUserCount = 1
//                cs.save()
//            }
//            (Stats.getCountStats.userCount must beEqualTo(1)) and
//            (db.getVertices("_class_", "com.ansvia.digaku.stats.model.CountStats").iterator().size must beEqualTo(1))
//        }
//
//        val u = genUser
//        u.reload()
//
//
//        def pairedUser = {
//
//            (Stats.getUserCountStats(u) must beAnInstanceOf[UserCountStats]) and
//                (Stats.getUserCountStats(u) must beAnInstanceOf[UserCountStats])
//
//        }
//
//        def userCountStatsOnly1 = {
//            (Stats.getUserCountStats(u) must beAnInstanceOf[UserCountStats])
//            (Stats.getUserCountStats(u) must beAnInstanceOf[UserCountStats])
//            (Stats.getUserCountStats(u) must beAnInstanceOf[UserCountStats])
//            (Stats.getUserCountStats(u) must beAnInstanceOf[UserCountStats])
//            (Stats.getUserCountStats(u) must beAnInstanceOf[UserCountStats])
//            db.getVertices("_class_", "com.ansvia.digaku.stats.model.UserCountStats").iterator().size must beEqualTo(1)
//        }
//    }
//}
