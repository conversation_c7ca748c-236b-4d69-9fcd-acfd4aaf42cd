/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
// * Copyright (c) 2013. Ansvia Inc.
// * Author: robin
// * Created: 6/14/13 5:06 PM
// */
//
//package com.ansvia.digaku.stats
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.tinkerpop.blueprints.Vertex
//import com.ansvia.digaku.model.{PostKind, SexType}
//
///**
// * Author: robin
// * Date: 6/14/13
// * Time: 5:06 PM
// *
// */
//class AnalEventStreamListenerSpec extends Specification with DigakuTest {
//
//    def is = "AnalEventStreamListener.Log should" ^
//        p ^
//            "get root vertex" ! trees.getRoot ^
//            "root vertex only one" ! trees.rootOnly1 ^
//            "can log" ! trees.canLog ^
//            "got correct latest ordering" ! trees.latestOrdering ^
//        end
//
//    import scala.collection.JavaConversions._
//    import com.ansvia.graph.BlueprintsWrapper._
//    import com.ansvia.digaku.database.GraphCompat.transact
//
//    object trees {
//
//        def getRoot = {
//            Log.rootVertex must beAnInstanceOf[Vertex]
//        }
//        def rootOnly1 = {
//            Log.rootVertex
//            Log.rootVertex
//            Log.rootVertex
//            db.query().has("_class_", "com.ansvia.digaku.stats.Log-0").vertices().size must beEqualTo(1L)
//        }
//        def canLog = {
//            Log << CreatePostLog("robin", 1, PostKind.SIMPLE_POST)
//            Log << CreatePostLog("danny", 2, PostKind.ARTICLE)
//
//            (Log.rootVertex.pipe.out(Log.LOG).has("event","create-post").count() must beEqualTo(2)) and
//            (Log.rootVertex.pipe.out(Log.LOG).has("event","create-post").has("userName", "danny").count() must beEqualTo(1))
//        }
//        def latestOrdering = {
//            LogTx << CreateUserLog("robin",123,SexType.MALE,26)
//            Thread.sleep(2)
//            LogTx << CreateUserLog("imam",124,SexType.MALE,28)
//            Thread.sleep(2)
//            LogTx << CreateUserLog("uton",125,SexType.MALE,25)
//            Thread.sleep(2)
//            val x = Log.getLatest("com.ansvia.digaku.stats.CreateUserLog",0,3).toList.map(_.asInstanceOf[CreateUserLog])
//            (x.map(_.userName).toList must contain("uton","imam","robin").only)
//        }
//    }
//}
