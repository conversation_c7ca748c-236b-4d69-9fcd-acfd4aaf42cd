///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.stats.model
//
///**
// * Author: robin
// * Date: 10/12/13
// * Time: 3:38 PM
// *
// */
//
//
// import com.ansvia.digaku.DigakuTest
// import org.specs2.Specification
//
//
// class UrlTrackerStatsSpec extends Specification with DigakuTest {
//     def is = "Url tracker stats should" ^
//         sequential ^
//         "get top most visited url" ! trees.getTopMostVisitedUrl ^
//     end
//
//     object trees {
//
//
//         val u1 = genUser
//         val u2 = genUser
//
//         val s = DigakuStats.getUrlTrackerStats
//
//         def getTopMostVisitedUrl = {
//
//             s.trackUrl(u1, "http://www.mindtalk.com/page1")
//             s.trackUrl(u1, "http://www.mindtalk.com/page1")
//             s.trackUrl(u1, "http://www.mindtalk.com/page2")
//             s.trackUrl(u2, "http://www.mindtalk.com/page2")
//             s.trackUrl(u2, "http://www.mindtalk.com/page2")
//             s.trackUrl(u2, "http://www.mindtalk.com/page3")
//
//             val rv = s.getTopMostVisitedUrls
//
//             (rv(0) must_==("http://www.mindtalk.com/page2", 3)) and
//                 (rv(1) must_== ("http://www.mindtalk.com/page1", 2)) and
//                 (rv(2) must_== ("http://www.mindtalk.com/page3", 1))
//         }
//
//     }
//
// }
//
