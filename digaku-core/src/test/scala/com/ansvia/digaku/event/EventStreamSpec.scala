/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.event

import org.specs2.Specification
import com.ansvia.digaku.{Digaku, DigakuTest}
import org.specs2.specification.Step

/**
 * Author: robin
 *
 */
class EventStreamSpec extends Specification with DigakuTest {
    def is =
        "EventStream should" ^
        p ^
            "started when needed" ! eventStream.started ^
            "add and use listener" ! eventStream.addUseListener ^
        end


    private var receivedEvents = Array.empty[StreamEvent]

    private class DummyListenerAll() extends EventStreamListener {
        def isEventHandled(eventName: String) = true

        def dispatch = {
            case event =>
                receivedEvents +:= event
        }
    }

    private case class DummyEvent(data:String) extends StreamEvent("dummy1")

    object eventStream {

        Digaku.engine.eventStream.addListeners(new DummyListenerAll())

        def started = {
            Digaku.engine.eventStream.emit(DummyEvent("startup"))
            Digaku.engine.eventStream.isReady must beTrue
        }

        def addUseListener = {
            Digaku.engine.eventStream.emit(DummyEvent("hello"))
            Thread.sleep(1000)
            receivedEvents.toList must contain(DummyEvent("hello"))
        }
    }
}

