/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.persistence

/**
 * Author: robin
 *
 */

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification

class CassandraCounterProviderSpec extends Specification with DigakuTest {

    def is = "Cassandra counter provider should" ^
        sequential ^
        "do increment" ! trees.doCountingBy1 ^
        "do decrement" ! trees.doDecrement ^
        end

    object trees {

        CassandraCounterProvider.setup("127.0.0.1:9160","digaku", "digaku_test",
            "SimpleStrategy", "replication_factor:1")

        val rowKey = genRandomString

        def doCountingBy1 = {
            CassandraCounterProvider(rowKey).increment("mycounter")
            CassandraCounterProvider(rowKey).incrementBy("mycounter", 5)
            CassandraCounterProvider(rowKey).increment("mycounter")
            Thread.sleep(500)
            CassandraCounterProvider(rowKey).get("mycounter") must_== 7
        }

        def doDecrement = {
            CassandraCounterProvider(rowKey).decrementBy("mycounter", 5)
            Thread.sleep(500)
            CassandraCounterProvider(rowKey).get("mycounter") must_== 2
        }

    }

}

