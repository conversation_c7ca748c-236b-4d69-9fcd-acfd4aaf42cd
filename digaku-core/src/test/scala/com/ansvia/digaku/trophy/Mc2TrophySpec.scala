/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.trophy

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.trophy.MC2Trophy.RuleParam
import org.specs2.Specification
import com.ansvia.graph.BlueprintsWrapper._

/**
* Author: robin
*
*/
class Mc2TrophySpec extends Specification with DigakuTest {

    def is = {
        "MC2 trophy should" ^
            p ^
            sequential ^
            "Execute basic rules:" ^
            p ^
            "  - posts " ! trees.basicRulePosts ^
            "  - responses " ! trees.basicRuleResponses ^
            "  - likes " ! trees.basicRuleLikes ^
            p ^
            "Use custom rules" ! trees.customRules ^
            end
    }

    object trees {

        val u1 = genUser


        def basicRulePosts = {

            val postTrophy = MC2Trophy("Thread Bronze", "Create your first thread").withAutoAssign(true)
                .withAttribute("articles").withRange(1, 100).save().toCC[MC2Trophy].get
            db.commit()

            val u2 = genUser
            val u3 = genUser

            u2.incrementArticleCount(50)

            (postTrophy.canReceive(u2) must beTrue) and (postTrophy.canReceive(u3) must beFalse)

        }


        def basicRuleResponses = {

            val trophy = MC2Trophy("Bronze Commentator", "For commented more than 100 times")
                .withAttribute("responses").withRange(100, 250).save().toCC[MC2Trophy].get
            db.commit()

            val u2 = genUser
            val u3 = genUser

            u2.getCounter.incrementBy("response_count", 150)

            (trophy.canReceive(u2) must beTrue) and (trophy.canReceive(u3) must beFalse)

        }


        def basicRuleLikes = {

            val trophy = MC2Trophy("Gold Popular", "For getting more than 100 likes")
                .withAttribute("likes").withRange(500, 1000).save().toCC[MC2Trophy].get
            db.commit()

            val u2 = genUser
            val u3 = genUser

            u2.getCounter.incrementBy("got_likes_count", 500)

            (trophy.canReceive(u2) must beTrue) and
                (trophy.canReceive(u3) must beFalse)

        }

        def customRules = {

            val trophy = MC2Trophy("Diamond Custom", "For test only")
                .withAttribute("custom").withRange(10, 50).save().toCC[MC2Trophy].get
            db.commit()

            val u2 = genUser
            u2.getCounter.incrementBy("custom_rule_count", 5)
            u2.getCounter.incrementBy("articleCount", 7)

            MC2Trophy.rulesParsers.append {
                case RuleParam("custom", _trophy, user) =>
                    val mixedCount = user.getArticleCount + user.getCounter.get("custom_rule_count")

                    _trophy.calcRange._1 <= mixedCount && _trophy.calcRange._2 > mixedCount
            }

            val canReceive = trophy.canReceive(u2) must beTrue

            trophy.updateTrophyState(u2)

            val hasTrophy = u2.getTrophies(0, 10).toList must contain(trophy)

            canReceive and
                hasTrophy
        }



    }

}
