/*
* Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
*
* This file is part of Digaku project.
*
* Unauthorized copying of this file, via any medium is strictly prohibited
* Proprietary and confidential.
*/

package com.ansvia.digaku.functional

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model.ExperienceRank
import org.specs2.Specification
import org.specs2.specification.{Step, Fragments}

/**
* Author: nadir (<EMAIL>)
*
*/
class UserRankSpec extends Specification with DigakuTest {
    override def is: Fragments = {
        sequential ^
            "UserRank should" ^
            p ^
            "increase experience user" ! trees.increaseExperience ^
            "decrease experience user" ! trees.decreaseExperience ^
            "get rank user" ! trees.getRankUser ^
            end
    }

    object trees {

        val user1 = genUser

        ExperienceRank.removeAllRank()

        ExperienceRank.createRank(0, "Rank 1")
        ExperienceRank.createRank(100, "Rank 2")
        ExperienceRank.createRank(200, "Rank 3")
        ExperienceRank.createRank(500, "Rank 4")
        ExperienceRank.createRank(750, "Rank 5")
        ExperienceRank.createRank(1000, "Rank 6")
        ExperienceRank.createRank(4000, "Rank 7")
        ExperienceRank.createRank(10000, "Rank 8")
        ExperienceRank.createRank(25000, "Rank 9")
        ExperienceRank.createRank(50000, "Rank 10")

        def increaseExperience = {
            user1.increaseExperience(10)

            user1.getExperience must beEqualTo(10)
        }

        def decreaseExperience = {
            user1.decreaseExperience(10)

            user1.getExperience must beEqualTo(0)
        }

        def getRankUser = {

            val user2 = genUser
            val user3 = genUser
            val user4 = genUser
            val user5 = genUser

            user1.increaseExperience(100)
            user2.increaseExperience(800)
            user3.increaseExperience(40000)
            user4.increaseExperience(90000)
            user5.increaseExperience(15)

            user1.getRank.get.name must beEqualTo("Rank 2") and (
                user2.getRank.get.name must beEqualTo("Rank 5")) and (
                user3.getRank.get.name must beEqualTo("Rank 9")) and (
                user4.getRank.get.name must beEqualTo("Rank 10")) and (
                user5.getRank.get.name must beEqualTo("Rank 1"))

        }

    }

}
