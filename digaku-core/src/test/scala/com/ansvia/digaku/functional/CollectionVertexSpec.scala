/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.functional

/**
 * Author: robin
 *
 */

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.Types.GremPipeVertex
import com.ansvia.digaku.model._
import com.thinkaurelius.titan.core.TitanVertex
import com.tinkerpop.blueprints.util.wrappers.id.IdVertex
import com.tinkerpop.blueprints.{Direction, Vertex}
import org.specs2.Specification


class CollectionVertexSpec extends Specification with DigakuTest {

    import com.ansvia.digaku.model.Label._
    import com.ansvia.graph.BlueprintsWrapper._

    import scala.collection.JavaConversions._


    def is = "Collection vertex should" ^
        sequential ^
        "iterate in right order (year based)" ! trees.rightOrderYearBased ^
        "iterate in right order (month based)" ! trees.rightOrderMonthBased ^
        "iterate in right order (month based inline mode)" ! trees.rightOrderMonthBasedInline ^
        end

    object trees {

        val u1 = genUser

        // val u2 = genUser

        val ch1 = genForumWithOwner(u1)

        val v1 = db.addVertex(null)
        val v2 = db.addVertex(null)
        val v3 = db.addVertex(null)
        val v4 = db.addVertex(null)
        val v5 = db.addVertex(null)
        val v6 = db.addVertex(null)

        def rightOrderYearBased = {

            val timeOrderKey = "timeOrder"

            (u1.getYPart(CollectionVertex.Kind.PUBLISH_CONTENT, 2014) --> PUBLISH_CONTENT --> v1 <()).setProperty(timeOrderKey, 1)
            (u1.getYPart(CollectionVertex.Kind.PUBLISH_CONTENT, 2014) --> PUBLISH_CONTENT --> v2 <()).setProperty(timeOrderKey, 2)
            Thread.sleep(500L)
            (u1.getYPart(CollectionVertex.Kind.PUBLISH_CONTENT, 2015) --> PUBLISH_CONTENT --> v3 <()).setProperty(timeOrderKey, 3)
            Thread.sleep(500L)
            (u1.getYPart(CollectionVertex.Kind.PUBLISH_CONTENT, 2016) --> PUBLISH_CONTENT --> v4 <()).setProperty(timeOrderKey, 4)
            (u1.getYPart(CollectionVertex.Kind.PUBLISH_CONTENT, 2016) --> PUBLISH_CONTENT --> v5 <()).setProperty(timeOrderKey, 5)
            Thread.sleep(500L)
            (u1.getYPart(CollectionVertex.Kind.PUBLISH_CONTENT, 2014) --> PUBLISH_CONTENT --> v6 <()).setProperty(timeOrderKey, 6)

            db.commit()


            val rv = u1.getVertex.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT)
                .range(0,3)
                .out(PUBLISH_CONTENT)
                .asInstanceOf[GremPipeVertex]
                .iterator().toList

            u1.getVertex.asInstanceOf[IdVertex].getBaseVertex.asInstanceOf[TitanVertex]
                .query().direction(Direction.OUT).labels(COLLECTION_VERTEX)
                .has("kind", CollectionVertex.Kind.PUBLISH_CONTENT)
                .limit(3).vertices()
                .foreach { ed:Vertex =>
                    println(ed.getProperty[java.lang.Integer]("year"))
                }

//            u1.getVertex.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT)
//                .range(0,3)
//                .outE(PUBLISH_CONTENT).interval(timeOrderKey, 0L, System.currentTimeMillis())
//                .asInstanceOf[GremPipeEdge]
//                .iterator().foreach { ed =>
//                    println(" - v: " + ed.getVertex(Direction.IN) + ", timeOrder: " + ed.getProperty[java.lang.Long](timeOrderKey))
////                    println(" - v: " + v)
//                }

            println(rv)

            rv must contain(v5,v4,v3,v6,v2,v1).inOrder
        }


        def rightOrderMonthBased = {

            // @TODO(robin): benerin ini
            skipped

//            val gallery = ChannelGallery.create(genRandomString, ch1)
//            val album = Album.create(genRandomString, "desc " + genRandomString, "tags", gallery)
//
//            val pic1 = Picture.create(u1, "some picture 1", ch1); Thread.sleep(100)
//            val pic2 = Picture.create(u1, "some picture 2", ch1); Thread.sleep(100)
//            val pic3 = Picture.create(u1, "some picture 3", ch1); Thread.sleep(100)
//            val pic4 = Picture.create(u1, "some picture 4", ch1); Thread.sleep(100)
//
//
//            album.getMPart(CollectionVertex.Kind.STREAM, 2014, 5) --> STREAM --> pic4
//            album.getMPart(CollectionVertex.Kind.STREAM, 2014, 3) --> STREAM --> pic3
//            album.getMPart(CollectionVertex.Kind.STREAM, 2013, 6) --> STREAM --> pic2
//            album.getMPart(CollectionVertex.Kind.STREAM, 2012, 10) --> STREAM --> pic1
//
//            val stream = album.getStream(-1, 0, 10).toList
//
//            // println(stream)
//
//            stream must contain (pic4, pic3, pic2, pic1).inOrder
        }

        def rightOrderMonthBasedInline = {
            // @TODO(robin): benerin ini
            skipped

//            val u1 = genUser
//
//            val ch1 = genChannelWithOwner(u1)
//
//            val p1:Streamable[IDType] = Post.createSimple(u1, "hello user", ch1); Thread.sleep(100)
//            val p2:Streamable[IDType] = Post.createSimple(u1, "hello user #2", ch1); Thread.sleep(100)
//            val p3:Streamable[IDType] = Post.createSimple(u1, "hello user #3", ch1); Thread.sleep(100)
//
//            val stream = u1.getStream(0, 10).toList.map(_.content)
//
//            val ordering = stream must contain (p3, p2, p1).inOrder
//
//            val year = new DateTime().getYear
//            val month = new DateTime().getMonthOfYear
//            val monthParted = u1.getVertex.pipe.out(COLLECTION_VERTEX)
//                .has("kind", CollectionVertex.Kind.STREAM)
//                .has("year", year)
//                .has("month", month)
//                .out(STREAM)
//                .iterator().toList.flatMap(_.toCC[Streamable[IDType]])
//            val monthPartedCorrectly = monthParted must contain (p3, p2, p1).inOrder
//            val same = stream must_== monthParted
//
//            ordering and monthPartedCorrectly and same
        }


    }

}
