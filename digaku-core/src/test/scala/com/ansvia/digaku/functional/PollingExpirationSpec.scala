/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 *
 */

package com.ansvia.digaku.functional

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model.Polling
import org.joda.time.DateTime
import org.specs2.mutable.Specification
import org.specs2.specification.Scope

/**
 * Author: robin (<EMAIL>)
 */



class PollingExpirationSpec extends Specification with DigakuTest {

    "Polling functionality" should {
        "can expired after n time" in new Ctx {
            poll.setExpiration(new DateTime().plusSeconds(2).getMillis)
            Thread.sleep(100)
            poll.isExpired must beFalse
            Thread.sleep(2001)
            poll.isExpired must beTrue
        }
        "never expired when expiration set to 0" in new Ctx {
            poll.setExpiration(0)
            Thread.sleep(100)
            poll.isExpired must beFalse
            Thread.sleep(2001)
            poll.isExpired must beFalse
        }
    }

    trait Ctx extends Scope {
        val poll = Polling.create(genRandomString, genRandomString)

    }

}

