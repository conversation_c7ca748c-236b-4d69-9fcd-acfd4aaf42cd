/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.functional

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import org.specs2.execute.Result
import org.specs2.specification.{Fragments, Step}

/**
 * Author: ubai
 *
 */
class PopularPostSpec extends Specification with DigakuTest {

    def is: Fragments = {
        sequential ^
            "Popular post should" ^
            p ^
            "get popular all post" ! trees.getPopularPost ^
        end
    }



    object trees {

//        val u = genUser
//        val ch = genChannel
//        ch.setAllPermission(SubForumPermission.ALL)
//
//        u.reload()
//        ch.reload()
//
//        val ar1 = Article.create(u, "ar1", "ar1 content", "ar1 tags", ch)
//        Thread.sleep(5000L)
//        u.reload()
//        ch.reload()
//
//        val vid1 = Post.createSimple(u, "https://www.youtube.com/watch?v=aAb7hSCtvGw", ch)
//        EmbeddedLink.create("https://www.youtube.com/watch?v=aAb7hSCtvGw", "API",
//            LinkKind.VIDEO, "", "desc", Some(vid1))
//        Thread.sleep(1000L)
//        u.reload()
//        ch.reload()
//
//        val pic1 =  Picture.create(u, "picture 2", ch)
//        Thread.sleep(1000L)

        def getPopularPost:Result = {
            // sementara skip dulu karena builtin popular builder tidak ada
            // telah dipindah ke digaku-shell
            // @TODO(robin): buat step untuk build popular sebelum testing
            return skipped

//            val u2 = genUser
//            val u3 = genUser
//            val u4 = genUser
//            val u5 = genUser
//
//            vid1.reload()
//            u2.reload()
//
//            vid1.addLike(u2)
//            vid1.incrementViewsBy(5)
//            vid1.save()
//            u3.reload()
//            vid1.addLike(u3)
//            u4.reload()
//            vid1.addLike(u4)
//            u5.reload()
//            vid1.addLike(u5)
//
//            pic1.reload(); u2.reload()
//            pic1.addResponse(u2, "foo bar")
//            pic1.likesCount = 2
//            pic1.incrementViewsBy(5)
//            pic1.save()
//
//            vid1.reload(); u2.reload()
//            vid1.addResponse(u2, "foo bar")
//
//            ar1.reload()
//            ar1.likesCount = 1
//            ar1.incrementViewsBy(1)
//            ar1.save()
//
//            Video.getPopularVideos(0, 0, false)
//            Article.getPopularArticles(0, 0, false)
//            Picture.getPopularPictures(0, 0, false)

            // @TODO(*): fix this
//            val ars = Article.getPopularPost(0, 10).map(_._1).toList

//            ars must contain(PostStreamObject(vid1), PostStreamObject(pic1), PostStreamObject(ar1)).only.inOrder

            skipped
        }

    }
}
