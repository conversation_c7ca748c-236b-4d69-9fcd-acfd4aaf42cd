/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.functional

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.exc.{AlreadyExistsException, PermissionDeniedException}
//import com.ansvia.digaku.model.PrivateMessage
import org.specs2.Specification
import org.specs2.specification.Step

/**
 * Author: robin
 *
 */
class BlockUnblockUserSpec extends Specification with DigakuTest {

    def is = {
        sequential ^
        "Block unblock user harusnya" ^
        p ^
        "bisa ngeblock user" ! trees.blockUser ^
        "bisa unblock user" ! trees.unblockUser ^
        "check apakah user diblock oleh user" ! trees.isBlockedUser ^
        "user tidak bisa ngeblock dirinya sendiri" ! trees.canNotBlockItSelf ^
        "user yang sudah diblock tidak bisa diblock lagi" ! trees.userAlreadyBlocked ^
        "user yang sudah diunblock tidak bisa diunblock lagi" ! trees.unblockUserNotBlocked ^
        p ^
        "user yang telah diblock harusan" ^
            p ^
            "tidak bisa merespon ke user yang ngeblock" ! trees.responseBlockedUser ^
//            "tidak bisa kirim message ke user yang ngeblock" ! trees.sendMessageBlockedUser ^
            "tidak bisa mensupport user yang ngeblock" ! trees.supportBlockedUser ^
            "apabila user dalam posisi support ketika diblock maka otomatis diunsupport" ! trees.blockUserMustUnsupportToo ^
//            "tidak bisa reply message ke user yang ngeblock" ! trees.replyMessage ^
        end
    }

    object trees {
        val user1 = genUser
        val user2 = genUser
        val user3 = genUser
        val user4 = genUser

        def blockUser = {
            user1.blockUser(user2)
            user1.blockUser(user3)

            user1.getBlockedUsers(0, 10).toList must be contain(user2, user3)
        }

        def unblockUser = {
            user1.unblockUser(user2)

            user1.getBlockedUsers(0, 10).toList must not contain(user2)
        }

        def isBlockedUser = {
            user1.isBlocked(user3) must beTrue
        }

        def canNotBlockItSelf = {
            user1.blockUser(user1) must throwAn[PermissionDeniedException]
        }

        def userAlreadyBlocked = {
            user1.blockUser(user3) must throwAn[AlreadyExistsException]
        }

        def unblockUserNotBlocked = {
            user1.unblockUser(user2)  must throwAn[PermissionDeniedException]
        }

        def responseBlockedUser = {
            // @TODO(robin): benerin ini
            skipped
//            val post1 = Post.createSimple(user1, "post 1", user1)
//
//
//            post1.addResponse(user3, "response 1")  must throwAn[PermissionDeniedException]
        }

//        def sendMessageBlockedUser = {
//
//            PrivateMessage.create(user3, genRandomString,
//                "send message to user1", Array(user1, user2)) must throwAn[PermissionDeniedException]
//
//        }

        def supportBlockedUser = {
            user3.support(user1) must throwAn[PermissionDeniedException]
        }

        def blockUserMustUnsupportToo = {
            user1.support(user2)

            user1.blockUser(user2)

            (user1.isSupport(user2) must beFalse) and
                (user1.isBlocked(user2) must beTrue)
        }

//        def replyMessage = {
//
//            user1.support(user4)
//            user4.support(user1)
//
//            val message2 = PrivateMessage.create(user1, genRandomString, "private message 2", Array(user4))
//
//            user1.blockUser(user4)
//
//            message2.addResponse(user4, "response message")  must throwAn[PermissionDeniedException]
//        }

    }

}
