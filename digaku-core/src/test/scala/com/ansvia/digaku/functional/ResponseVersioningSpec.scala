/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 *
 */

package com.ansvia.digaku.functional

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model.Response
import org.specs2.mutable.Specification
import org.specs2.specification.Scope

/**
 * Author: robin (<EMAIL>)
 */

class ResponseVersioningSpec extends Specification with DigakuTest {

    sequential

    "Response functionality for versioning" should {
        "default revision in first creation is 0" in new Ctx {
            response.latestRevision must_== 0
        }
        "can increment revision" in new Ctx {
            response.incRevision()
            response.latestRevision must_== 1
        }
    }

    class Ctx extends Scope {
        val u1 = genUser
        val response = Response.create(u1, "some response")
    }

}