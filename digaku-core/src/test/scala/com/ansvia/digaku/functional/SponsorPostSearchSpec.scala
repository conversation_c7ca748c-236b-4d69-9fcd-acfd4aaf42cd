///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.functional
//
///**
// * Author: robin
// *
// */
//
//import com.ansvia.digaku.model.{SexType, SponsorPost}
//import com.ansvia.digaku.se.{NopSearchEngine, EmbeddedElasticSearchEngine}
//import com.ansvia.digaku.{Digaku, DigakuTest, DigakuTestEngine}
//import org.joda.time.DateTime
//import org.specs2.Specification
//import org.specs2.specification.Step
//
//import scala.sys.process._
//
//class SponsorPostSearchSpec extends Specification with DigakuTest {
//
////    var origEngine: Engine = null
//
//    def is = "Sponsor post search functionality should" ^
//        sequential ^
//        Step {
//            trees.setup()
//        } ^
//        "able to search by simple query" ! trees.simpleQuery ^
//        "able to search by time range query" ! trees.byTimeRangeQuery ^
//        "able to search by age range query" ! trees.ageRangeQuery ^
//        "able to search by gender target query" ! trees.genderTargetQuery ^
//        "able to search by show time range query" ! trees.testShowTimeRangeQuery ^
//        "able to search by target country" ! trees.targetCountryQuery ^
//        "able to search by target province" ! trees.targetProvinceQuery  ^
//        "able to search by target city" ! trees.targetCityQuery  ^
//        "able to search only return non closed sponsor by default" ! trees.nonClosedByDefault ^
//        "able to search closed sponsor" ! trees.searchClosed ^
//        "able to search by combination time, age, and gender" ! trees.searchCombTimeAgeGender ^
//        "able to get all by empty query string" ! trees.searchAll ^
//        "able to delete index" ! trees.deleteIndex ^
////        "able to search by combination time, age, gender range, and target country" ^
////        "able to search by combination time, age, gender range, and target province" ^
////        "able to search by combination time, age, gender range, and target city" ^
//        Step {
//            trees.cleanup()
//        } ^
//        end
//
//    object trees {
//
//
//        def setup(){
//
//            Seq("rm", "-rf", testIndexDir).!
//
//            val se = new EmbeddedElasticSearchEngine(testIndexDir)
//            Digaku.engine.asInstanceOf[DigakuTestEngine].setSearchEngine(se)
//
//            se.index(sp1)
//            se.index(sp2)
//            se.index(sp3)
//            se.index(sp4)
//            se.index(sp5)
//
//            Thread.sleep(5000)
//        }
//
//
//        private lazy val se = Digaku.engine.searchEngine.asInstanceOf[EmbeddedElasticSearchEngine]
//
//        Thread.sleep(5000)
//
//        val now = new DateTime()
//        val startTime = now.toDate
//        val endTime = now.plusDays(30).toDate
//
//        val sp1 = SponsorPost.create("motor baru merah", "iklan motor", "goto-url||http://motor.com",
//            startTime, endTime, active = true)
//
//        val sp2 = SponsorPost.create("ini motor lama", "iklan motor lama nih", "goto-url||http://motor.com",
//            now.plusDays(30).toDate, now.plusDays(35).toDate, active = true)
//        sp2.genderTarget = SexType.MALE
//        sp2.save()
//        db.commit()
//
//        val sp3 = SponsorPost.create("kondom warna warni", "ini iklan kondom", "goto-url||http://kondom.com",
//            startTime, endTime, active = true)
//        sp3.minAge = 25
//        sp3.showTimeStart = 21 // jam 9 malam
//        sp3.showTimeEnds = 5 // 21+5 = jam 2 pagi
//        sp3.save()
//        db.commit()
//
//        val sp4 = SponsorPost.create("mobil warna merah", "ini iklan mobil warna merah", "goto-url||http://mobil.com",
//            startTime, endTime, active = true)
//        sp4.targetCountry = "india"
//        sp4.targetProvince = "new delhi"
//        sp4.targetCity = "mumbai"
//        sp4.minAge = 28
//        sp4.save()
//        db.commit()
//
//        val sp5 = SponsorPost.create("baju warna merah", "ini iklan baju warna merah", "goto-url||http://baju.com",
//            startTime, endTime, active = true)
//        sp5.targetCountry = "india"
//        sp5.minAge = 28
//        sp5.save()
//        db.commit()
//
//        Thread.sleep(10000)
//
//        def simpleQuery = {
//            val rv = se.searchSponsorPost("motor", 0, 5)
//            println(rv)
//            (rv.count must_== 2) and
//                (rv.entries must contain(sp1, sp2).only)
//        }
//
//
//        def byTimeRangeQuery = {
//            val rv = se.searchSponsorPost("motor", 0, 5, Some((now.plusDays(5).toDate, null)))
//            val rv2 = se.searchSponsorPost("motor", 0, 5, Some((now.plusDays(31).toDate, now.plusDays(33).toDate)))
//
//            println("byTimeRangeQuery.rv1: " + rv.entries.toList)
//            println("byTimeRangeQuery.rv2: " + rv2.entries.toList)
//
//            (rv.count must_== 1) and
//                (rv.entries must contain(sp1).only) and
//                (rv2.count must_== 1) and
//                (rv2.entries must contain(sp2).only)
//        }
//
//        def ageRangeQuery = {
//            val rv = se.searchSponsorPost("warna", 0, 5)
//            val rv2 = se.searchSponsorPost("warna", 0, 5, ageRange=Some((25, 0)))
//            val rv3 = se.searchSponsorPost("warna", 0, 5, ageRange=Some((25, 50)))
//
//            println("ageRangeQuery rv1: " + rv)
//            println("ageRangeQuery rv2: " + rv2)
//            println("ageRangeQuery rv3: " + rv3)
//
//            (rv.count must_== 3) and
//                (rv.entries must contain(sp4)) and
//                (rv2.count must_== 1) and
//                (rv2.entries must contain(sp3).only)
//                (rv3.count must_== 1) and
//                (rv3.entries must contain(sp3).only)
//        }
//
//
//
//        def genderTargetQuery = {
//            val rv = se.searchSponsorPost("motor", 0, 5, genderTarget=Some(SexType.MALE))
//
//            println("genderTargetQuery rv: " + rv.entries.toList)
//
//            // result harusnya berisi sp1 dan sp2
//            // sp1 ? ya karena sp1 tidak diset gender-nya jadi gender=ALL
//            //      tidak ikut terfilter
//
//            (rv.count must_== 2) and
//                (rv.entries must contain(sp1, sp2).only)
//        }
//
//
//        def testShowTimeRangeQuery = {
//            val rv = se.searchSponsorPost("warna", 0, 5, showTimeRange = Some((22,4))) // dari jam 9 ke +4 (jam 2)
//            (rv.count must_== 1) and
//                (rv.entries must contain(sp3).only)
//        }
//
//        def targetCountryQuery = {
//            val rv = se.searchSponsorPost("merah", 0, 5, targetCountry = Some("india"))
//            println("targetCountry: " + rv)
//            (rv.count must_== 2) and
//                (rv.entries must contain(sp4, sp5).only)
//        }
//
//        def targetProvinceQuery = {
//            val rv = se.searchSponsorPost("merah", 0, 5, targetCountry = Some("india"),
//                targetProvince = Some("new delhi"))
//            (rv.count must_== 1) and
//                (rv.entries must contain(sp4).only)
//        }
//
//        def targetCityQuery = {
//            val rv = se.searchSponsorPost("merah", 0, 5, targetCity=Some("mumbai"))
//            (rv.count must_== 1) and
//                (rv.entries must contain(sp4).only)
//        }
//
//        def nonClosedByDefault = {
//            val u1 = genUser
//            sp1.setClosed(true, u1, "")
//            sp4.setClosed(true, u1, "")
//            sp1.save()
//            sp4.save()
//            db.commit()
//            se.index(sp1)
//            se.index(sp4)
//            Thread.sleep(5000)
//            val rv = se.searchSponsorPost("merah", 0, 5)
//            (rv.count must_== 1) and
//                (rv.entries must contain(sp5).only)
//        }
//
//
//        def searchClosed = {
//            val rv = se.searchSponsorPost("merah", 0, 5, containsClosed=true)
//            (rv.count must_== 3) and
//                (rv.entries must contain(sp1, sp4, sp5).only)
//        }
//
//
//        def searchCombTimeAgeGender = {
//
//            sp2.maxAge = 50
//            sp2.minAge = 17
//            sp2.genderTarget = SexType.MALE
//            sp2.save()
//            db.commit()
//            se.index(sp2)
//            Thread.sleep(5000)
//
//            val rv = se.searchSponsorPost("motor lama", 0, 5,
//                timeRange = Some((now.plusDays(30).toDate, null)),
//                ageRange = Some((17,50)),
//                genderTarget = Some(SexType.MALE),
//                containsClosed=true)
//
//            println("searchCombTimeAgeGender rv: " + rv.entries.toList)
//
//            // harusnya berisi sp1 dan sp2
//            // kenapa ada sp1? Karena sp1 itu general tidak diset attribut khusus
//            // jadi tetap masuk ke semua jenis pencarian
//            (rv.count must_== 2) and
//                (rv.entries must contain(sp1, sp2).only)
//        }
//
//        def searchAll = {
//            val rv = se.searchSponsorPost("",0,10)
//            (rv.count must_== 3)
//        }
//
//
//        def deleteIndex = {
//            se.deleteSponsorPost(sp1)
//            se.deleteSponsorPost(sp2)
//            se.deleteSponsorPost(sp4)
//            se.deleteSponsorPost(sp5)
//            Thread.sleep(5000)
//            val rv = se.searchSponsorPost("motor baru merah", 0, 5)
//
//            (rv.count must_== 0) and
//                (rv.entries must beEmpty)
//        }
//
//
//
//        def cleanup(){
//            Digaku.engine.searchEngine.close()
//            Thread.sleep(5000)
//            Digaku.engine.asInstanceOf[DigakuTestEngine].setSearchEngine(NopSearchEngine)
////            Digaku.engine = origEngine
////            Digaku.engine.searchEngine = NopSearchEngine // restore
//        }
//
//    }
//
//}
//
