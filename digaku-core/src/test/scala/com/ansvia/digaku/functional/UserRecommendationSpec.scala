/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.functional

/**
 * Author: robin
 * Date: 1/7/14
 * Time: 1:31 PM
 *
 */


import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import com.ansvia.digaku.model.User


class UserRecommendationSpec extends Specification with DigakuTest {

    def is = "User recommendation should" ^
        sequential ^
        "get users by email domain" ! trees.getUserByEmailDomain ^
        "get user recommendation by email domain" ! trees.getUserRecommendationByEmailDomain  ^
        "user yang pake public email provider jangan direkomendasikan" ! trees.getUserRecommendationByEmailDomainPublicDomain  ^
        "get recommendation based on supporting support (pymk)" ! trees.getPymk ^
        "get recommendation by name based on supporting support (pymk)" ! trees.getPymkByName ^
        end

    object trees {

        val u1 = genUserWithEmail("<EMAIL>")
        val u2 = genUserWithEmail("<EMAIL>")
        val u3 = genUserWithEmail("<EMAIL>")

        u1.support(u3)

        val gmailUser1 = genUserWithEmail("<EMAIL>")
        val gmailUser2 = genUserWithEmail("<EMAIL>")


        def getUserByEmailDomain = {
            User.getUsersByEmailDomain("dieng.com").toList must be contain(u1, u2, u3) only
        }

        def getUserRecommendationByEmailDomain = {
            // u3 harusnya gak muncul karena u1 sudah support u3
            u1.getRecommendation(0, 5).toList must be contain(u2) only
        }

        def getUserRecommendationByEmailDomainPublicDomain = {
            gmailUser1.getRecommendation(0, 5).toList must be not contain(gmailUser2)
        }

        def getPymk = {

            // sementara di-pending dulu karena belum dibuat kalkulasi PYMK inline-nya

//            val u4 = genUser
//            val u5 = genUser
//            val u6 = genUser
//
//            u1.support(u2)
//
//            u3.support(u4)
//            u2.support(u4)
//            u2.support(u5)
//            u2.support(u6)
//
//            /**
//             * harusnya u1 hanya dapet u4, u5, dan u6
//             * karena mereka disupport oleh u2 das u3, dimana u2 dan u3 disupport oleh u1
//             */
//
//            u1.getRecommendation(0, 10).toList must contain(u4, u5, u6).only

            pending


        }

        def getPymkByName = {

            // sementara di-pending dulu karena belum dibuat kalkulasi PYMK inline-nya

//            val u4 = genUser
//            val u5 = genUser
//            val u6 = genUser
//
//            u1.support(u2)
//
//            u3.support(u4)
//            u2.support(u4)
//            u2.support(u5)
//            u2.support(u6)
//
//            /**
//             * harusnya u1 hanya dapet u4, u5, dan u6
//             * karena mereka disupport oleh u2 dan u3, dimana u2 dan u3 disupport oleh u1
//             */
//
//            u1.getRecommendation(u4.name, 0, 10).toList must contain(u4).only

            pending
        }



    }

}
