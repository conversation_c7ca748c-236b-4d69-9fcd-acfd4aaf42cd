package com.ansvia.digaku.functional

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import org.specs2.specification.Step

/**
 * Author: robin (<EMAIL>)
 */

class UserNotifSpec extends Specification with DigakuTest {


    def is = {
        sequential ^
            "User notif should" ^
            p ^
            "mark as read notification by ids" ! trees.markReadById ^
            end
    }

    object trees {
        def markReadById = {
            // @TODO(robin): benerin ini
            skipped

//            val user1 = genUser
//            val user2 = genUser
//            val user3 = genUser
//
//            val ch1 = genChannelWithOwner(user1)
//
//            val post1 = Post.createSimple(user1, "user1 create post #1", ch1)
//            val post2 = Post.createSimple(user1, "user1 create post #2", ch1)
//
//            val notifCountBefore = user1.getUnreadNotificationCount must beEqualTo(0)
//
//            post1.addLike(user2)
//            // ini untuk ngetest groupable Like
//            // user beda tapi satu post notif-nya hanya akan muncul satu
//            post1.addLike(user3)
//
//            // ini untuk ngetest di post lain harusnya notif-nya beda
//            // tidak ter-group-kan
//            post2.addLike(user3)
//
//
//            {
//                val notif = user1.getNotifications(0,10).toList
//                println("markReadByID (before) | notif: " + notif)
//                user1.markReadNotifByIds(true, notif.map(_.getId):_*)
//            }
//
//            {
//                val notif = user1.getNotifications(0,10).toList
//                println("markReadByID (after) | notif: " + notif)
//            }
//
//            notifCountBefore and
//                (user1.getUnreadNotificationCount must beEqualTo(0))

        }
    }
}
