///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.functional
//
///**
// * Author: robin
// * Date: 12/6/13
// * Time: 1:43 PM
// *
// */
//
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.model.Post
//
//
//class UserBookmarkChannelSpec extends Specification with DigakuTest {
//     def is = "user bookmark group functionality" ^
//         sequential ^
//         "after user bookmark, user get zero unread count" ! trees.zeroInFirst ^
//         "check for one group only" ! trees.checkOneChannel ^
//         "unread content counted when new content appear in bookmarked channels" ! trees.unreadContentCounted ^
//     end
//
//     object trees {
//
//         val u1 = genUser
//         val u2 = genUser
//         val ch1 = genChannelWithOwner(u2)
//
//         def zeroInFirst = {
//             u1.bookmarkChannel(ch1)
//             u1.getBookmarkedChannels(0, 10).toList.head.unreadContent must_== 0
//         }
//
//         def checkOneChannel = {
//             u1.getBookmarkedChannel(ch1).head.unreadContent must_== 0
//         }
//
//         def unreadContentCounted = {
//             Post.createSimple(u2, "hello", ch1)
//             u1.getBookmarkedChannels(0, 10).toList.head.unreadContent must_== 1
//         }
//
//
//     }
//
// }
//
