/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.functional

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.exc.PermissionDeniedException
import com.ansvia.digaku.model._
import org.specs2.Specification
import org.specs2.specification.Step
import com.ansvia.digaku.utils.ForumSettings._

/**
 * Author: nadir
 *
 */
class DeletePermanentUserSpec extends Specification with DigakuTest {

    def is = {
        sequential ^
            "delete permanent user harusnya" ^
            p ^
                "owner pada Group yang dimiliki harus di ubah ke orang lain sebelum dilakukan penghapusan" ! trees.deleteUserCorrectly1 ^
                "deleted user" ! trees.deleteUserCorrectly2 ^
                "semua post dari user yang di delete harus dihapus beserta response2 nya" ! trees.deleteUserCorrectly3 ^
                "semua response dari user yang didelete harus dihapus juga" ! trees.deleteUserCorrectly4 ^
//                "semua private message dari user yang didelete harus dihapus juga beserta message responsenya" ! trees.deleteUserCorrectly5 ^
                "semua edge in/out dari user yang didelete semua harus dihapus" ! trees.deleteUserCorrectly4 ^
            end
    }


    object trees {

        val user1 = genUser
        val user1Id = user1.getId
        val user2 = genUser
        val user3 = genUser

        user1.support(user2)
        user1.support(user3)

        user2.support(user1)
        user3.support(user1)


        val channel1 = genForumWithOwner(user1)

        channel1.setWhoCanCreateArticle(ForumPermission.ALL)
        channel1.setWhoCanResponses(ForumPermission.ALL)

        channel1.save()

        db.commit()

        val post1 = Article.create(user1, "title post user1", "content post user1", "tags", channel1)
        val post1Id = post1.getId

        val response1 = post1.addResponse(user2, "content commeent")
        val response1Id = response1.getId
        val response2 = post1.addResponse(user1, "apa ya ?")
        val response2Id = response2.getId


        val post2 = Article.create(user2, "title post2 user2", "content pos2 user2", "tags", channel1)
        val post2Id = post2.getId
        post2.addLikeWithRate(user1, 5)

        val response3 = post2.addResponse(user1, "content comment post2")
        val response3Id = response3.getId

        val response4 = post2.addResponse(user3, "content comment2 post2")
        val response4Id = response4.getId


//        val privateMessage1 = PrivateMessage.create(user1, genRandomString, "hyy", Array(user2, user3))
//        val privateMessage1Id = privateMessage1.getId

//        val messageResponse1 = privateMessage1.addResponse(user2, "hyy juga")
//        val messageResponse1Id = messageResponse1.getId


        def deleteUserCorrectly1 = {
            User.delete(user1) must throwAn[PermissionDeniedException]
        }

        def deleteUserCorrectly2 = {

            user2.setActivated(true)
            channel1.changeOwnerTo(user2)

            User.delete(user1)

            User.getById(user1Id).isEmpty must beTrue

        }

        def deleteUserCorrectly3 = {
            (Post.getPostById(post1Id).isEmpty must beTrue) and
                (Response.getById(response1Id).isEmpty must beTrue) and
                    (Response.getById(response2Id).isEmpty must beTrue)
        }


        def deleteUserCorrectly4 = {
            (Post.getPostById(post2Id).isEmpty must beFalse) and
                    (Response.getById(response3Id).isEmpty must beTrue) and
                        (Response.getById(response4Id).isEmpty must beFalse)
        }

//        def deleteUserCorrectly5 = {
//            (PrivateMessage.getById(privateMessage1Id).isEmpty must beTrue) and
//                (MessageResponse.getById(messageResponse1Id).isEmpty must beTrue)
//        }

        def deleteUserCorrectly6 = {
            (post2.getLikesCount must beEqualTo(0)) and
                (user2.supportersCount must beEqualTo(0)) and
                    (user3.supportersCount must beEqualTo(0)) and
                        (user2.supportingCount must beEqualTo(0)) and
                            (user3.supportingCount must beEqualTo(0))
        }
    }

}
