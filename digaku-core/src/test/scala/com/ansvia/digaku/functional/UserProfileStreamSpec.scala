/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.functional

/**
 * Author: robin
 *
 */


import com.ansvia.digaku.DigakuTest
import org.specs2.Specification


class UserProfileStreamSpec extends Specification with DigakuTest {
     def is = "User profil stream should" ^
         sequential ^
         "get in reverse order" ! trees.getInReverseOrder ^
         "get pic in stream" ! trees.getPicInStream ^
//         "get picGroup without its pic in stream" ! trees.getPictureGroupInStream ^
     end

     object trees {

         val u = genUser
         val ch = genForumWithOwner(u)

//         val p1 = Post.createSimple(u, "post #1", ch)
//         Thread.sleep(2000)
//         val p2 = Post.createSimple(u, "post #2", ch)
//         Thread.sleep(2000)
//         val p3 = Post.createSimple(u, "post #3", ch)

         def getInReverseOrder = {
             // @TODO(robin): benerin ini
             skipped
//             val ps = u.getProfileStreamByType(0,10).map(_.content)
//
//             val rv = ps.toList
//
//             println(rv)
//
//             rv must be contain(p3,p2,p1) inOrder
         }

         def getPicInStream = {

             // @TODO(robin): benerin ini
             skipped

//             val pic1 = Picture.create(u, "pic #1", ch)
//
//             val ps = u.getProfileStreamByType(0,10).map(_.content)
//
//             val rv = ps.toList
//
//             println(rv)
//
//             rv must be contain(pic1, p3, p2, p1) inOrder
         }
         
//         def getPictureGroupInStream = {
//             val pg1 = PictureGroup.create("some title", u,  ch)
//             val pic1pg1 = Picture.create(u, "pic 1 pg 1", ch)
//             val pic2pg1 = Picture.create(u, "pic 2 pg 1", ch)
//
//             pg1.addPictures(pic1pg1, pic2pg1)
//
//             val ps = u.getProfileStreamByType(0,10).map(_.content)
//
//             val rv = ps.toList
//
//             println(rv)
//
//             (rv must be not contain(pic1pg1, pic2pg1)) and (rv must be contain(pg1, p3, p2, p1))
//         }


     }

 }

