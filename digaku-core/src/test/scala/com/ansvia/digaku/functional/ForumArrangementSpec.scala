/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.functional

import org.specs2.Specification
import com.ansvia.digaku.DigakuTest
import org.specs2.specification.{Step, Fragments}
import com.ansvia.digaku.model.Forum


/**
 * Author: andrie (<EMAIL>)
 *
 */
class ForumArrangementSpec extends Specification with DigakuTest {

    override def is:Fragments = {
        sequential ^
            "Forum Arrangement should" ^
            p ^
            "move forum to top" ! trees.moveForumToTop ^
            "move forum to bottom" ! trees.moveForumToBottom ^
            "arrange forum up" ! trees.arrangeForumUp ^
            "arrange forum down" ! trees.arrangeForumDown ^
            "remove forum arrangement list" ! trees.removeForumList ^
            end
    }

    object trees {
        val f1 = genForum
        val f2 = genForum
        val f3 = genForum

        def moveForumToTop = {
            Forum.moveForumToTop(Forum.forumListStore, f1)
            Forum.moveForumToTop(Forum.forumListStore, f2)

            Forum.getForumArrangement(Forum.forumListStore) must contain(f2, f1).inOrder
        }

        def moveForumToBottom = {
            Forum.moveForumToBottom(Forum.forumListStore, f3)
            Forum.moveForumToBottom(Forum.forumListStore, f1)
            Forum.moveForumToBottom(Forum.forumListStore, f2)

            Forum.getForumArrangement(Forum.forumListStore) must contain(f3, f1, f2).inOrder
        }

        def arrangeForumUp = {
            Forum.arrangeForumUp(Forum.forumListStore, f2)

            Forum.getForumArrangement(Forum.forumListStore) must contain(f3, f2, f1).inOrder
        }

        def arrangeForumDown = {
            Forum.arrangeForumDown(Forum.forumListStore, f3)

            Forum.getForumArrangement(Forum.forumListStore) must contain(f2, f3, f1).inOrder
        }

        def removeForumList = {
            Forum.removeFromForumList(Forum.forumListStore, f2)

            Forum.getForumArrangement(Forum.forumListStore) must contain(f3, f1).inOrder
        }
    }

}
