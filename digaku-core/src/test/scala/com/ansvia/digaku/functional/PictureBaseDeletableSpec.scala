///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.functional
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import org.specs2.specification.{Step, Fragments}
//import com.ansvia.digaku.model.{DeletedRole, DeletedType, PictureGroup, Picture}
//
///**
// * Author: ubai
// * Date: 2/27/14
// * Time: 9:31 PM
// *
// */
//class PictureBaseDeletableSpec extends Specification with DigakuTest {
//    def is: Fragments = {
//        sequential ^
//            "PictureBase deletable should" ^
//            p ^
//            Step(cleanUp()) ^
//            "harusnya ketika delete picture group picture yang di-embed-nya juga ikut di-hapus" ! trees.deletePitureGroup ^
//            "ketika delete salah satu picture dari picture group 3 photo harusnya embeded picture group nya" +
//                " tinggal 2" ! trees.deleteEmbededPictureGroup ^
//            "ketika delete picture satu per satu dari picture group, harusnya ketika delete picture yang terakhir " +
//                "picture group juga ikut terhapus" ! trees.deleteAllEmbeddedPictureGroup ^
//            Step(tearDown()) ^
//            end
//    }
//
//    object trees {
//
//        val user = genUser
//        val group = genChannelWithOwner(user)
//
//        val pic1 = Picture.create(user, "pic1", group)
//        val pic2 = Picture.create(user, "pic2", group)
//        val pic3 = Picture.create(user, "pic3", group)
//
//        val pic4 = Picture.create(user, "pic4", group)
//        val pic5 = Picture.create(user, "pic5", group)
//        val pic6 = Picture.create(user, "pic6", group)
//
//        val pg1 = PictureGroup.create("ini picture group", user, group)
//        pg1.addPictures(pic1, pic2, pic3)
//
//
//        val pg2 = PictureGroup.create("ini picture group", user, group)
//        pg2.addPictures(pic4, pic5, pic6)
//
//
//        def deletePitureGroup = {
//            pg1.setDeleted(DeletedType.SOFT, DeletedRole.POST_CREATOR, user)
//
//            val deleted = pg1.reload().deleted && pic1.reload().deleted && pic2.reload().deleted && pic3.reload().deleted
//
//            deleted must beTrue
//
//        }
//
//        def deleteEmbededPictureGroup = {
//            pic4.setDeleted(DeletedType.SOFT, DeletedRole.POST_CREATOR, user)
//
//            pg2.getPictures.toList must contain(pic5, pic6).only
//        }
//
//
//        def deleteAllEmbeddedPictureGroup = {
//            pic5.setDeleted(DeletedType.SOFT, DeletedRole.POST_CREATOR, user)
//            pic6.setDeleted(DeletedType.SOFT, DeletedRole.POST_CREATOR, user)
//
//            pg2.reload().deleted must beTrue
//
//        }
//    }
//}
