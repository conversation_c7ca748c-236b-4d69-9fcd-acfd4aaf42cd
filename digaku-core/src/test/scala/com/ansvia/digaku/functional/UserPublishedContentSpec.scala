/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.functional

/**
 * Author: robin
 *
 */

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model._
import org.specs2.Specification

class UserPublishedContentSpec extends Specification with DigakuTest {

    def is = "user published content should" ^
        sequential ^
        "get published content by kind" ! trees.getPublishedByKind ^
        "get published content by kind with offseting" ! trees.getPublishedByKindWithOffset ^
        "get published content by kind with contains flag" ! trees.getPublishedWithContainsFlag ^
        end

    object trees {

        val u1 = genUser
        val ch1 = genForumWithOwner(u1)

//        val p1 = Post.createSimple(u1, "post #1" + genRandomString, ch1)
        val p2 = Article.create(u1, "title #1" + genRandomString, "post #1" + genRandomString, "", ch1)
//        val p3 = Post.createSimple(u1, "post #1" + genRandomString, ch1)

        def getPublishedByKind = {
            // @TODO(robin): benerin ini
            skipped

//            val rv1 = u1.getPublishedContents[SimplePost](0, 5).toList must contain(p1, p3).only
//            val rv2 = u1.getPublishedContents[Article](0, 5).toList must contain(p2).only
//
//            rv1 and rv2
        }

        def getPublishedByKindWithOffset = {
            u1.getPublishedContents[Post](0, 1).size must_== 1
        }

        def getPublishedWithContainsFlag = {

            // @TODO(robin): benerin ini
            skipped

//            EmbeddedLink.create("http://www.com.com","title", LinkKind.VIDEO, "", "", Some(p3))
//
//            u1.getPublishedContents[SimplePost](0, 5, Post.contains.VIDEO_LINK).toList must contain(p3).only
        }

    }

}
