///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.functional
//
///**
// * Author: robin
// *
// */
//
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.model.PrivateMessage
//import com.ansvia.digaku.exc.LimitationReachedException
//
//
//class PrivateMessageSpec extends Specification with DigakuTest {
//
//    def is = "Private message should" ^
//        sequential ^
//        "user can't send same message twice (duplicate)" ! trees.cantSendMessageDup ^
//        "send same message twice to different user not treated as duplicate" ! trees.canSendMessageDupDiffUser ^
//        "responder/replier can't send same response twice (duplicate)" ! trees.responderCantDuplicate ^
//        "responder/replier can send same response content twice in different message" ! trees.responderCanDuplicateDiffMsg ^
//        "able to get reply/responses" ! trees.getResponses ^
//        end
//
//    object trees {
//
//        val u1 = genUser
//        val u2 = genUser
//
//        u1.support(u2)
//        u2.support(u1)
//
//        def cantSendMessageDup = {
//
//            PrivateMessage.create(u1, genRandomString,"hello user2", Array(u2))
//
//            PrivateMessage.create(u1, genRandomString,"hello user2", Array(u2)) must throwAn[LimitationReachedException]("Possibly duplicated message")
//
//        }
//
//        def canSendMessageDupDiffUser = {
//
//            val u3 = genUser
//
//            val postfix = genRandomString
//
//            PrivateMessage.create(u1, genRandomString,"hello " + postfix, Array(u2))
//
//            (PrivateMessage.create(u1, genRandomString,"hello " + postfix, Array(u3))
//                must be not throwAn[LimitationReachedException]("Possibly duplicated message"))
//
//        }
//
//
//        def responderCantDuplicate = {
//
//            val postfix = genRandomString
//
//            val pm = PrivateMessage.create(u1, genRandomString,"hello " + postfix, Array(u2))
//
//            pm.addResponse(u2, "oke " + postfix)
//            pm.addResponse(u2, "oke " + postfix) must throwAn[LimitationReachedException]
//
//        }
//
//        def responderCanDuplicateDiffMsg = {
//
//            val u3 = genUser
//            u1.support(u3)
//            u3.support(u1)
//
//            val postfix = genRandomString
//
//            val pm = PrivateMessage.create(u1, genRandomString,"hello " + postfix, Array(u2))
//            val pm2 = PrivateMessage.create(u1, genRandomString,"hello " + postfix, Array(u3))
//
//            pm.addResponse(u2, "oke " + postfix)
//            pm2.addResponse(u2, "oke " + postfix) must be not throwAn[LimitationReachedException]
//
//        }
//
//
//        def getResponses = {
//
//            val postfix = genRandomString
//
//            val pm = PrivateMessage.create(u1, genRandomString,"hello " + postfix, Array(u2))
//            pm.addResponse(u2, "hello juga coy")
//
//            pm.getResponses(u2, 0, 10).toList.map(_.content) must be contain "hello juga coy"
//
//        }
//
//
//
//    }
//
//}
//
