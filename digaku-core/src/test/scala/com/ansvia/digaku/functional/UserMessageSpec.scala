//package com.ansvia.digaku.functional
//
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.model.{PrivateMessage, User, Post}
//import org.specs2.Specification
//import org.specs2.specification.Step
//
///**
// * Author: robin (<EMAIL>)
// */
//
//class UserMessageSpec extends Specification with DigakuTest {
//
//
//    def is = {
//        sequential ^
//            "User message functionality should" ^
//            p ^
//            Step(cleanUp()) ^
//            "get message stream" ! trees.getMessageStream ^
//            "get messageNotifications list" ! trees.getMessageNotificationsList ^
//            "get unread message notification count" ! trees.unreadMessageNotifCount ^
//            "mark all message notification as read test" ! trees.markAllMessageNotifAsRead ^
//            Step(tearDown()) ^
//            end
//    }
//
//    object trees {
//
//        val user1 = genUser
//        val user2 = genUser
//        val user3 = genUser
//        val user4 = genUser
//        val user5 = genUser
//
//        def getMessageStream = {
//            user1.activated = true
//            user1.save()
//            db.commit()
//
//            user1.support(user2)
//            user2.support(user1)
//            user1.support(user3)
//            user3.support(user1)
//            val message1 = PrivateMessage.create(user1, genRandomString, "user1 message user2, user3", Array(user2, user3))
//            user4.support(user2)
//            user2.support(user4)
//            val message2 = PrivateMessage.create(user2, genRandomString, "user2 message user1, user4", Array(user1, user4))
//            user4.support(user1)
//            user1.support(user4)
//            user4.support(user5)
//            user5.support(user4)
//            val message3 = PrivateMessage.create(user4, genRandomString, "user4 message user1, user5", Array(user1, user5))
//            PrivateMessage.remove(message3)
//
//            (user1.getMessageStream(0,10) must be contain(message1, message2)) and
//                (user1.getMessageStream(0,10) must not contain(message3)) and
//                (user2.getMessageStream(0,10) must be contain(message1, message2)) and
//                (user3.getMessageStream(0,10) must be contain(message1)) and
//                (user4.getMessageStream(0,10) must be contain(message2)) and
//                (user4.getMessageStream(0, 10) must not contain(message3)) and
//                (user5.getMessageStream(0, 10) must beEmpty) and
//                (user1.getMessageCount must_==2)
//        }
//
//        def getMessageNotificationsList = {
//            val message = PrivateMessage.create(user2, genRandomString, "user2 message user1, user4 " + genRandomString, Array(user1))
//            message.addResponse(user2, "user2 response1 " + genRandomString)
//            message.addResponse(user2, "user2 response2 " + genRandomString)
//
//            user1.getMessageNotifications(0, 10).length must beGreaterThan(0)
//        }
//
//        def unreadMessageNotifCount = {
//            user1.getUnreadMessageNotificationCount must beGreaterThan(0)
//        }
//
//        def markAllMessageNotifAsRead = {
//            user1.markAllMessageNotifAsRead()
//            user1.getUnreadMessageNotificationCount must beEqualTo(0)
//        }
//    }
//}
