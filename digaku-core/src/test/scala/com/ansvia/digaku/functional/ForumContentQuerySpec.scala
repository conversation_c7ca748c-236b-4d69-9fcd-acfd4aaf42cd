package com.ansvia.digaku.functional

import com.ansvia.digaku._
import com.ansvia.digaku.model.Article
import com.ansvia.digaku.se.SearchEngineTest
import com.ansvia.digaku.utils.SortDirection
import org.specs2.mutable.Specification
import org.specs2.specification.Scope

/**
 * Author: robin (<EMAIL>)
 */

class ForumContentQuerySpec extends Specification with DigakuTest with BeforeAllAfterAllMutable with SearchEngineTest[BeforeAllAfterAllMutable] {

    sequential


    "Forum content query" should {
        "basic functional for listing of articles" in new BasicCtx {
            f1.contentQuery()
                .get(0,10).entries must containAllOf(Seq(article1, article2, article3))
        }
        "sorting using views" in new SortingCtx {

            override def modify(){
                article3.incrementViewsBy(3)
                article2.incrementViewsBy(5)
                reindex()
            }

            test("views")

        }
        "sorting using responses" in new SortingCtx {

            override def modify(){
                article3.incrementResponseCount(10)
                article2.incrementResponseCount(25)
                reindex()
            }

            test("responses")

        }
        "sorting using rating" in new SortingCtx {

            override def modify(){
                article3.incrementRating(5)
                article2.incrementRating(6)
                reindex()
            }

            test("rating")

        }
    }


//    def beforeAll(){
//        println("setup search engine.")
//        val se = new EmbeddedElasticSearchEngine(testIndexDir)
//        Digaku.engine.asInstanceOf[DigakuTestEngine].setSearchEngine(se)
//        se.ensureIndicesCreated()
//
//        // set flag no state agar tidak perlu menyimpan state ketika testing
//        SearchEngineIndexWorker.noState = true
//    }
//
//    def afterAll(){
//        println("restore search engine.")
//        Digaku.engine.asInstanceOf[DigakuTestEngine].setSearchEngine(NopSearchEngine)
//    }

    object BasicStaticData {
        val f1 = genForum
        val u1 = genUser

        f1.addMembers(u1)

        val article1 = Article.create(u1, "hallo forum satu", "ini adalah thread untuk testing", "", f1)
        val article2 = Article.create(u1, "hallo forum dua", "ini adalah thread untuk testing #2", "", f1)
        val article3 = Article.create(u1, "hallo forum tiga", "ini adalah thread untuk testing #3", "", f1)
    }

    trait BasicCtx extends Scope {

        val f1 = BasicStaticData.f1

        val article1 = BasicStaticData.article1
        val article2 = BasicStaticData.article2
        val article3 = BasicStaticData.article3

        Thread.sleep(2000)
    }

    trait SortingCtx extends BasicCtx {

        def modify()

        protected def reindex(){
            Digaku.engine.searchEngine.indexArticle(article3)
            Digaku.engine.searchEngine.indexArticle(article2)
            Thread.sleep(2000)
        }

        def test(sortingKey:String) = {

            modify()

            val rv = f1.contentQuery()
                .setSorting(sortingKey, SortDirection.DESC)
                .get(0, 10)

            rv.entries must contain(article2, article3, article1).inOrder
        }


    }


}
