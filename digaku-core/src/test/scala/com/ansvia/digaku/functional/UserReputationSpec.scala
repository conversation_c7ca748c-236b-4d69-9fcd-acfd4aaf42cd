/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 *
 */

package com.ansvia.digaku.functional

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.exc.{AlreadyExistsException, PermissionDeniedException, LimitationReachedException}
import com.ansvia.digaku.model.{Article, Reputation}
import com.ansvia.digaku.utils.DateUtils
import org.joda.time.DateTime
import org.specs2.mock.Mockito
import org.specs2.mutable.{BeforeAfter, Specification}
import org.specs2.specification.Scope

/**
 * Author: robin (<EMAIL>)
 */

class UserReputationSpec extends Specification with DigakuTest with Mockito {


    "User reputation functionality" should {
        "user can give reputation for post creator" in new Ctx {

            val userx = genUser
            ch1.addMembers(userx)
            val post1 = Article.create(userx, "title","content", "", ch1)

            u1.giveReputation(post1, Reputation.Good, "cool bro")

            userx.getReputationList().toList must contain((1, "cool bro")).only
        }
        "user can give reputation for response creator" in new Ctx {
            val userx = genUser
            ch1.addMembers(userx)

            val post1 = Article.create(u2, "title","content", "", ch1)
            val resp = post1.addResponse(userx, "aku response nih")

            u1.giveReputation(resp, Reputation.Good, "aku kasih reputation di response nih")

            userx.getReputationList().toList must contain((1, "aku kasih reputation di response nih")).only
        }
        "can't give reputation to the same user twice before rotate to other 20 users" in new Ctx {

            val post1 = Article.create(u2, "title 1","content 1", "", ch1)

            u1.giveReputation(post1, Reputation.Good, "")
            u1.giveReputation(post1, Reputation.Good, "") must throwAn[LimitationReachedException]
        }
        "can't give to another user in range of time even the other is not in previous 20 list" in new Ctx {
            val u3 = genUser

            val post1 = Article.create(u2, "title 1","content 1", "", ch1)

            u1.giveReputation(post1, Reputation.Good, "")
            u1.giveReputation(post1, Reputation.Good, "") must throwAn[LimitationReachedException]
        }
        "can give reputation to other user after 24 hours" in new Ctx2 {
            val u3 = genUser

            ch1.addMembers(u3)

            val post1 = Article.create(u2, "title 1","content 1", "", ch1)
            val post2 = Article.create(u3, "title 1","content 1", "", ch1)

            u3.incrementPublishedContentCount(500)
            u1.giveReputation(post1, Reputation.Good, "")

            u1.giveReputation(post2, Reputation.Good, "")
            u3.giveReputation(post1, Reputation.Bad, "parah")

            u2.getReputationList().toList must contain((-1, "parah")).only
            u2.getReputationCount must_== 0 // enol karena telah di-kurangi (Bad) oleh u3
        }
        "can't give reputation to the same user even after 24 hours left when giver not rotating yet" in new Ctx2 {
            val u3 = genUser

            val post2 = Article.create(u2, "title 1","content 1", "", ch1)
            val post3 = Article.create(u3, "title 1","content 1", "", ch1)

            u1.giveReputation(post2, Reputation.Good, "")

            u1.giveReputation(post3, Reputation.Good, "")
            u1.giveReputation(post3, Reputation.Bad, "parah") must throwAn[LimitationReachedException]

        }
        "only can give to the same user after 20 rotation" in new Ctx2 {

            val users = for (i <- 1 to 20) yield genUser

            val post2 = Article.create(u2, "title 1","content 1", "", ch1)
            val post3 = Article.create(u2, "title 1","content 1", "", ch1)

            u1.giveReputation(post2, Reputation.Good, "")

            var count = 0
            for (tu <- users){
                count += 1
//                println("count: " + count)
                val post4 = Article.create(tu, "title 1","content 1", "", ch1)
                u1.giveReputation(post4, Reputation.Good, "")
            }

            u1.giveReputation(post3, Reputation.Good, "kasih lagi nih")

            println("u2.getReputationList().toList: ")
            println(u2.getReputationList().toList)

            u2.getReputationList().toList must contain((1, "kasih lagi nih"))


        }
        "user must have more than 500 post to give reputation" in new Ctx2 {
            val u5 = genUser
            val post2 = Article.create(u2, "title 1","content 1", "", ch1)
            u5.giveReputation(post2, Reputation.Bad, "parah") must throwAn[PermissionDeniedException]
        }
        "check user can give reputation"  in new Ctx2 {
            val u6 = genUser
            val u7 = genUser
            u7.incrementPublishedContentCount(500)
            val post1 = Article.create(u1, "title 1","content 1", "", ch1)
            u6.userCanGiveReputation(post1) must beFalse and (
                u7.userCanGiveReputation(post1) must beTrue)
        }
        "Can't give reputation in the same post" in new Ctx2 {
            val users = for (i <- 1 to 20) yield genUser

            val post2 = Article.create(u2, "title 1","content 1", "", ch1)
            val post3 = Article.create(u2, "title 1","content 1", "", ch1)

            u1.giveReputation(post2, Reputation.Good, "")

            var count = 0
            for (tu <- users){
                count += 1
                val post4 = Article.create(tu, "title 1","content 1", "", ch1)
                u1.giveReputation(post4, Reputation.Good, "")
            }

            u1.giveReputation(post2, Reputation.Good, "kasih lagi nih") must throwAn[AlreadyExistsException]
        }
    }

    class Ctx extends Scope {
        val u1 = genUser
        val u2 = genUser

        val ch1 = genForumWithOwner(u2)

        ch1.addMembers(u1)

        // anggap user sudah memiliki 500 post, supaya bisa langsung give reputation
        u1.incrementPublishedContentCount(500)
        u2.incrementPublishedContentCount(500)

        def mockit(){
            // mock time agar bisa ngerubah waktu 24 jam
            val mod = mock[DateUtils]
            var now = new DateTime()
            mod.getCurrentTime() answers { x =>
                now = now.plusHours(24).plusMinutes(1) // + 24 jam 1 menit
                now
            }
            testEngine.dateUtilsMock = mod
        }

        def unmockit(){
            testEngine.resetMock()
        }


    }

    class Ctx2 extends Ctx with BeforeAfter {

        override def before: Any = {
            mockit()
        }

        override def after: Any = {
            unmockit()
        }
    }
}
