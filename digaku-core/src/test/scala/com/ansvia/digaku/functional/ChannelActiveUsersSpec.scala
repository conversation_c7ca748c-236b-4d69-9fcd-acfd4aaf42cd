///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.functional
//
///**
// * Author: robin
// * Date: 1/28/14
// * Time: 6:42 PM
// *
// */
//
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.model.Post
//import com.ansvia.perf.PerfTiming
//
//
//class ChannelActiveUsersSpec extends Specification with DigakuTest with PerfTiming {
//
//     def is = "Group active users should" ^
//         sequential ^
//         "get active users based on publish activity" ! trees.getBasic ^
//         "user with publish activity larger than other should be positioned on top" ! trees.highActivePosTop ^
//         "oredered/scored correctly" ! trees.orderedScoredCorrectly ^
//         "oredered/scored and offseted correctly" ! trees.orderedScoredOffsetedCorrectly ^
//     end
//
//     object trees {
//
//         val u1 = genUser
//         val u2 = genUser
//         val u3 = genUser
//
//         val ch1 = genChannelWithOwner(u1).addMembers(u2, u3)
//
//         def getBasic = {
//
//             Post.createSimple(u1, "hello there", ch1)
//
//             timing("basic"){
//                 (ch1.getActiveUsers(0, 5).toList must contain(u1).only) and
//                     (ch1.getActiveUsersCount() must beEqualTo(1))
//             }
//
//         }
//
//         def highActivePosTop = {
//             Post.createSimple(u2, "aku juga hello", ch1)
//             Post.createSimple(u2, "aku juga hello lagi loh", ch1)
//
//             timing("highActivePosTop"){
//                 (ch1.getActiveUsers(0, 5, cached=false).toList must contain(u2, u1).inOrder) and
//                 (ch1.getActiveUsersCount() must beEqualTo(2))
//             }
//         }
//
//
//         def orderedScoredCorrectly = {
//             Post.createSimple(u2, "aku juga hello #3", ch1)
//             Post.createSimple(u3, "aku juga hello #1", ch1)
//             Post.createSimple(u3, "aku juga hello #2", ch1)
//
//             timing("ordered-scored"){
//                 ch1.getActiveUsers(0, 5, cached=false).toList must contain(u2, u3, u1).inOrder
//             }
//         }
//
//         def orderedScoredOffsetedCorrectly = {
//             ch1.getActiveUsers(0, 2, cached=false).toList must contain(u2, u3).only.inOrder
//         }
//
//
//
//     }
//
// }
