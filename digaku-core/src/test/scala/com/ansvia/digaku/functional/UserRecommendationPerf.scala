/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.functional
//
///**
// * Author: robin
// * Date: 1/7/14
// * Time: 1:31 PM
// *
// */
//
//
//import com.ansvia.digaku.model.User
//import sperformance.dsl.PerformanceDSLTest
//import sperformance.Keys
//import com.ansvia.digaku.DigakuPerfTest
//import com.ansvia.digaku.Types._
//import scala.collection.JavaConversions._
//import com.ansvia.graph.BlueprintsWrapper._
//import com.tinkerpop.gremlin.Tokens.T
//import com.ansvia.digaku.model.Label._
//
//
//class UserRecommendationPerf extends PerformanceDSLTest with DigakuPerfTest {
//
//
//    performance of "User Recommendation" in {
//        val u = genUser
//        val mu = getMasterUser()
//        u.support(mu)
//
//        having attribute (Keys.WarmupRuns -> 3) in {
//            having attribute (Keys.TestRuns -> 3) in {
//                measure method "m1" in {
//
//                    withSize upTo 1000 withSetup { size =>
//                        size
//                    } run { size =>
//                        val recCount = u.getRecommendation(0, 10).size
//                        println("recCount: " + recCount)
//                    }
//
//                }
//                measure method "m2" in {
//
//                    withSize upTo 1000 withSetup { size =>
//                        size
//                    } run { size =>
//
//                        val recCount = u.getVertex.pipe
//                            .out(SUPPORT)
//                            .out(SUPPORT)
//                            .groupCount.cap()
//                            .orderMap(T.decr)
//                            .range(0, 20)
//                            .asInstanceOf[GremPipeVertex]
//                            .iterator().flatMap(_.toCC[User]).size
//
//                        println("recCount: " + recCount)
//                    }
//                }
//            }
//        }
//    }
//
//}
