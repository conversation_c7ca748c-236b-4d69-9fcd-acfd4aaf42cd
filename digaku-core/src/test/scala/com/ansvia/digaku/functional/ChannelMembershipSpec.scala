///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.functional
//
///**
// * Author: robin
// *
// */
//
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.exc.{AlreadyExistsException, LimitationReachedException}
//import com.ansvia.digaku.model.{Staff, _}
//import com.ansvia.digaku.notifications.impl.ChannelInviteNotification
//import org.specs2.Specification
//
//class ChannelMembershipSpec extends Specification with DigakuTest {
//
//    def is = "Add and getter staff and member" ^
//        p ^
//        "add data member and get data member for get correctly" ! trees.getMemberChannel ^
//        "get member with relevance" ! trees.getMemberRelevance ^
//        "get data staff for correctly" ! trees.getStaffChannel ^
//        "get staff return Staff model" ! trees.getStafReturnStaff ^
//        "add staff group lebih dari 30 staff" ! trees.staffMoreThan30 ^
//        "add user staff di lebih dari 30 group" ! trees.userToStaffMoreThan30 ^
//        "get staff by name for correctly" ! trees.getStaffByName ^
//        "get staff by id for correctly" ! trees.getStaffById ^
//        "check staff" ! trees.isStaff ^
//        "get attributes" ! trees.getAttributes ^
//        "get member group by name" ! trees.getMemberByName ^
//        "get member group by id" ! trees.getMemberById ^
//        "remove group member" ! trees.removeMember ^
//        "remove group member also remove it staff status" ! trees.removeMemberStaff ^
//        "prevent multiple join" ! trees.preventMultipleJoin ^
//        "set and get staff ability correctly" ! trees.getAndSetStaffAbility ^
//        "check staff ability" ! trees.checkStaffAbility ^
//        "unset staff group correctly" ! trees.removeStaffChannel ^
//        "invite and remove member invitation" ! trees.inviteAndRemove ^
//        "invite and accept member invitation should become member quickly" ! trees.inviteAndAcceptAndBecomeMember ^
//        "invite and reject member invitation should not become member but remove invitation and attention" ! trees.inviteAndRejectAndNeverBecomeMember ^
//        "prevent multiple member invitation" ! trees.preventMultipleInvitation ^
//        "get invited users (includes member)" ! trees.getInvitedUsers ^
//        "remove invitation by user (includes member)" ! trees.removeInvitationByUser ^
//        "get member count" ! trees.getMemberCount ^
//        p ^
////        "group invitations" ^
////        p ^
////        "add and get invitation for member" ! getInvitationChannel.addInviteUser ^
////        "add and get invitation for staff" ! getInvitationChannel.addInviteToBecomeStaff ^
////        "add and get invitation for owner" ! getInvitationChannel.addInviteToBecomeOwner ^
////        p ^
//        end
//
//
//    object trees {
//
//        val ch1name = genForumName
//
//        val userA = genUser
//        val userB = genUser
//        val userC = genUser
//        var users = new Array[User](29)
//        var channels = new Array[Forum](30)
//        var channels2 = new Array[Forum](30)
//
//        userA.increasePoints(15)
////        userC.channelQuota = 70
//
//        val chnla = Forum.create(ch1name, ch1name + " desc", userA)
//
//        for (n <- 0 to 29 - 1) {
//
//            // create 30 user
//            users(n) = User.create("user" + n + "_" + genRandomString, "user" + n + "_" + genRandomString + "@yahoo.com", SexType.MALE, "12/12/1992", "123456")
//
//            // create 30 group dan menambahkan staff 30 user kedalam group
//            channels(n) = Forum.create("chnxx-" + n + "_" + genRandomString, "group desc", userC)
//
//            channels(0).addMembers(users(n))
//            channels(0).addStaff(users(n), "cs", Array.empty[String])
//
//            // menambahkan userA pada 30 channels2(n) ke staffnya
//            channels2(n) = Forum.create("chn2" + n + "_" + genRandomString, "channel2 desc", userC)
//            channels2(n).addMembers(userA)
//            channels2(n).addStaff(userA, "master", Array.empty[String])
//        }
//
//        // add members dari user array
//        chnla.addMembers(users: _*)
//        chnla.addMembers(userB)
//
//        chnla.addStaff(userB, "captain", Array("kick", "delete", "drop"))
//        val staff1 = chnla.getStaffById(userB.getId).get
//        val member1 = chnla.getMemberByUserId(users(0).getId).get
//
//        def getMemberChannel = {
//            //get all member
//            chnla.getMembers(0,31).length > 0 must beTrue
//        }
//
//        def getMemberRelevance = {
//
//            userB.support(users(2))
//            userB.support(users(3))
//
//            val rv = channels(0).getMembers(0, 10, Some(userB))
//
//            (rv.slice(0, 2) must be contain(users(2), users(3)))
//        }
//
//        def getStaffChannel = {
//            channels(0).getStaffs(0,31).length==30 must beTrue
//        }
//
//        def getStafReturnStaff = {
//            channels(0).getStaffs(0, 1).apply(0) must beAnInstanceOf[Staff]
//        }
//
//        def staffMoreThan30 = {
//            channels(0).addStaff(userB, "letu", Array.empty[String]) must throwAn[LimitationReachedException]
//        }
//
//        def userToStaffMoreThan30 = {
//            channels(1).addStaff(userA, "guru", Array.empty[String]) must throwAn[LimitationReachedException]
//        }
//
//        def getStaffByName = {
//            val name = userA.name
//            channels2(0).getStaffByUserName(name).get.name must beEqualTo(userA.name)
//        }
//
//        def isStaff = {
//            chnla.isStaff(userB) must beTrue
//        }
//
//        def getAttributes = {
//            val attr = chnla.getStaffAttribute(userB)
//            attr.title must beEqualTo("captain") and(attr.abilities.toList must contain("delete","drop"))
//        }
//
//        def getAndSetStaffAbility = {
//            channels2(0).setStaffAbility(userA, Array("aaa", "bbb"))
//            channels2(0).getStaffAbility(userA) must beEqualTo(Array("aaa", "bbb"))
//        }
//
//        def checkStaffAbility = {
//            //            println("channels2(0).getStaffAbility(userA): " + channels2(0).getStaffAbility(userA).toList)
//            (channels2(0).hasAbility(userA, "bbb") must beTrue) and
//                (channels2(0).hasAbility(userA, "aaa") must beTrue) and
//                (channels2(0).hasAbility(userA, "zzz") must beFalse)
//        }
//
//        def removeStaffChannel = {
//            channels2(0).unsetStaff(userA)
//            channels2(0).getStaffByUserName(userA.name) must beEqualTo(None)
//        }
//
//        def getStaffById = {
//            staff1.name must beEqualTo(userB.name)
//        }
//
//        def getMemberByName = {
//            val name = users(0).name
//            chnla.getMemberByUserName(Some(name)).get.name must startWith("user0")
//        }
//
//        def getMemberById = {
//            member1.name must startWith("user0")
//        }
//
//        def removeMember = {
//            chnla.removeMembers(users: _*)
//            chnla.removeMembers(userA)
//            chnla.removeMembers(userB)
//            chnla.removeMembers(userC)
//
//            chnla.getMembers(0,31).toList.filter(_.getId != userA.getId) must beEmpty
//        }
//
//        def removeMemberStaff = {
//            val u = genUser
//
//            u.reload()
//            chnla.reload()
//
//            chnla.addMembers(u)
//
//            u.reload()
//            chnla.reload()
//
//            chnla.addStaff(u,"test",Array("tester"))
//
//            val isStaff = chnla.isStaff(u)
//
//            u.reload()
//            chnla.reload()
//
//            chnla.removeMembers(u)
//
//            u.reload()
//            chnla.reload()
//
//            val notMember = !chnla.isMember(u)
//            val notStaff = !chnla.isStaff(u)
//
//            (isStaff and notMember and notStaff)
//
//        }
//
//        def preventMultipleJoin = {
//            val userx = genUser
//
//            chnla.addMembers(userx)
//            chnla.addMembers(userx)
//            chnla.addMembers(userx, userx, userx)
//
//            // userA is owner
//            chnla.getMembers(0, 5).toList must be contain(userx, userA) only
//        }
//
//        def inviteAndRemove = {
//
//            val userC = genUser
//
//            val notMemberBefore = chnla.isMember(userC) must beFalse
//
//            //            chnla.setOwner(userA)
//
//            // add invitation to user
//            val inv = chnla.inviteUser(userA, userC)
//
//            // validate invitation code
//            val invCodeValid = chnla.isInvitationCodeValid(inv.code) must beTrue
//            val invCodeInvalid = chnla.isInvitationCodeValid("123") must beFalse
//
//            // remove invitation
//            chnla.removeInvitation(inv.code)
//            val invCodeInvalidAfter = chnla.isInvitationCodeValid(inv.code) must beFalse
//
//            // add members
//            chnla.addMembers(userC)
//
//            val memberAfter = chnla.isMember(userC) must beTrue
//
//            (notMemberBefore and invCodeValid and invCodeInvalid and invCodeInvalidAfter and memberAfter)
//        }
//
//        def inviteAndAcceptAndBecomeMember = {
//            val userx = genUser
//
//            chnla.inviteUser(userA, userx)
//
//            Thread.sleep(2000)
//
//            val att = userx.getAttentionNotifications(0,1).toSeq
//
//            att.length must beEqualTo(1)
//
//        }
//
//        def inviteAndRejectAndNeverBecomeMember = {
//            val userx = genUser
//
//            chnla.inviteUser(userA, userx)
//
//            Thread.sleep(2000)
//
//            val att = userx.getAttentionNotifications(0,1).toSeq.head.asInstanceOf[ChannelInviteNotification]
//
//            att.reject()
//
//            (chnla.isMember(userx) must beFalse) and
//                (chnla.isInvited(userx, InvitationKind.BECOME_MEMBER) must beFalse) and
//                (chnla.isInvitationCodeValid(att.invitationCode) must beFalse)
//        }
//
//        def preventMultipleInvitation = {
//            val userx = genUser
//
//            // add invitation to user
//            chnla.inviteUser(userA, userx)
//            chnla.inviteUser(userA, userx) must throwAn[AlreadyExistsException]
//        }
//
//
//        def getInvitedUsers = {
//
//            val userD = genUser
//
//            // add invitation to user
//            val inv = chnla.inviteUser(userA, userD)
//
//            chnla.getInvitedUsers(InvitationKind.BECOME_MEMBER, 0, 10).toList must be contain(userD)
//        }
//
//        def removeInvitationByUser = {
//            val userx = genUser
//
//            // add invitation to user
//            chnla.inviteUser(userA, userx)
//
//            val before = chnla.isInvited(userx, InvitationKind.BECOME_MEMBER) must beTrue
//
//            chnla.removeInvitation(userx, InvitationKind.BECOME_MEMBER)
//
//            (before and (chnla.isInvited(userx, InvitationKind.BECOME_MEMBER) must beFalse))
//        }
//
//        def getMemberCount = {
//            val memberCount = chnla.getMemberCount()
//            println("memberCount: " + memberCount)
//
//            val userx = genUser
//            val ch9 = genForumWithOwner(userx)
//
//            println("before addMembers: " + ch9.getMemberCount())
//            ch9.addMembers(userA, userB)
//
//            println("before: " + ch9.getMemberCount())
//            val before = ch9.getMemberCount() must_== 3
//
//            ch9.removeMembers(userA, userB)
//
//            println("after: " + ch9.getMemberCount())
//            val after = ch9.getMemberCount() must_== 1
//
//            before and
//            after
//        }
//
//    }
//
////    object getInvitationChannel {
////        val ch2Name = genForumName
////
////        val user1 = genUser
////        val user2 = genUser
////        val user3 = genUser
////        val user4 = genUser
////
////        val channel1 = Forum.create(ch2Name, ch2Name + " desc", user1)
////
////        def addInviteUser = {
////            val invite = channel1.inviteUser(user1, user2)
////            channel1.getInvitation(user2).get must beEqualTo(invite)
////        }
////
////        def addInviteToBecomeStaff = {
////            val invite = channel1.inviteToBecomeStaff(user1, user4, "Staff", Array("edit-group", "edit-post", "delete-post"))
////            channel1.getInvitation(user4).get must beEqualTo(invite)
////        }
////        def addInviteToBecomeOwner() = {
////            user3.setActivated(true)
////            val invite = channel1.inviteToBecomeOwner(user1, user3)
////            channel1.getInvitation(user3).get must beEqualTo(invite)
////        }
////
////
////    }
//
//
//}
//
