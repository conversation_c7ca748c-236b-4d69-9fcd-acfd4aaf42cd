package com.ansvia.digaku.functional

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.exc.LimitationReachedException
import org.specs2.mutable.Specification
import org.specs2.specification.Scope


/**
 * Author: robin (<EMAIL>)
 */

class SubForumSpec extends Specification with DigakuTest {

    "Sub forum functionality" should {
        sequential

        "add sub forum to forum" in {
            trees.f1.addSubForum(trees.f3)
            trees.f3.isSubForum must beTrue
        }
        "get sub forum count" in {
            trees.f1.getSubForumCount must_== 1
            trees.f2.getSubForumCount must_== 0
        }
        "get sub forum list" in {
            trees.f1.getSubForums.toList must contain(trees.f3)
            trees.f2.getSubForums.toList must beEmpty
            trees.f3.getSubForums.toList must beEmpty
        }
        "get parent" in {
            trees.f3.getParentForum must_== Some(trees.f1)
            trees.f1.getParentForum must beNone
            trees.f2.getParentForum must beNone
        }
        "max sub forum count is 100" in {
            for (i <- 1 to 99){
                trees.f1.addSubForum(genForum)
            }
            // this should be fail
            println("trees.f1.getSubForumCount: " + trees.f1.getSubForumCount)
            trees.f1.getSubForumCount must_== 100
            trees.f1.addSubForum(genForum) must throwAn[LimitationReachedException]
        }
        "can handle sub of sub of sub forum" in {
            /**
             * f1
             *   |
             *   ----> f3
             *          |
             *          -----> f4
             *                  |
             *                  -------> f5
             */
            val f4 = genForum
            val f5 = genForum
            trees.f3.addSubForum(f4)
            f4.addSubForum(f5)

            trees.f2.getParentForum must_== None
            trees.f3.getSubForumCount must_== 1
            f4.getSubForumCount must_== 1
            trees.f3.getSubForums.toList must contain(f4)
            f4.getSubForums.toList must contain(f5)
        }
        "sub-forum listing ordered by it's name ascending by default" in new Ctx {
            val f4 = genForum
            f1.addSubForum(f2)
            f1.addSubForum(f3)
            f1.addSubForum(f4)
            val subForums = f1.getSubForums.toList
            val orderedList = subForums.sortBy(_.name)
            println("orderedList: " + orderedList.toList)
            subForums must haveTheSameElementsAs(orderedList)
        }
    }

    trait Ctx extends Scope {
        val f1 = genForum
        val f2 = genForum
        val f3 = genForum
    }

    object trees extends Ctx

}
