///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.functional
//
///**
// * Author: robin
// *
// */
//
//
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.model.{LinkKind, EmbeddedLink, Post}
//
//class GetOtherPostMayLikeSpec extends Specification with DigakuTest {
//
//    def is = "Post you may like functional test should" ^
//        sequential ^
//            "get post may like based on user who like X post aslo like Y" ! trees.getMayLike ^
//            "able to get only contains some object (link)" ! trees.onlyContainsLink ^
//            "able to get only contains some object (video link)" ! trees.onlyContainsVideoLink ^
//            "able to get only contains some object (pic)" ! trees.onlyContainsPic ^
//            "able to get only contains some object multi" ! trees.onlyContainsMulti ^
//            "able to get post ordered by its score" ! trees.getMayLikeTop ^
//        end
//
//    object trees {
//
//        import Post.contains._
//
//        val u1 = genUser
//        val u2 = genUser
//        val u3 = genUser
//        val ch1 = genChannelWithOwner(u1).addMembers(u2, u3)
//
//        val p1 = Post.createSimple(u1, "post #1", ch1)
//        val p2 = Post.createSimple(u1, "post #2", ch1)
//        val p3 = Post.createSimple(u1, "post #3", ch1)
//        val p4 = Post.createSimple(u2, "post #4", ch1)
//        val p5 = Post.createSimple(u2, "post #5", ch1)
//        val p6 = Post.createSimple(u2, "post #6", ch1)
//        val p7 = Post.createSimple(u2, "post #7", ch1)
//
//        p1.addLike(u2)
//        p2.addLike(u2)
//
//        def getMayLike = {
//            p1.getOtherMayLike(0, 5).toList must contain(p2).only
//        }
//
//        def onlyContainsLink = {
//
//            p4.addLike(u2)
//
//            EmbeddedLink.create("http://www.youtube.com",
//                "Youtube link", LinkKind.TEXT, "", "", Some(p4))
//
//            p1.getOtherMayLike(0, 5, LINK).toList must contain(p4).only
//        }
//
//        def onlyContainsVideoLink = {
//
//            p5.addLike(u2)
//
//            EmbeddedLink.create("http://www.youtube.com/watch?v=oWPm1jT3kwg",
//                "Angel lelga", LinkKind.VIDEO, "", "", Some(p5))
//
//            p1.getOtherMayLike(0, 5, VIDEO_LINK).toList must contain(p5).only
//        }
//
//        def onlyContainsPic = {
//
//            p6.addLike(u2)
//
//            EmbeddedLink.create("http://i1.ytimg.com/vi/RISxGgryX_Y/mqdefault.jpg",
//                "Airbus", LinkKind.PIC, "", "", Some(p6))
//
//            p1.getOtherMayLike(0, 5, PIC).toList must contain(p6).only
//        }
//
//
//        def onlyContainsMulti = {
//
//            EmbeddedLink.create("http://i1.ytimg.com/vi/RISxGgryX_Y/mqdefault.jpg",
//                "Airbus", LinkKind.PIC, "", "", Some(p5))
//            EmbeddedLink.create("http://www.youtube.com/watch?v=oWPm1jT3kwg",
//                "Angel lelga", LinkKind.VIDEO, "", "", Some(p6))
//
//            p1.getOtherMayLike(0, 5, PIC | VIDEO_LINK).toList must contain(p5, p6).only
//        }
//
//        def getMayLikeTop = {
//
//            val u1 = genUser
//            val u2 = genUser
//            val u3 = genUser
//            val u4 = genUser
//            val u5 = genUser
//
//            ch1.addMembers(u1, u2)
//
//            val p1 = Post.createSimple(u1, "post #1", ch1)
//            val p2 = Post.createSimple(u1, "post #2", ch1)
//            val p3 = Post.createSimple(u1, "post #3", ch1)
//            val p4 = Post.createSimple(u2, "post #4", ch1)
//
//            p1.addLike(u2)
//            p1.addLike(u3)
//            p1.addLike(u4)
//            p1.addLike(u5)
//
//            p2.addLike(u2)
//            p3.addLike(u3)
//            p3.addLike(u4)
//            p3.addLike(u5)
//
//            p4.addLike(u2)
//            p4.addLike(u3)
//            p4.addLike(u4)
//
//            p1.getOtherMayLike(0, 5).toList must contain(p4,p3,p2).inOrder
//        }
//
//    }
//
//}
//
