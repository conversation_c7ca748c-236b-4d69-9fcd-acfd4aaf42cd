///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.functional
//
///**
// * Author: robin
// * Date: 12/10/13
// * Time: 3:27 PM
// *
// */
//
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.model.PictureGroup
//
//
//class PictureGroupStreamSpec extends Specification with DigakuTest {
//
//    def is = "PictureGroup should" ^
//        sequential ^
//            "masuk ke stream supporter-nya apabila user create" ! trees.streamed ^
//            "masuk ke stream member-nya group X apabila user create" ! trees.streamed2 ^
//        end
//
//    object trees {
//
//        val u1 = genUser
//        val u2 = genUser
//        val u3 = genUser
//
//        u2.support(u1)
//
//        val ch1 = genChannelWithOwner(u1)
//
//        ch1.addMembers(u3)
//
//        def streamed = {
//
//            // skip aja karena berdasarkan kesepakatan tidak akan ada lagi stream dari user yang di-support.
//            skipped
//
////            val pg = PictureGroup.create("judul #1", u1, ch1)
////
////            val pic1 = Picture.create(u1, "pic #1", ch1)
////            val pic2 = Picture.create(u1, "pic #2", ch1)
////
////            pg.addPictures(pic1, pic2)
////
////            (u2.getStream(0, 10).toList.map(_.content) must contain(pg)) and
////                (u2.getStream(0, 10).toList.filter(_.content == pg).head.content.asInstanceOf[PictureGroup].getPictures.toList must contain(pic1, pic2))
//
//        }
//
//        def streamed2 = {
//
//            val pg = PictureGroup.create("judul #2", u1, ch1)
//
//            u3.getStream(0, 10).toList.map(_.content) must contain(pg)
//
//        }
//
//
//    }
//
//}
//
