/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.functional

/// di-disable dulu untuk sementara

///**
// * Author: nadir, robin
// *
// */
//class ChannelPermissionSpec extends Specification with DigakuTest {
//
//    def is = {
//        sequential ^
//            "Block unblock user harusnya" ^
//            p ^
//            Step(cleanUp()) ^
//            "pada ch1 owner harus bisa post, create event, create picture, dan create response" ! trees.correctly1ChannelPermissionCh1 ^
//            "pada ch1 hanya owner yang harus bisa post, create event, create picture, dan create response" ! trees.correctly2ChannelPermissionCh1 ^
//            "pada ch2 owner dan staff harus mempunyai semua akses terhadap post" ! trees.correctly3ChannelPermissionCh2 ^
//            "pada ch2 selain owner dan staff harus tidak mempunyai semua akses terhadap post" ! trees.correctly4ChannelPermissionCh2 ^
//            "pada ch3 owner, staff dan member harus mempunyai semua akses terhadap post" ! trees.correctly5ChannelPermissionCh3 ^
//            "pada ch3 selain owner, staff dan member harus tidak mempunyai semua akses terhadap post" ! trees.correctly6ChannelPermissionCh3 ^
//            "pada ch4 semua user yang registered harus mempunyai semua akses terhadap post" ! trees.correctly7ChannelPermissionCh4 ^
//            "simple post creation permission bekerja seperti yang diharapkan" ! trees.simplePostPermission ^
//            Step(tearDown()) ^
//            end
//    }
//
//    object trees {
//        val user1 = genUser
//        val user2 = genUser
//        val user3 = genUser
//        val user4 = genUser
//        val user5 = genUser
//
//            user5.setActivated(true)
//
//        val cal = Calendar.getInstance()
//        cal.setTime(new Date())
//        cal.add(Calendar.DATE, 5)
//
//        /**
//         * ch1 hanya owner yang bisa post, create event, create picture, dan create response
//         */
//        val ch1 = genForumWithOwner(user1)
//        // reset all permission group
//        ch1.setWhoCanCreateArticle()
//        ch1.setWhoCanResponses()
//        ch1.save()
//
//        db.commit()
//
////        ch1.setOwner(user1)
//        val post1 = Article.create(user1, "post 11", "content post11", "tags,tags2", ch1)
//
//        /**
//         * ch2 hanya owner dan staff yang bisa post, create event, create picture, dan create response
//         */
//        val ch2 = genForumWithOwner(user1)
//
//        ch2.setWhoCanCreateArticle(ForumPermission.STAFF)
//        ch2.setWhoCanCreateEvents(ForumPermission.STAFF)
//        ch2.setWhoCanResponses(ForumPermission.STAFF)
//        ch2.save()
//
//        /**
//         * ch3 hanya owner dan staff dan member yang bisa post, create event, create picture, dan create response
//         */
//        val ch3 = genForumWithOwner(user1)
//
//        ch3.setWhoCanCreateArticle(ForumPermission.STAFF, ForumPermission.MEMBER)
//        ch3.setWhoCanCreateEvents(ForumPermission.STAFF, ForumPermission.MEMBER)
//        ch3.setWhoCanResponses(ForumPermission.STAFF, ForumPermission.MEMBER)
//        ch3.save()
//
//        /**
//         * ch4 => registered dan owner dan staff dan member bisa post, create event, create picture, dan create response
//         */
//        val ch4 = genForumWithOwner(user1)
//        ch4.setWhoCanCreateArticle(ForumPermission.REGISTERED, ForumPermission.STAFF, ForumPermission.MEMBER)
//        ch4.setWhoCanCreateEvents(ForumPermission.REGISTERED, ForumPermission.STAFF, ForumPermission.MEMBER)
//        ch4.setWhoCanResponses(ForumPermission.STAFF, ForumPermission.MEMBER, ForumPermission.REGISTERED)
//        ch4.save()
//
//        // ch5 => hanya member yang bisa nulis simple post
//        // ch6 => semua registered user bisa nulis simple post
//        val ch5 = genForumWithOwner(user1)
//        val ch6 = genForumWithOwner(user1)
//        ch5.reload()
////        ch5.setWhoCanCreateSimplePost(SubForumPermission.MEMBER)
//        ch6.save()
//        ch6.reload()
////        ch6.setWhoCanCreateSimplePost(SubForumPermission.REGISTERED)
//        ch5.save()
//
//        db.commit()
//
//        /**
//         * owner harus bisa post, create event, create picture, dan create response
//         */
//        def correctly1ChannelPermissionCh1 = {
//            val event1 = Event.create(user1, "event25", "content event25", "yogya", ch1, cal.getTime)
//            val picture1 = Picture.create(user1, "picture25", ch1)
//            val resp1 = post1.addResponse(user1, "response post25")
//            (post1 must beAnInstanceOf[Post]) and
//                (event1 must beAnInstanceOf[Event]) and
//                    (picture1 must beAnInstanceOf[Picture]) and
//                        (resp1 must beAnInstanceOf[Response])
//        }
//
//        /**
//         * ch1 selain owner tidak bisa post, create event, create picture, dan create response
//         */
//        def correctly2ChannelPermissionCh1 = {
//            ch1.setAllPermission("")
//            ch1.addMembers(user3)
//            ch1.addMembers(user4)
//            ch1.addStaff(user4, "programmer", Array(Ability.ALL))
//            (Article.create(user2, "post 26", "content post26", "tags,tags2", ch1) must throwAn[PermissionDeniedException]) and
//            (Event.create(user3, "event26", "content event11", "yogya", ch1,  cal.getTime) must throwAn[PermissionDeniedException]) and
////            (Picture.create(user4, "picture26", ch1) must throwAn[PermissionDeniedException]) and
//            (post1.addResponse(user3, "user4 response2 post26 ") must throwAn[PermissionDeniedException])
//        }
//
//        /**
//         * ch2 owner dan staff harus bisa post, create event, create picture, dan create response
//         */
//        def correctly3ChannelPermissionCh2 = {
//            ch2.addMembers(user4)
//            db.commit()
//            user4.reload()
//            ch2.addStaff(user4, "programmer", Array(Ability.ALL))
//            val post2 = Article.create(user1, "post 26", "content post26", "tags,tags2", ch2)
//            val event1 = Event.create(user1, "event26", "content event11", "yogya", ch2, cal.getTime)
//            val picture1 = Picture.create(user4, "picture26", ch2)
//            val resp2 = post2.addResponse(user4, "response post26")
//            (post2 must beAnInstanceOf[Post]) and
//                (event1 must beAnInstanceOf[Event]) and
//                (picture1 must beAnInstanceOf[Picture]) and
//                (resp2 must beAnInstanceOf[Response])
//        }
//
//        /**
//         * ch2 selain owner dan staff harus tidak bisa post, create event, create picture, dan create response
//         */
//        def correctly4ChannelPermissionCh2 = {
//            val post3 = Article.create(user1, "post 27", "content post27", "tags,tags2", ch2)
//            ch1.addMembers(user3)
//            (Article.create(user2, "post 27", "content post27", "tags,tags2", ch2) must throwAn[PermissionDeniedException]) and
//                (Event.create(user2, "event27", "content event27", "yogya", ch2,  cal.getTime) must throwAn[PermissionDeniedException]) and
//                (Picture.create(user3, "picture27", ch2) must throwAn[PermissionDeniedException]) and
//                (post3.addResponse(user3, "response2 post27") must throwAn[PermissionDeniedException])
//        }
//
//        /**
//         * ch3 owner dan staff dan member yang bisa post, create event, create picture, dan create response
//         */
//        def correctly5ChannelPermissionCh3 = {
//            ch3.addMembers(user3)
//            ch3.addMembers(user4)
//            ch3.addStaff(user4, "programmer", Array(Ability.ALL))
//            val post3 = Article.create(user1, "post 22", "content post22", "tags,tags2", ch3)
//            val event3 = Event.create(user3, "event11", "content event22", "yogya", ch3, cal.getTime)
//            val picture3 = Picture.create(user4, "picture1", ch3)
//            val resp3 = post3.addResponse(user4, "response post22")
//            (post3 must beAnInstanceOf[Post]) and
//                (event3 must beAnInstanceOf[Event]) and
//                (picture3 must beAnInstanceOf[Picture]) and
//                    (resp3 must beAnInstanceOf[Response])
//        }
//
//        /**
//         * ch3 selain owner, staff dan member tidak bisa post, create event, create picture, dan create response
//         */
//        def correctly6ChannelPermissionCh3 = {
//            val post4 = Article.create(user1, "post 23", "content post23", "tags,tags2", ch3)
//            (Article.create(user5, "post 23", "content post23", "tags,tags2", ch3) must throwAn[PermissionDeniedException]) and
//                (Event.create(user5, "event23", "content event23", "yogya", ch3,  cal.getTime) must throwAn[PermissionDeniedException]) and
//                (Picture.create(user5, "picture1", ch3) must throwAn[PermissionDeniedException]) and
//                (post4.addResponse(user5, "response2 post23") must throwAn[PermissionDeniedException])
//        }
//
//        /**
//         * ch4 selain semua harus bisa post, create event, create picture, dan create response
//         */
//        def correctly7ChannelPermissionCh4 = {
//            ch4.addMembers(user3)
//            ch4.addMembers(user4)
//            ch4.addStaff(user4, "programmer", Array(Ability.ALL))
//            val post3 = Article.create(user1, "post 24", "content post24", "tags,tags2", ch4)
//            val event3 = Event.create(user3, "event24", "content event11", "yogya", ch4, cal.getTime)
//            val picture3 = Picture.create(user4, "picture24", ch4)
//            val resp3 = post3.addResponse(user5, "response post24")
//            (post3 must beAnInstanceOf[Post]) and
//                (event3 must beAnInstanceOf[Event]) and
//                (picture3 must beAnInstanceOf[Picture]) and
//                    (resp3 must beAnInstanceOf[Response])
//        }
//
//        def simplePostPermission = {
//
//            // @TODO(robin): benerin ini
//            skipped
//
////            ch5.addMembers(user3)
////
////            (Post.createSimple(user2, "nyoba aja", ch5) must throwAn[PermissionDeniedException]) and
////                (Post.createSimple(user3, "nyoba aja", ch5) must be not throwAn[PermissionDeniedException]) and
////                (Post.createSimple(user3, "nyoba aja", ch6) must be not throwAn[PermissionDeniedException]) and
////                (Post.createSimple(user2, "nyoba aja", ch6) must be not throwAn[PermissionDeniedException])
////
//
//        }
//
//
//    }
//
//}
