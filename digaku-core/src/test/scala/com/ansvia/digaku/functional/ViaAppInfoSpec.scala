/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.functional

/**
 * Author: robin
 * Date: 3/11/14
 * Time: 8:14 PM
 *
 */

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification



class ViaAppInfoSpec extends Specification with DigakuTest {

    def is = "Via app info should" ^
        sequential ^
        "set via app" ! trees.setViaApp ^
    end

    object trees {

        val u1 = genUser
        val app = genAppWithOwner(u1)
        val app2 = genAppWithOwner(u1)


        def setViaApp = {

            // @TODO(robin): benerin ini
            skipped

//            val post = Post.createSimple(u1,"hello",u1)
//            post.setPostVia(app)
//
//            val post2 = Post.getPostById(post.getId).get
//
//            val rv1 = post2.getPostVia must_== Some(app)
//
//            val resp = post.addResponse(u1, "hello lagi")
//            resp.setPostVia(app)
//
//            val rv2 = Response.getById(resp.getId).get.getPostVia must_== Some(app)
//            val rv3 = Response.getById(resp.getId).get.getPostVia must not equalTo Some(app2)
//
//            rv1 and rv2 and rv3

        }

    }

}

