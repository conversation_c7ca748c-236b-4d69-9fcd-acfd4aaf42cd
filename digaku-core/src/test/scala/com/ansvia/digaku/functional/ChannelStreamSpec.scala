///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.functional
//
///**
// * Author: robin
// *
// */
//
//
//import com.ansvia.digaku.{RecordChannelGeneratorHelper, DigakuTest}
//import org.specs2.Specification
//import com.ansvia.digaku.model._
//
//
//class ChannelStreamSpec extends Specification with DigakuTest with RecordChannelGeneratorHelper {
//     def is = "Group stream functionality should be able to" ^
//         sequential ^
//            "get article stream" ! trees.getArticleStream ^
//            "get all post stream" ! trees.getAllPostStream ^
//            "get post with MaxId" ! trees.getAllPostStreamMaxId ^
//            "get post with MaxId not inclusive" ! trees.getAllPostStreamMaxIdNotInclusive ^
//            "get post with SinceId" ! trees.getAllPostStreamSinceId ^
//     end
//
//     object trees {
//
//         val u1 = genUser
//         val ch1 = genForumWithOwner(u1)
//
//         val p1 = Article.create(u1, "judul 1", "content 1", "", ch1)
//         ch1.reload()
//
//         def getArticleStream = {
//             ch1.getStream[Article](0, 10).toList must contain(p1).only
//         }
//
//         def getAllPostStream = {
//             // @TODO(robin): benerin ini
//             skipped
////
////             val pic1 = Picture.create(u1, "photo #1", ch1)
////
////             val rv = ch1.getStreamAll(None, None, 0, 10).toList
////
////             rv.map(_.content) must contain(p1, p2, pic1)
//         }
//
//         def getAllPostStreamMaxId = {
//             // @TODO(robin): benerin ini
//             skipped
//
////             val pic1 = Picture.create(u1, "photo #1", ch1)
////             val pic2 = Picture.create(u1, "photo #2", ch1)
////             val pic3 = Picture.create(u1, "photo #3", ch1)
////
////             val rv = ch1.getStreamAll(Some(pic1.getId), None, 0, 10).toList
////
////             rv.map(_.content.getId) must contain(p1.getId, p2.getId)
////             rv.map(_.content.getId) must not contain(pic2.getId, pic3.getId)
//         }
//
//         def getAllPostStreamMaxIdNotInclusive = {
//             // @TODO(robin): benerin ini
//             skipped
//
////             val pic1 = Picture.create(u1, "photo #b1", ch1)
////             val pic2 = Picture.create(u1, "photo #b2", ch1)
////             val pic3 = Picture.create(u1, "photo #b3", ch1)
////
////             val rv = ch1.getStreamAll(Some(pic1.getId), None, 0, 10).toList
////
////             // harusnya stream yang didapatkan tidak termasuk post yang maxId-nya (non-inclusive)
////             rv.map(_.content.getId) must contain(p1.getId, p2.getId)
////             rv.map(_.content.getId) must not contain(pic1.getId, pic2.getId, pic3.getId)
//         }
//
//
//         def getAllPostStreamSinceId = {
//
//             // @TODO(robin): benerin ini
//             skipped
//
////             val pic1 = Picture.create(u1, "photo #1", ch1)
////             val pic2 = Picture.create(u1, "photo #2", ch1)
////             val pic3 = Picture.create(u1, "photo #3", ch1)
////
////             val rv = ch1.getStreamAll(None, Some(pic1.getId), 0, 10).toList
////
////             rv.map(_.content) must not contain(p1, p2, pic1)
////             rv.map(_.content) must contain(pic2, pic3)
//         }
//
//
//
//
//     }
//
// }
//
