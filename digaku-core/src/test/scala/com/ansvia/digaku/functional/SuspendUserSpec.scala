/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.functional

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import org.specs2.specification.Step

/**
 * Author: nadir
 *
 */
class SuspendUserSpec extends Specification with DigakuTest {

    def is = {
        sequential ^
            "Block unblock user harusnya" ^
            p ^
            "Tidak bisa nulis post." ! trees.cantCreatePost ^
            "Tidak bisa nulis respon." ! trees.cantResponsePost ^
            "Tidak bisa nge-like." ! trees.cantLikePost ^
            end
    }

    object trees {
        val user1 = genUser
        user1.setLocked(true)
        user1.save()

        val user2 = genUser
        val user3 = genUser

        def cantCreatePost = {
            // @TODO(robin): benerin ini
            skipped
//            (Post.createSimple(user1, "post 1", user1) must throwAn[PermissionDeniedException]) and
//            (Article.create(user1, "article1", "content article 1", "tags", user1) must throwAn[PermissionDeniedException]) and
//            (Picture.create(user1, "pict1", user1) must throwAn[PermissionDeniedException]) and
//            (Event.create(user1, "event 1", "content event 1", "yogya", user1, new Date()) must throwAn[PermissionDeniedException])
        }

        def cantResponsePost = {
            // @TODO(robin): benerin ini
            skipped
//            val post = Post.createSimple(user2, "post ke 2", user2)
//            post.addResponse(user1, "response user1") must throwA[PermissionDeniedException]
        }

        def cantLikePost = {
            // @TODO(robin): benerin ini
            skipped
//            val post = Post.createSimple(user2, "post ke 3", user2)
//            post.addLike(user1) must throwAn[PermissionDeniedException]
        }

    }

}
