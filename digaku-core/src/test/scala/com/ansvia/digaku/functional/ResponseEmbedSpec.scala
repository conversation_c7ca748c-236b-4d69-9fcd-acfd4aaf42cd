/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 *
 */

package com.ansvia.digaku.functional

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.exc.LimitationReachedException
import com.ansvia.digaku.model.{EmbeddedFile, Response}
import org.specs2.mutable.Specification
import org.specs2.specification.Scope

/**
 * Author: robin (<EMAIL>)
 */

class ResponseEmbedSpec extends Specification with DigakuTest {

    import com.ansvia.digaku.utils.RichString._

    sequential

    "Response functionality for embedding" should {
        "has no embedded object by default" in new Ctx {
            response.embeddedObjectCount must_== 0
        }
        "able to embed embeddable object" in new Ctx2 {
            response.embeddedObjectCount must_== 1
        }
        "get embedded objects" in new Ctx2 {
            response.getEmbeddedObjects must contain(file)
        }
        "able to remove embedded object" in new Ctx2 {
            response.removeEmbeddedObject(file)
            response.embeddedObjectCount must_== 0
            response.getEmbeddedObjects must beEmpty
        }
        "max embedded object is 100" in new Ctx {
            val file = EmbeddedFile("file.txt","http://www.file.com/file.txt","abc".md5,5000)
            for (i <- 1 to 100){
                file.save()
                response.addEmbeddedObject(file)
            }
            // must be fail
            response.addEmbeddedObject(file) must throwAn[LimitationReachedException]
        }
    }

    class Ctx extends Scope {
        val u1 = genUser
        val response = Response.create(u1, "some response")
    }

    class Ctx2 extends Ctx {
        val file = EmbeddedFile("file.txt","http://www.file.com/file.txt","abc".md5,5000)
        file.save()
        println("file: " + file)
        response.addEmbeddedObject(file)
    }


}
