/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

//package com.ansvia.digaku.functional
//
///**
// * Author: robin
// * Date: 12/19/13
// * Time: 2:55 PM
// *
// */
//
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.live.SponsorPostBroadcastLive
//import com.ansvia.digaku.model.{User, SexType, SponsorPost}
//import com.ansvia.digaku.utils.DateUtils
//import java.util.Calendar
//import java.text.SimpleDateFormat
//
//
//class SponsorPostBroadcastLiveSpec extends Specification with DigakuTest {
//    def is = "SponsorPost broadcast live should" ^
//        sequential ^
//        "broadcast to random user" ! trees.random ^
//        "don't broadcast double" ! trees.dontDouble ^
//        "restricted by gender/sex" ! trees.restrictGender ^
//        "restricted by min/max age" ! trees.restrictAge ^
//        end
//
//    object SPBL extends SponsorPostBroadcastLive {
//        /**
//         * usable for mocking on test.
//         * @return
//         */
//        override def getUserCount: Long = 100
//
//        override def getUserLimit: Int = 6 // agar gak kena limitasi
//
//        // no random for unittest, or test will be failing
//        override val random = false
//    }
//
//    object trees {
//
//
//        private def _genUser(gender:Int, bd:String="01/01/1986") = {
//            val userName = genUserName
//            val u = User.create(userName, userName + "@mail.com", gender, bd, "123456")
//            u.setActivated(true)
//            u.reload()
//            u
//        }
//
//        private def genStEt() = {
//            val st = Digaku.engine.dateUtils.now
//            val cal = Calendar.getInstance()
//            cal.setTime(st)
//            cal.add(Calendar.DATE, 30)
//            val et = cal.getTime
//            (st, et)
//        }
//
//        private def genSp() = {
//            val (st, et) = genStEt()
//            SponsorPost.create(genRandomString, genRandomString,
//                "goto-url||http://digaku.com", st, et, active = true)
//        }
//
//        private val users = for (i <- 1 to 100)
//            yield genUser
//
//        private val sps = for (i <- 1 to 100)
//            yield genSp()
//
//        //         val rsp = SponsorPostBroadcastLive.pickOneSponsor()
//
//
//        def random = {
//            val vs = SponsorPostBroadcastLive.buildSponsorVertices()
//            val rsps = for (i <- 1 to 10)
//                yield SponsorPostBroadcastLive.pickOneSponsor(vs)
////            rsps.flatMap(x => x).foreach(x => println("sp: " + x))
//            rsps.distinct.length must beGreaterThan(2)
//        }
//
//        def dontDouble = {
//            SPBL.run()
//            SPBL.run()
//            SPBL.run()
//            SPBL.run()
//            SPBL.run()
//            SPBL.run()
//
//            val usersHasSponsor = users.filter(_.getStream(0, 10).toList
//                .count(_.content.isInstanceOf[SponsorPost]) > 0)
//
//            val streams = usersHasSponsor.head.getStream(0, 10).toList
//
//            (sps must contain(streams(0).content)) and
//                (streams.length must_== 1)
//        }
//
//
//        def restrictGender = {
//            import com.ansvia.graph.BlueprintsWrapper._
//
//            println("in restrictGender...")
//
//            // clear all data
//            SponsorPost.clear()
//            User.rootVertex.pipe.out(User.rootVertexLabel).remove()
//            db.commit()
//
//            val (st, et) = genStEt()
//            val sp = SponsorPost.create(genRandomString, genRandomString, "goto-group||test", st, et, true)
//            sp.genderTarget = SexType.MALE
//            sp.save()
//            db.commit()
//
//            sp.reload()
//
//
//            val u1 = _genUser(SexType.MALE)
//            val u2 = _genUser(SexType.FEMALE)
//            val u3 = _genUser(SexType.MALE)
//            val u4 = _genUser(SexType.FEMALE)
//
//            db.commit()
//
//            SPBL.run()
//            SPBL.run()
//            SPBL.run()
//
//            db.commit()
//            u1.reload()
//            u2.reload()
//            u3.reload()
//            u4.reload()
//
//            (u1.getStream(0, 10).toList.count(_.content == sp) must beGreaterThan(0)) and
//            (u3.getStream(0, 10).toList.count(_.content == sp) must beGreaterThan(0)) and
//            (u2.getStream(0, 10).toList.count(_.content == sp) must beEqualTo(0)) and
//            (u4.getStream(0, 10).toList.count(_.content == sp) must beEqualTo(0))
//        }
//
//
//        def restrictAge = {
//
//            println("in restrictAge...")
//
//            import com.ansvia.graph.BlueprintsWrapper._
//
//            // clear all data
//            SponsorPost.clear()
//            User.rootVertex.pipe.out(User.rootVertexLabel).remove()
//            db.commit()
//
//            val (st, et) = genStEt()
//            val sp = SponsorPost.create(genRandomString, genRandomString, "goto-group||test", st, et, true)
//            sp.minAge = 18
//            sp.maxAge = 30
//            sp.save()
//            db.commit()
//
//            sp.reload()
//
//
//            val tf = new SimpleDateFormat("dd/MM/yyyy")
//            val cal = Calendar.getInstance()
//            cal.setTime(Digaku.engine.dateUtils.now)
//
//            cal.add(Calendar.YEAR, -16)
//            val youngBd = tf.format(cal.getTime)
//
//            cal.setTime(Digaku.engine.dateUtils.now)
//            cal.add(Calendar.YEAR, -18)
//            val adultBd = tf.format(cal.getTime)
//
//            cal.setTime(Digaku.engine.dateUtils.now)
//            cal.add(Calendar.YEAR, -31)
//            val oldBd = tf.format(cal.getTime)
//
//            val u1 = _genUser(SexType.MALE, youngBd)
//            val u2 = _genUser(SexType.FEMALE, adultBd)
//            val u3 = _genUser(SexType.MALE, youngBd)
//            val u4 = _genUser(SexType.FEMALE, adultBd)
//            val u5 = _genUser(SexType.FEMALE, oldBd)
//
//            db.commit()
//
//            println("user generated: " + u1 + ", " + u2 + ", " + u3 + ", " + u4 + ", " + u5)
//
//            SPBL.run()
//            SPBL.run()
//            SPBL.run()
//
//            db.commit()
//            u1.reload()
//            u2.reload()
//            u3.reload()
//            u4.reload()
//            u5.reload()
//
//            (u1.getStream(0, 10).toList.count(_.content == sp) must beEqualTo(0)) and
//                (u2.getStream(0, 10).toList.count(_.content == sp) must beGreaterThan(0)) and
//                (u3.getStream(0, 10).toList.count(_.content == sp) must beEqualTo(0)) and
//                (u4.getStream(0, 10).toList.count(_.content == sp) must beGreaterThan(0)) and
//                (u5.getStream(0, 10).toList.count(_.content == sp) must beEqualTo(0))
//        }
//
//
//
//    }
//
//}
//
