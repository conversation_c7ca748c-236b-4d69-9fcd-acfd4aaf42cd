/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.functional

import com.ansvia.digaku.{BeforeAllAfterAllImmutable, DigakuTest}
import com.ansvia.digaku.model._
import com.ansvia.digaku.se.SearchEngineTest
import org.specs2.Specification
import org.specs2.specification.{Fragments, Step}

/**
 * Author: ubai, robin
 *
 */
class BlockedSearchSpec extends Specification with BeforeAllAfterAllImmutable with DigakuTest with SearchEngineTest[BeforeAllAfterAllImmutable] {

    def is: Fragments = {
        sequential ^
        "blocked search should" ^
            p ^
            "get blocked article" ! trees.getBlockedArticle ^
//            "get blocked video" ! trees.getBlockedVideo ^
            "get blocked forum" ! trees.getBlockedForum ^
//            "get blocked picture" ! trees.getBlockedPicture ^
            end
    }


    object trees {

        import com.ansvia.graph.BlueprintsWrapper._

        // clean up / drop existing channels apabila ada di db
        Forum.rootVertex.pipe.out(Forum.rootVertexLabel).remove()
        db.commit()

        val u = genUser
        val ch = genForum
//        ch.setAllPermission(ForumPermission.ALL)

        u.reload()
        ch.reload()

        def getBlockedForum = {
            val user1 = genUser
//            val channel1 =  genChannelWithOwner(user1)
//            val channel2 = genChannelWithOwner(user1)
//            val channel3 = genChannelWithOwner(user1)
//            val channel4 = genChannelWithOwner(user1)
            val channel5 = genForumWithOwner(user1)
            val channel6 = genForumWithOwner(user1)
            val channel7 = genForumWithOwner(user1)
            val channel8 = genForumWithOwner(user1)
            val channel9 = genForumWithOwner(user1)

            channel5.setBlocked(true, user1)
            Thread.sleep(1000L)
            channel6.setBlocked(true, user1)
            Thread.sleep(1000L)
            channel7.setBlocked(true, user1)
            Thread.sleep(1000L)
            channel8.setBlocked(true, user1)
            Thread.sleep(1000L)
            channel9.setBlocked(true, user1)
            Thread.sleep(1000L)


            Forum.getBlockedChannels(0, 10).entries.toList must contain(channel5, channel6, channel7, channel8, channel9).only

        }


//        def getBlockedPicture = {
//            val pic1 =  Picture.create(u, "picture 1", ch)
//
//            val pic2 =  Picture.create(u, "picture 2", ch)
//
//            val pic3 =  Picture.create(u, "picture 3", ch)
//
//            val pic4 =  Picture.create(u, "picture 4", ch)
//
//            val pic5 =  Picture.create(u, "picture 5", ch)
//
//            val pic6 =  Picture.create(u, "picture 6", ch)
//
//            pic4.setBlocked(true, u)
//            Thread.sleep(1000L)
//
//            pic5.setBlocked(true, u)
//            Thread.sleep(1000L)
//
//            pic6.setBlocked(true, u)
//            Thread.sleep(1000L)
//
//            val ars = Picture.getBlockedPictures(0, 10).entries.toList
//            ars must contain(pic4, pic5,  pic6).only
//
//        }


//        def getBlockedVideo = {
//            val vid1 = Post.createSimple(u, "https://www.youtube.com/watch?v=sfuGhGNW8qE", ch)
//            EmbeddedLink.create("https://www.youtube.com/watch?v=sfuGhGNW8qE", "balawan",
//                LinkKind.VIDEO, "", "desc", Some(vid1))
//
//            val vid2 = Post.createSimple(u, "https://www.youtube.com/watch?v=aAb7hSCtvGw", ch)
//            EmbeddedLink.create("https://www.youtube.com/watch?v=aAb7hSCtvGw", "API",
//                LinkKind.VIDEO, "", "desc", Some(vid2))
//
//            val vid3 = Post.createSimple(u, "https://www.youtube.com/watch?v=34_xXMXaFcc", ch)
//            EmbeddedLink.create("https://www.youtube.com/watch?v=34_xXMXaFcc", "6String",
//                LinkKind.VIDEO, "", "desc", Some(vid3))
//
//            val vid4 = Post.createSimple(u, "http://www.youtube.com/watch?v=NijTv846Nxo", ch)
//            EmbeddedLink.create("http://www.youtube.com/watch?v=NijTv846Nxo", "API",
//                LinkKind.VIDEO, "", "desc", Some(vid4))
//
//            val vid5 = Post.createSimple(u, "http://www.youtube.com/watch?v=8Z0VN7wHJms", ch)
//            EmbeddedLink.create("http://www.youtube.com/watch?v=8Z0VN7wHJms", "API",
//                LinkKind.VIDEO, "", "desc", Some(vid5))
//
//            val vid6 = Post.createSimple(u, "http://www.youtube.com/watch?v=O1qIb0asLdo", ch)
//            EmbeddedLink.create("http://www.youtube.com/watch?v=O1qIb0asLdo", "API",
//                LinkKind.VIDEO, "", "desc", Some(vid6))
//
//            vid4.setBlocked(true, u)
//            Thread.sleep(3000L)
//
//            vid5.setBlocked(true, u)
//            Thread.sleep(3000L)
//
//            vid6.setBlocked(true, u)
//            Thread.sleep(3000L)
//
//            val videos = Post.getBlockedVideos(0, 10).entries.toList
//            videos must contain(vid4, vid5,  vid6).only
//        }


        def getBlockedArticle = {
            val ar1 = Article.create(u, "ar1", "ar1 content", "ar1 tags", ch)

            val ar2 = Article.create(u, "ar2", "ar2 content", "ar2 tags", ch)

            val ar3 = Article.create(u, "ar3", "ar3 content", "ar3 tags", ch)

            val ar4 = Article.create(u, "ar4", "ar4 content", "ar4 tags", ch)

            val ar5 = Article.create(u, "ar5", "ar5 content", "ar5 tags", ch)

            val ar6 = Article.create(u, "ar6", "ar6 content", "ar6 tags", ch)

            ar4.setBlocked(true, u)
            Thread.sleep(1000L)

            ar5.setBlocked(true, u)
            Thread.sleep(1000L)

            ar6.setBlocked(true, u)
            Thread.sleep(1000L)


            val ars = Post.getBlockedArticles(0, 10).entries.toList
            ars must contain(ar4, ar5,  ar6).only
        }
    }

}
