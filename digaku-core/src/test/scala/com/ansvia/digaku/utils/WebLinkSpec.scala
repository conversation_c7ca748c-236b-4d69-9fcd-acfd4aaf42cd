/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import org.specs2.Specification
//import com.ansvia.digaku.utils.WebLink

/**
 * Author: alamybs, robin
 * Date: 11/16/13
 * Time: 2:58 PM
 * 
 */
class WebLinkSpec extends Specification {
    def is = "Check web link" ^
        p ^
        "check the mobile video url is video url" ! trees.isMobileUrlVideo
        end

    object trees {
        val urlMobileVideo = List(
            "http://m.youtube.com/watch?v=lfdFRoMK0HE",
            "http://vimeo.com/m/99751349"
        )
        def isMobileUrlVideo = {
            var listIsVideo = List.empty[Boolean]

            urlMobileVideo.map { url =>
                if (WebLink.isVideoLink(url))
                    listIsVideo +:= true
            }

            listIsVideo.length mustEqual(urlMobileVideo.length)

        }

    }
}
