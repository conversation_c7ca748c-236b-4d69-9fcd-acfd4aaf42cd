/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import org.specs2.Specification

/**
 * Author: robin
 * Date: 2/25/13
 * Time: 12:06 AM
 * 
 */
class TextExtractorSpec extends Specification {
    def is =
        "TextExtractor URL should" ^
        p ^
            "extract basic url correctly" ! urlExtractor.extractBasicUrl ^
            "extract url with +" ! urlExtractor.extractUrlWithPlus ^
            "extract url contains tilde ~" ! urlExtractor.extractTilde ^
            "extract url contains bang !" ! urlExtractor.extractBang ^
            "extract complex url" ! urlExtractor.extractComplexUrl ^
            "extract complex url #2" ! urlExtractor.extractComplexUrl2 ^
            "extract url textile style" ! urlExtractor.extractLinkFromTextile ^
        p ^
        "TextExtractor user names should" ^
        p ^
            "extract basic user names" ! userNameExtractor.extractBasic ^
            "not extract invalid text e.g: y@ahoo" ! userNameExtractor.noInvalid ^
        p ^
        "Html TextExtractor user ids should" ^
        p^
            "extract basic user ids" ! userIdExtractor.extractBasic ^
            "not extract invalid html text e.g: <span class=\"mentioned\" data-user-id=\"1234567\"></span>" ! userIdExtractor.noValid ^
        p^
        "TextExtractor group names should" ^
            "extract basic group names" ! channelNameExtractor.extractBasic ^
            "not extract invalid text e.g: #e" ! channelNameExtractor.invalid ^
        p ^
        "TextExtractor pic links should" ^
            "extract commons pic link" ! picLink.extractCommons ^
            "extract textile based pic link" ! picLink.extractTextile ^
            "check is text has pic link" ! picLink.check ^
        end

    object urlExtractor {
        private val text1 =
            """
              |hello http://www.ansvia.com/hello
              |ini url loh https://cdn01.digaku.com/09/12/22/img.png
              |dan ini bukan url loh: http://..mem.wow
              |ini url dengan karakter ; : https://cdn01.digaku.com/09/12/22/img.png?test=123;own=123
            """.stripMargin

        private val text2 =
            """
              |mem http://api.test.com/itu/tags/motor+mobil mem
              | :D http://api.sub.22.test2.com/itu/tags/motor,mobil.-.html mem
            """.stripMargin

        private lazy val rvText2 = TextExtractor.extractLinks(text2).toList

        def extractBasicUrl = {
            val rv = TextExtractor.extractLinks(text1).toList
            (rv must be contain("http://www.ansvia.com/hello")) and
                (rv must be contain("https://cdn01.digaku.com/09/12/22/img.png")) and
                (rv must be contain("https://cdn01.digaku.com/09/12/22/img.png?test=123;own=123")) and
                (rv must not contain("http://..mem.wow"))
        }

        def extractUrlWithPlus = {
            (rvText2 must be contain("http://api.test.com/itu/tags/motor+mobil")) and (rvText2.length must beEqualTo(2))
        }

        def extractTilde = {
            TextExtractor.extractLinks("ini http://url.domain.com/~ada-tilde-nya+loh~").toList must_== List("http://url.domain.com/~ada-tilde-nya+loh~")
        }

        def extractBang = {
            TextExtractor.extractLinks("ini http://url.domain.com/ada-tanda-seru-nya!+loh").toList must_== List("http://url.domain.com/ada-tanda-seru-nya!+loh")
        }
        def extractComplexUrl = {
            (rvText2 must be contain("http://api.sub.22.test2.com/itu/tags/motor,mobil.-.html"))
        }

        def extractComplexUrl2 = {

            val t =
                """
                  |!http://1.bp.blogspot.com/-HpnSkbq-lpg/TfnwTg3vKGI/AAAAAAAAABs/EQwhgoFfrVs/s1600/mahfud%2Bmd.jpg! dan
                  |!http://3.bp.blogspot.com/-L79-mDioJPU/UPesE52WPgI/AAAAAAAAFd8/joO6PEMyrL8/s400/1+(1).jpg!
                """.stripMargin

            val rv = TextExtractor.extractLinks(t).toList

            (rv must be contain("http://1.bp.blogspot.com/-HpnSkbq-lpg/TfnwTg3vKGI/AAAAAAAAABs/EQwhgoFfrVs/s1600/mahfud%2Bmd.jpg")) and
                (rv must be contain("http://3.bp.blogspot.com/-L79-mDioJPU/UPesE52WPgI/AAAAAAAAFd8/joO6PEMyrL8/s400/1+(1).jpg"))

        }

        def extractLinkFromTextile = {
            val text1 =
                """
                  |"http://tekno.kompas.com/...l.Berbasis.Minat":http://digakusite.com/url-go?url=http%3A%2F%2Ftekno.kompas.com%2Fread%2F2012%2F11%2F15%2F19290595%2FMindTalk.com..Jejaring.Sosial.Lokal.Berbasis.Minat
                """.stripMargin.trim

            val text2 =
                """
                  |"http://tekno.kompas.com/...l.Berbasis.Minat":http://tekno.kompas.com/read/2012/11/15/19290595/MindTalk.com..Jejaring.Sosial.Lokal.Berbasis.Minat
                """.stripMargin.trim

            val text3 =
                """
                  |"hehehe":http://tekno.kompas.com/read/2012/11/15/19290595/MindTalk.com..Jejaring.Sosial.Lokal.Berbasis.Minat
                """.stripMargin.trim
            val expected = "http://tekno.kompas.com/read/2012/11/15/19290595/MindTalk.com..Jejaring.Sosial.Lokal.Berbasis.Minat"
            (TextExtractor.extractLinkTextile(text1) must_== expected) and
                (TextExtractor.extractLinkTextile(text2) must_== expected) and
                (TextExtractor.extractLinkTextile(text3) must_== text3)
        }

    }

    object userNameExtractor {
        private val text1 =
            """
              |hello @robin @GONDeZ :)
              |y@ahoo
            """.stripMargin

        private val rvText1 = TextExtractor.extractUserNames(text1).toList

        def extractBasic = {
            (rvText1 must be contain("robin")) and (rvText1 must be contain("GONDeZ"))
        }

        def noInvalid = {
            (rvText1 must be not contain("ahoo"))
        }
    }

    object userIdExtractor {
        private val html =
            """
              |<p>Ini summon <span class="mentioned" data-user-id="659592351424974800">@Akhmad Andriana Khadafi Pranoto Adicoro</span>
              |<span class="mentioned" data-user-id="659592671286792200">@Bramatya Aji Pangestu</span>
              |<span class="mentioned" data-user-id="1234567"></span>
              |</p>
            """.stripMargin

        val rvHtml = TextExtractor.extractUserIds(html).toList

        def extractBasic = {
            (rvHtml must be contain(659592351424974800L) and (rvHtml must be contain(659592671286792200L)))
        }

        def noValid = {
            (rvHtml must be not contain(1234567L))
        }
    }

    object channelNameExtractor {
        private val text1 =
            """
              |hello ini group #motor #mobil.keren
              |#lucu-banget
              |dan ini invalid group #e
            """.stripMargin

        private val rvText1 = TextExtractor.extractChannelNames(text1)

        def extractBasic = {
            (rvText1.toList must be contain("motor", "mobil.keren", "lucu-banget") inOrder)
        }

        def invalid = {
            rvText1.toList must not contain("e")
        }
    }

    object picLink {
        val text =
            """hi there http://www.some.com/with-image.jpg
              |pic bagus
            """.stripMargin
        val text2 =
            """hi there !http://www.some.com/with-image-no-ext!
              |pic bagus
            """.stripMargin

        def extractCommons = {
            TextExtractor.extractPicLinks(text).toList must be contain("http://www.some.com/with-image.jpg") only
        }

        def extractTextile = {
            TextExtractor.extractPicLinks(text2).toList must be contain("http://www.some.com/with-image-no-ext") only
        }

        def check = {
            (TextExtractor.containsPicLinks(text) must beTrue) and
                (TextExtractor.containsPicLinks("gak ada link je !-!") must beFalse)
        }
    }
}
