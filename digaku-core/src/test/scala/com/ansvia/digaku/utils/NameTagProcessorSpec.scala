/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import org.specs2.Specification

/**
 * Author: robin
 * 
 */
class NameTagProcessorSpec extends Specification {
    def is =
        "Text tag processor should" ^
        p ^
            "compile user name @robin" ! trees.compileUserName ^
        end

    object trees {


        val textTagProcessor = new NameTagProcessor {
            def handle(tag: String) = "--%s--".format(tag.substring(1))
        }

        def compileUserName = {
            textTagProcessor.handle("@robin") must beEqualTo("""--robin--""")
        }

    }
}
