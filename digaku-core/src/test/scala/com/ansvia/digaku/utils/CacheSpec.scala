// @TODO(robin): benerin ini setelah major refactor

///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.utils
//
//import org.specs2.Specification
//import java.util.concurrent.TimeUnit
//
///**
// * Author: robin
// *
// */
//class CacheSpec extends Specification {
//
//    def is = "Cache utils untuk" ^
//        p ^
//            "Steady cache harus" ^
//            p ^
//                "bisa set dan get data" ! trees.setGet ^
//        end
//
//    object trees {
//        private val steadyCache = Cache.steady[String, String]("set-get-test", 2, 5, TimeUnit.SECONDS){ key =>
//            key.toUpperCase
//        }
//        def setGet = {
//            val rv = steadyCache.get("hallo robin")
//            val rv2 = steadyCache.get("hallo robin")
//
//            (rv must beEqualTo("HALLO ROBIN")) and
//                (rv2 must beEqualTo("HALLO ROBIN"))
//        }
//    }
//}
