/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import org.specs2.mutable.Specification

/**
 * Author: robin
 *
 */
class ArticleUtilSpec extends Specification {

    "ArticleUtil.shortDesc" should {
        "strip [img] tags" in {
            ArticleUtil.shortDesc("[img]http://link.ke/gambar.jpg[/img] ini gambar") must_== "ini gambar"
        }
        "strip links" in {
            ArticleUtil.shortDesc("http://mndt.lk/123 ini BCA MC2") must_== "ini BCA MC2"
        }
        "strip html element tags" in {
            ArticleUtil.shortDesc("<b>coba</b> kalo di <i>article</i>") must_== "coba kalo di article"
            ArticleUtil.shortDesc("<div>pem pem</div>") must_== "pem pem"
            ArticleUtil.shortDesc("<strong>pem pem</strong>") must_== "pem pem"
        }
        "stip html tags but keep space" in {
            ArticleUtil.shortDesc("hello <ol><li>satu</li><li>dua</li><li>tiga</li></ol>") must_== "hello satu dua tiga"
        }
        "strip !oembed! tags" in {
            ArticleUtil.shortDesc("ini ada oembed!http://youtu.be/123123!oembed yo") must_== "ini ada yo"
        }
    }
}
