/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import com.ansvia.digaku.Digaku
import org.specs2.mutable.Specification
import java.util.{Date, Calendar}

/**
 * Author: robin
 *
 */
class DateUtilsSpec extends Specification {

    "Digaku.engine.dateUtils" should {
        "get current year" in {
            val cal = Calendar.getInstance()
            cal.setTime(new Date())
            val year = cal.get(Calendar.YEAR)
            println("year: " + year)
            Digaku.engine.dateUtils.getCurrentYear must_== year
        }
        "get current month (of year)" in {
            val cal = Calendar.getInstance()
            cal.setTime(new Date())
            val month = cal.get(Calendar.MONTH) + 1
            Digaku.engine.dateUtils.getCurrentMonth must_== month
        }
        "get current day of month" in {
            val cal = Calendar.getInstance()
            cal.setTime(new Date())
            val dom = cal.get(Calendar.DAY_OF_MONTH)
            Digaku.engine.dateUtils.getCurrentDayOfMonth must_== dom
        }
    }
}
