/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils.emo

import org.specs2.mutable.Specification
import org.specs2.specification.Scope

/**
 * Author: robin
 *
 */
class BaseDirEmoticonGroupProcessorSpec extends Specification {



    class Ctx extends Scope {

        object ep extends BaseDirEmoticonGroupProcessor("Mindtalk", "mindtalk") with EmoMapper {
            def mapEmo(emoCode: String): String = emoCode match {
                case ":)" => "smile"
                case x => x
            }
        }

    }

    "BaseDirEmoticonGroupProcessor" should {
        "compiled saved code" in new Ctx {
            val rv = ep.compile(":smile", true)
            println("rv: " + rv)
            rv.startsWith("[saved-code-") must beTrue
        }
        "compiled using emo mapper" in new Ctx {
            val rv = ep.compile(":)", false)
            println("rv: " + rv)
            rv must_== """<img src="/assets/img/emoticons/mindtalk/commons/smile.gif" title=":)" alt=":)" class="emo" />"""
        }
        "compiled grouped" in new Ctx {
            ep.compile(":hug :sir", false) must_== """<img src="/assets/img/emoticons/mindtalk/commons/hug.gif" title=":hug" alt=":hug" class="emo" /> :sir"""
        }
    }
}

