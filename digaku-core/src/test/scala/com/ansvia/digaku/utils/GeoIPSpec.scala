///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.utils
//
//import org.specs2.Specification
//import com.maxmind.geoip2.record.Country
//
///**
// * Author: alamybs, robin
// * Date: 11/16/13
// * Time: 2:58 PM
// *
// */
//class GeoIPSpec extends Specification {
//    def is = "Geo IP should" ^
//        p ^
//        "get country by ip address correctly" ! trees.getCountryCorrectly ^
//        "get country name by ip address correctly" !  trees.getCountryName ^
//        "get country code by ip address correctly" !  trees.getCountryCode ^
//        "get country by wrong ip address must be return None option" ! trees.getaaa ^
//        "get city" ! trees.getCity ^
//        end
//
//    object trees {
//        val ipAddress = List(("AF", "************", "Afghanistan"), ("AL", "***********", "Albania"), ("DZ", "*********", "Algeria"),
//            ("ID", "**************" ,"Indonesia"), ("MY", "*******", "Malaysia"), ("PH", "*************", "Philippines"),
//            ("SG", "************", "Singapore"), ("TH", "*********", "Thailand"), ("US", "*******", "United States")
//        )
//        def getCountryCorrectly = {
//            var listCountry = List.empty[Option[Country]]
//
//            ipAddress.foreach { ip =>
//                listCountry +:= GeoIP.getCountry(ip._2)
//            }
//
//            listCountry.isEmpty must beFalse
//
//        }
//
//        def getCountryName = {
//            var listCountryName = List.empty[String]
//
//            ipAddress.foreach { ip =>
//                listCountryName :+= GeoIP.getCountryName(ip._2)
//            }
//
//            listCountryName must beEqualTo(ipAddress.map(_._3))
//
//        }
//
//        def getCountryCode = {
//            var listCountryCode = List.empty[String]
//
//            ipAddress.foreach { ip =>
//                listCountryCode :+= GeoIP.getCountryCode(ip._2)
//            }
//
//            listCountryCode must beEqualTo(ipAddress.map(_._1))
//
//        }
//
//        def getaaa = {
//            GeoIP.getCountry("***************.123") must beEqualTo(None)
//        }
//
//        def getCity = {
//            (GeoIP.getCity("**************").get.getName must beEqualTo("Magelang")) and
//            (GeoIP.getLocation("**************").getLocation.getLatitude must beEqualTo(-7.4706))
//        }
//
//
//    }
//}
