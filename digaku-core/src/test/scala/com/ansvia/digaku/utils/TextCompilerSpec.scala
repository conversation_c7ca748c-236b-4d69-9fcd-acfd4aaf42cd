/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import java.net.{MalformedURLException, URI}

import com.ansvia.digaku.persistence.BackedKVStoreIface
import com.ansvia.digaku.utils.emo.impl.MindtalkEmoticonProcessor
import com.ansvia.digaku._
import org.specs2.Specification

import scala.xml.{Null, UnprefixedAttribute, XML}

/**
 * Author: robin, temon
 *
 */
class TextCompilerSpec extends Specification with DigakuTest with BeforeAllAfterAllImmutable {
    def is =
        "TextCompiler harusnya bisa" ^
        p ^
            "compile :) emoticon" ! trees.compileEmoSmile ^
            "compile :smile emoticon" ! trees.compileEmoSmileNoMap ^
            "compile :( emoticon" ! trees.compileEmoSad ^
            "compile :omg emoticon" ! trees.compileEmoOmg ^
            "compile quote" ! trees.compileQuote ^
            "compile quote multi line" ! trees.compileQuoteMultiLine ^
            "compile quote no name" ! trees.compileQuoteNoName ^
            "compile link target _blank" ! trees.compileLinkTargetBlank ^
            "compile link ada karakter tilde-nya" ! trees.compileLinkTilde ^
            "compile link ada karakter plus-nya" ! trees.compileLinkPlus ^
            "compile https link" ! trees.compileHttpsLink ^
            "compile with port link" ! trees.compileWithPortLink ^
            "compile textile pic img !link! style" ! trees.compileImageLink ^
            "compile textile pic img !link! style with plus char" ! trees.compileImageLinkWithPlusChar ^
            "compile html code" ! trees.compileHtmlCode ^
//            "compile embeddable" ! trees.compileEmbeddable ^
            "compile link inside quote" ! trees.compileLinkInsideQuote ^
            "compile spoiler" ! trees.spoiler ^
            "compile spoiler not break up name tagging" ! trees.spoilerNotBreakTag ^
            "compile spoiler not break up textile img !link!" ! trees.spoilerWithTextileImg ^
            "compile html link" ! trees.compileLinkTransformer ^
            "compile html link yang mengandung karakter & harusnya di rubah jadi &amp;" ! trees.compileLinkWithAndCharacter ^
            "compile html link yang tidak sesuai dengan standar xhtml" +
                " tidak akan di proses link rel nya" ! trees.compileNotStandardLink ^
//            "compile response snip no xss #1" ! trees.compileResponseSnipNoXss ^
//            "compile response snip no xss #2" ! trees.compileResponseSnipNoXss2 ^
//            "compile response snip no xss #3" ! trees.compileResponseSnipNoXss3 ^
            "decode char %C3%97 atau %D7 menjadi string x" ! trees.compileDecodeUrl ^
            "gak error ketika compile text yang mengandung $1 $2 #1" ! trees.canHandleDollarNum ^
            "gak error ketika compile text yang mengandung $1 $2 #2" ! trees.canHandleDollarNum2 ^
            "dapat menghandle code tag name misal scala, python, html, dll" ! trees.recognizedCodeTag ^
            "text & harusnya jadi &amp; -nya tidak di-escape lagi" ! trees.noEscapedAmp ^
            "user name tag di dalam link harusnya gak keproses" ! trees.noProcessUnamelikeTagInLink ^
            "bisa compile user name tag yang mengandung dot di dalamnya" ! trees.canHandleUserNameTagContainingDot ^
            "bisa compile user name dan emoticon dari sebuah content" ! trees.canCompileNameTagEmo ^
            "remove texttile format" ! trees.removeTextileFormat ^
            "compile oembed embed (widget untuk youtube, vimeo, slideshare, kickstarter)" ! trees.canHandleMediaEmbed ^
            "compile oembed embed (widget untuk youtube, vimeo, slideshare, kickstarter) di editor" ! trees.canHandleMediaEmbedEditorMode ^
            "normalisasi html" ! trees.normHtml ^
            "compile spoiler" ! trees.compileSpoiler ^
            "strip url" ! trees.stripUrl ^
//            "convert more than one <br> to one" ! trees.brConvert ^
        end


    // to avoid messing another tests
    val originalEngine:Engine = Digaku.engine


    override protected def beforeAll(): Unit = {
        println("replacing engine for mock")
        Digaku.engine = new DigakuTestEngine {
            override val systemConfig: BackedKVStoreIface = null
            override val contentProcessor: ContentProcessor = new ContentProcessor {
                override val linkTransformer: LinkTransformer = new LinkTransformer {
                    def handle(tag: String) = {
                        try {
                            val elm = XML.loadString(tag.replaceAll("&(amp;)*", "&amp;"))
                            val url = elm.attribute("href").map(_.toString()).getOrElse("")
                            val link = new URI(url)

                            if ((link.getScheme + "://" + link.getHost) == "http://digakusite.com" || link.getHost == null) {
                                tag
                            } else {
                                (elm % new UnprefixedAttribute("rel", "nofollow", Null)).toString()
                            }
                        }
                        catch {
                            case e:MalformedURLException =>
                                tag
                            case e:Exception =>
                                e.getStackTraceString.replaceAll("\n", "\n - ")
                                tag
                        }
                    }
                }
                emoticonProcessor = new MindtalkEmoticonProcessor
                override val textTagProcessor: NameTagProcessor = new NameTagProcessor {
                    def handle(tag: String) = {
                        tag(0) match {
                            case '@' =>
                                """@<a href="/%s/%s">%s</a>""".format("u", tag.substring(1), tag.substring(1))
                            case '#' =>
                                """#<a href="/%s/%s">%s</a>""".format("channel", tag.substring(1), tag.substring(1))
                        }
                    }
                }
                override val textNameCompiler: NameCompiler = new NameCompiler {
                    def compileUserName(str: String) = """<a href="/%s/%s">%s</a>""".format("u", str, str)
                    def compileChannelName(str: String) = """<a href="/%s/%s">%s</a>""".format("channel", str, str)
                }
            }

        }
    }

    override protected def afterAll(): Unit = {
        println("restore original engine")
        Digaku.engine = originalEngine
    }

    object trees {




        def emoHtml(src:String, title:String) = {
            """<img src="%s" title="%s" alt="%s" class="emo" />""".format(src, title, title)
        }

        def userNameLink(userName:String) =
            """<a href="/u/%s">%s</a>""".format(userName, userName)

        def channelLink(channelName:String) = {
            """<a href="/channel/%s">%s</a>""".format(channelName, channelName)
        }



        def compileEmoSmile = {
            TextCompiler.compileEmoticon("hello :)") must beEqualTo("hello %s".format(emoHtml("/assets/img/emoticons/mindtalk/commons/smile.gif", ":)")))
        }
        def compileEmoSmileNoMap = {
            TextCompiler.compileEmoticon("hello :smile") must beEqualTo("""hello %s""".format(emoHtml("/assets/img/emoticons/mindtalk/commons/smile.gif", ":smile")))
        }
        def compileEmoSad = {
            TextCompiler.compileEmoticon("hello :(") must beEqualTo("""hello %s""".format(emoHtml("/assets/img/emoticons/mindtalk/commons/sad.gif",":(")))
        }
        def compileEmoLovedSmile = {
            TextCompiler.compileEmoticon("hello :loved :)") must beEqualTo("""hello %s %s""".format(emoHtml("/assets/img/emoticons/mindtalk/commons/loved.gif",":loved"), emoHtml("/assets/img/emoticons/mindtalk/smile.png", ":)")))
        }
        def compileEmoOmg = {
            TextCompiler.compileEmoticon(":wtf :omg") must beEqualTo(""":wtf %s""".format(emoHtml("/assets/img/emoticons/mindtalk/commons/holymother.gif",":omg")))
        }
        def compileQuote = {
            TextCompiler.compileMessage("""[quote=robin;123]hello[/quote]""") must beEqualTo("""<p><blockquote><strong><a href="/u/robin">robin</a></strong>'s <a href="javascript://" onclick="openResponse('123');">response</a>: hello</blockquote></p>""".trim)
        }
        def compileQuoteMultiLine = {
            TextCompiler.compileMessage(
                """[quote=robin;123]
                  | hello
                  |[/quote]""".stripMargin) must beEqualTo(
                ("""<p><blockquote><strong><a href="/u/robin">robin</a></strong>'s <a href="javascript://" onclick="openResponse('123');">response</a>: """ + "\n hello" +
                  "\n</blockquote></p>").trim)
        }
        def compileQuoteNoName = {
            val text =
                """
                  |[quote]ini di dalam quote[/quote]
                """.stripMargin.trim
            val expected =
                """
                  |<p><blockquote>ini di dalam quote</blockquote></p>
                """.stripMargin.trim
            TextCompiler.compileMessage(text) must beEqualTo(expected)
        }


        def brConvert = {
            TextCompiler.compileMessage("a\n\nb") must beEqualTo("a<br />b")
        }
        def compileLinkTargetBlank= {
            TextCompiler.compileMessage("ini adalah link: http://www.ansvia.com yeee") must beEqualTo("""<p>ini adalah link: <a rel="nofollow" title="http://www.ansvia.com" target="_blank" href="http://www.ansvia.com">http://www.ansvia.com</a> yeee</p>""")
        }

        def compileLinkTilde = {
            TextCompiler.compileMessage("ini adalah link: http://www.ansvia.com/~robin/yeee yeee") must beEqualTo("""<p>ini adalah link: <a rel="nofollow" title="http://www.ansvia.com/~robin/yeee" target="_blank" href="http://www.ansvia.com/~robin/yeee">http://www.ansvia.com/~robin/yeee</a> yeee</p>""")
        }

        def compileLinkPlus = {
            TextCompiler.compileMessage("ini adalah link: http://www.ansvia.com/~robin/yeee+yeee") must beEqualTo("""<p>ini adalah link: <a rel="nofollow" title="http://www.ansvia.com/~robin/yeee+yeee" target="_blank" href="http://www.ansvia.com/~robin/yeee+yeee">http://www.ansvia.com/~robin/yeee+yeee</a></p>""")
        }


        def compileHttpsLink = {
            TextCompiler.compileMessage("test https://github.com/zeromq/jzmq/issues/114 ae") must beEqualTo("""<p>test <a rel="nofollow" title="https://github.com/zeromq/jzmq/issues/114" target="_blank" href="https://github.com/zeromq/jzmq/issues/114">https://github.com/.../jzmq/issues/114</a> ae</p>""")
        }
        def compileWithPortLink = {
            TextCompiler.compileMessage("test https://localhost:8080/zeromq/jzmq/issues/114 ae") must beEqualTo("""<p>test <a rel="nofollow" title="https://localhost:8080/zeromq/jzmq/issues/114" target="_blank" href="https://localhost:8080/zeromq/jzmq/issues/114">https://localhost:8080/.../jzmq/issues/114</a> ae</p>""")
        }
        def compileImageLink = {
            TextCompiler.compileMessage("hello ini pic image loh: !http://www.ansvia.com/mindtalk.png!") must beEqualTo("""<p>hello ini pic image loh: <img alt="mindtalk png" src="http://www.ansvia.com/mindtalk.png"/></p>""")
        }
        def compileImageLinkWithPlusChar = {
            TextCompiler.compileMessage("hello ini pic image loh: !http://www.ansvia.com/mindtalk+keren.png!") must beEqualTo("""<p>hello ini pic image loh: <img alt="mindtalk keren png" src="http://www.ansvia.com/mindtalk%2Bkeren.png"/></p>""")
        }

        def compileHtmlCode = {
            val msg =
                """
                  |test code html
                  |```html
                  |<div>test</div>
                  |```
                """.stripMargin
            val msgNoLang =
                """
                  |test code html
                  |```
                  |<div>test</div>
                  |```
                """.stripMargin
            val msgC =
                """<p>test code html<br/><pre><code data-language="html">&lt;div&gt;test&lt;/div&gt;</code></pre></p>""".stripMargin
            val msgCNoLang =
                """<p>test code html<br/><pre><code>&lt;div&gt;test&lt;/div&gt;</code></pre></p>""".stripMargin

            (TextCompiler.compileMessage(msg) must beEqualTo(msgC)) and
                (TextCompiler.compileMessage(msgNoLang) must beEqualTo(msgCNoLang))
        }

        def compileEmbeddable = {
            val text =
                """
                  |hello http://www.youtube.com/watch?v=8mwKq7_JlS8 youtube
                """.stripMargin.trim
            val text2 =
                """
                  |youku http://v.youku.com/v_show/id_XNTc2NTY1ODg4.html
                """.stripMargin
            val text3 =
                """
                  |youtu.be http://youtu.be/aIUBCmfiANA
                """.stripMargin
            val text4 =
                """
                  |vimeo http://vimeo.com/84135659
                """.stripMargin
            val text5 =
                """
                  |slideshare.net http://www.slideshare.net/xtin101/getting-to-know-31309433
                """.stripMargin
            val text6 =
                """
                  |soundcloud.net https://soundcloud.com/bengkytunes/aku-rapopo-im-ok
                """.stripMargin

            (TextCompiler.compileEmbeddable(text) must beEqualTo("""hello <a href="http://www.youtube.com/watch?v=8mwKq7_JlS8" class="embedded-object">http://www.youtube.com/watch</a> youtube""")) and
                (TextCompiler.compileMessage(text) must beEqualTo("""<p>hello <a rel="nofollow" class="embedded-object" href="http://www.youtube.com/watch?v=8mwKq7_JlS8">http://www.youtube.com/watch</a> youtube</p>""")) and
                (TextCompiler.compileMessage(text2) must beEqualTo("""<p>youku <a rel="nofollow" class="embedded-object" href="http://v.youku.com/v_show/id_XNTc2NTY1ODg4.html">http://v.youku.com/...Tc2NTY1ODg4.html</a></p>""")) and
                (TextCompiler.compileMessage(text3) must beEqualTo("""<p>youtu.be <a rel="nofollow" class="embedded-object" href="http://youtu.be/aIUBCmfiANA">http://youtu.be/aIUBCmfiANA</a></p>""")) and
                (TextCompiler.compileMessage(text4) must beEqualTo("""<p>vimeo <a rel="nofollow" title="http://vimeo.com/84135659" target="_blank" href="http://vimeo.com/84135659">http://vimeo.com/84135659</a></p>""")) and
                (TextCompiler.compileMessage(text5) must beEqualTo("""<p>slideshare.net <a rel="nofollow" class="embedded-object" href="http://www.slideshare.net/xtin101/getting-to-know-31309433">http://www.slideshare.net/...to-know-31309433</a></p>""")) and
                (TextCompiler.compileMessage(text6) must beEqualTo("""<p>soundcloud.net <a rel="nofollow" class="embedded-object" href="https://soundcloud.com/bengkytunes/aku-rapopo-im-ok">https://soundcloud.com/...aku-rapopo-im-ok</a></p>"""))
        }

        def compileLinkInsideQuote = {
            val text =
                """
                  |[quote=robin;210055]@dafi kalo tidak memenuhi quota akan dipotong gajinya [/quote] ngeri
                """.stripMargin.trim
            val expectation =
                """
                  |<p><blockquote><strong><a href="/u/robin">robin</a></strong>'s <a href="javascript://" onclick="openResponse('210055');">response</a>: @dafi kalo tidak memenuhi quota akan dipotong gajinya </blockquote> ngeri</p>
                """.stripMargin.trim

            TextCompiler.compileMessage(text) must beEqualTo(expectation)
        }


        def spoiler = {
            val text =
                """
                  |[[[ini di dalam spoiler :)]]]
                """.stripMargin.trim
            val expectation =
                """
                  |<p><div class="spoiler">ini di dalam spoiler %s</div></p>
                """.stripMargin.format(emoHtml("/assets/img/emoticons/mindtalk/commons/smile.gif", ":)")).trim

            TextCompiler.compileMessage(text) must beEqualTo(expectation)
        }

        def spoilerNotBreakTag = {

            val text =
                """
                  |hello @robin di #channel
                  |[[[ ini spoiler ]]]
                """.stripMargin.trim
            val expectation =
                """
                  |<p>hello @<a href="/u/robin">robin</a> di #<a href="/channel/channel">channel</a><br/><div class="spoiler"> ini spoiler </div></p>
                """.stripMargin.trim.format(userNameLink("robin"), channelLink("channel")).replaceAll("\\n","")

            TextCompiler.compileMessage(text) must beEqualTo(expectation)
        }

        def spoilerWithTextileImg = {
            val text =
                """
                  |[[[ini image dalam spoiler !http://www.ansvia.com/mindtalk.png!]]]
                """.stripMargin.trim

            val expectation =
                """
                  |<p><div class="spoiler">ini image dalam spoiler <img alt="mindtalk png" src="http://www.ansvia.com/mindtalk.png"/></div></p>
                """.stripMargin.trim

            TextCompiler.compileMessage(text) must beEqualTo(expectation)
        }

        def compileLinkTransformer = {
            val text = {
                """
                  |<a href="http://youtube.com/channel/dafi">#dafi</a>
                """.stripMargin.trim
            }
            val text2 = {
                """
                  |<a href="http://digakusite.com/channel/dafi">#dafi</a>
                """.stripMargin.trim
            }
            (TextCompiler.compileLink(text) must beEqualTo("""<a rel="nofollow" href="http://youtube.com/channel/dafi">#dafi</a>""")) and
                (TextCompiler.compileLink(text2) must beEqualTo("""<a href="http://digakusite.com/channel/dafi">#dafi</a>"""))
        }

        def compileLinkWithAndCharacter = {
            val text = {
                """
                  |<a href="http://www.youtube.com/watch?v=qTKrJIA88mE&feature=c4-overview&list=UU6RHORmRuCiGJQILQDu5B7Q">#dafi</a>
                """.stripMargin.trim
            }
            val text2 = {
                """
                  |<a href="http://www.youtube.com/watch?v=qTKrJIA88mE&feature=c4-overview&amp;list=UU6RHORmRuCiGJQILQDu5B7Q">#dafi</a>
                """.stripMargin.trim
            }
            val expectation =
                """
                  |<a rel="nofollow" href="http://www.youtube.com/watch?v=qTKrJIA88mE&amp;feature=c4-overview&amp;list=UU6RHORmRuCiGJQILQDu5B7Q">#dafi</a>
                """.stripMargin.trim
            (TextCompiler.compileLink(text) must beEqualTo(expectation)) and
                (TextCompiler.compileLink(text2) must beEqualTo(expectation))
        }

        def compileNotStandardLink = {
            val text = {
                """
                  |<a href="http://www.youtube.com/watch?v=qTKrJIA88mE&feature=c4-overview&list=UU6RHORmRuCiGJQILQDu5B7Q">#da><fi</a>
                """.stripMargin.trim
            }
            TextCompiler.compileLink(text) must beEqualTo(text)
        }

        def canHandleDollarNum = {
            val text =
                """
                  |```ini code $1 dan $2
                  |```
                """.stripMargin.trim
            val expected =
                """
                  |<p><pre><code>ini code $1 dan $2</code></pre></p>
                """.stripMargin.trim
            TextCompiler.compileMessage(text) must beEqualTo(expected)
        }

        def canHandleDollarNum2 = {
            val text =
                """
                  |h1. ini title $1 $2
                """.stripMargin.trim
            val expected =
                """
                  |<h3>ini title $1 $2</h3>
                """.stripMargin.trim
            TextCompiler.compileMessage(text) must beEqualTo(expected)
        }

        def recognizedCodeTag = {
            val text =
                """
                  |```scala
                  |println("ini code scala")
                  |```
                """.stripMargin.trim
            val expected =
                """
                  |<p><pre><code data-language="scala">println(&quot;ini code scala&quot;)</code></pre></p>
                """.stripMargin.trim
            TextCompiler.compileMessage(text) must beEqualTo(expected)
        }

//        def compileResponseSnipNoXss = {
//            val text = """<script>test</script>"""
//            val expected = """<p>&lt;script&gt;test&lt;/script&gt;</p>"""
//            TextCompiler.compileResponseSnip(text) must beEqualTo(expected)
//        }
//
//        def compileResponseSnipNoXss2 = {
//            val text = """<script>yay nyoba saja nih nyoba saja nih nyoba saja nih nyoba saja nih nyoba saja nih nyoba saja nih</script>"""
//            val expected = """<p>&lt;script&gt;yay nyoba saja nih nyoba saja nih nyoba saja nih nyoba saja nih nyoba saja nih nyoba saja…</p>"""
//            TextCompiler.compileResponseSnip(text) must beEqualTo(expected)
//        }
//
//        def compileResponseSnipNoXss3 = {
//            val text1 = """<script>nyoba yang ada link-nya http://www.mindtalk.com/u/andrie, harusnya protocol link-nya dihapus supaya tidak diproses</script>"""
//            val text2 = """<script>nyoba yang ada link textile-nya "andrie":http://www.mindtalk.com/u/andrie, harusnya protocol link-nya dihapus supaya tidak diproses</script>"""
//            val expected1 = """<p>&lt;script&gt;nyoba yang ada link-nya www.mindtalk.com/u/andrie, harusnya protocol link-nya diha…</p>"""
//            val expected2 = """<p>&lt;script&gt;nyoba yang ada link textile-nya www.mindtalk.com/u/andrie, harusnya proto…</p>"""
//            (TextCompiler.compileResponseSnip(text1) must beEqualTo(expected1)) and
//                (TextCompiler.compileResponseSnip(text2) must beEqualTo(expected2))
//        }

        def compileDecodeUrl = {
            val text = """http://www.mindtalk.com/300×500.jpg"""
            val expected = """<p><a rel="nofollow" title="http://www.mindtalk.com/300x500.jpg" target="_blank" href="http://www.mindtalk.com/300x500.jpg">http://www.mindtalk.com/300x500.jpg</a></p>"""
            TextCompiler.compileMessage(text) must beEqualTo(expected)
        }

        def noEscapedAmp = {
            val text = "hello & bellow"
            val expected = "<p>hello &amp; bellow</p>"
            TextCompiler.compileMessage(text) must_== expected
        }

        def noProcessUnamelikeTagInLink = {
            val text = "hello @robin ini link http://www.ansvia.com/@robin/hello"
            val expected = """<p>hello @<a href="/u/robin">robin</a> ini link <a rel="nofollow" title="http://www.ansvia.com/@robin/hello" target="_blank" href="http://www.ansvia.com/@robin/hello">http://www.ansvia.com/@robin/hello</a></p>"""
            TextCompiler.compileMessage(text) must_== expected
        }

        def canHandleUserNameTagContainingDot = {
            val text = "hello @ahmed.elqahtani"
            val expected = "<p>hello @<a href=\"/u/ahmed.elqahtani\">ahmed.elqahtani</a></p>"
            TextCompiler.compileMessage(text) must_== expected
        }


        def canHandleMediaEmbed = {
            val text =
                """
                  |video youtube oembed!https://www.youtube.com/watch?v=AgpTtCs-SN4!oembed -
                  |slideshare widget oembed!http://www.slideshare.net/robhawkes/the-state-of-html5-games-fluent-js!oembed -
                  |scribd widget oembed!https://www.scribd.com/read/224237509/Oh-No-She-Didn-t-The-Top-100-Style-Mistakes-Women-Make-and-How-to-Avoid-Them!oembed -
                  |kickstarter widget oembed!https://www.kickstarter.com/projects/angeliatrinidad/passion-planner-the-one-place-for-all-your-thought!oembed
                """.stripMargin
            val expected = "<p>video youtube <a rel=\"nofollow\" href=\"https://www.youtube.com/watch?v=AgpTtCs-SN4\" target=\"_blank\" class=\"embedded-object\"/> -<br/>slideshare widget <a rel=\"nofollow\" href=\"http://www.slideshare.net/robhawkes/the-state-of-html5-games-fluent-js\" target=\"_blank\" class=\"embedded-object\"/> -<br/>scribd widget <a rel=\"nofollow\" href=\"https://www.scribd.com/read/224237509/Oh-No-She-Didn-t-The-Top-100-Style-Mistakes-Women-Make-and-How-to-Avoid-Them\" target=\"_blank\" class=\"embedded-object\"/> -<br/>kickstarter widget <a rel=\"nofollow\" href=\"https://www.kickstarter.com/projects/angeliatrinidad/passion-planner-the-one-place-for-all-your-thought\" target=\"_blank\" class=\"embedded-object\"/></p>"
            TextCompiler.compileMessage(text) must_== expected
        }

        def canHandleMediaEmbedEditorMode = {
            val text =
                """
                  |video youtube oembed!https://www.youtube.com/watch?v=AgpTtCs-SN4!oembed -
                  |slideshare widget oembed!http://www.slideshare.net/robhawkes/the-state-of-html5-games-fluent-js!oembed -
                  |scribd widget oembed!https://www.scribd.com/read/224237509/Oh-No-She-Didn-t-The-Top-100-Style-Mistakes-Women-Make-and-How-to-Avoid-Them!oembed -
                  |kickstarter widget oembed!https://www.kickstarter.com/projects/angeliatrinidad/passion-planner-the-one-place-for-all-your-thought!oembed
                """.stripMargin
            val expected ="<p>video youtube <img class=\"embedded-object icon icon-play-video\" data-url=\"https://www.youtube.com/watch?v=AgpTtCs-SN4\" /> -<br/>slideshare widget <img class=\"embedded-object icon icon-play-video\" data-url=\"http://www.slideshare.net/robhawkes/the-state-of-html5-games-fluent-js\" /> -<br/>scribd widget <img class=\"embedded-object icon icon-play-video\" data-url=\"https://www.scribd.com/read/224237509/Oh-No-She-Didn-t-The-Top-100-Style-Mistakes-Women-Make-and-How-to-Avoid-Them\" /> -<br/>kickstarter widget <img class=\"embedded-object icon icon-play-video\" data-url=\"https://www.kickstarter.com/projects/angeliatrinidad/passion-planner-the-one-place-for-all-your-thought\" /></p>"
            TextCompiler.compileMessage(text, true) must beEqualTo(expected)
        }
        def canCompileNameTagEmo = {
            val text = "hai @temon, lagi apa? :nomnom"
            val expected = "hai @<a href=\"/u/temon\">temon</a>, lagi apa? %s".format(emoHtml("/assets/img/emoticons/mindtalk/commons/nomnom.gif", ":nomnom"))
            TextCompiler.compileNameTagEmo(text) must_== expected
        }

        def removeTextileFormat = {
            val text = "karena jogja itu indah \"link\":http://google.com *text bold* _text miring nih_ +text bergaris bawah nih+"
            val expected = "karena jogja itu indah link text bold text miring nih text bergaris bawah nih"
            TextCompiler.removeTextileFormat(text) must_== expected
        }

        def normHtml = {
            val text = "ini text <img class=\"embedded-object icon icon-play-video\" src=\"http://youtu.be/XwmHoWfwHiw\" data-url=\"https://www.youtube.com/watch?v=XwmHoWfwHiw\"> ada videonya"
            val expected = "ini text <a href=\"https://www.youtube.com/watch?v=XwmHoWfwHiw\" class=\"embedded-object\" data-thumb-url=\"http://youtu.be/XwmHoWfwHiw\">https://www.youtube.com/watch?v=XwmHoWfwHiw</a> ada videonya"
            TextCompiler.normHtml(text) must_== expected
        }

        def compileSpoiler = {
            val text =
                """
                  |ini text yang keren
                  |[[[ berisi spoiler :D <div>keren</div> ]]]
                  |yang keren juga
                """.stripMargin.trim
            val expected =
                """
                  |ini text yang keren
                  |<div class="spoiler"> berisi spoiler <img src="/assets/img/emoticons/mindtalk/commons/grin.gif" title=":D" alt=":D" class="emo" /> <div>keren</div> </div>
                  |yang keren juga
                """.stripMargin.trim
            TextCompiler.compileSpoiler(text, editorMode = false) must_== expected
        }

        def stripUrl = {
            TextCompiler.stripUrl("hello https://ini.dot.com dan ini tapi yg make http://saja.dot.com :)") must_== "hello  dan ini tapi yg make  :)"
        }



    }
}
