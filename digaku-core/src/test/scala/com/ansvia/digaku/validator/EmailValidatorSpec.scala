/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import org.specs2.Specification
import org.specs2.specification.Step
import java.util.Hashtable
import javax.naming.directory.InitialDirContext
import javax.naming.{OperationNotSupportedException, NameNotFoundException}
import org.specs2.specification.Step
import org.specs2.execute.Result


/**
 * Author: nadir
 * Date: 1/7/13
 * Time: 11:52 AM
 * 
 */
class EmailValidatorSpec extends Specification {

    protected def whenSupportMx[T](func: => Result):Result = {
        try {
            val env = new Hashtable[String, String]
            env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory")

            new InitialDirContext(env).getAttributes("google.com", Array("MX")).get("MX")

            func
        } catch {
            case e:OperationNotSupportedException =>
                success
            case e:NameNotFoundException =>
                success
        }
    }

    def is =
        "EmailValidator should" ^
        sequential ^
        p ^
            Step {
                try {
                    val env = new Hashtable[String, String]
                    env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory")

                    new InitialDirContext(env).getAttributes("google.com", Array("MX")).get("MX")
                } catch {
                    case e:OperationNotSupportedException =>
                        EmailValidator.mxRecordCheck = false
                    case e:NameNotFoundException =>
                        EmailValidator.mxRecordCheck = false
                }
            } ^
            "email test tidak valid" ! {EmailValidator.isValid("test") must beFalse} ^
            "email <EMAIL> valid" ! {EmailValidator.isValid("<EMAIL>") must beTrue} ^
            "email test@test tidak valid" ! {EmailValidator.isValid("test@test") must beFalse} ^
            "email <EMAIL> harusnya valid" ! {EmailValidator.isValid("<EMAIL>") must beTrue} ^
            "normalize email gmail dot" ! {
                EmailValidator.normalizedEmail("<EMAIL>") must_== "<EMAIL>"
            } ^
            "normalize email non gmail dot" ! {
               EmailValidator.normalizedEmail("<EMAIL>") must_== "<EMAIL>"
            } ^
            "cek mxlookup untuk domain yang tidak ada mail server-nya harus false" ! {
                whenSupportMx {
                    EmailValidator.mxLookup("realhowto.com") must beFalse
                }
            } ^
            "cek domain yang memiliki satu atau lebih mail server harusnya true" ! {
                whenSupportMx {
                    EmailValidator.mxLookup("gmail.com") must beTrue
                }
            } ^
            "extract domain from email" ! {
                (EmailValidator.getDomain("<EMAIL>") must beEqualTo("digaku.com")) and
                    (EmailValidator.getDomain("<EMAIL>") must beEqualTo("yahoo.com.sg"))
            } ^
            "cek mxlookup dengan domain asal harus false" ! {
                whenSupportMx {
                    EmailValidator.mxLookup("pwpwpwpwpwpwpwpw.com") must beFalse
                }
            } ^
            "parse email domain public from resource public-email-domain.txt" ! {EmailValidator.PUBLIC_EMAIL_PROVIDER_DOMAIN.toList must be contain("5iron.com")} ^
            "gmail.com must be treated as public email domain" ! {EmailValidator.isPublicEmailDomain("gmail.com") must beTrue} ^
            "vidchart.com, spam5.co.sg, spam4.com must be treated as public email domain" ! {
                (EmailValidator.isPublicEmailDomain("vidchart.com") must beTrue) and
                (EmailValidator.isPublicEmailDomain("spam5.com.sg") must beTrue) and
                (EmailValidator.isPublicEmailDomain("spam4.com") must beTrue)
            } ^
            "cek domain yang memiliki satu atau lebih mail server harusnya true" ! whenSupportMx {
                whenSupportMx {
                    EmailValidator.mxLookup("ansvia.com") must beTrue
                }
            } ^
            "<EMAIL> harusnya dianggap sebagai public email" ! {
                EmailValidator.isPublicEmailProvider("<EMAIL>") must beTrue
            } ^
            "<EMAIL> harusnya dianggap sebagai public email" ! {
                EmailValidator.isPublicEmailProvider("<EMAIL>") must beTrue
            } ^
            "kororo@mindtalk.<NAME_EMAIL> harusnya dianggap sebagai private domain email" ! {
                (EmailValidator.isPublicEmailProvider("<EMAIL>") must beFalse) and
                (EmailValidator.isPublicEmailProvider("<EMAIL>") must beFalse)
            } ^
        end

}
