/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import com.ansvia.digaku.exc.InvalidParameterException
import org.specs2.Specification

/**
 * Author: nadir
 * Date: 1/18/13
 * Time: 10:15 AM
 * 
 */
class UserValidatorSpec extends Specification {

    def is = {
        "User Validator should" ^
        p ^
            "nama _test harus tidak valid" ! trees.validNameCorrectly1 ^
            "nama 0test harus tidak valid" ! trees.validNameCorrectly2 ^
            "nama test harus valid" ! trees.validNameCorrectly3 ^
            "tanggal 12-12-2011 tidak valid" ! trees.validBirthDateCorrectly1 ^
            "tanggal 13/1/1992 tidak valid" ! trees.validBirthDateCorrectly2 ^
            "test tanggal 11/12/1999 harus valid" ! trees.validBirthDateCorrectly3 ^
            "tanggal 13/29/1992 tidak valid" ! trees.validBirthDateCorrectly4 ^
            "test location _yogya harus tidak valid" ! trees.validLocationCorrectly1 ^
            "test location Yogya harus tidak valid" ! trees.validLocationCorrectly2 ^
            "test location yogya harus valid" ! trees.validLocationCorrectly3 ^
            "test location simo rt 17/06, boyolali harus valid" ! trees.validLocationCorrectly4 ^
            "test location bausasran no.37 harus valid" ! trees.validLocationCorrectly5 ^
            "excluded names tidak valid ex: `god` atau `God`" ! trees.excludedNameNotValid ^
            "nama user kurang dari 3 karakter tidak valid" ! (UserValidator.validName("ab") must beFalse) ^
            "nama user lebih dari 60 karakter tidak valid" ! (UserValidator.validName("a" * 61) must beFalse) ^
        end
    }

    object trees {
        def validNameCorrectly1 = {
            UserValidator.validName("_test") must beFalse
        }

        def validNameCorrectly2 = {
            UserValidator.validName("0test") must beFalse
        }

        def validNameCorrectly3 = {
            UserValidator.validName("test") must beTrue
        }

        def validBirthDateCorrectly1 = {
            UserValidator.validBirthDate("12-12-2011") must beFalse
        }

        def validBirthDateCorrectly2 = {
            UserValidator.validateBirthDate("13/1/1992") must throwAn[InvalidParameterException]
        }

        def validBirthDateCorrectly3 = {
            UserValidator.validBirthDate("11/12/1999") must beTrue
        }


        def validBirthDateCorrectly4 = {
            UserValidator.validateBirthDate("13/29/1992") must throwAn[InvalidParameterException]
        }


        def validLocationCorrectly1 = {
            UserValidator.validLocation("_yogya") must beFalse
        }

        def validLocationCorrectly2 = {
            UserValidator.validLocation("Yogya") must beFalse
        }

        def validLocationCorrectly3 = {
            UserValidator.validLocation("yogya") must beTrue
        }

        def validLocationCorrectly4 = {
            UserValidator.validLocation("simo rt 17/06, boyolali") must beTrue
        }

        def validLocationCorrectly5 = {
            UserValidator.validLocation("bausasran no.37") must beTrue
        }

        def excludedNameNotValid= {
            (UserValidator.validName("God") must beFalse) and
                (UserValidator.validName("hitler") must beFalse)
        }
    }

}
