/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import org.specs2.Specification


class ChannelValidatorSpec extends Specification {

    def is = {
        "Group Validator should" ^
            p ^
            "treat 2 char name or less as invalid" ! trees.validNameCorrectly1 ^
            "treat name more than 20 chars as invalid" ! trees.validNameCorrectly2 ^
            "treat `gondez` as valid" ! trees.validNameCorrectly3 ^
            "nama group diawali dengan - valid" ! trees.validNameCorrectly4 ^
            "nama group ada karakter %-nya valid" ! trees.validNameCorrectly5 ^
            "nama group `oke.doke` valid" ! trees.validNameCorrectly6 ^
            "nama group `pacTest` valid" ! trees.validNameCorrectly7 ^
            "nama group bisa diakhiri dengan tanda dot dan dash" ! trees.noEndDotDash ^
            "max description length adalah %s".format(ChannelValidator.MAX_DESC_LENGTH) ! trees.maxDescLength ^
        end
    }

    object trees {
        def validNameCorrectly1 = {
            ChannelValidator.validName("du") must beFalse
        }

        def validNameCorrectly2 = {
            ChannelValidator.validName("namachannelinisangatpanjangsekalihehehehheheheheheheheheh") must beFalse
        }

        def validNameCorrectly3 = {
            ChannelValidator.validName("gondez")
        }

        def validNameCorrectly4 = {
            ChannelValidator.validName("-hore") must beTrue
        }

        def validNameCorrectly5 = {
            ChannelValidator.validName("mmo%eee") must beTrue
        }

        def validNameCorrectly6 = {
            ChannelValidator.validName("oke.doke") must beTrue
        }

        def validNameCorrectly7 = {
            ChannelValidator.validName("pacTest") must beTrue
        }

        def noEndDotDash = {
            (ChannelValidator.validName("hello.") must beTrue) and
            (ChannelValidator.validName("hello-") must beTrue) and
            (ChannelValidator.validName("hello_") must beTrue)
        }

        def maxDescLength = {
            (ChannelValidator.validDescription("a" * 501) must beFalse) and
                (ChannelValidator.validDescription("a" * 500) must beTrue)
        }

    }


}
