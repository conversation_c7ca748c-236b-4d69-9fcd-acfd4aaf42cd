/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import org.specs2.Specification

/**
 * Author: surya, robin
 *
 */
class PostValidatorSpec extends Specification {

    def is = {
        "Post Validator should" ^
            p ^
            "thread Article title 2 char or less as invalid" ! trees.validArticleTitleCorrectly1 ^
            "thread Article title 3 char as valid" ! trees.validArticleTitleCorrectly2 ^
            "thread Article title more 3 char is valid" ! trees.validArticleTitleCorrectly3 ^
            "thread Article title more 160 char as invalid" ! trees.validArticleTitleCorrectly4 ^
            "thread Question title 2 char or less as invalid" ! trees.validQuestionTitleCorrectly1 ^
            "thread Question title 3 char as valid" ! trees.validQuestionTitleCorrectly2 ^
            "thread Question title more than 3 char as valid" ! trees.validQuestionTitleCorrectly3 ^
            "thread Question title more than 100 char as invalid" ! trees.validQuestionTitleCorrectly4 ^
            "thread simple article less 3 char as invalid" ! trees.isValidSimpleContentCorrectly1 ^
            "thread simple post more than 3 char as valid" ! trees.isValidSimpleContentCorrectly2 ^
            "thread simple post more than 160 char as invalid" ! trees.isValidSimpleContentCorrectly3 ^
            end
    }

        object trees {
            def validArticleTitleCorrectly1 ={
                PostValidator.validArticleTitle("as") must beFalse
            }

            def validArticleTitleCorrectly2 = {
                PostValidator.validArticleTitle("123") must beTrue
            }

            def validArticleTitleCorrectly3 = {
                PostValidator.validArticleTitle("1231231231231") must beTrue
            }

            def validArticleTitleCorrectly4 = {
                PostValidator.validArticleTitle("a" * 170) must beFalse
            }

            def validQuestionTitleCorrectly1 = {
                PostValidator.validQuestionTitle("as") must beFalse
            }

            def validQuestionTitleCorrectly2 = {
                PostValidator.validQuestionTitle("123") must beTrue
            }

            def validQuestionTitleCorrectly3 = {
                PostValidator.validQuestionTitle("12312312312") must beTrue
            }

            def validQuestionTitleCorrectly4 = {
                PostValidator.validQuestionTitle("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012") must beFalse
            }

            def isValidSimpleContentCorrectly1 = {
                PostValidator.isValidSimpleContent("as") must beFalse
            }

            def isValidSimpleContentCorrectly2 = {
                PostValidator.isValidSimpleContent("123123") must beTrue
            }

            def isValidSimpleContentCorrectly3 = {
                PostValidator.isValidSimpleContent("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012") must beFalse
            }

        }

}
