/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import org.specs2.Specification
import com.ansvia.digaku.exc.InvalidParameterException

/**
 * Author: robin
 * Date: 3/6/13
 * Time: 10:13 PM
 * 
 */
class PostMarkValidatorSpec extends Specification {

    def is =
        "Polling validator should" ^
            p ^
            "post mark dengan nama `keren` dan color `#cacaca` valid" ! (PostMarkValidator.isValid("keren", "#cacaca") must beTrue) ^
            "post mark dengan nama `a` gak valid" ! (PostMarkValidator.isValid("a", "#cacaca") must beFalse) ^
            "post mark dengan nama `abc` dan color `#a893t` gak valid" ! (PostMarkValidator.isValid("abc", "#a893t") must beFalse) ^
            "post mark dengan nama `abc` dan color `#FFF` valid" ! (PostMarkValidator.isValid("abc", "#FFF") must beTrue) ^
            "post mark dengan nama `abc-def` dan color `#FFF222` valid" ! (PostMarkValidator.isValid("abc-def", "#FFF222") must beTrue) ^
            "post mark dengan nama `abc def` dan color `#FFF222` gak valid" ! (PostMarkValidator.isValid("abc def", "#FFF222") must beFalse) ^
            "gagal di validate harus throw InvalidParameterException" ! (PostMarkValidator.validate("abc-def", "#FF222") must throwAn[InvalidParameterException]) ^
    end


}
