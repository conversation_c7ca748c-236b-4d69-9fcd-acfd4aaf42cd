/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import org.specs2.Specification


class UrlValidatorSpec extends Specification {
    def is =
        "UrlValidator should" ^
        p ^
            "validate url http://www.mindtalk.com/hello" ! {
               UrlValidator.isValid("http://www.mindtalk.com/hello") must beTrue
            } ^
            "validate url https://www.mindtalk.com/hello/index.html" ! {
               UrlValidator.isValid("https://www.mindtalk.com/hello/index.html") must beTrue
            } ^
            "url https://.www.mindtalk.com/hello/index.html is invalid" ! {
               UrlValidator.isValid("https://.www.mindtalk.com/hello/index.html") must beFalse
            } ^
            "validate url https://www.mindtalk.com//hello" ! {
                UrlValidator.isValid("https://www.mindtalk.com//hello") must beTrue
            } ^
        end
}
