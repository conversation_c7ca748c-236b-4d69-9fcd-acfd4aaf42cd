/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import org.specs2.Specification

/**
 * Author: nadir
 * Date: 1/25/13
 * Time: 4:41 PM
 * 
 */
class AppValidatorSpec extends Specification {

    def is = {
        "App Validator should" ^
        p ^
        "treat 2 char name or less as invalid" ! trees.validNameCorrectly1 ^
        "treat `andrie` is valid" ! trees.validNameCorrectly2 ^
        "treat app name more 100 char as invalid" ! trees.validNameCorrectly3 ^
        "treat `<PERSON>` as valid" ! trees.validNameCorrectly4 ^
        "treat `9buku` as valid" ! {AppValidator.validName("9buku") must beTrue} ^
        end
    }

    object trees {
        def validNameCorrectly1 = {
            AppValidator.validName("na") must beFalse
        }

        def validNameCorrectly2 = {
            AppValidator.validName("andrie") must beTrue
        }

        def validNameCorrectly3 = {
            AppValidator.validName("123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012") must beFalse
        }

        def validNameCorrectly4 = {
            AppValidator.validName("<PERSON> Bros") must beTrue
        }

    }


}
