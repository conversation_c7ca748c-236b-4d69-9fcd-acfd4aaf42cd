/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import com.ansvia.digaku.exc.InvalidParameterException
import org.specs2.mutable.Specification

/**
 * Author: robin
 *
 */
class PollingValidatorSpec extends Specification {


    "Polling validator" should {
        "polling dengan judul 'presiden pilihanmu harus valid" in {
            PollingValidator.validTitle("presiden pilihanmu") must beTrue
        }
        "polling dengan judul `a` harus tidak valid" in {
            PollingValidator.validTitle("a") must beFalse
        }
        "polling dengan judul lebih dari 160 karakter tidak valid" in {
            PollingValidator.validTitle("x" * 301) must beFalse
        }
        "polling dengan judul kurang atau sama dengan 160 karakter valid" in {
            PollingValidator.validTitle("x" * 160) must beTrue
        }
        "polling validate harus throw en exception InvalidParameter" in {
            PollingValidator.validateTitle("x") must throwAn[InvalidParameterException]
        }
    }


}
