// @TODO(robin): benerin ini

///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.helpers
//
///**
// * Author: robin
// *
// */
//
//import com.ansvia.digaku.Types.IDType
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.model.{BaseModel, Post, SimplePost}
//import com.ansvia.perf.PerfTiming
//import com.ansvia.digaku.dao.CassandraPartitionedDaoListIndex
//
//class CassandraPartitionedDaoListIndexSpec extends Specification with DigakuTest with PerfTiming {
//
//     def is = "Cassandra partitioned dao list index should" ^
//         sequential ^
//         "can store" ! trees.store ^
//         "partitioned correctly" ! trees.partitioned ^
//         "can slice left" ! trees.sliceLeft ^
//         "can slice right" ! trees.sliceRight ^
//         "can get count" ! trees.getCount ^
//         "can paging" ! trees.paging ^
//         "can transparently indexed when dao creation" ! trees.transIndex ^
//         "can remove" ! trees.rem ^
//         "can reset" ! trees.reset ^
//     end
//
//     object trees {
//
//         val dli = CassandraPartitionedDaoListIndex("127.0.0.1:9160" , "digaku", "digaku_test",
//             "dg_postindex-" + genRandomString,
//             "SimpleStrategy", "replication_factor:1")
//
//         dli.MAX_DATA_PER_ROW = 100
//         val MAX_TOTAL = 36701
//         dli.reset()
//
//         var expectedPartitionCount = 0
//
//         timing("filling data") {
//             for (i <- 1 to MAX_TOTAL){
//
//                 val v = db.addVertex(null)
//
//                 v.setProperty("_class_", "com.ansvia.digaku.model.SimplePost")
//                 v.setProperty("content", "hello #" + v.getId)
//                 v.setProperty("kind", 1)
//                 v.setProperty("shoutCount", 0)
//                 v.setProperty("containsPic", false)
//                 v.setProperty("containsLink", false)
//                 v.setProperty("containsVideoLink", false)
//                 v.setProperty("containsGreeting", false)
//                 v.setProperty("channelPick", false)
//                 v.setProperty("sticky", false)
//                 v.setProperty("originKind", 1)
//                 v.setProperty("streamKind", 1)
//
////                 println("created " + v)
//
//                 if (i % dli.MAX_DATA_PER_ROW == 0)
//                     expectedPartitionCount += 1
//
//                 if (i % 350 == 0)
//                     print(".")
//
//                 if (i % 100 == 0)
//                     db.commit()
//
////                 v
//
//                 dli.store(v)
//             }
//
//             db.commit()
//
//             println("")
//
//         }
//
//         def store = {
//             success
//         }
//
//
//         def partitioned = {
//            dli.getPartitionCount must_== (expectedPartitionCount + (MAX_TOTAL % dli.MAX_DATA_PER_ROW))
//         }
//
//
//
//         def sliceLeft = {
//             val rv1 = timing("rv1"){dli.slice[SimplePost](0, 5, false).toList}
//             val rv2 = timing("rv2"){dli.slice[SimplePost](rv1.last.getId, 3, false).toList}
//             val rv3 = timing("rv3"){dli.slice[SimplePost](rv2.last.getId, 3, false).toList}
//             val rv4 = timing("rv4"){dli.slice[SimplePost](rv3.last.getId + 1000, 3, false).toList}
//             val rv5 = timing("rv5"){dli.slice[SimplePost](rv4.last.getId + 2000, 3, false).toList}
//             val rv6 = timing("rv6"){dli.slice[SimplePost](rv5.last.getId + 25000, 6, false).toList}
//
//             val rv1StartIdx = rv1.head.getId
//             val rv2StartIdx = rv2.head.getId
//             val rv3StartIdx = rv3.head.getId
//             val rv4StartIdx = rv4.head.getId
//             val rv5StartIdx = rv5.head.getId
//             val rv6StartIdx = rv6.head.getId
//
//             (rv1.map(_.content) must contain("hello #" + rv1StartIdx, "hello #" + (rv1StartIdx + 1), "hello #" + (rv1StartIdx + 2), "hello #" + (rv1StartIdx + 3), "hello #" + (rv1StartIdx + 4)).only) and
//             (rv2.map(_.content) must contain("hello #" + rv2StartIdx, "hello #" + (rv2StartIdx + 1), "hello #" + (rv2StartIdx + 2)).only) and
//             (rv3.map(_.content) must contain("hello #" + rv3StartIdx, "hello #" + (rv3StartIdx + 1), "hello #" + (rv3StartIdx + 2)).only) and
//             (rv4.map(_.content) must contain("hello #" + rv4StartIdx, "hello #" + (rv4StartIdx + 1), "hello #" + (rv4StartIdx + 2)).only) and
//             (rv5.map(_.content) must contain("hello #" + rv5StartIdx, "hello #" + (rv5StartIdx + 1), "hello #" + (rv5StartIdx + 2)).only) and
//             (rv6.map(_.content) must contain("hello #" + rv6StartIdx, "hello #" + (rv6StartIdx + 1), "hello #" + (rv6StartIdx + 2), "hello #" + (rv6StartIdx + 3), "hello #" + (rv6StartIdx + 4), "hello #" + (rv6StartIdx + 5)).only)
//         }
//
//         def sliceRight = {
//             val rv1 = timing("rv1"){dli.slice[SimplePost](0, 5, true).toList}
//             println("sliceRight rv1: " + rv1)
//             val rv2 = timing("rv2"){dli.slice[SimplePost](rv1.last.getId, 3, true).toList}
//             val rv3 = timing("rv3"){dli.slice[SimplePost](rv2.last.getId, 3, true).toList}
//             val rv4 = timing("rv4"){dli.slice[SimplePost](rv3.last.getId - 1000, 3, true).toList}
//             val rv5 = timing("rv5"){dli.slice[SimplePost](rv4.last.getId - 14000, 3, true).toList}
//             val rv6 = timing("rv6"){dli.slice[SimplePost](rv5.last.getId, 6, true).toList}
//             val rv7 = timing("rv7"){dli.slice[SimplePost](0, 3, true).toList}
//
//             val rv1StartIdx = rv1.head.getId
//             val rv2StartIdx = rv2.head.getId
//             val rv3StartIdx = rv3.head.getId
//             val rv4StartIdx = rv4.head.getId
//             val rv5StartIdx = rv5.head.getId
//             val rv6StartIdx = rv6.head.getId
//             val rv7StartIdx = rv7.head.getId
//
//             (rv1.map(_.content) must contain("hello #" + rv1StartIdx, "hello #" + (rv1StartIdx - 1), "hello #" + (rv1StartIdx - 2), "hello #" + (rv1StartIdx - 3), "hello #" + (rv1StartIdx - 4)).only) and
//             (rv2.map(_.content) must contain("hello #" + rv2StartIdx, "hello #" + (rv2StartIdx - 1), "hello #" + (rv2StartIdx - 2)).only) and
//             (rv3.map(_.content) must contain("hello #" + rv3StartIdx, "hello #" + (rv3StartIdx - 1), "hello #" + (rv3StartIdx - 2)).only) and
//             (rv4.map(_.content) must contain("hello #" + rv4StartIdx, "hello #" + (rv4StartIdx - 1), "hello #" + (rv4StartIdx - 2)).only) and
//             (rv5.map(_.content) must contain("hello #" + rv5StartIdx, "hello #" + (rv5StartIdx - 1), "hello #" + (rv5StartIdx - 2)).only) and
//             (rv6.map(_.content) must contain("hello #" + rv6StartIdx, "hello #" + (rv6StartIdx - 1), "hello #" + (rv6StartIdx - 2), "hello #" + (rv6StartIdx - 3), "hello #" + (rv6StartIdx - 4), "hello #" + (rv6StartIdx - 5)).only) and
//             (rv7.map(_.content) must contain("hello #" + rv7StartIdx, "hello #" + (rv7StartIdx - 1), "hello #" + (rv7StartIdx- 2)).only)
//         }
//
//         def getCount = {
//             timing("getCount"){
//                 dli.getCount must_== 36701
//             }
//         }
//
//         def paging = {
//             def test[TModel <: BaseModel[IDType]](limitPerPage:Int,total:Int,reverse:Boolean)(implicit m:Manifest[TModel]) = {
//
//                 println(s" TEST PAGING lpp: $limitPerPage for total: $total reverse: $reverse")
//
//                 // cycle gak penting, yang penting count
////                 var cycle = 0
//                 var count = 0
//                 var items = scala.collection.mutable.Set[TModel]()
//
//                 dli.paging[TModel](None, None, limitPerPage, Some(total), reverse){ (page, ds) =>
//                     println("page #" + page + " --------------------")
//                     ds.foreach { p =>
//                         print(p.getId + ",")
//                         items += p
//                         count += 1
//                     }
//                     println("")
//                     true
//                 }
//                 (count must_== total) and
//                     (items.size must_== total) // distinct counting matcher
//             }
//
//             def multiTest[TModel <: BaseModel[IDType]]()(implicit m:Manifest[TModel]) = {
//                 test[TModel](10,100, true) and
//                     test[TModel](5,100, true) and
//                     test[TModel](100,305, true) and
//                     test[TModel](10,210, false) and
//                     test[TModel](5,110, false) and
//                     test[TModel](100,405, false) and
//                     test[TModel](100,5013, true)
//             }
//
//
//             timing("paging"){
//                 multiTest[SimplePost]() and
//                 multiTest[BaseModel[IDType]]()
//             }
//         }
//
//         def transIndex = {
//             Post.getDaoListIndex.reset()
//             val cu = genUser
//             val p = Post.createSimple(cu, "hello aoi", cu)
//             val rv = Post.getListRight(None, None, 2).toList
//             println("transIndex.rv: " + rv)
//             rv must contain(p)
//         }
//
//
//         def rem = {
//
//             dli.remove(dli.slice[SimplePost](0,1, false).toList.head.getVertex)
//             val rv1 = timing("rv1 after remove"){dli.slice[SimplePost](0, 5, false).toList}
//
//             println(rv1)
//
//             rv1.map(_.content) must not contain("hello #1")
//         }
//
//         def reset = {
//             dli.reset()
//             // ini gak bisa langsung ditest karena ini http://wiki.apache.org/cassandra/DistributedDeletes
////             dli.slice[SimplePost](0,1000,10,false).toList must beEmpty
//             // asal gak terjadi exception anggap aja sukses
//             success
//         }
//
//
//     }
//
//////  last performance result for 600.000 entry:
////
////    .... done in 301.351013s (301351ms) [filling data]
////    ----- rv1:
////    .... done in 2.263000s (2263ms) [rv1]
////    ----- rv2:
////    .... done in 8ms [rv2]
////    ----- rv3:
////    .... done in 13ms [rv3]
////    ----- rv4:
////    .... done in 41ms [rv4]
////    ----- rv5:
////    .... done in 60ms [rv5]
////    ----- rv6:
////    .... done in 10ms [rv6]
//
// }
//
