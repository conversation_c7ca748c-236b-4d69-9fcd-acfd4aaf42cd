/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.helpers

/**
 * Author: robin
 * Date: 4/4/14
 * Time: 4:00 AM
 *
 */

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import com.ansvia.digaku.model.Label
import org.specs2.specification.Step
import com.ansvia.digaku.Types.IDType

class RootVertexSpec extends Specification with DigakuTest {

    def is = "RootVertex should" ^
        sequential ^
        Step {
            RootVertex.DEFAULT_SIZE_PER_PARTITION = 10
        } ^
        "first addition masuknya ke partition 0" ! trees.firstAddPart0 ^
        "after X(size) addition masuknya ke partition 1" ! trees.nextPart(1) ^
        "after X(size) addition masuknya ke partition 2" ! trees.nextPart(2) ^
        "after X(size) addition masuknya ke partition 3" ! trees.nextPart(3) ^
        "after X(size) addition masuknya ke partition 4" ! trees.nextPart(4) ^
        "after X(size) addition masuknya ke partition 5" ! trees.nextPart(5) ^
        "after X(size) addition masuknya ke partition 6" ! trees.nextPart(6) ^
        "after X(size) addition masuknya ke partition 7" ! trees.nextPart(7) ^
        "paginate across partition pf=22" ! trees.paginate(22) ^
        "paginate across partition pf=20" ! trees.paginate(20) ^
        "paginate across partition pf=17" ! trees.paginate(17) ^
        "paginate across partition pf=15" ! trees.paginate(15) ^
        "paginate across partition pf=10" ! trees.paginate(10) ^
        "paginate across partition pf=5" ! trees.paginate(5) ^
        "paginate across partition pf=3" ! trees.paginate(3) ^
        "paginate across partition pf=2" ! trees.paginate(2) ^
        "paginate across partition with max page" ! trees.paginateMaxPage ^
        "paginate across partition with startsId pf=10" ! trees.paginateWithStartsId(0,3,10,7) ^
        "paginate across partition with startsId pf=10 #2" ! trees.paginateWithStartsId(1,3,10,17) ^
        "bisa mendapatkan total partition di root vertex config" ! trees.getTotalPart ^
        "bisa mendapatkan total data dalam collection dari semua partition" ! trees.getCount ^
        "bisa reset" ! trees.reset ^
        Step {
           RootVertex.DEFAULT_SIZE_PER_PARTITION = 10000
        } ^
        end

    object trees {

        object roov extends RootVertex with DbAccess {

            val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.helpers.testing.RootVertex"
            val rootVertexLabel: String = Label.DAO_LIST
        }

        val v1 = db.addVertex(null)
        db.commit()

        def firstAddPart0 = {
            roov.addToRoot(v1)
            roov.whereIs(v1) must_== 0
        }

        def nextPart(part:Int) = {
            for(i <- 1 to 10){
                val v = db.addVertex(null)
                roov.addToRoot(v)
                db.commit()
            }
            val v = db.addVertex(null)
            roov.addToRoot(v)
            db.commit()
            roov.whereIs(v) must_== part
        }

        def paginate(pf:Int) = {
            var count = 0
            roov.paging(None, None, pf, None){ case (page, vs) =>
                println("page #" + page)
                vs.foreach { v =>
                    print(v + ",")
                    count = count + 1
                }
                println("")
                true
            }
            count must_== 78
        }

        def paginateMaxPage = {
            var count = 0
            roov.paging(None, None, 2, Some(1)){ case (page, vs) =>
                vs.foreach { v =>
                    print(v + ",")
                    count = count + 1
                }
                println("")
                true
            }
            count must_== 2
        }


        def paginateWithStartsId(partition:Int, offset:Int, pf:Int, mustBe:Int) = {

            import com.ansvia.graph.BlueprintsWrapper._

            var count = 0

            val lst = roov.rootVertex(partition).pipe.out(roov.rootVertexLabel).range(0,10).toList
            println("first roov list: " + lst)
            val startsV = lst.get(offset)
            println("startsV: " + startsV)

            roov.paging(Some(startsV.getId.asInstanceOf[IDType]), None, pf, Some(2)){ case (page, vs) =>
                println("page #" + page)
                vs.foreach { v =>
                    print(v + ",")
                    count = count + 1
                }
                println("")
                true
            }
            count must_== mustBe
        }

        def getTotalPart = {
            import com.ansvia.graph.BlueprintsWrapper._
            val v = db.getVertices("_class_", roov.ROOT_VERTEX_CLASS + "Config").iterator().next()
            (v.getOrElse(RootVertex.PARTITION_COUNT_KEY, 0) must_== 8) and
                (roov.getRootVertexPartitionCount must_== 8)
        }

        def getCount = {
            roov.getCount must_== 78
        }


        def reset = {
            val before = (roov.getAvailablePartition must beGreaterThan(0)) and
                (roov.getRootVertexPartitionCount must beGreaterThan(0))
            RootVertex.reset(roov)
            before and
            (roov.getAvailablePartition must_== 0) and
                (roov.getRootVertexPartitionCount must_== 1)
        }


    }

}

