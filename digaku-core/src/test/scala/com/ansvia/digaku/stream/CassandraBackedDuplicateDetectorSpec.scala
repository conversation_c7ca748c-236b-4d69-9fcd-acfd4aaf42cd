// @TODO(robin): benerin ini setelah major refactor

///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.stream
//
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.model.Post
//import com.ansvia.digaku.persistence.CassandraDriver
//import com.ansvia.perf.PerfTiming
//import com.netflix.astyanax.model.ColumnFamily
//import com.netflix.astyanax.serializers.{LongSerializer, StringSerializer}
//import org.specs2.mutable.Specification
//
//import scala.util.Random
//
///**
// * Author: robin
// *
// */
//class CassandraBackedDuplicateDetectorSpec extends Specification with DigakuTest with PerfTiming {
//
//    sequential
//
//
//
//    "Cassandra backed duplicate stream detector" should {
//
//        val clusterName = "digaku"
//        val keyspaceName= "digaku_test"
//        val cfName = "Standard1"
//
//
//
//        // digunakan hanya untuk ensure exists keyspace dan column family saja
//        {
//            val CF = new ColumnFamily[String, java.lang.Long](
//                cfName, StringSerializer.get(), LongSerializer.get())
//            val csh =
//                new CassandraDriver(keyspaceName, CF, clusterName, "127.0.0.1:9160",
//                    "SimpleStrategy", "replication_factor:1")
//
//
//            //    private lazy val idFactory:CassandraBackedIdFactory = new CassandraBackedIdFactory(2L, csh.context)
//
//            //    csh.keyspace.dropKeyspace()
//            csh.ensureKeyspaceExists
//
//            if (csh.columnFamilyExists(cfName))
//                csh.keyspace.dropColumnFamily(cfName)
//
//            csh.ensureColumnFamilyExists
//        }
//
//
//        val u = genUser
//        val u2 = genUser
//        val ch = genChannelWithOwner(u)
//        ch.addMembers(u2)
//        val p = Post.createSimple(u, genRandomString, ch)
//        val p2 = Post.createSimple(u, genRandomString, ch)
//        val p3 = Post.createSimple(u, genRandomString, ch)
//        val p4 = Post.createSimple(u, genRandomString, ch)
//        val p5 = Post.createSimple(u2, genRandomString, ch)
//        val ps = List(p2,p3,p4)
//
//        val duplicateDetector =
//            new CassandraBackedDuplicateDetector(clusterName, keyspaceName, "127.0.0.1:9160")
//
//        "be able to marking" in {
//            duplicateDetector.markStreamExists(u, p) must be not throwAn[Exception]
//            // since markStreamExists is async, we need to wait for 2 seconds
//            Thread.sleep(2000)
//        }
//        "be able to check" in {
//            duplicateDetector.isStreamExists(u, p) must beTrue
//        }
//        "marker should be affected immediately" in {
//            val rnd = new Random(System.currentTimeMillis())
//            timing("mark and check"){
//                for ( i <- 1 to 200 ){
//                    val _p = ps.apply(rnd.nextInt(ps.length))
//                    println(s" $i . adding stream ${_p} to $u")
//                    duplicateDetector.markStreamExists(u, _p)
//                    duplicateDetector.isStreamExists(u, _p) must beTrue
//                    duplicateDetector.isStreamExists(u, p5) must beFalse
//                }
//            }
//        }
//        "double checking" in {
//            val u2 = genUser
//
//            duplicateDetector.isStreamExists(u2, p2) must beFalse
//            duplicateDetector.markStreamExists(u2, p2)
//            duplicateDetector.isStreamExists(u2, p2) must beTrue
//            p2.getVertex.setProperty("content", "updated content")
//            val _p2 = Post.getPostById(p2.getId).get
//            _p2.getContent must beEqualTo("updated content")
//            duplicateDetector.isStreamExists(u2, p2) must beTrue
//        }
//
//    }
//}
