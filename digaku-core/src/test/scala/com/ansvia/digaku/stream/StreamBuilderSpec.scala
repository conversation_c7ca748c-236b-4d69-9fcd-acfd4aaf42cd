// @TODO(robin): benerin ini setelah major refactor
///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.stream
//
///**
// * Author: robin
// *
// */
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.model.{SimplePost, Post}
//
//class StreamBuilderSpec extends Specification with DigakuTest {
//
//    def is = "Stream builder should" ^
//        sequential ^
//        "link user join" ! trees.linkUserJoin ^
//        "link user item tidak memasukkan duplicate stream" ! trees.linkUserItemIdempotent ^
//        "delete all stream post by creator" ! trees.deleteStreamForUser ^
//        end
//
//    object trees {
//
//        val u1 = genUser
//        val u2 = genUser
//        val ch1 = genChannelWithOwner(u1)
//        val ch2 = genChannelWithOwner(u2)
//
//        val post1 = Post.createSimple(u1, "hello ini post 1", ch1); Thread.sleep(300)
//        val post2 = Post.createSimple(u1, "hello ini post 2", ch1); Thread.sleep(300)
//        val post3 = Post.createSimple(u1, "hello ini post 3", ch1); Thread.sleep(300)
//
//
//        def linkUserJoin = {
//
//            val post4 = Post.createSimple(u2, "ini post dari user 2 ke group 2", ch2)
//
//            val streamBefore = u2.getStream(0,10).toSeq.map(_.content.asInstanceOf[SimplePost])
//
//            // setelah link user join ini harusnya user 2 punya stream dari group 1
//            // tapi urutannya harus lebih dulu post-nya u2 karena post u2
//            // adalah yang paling baru
//            StreamBuilder.linkUserJoin(u2, ch1)
//
//            val streamAfter = u2.getStream(0,10).toSeq.map(_.content.asInstanceOf[SimplePost])
//
//            (streamBefore must contain(post4).only) and
//                (streamAfter must contain(post4, post3, post2, post1).only.inOrder)
//        }
//
//
//        def linkUserItemIdempotent = {
//
//            val u4 = genUser
//            val u3 = genUser
//            val ch4 = genChannelWithOwner(u4)
//
//            val post2 = Post.createSimple(u4, "u4 nulis post 2", ch4)
//            val post5 = Post.createSimple(u4, "lagi u4 nulis post 5", ch4)
//
//            ch4.addMembers(u3)
//
//            StreamBuilder.linkUserItem(u3, post2)
//
//            // walaupun di-call berkali-kali harusnya tetep tidak membuat duplicate stream (idempotent)
//            StreamBuilder.linkUserItem(u3, post5)
//            StreamBuilder.linkUserItem(u3, post5)
//            StreamBuilder.linkUserItem(u3, post5)
//            StreamBuilder.linkUserItem(u3, post2)
//            StreamBuilder.linkUserItem(u3, post5)
//
//            val stream = u3.getStream(0,50).toSeq.map(_.content.asInstanceOf[SimplePost])
//
//            println("stream.distinct: " + stream.distinct.toList)
//
//            (stream must contain(post2)) and
//            (stream must contain(post5)) and
//                (stream.distinct.length must_== 2)
//        }
//
//        def deleteStreamForUser = {
//            val user1 = genUser
//            val user2 = genUser
//            val channel1 = genChannelWithOwner(user1)
//
//            channel1.addMembers(user2)
//
//            val p1 = Post.createSimple(user1, "ini post pertama", channel1); Thread.sleep(300)
//            val p2 = Post.createSimple(user1, "ini post kedua", channel1); Thread.sleep(300)
//            val p3 = Post.createSimple(user1, "ini post ketiga", channel1); Thread.sleep(300)
//            val p4 = Post.createSimple(user2, "ini post keempat", channel1); Thread.sleep(300)
//            val p5 = Post.createSimple(user2, "ini post kelima", channel1); Thread.sleep(300)
//            val p6 = Post.createSimple(user2, "ini post keenam", channel1); Thread.sleep(300)
//
//            user1.reload()
//            user2.reload()
//
//            // @NOTE: dua stream ini perlu dievaluasi terlebih dahulu dengan meng-convert-nya ke list `toList`
//            // karena apabila tidak atau apabila dievaluasi setelah operasi penghapusan (di bagian deleteStreamFor)
//            // maka evaluasi setelah operasi tersebut mengakibatkan StackOverflowException, ini mungkin bug/unhandled
//            // exception yang terjadi pada Titan 0.5 https://github.com/thinkaurelius/titan/issues/744
//            val beforeStreamUser1 = user1.getStream(0, 10).toList.map(_.content.asInstanceOf[SimplePost])
//            val beforeStreamUser2 = user2.getStream(0, 10).toList.map(_.content.asInstanceOf[SimplePost])
//
//
//            StreamBuilder.deleteStreamFor(user1, user2)
//            StreamBuilder.deleteStreamFor(user2, user1)
//
//            db.commit()
//
//            val afterStreamUser1 = user1.getStream(0, 10).toSeq.map(_.content.asInstanceOf[SimplePost])
//            val afterStreamUser2 = user2.getStream(0, 10).toSeq.map(_.content.asInstanceOf[SimplePost])
//
//
//
//            (beforeStreamUser1 must contain(p1, p2, p3, p4, p5, p6)) and
//                (beforeStreamUser2 must contain(p1, p2, p3, p4, p5, p6)) and
//                    (afterStreamUser1 must contain(p1, p2, p3).only) and
//                        (afterStreamUser2 must contain(p4, p5, p6).only)
//
//        }
//
//    }
//
//}
