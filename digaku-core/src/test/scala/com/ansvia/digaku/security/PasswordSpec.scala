/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.security

import org.specs2.mutable.Specification

/**
 * Author: robin
 * Date: 1/28/13
 * Time: 7:52 PM
 * 
 */
class PasswordSpec extends Specification {

    "Password object" should {
        "generate password" in {
            Password.generate().length must beEqualTo(6)
        }

        "can encrypt and match" in {
            val pass = "123"
            val hashed = Password.encrypt(pass)
            hashed.length must beGreaterThan(0)
            Password.isMatch(pass, hashed, 3)
        }

        "123 is weak password" in {
            Password.isStrong("123") must beFalse
        }

        "abc123 is weak password" in {
            Password.isStrong("abc123") must beFalse
        }

        "w47x@st0 is strong password" in {
            Password.isStrong("w47x@st0") must beTrue
        }

    }
}
