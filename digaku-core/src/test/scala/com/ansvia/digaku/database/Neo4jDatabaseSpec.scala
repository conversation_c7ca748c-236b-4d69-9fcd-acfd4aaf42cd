/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

// /*
// * Copyright (C) 2013. Ansvia Inc.
// * Author: robin
// * Date: 2013/1/6
// */
// 
// package com.ansvia.digaku.database
// 
// import org.specs2.mutable.Specification
// 
// class Neo4jDatabaseSpec extends Specification {
// 
//     skipped
// //
// //    sequential
// //
// //    val dbdir = "/tmp/neo4jdbtest"
// //
// //    // clean up db dir if any
// //    Seq("rm", "-rf", dbdir) !
// //
// //    val db = new Neo4jDatabase(dbdir)
// //    Global.database = db
// //
// //
// //    "Neo4j implementation" should {
// //        var id: Int = 1
// //        var data: DigakuDbRecord = null
// //        var rvData: Option[DigakuDbRecord] = None
// //        var rv2: Array[DigakuDbRecord] = null
// //        var rv3: Array[DigakuDbRecord] = null
// //        var rv4: Array[DigakuDbRecord] = null
// //        step {
// //
// //            val id = 1
// //            data = new DigakuDbRecord
// //            data += "_id" -> id
// //            data += "name" -> "robin"
// //            db.save(data, GraphBucketType.VERTEX)
// //            rvData = db.getById(id.toString, GraphBucketType.VERTEX)
// //            rv2 = db.findByMap(Map("_id" -> id), GraphBucketType.VERTEX, 0, 10)
// //            rv3 = db.findByMap(Map("name" -> "robin"), GraphBucketType.VERTEX, 0, 10)
// //            rv4 = db.list(GraphBucketType.VERTEX)
// //
// //        }
// //        "Get name" in {
// //            db.getName must beEqualTo("Neo4j")
// //        }
// //        "retreive data by id" in {
// //            rvData.get.getOrElse("_id", "").toString must beEqualTo(id.toString)
// //        }
// //        "find by filter #1" in {
// //            rv2.length must beGreaterThan(0)
// //        }
// //        "find by filter #2" in {
// //            rv2.length must beEqualTo(1)
// //        }
// //        "find by filter return expected data" in {
// //            rv2.head.getOrElse("name", "") must beEqualTo("robin")
// //        }
// //        "find by filter non id return expected data" in {
// //            rv3.head.getOrElse("name", "") must beEqualTo("robin")
// //        }
// //        "get list" in {
// //            rv4.head.getOrElse("name", "") must beEqualTo("robin")
// //        }
// //        "delete by id" in {
// //            db.deleteById(id, GraphBucketType.VERTEX)
// //            val rv = db.findByMap(Map("_id" -> id), GraphBucketType.VERTEX, 0, 10)
// //            rv.length must beEqualTo(0)
// //        }
// //        //        step {
// //        //            var rec = new DigakuDbRecord
// //        //            rec += "name" -> "temon"
// //        //            db.save(rec, GraphBucketType.VERTEX)
// //        //            rv2 = db.findByMap(Map("name" -> "temon"), GraphBucketType.VERTEX)
// //        //            if (rv2.length == 0) {
// //        //                throw FailureException(Failure("cannot insert new user named temon"))
// //        //            }
// //        //        }
// //        "delete by map" in {
// //            //            db.deleteByMap(Map("name" -> "temon"), GraphBucketType.VERTEX)
// //            //            db.findByMap(Map("name" -> "temon"), GraphBucketType.VERTEX).length must beEqualTo(0)
// //            Skipped()
// //        }
// //    }
// 
// }
