/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku

import com.ansvia.digaku.config.EngineWithConfig
import com.ansvia.digaku.utils.DateUtils
import org.specs2.mock.Mockito

//import com.ansvia.digaku.notifications.PersistentNotificationHandler
import com.ansvia.digaku.persistence.{DummyCounterProvider, CassandraCounterProvider, CounterProvider}
import com.ansvia.digaku.se.{NopSearchEngine, DigakuSearchEngine}
import org.apache.commons.lang3.RandomStringUtils
import org.streum.configrity.Configuration

/**
 * Author: robin (<EMAIL>)
 */

abstract class DigakuTestEngine extends EngineWithConfig(Configuration.load("digaku-test.conf")) with <PERSON><PERSON><PERSON> {
    override lazy val config: EWCConfigCore = new EWCConfigCore {
        override val testMode: Boolean = true

        // disable builtin stream builder when in test mode
        override val usingExternalStreamBuilder: Boolean = true

        // make all async to be blocking in test mode
        // because berkeleyje db backend does not support multi threading
        override val useBlockingNotifierSender: Boolean = true
        override val useBlockingSEEStreamListener: Boolean = true

//        PersistentNotificationHandler.testMode = true

    }


    // make search engine is configurable during runtime
    override def searchEngine: DigakuSearchEngine = _internalSe.get()

    // changeable via setSearchEngine
    private val _internalSe: ThreadLocal[DigakuSearchEngine] = {
        val _thlocSe = new ThreadLocal[DigakuSearchEngine]()
        _thlocSe.set(NopSearchEngine)
        _thlocSe
    }

    def setSearchEngine(internalSe:DigakuSearchEngine){
        _internalSe.set(internalSe)
    }


    override val contentProcessor: ContentProcessor = NopContentProcessor


//    // perlu di-override counter provider-nya, karena user id di test mode gak random (incremental)
//    // sedangkan counter provider menggunakan cassandra dengan data yang volatile,
//    // berpotensi data tidak bersih untuk setiap kali test.
//    private object TestCassandraCounterProvider extends CassandraCounterProvider {
//        private val uniquePostfix = RandomStringUtils.random(5)
//        override def apply(rowKey: String) = super.apply(rowKey + "-" + uniquePostfix)
//    }
//
//    override lazy val counterProvider: CounterProvider = {
//        TestCassandraCounterProvider.setup(config.mainDatabase.hostName,
//            config.mainDatabase.clusterName,
//            config.mainDatabase.keyspaceName,
//            config.mainDatabase.replStrategy,
//            config.mainDatabase.replStrategyOpts)
//    }


    override lazy val counterProvider: CounterProvider = DummyCounterProvider

    // helper untuk mempermudah mocking time
    var dateUtilsMock:DateUtils = new DateUtils {}


    override def dateUtils:DateUtils = {
//        super.dateUtils
        dateUtilsMock
    }

    def resetMock(){
        dateUtilsMock = new DateUtils {}
    }

}

