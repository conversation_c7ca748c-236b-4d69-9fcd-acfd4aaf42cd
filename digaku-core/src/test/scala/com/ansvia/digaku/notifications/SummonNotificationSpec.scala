
/*
* Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
*
* This file is part of Digaku project.
*
* Unauthorized copying of this file, via any medium is strictly prohibited
* Proprietary and confidential.
*/

package com.ansvia.digaku.notifications

import impl.SummonNotification
import org.specs2.Specification
import com.ansvia.digaku.DigakuTest
import org.specs2.specification.Step
import com.ansvia.digaku.model._
import com.tinkerpop.blueprints.Vertex

/**
  * Author: robin
  * Date: 2/28/13
  * Time: 11:15 PM
  *
  */
class SummonNotificationSpec extends Specification with DigakuTest {

     def is =
        sequential ^
         "SummonNotification basic operation should" ^
//             Step(cleanUp()) ^
         p ^
            "saved into db" ! basicOp.savedToDb ^
            "get user who summoned" ! basicOp.getSummoner ^
            "get object where user summon (SimplePost)" ! basicOp.getObjSimplePost ^
            "get object where user summon (Response)" ! basicOp.getObjResponse ^
        p ^
        "SummonNotification realtime for user should" ^
        p ^
            "receive notification when summoned by other user (SimplePost)" ! realtime.gotNotifSimple ^
            "receive notification when summoned by other user (Response)" ! realtime.gotNotifResponse ^
        p ^
//        "SummonNotification groupable should" ^
//            "groupable" ! groupable.groupable ^
//             Step(tearDown()) ^
     end

    object basicOp {

        import com.ansvia.graph.BlueprintsWrapper._

        val u1Name = genUserName
        val u1 = User.create(u1Name,u1Name + "@mail.com", SexType.MALE, "01/01/1986", "123")
        val forum1 = genForumWithOwner(u1)
        val post1 = Article.create(u1, "ini title", "hi @u2", "tags", forum1)
        val resp = Response.create(u1, "hi on resp @u2")

        val ntf = SummonNotification(u1.getId, post1.getId)
        val ntfV = ntf.save()

        val ntf2 = SummonNotification(u1.getId, resp.getId)
        val ntfV2 = ntf2.save()

        def savedToDb = {
            ntfV must beAnInstanceOf[Vertex]
        }

        def getSummoner = {
            ntfV.toCC[SummonNotification].get.user.get must beEqualTo(u1)
        }

        def getObjSimplePost = {
            ntfV.toCC[SummonNotification].get.obj.get must beEqualTo(post1)
        }

        def getObjResponse = {
            ntfV2.toCC[SummonNotification].get.obj.get must beEqualTo(resp)
        }
    }

    object realtime {

        // tambah 3 karakter dibelakang-nya biar valid dihandle oleh TextExtractor
        val u1Name = genUserName + "abc"
        val u2Name = genUserName + "def"

        val u1 = User.create(u1Name, u1Name + "@mail.com", SexType.MALE, "01/01/1986", "123456")
        val u2 = User.create(u2Name, u2Name + "@mail.com", SexType.MALE, "01/01/1986", "123456")

        val forum2 = genForumWithOwner(u1)

        val post1 = Article.create(u1, "ini title", "hi @" + u2Name, "ini tag", forum2)
        val resp = Response.create(u1, "hi on resp @" + u2Name)
        post1.addResponse(resp)

        // pastikan semua notifikasi dah dihandle oleh PersistentNotificationHandler
        // sleep ini dibutuhkan karena operasi-nya asynchronous.
        Thread.sleep(2000)

        def gotNotifSimple = {
            u2.getNotifications(0,5).map(_.renderFor(u2)) must be contain("$[user:%s;%s] menyebut anda di dalam $[post:%s;%s;%s]".format(u1.name, u1.getId, post1.getId, post1.origin.kind, post1.origin.getName))
        }

        def gotNotifResponse = {
            u2.getNotifications(0,5).map(_.renderFor(u2)) must be contain("$[user:%s;%s] menyebut anda di dalam $[response:%s;%s;%s]".format(u1.name, u1.getId, resp.getId, resp.origin.kind, resp.origin.getName))
        }
    }
//
//    object groupable {
//
//        val u1 = genUser
//        val u2 = genUser
//        val u3 = genUser
//        val p1 = Post.createSimple(u1, "hello :)", u1)
//
//        p1.addResponse(u1, "hello @" + u2.name)
//        p1.addResponse(u3, "hello too @" + u2.name + " :D")
//
//        def groupable = {
//
//            u2.getNotifications(0, 5).toList.foreach(x => println(x.renderFor(u2)))
//
//            (u2.getNotifications(0, 5).toList.length must beEqualTo(1)) and
//                (u2.getNotifications(0, 5).toList.head.asInstanceOf[SummonNotification]
//                    .renderFor(u2) must beMatching("\\$\\[user\\:%s;%s\\] and \\$\\[user\\:%s;%s\\].+".format(u1.name, u1.getId, u3.name, u3.getId)))
//
//        }
//
//    }
}
