///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.model
//import model._
//import com.ansvia.digaku.exc.{PermissionDeniedException, AlreadyExistsException, NotSupportedException}
//import org.specs2.specification.Step
//
//
///**
// * Author: temon, robin
// *
// */
//class BecomeOwnerInviteNotificationSpec extends Specification with DigakuTest {
//
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    def is =
//        "BecomeStaffInviteNotification should" ^
//            p ^
//            sequential ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = true)
//            } ^
//            "reject get new owner correctly" ! reject.rejectGetOwner ^
//            "can't invite after invited" ! accept.doubleInvitation ^
//            "can't transfer owner group because user target has no more group quota" ! accept.userTargetNoQuotaCh ^
//            "accept get new owner correctly" ! accept.acceptGetOwner ^
//            "accept get invitor status as staff correctly" ! accept.acceptGetInvitorStatus ^
//            "reject become owner invitation notification" ! reject.rejectBecomeOwner ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = false)
//            } ^
//    end
//
//    object accept {
//
//        val u1 = genUser
//        val u2 = genUser
//
//        u1.setActivated(true)
//        u2.setActivated(true)
//
//        u1.increasePoints(15)
//        u2.increasePoints(15)
//
//        val ch1 = genForumWithOwner(u1)
//        val ch2 = genForumWithOwner(u2)
//
//        ch1.inviteToBecomeOwner(u1, u2)
//
//        Thread.sleep(1000)
//
//        val att = u2.getAttentionNotifications(0, 1).toSeq.apply(0).asInstanceOf[AcceptRejectAttention]
//
//        def doubleInvitation = {
//            ch1.inviteToBecomeOwner(u1, u2) must throwAn[AlreadyExistsException]
//        }
//
//        def userTargetNoQuotaCh = {
//            genForumWithOwner(u1)
//            genForumWithOwner(u1)
//            genForumWithOwner(u1)
//
//            (u1.channelQuota must beEqualTo(6) and
//                (ch2.inviteToBecomeOwner(u2, u1) must throwAn[PermissionDeniedException]))
//        }
//
//        def acceptGetOwner = {
//            att.accept()
//            ch1.getOwner must beSome(u2)
//        }
//
//        def acceptGetInvitorStatus = {
//            ch1.getStaffByUserName(u1.getName) must beSome(u1)
//        }
//    }
//
//    object reject {
//
//        val u1Name = genUserName
//        val u2Name = genUserName
//
//        val u1 = genUser
//        val u2 = genUser
//
//        u1.setActivated(true)
//        u2.setActivated(true)
//
//        val invited = {
//            val v = db.getVertex(u2.getId)
//            if(v != null)
//                v.toCC[User]
//            else
//                None
//        }
//
//        val ch = genForumWithOwner(u1)
//
//        ch.inviteToBecomeOwner(u1, u2)
//
//        Thread.sleep(1000)
//
//        val att = u2.getAttentionNotifications(0, 1).toSeq.apply(0).asInstanceOf[AcceptRejectAttention]
//
//        def rejectGetOwner = {
//            att.reject()
//            ch.getOwner must beSome(u1)
//        }
//
//        def rejectBecomeOwner = {
//            try {
//                u1.setActivated(true)
//            } catch {
//                case e:NotSupportedException =>
//                    val att = u2.getAttentionNotifications(0,1).toSeq.head.asInstanceOf[AcceptRejectAttention]
//
//                    att.reject()
//            }
//            Thread.sleep(2000)
//
//            u1.getNotifications(0,5).toList.map(_.renderFor(u1)).toList must be contain
//                ("Sorry, $[user:%s;%s] refused to accept your #$[ch:%s;%s] owner request now".format(invited.get.name,
//                    invited.get.getId, ch.getName, ch.getId))
//        }
//    }
//}
