// @TODO(robin): benerin ini setelah major refactor
///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications
//
//import impl._
//import org.specs2.Specification
//import com.ansvia.digaku.model._
//import org.specs2.specification.Step
//import com.ansvia.digaku.DigakuTest
//import java.util.{Date, Calendar}
//
///**
// * Author: nadir, robin
// *
// */
//class NotificationEventStreamListenerSpec extends Specification with DigakuTest {
//    def is = {
//        sequential ^
//        "notification listeners should" ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = true)
//            } ^
//            p ^
//            Step {
//                NotificationSender.addHandlers(<PERSON>mmy<PERSON><PERSON><PERSON>)
//            } ^
//            "receive join notifications" ! trees.receiveJoinNotif ^
//            "receive response notifications" ! trees.receiveResponseNotif ^
//            "receive support notifications" ! trees.receiveSupportNotif ^
//            "receive like post notifications" ! trees.receiveLikePostNotif ^
//            "receive like response notifications" ! trees.receiveLikeResponseNotif ^
//            "receive like event notifications" ! trees.receiveLikeEventNotif ^
//            "receive like picture notifications" ! trees.receiveLikePictureNotif ^
//            "receive message notifications" ! trees.receivePrivateMessageNotif ^
//            "receive message response notifications" ! trees.receiveMessageResponseNotif ^
//            Step {
//                NotificationSender.removeHandler(DummyHandler)
//            } ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = false)
//            } ^
////            Step(tearDown()) ^
//        end
//    }
//
//
//    object DummyHandler extends NotificationSendHandler {
//        val name = "dummy"
//        var receiveNotifs = Array.empty[NotificationBase]
//        def dispatch = {
//            case d:JoinNotification =>
//                receiveNotifs :+= d
//
//            case d:ResponseNotification =>
//                d.reload()
////                println("db: " + db)
////                println(d + " ntf.isSaved #2: " + d.isSaved)
//                receiveNotifs :+= d
//
//            case d:SupportNotification =>
//                receiveNotifs :+= d
//
//            case d:LikeNotification =>
//                d.reload()
//                receiveNotifs :+= d
//
//            case d:SummonNotification =>
//                d.reload()
//                receiveNotifs :+= d
//
//            case d:PrivateMessageNotification =>
//                receiveNotifs :+= d
//
//            case d:MessageResponseNotification =>
//                receiveNotifs :+= d
//
//            case _ =>
//                // swallowed
//        }
//
//    }
//
//
//
//    object trees {
//
////        println("NotificationSender.handlers: " + NotificationSender.getHandlers.mkString(","))
//
//        val user1 = genUser
//        val ch1  = genChannelWithOwner(user1)
//        val user1Id = user1.getId
//        val user2 = genUser
//        val user3 = genUser
////        ch1.setOwner(user1)
//
//        private def dueDate = {
//            val cal = Calendar.getInstance()
//            cal.setTime(new Date())
//            cal.add(Calendar.DATE, 1)
//            cal.getTime
//        }
//
//        def receiveJoinNotif = {
//            ch1.addMembers(user2)
//            Thread.sleep(500)
//            val notifsStr = DummyHandler.receiveNotifs.map(_.renderFor(user1))
//            notifsStr.toList must be contain("$[user:%s;%s] has joined your group #$[ch:%s;%s]".format(user2.name, user2.getId, ch1.name, ch1.getId))
//        }
//
//        def receiveResponseNotif = {
//            val post1 = Post.createSimple(user1, "Hello", ch1)
//            val response = post1.addResponse(user2, "Hello juga")
//            Thread.sleep(500)
//            val notifsStr = DummyHandler.receiveNotifs.map { x =>
//                x.reload()
//                x.save()
//                db.commit()
//                x.renderFor(user1)
//            }
//            notifsStr.toList must be contain("$[user:%s;%s] responded to your $[post:%s;%s;%s]".format(user2.name,
//                user2.getId,
//                post1.getId, post1.origin.kind, post1.origin.getName))
//        }
//
//        def receiveSupportNotif = {
//            user1.support(user2)
//            Thread.sleep(500)
//            val notifsStr = DummyHandler.receiveNotifs.map(_.renderFor(user2))
//
//            notifsStr.toList must be contain("$[user:%s;%s] is now supporting you".format(user1.name, user1.getId))
//        }
//
//        def receiveLikePostNotif = {
//            import com.ansvia.graph.BlueprintsWrapper._
//
//            val post2 = Post.createSimple(user1, "Hello2", ch1)
//            post2.addLike(user2)
//            post2.addLike(user3)
//            Thread.sleep(500)
//            val notifsStr = DummyHandler.receiveNotifs.map { x =>
//                val xntf = db.getVertex(x.getId).toCC[NotificationBase].get
//                xntf.renderFor(user1)
//            }
//            (notifsStr.toList must be contain("$[user:%s;%s] and $[user:%s;%s] thought your $[post:%s;%s;%s] was cool"
//                .format(user2.name, user2.getId, user3.name, user3.getId, post2.getId, post2.kindStr, post2.origin.getName)))
////            and
////                    (notifsStr.toList must be contain("$[user:%s;%s] like your post: $[post:%s;%s;%s]"
////                        .format(user3.name, user3.getId, post2.getId, post2.kind, post2.origin.getName)))
//        }
//
//        def receiveLikeResponseNotif = {
//            val post2 = Post.createSimple(user1, "Hello3", ch1)
//            val response = post2.addResponse(user2, "hello gmn kbr")
//            response.addLike(user3)
//            response.addLike(user1)
//            Thread.sleep(500)
//            val notifsStr = DummyHandler.receiveNotifs.map(_.renderFor(user2))
//            (notifsStr.toList must be contain("$[user:%s;%s] and $[user:%s;%s] thought your $[response:%s;%s;%s] was cool"
//                .format(user3.name, user3.getId, user1.name, user1.getId, response.getId, post2.kindStr, post2.origin.getName)))
//        }
//
//        def receiveLikeEventNotif = {
//            val event1 = Event.create(user1, "makan2", "disana", "dijogja", ch1, dueDate)
//            event1.addLike(user2)
//            event1.addLike(user3)
//            Thread.sleep(500)
//            val notifsStr = DummyHandler.receiveNotifs.map(_.renderFor(user1))
//
//            (notifsStr.toList must be contain("$[user:%s;%s] and $[user:%s;%s] thought your $[event:%s;%s;%s] was cool"
//                .format(user2.name, user2.getId, user3.name, user3.getId, event1.getId, event1.kindStr, event1.origin.getName)))
//        }
//
//        def receiveLikePictureNotif = {
//            val picture1 = Picture.create(user1, "dijogja", ch1)
//            picture1.addLike(user2)
//            picture1.addLike(user3)
//            Thread.sleep(500)
//            val notifsStr = DummyHandler.receiveNotifs.map(_.renderFor(user1))
//            notifsStr.toList must be contain("$[user:%s;%s] and $[user:%s;%s] thought your $[picture:%s;%s;%s] was cool"
//                .format(user2.name, user2.getId, user3.name, user3.getId, picture1.getId, picture1.kindStr, picture1.origin.getName))
//        }
//
//        def receivePrivateMessageNotif = {
//            user1.support(user2)
//            user2.support(user1)
//            user1.support(user3)
//            user3.support(user1)
//            val pm = PrivateMessage.create(user1, genRandomString, "sungguh terlalu", Array(user2, user3))
//
//            Thread.sleep(500)
//
//            val notifsStr = DummyHandler.receiveNotifs.map(_.renderFor(user2))
//
//            notifsStr.toList must be contain("You have new message $[private-message:%s] from $[user:%s;%s]".format(pm.getId, user1.getName, user1.getId))
//
//        }
//
//        def receiveMessageResponseNotif = {
//
//            val user4 = genUser
//            val user5 = genUser
//            val user6 = genUser
//
//            user4.support(user5)
//            user5.support(user4)
//            user4.support(user6)
//            user6.support(user4)
//
//            val pM2 = PrivateMessage.create(user4, genRandomString, "user1 send message to user2, user3 : hay apa kabar ?", Array(user5, user6))
//
//            val resMessage1 = pM2.addResponse(user5, "sehat bro")
//            val resMessage2 = pM2.addResponse(user4, "alhamdulillah...")
//
//            Thread.sleep(500)
//
//            val notifStrUser6 = DummyHandler.receiveNotifs.map(_.renderFor(user6)).toList
//            val notifStrUser5 = DummyHandler.receiveNotifs.map(_.renderFor(user5)).toList
//            val notifStrUser4 = DummyHandler.receiveNotifs.map(_.renderFor(user4)).toList
//
//            (notifStrUser6 must be contain("$[user:%s;%s] reply $[message-response:%s] on $[user:%s;%s]'s message $[private-message:%s]".format(user5.getName,
//                user5.getId,resMessage1.getId, user4.getName,user4.getId, pM2.getId))) &&
//                (notifStrUser4 must be contain("$[user:%s;%s] reply $[message-response:%s] on your message $[private-message:%s]".format(user5.getName,
//                    user5.getId, resMessage1.getId, pM2.getId))) &&
//            (notifStrUser5 must be contain("$[user:%s;%s] reply $[message-response:%s] on his message $[private-message:%s]"
//                .format(user4.getName,
//                user4.getId, resMessage2.getId, pM2.getId))
//                )
//        }
//    }
//}
