///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.model
//import model._
//import org.specs2.specification.Step
//
//
///**
// * Author: puput
// *
// */
//class ChannelInviteNotificationSpec extends Specification with DigakuTest {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//
//    def is =
//        sequential ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = true)
//            } ^
//            "ChannelInviteNotification basic operation should" ^
//            p ^
//            "reject group invitation notification" ! reject.userRejectedInvitation ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = false)
//            } ^
//    end
//
//    object reject {
//        val u1 = genUser
//        val u2 = genUser
//
//        val ch = genChannelWithOwner(u1)
//        ch.inviteUser(u1, u2)
//
//        Thread.sleep(2000)
//
//        val invited = {
//            val v = db.getVertex(u2.getId)
//            if(v != null)
//                v.toCC[User]
//            else
//                None
//        }
//
//        val att = u2.getAttentionNotifications(0, 1).toSeq.apply(0).asInstanceOf[AcceptRejectAttention]
//
//        def userRejectedInvitation = {
//            att.reject()
//            Thread.sleep(2000)
//
//            u1.getNotifications(0,5).map(_.renderFor(u1)).toList must be contain
//            ("Sorry, $[user:%s;%s] refused to accept your invitation to join #$[ch:%s;%s] now".format(invited.get.name,
//                invited.get.getId, ch.getName, ch.getId))
//        }
//    }
//
//}
