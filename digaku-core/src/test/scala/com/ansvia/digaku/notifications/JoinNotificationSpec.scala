package com.ansvia.digaku.notifications

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.notifications.impl.JoinNotification
import org.specs2.Specification

/**
 * Author: nadir (<EMAIL>)
 *
 */
class JoinNotificationSpec extends Specification with DigakuTest {
    def is = {
        "JoinNotification basic operation should" ^
            p ^
            sequential ^
            "get add member notification" ! trees.addMembers ^
            "get rendered add member notification" ! trees.getRenderedNotif ^
//            "get like notif for a response" ! trees.likeReply ^
//            "get groupable notif for like response notifs" ! trees.groupableLike ^
            end
    }


    object trees {

        val u1 = genUser
        val u2 = genUser
        val u3 = genUser
        val u4 = genUser
        val ch1 = genForumWithOwner(u1)

        def addMembers = {
            ch1.addMembers(u2)

            Thread.sleep(1000)

            u2.getNotifications(0, 10).length must_== 1
        }

        def getRenderedNotif = {
            ch1.addMembers(u3, u4)

            Thread.sleep(1000)

            u3.getNotifications(0, 10).head.asInstanceOf[JoinNotification].renderFor(u3) must
                beEqualTo("Anda telah ditambahkan sebagai anggota di subforum #$[ch:%s;%s]".format(ch1.getName, ch1.getId)) and
                (u4.getNotifications(0, 10).head.asInstanceOf[JoinNotification].renderFor(u4) must
                    beEqualTo("Anda telah ditambahkan sebagai anggota di subforum #$[ch:%s;%s]".format(ch1.getName, ch1.getId)))
        }
    }
}
