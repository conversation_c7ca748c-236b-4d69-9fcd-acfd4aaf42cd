///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications
//
//import org.specs2.specification.{Step, Fragments}
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import java.util.Random
//
///**
// * Author: nadir
// *
// */
//class ConnectWithTwitterNotificationSpec  extends Specification with DigakuTest {
//    def is: Fragments = {
//        "ConnectWithTwitterNotification should" ^
//            p ^
//            sequential^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = true)
//            } ^
//            "notification user connect to twitter correctly" ! trees.userGotNotif ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = false)
//            } ^
//            end
//    }
//
//
//    object trees {
//
//        import com.ansvia.graph.BlueprintsWrapper._
//
//        val user1 = genUser
//        val user2 = genUser
//
//        val rnd = new Random()
//
//        transact {
//            user1.getVertex.setProperty("tw.id", "111" + rnd.nextInt)
//            user2.getVertex.setProperty("tw.id", "222" + rnd.nextInt)
//        }
//
//        val twitterInfo1 = user1.connectTwitterInfo()
//        val twitterInfo2 = user2.connectTwitterInfo()
//
//        twitterInfo1.addFriends(twitterInfo2)
//        twitterInfo2.addFriends(twitterInfo1)
//
//
//        Thread.sleep(1000)
//
//        val att = user1.getNotifications(0, 1).toList
//
//        def userGotNotif = {
//            att.map(_.renderFor(user1)) must be contain("$[user:%s;%s], your friend on twitter, has joined mindtalk".format(user2.name, user2.getId))
//        }
//    }
//}
