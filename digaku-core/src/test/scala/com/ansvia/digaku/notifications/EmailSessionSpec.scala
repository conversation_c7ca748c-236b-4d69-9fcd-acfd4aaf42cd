// @TODO(robin): benerin ini setelah major refactor
///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications
//
///**
// * Author: robin
// * Date: 3/10/14
// * Time: 7:23 PM
// *
// */
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.notifications.impl.ResponseNotification
//import com.ansvia.digaku.model.Post
//import scala.collection.JavaConversions._
//import com.ansvia.graph.BlueprintsWrapper._
//
//class EmailSessionSpec extends Specification with DigakuTest {
//
//     def is = "Email session should" ^
//         sequential ^
//         "automatically set when inherited object saved" ! trees.autoSetWhenSaved ^
//         "not renew sid even generateSession called more than one" ! trees.keepSid ^
//     end
//
//     object trees {
//         val u1 = genUser
//         val post = Post.createSimple(u1,"hello", u1)
//         val resp = post.addResponse(u1, "ada deh")
//         val respNotif = ResponseNotification(u1.getId, resp.getId, post.getId).save().toCC[ResponseNotification].get
//         db.commit()
//
//
//         def autoSetWhenSaved = {
//             val sid = respNotif.getEmailSession
//             println("sid: " + sid)
//             sid must not equalTo("")
//         }
//
//         def keepSid = {
//             val sid = respNotif.getEmailSession
//             println("sid: " + sid)
//             respNotif.generateEmailSession()
//             respNotif.getEmailSession must_== sid
//         }
//
//
//     }
//
// }
