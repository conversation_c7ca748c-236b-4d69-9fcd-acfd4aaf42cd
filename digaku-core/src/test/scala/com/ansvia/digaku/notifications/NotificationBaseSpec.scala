/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications

import com.ansvia.digaku.model.{NotificationState, User}
import com.ansvia.digaku.notifications.impl.{DummyNotif, NotifPartition, TestNotifAttention}
import com.ansvia.digaku.{Digaku, DigakuTest}
import com.ansvia.graph.BlueprintsWrapper._
import com.tinkerpop.blueprints.{Edge, Vertex}
import org.specs2.Specification
import org.specs2.specification.Step

import scala.collection.JavaConversions._

/**
 * Author: robin
 *
 */



class NotificationBaseSpec extends Specification with DigakuTest {

    def is =
        "Notification should" ^
            Step {
                NotifUtil.customRecalcNotifCounter = (cls:String, user:User, edge:Edge) => {
                    cls match {
                        case "com.ansvia.digaku.notifications.impl.DummyNotif" =>
                            if (user.getCounter.get("notif.generic") > 0)
                                user.getCounter.decrement("notif.generic")
                            if (user.getCounter.get("notif.all") > 0)
                                user.getCounter.decrement("notif.all")
                    }
                }
            } ^
        p ^
            sequential ^
            "saved to database" ! trees.savedToDb ^
            "read from user object" ! trees.readFromUser ^
            "read from user object right order (creationTime DESC)" ! trees.readRightOrder ^
            "read from user object with offset" ! trees.readFromUserOffset ^
            "read from user object with limit" ! trees.readFromUserLimit ^
            "read from user object state unread" ! trees.readStateUnread ^
            "read from user object state read" ! trees.readStateRead ^
            "read from user object state all" ! trees.readStateAll ^
            "default is unread" ! trees.defaultIsUnread ^
            "get unread notification count from user object" ! trees.getUnreadCount ^
            "mark read" ! trees.markRead ^
            "read from user object state all #2" ! trees.readStateAll2 ^
            "use extended mapper" ! trees.useExtendedMapper ^
//            Step(tearDown()) ^
        end

    object trees {

        import com.ansvia.digaku.model.Label._

        val user1 = genUser

        var notifV1: Vertex = _
        var notifV2: Vertex = _
        var notifV3: Vertex = _

        transact {
            notifV1 = db.save(DummyNotif("notif #1"))
//            Thread.sleep(1000) // butuh ini untuk check ordering by creationTime.
            notifV2 = db.save(DummyNotif("notif #2"))
//            Thread.sleep(1000) // butuh ini untuk check ordering by creationTime.
            notifV3 = db.save(DummyNotif("notif #3"))

            val edge1 = user1 --> NOTIFICATION --> notifV1 <()
            val edge2 = user1 --> NOTIFICATION --> notifV2 <()
            val edge3 = user1 --> NOTIFICATION --> notifV3 <()

            user1.getCounter.increment("notif.generic")
            user1.getCounter.increment("notif.generic")
            user1.getCounter.increment("notif.generic")

            setEdge(edge1, notifV1)
            setEdge(edge2, notifV2)
            setEdge(edge3, notifV3)
        }

        private def setEdge(edge:Edge, ve:Vertex){
            Thread.sleep(1000)
            edge.setProperty("notifId", ve.getId)
            edge.setProperty("_target_userId", user1.getId)
            edge.setProperty("read", false)
            edge.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)
            edge.setProperty("notification.partition", NotifPartition.LEVEL_2_NOTICE)
        }

        def savedToDb = {
            db.getVertex(notifV1.getId) must not equalTo(null)
        }

        def readFromUser = {
//            println("user1.getVertex.pipe.outE(NOTIFICATION).count(): " +
//                user1.getVertex.pipe.outE(NOTIFICATION)
//                .hasNot("notifKind", NotificationKind.PRIVATE_MESSAGE)
//                .hasNot("groupMerged", true) // jangan masukkan yang dah di merge oleh [[com.ansvia.digaku.notifications.impl.ResponseNotification.regroup]]
//                .has("read", false)
//                .range(0, 10)
//                .inV()
//                .count())
            (user1.getNotifications(0, 10).length must beGreaterThan(0)) and
                (user1.getNotifications(0, 5).map(_.asInstanceOf[DummyNotif].text).toList must be contain("notif #1"))
        }

        def readRightOrder = {
            (user1.getNotifications(0, 10).map(_.asInstanceOf[DummyNotif].text).toList
                must be contain("notif #3","notif #2", "notif #1")).inOrder
        }

        /**
         * harusan tidak dapet notif #1 hanya notif #2 (ordering terbalik/terbaru duluan).
         * @return
         */
        def readFromUserOffset = {
            val rv = user1.getNotifications(1, 5).map(_.asInstanceOf[DummyNotif].text).toList
            (rv must be contain("notif #1")) and
                (rv must not contain("notif #2"))
        }

        /**
         * harusan hanya dapet notif #2 tidak notif #1 (ordering terbalik/terbaru duluan).
         * @return
         */
        def readFromUserLimit = {
            val rv = user1.getNotifications(0, 1).map(_.asInstanceOf[DummyNotif].text).toList
            (rv must be contain("notif #3")) and
                (rv must not contain("notif #2"))
        }

        def readStateUnread = {
            user1.getNotifications(0, 10, NotificationState.UNREAD)
                .map(_.asInstanceOf[DummyNotif].text).length must beEqualTo(3)
        }

        def readStateRead = {
            user1.getNotifications(0, 10, NotificationState.READ)
                .map(_.asInstanceOf[DummyNotif].text).length must beEqualTo(0)
        }

        def readStateAll = {
            user1.getNotifications(0, 10, NotificationState.ALL)
                .map(_.asInstanceOf[DummyNotif].text).length must beEqualTo(3)
        }

        def defaultIsUnread = {
            notifV1.pipe.inE(NOTIFICATION).iterator()
                .toList
                .head
                .getOrElse("read", true) must beFalse
        }

        def getUnreadCount = {
            user1.reload()
            (user1.getUnreadNotificationCount must beEqualTo(3))
        }

        def markRead = {
            val ntf = user1.getNotifications(0, 1).toSeq.head
            user1.reload()
            user1.markReadNotif(ntf, state = true)
            (user1.getUnreadNotificationCount must beEqualTo(2))
        }

        def readStateAll2 = {
            user1.getNotifications(0, 10, NotificationState.ALL)
                .map(_.asInstanceOf[DummyNotif].text).length must beEqualTo(3)
        }

        def useExtendedMapper = {
            val attentionV = TestNotifAttention().save()
            db.commit()

            // extend attention mapper, agar NotificationMapper bisa menghandle jenis notification baru
            // dari layer atasnya.
            NotificationMapper.extendedAttentionMapper.append({
                case v if v.getProperty[String]("_class_") == "com.ansvia.digaku.notifications.impl.TestNotifAttention" =>
                    v.toCC[com.ansvia.digaku.notifications.impl.TestNotifAttention]
            })

            // harusnya dapat TestNotifAttention
            NotificationMapper.attentionMapper(attentionV) must beAnInstanceOf[Some[TestNotifAttention]]
        }


    }

}

