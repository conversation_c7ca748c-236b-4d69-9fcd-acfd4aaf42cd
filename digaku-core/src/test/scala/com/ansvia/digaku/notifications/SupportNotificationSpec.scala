///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications
//
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.notifications.impl.SupportNotification
//import org.specs2.Specification
//import org.specs2.specification.Step
//
///**
//  * Author: robin
//  *
//  */
//class SupportNotificationSpec extends Specification with DigakuTest {
//
//     def is =
//         "SupportNotification basic operation should" ^
//         p ^
//            sequential ^
//             Step {
////                 BlockingPersistentNotificationHandler.setEnabled(state = true)
//             } ^
////             Step(cleanUp()) ^
//            "user got notification" ! trees.userGotNotif ^
//             Step {
////                 BlockingPersistentNotificationHandler.setEnabled(state = false)
//             } ^
////             Step(tearDown()) ^
//     end
//
//    object trees {
//
//        val u1 = genUser
//        val u2 = genUser
//
//        u1.support(u2)
//
//        def userGotNotif = {
//            Thread.sleep(1000)
//            u2.getNotifications(0, 1) must be contain(SupportNotification(u1.getId,u2.getId))
//        }
//    }
//
// }
