///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import org.specs2.specification.{Fragments, Step}
//
///**
// * Author: nadir, robin
// *
// */
//class ConnectWithFacebookNotificationSpec extends Specification with DigakuTest {
//    def is: Fragments = {
//        "ConnectWithFacebookNotification should" ^
//            p ^
//            sequential^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = true)
//            } ^
//            "notification user connect to facebook correctly" ! trees.userGotNotif ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = false)
//            } ^
//            end
//    }
//
//
//    object trees {
//
//        import com.ansvia.graph.BlueprintsWrapper._
//
//        val user1 = genUser
//        val user2 = genUser
//
//
//        transact {
//            user1.getVertex.setProperty("fb.id", genRandomNumber().toString)
//            user2.getVertex.setProperty("fb.id", genRandomNumber().toString)
//        }
//
//        val facebookInfo1 = user1.connectFacebookInfo()
//        val facebookInfo2 = user2.connectFacebookInfo()
//
//        facebookInfo1.addFriends(facebookInfo2)
//        facebookInfo2.addFriends(facebookInfo1)
//
//
//        Thread.sleep(1000)
//
//        val att = user1.getNotifications(0, 1).toList
//
//        def userGotNotif = {
//            att.map(_.renderFor(user1)) must be contain("$[user:%s;%s], your friend in facebook, has joined mindtalk".format(user2.name, user2.getId))
//        }
//    }
//}
