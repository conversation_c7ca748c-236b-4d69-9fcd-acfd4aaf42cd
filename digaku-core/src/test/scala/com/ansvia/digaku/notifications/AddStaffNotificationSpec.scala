package com.ansvia.digaku.notifications

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.notifications.impl.AddStaffNotification
import org.specs2.Specification
import org.specs2.specification.Fragments

/**
 * Author: nadir (<EMAIL>)
 *
 */
class AddStaffNotificationSpec extends Specification with DigakuTest {
    override def is: Fragments =
        "AddStaffNotification should" ^
            p ^
            sequential ^
            "get and rendering notification add staff to forum" ! trees.addStaff ^
            end

    object trees {
        val user1 = genUser
        val user2 = genUser
        val user3 = genUser
        val forum1 = genForumWithOwner(user1)

        forum1.addMembers(user2, user3)

        def addStaff = {
            forum1.addStaff(user2, "moderator", Array.empty[String])
            forum1.addStaff(user3, "moderator", Array.empty[String])

            val ntf1 = user2.getNotifications(0, 10).filter(_.isInstanceOf[AddStaffNotification]).head
            val ntf2 = user3.getNotifications(0, 10).filter(_.isInstanceOf[AddStaffNotification]).head

            ntf1.renderFor(user2) must beEqualTo("Anda telah diangkat sebagai moderator di #$[ch:%s;%s]".format(forum1.getName, forum1.getId)) and
                (ntf2.renderFor(user3) must beEqualTo("Anda telah diangkat sebagai moderator di #$[ch:%s;%s]".format(forum1.getName, forum1.getId)))
        }

    }
}