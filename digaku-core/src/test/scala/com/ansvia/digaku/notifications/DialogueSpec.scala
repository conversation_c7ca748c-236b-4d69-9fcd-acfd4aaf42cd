// @TODO(robin): benerin ini setelah major refactor
///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications
//
///**
// * Author: robin
// * Date: 11/2/13
// * Time: 1:00 PM
// *
// */
//
//
// import com.ansvia.digaku.DigakuTest
// import org.specs2.Specification
//import com.ansvia.digaku.model.Post
//
//
//class DialogueSpec extends Specification with DigakuTest {
//     def is = "Dialogue function should" ^
//         sequential ^
//         "get unread dialogue count" ! trees.getUnread ^
//         "get unread dialogue count not interfered by non dialogue notif" ! trees.notInterfered ^
//     end
//
//     object trees {
//
//         val u1 = genUser
//         val u2 = genUser
//
//         val post = Post.createSimple(u1, "hell yeah", u1)
//
//         post.addResponse(u2, "apaan tuh?")
//
//
//         def getUnread = {
//             (u1.getUnreadDialogueCount must beEqualTo(1)) and
//                 (u1.getUnreadNotificationCount must beEqualTo(0))
//         }
//
//         def notInterfered = {
//
//             post.addLike(u2)
//
//             (u1.getUnreadDialogueCount must beEqualTo(1)) and
//                 (u1.getUnreadNotificationCount must beEqualTo(1))
//         }
//
//
//
//
//     }
//
// }
