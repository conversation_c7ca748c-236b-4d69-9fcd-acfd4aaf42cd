///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications
//
//import com.ansvia.digaku.notifications.impl.ShoutNotification
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.model._
//import org.specs2.specification.Step
//
///**
//  * Author: robin
//  *
//  */
//class ShoutNotificationSpec extends Specification with DigakuTest {
//
//     def is =
//        sequential ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = true)
//            } ^
//         "ShoutNotification should" ^
//         p ^
//            "get simple notif" ! trees.simple ^
//            "groupable" ! trees.groupable ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = false)
//            } ^
//     end
//
//
//    object trees {
//
//        val u1 = genUser
//        val u2 = genUser
//        val u3 = genUser
//        val p1 = Post.createSimple(u1, "hello :)", u1)
//
//        u2.shout(p1, "shout #1")
//
//
//        def simple = {
//            u1.getNotifications(0, 10).toList.length must_== 1
//        }
//
//
//        def groupable = {
//
//            u3.shout(p1, "shout #2")
//
//            (u1.getNotifications(0, 10).toList.length must_== 1) and
//                (u1.getNotifications(0,10).toList.head must beAnInstanceOf[ShoutNotification]) and
//                (u1.getNotifications(0,10).toList.head.asInstanceOf[ShoutNotification].renderFor(u1)
//                    must beEqualTo("""$[user:%s;%s] and $[user:%s;%s] retalked your post: $[post:%s;SimplePost;%s]"""
//                        .format(u2.name, u2.getId, u3.name, u3.getId, p1.getId, u1.name)))
//
//        }
//
//    }
//
// }
