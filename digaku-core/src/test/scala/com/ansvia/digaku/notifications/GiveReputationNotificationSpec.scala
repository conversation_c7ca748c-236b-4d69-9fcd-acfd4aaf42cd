package com.ansvia.digaku.notifications

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model.{Reputation, Article}
import com.ansvia.digaku.notifications.impl.GiveReputationNotification
import org.specs2.Specification
import org.specs2.specification.Fragments

/**
 * Author: nadir (<EMAIL>)
 *
 */
class GiveReputationNotificationSpec  extends Specification with DigakuTest {
    override def is: Fragments =
        "GiveReputationNotification should" ^
            p ^
            sequential ^
            "get give reputation notification" ! trees.reputationNotif ^
            "get groupable reputation notification" ! trees.groupable ^
            end

    object trees {

        val user1 = genUser
        val user2 = genUser
        val user3 = genUser
        val user4 = genUser

        val forum1 = genForumWithOwner(user1)

        user1.incrementPublishedContentCount(500)
        user2.incrementPublishedContentCount(500)
        user3.incrementPublishedContentCount(500)
        user4.incrementPublishedContentCount(500)

        val post1 = Article.create(user1, "title art", "content article", "tags", forum1)
        val post2 = Article.create(user2, "title art 2", "content article 2", "tags", forum1)

        def reputationNotif = {
            user2.giveReputation(post1, Reputation.Good, "ini aku kasih cendol")

            Thread.sleep(1000)

            user1.getNotifications(0, 10).length must_== 1

        }

        def groupable = {
            user1.giveReputation(post2, Reputation.Good, "ko article nya bagus")
            user3.giveReputation(post2, Reputation.Good, "iya bagus")

            Thread.sleep(1000)

            user2.getNotifications(0, 10).head.asInstanceOf[GiveReputationNotification].renderFor(user2) must
                beEqualTo("$[user:%s;%s] and $[user:%s;%s] menambahkan reputasi untuk post Anda.".format(user3.getName, user3.getId, user1.getName, user1.getId))

        }
    }

}
