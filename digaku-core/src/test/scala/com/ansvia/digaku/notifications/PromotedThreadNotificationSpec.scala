package com.ansvia.digaku.notifications

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model.{UserRole, Promoted, Article}
import com.ansvia.digaku.notifications.impl.PromotedThreadNotification
import org.specs2.Specification
import org.specs2.specification.Fragments

/**
 * Author: nadir (<EMAIL>)
 *
 */
class PromotedThreadNotificationSpec extends Specification with DigakuTest {
    override def is: Fragments =
        "PromotedThreadNotification should" ^
            p ^
            sequential ^
            "get and rendering notification promoted thread." ! trees.promotedNotif ^
            end

    object trees {

        val user1 = genUser
        val user2 = genUser
        val user3 = genUser
        user2.setRole(UserRole.SUPER_ADMIN, true)

        val forum1 = genForumWithOwner(user1)

        val post1 = Article.create(user1, "ini title", "ini content", "tags", forum1)
        val post2 = Article.create(user3, "ini title", "ini content", "tags", forum1)

        def promotedNotif = {
            Promoted.create(user2, post1, Promoted.Kind.POST, "http://bcasite.com/image.jpg", "")
            Promoted.create(user2, post2, Promoted.Kind.POST, "http://bcasite.com/image.jpg", "")

            Promoted.setVisible(post1.getVertex, Promoted.Kind.POST, user2)
            Promoted.setVisible(post2.getVertex, Promoted.Kind.POST, user2)

            user1.getNotifications(0, 10).head.asInstanceOf[PromotedThreadNotification].renderFor(user1) must
                beEqualTo("Selamat, thread $[post:%s;%s;%s] telah dipromosikan!".format(post1.getId, post1.kind, post1.origin.getName)) and
                (user3.getNotifications(0, 10).head.asInstanceOf[PromotedThreadNotification].renderFor(user3) must
                    beEqualTo("Selamat, thread $[post:%s;%s;%s] telah dipromosikan!".format(post2.getId, post2.kind, post2.origin.getName)))

        }
    }

}
