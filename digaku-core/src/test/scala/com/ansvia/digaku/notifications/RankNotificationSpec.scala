package com.ansvia.digaku.notifications

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model.ExperienceRank
import com.ansvia.digaku.notifications.impl.{RankNotification, JoinNotification}
import org.specs2.Specification
import org.specs2.specification.Fragments

/**
* Author: nadir (<EMAIL>)
*
*/
class RankNotificationSpec extends Specification with DigakuTest {
    override def is: Fragments =
        "RankNotification should" ^
            p ^
            sequential ^
            "get and rendered user rank notifs" ! trees.addExperience ^
            end

    object trees {

        ExperienceRank.removeAllRank()

        val rank1 = ExperienceRank.createRank(0, "Rank 1")
        val rank2 = ExperienceRank.createRank(100, "Rank 2")
        val rank3 = ExperienceRank.createRank(200, "Rank 3")
        val rank4 = ExperienceRank.createRank(500, "Rank 4")
        val rank5 = ExperienceRank.createRank(750, "Rank 5")
        val rank6 = ExperienceRank.createRank(1000, "Rank 6")

        val user = genUser

        def addExperience = {

            user.increaseExperience(100)
            Thread.sleep(1000)
            val notif1 = user.getNotifications(0, 1).head.asInstanceOf[RankNotification].renderFor(user) must equalTo("Selamat, Anda telah mencapai rank <b>%s</b>!".format(rank2.name))

            user.increaseExperience(100)
            Thread.sleep(1000)
            val notif2 = user.getNotifications(0, 1).head.asInstanceOf[RankNotification].renderFor(user) must equalTo("Selamat, Anda telah mencapai rank <b>%s</b>!".format(rank3.name))

            user.increaseExperience(400)
            Thread.sleep(1000)
            val notif3 = user.getNotifications(0, 1).head.asInstanceOf[RankNotification].renderFor(user) must equalTo("Selamat, Anda telah mencapai rank <b>%s</b>!".format(rank4.name))

            user.increaseExperience(200)
            Thread.sleep(1000)
            val notif4 = user.getNotifications(0, 1).head.asInstanceOf[RankNotification].renderFor(user) must equalTo("Selamat, Anda telah mencapai rank <b>%s</b>!".format(rank5.name))

            user.increaseExperience(200)
            Thread.sleep(1000)
            val notif5 = user.getNotifications(0, 1).head.asInstanceOf[RankNotification].renderFor(user) must equalTo("Selamat, Anda telah mencapai rank <b>%s</b>!".format(rank6.name))

            notif1 and notif2 and notif3 and notif4 and notif5

        }
    }
}
