/*
* Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
*
* This file is part of Digaku project.
*
* Unauthorized copying of this file, via any medium is strictly prohibited
* Proprietary and confidential.
*/

package com.ansvia.digaku.notifications

import com.ansvia.digaku.notifications.impl.LikeNotification
import org.specs2.Specification
import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model._

/**
  * Author: robin
  *
  */
class LikeNotificationSpec extends Specification with DigakuTest {

     def is = {

         "JoinNotification basic operation should" ^
             p ^
             sequential ^
             "get article notif" ! trees.rateThread ^
             "groupable" ! trees.groupable ^
             "get like notif for a response" ! trees.likeReply ^
             "get groupable notif for like response notifs" ! trees.groupableLike ^
             end
     }


    object trees {

        val u1 = genUser
        val u2 = genUser
        val u3 = genUser
        val u4 = genUser
        val ch1 = genForumWithOwner(u4)

        val p1 = Article.create(u1, "title article", "ini content article", "tags", ch1)

        p1.addLikeWithRate(u2, 5)

        Thread.sleep(1000)

        def rateThread = {
            u1.getNotifications(0, 10).length must_== 1
        }


        def groupable = {

            p1.addLikeWithRate(u3, 5)

            Thread.sleep(1000)

            (u1.getNotifications(0, 10).length must_== 1) and
                (u1.getNotifications(0,10).head must beAnInstanceOf[LikeNotification]) and
                (u1.getNotifications(0,10).head.asInstanceOf[LikeNotification].renderFor(u1)
                    must beEqualTo("""$[user:%s;%s] and $[user:%s;%s] memberikan rating untuk $[post:%s;Article;%s]""".format(u2.name, u2.getId, u3.name, u3.getId, p1.getId, p1.origin.getName)))

        }

        def likeReply = {
            val resp = p1.addResponse(u3, "ini response")

            resp.addLike(u1)

            u3.getNotifications(0, 10).length must_== 1
        }

        def groupableLike = {
            val resp = p1.addResponse(u4, "ini response u4")

            resp.addLike(u1)
            resp.addLike(u3)

            (u4.getNotifications(0, 10).length must_== 1) and
                (u4.getNotifications(0,10).head must beAnInstanceOf[LikeNotification]) and
                (u4.getNotifications(0,10).head.asInstanceOf[LikeNotification].renderFor(u4)
                    must beEqualTo("""$[user:%s;%s] and $[user:%s;%s] menyukai post Anda."""
                    .format(u1.name, u1.getId, u3.name, u3.getId)))
        }

    }

}
