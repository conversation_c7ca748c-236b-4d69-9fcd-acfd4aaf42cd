///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.model
//import model.{PrivacyRole}
//import org.specs2.specification.Step
//import scala.Array
//import com.ansvia.digaku.exc.NotSupportedException
//
//
///**
// * Author: puput, robin
// *
// */
//class SupportApprovalInviteNotificationSpec extends Specification with DigakuTest {
//
//    def is =
//        sequential ^
//        "SupportApprovalInviteNotification basic operation should" ^
//            p ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = true)
//            } ^
//            "reject support aproval invitation" ! reject.userRejectedSupportApproval
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = false)
//            } ^
//            end
//
//    object reject {
//        val u1 = genUser
//        val u2 = genUser
//        val privacys:Array[String] = Array(PrivacyRole.SUPPORT_APPROVAL)
//        u2.setPrivacys(privacys)
//        u2.save()
//
//        def userRejectedSupportApproval = {
//            try {
//                u1.support(u2)
//            } catch {
//                case e:NotSupportedException =>
//                    val att = u2.getAttentionNotifications(0,1).toSeq.head.asInstanceOf[AcceptRejectAttention]
//
//                    att.reject()
//            }
//
//            Thread.sleep(2000)
//
//            u1.getNotifications(0,5).toList.map(_.renderFor(u1)).toList must be contain (("Sorry, $[user:%s;%s] refused to " +
//                "accept your supporting request now").format(u2.name, u2.getId))
//        }
//    }
//
//}
