/*
* Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
*
* This file is part of Digaku project.
*
* Unauthorized copying of this file, via any medium is strictly prohibited
* Proprietary and confidential.
*/

package com.ansvia.digaku.notifications

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.Types.GremPipeEdge
import com.ansvia.digaku.model._
import com.ansvia.digaku.notifications.impl.{NotifPartition, ResponseNotification}
import com.ansvia.graph.BlueprintsWrapper._
import org.specs2.Specification
import org.specs2.specification.Step

import scala.collection.JavaConversions._

/**
* Author: robin
*
*/
class ResponseNotificationSpec extends Specification with DigakuTest  {

    def is =
        sequential ^
        "ResponseNotification should" ^
        Step {
//            BlockingPersistentNotificationHandler.setEnabled(state = true)
        } ^
        p ^
            "post creator got notification when someone response on their post" ! trees.gotNotif ^
            "post creator got notification grouped when someones response on their post" ! trees.gotNotifGroup ^
            "Hanya creator thread yang akan dapat notif response, responder tidak akan dapet." ! trees.addAnotherResponder ^
//            "other responder got notification grouped when " +
//                "someones response on their post but not include already read notif" ! trees.gotNotifGroupOtherNotIncludingRead ^
            "parted as NotifPartition.CONVERSATION" ! trees.parted ^
        Step {
//            BlockingPersistentNotificationHandler.setEnabled(state = false)
        } ^
        end


    object trees {

        val user1 = genUser
        val user2 = genUser
        val user3 = genUser
        val user4 = genUser
        val user5 = genUser
        val user6 = genUser

        val ch1 = genForumWithOwner(user6)

        val post = Article.create(user1, "hello title", "hello content", "tags", ch1)
        val resp = post.addResponse(user2, "hello juga")

        Thread.sleep(5000)

        def gotNotif = {
            user1.getNotifications(0, 1) must be contain(ResponseNotification(user2.getId, resp.getId, post.getId))
        }

        def gotNotifGroup = {

            val resp2 = post.addResponse(user3, "hello juga dari user3")

            db.commit()

            val ntfs = user1.getNotifications(0, 5)
            val ntf = ntfs.head.asInstanceOf[ResponseNotification]

            (ntf.responderId must_== user3.getId) and
                (ntf.respondedObjectId must_== post.getId) and
                (ntfs.length must beEqualTo(1)) and
                (ntf.renderFor(user1) must_== """$[user:%s;%s] and $[user:%s;%s] membalas kiriman Anda."""
                    .format(user3.name, user3.getId, user2.name, user2.getId, post.getId, post.kind, post.origin.getName))
        }

        def addAnotherResponder = {
            val resp3 = post.addResponse(user4, "ini content response 1")
            val resp4 = post.addResponse(user5, "ini content response 2")
            val resp5 = post.addResponse(user6, "ini content response 3")

            (user5.getNotifications(0, 10).length must_== 0) and
                (user4.getNotifications(0, 10).length must_== 0)

        }

//        def gotNotifGroupOther = {
//
//            val resp3 = post.addResponse(user4, "hello juga dari user4")
//            val resp4 = post.addResponse(user5, "hello juga dari user5")
//
//            val ntfs = user2.getNotifications(0, 10).toList.filterNot(_.isInstanceOf[UserGetExtraPointNotification])
//            val ntf = ntfs.head.asInstanceOf[ResponseNotification]
//
//            (ntfs.length must_== 1) and
//                (ntf.responderId must_== user5.getId) and
//                (ntf.renderFor(user2) must beMatching(".*?, .*?, and .*? " +
//                    "responded to .*?"))
//
//        }

//        def gotNotifGroupOtherNotIncludingRead = {
//
//            import com.ansvia.digaku.model.Label._
//
//            val resp3 = post.addResponse(user4, "hello juga dari user4 #2")
//            val resp4 = post.addResponse(user5, "hello juga dari user5 #2")
//
//            var ntfs = user2.getNotifications(0, 10).toList.filterNot(_.isInstanceOf[UserGetExtraPointNotification])
//            var ntf = ntfs.head.asInstanceOf[ResponseNotification]
//
//            ntf.reload().getVertex.pipe.inE(NOTIFICATION)
//                .has("_target_userId", user2.getId)
//                .asInstanceOf[GremPipeEdge]
//                .iterator().foreach { ed =>
//
//                ed.setProperty("read", true)
//            }
//            db.commit()
//
//            val resp5 = post.addResponse(user5, "hello lagi dari user5")
//
//
//            ntfs = user2.getNotifications(0, 10).toList.filterNot(_.isInstanceOf[UserGetExtraPointNotification])
//            ntf = ntfs.head.asInstanceOf[ResponseNotification]
//
//            (ntfs.length must_== 1) and
//                (ntf.responderId must_== user5.getId) and
//                (ntf.renderFor(user2) must beMatching("""\$\[user\:.*?\] responded to .*?"""))
//
//        }

        def parted = {

            val dialogue = user1.getNotifications(0, 5, partition=NotifPartition.CONVERSATION).toList
            val dialogue2 = user1.getDialogue(0, 5, NotificationState.UNREAD)
//            val level1 = user1.getNotifications(0, 10, partition=NotifPartition.LEVEL_1_NOTICE).toList
//                .filterNot(_.isInstanceOf[UserGetExtraPointNotification])
            val level2 = user1.getNotifications(0, 5, partition=NotifPartition.LEVEL_2_NOTICE).toList
            val level3 = user1.getNotifications(0, 5, partition=NotifPartition.LEVEL_3_NOTICE).toList
            val level4 = user1.getNotifications(0, 5, partition=NotifPartition.LEVEL_4_NOTICE).toList
            val _any = user1.getNotifications(0, 5, partition=NotifPartition.ANY).toList

            val ntf = dialogue.head

//            (List(1) must contain(2)) and
            (dialogue must contain(ntf)) and
                (dialogue2 must contain(ntf)) and
//                (level1 must be not contain(ntf)) and
                (level2 must be not contain(ntf)) and
                (level3 must be not contain(ntf)) and
                (level4 must be not contain(ntf)) and
                (_any must contain(ntf))
        }

    }

}
