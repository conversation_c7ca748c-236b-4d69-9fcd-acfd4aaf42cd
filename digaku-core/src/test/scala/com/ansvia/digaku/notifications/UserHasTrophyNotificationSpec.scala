package com.ansvia.digaku.notifications

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model.VertexLabels
import com.ansvia.digaku.notifications.impl.UserHasTrophyNotification
import com.ansvia.digaku.trophy.MC2Trophy
import org.specs2.Specification
import org.specs2.specification.Fragments
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.graph.IdGraphTitanDbWrapper._

/**
 * Author: nadir (<EMAIL>)
 *
 */
class UserHasTrophyNotificationSpec extends Specification with DigakuTest {
    override def is: Fragments =
        "UserHasTrophyNotification should" ^
            p ^
            sequential ^
            "get and rendered user rank notifs" ! trees.giveTrophy ^
            end

    object trees {
        val user1 = genUser
        val user2 = genUser
        val user3 = genUser

        val trophy1 = MC2Trophy("Reputation Bronze", "Get your first reputation")
            .withAutoAssign(true)
            .withAttribute("reputation")
            .withRange(1, 100)
            .saveWithLabel(VertexLabels.TROPHY)
            .toCC[MC2Trophy]

        val trophy2 = MC2Trophy("Reputation Silver", "Get 100 reputation")
            .withAutoAssign(true)
            .withAttribute("reputation")
            .withRange(100, 500)
            .saveWithLabel(VertexLabels.TROPHY)
            .toCC[MC2Trophy]

        def giveTrophy = {

            trophy1.get.forceGiveTo(user2, Some(user1))
            trophy2.get.forceGiveTo(user3, Some(user1))

            user2.getNotifications(0, 10).head.asInstanceOf[UserHasTrophyNotification].renderFor(user2) must
                beEqualTo("Selamat, Anda berhasil mendapatkan <b>badge</b> baru!") and
                (user3.getNotifications(0, 10).head.asInstanceOf[UserHasTrophyNotification].renderFor(user3) must
                    beEqualTo("Selamat, Anda berhasil mendapatkan <b>badge</b> baru!"))

        }

    }
}
