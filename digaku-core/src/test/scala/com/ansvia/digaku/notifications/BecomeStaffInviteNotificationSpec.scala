///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.model
//import model._
//import org.specs2.specification.Step
//
///**
//  * Author: robin
//  *
//  */
//class BecomeStaffInviteNotificationSpec extends Specification with DigakuTest {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    def is =
//        "BecomeStaffInviteNotification should" ^
//            p ^
//            sequential ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = true)
//            } ^
//            "accept get title correctly" ! trees.acceptGetTitle ^
//            "accept get abilities correctly" ! trees.acceptGetAbilities ^
//            "reject become staff" ! reject.rejectBecomeStaff ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = true)
//            } ^
//    end
//
//    object trees {
//        val u1 = genUser
//        val u2 = genUser
//
//        val ch = genForumWithOwner(u1)
//
//        ch.addMembers(u2)
//
//        val abb = Array("edit", "delete")
//        ch.inviteToBecomeStaff(u1, u2, "Master", abb)
//
//        Thread.sleep(1000)
//
//        val att = u2.getAttentionNotifications(0, 1).toSeq.apply(0).asInstanceOf[AcceptRejectAttention]
//
//        def acceptGetTitle = {
//            att.accept()
//            ch.getStaffAttribute(u2).title must beEqualTo("Master")
//        }
//        def acceptGetAbilities = {
//            (ch.getStaffAttribute(u2).abilities must beEqualTo(abb)) and
//            (ch.hasAbility(u2, "edit") must beTrue) and
//            (ch.hasAbility(u2, "create") must beFalse)
//        }
//    }
//
//    object reject {
//        val u1 = genUser
//        val u2 = genUser
//
//        val ch = genForumWithOwner(u1)
//
//        ch.addMembers(u2)
//
//        val abb = Array("edit", "delete")
//        ch.inviteToBecomeStaff(u1, u2, "Master", abb)
//
//        Thread.sleep(1000)
//
//        val att = u2.getAttentionNotifications(0, 1).toSeq.apply(0).asInstanceOf[AcceptRejectAttention]
//
//        val invited = {
//            val v = db.getVertex(u2.getId)
//            if(v != null)
//                v.toCC[User]
//            else
//                None
//        }
//
//        def rejectBecomeStaff = {
//            att.reject()
//            Thread.sleep(2000)
//
//            u1.getNotifications(0,5).toList.map(_.renderFor(u1)).toList must be contain
//                ("Sorry, $[user:%s;%s] refused to accept your #$[ch:%s;%s] staff request now".format(invited.get.name,
//                    invited.get.getId, ch.getName, ch.getId))
//        }
//    }
//
// }
