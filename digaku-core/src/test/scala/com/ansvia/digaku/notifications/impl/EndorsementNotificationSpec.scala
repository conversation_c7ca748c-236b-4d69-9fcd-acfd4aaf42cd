///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications.impl
//
///**
// * Author: robin
// *
// */
//
//
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.exc.DigakuException
//import com.ansvia.digaku.model.Endorsement
//import com.ansvia.digaku.notifications.{AcceptRejectAttention}
//import com.ansvia.graph.BlueprintsWrapper._
//import org.specs2.Specification
//import org.specs2.specification.Step
//
//
//class EndorsementNotificationSpec extends Specification with DigakuTest {
//    def is = "Endorsement notification should" ^
//        sequential ^
//        p ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = true)
//            } ^
//            "can endorse" ! trees.endorse ^
//            "endorsed user can accept" ! trees.accept ^
//            "endorsed user can reject" ! trees.rejectOrIgnore ^
//            Step {
////                BlockingPersistentNotificationHandler.setEnabled(state = false)
//            } ^
//        end
//
//    object trees {
//
//
//        val u1 = genUser
//        val u2 = genUser
//        val end = Endorsement("scala-programmer").save().toCC[Endorsement].getOrElse {
//            throw new DigakuException("Cannot create endorsement object")
//        }
//        val end2 = Endorsement("vb-programmer").save().toCC[Endorsement].getOrElse {
//            throw new DigakuException("Cannot create endorsement object")
//        }
//
//
//        def endorse = {
//            var notifs = u2.getAttentionNotifications(0, 2).toList
//            val before = notifs.length must_== 0
//            Endorsement.endorse(u1, end, u2)
//            notifs = u2.getAttentionNotifications(0, 2).toList
//            val notif = notifs(0).asInstanceOf[AcceptRejectAttention]
//            val after = u2.getAttentionNotifications(0, 2).toList must contain(notif)
//            before and after
//        }
//
//        def accept = {
//            val notifs = u2.getAttentionNotifications(0, 2).toList
//            val notif = notifs(0).asInstanceOf[AcceptRejectAttention]
//            val before = notifs must contain(notif)
//            notif.accept()
//            u2.reload()
//
//            before and
//                (u2.getEndorsements(0, 2).toList must contain(end))
//
//        }
//
//        def rejectOrIgnore = {
//
//            Endorsement.endorse(u1, end2, u2)
//
//            val notifs = u2.getAttentionNotifications(0, 2).toList
//            val notif = notifs(0).asInstanceOf[AcceptRejectAttention]
//            notif.reject()
//
//            (u2.getAttentionNotifications(0, 2).toList must be not contain(notif)) and
//                (u2.getEndorsements(0, 2).toList must be not contain(end2))
//        }
//    }
//
//}
//
