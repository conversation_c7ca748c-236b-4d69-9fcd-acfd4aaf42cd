///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku
//
//import com.ansvia.digaku.DigakuTest.ChannelNameGenerator
//import exc.AlreadyExistsException
//import model.{Forum, User}
//import org.specs2.Specification
//import org.specs2.specification.Step
//
//class ChannelOperationSpec extends Specification with DigakuTest {
//
//    def is =
//        sequential ^
//        Step(cleanUp()) ^
//        "Commons forum operation" ^
//            "set owner to forum" ! trees.setChannelOwner ^
//            "tidak bisa set owner lebih dari satu" ! trees.multiChannelOwnerSet ^
//            Step(tearDown()) ^
//        end
//
//    object trees {
//        val ch1Name = ChannelNameGenerator.nextName
//
//        var u1 = genUser
//        u1.increasePoints(15)
//
//        var ch1 = Forum.create(ch1Name, "desc for ch1", u1)
//
//        ch1 = Forum.getByName(ch1Name).get
//        u1 = User.getByName(u1.name).get
//
//        def tearDown {
//            Forum.delete(Forum.getByName(ch1Name).get)
//            User.delete(User.getByName(u1.name).get)
////            Digaku.engine.database.done()
//        }
//
//        def setChannelOwner = {
//            ch1.removeOwner()
//            val u2 = genUser
//            ch1.setOwner(u2)
//            val ch1b = Forum.getByName(ch1Name).get
//            ch1b.owner.name must beEqualTo(u2.name)
//        }
//        def multiChannelOwnerSet = {
//            val ch = Forum.getByName(ch1Name).get
//            val u2 = genUser
//            ch.setOwner(u2) must throwAn[AlreadyExistsException]
//        }
//    }
//
//
//
//
//}
