///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.popular
//
///**
// * Author: robin
// * Date: 4/14/14
// * Time: 11:51 AM
// *
// */
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.model.Picture
//
//class PopularPictureBuilderSpec extends Specification with DigakuTest {
//
//    def is = "Popular picture builder should" ^
//        sequential ^
//        "build popular pictures correctly" ! trees.built ^
//        "build popular pictures not includes from private group" ! trees.builtNoPrivate ^
//        end
//
//    object trees {
//
//        import com.ansvia.graph.BlueprintsWrapper._
//
//        val u1 = genUser
//        val u2 = genUser
//
//        val ch1 = genChannelWithOwner(u1)
//        val ch2 = genChannelWithOwner(u1)
//
//
//        ch2.addMembers(u2)
//        ch2.setPrivate(true)
//
//        Picture.rootVertex(0).pipe.out(Picture.rootVertexLabel).remove()
//        db.commit()
//
//        val pic1 = Picture.create(u1,"test pop article 1", ch1,"","","")
//        val pic2 = Picture.create(u2,"test pop article 2", ch2,"","","")
//        val ppb = new PopularPictureBuilder()
//
//
//        def built = {
//            ppb.build()
//            val rv = Picture.getPopularPictures(0, 10, false).toList
//            println(rv)
//            rv.map(_._1) must contain(pic1)
//        }
//
//        def builtNoPrivate = {
//            ppb.build()
//            val rv = Picture.getPopularPictures(0, 10, false).toList
//            println(rv)
//            rv.map(_._1) must not contain(pic2)
//        }
//
//
//    }
//
//}
//
