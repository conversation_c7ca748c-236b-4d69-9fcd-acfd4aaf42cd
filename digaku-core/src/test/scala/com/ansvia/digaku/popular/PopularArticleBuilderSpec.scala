/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.popular

///**
// * Author: robin
// *
// */
//
//import com.ansvia.digaku.DigakuTest
//import org.specs2.Specification
//import com.ansvia.digaku.model.{Post, Article}
//
//class PopularArticleBuilderSpec extends Specification with DigakuTest {
//
//    def is = "Popular article builder should" ^
//        sequential ^
//        "build popuilar articles correctly" ! trees.built ^
//        "build popuilar articles not includes from private group" ! trees.builtNoPrivate ^
//        "build popuilar articles not includes post without pic" ! trees.builtNoPic ^
//        end
//
//    object trees {
//
//        import com.ansvia.graph.BlueprintsWrapper._
//
//        val u1 = genUser
//        val ch1 = genForumWithOwner(u1)
//
//        Article.rootVertex(0).pipe.out(Article.rootVertexLabel).remove()
//        db.commit()
//
//        val art1 = Article.create(u1,"test pop article 1", "content of test pop article 1", "", ch1)
//        art1.containsPic = true
//        art1.getVertex.setProperty("likesCount", 10)
//        art1.incrementViewsBy(10)
//        art1.incrementResponseCount(10)
//        art1.save()
//        db.commit()
//
//        val pab = new PopularArticleBuilder()
//
//
//        def built = {
//            pab.build()
//            val rv1 = Article.getPopularArticles(0, 10, false).toList
//            println("built rv1: " + rv1)
//
//            // setelah rebuild harusnya masih tetap konsistent dapet result yang sama
//            pab.reset()
//            pab.build()
//
//            val rv2 = Article.getPopularArticles(0, 10, false).toList
//            println("built rv2: " + rv2)
//
//            (rv1.map(_._1) must contain(art1)) and
//                (rv2.map(_._1) must contain(art1))
//        }
//
//        def builtNoPrivate = {
//
//            val ch2 = genForumWithOwner(u1)
//
//
//            val art2 = Article.create(u1, "test pop article 2", "content of test pop article 2", "", ch2)
//            art2.containsPic = true
//            art2.getVertex.setProperty("likesCount", 10)
////            art2.viewsCount = 10
//            art2.incrementViewsBy(10)
////            art2.responseCount = 10
//            art2.incrementResponseCount(10)
//            art2.save()
//            db.commit()
//
//            pab.reset()
//            pab.build()
//
//            val rv1 = Article.getPopularArticles(0, 10, false).toList
//            println("builtNoPrivate rv1: " + rv1)
//
//            ch2.setPrivate(true)
//
//            pab.reset()
//            pab.build()
//
//            val rv2 = Article.getPopularArticles(0, 10, false).toList
//
//            (rv1.map(_._1) must contain(art1, art2)) and
//                (rv2.map(_._1) must not contain(art2))
//
//        }
//
//        def builtNoPic = {
//            val art3 = Article.create(u1, "test pop article 3", "content of test pop article 3", "", ch1)
//            art3.containsPic = false
//            art3.getVertex.setProperty("likesCount", 10)
////            art3.viewsCount = 10
//            art3.incrementViewsBy(10)
////            art3.responseCount = 10
//            art3.incrementResponseCount(10)
//            art3.save()
//            val art4 = Article.create(u1, "test pop article 3", "content of test pop article 3", "", ch1)
//            art4.containsPic = false
//            art4.thumbUrl = "http://some.thumb.com/image.png"
//            art4.getVertex.setProperty("likesCount", 10)
////            art4.viewsCount = 10
//            art4.incrementViewsBy(10)
////            art4.responseCount = 10
//            art4.incrementResponseCount(10)
//            art4.save()
//            db.commit()
//
//            pab.reset()
//            pab.build()
//
//            val rv1 = Article.getPopularArticles(0, 10, false).toList
//
//
//            (rv1.map(_._1) must not contain(art3)) and
//            (rv1.map(_._1) must contain(art4)) // tetep dapet art4 karena walaupun gak keset containsPic tapi punya thumbUrl
//        }
//
//
//    }
//
//}
//
