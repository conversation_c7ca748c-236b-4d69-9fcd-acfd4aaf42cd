/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku


import java.io.File
import java.util.Random
import java.util.concurrent.atomic.AtomicLong

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.DigakuTest.AppNameGenerator
import com.ansvia.digaku.Types._
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.model._
import com.ansvia.digaku.persistence.{BackedKVStoreIface, CassandraDriver, LocalIdFactory}
import com.ansvia.digaku.validator.EmailValidator
import com.ansvia.util.idgen
import com.ansvia.util.idgen.{PrefixedIncrementalNumIdGenerator, TokenIdGenerator}
import org.apache.cassandra.io.util.FileUtils
import org.specs2.Specification
import org.specs2.mutable.{Specification => MutableSpecification}
import org.specs2.specification.{Fragments, Step}

import scala.collection.mutable.ListBuffer

/**
 * Author: robin (<EMAIL>)
 */


trait DigakuTest extends RecordGeneratorHelper with RandomStringGeneratorTestHelper with Slf4jLogger {


    protected implicit def db:GraphType = Digaku.engine.database.getRaw


    setUp()

    lazy val testEngine = Digaku.engine.asInstanceOf[DigakuTestEngine]


    def setUp(){

        if (DigakuTest.initialized)
            return

        DigakuTest.initialized = true


        try {
            Digaku.engine = new DigakuTestEngine {
                //            override val contentProcessor: ContentProcessor = null

                // @TODO(robin): untuk test sebaiknya pakai local memory saja
//                override lazy val systemConfig: BackedKVStoreIface = new CassandraBackedKVStore("Standard1", "config",
//                    config.mainDatabase.keyspaceName, config.mainDatabase.clusterName, config.mainDatabase.hostName,
//                    config.mainDatabase.replStrategy, config.mainDatabase.replStrategyOpts) {}
                override lazy val systemConfig: BackedKVStoreIface = kvStoreProvider.build("config")

                override lazy val idFactory = new LocalIdFactory()
            }

            Digaku.engine.commonCassandraCf.ensureColumnFamilyExists

            val c = Digaku.engine.config.mainDatabase

            if (!Digaku.config.mainDatabase.keyspaceName.endsWith("_test")){
                throw new DigakuException("for test please name the keyspace with postfix `_test`")
            }

            // drop keyspace (clean up working test keyspace)
            val csd = CassandraDriver.getContext(c.clusterName,
                c.keyspaceName, c.hostName, c.replStrategy, c.replStrategyOpts)

//            info("dropping keyspace " + c.keyspaceName + "...")
//            csd.cluster.getClient.dropKeyspace(c.keyspaceName)
//            Thread.sleep(1000)
//            info("drop done.")

            info("clean up database for testing...")
            csd.clearColumnFamily(Global.STANDARD_CF_NAME)
            csd.clearColumnFamily("edgestore")
            csd.clearColumnFamily("graphindex")
            csd.clearColumnFamily("counter")
            csd.clearColumnFamily("dli")
            Thread.sleep(13000)
            info("clean up done.")

            Digaku.init()

            // ketika unittesting gak perlu pake dns checking karena
            // akan fail apabila unittesting tanpa koneksi internet.
            EmailValidator.mxRecordCheck = false

            val indexDir = "/tmp/digaku-es-index-test"
            val fIndexDir = new File(indexDir)

            if (fIndexDir.exists())
                FileUtils.deleteRecursive(fIndexDir)
        }catch{
            case e:Throwable =>
                error(e.getMessage)
                e.printStackTrace()
        }

    }


}

trait BeforeAllAfterAll {
    protected def setUp()
    protected def beforeAll()
    protected def afterAll()
}

trait BeforeAllAfterAllImmutable extends Specification with BeforeAllAfterAll {
    // see http://bit.ly/11I9kFM (specs2 User Guide)
    override def map(fragments: =>Fragments) =
        Step(beforeAll) ^ fragments ^ Step(afterAll)

    protected def setUp()
    protected def beforeAll()
    protected def afterAll()
}

trait BeforeAllAfterAllMutable extends MutableSpecification with BeforeAllAfterAll {
    // see http://bit.ly/11I9kFM (specs2 User Guide)
    override def map(fragments: =>Fragments) =
        Step(beforeAll) ^ fragments ^ Step(afterAll)

    protected def setUp()
    protected def beforeAll()
    protected def afterAll()
}

trait RandomStringGeneratorTestHelper {
    private val atomicIncr = new AtomicLong(0)
    private val rnd = new Random()

    /**
     * helper for generating random number.
     * @param maxLength number max length.
     * @return
     */
    def genRandomNumber(maxLength:Int=5) = {
        System.currentTimeMillis().toString.grouped(maxLength).toSeq.reverse.head.toInt +
            rnd.nextLong() +
            atomicIncr.getAndIncrement
    }

    private object randomStringGenerator extends TokenIdGenerator

    /**
     * Helper untuk menggenerasikan random string.
     * @return
     */
    def genRandomString = {
        randomStringGenerator.nextId()
    }

}

trait RecordGeneratorHelper {

    import com.ansvia.digaku.DigakuTest.{ChannelNameGenerator, UserNameGenerator}

    implicit protected def db:GraphType

    def genUserName = UserNameGenerator.nextName
    def genAppName = AppNameGenerator.nextName
    def genForumName = ChannelNameGenerator.nextName

    def genUser = {
        val userName = genUserName
        val u = User.create(userName, userName + "@mail.com", SexType.MALE, "01/01/1986", "123456", emailValidator = false)
        u.setActivated(true)
        u.reload()
        u
    }

    def genAppWithOwner(u:User,internal:Boolean=false) = {
        val app = App.create(genAppName, "description", "0.1", "", u, internal)
        app
    }


    def genUserWithEmail(email:String) = {
        val userName = genUserName
        val u = User.create(userName, email, SexType.MALE, "01/01/1986", "123456")
        u.setActivated(true)
        u.reload()
        u
    }

    def genForum = {
        val name = genForumName
        Forum.create(name, "forum %s desc".format(name), genUser)
    }

    def genForumWithOwner(owner:User, additionalQuota:Int=1) = {
//        owner.channelQuota += additionalQuota
        val name = genForumName
        val ch = Forum.create(name, "forum %s desc".format(name), owner, Array.empty[String])
        ch
    }

}

trait RecordChannelGeneratorHelper extends RecordGeneratorHelper with RandomStringGeneratorTestHelper {

    case class ChannelMeta(group:Forum, owner:User, stream:List[Post])

    def genChannelComplete() = {
        val owner = genUser
        val ch = genForumWithOwner(owner)

        val posts = new ListBuffer[Post]

//        for ( i <- 1 to 10 ){
//            posts.append(Post.createSimple(owner, genRandomString, ch))
//        }
        for ( i <- 1 to 10 ){
            posts.append(Article.create(owner, genRandomString, genRandomString, "", ch))
        }

        ChannelMeta(ch, owner, posts.result())
    }
}

object DigakuTest extends idgen.IncrementalNumIdGenerator(1) with Slf4jLogger {
    // scalastyle:off magic.number

    //    private val lock = new com.ansvia.digaku.concurrent.Lock()
    //    var ready = false

    var initialized = false

    //    val dbTls = new ThreadLocal[TitanDatabase]()
    //    val dirTls = new ThreadLocal[String]()

    object UserNameGenerator extends PrefixedIncrementalNumIdGenerator(133L, "u") {
        def nextName = nextId()
    }

    object AppNameGenerator extends PrefixedIncrementalNumIdGenerator(133L, "app") {
        def nextName = nextId()
    }


    object ChannelNameGenerator extends PrefixedIncrementalNumIdGenerator(133L, "forum") {
        def nextName = nextId()
    }

    // scalastyle:on magic.number
}
