/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.algo

import org.specs2.mutable.Specification

/**
 * Author: robin
 *
 */
class PointLevelSpec extends Specification {

    "Point level algorithm" should {
        "old level 0 point 1 == level 0" in {
            PointLevel.calculateLevel(1) must_== 0
        }
        "old level 0 point 5 == level 0" in {
            PointLevel.calculateLevel(1) must_== 0
        }
        "old level 0 point 12 == level 1" in {
            PointLevel.calculateLevel(12) must_== 1
        }
        "old level 0 point 25 == level 2" in {
            PointLevel.calculateLevel(25) must_== 2
        }
        "old level 0 point 40 == level 2" in {
            PointLevel.calculateLevel(40) must_== 2
        }
        "old level 0 point 41 == level 3" in {
            PointLevel.calculateLevel(41) must_== 3
        }
        "calculate point lavel 3 == point 41" in {
            PointLevel.calculatePoints(3) must_== 41
        }
        "calculate point lavel 69 == 9352246" in {
            PointLevel.calculatePoints(69) must_== 9352246
        }
        "level sekarang 1 maka untuk mendapatkan level 2 membutuhkan point 13" in {
            PointLevel.calculateNextLevelPoints(1) must_== 13
        }
        "level sekarang 9 maka untuk mendapatkan level 10 membutuhkan point 54" in {
            PointLevel.calculateNextLevelPoints(9) must_== 54
        }
        "level sekarang 13 maka untuk mendapatkan level 14 membutuhkan point 108" in {
            PointLevel.calculateNextLevelPoints(13) must_== 108
        }
        "point 15 Percentage untuk menuju ke level salanjutnya (2) == 23%" in {

            PointLevel.calculatePercentageNextLevel(15) must_== 23
        }

    }
}
