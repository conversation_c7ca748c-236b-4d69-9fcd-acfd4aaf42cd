/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.concurrent

import org.specs2.mutable.Specification

/**
 * Author: robin
 * Date: 7/12/14
 * Time: 2:29 PM
 *
 */
class LockSpec extends Specification {

    def createThread(id:String, lock:Lock, delay:Int=5000) = {
        new Thread {
            private val _lock = lock
            override def run(){
                _lock.lock()
                Thread.sleep(delay)
                _lock.unlock()
            }
        }
    }


    "digaku Lock" should {
        "lock correctly" in {
            val lock = new Lock()

            createThread("th1",lock).start()

            Thread.sleep(10)

            val ms = System.currentTimeMillis()

            lock.lock()

            (System.currentTimeMillis() - ms) must beGreater<PERSON>han(4990L)
        }
        "detect deadlock when wait too long as expected" in {
            var deadlockDetected = false
            val lock = new Lock(10){
                /**
                 * override this for custom deadlock handler.
                 */
                override protected def onDeadlock(){
                    deadlockDetected = true
                }
            }

            createThread("th2", lock).start()
            Thread.sleep(1)

            lock.lock()

            deadlockDetected must beTrue
        }
    }
}
