/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku
//
//import com.ansvia.digaku.model.{Group, SexType, User}
//import com.ansvia.digaku.test.RandomStringGeneratorTestHelper
//import java.util.Random
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.validator.EmailValidator
//import com.ansvia.digaku.config.{TitanConfig, Config}
//import com.ansvia.digaku.persistence.{CassandraBackedIdFactory, CassandraDriver}
//import java.io.File
//import org.apache.cassandra.io.util.FileUtils
//import com.ansvia.digaku.se.{BlockingSEEventStreamListener, NopSearchEngine}
//import com.ansvia.digaku.event.EventStream
//import com.ansvia.digaku.notifications._
//import com.ansvia.digaku.stream.{StreamBuilderDispatcher, BlockingStreamBuilder}
//import com.ansvia.digaku.event.listener.BlockingBookmarkChannelEventStreamListener
//import com.ansvia.digaku.event.EventStreamLogger
//import com.ansvia.digaku.notifications.BlockingNotificationEventStreamListener
//import com.ansvia.digaku.event.impl.StartupEvent
//import scala.collection.JavaConversions._
//import com.ansvia.graph.BlueprintsWrapper._
//
///**
// * Author: robin
// *
// */
//
//trait DigakuPerfTest extends ModelGenerator {
//
//
//    import DigakuPerfTest.setUp
//    import DigakuPerfTest.fillDb
//
//    protected implicit def db = DigakuPerfTest.db
//
//    setUp
//    fillDb
//
//
//    def getMasterUser() = {
//        User.rootVertex.pipe.out(User.rootVertexLabel).range(0,1)
//            .headOption.flatMap(_.toCC[User]).get
//    }
//
//
//}
//
//trait ModelGenerator extends RandomStringGeneratorTestHelper {
//
//    def genRandomString:String
//
//
//    protected val rnd = new Random()
//    private val sexs = List(SexType.MALE, SexType.FEMALE)
//
//    def genUser = {
//        val name = "b" + genRandomString.slice(0, 5)
//        val bday = "01/05/1986"
//        val sex = sexs(rnd.nextInt(sexs.length-1))
//        User.create(name, name + "@mail.com", sex, bday,
//            genRandomString, activated=true, emailValidator=false)
//    }
//
//}
//
//object DigakuPerfTest extends ModelGenerator  {
//
//    import com.ansvia.digaku.model.Label._
//
//    protected implicit def db = {
//        Digaku.engine.database.getRaw[GraphType]
//    }
//
//
//    lazy val setUp = {
//
//        println("setup engine for perf test...")
//
//        // ketika unittesting gak perlu pake dns checking karena
//        // akan fail apabila unittesting tanpa koneksi internet.
//        EmailValidator.mxRecordCheck = false
//
//        Config.testMode = false
//
//        val cshCtx = CassandraDriver.getContext("digaku", "digaku2_perf_test", "127.0.0.1:9160",
//            "SimpleStrategy", "replication_factor:1")
//        val idFactory = new CassandraBackedIdFactory(2, cshCtx)
//        Global.database = TitanDatabase(TitanConfig.getDefault(), idFactory, useIdGraph = true)
//        idFactory.db = db
//
//        // ensure index
//        println("ensure db indexed...")
//        Global.database.index()
//
//        /**
//         * Gunakan blocking environment
//         * untuk menghindari race condition.
//         */
//        Config.useBlockingEventStream = true
//        Config.useBlockingEventStreamListener = true
//        Config.useBlockingNotifierSender = true
//
//        val indexDir = "/tmp/digaku-es-index-test"
//        val fIndexDir = new File(indexDir)
//
//        if (fIndexDir.exists())
//            FileUtils.deleteRecursive(fIndexDir)
//
//        Digaku.engine.searchEngine = NopSearchEngine
//
//        EventStream.setBlocking(Config.useBlockingEventStream)
//
//        EventStream.addListeners(EventStreamLogger())
//        if (!Config.useBlockingEventStreamListener)
//            EventStream.addListeners(NotificationEventStreamListener())
//        else
//            EventStream.addListeners(BlockingNotificationEventStreamListener())
//
//
//        EventStream.addListeners(new BlockingStreamBuilder(new StreamBuilderDispatcher(1)))
//
//        EventStream.addListeners(BlockingSEEventStreamListener)
//
//        EventStream.addListeners(new BlockingBookmarkChannelEventStreamListener)
//
//        NotificationSender.setBlocking(Config.useBlockingNotifierSender)
//
//        if (!Config.useBlockingNotifierSender){
//            NotificationSender.start()
//        }
//
//        if (!Config.useBlockingNotifierSender)
//            NotificationSender.addHandlers(PersistentNotificationHandler)
//        else
//            NotificationSender.addHandlers(BlockingPersistentNotificationHandler)
//
//
//        Digaku.engine.eventStream.emit(StartupEvent())
//
//    }
//
//    lazy val fillDb = {
//
//
//        val perfVO = db.getVertices("_class_", "PerformanceTestFlag").headOption
//        if (!perfVO.isDefined){
//
//            println("filling db...")
//
//            // fill db internal
//            fillUsers()
//            fillChannels()
//
//            linkUsers()
//
//            // mark as already filled
//
//            val v = db.addVertex(null)
//            v.setProperty("_class_", "PerformanceTestFlag")
//            db.commit()
//
//            println("fill db done.")
//        }
//
//    }
//
//    private val sexs = List(SexType.MALE, SexType.FEMALE)
//
//    def section(name:String)(func: => Unit) = {
//        assert(name.matches("^[\\w_\\.]+$"), "name only accept alpha numeric")
//
//        val key = "perf." + name
//        if (!db.getVertices("_class_", key).iterator().hasNext){
//
//            println("processing " + name + "...")
//
//            func
//
//
//            val v = db.addVertex(null)
//            v.setProperty("_class_", key)
//            db.commit()
//
//            println("processing " + name + " done.")
//        }
//
//    }
//
//
//    def fillUsers(){
//        section("fillUsers"){
//            println("filling users...")
//            for (i <- 1 to 1000){
//                val name = "a" + genRandomString.slice(0, 5) + "_" + i
//                val sex = sexs(i % 2)
//                val bday = "01/05/1986"
//
//                val newUser = User.create(name, name + "@mail.com", sex, bday,
//                    genRandomString, activated=true, emailValidator=false, noTx=true)
//
//                println("[%d] created: %s".format(i, newUser))
//
//                if (i % 100 == 0)
//                    db.commit()
//
//            }
//
//            db.commit()
//            println("fill user done.")
//        }
//        println("users count: " + User.getCount)
//    }
//
//    def fillChannels(){
//        section("fillChannels"){
//            println("filling channels...")
//            val owners = for (i <- 0 to 10) yield genUser
//            for (i <- 1 to 10000){
//                val name = "c" + genRandomString.slice(0,5) + "_" + i
//                val newCh = Group.create(name, name + " desc",
//                    owners(rnd.nextInt(owners.length-1)), noTx=true)
//                println("[%s] created: %s".format(i, newCh))
//
//                if (i % 100 == 0)
//                    db.commit()
//            }
//            db.commit()
//            println("fill group done.")
//        }
//        println("channels count: " + Group.rootVertex.getOrElse("count", 0L))
//    }
//
//    private def support(userA:User, userB:User){
//        val ed = userA.reload() --> SUPPORT --> userB.reload() <()
//
//        // digunakan untuk optimasi relevansi search
//        // contoh digunakan di group members relevance.
//        ed.setProperty("sourceId", userA.getId)
//        ed.setProperty("targetId", userB.getId)
//        ed.setProperty("targetName", userB.lowerName)
//
//        val v = userA.getVertex
//
//        val supportingCount = v.getOrElse("supportingCount", 0) + 1
//
//        v.setProperty("supportingCount", supportingCount)
//
//        userB.reload()
//        val targetV = userB.getVertex
//        userB.supportersCount = targetV.getOrElse("supportersCount", 0) + 1
//        targetV.setProperty("supportersCount",  userB.supportersCount)
//
//        println("  %s -support-> %s".format(userA.name, userB.name))
//    }
//
//    // user selanjutnya men-support user sebelumnya.
//    def linkUsers(){
//        section("linkUsers"){
//            println("linking users...")
//
//            var prevUser:User = null
//            var masterUser:User = null
//
//            User.rootVertex.pipe.out(User.rootVertexLabel).range(0, 100)
//                .iterator()
//                .flatMap(_.toCC[User])
//                .foreach { u =>
//
//                if (prevUser != null){
//                    support(u, prevUser)
//                }
//                prevUser = u
//                if (masterUser == null){
//                    masterUser = u
//                }
//
//            }
//            db.commit()
//
//            masterUser = User.getListLeft(Some(0), None, 5).toList.head
//
//            println("make %s supporting all existing users...".format(masterUser.name))
//
//            // masterUser buat support ke semua existing users di db
//            User.rootVertex.pipe.out(User.rootVertexLabel)
//                .iterator()
//                .flatMap(_.toCC[User])
//                .foreach { u =>
//
//                if (masterUser.getId != u.getId){
//                    support(masterUser, u)
//                }
//            }
//
//            db.commit()
//            masterUser.reload()
//
//            println("make %s joining all existing channels...".format(masterUser.name))
//
//            // buat masterUser join ke semua existing channels
//            Group.rootVertex.pipe.out(Group.rootVertexLabel)
//                .iterator()
//                .flatMap(_.toCC[Group])
//                .foreach { ch =>
//
//            //                ch.addMembers(masterUser)
//
//            //                for (u <- users) {
//            // don't join already joined users.
//                val uV = db.getVertex(masterUser.getId)
//                val chV = db.getVertex(ch.getId)
//                //                    if(!ch.isMember(masterUser)) {
//
//                val ed = uV --> JOIN --> chV <()
//
//                ed.setProperty("sourceId", masterUser.getId)
//                ed.setProperty("targetId", ch.getId)
//                ed.setProperty("userGender", masterUser.sex)
//                ed.setProperty("targetName", ch.lowerName)
//
//                //                    }
//                //                }
//
//                ch.getVertex.setProperty("memberCount", ch.getVertex.getOrElse("memberCount", 0) + 1)
//            }
//
//            db.commit()
//
//
//            println("link users done.")
//        }
//    }
//
//
//
//}
//
//
