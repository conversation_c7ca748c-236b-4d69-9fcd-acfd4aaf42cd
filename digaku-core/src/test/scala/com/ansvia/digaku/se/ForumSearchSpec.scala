/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 *
 */

package com.ansvia.digaku.se

import com.ansvia.digaku.model.Forum
import com.ansvia.digaku.{BeforeAllAfterAllMutable, DigakuTest}
import com.ansvia.perf.PerfTiming
import org.specs2.mutable.Specification

/**
 * Author: robin (<EMAIL>)
 */

class ForumSearchSpec extends Specification with BeforeAllAfterAllMutable with DigakuTest with SearchEngineTest[BeforeAllAfterAllMutable] {

    "Search engine for Forum" should {
        "cannot search privated forum by default" in trees.cantSearchPrivatedForumByDefault
        "can search all forum includes privated when includePrivate flag set" in trees.canSearchAllIncPrivated
        "can search includes joined forum even forum is privated" in trees.canSearchIncludeJoinedPrivate
        "cant search privated forum by non joined user" in trees.cantSearchPrivatedNonJoinedUser
        "can search non privated forum even searcher is set" in trees.canSearchNonPrivatedWithSearcher
        "can handle many filtered records" in trees.canHandleManyFilteredRecords
    }


    object trees extends PerfTiming {

        val ch1Name = genForumName
        val ch2Name = genForumName
        val term = genRandomString

        val u1 = genUser
//        u1.increasePoints(15)

        val u2 = genUser
        val u3 = genUser
        val u4 = genUser

        val ch1 = Forum.create("ch1_" + ch1Name, ch1Name + " " + term, u1)
        val ch2 = Forum.create("ch2_" + ch2Name, ch2Name + " " + term, u1)
        val ch3 = Forum.create("ch3_" + ch2Name, ch2Name + " meow", u4)

        ch2.setPrivate(true)
        ch2.addMembers(u3)

        // reindex
        se.indexForum(ch2)

        Thread.sleep(5000)

        def cantSearchPrivatedForumByDefault = {
            val rv = timing("cantSearchPrivatedForumByDefault"){
                se.searchForum(term)
            }

            (rv.count must_== 1) and
                (rv.entries.toList must contain(ch1).only)
        }

        def canSearchAllIncPrivated = {
            val rv = timing("canSearchAllIncPrivated"){
                se.searchForum(term, includePrivate=true)
            }

            rv.entries.toList must contain(ch1, ch2).only
        }

        def canSearchIncludeJoinedPrivate = {
            val rv = timing("canSearchIncludeJoinedPrivate #1"){
                se.searchForum(term, searcher=Some(u1))
            }

            rv.entries.toList must contain(ch1, ch2).only
        }

        def cantSearchPrivatedNonJoinedUser {
            val rv = timing("cantSearchPrivatedNonJoinedUser #1"){
                se.searchForum(term, searcher=Some(u2))
            }

            // harusnya hanya muncul ch1, karena ch1 tidak private
            rv.entries.toList must contain(ch1)
        }

        def canSearchNonPrivatedWithSearcher {
            // non privated channels and non member should be returned
            se.searchForum("meow", searcher=Some(u2)).entries.toList must contain(ch3)
        }


        def canSearchPrivatedMemberedForum = {
            val rv = timing("canSearchPrivatedMemberedForum"){
                se.searchForum(term, searcher=Some(u3))
            }

            rv.entries.toList must contain(ch2).only
        }

        def canHandleManyFilteredRecords = {

            // generate 20 records
            val term2 = genRandomString

            val forums =
            for (i <- 0 to 20)
                yield Forum.create("forum %s %s".format(i, term2),
                    "desc %s. %s %s".format(i, term2, genRandomString), u1)

            forums(5).setPrivate(true)
            forums(6).setPrivate(true)

            se.index(forums(5))
            se.index(forums(6))

            Thread.sleep(2000)

            timing("canHandleManyFilteredRecords"){
                se.searchForum(term2, searcher=Some(u3), limit = 21)
            }.entries.toList must not contain(forums(5), forums(6))

            timing("canHandleManyFilteredRecords"){
                se.searchForum(term2, searcher=Some(u1), limit = 21)
            }.entries.toList must contain(forums(5), forums(6))
        }


    }

}
