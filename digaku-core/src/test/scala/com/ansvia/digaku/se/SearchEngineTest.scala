/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 *
 */

package com.ansvia.digaku.se

import java.util.concurrent.atomic.AtomicLong

import com.ansvia.digaku._
import scala.sys.process._

/**
 * Author: robin (<EMAIL>)
 */

trait SearchEngineTest[T <: BeforeAllAfterAll] {
    this: DigakuTest with T =>

    protected def se:DigakuSearchEngine = Digaku.engine.searchEngine

    protected def beforeAll(){

        val testIndexDir = "/tmp/digaku-es-index-test-" + SearchEngineTest.idx.incrementAndGet() + "-" + System.currentTimeMillis()

        println("[%s] ..............preparing search engine for test.......................".format(Thread.currentThread().getId))
        println("got test index dir: " + testIndexDir)

        try {
            Seq("rm", "-rf", testIndexDir).!

            val se = new EmbeddedElasticSearchEngine(testIndexDir)
            Digaku.engine.asInstanceOf[DigakuTestEngine].setSearchEngine(se)
            se.ensureIndicesCreated()
        }catch{
            case e:Exception =>
                error("error during setup in scope search engine.")
                e.printStackTrace()
        }

        Thread.sleep(500)

        // set flag no state agar tidak perlu menyimpan state ketika testing
        SearchEngineIndexWorker.noState = true
        println("[%s] preparation completed.".format(Thread.currentThread().getId))
    }

    protected def afterAll(){
        Digaku.engine.asInstanceOf[DigakuTestEngine].setSearchEngine(NopSearchEngine)
        println("[%s] restore back search engine into NopSearchEngine.".format(Thread.currentThread().getId))
    }
}

object SearchEngineTest {
    val idx = new AtomicLong(0)
}