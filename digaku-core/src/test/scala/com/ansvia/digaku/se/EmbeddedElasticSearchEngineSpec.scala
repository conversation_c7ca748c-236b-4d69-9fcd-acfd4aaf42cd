/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.se

import java.util.{Calendar, Date}

import com.ansvia.digaku._
import com.ansvia.digaku.model._
//import com.ansvia.digaku.se.DigakuSearchEngine.PrivateMessageKey
import org.specs2.Specification




/**
 * Author: robin
 *
 */
class EmbeddedElasticSearchEngineSpec extends Specification with DigakuTest
        with BeforeAllAfterAllImmutable with SearchEngineTest[BeforeAllAfterAllImmutable] {


    def is = sequential ^
        "Embedded elastic search engine should" ^
        p ^
        "index article" ! trees.indexArticle ^
        "search article" ! trees.searchArticle ^
        "search article by title multi" ! trees.byTitleMulti ^
        "search article by title match all" ! trees.byTitleMatchAll ^
        "search article sort by relevance" ! trees.relevance ^
        "search article from private origin" ! trees.searchFromPrivate ^
        "search article by scope" ! trees.searchByScope ^
        "search article by tag" ! trees.searchByTag ^
        "be able to processing index history" ! trees.indexHistory ^
        p ^
//        "Search engine with deal should be able to" ^
//        p ^
//            "index and search it" ! dealTrees.index ^
//            "doing scoped search" ! dealTrees.scopedSearch ^
//            "handle from private origin" ! dealTrees.privateOrigin ^
//        p ^
        "Search engine with event should be able to" ^
        p ^
            "doing normal search" ! eventTrees.normalSearch ^
            "doing scoped search" ! eventTrees.scoped ^
            "handle from private origin" ! eventTrees.privateOrigin ^
        p ^
//        "Search engine with picture should" ^
//        p ^
//            "able to doing normal search" ! picTrees.normalSearch ^
//            "able to doing scoped search" ! picTrees.scoped ^
//            "handle from private origin" ! picTrees.privateOrigin ^
//        p ^
//        "Search engine with private message should" ^
//        p ^
//            "able to search private message for user" ! pmTrees.search1 ^
//            "not able to search private message for user from other user" ! pmTrees.search2 ^
//            "able to search by from key" ! pmTrees.searchByFrom ^
//            "able to search by participants key" ! pmTrees.searchByParticipants ^
//            "not able to search deleted pm" ! pmTrees.searchDelete ^
//        p ^
        "Search engine with App should" ^
            "able to search app" ! appTrees.search ^
            "able to delete index" ! appTrees.delete ^
        end







    object trees {

        //        val u1Name = genUserName
        //        val u2Name = genUserName
        //        val ch1Name = genChannelName

        val u1 = genUser
        val ch1 = genForum
//        ch1.setAllPermission(ForumPermission.ALL)
        var p1 = Article.create(u1, "daftar caleg dari pdip",  "gondez dan temon", "caleg,pdip,merah", ch1)
        Thread.sleep(500)
        var p2 = Article.create(u1, "montor mabur",  "ada daftar montor mabur", "montor,mabur", ch1)
        Thread.sleep(500)
        var p3 = Article.create(u1, "jin kura-kura",  "kura-kura aneh", "pelaut,air,daftar,merah", ch1)

        val chPriv = genForum
        chPriv.setPrivate(true)
        chPriv.reload()
        u1.reload()
        chPriv.addMembers(u1)

        Thread.sleep(500)
        val pInPriv1 = Article.create(u1, "dari private group daftar", "ini dari private group", "montor, ada,deh", chPriv)


        se.reset()

        def indexArticle = {
            se.indexArticle(p3)
            Thread.sleep(5000)
            se.indexArticle(p2)
            Thread.sleep(5000)
            se.indexArticle(p1)
            Thread.sleep(5000)
            true must beTrue // just ignore it
        }

        def searchArticle = {
            se.searchArticle("daftar").entries must be contain(p1)
        }

        def byTitleMulti = {
            se.searchArticle("daftar caleg").entries must be contain(p1)
        }

        def byTitleMatchAll = {
            (se.searchArticle("daftar caleg dari pdip").entries must be contain(p1)) and
                (se.searchArticle("jin kura-kura").entries must be contain(p3))
        }

        // sementara di-skip dulu, karena belum tahu, entah kenapa
        // selalu gagal ketika test secara multiple tapi selalu berhasil
        // ketika test secara single
        def relevance = {
//            Thread.sleep(10000)
            val rv = se.searchArticle("daftar").entries.toList
            println(rv)
//            (rv must be contain(p1, p3, p2)).inOrder
            pending
        }

        def searchFromPrivate = {
            se.indexArticle(pInPriv1)
            Thread.sleep(3000)
            val rv = se.searchArticle("daftar", includeFromPrivateOrigin = true).entries.toList
            val rv2 = se.searchArticle("daftar", includeFromPrivateOrigin = false).entries.toList
            println(rv)
            (rv must be contain(pInPriv1)) and
                (rv2 must be not contain(pInPriv1))
        }

        def searchByScope = {
            val rv = se.searchArticle("daftar", includeFromPrivateOrigin = true,
                scope=Some(chPriv)).entries.toList
            val rv2 = se.searchArticle("daftar").entries.toList
            (rv must contain(pInPriv1).only) and
                (rv2 must contain(p1, p2, p3).only)
        }

        def searchByTag = {
            val rv = se.searchArticleByTag("merah", includeFromPrivateOrigin = false).entries.toList
            val rv2 = se.searchArticleByTag("deh", includeFromPrivateOrigin = true,
                scope=Some(chPriv)).entries.toList
            val rv3 = se.searchArticleByTag("montor").entries.toList

            (rv must contain(p1, p3).only) and
                (rv2 must contain(pInPriv1).only) and
                (rv3 must contain(p2).only)
        }


        def indexHistory = {
            val ux = genUser
            se.indexUser(ux)
            Thread.sleep(5000)
            se.getLastIndexed must_== Some(ux)
        }


    }

//    object dealTrees {
//
//        val u = genUser
//        val ch = genChannelWithOwner(u)
//        val ch2 = genChannelWithOwner(u)
//
//        val deal1 = Deal.create(u, "laptop satu", 500, ("aaa " * 50) + " mobile",
//            Currency.IDR, Locale.id_ID, "new", "Jakarta", ch, triggerEvent = false)
//        val deal2 = Deal.create(u, "laptop dua", 500, ("aaa " * 50) + " device",
//            Currency.IDR, Locale.id_ID, "new", "Jakarta", ch2, triggerEvent = false)
//
//        def index = {
//            se.indexDeal(deal1)
//            se.indexDeal(deal2)
//            Thread.sleep(1000)
//            (se.searchDeal("satu").entries.toList must be contain(deal1) only) //and // prefixed name
//            (se.searchDeal("laptop satu").entries.toList must be contain(deal1)) and // exact name
//                (se.searchDeal("mobile").entries.toList must be contain(deal1) only) and // in description
//                (se.searchDeal("device").entries.toList must be contain(deal2) only) // in description
//        }
//
//        def scopedSearch = {
//            val rv = se.searchDeal("laptop", scope=Some(ch2)).entries.toList
//            rv must be contain(deal2) only
//        }
//
//        def privateOrigin = {
//            ch2.reload()
//            ch2.setPrivate(true)
//            val deal3 = Deal.create(u, "laptop tiga", 500, ("aaa " * 50) + " device",
//                Currency.IDR, Locale.id_ID, "new", "Jakarta", ch2, triggerEvent = false)
//
//            val rv1 = se.searchDeal("laptop", includePrivate = false).entries.toList
//            val rv2 = se.searchDeal("laptop", includePrivate = true).entries.toList
//
//            (rv1 must be not contain(deal3)) and
//                (rv2 must be contain(deal3, deal2, deal1))
//        }
//
//    }


    object eventTrees {

        val u = genUser
        val ch1 = genForumWithOwner(u)
        val ch2 = genForumWithOwner(u)
        val ch3 = genForumWithOwner(u)
        ch3.setPrivate(true)

        private def dueDate = {
            val cal = Calendar.getInstance()
            cal.setTime(new Date())
            cal.add(Calendar.DATE, 1)
            cal.getTime
        }

        private def finishDate = {
            val cal = Calendar.getInstance()
            cal.setTime(new Date())
            cal.add(Calendar.DATE, 2)
            cal.getTime
        }

        val event1 = Event.create(u,"ada deh","apa ya bingung deh","di sini", ch1, dueDate, finishDate)
        val event2 = Event.create(u,"ada deh2","apa ya bingung deh2","di sini", ch2, dueDate, finishDate)
        val event3 = Event.create(u,"ada deh3","apa ya bingung deh3","di sini", ch3, dueDate, finishDate)

        se.index(event1)
        se.index(event2)
        se.index(event3)

        Thread.sleep(1000)

        def normalSearch = {
            se.searchEvent("ada").entries.toList must be contain(event1, event2) only
        }

        def scoped = {
            se.searchEvent("ada", scope = Some(ch2)).entries.toList must be contain(event2) only
        }

        def privateOrigin = {
            (se.searchEvent("ada").entries.toList must be not contain(event3)) and
                (se.searchEvent("ada", includePrivate = true).entries.toList must be contain(event3))
        }

    }


//    object picTrees {
//
//        val u = genUser
//        val ch1 = genForumWithOwner(u)
//        val ch2 = genForumWithOwner(u)
//        val ch3 = genForumWithOwner(u)
//        ch3.setPrivate(true)
//
//        //        private def dueDate = {
//        //            val cal = Calendar.getInstance()
//        //            cal.setTime(new Date())
//        //            cal.add(Calendar.DATE, 1)
//        //            cal.getTime
//        //        }
//
//        val pic1 = Picture.create(u, "kucing gendut", ch1)
//        val pic2 = Picture.create(u, "kucing gendut2", ch2)
//        val pic3 = Picture.create(u, "kucing gendut3", ch3)
//
//        se.index(pic1)
//        se.index(pic2)
//        se.index(pic3)
//
//        Thread.sleep(1000)
//
//        def normalSearch = {
//            se.searchPicture("kucing").entries.toList must be contain(pic1, pic2) only
//        }
//
//        def scoped = {
//            se.searchPicture("kucing", scope = Some(ch2)).entries.toList must be contain(pic2) only
//        }
//
//        def privateOrigin = {
//            (se.searchPicture("kucing").entries.toList must be not contain(pic3)) and
//                (se.searchPicture("kucing", includePrivate = true).entries.toList must be contain(pic3)) and
//                (se.searchPicture("kucing", includePrivate = true, scope=Some(ch3)).entries.toList must be contain(pic3) only)
//        }
//
//    }

//    object pmTrees {
//        val u1 = genUser
//        val u2 = genUser
//        val u3 = genUser
//
//        u1.support(u2)
//        u2.support(u1)
//        val pm = PrivateMessage.create(u1, genRandomString, "hi gimana kabar user2?", Array(u2))
//
//        Thread.sleep(2000)
//
//        def search1 = {
//            val rv1 = se.searchPrivateMessage("kabar", u1, PrivateMessageKey.MESSAGE, 0, 2)
//            val rv2 = se.searchPrivateMessage("kabar", u2, PrivateMessageKey.MESSAGE, 0, 2)
//
//            (rv1.entries.toList must contain(pm).only) and
//                (rv2.entries.toList must be contain(pm))
//        }
//
//        def search2 = {
//            val rv = se.searchPrivateMessage("kabar", u3, PrivateMessageKey.MESSAGE, 0, 2)
//            rv.entries.toList must be not contain(pm)
//        }
//
//        def searchByFrom = {
//            val rv1 = se.searchPrivateMessage(u1.lowerName, u2, PrivateMessageKey.FROM, 0, 2)
//            val rv2 = se.searchPrivateMessage("user2", u2, PrivateMessageKey.FROM, 0, 2)
//
//            (rv1.entries.toList must contain(pm).only) and
//                (rv2.entries.toList must be not contain(pm))
//        }
//
//        def searchByParticipants = {
//            val rv = se.searchPrivateMessage(u2.lowerName, u1, PrivateMessageKey.PARTICIPANTS, 0, 2)
//            rv.entries.toList must contain(pm).only
//        }
//
//        def searchDelete = {
//            PrivateMessage.delete(pm)
//            val rv = se.searchPrivateMessage("kabar", u1, PrivateMessageKey.MESSAGE, 0, 2)
//            rv.entries.toList must be not contain(pm)
//        }
//
//    }

    object appTrees {
        val u1 = genUser
        val app = App.create("app one", "app one cool", "0.1", "http://redir.com/uri", u1)

        def search = {

            Thread.sleep(5000)

            val rv = se.searchApp("one", 0, 10)

            (rv.count must_== 1) and
                (rv.entries.toList must contain(app))

        }

        def delete = {
            se.deleteApp(app)

            Thread.sleep(5000)

            val rv = se.searchApp("one", 0, 10)

            (rv.count must_== 0) and
                (rv.entries.toList must not contain(app))
        }



    }




}
