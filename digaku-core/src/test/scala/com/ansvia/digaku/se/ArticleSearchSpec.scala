/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.se

import com.ansvia.digaku.model.{Article, Forum}
import com.ansvia.digaku.{BeforeAllAfterAllMutable, DigakuTest}
import com.ansvia.perf.PerfTiming
import org.specs2.mutable.Specification

/**
 * Author: robin (<EMAIL>)
 */

class ArticleSearchSpec extends Specification with BeforeAllAfterAllMutable with DigakuTest with SearchEngineTest[BeforeAllAfterAllMutable] {

    "Search engine for Article" should {
        "cannot search privated article by default" in trees.cantSearchFromPrivatedForumByDefault
        "can search all articles includes privated when includePrivate flag set" in trees.canSearchAllIncPrivated
        "can search includes joined forum even forum is privated" in trees.canSearchIncludeJoinedPrivate
        "can't search privated forum by non joined user" in trees.cantSearchPrivatedNonJoinedUser
        "can search non privated forum even searcher is set" in trees.canSearchNonPrivatedWithSearcher
    }


    object trees extends PerfTiming {

        val ch1Name = genForumName
        val ch2Name = genForumName
        val term = genRandomString

        val u1 = genUser
//        u1.increasePoints(15)

        val u2 = genUser
        val u3 = genUser
        val u4 = genUser

        val ch1 = Forum.create(ch1Name, ch1Name + " " + term, u1)
        val ch2 = Forum.create(ch2Name, ch2Name + " " + term, u1)
        val ch3 = Forum.create(ch2Name, ch2Name + " meow", u4)

        ch2.setPrivate(true)
        ch2.addMembers(u3)

        // reindex
        se.indexForum(ch2)

        // add articles

        // ini ada di private forum ch2
        val p1 = Article.create(u3, "article1 " + term, term + " " + genRandomString, "", ch2)

        // ini ada di public forum ch1
        val p2 = Article.create(u1, "article2 " + term, term + " " + genRandomString, "", ch1)

        Thread.sleep(5000)

        def cantSearchFromPrivatedForumByDefault = {
            val rv = timing("cantSearchPrivatedForumByDefault"){
                se.searchArticle(term)
            }

            rv.count must_== 1
            rv.entries.toList must contain(p2).only
        }

        def canSearchAllIncPrivated = {
            val rv = timing("canSearchAllIncPrivated"){
                se.searchArticle(term, includeFromPrivateOrigin=true)
            }

            rv.entries.toList must contain(p1, p2).only
        }

        def canSearchIncludeJoinedPrivate = {
            val rv = timing("canSearchIncludeJoinedPrivate #1"){
                se.searchArticle(term, searcher=Some(u1))
            }

            rv.entries.toList must contain(p1, p2).only
        }

        def cantSearchPrivatedNonJoinedUser {
            val rv = timing("cantSearchPrivatedNonJoinedUser #1"){
                se.searchArticle(term, searcher=Some(u2))
            }

            // harusnya hanya muncul p2, karena p2 berada di non private forum
            rv.entries.toList must contain(p2).only
        }

        def canSearchNonPrivatedWithSearcher {
            // non privated channels and non member should be returned
            se.searchArticle(term, searcher=Some(u2)).entries.toList must contain(p2)
        }


        def canSearchPrivatedMemberedForum = {
            val rv = timing("canSearchPrivatedMemberedForum"){
                se.searchArticle(term, searcher=Some(u3))
            }

            rv.entries.toList must contain(p1, p2).only
        }


    }

}
