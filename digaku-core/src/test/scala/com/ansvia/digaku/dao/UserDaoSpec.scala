/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.model.{SexType, User}
import com.ansvia.digaku.{DigakuTest, model}
import org.specs2.Specification
import org.specs2.specification.Step

class UserDaoSpec extends Specification with DigakuTest {

    def is = {
            sequential ^
            "User dao should" ^
            p ^
//            Step {
//                Digaku.engine.print()
//            } ^
            "saved data read name correctly" ! trees.savedDataCorrectly1 ^
            "saved data read email correctly" ! trees.savedDataCorrectly2 ^
            "saved data read sex type correctly" ! trees.savedDataCorrectly3 ^
            "saved data read id correctly" ! trees.savedDataCorrectly4 ^
            "get user by name correctly" ! trees.getUserByName ^
            "get user by id correctly" ! trees.getUserById ^
            "delete user by id" ! trees.deleteUserById ^
//            "delete user by name" ! trees.deleteUserByName ^
            "delete user by model" ! trees.deleteUserByModel ^
            end
    }

    object trees {

        val user1Name = genUserName
        val user2Name = genUserName
        val user3Name = genUserName

        var user1: model.User = null
        var user2: model.User = null
        var user3: model.User = null
        var id1 = 0L
        var id2 = 0L
        var id3 = 0L

        user1 = User.create(user1Name, user1Name + "@mail.com", SexType.MALE, "01/05/1986", "123")
        id1 = user1.getId
        user2 = User.create(user2Name, user2Name + "@mail.com", SexType.FEMALE, "01/05/1992", "123")
        id2 = user2.getId
        user3 = User.create(user3Name, user3Name + "@mail.com", SexType.MALE, "01/05/1990", "123")
        id3 = user3.getId

        def savedDataCorrectly1 = {
            (user1.name must beEqualTo(user1Name)) and
                (user2.name must beEqualTo(user2Name)) and
                    (user3.name must beEqualTo(user3Name))
        }

        def savedDataCorrectly2 = {
            (user1.emailLogin must beEqualTo(user1Name+"@mail.com")) and
                (user2.emailLogin must beEqualTo(user2Name+"@mail.com")) and
                    (user3.emailLogin must beEqualTo(user3Name+"@mail.com"))
        }

        def savedDataCorrectly3 = {
            (user1.sex must beEqualTo(SexType.MALE)) and
                (user2.sex must beEqualTo(SexType.FEMALE)) and
                    (user3.sex must beEqualTo(SexType.MALE))
        }

        def savedDataCorrectly4 = {
            (user1.getId must beEqualTo(id1)) and
                (user2.getId must beEqualTo(id2)) and
                    (user3.getId must beEqualTo(id3))
        }

        def getUserByName = {
            (User.getByName(user1Name).get must beEqualTo(user1)) and
                (User.getByName(user2Name).get must beEqualTo(user2)) and
                    (User.getByName(user3Name).get must beEqualTo(user3))
        }

        def getUserById = {
            (User.getById(id1).get must beEqualTo(user1)) and
                (User.getById(id2).get must beEqualTo(user2)) and
                    (User.getById(id3).get must beEqualTo(user3))
        }


        def deleteUserById = {
            User.deleteById(id1)
            User.getByName(user1Name).isEmpty must beTrue
        }

//        def deleteUserByName = {
//            User.deleteByName(user2Name)
//            User.getByName(user2Name).isEmpty must beTrue
//        }

        def deleteUserByModel = {
            User.delete(user3)
            User.getByName(user3Name).isEmpty must beTrue
        }

    }


}
