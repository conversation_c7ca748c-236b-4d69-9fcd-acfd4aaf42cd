package com.ansvia.digaku.dao

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.exc.InvalidParameterException
import com.ansvia.digaku.model.Topic
import org.specs2.Specification
import org.specs2.specification.Fragments

/**
 * Author: nadir (<EMAIL>)
 */

class TopicDaoSpec extends Specification with DigakuTest {
    override def is: Fragments = {
        sequential ^
            "topic creation and getter should" ^
            p ^
            "create topic must be instant of topic" ! TopicCreateGetDelete.create ^
            "topic name minimum 2 and maximum 50 character" ! TopicCreateGetDelete.creteWithInvalidName ^
            "get topic by ID" ! TopicCreateGetDelete.getById ^
            "get topic list" ! TopicCreateGetDelete.getTopicList ^
        end
    }

    object TopicCreateGetDelete {
        val topic = Topic.create("Credit Card", "Tentang Credit Card")

        def create = {
            topic must beAnInstanceOf[Topic]
        }

        def creteWithInvalidName = {
            val character51:String = (for (i <- 0 to 50) yield "a").mkString("")

            (Topic.create("a", "") must throwAn[InvalidParameterException]) and
                (Topic.create(character51, "") must throwAn[InvalidParameterException])
        }

        def getById = {
            val topicById = Topic.getById(topic.getId)

            topicById.get mustEqual topic
        }

        def getTopicList = {
            val topics = (for (i <- 1 to 10) yield {
                Topic.create(s"Topic $i", s"Desc $i")
            }).toSeq

            val getList = Topic.getListRight(None, None, 10).toList

            getList.must(containAllOf(topics.reverse).only.inOrder)
        }
    }
}
