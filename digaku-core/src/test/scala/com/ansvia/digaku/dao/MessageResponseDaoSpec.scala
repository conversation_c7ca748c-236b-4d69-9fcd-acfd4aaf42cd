///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.dao
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import org.specs2.specification.Step
//import com.ansvia.digaku.model.MessageResponse
//
///**
// * Author: nadir
// * Date: 4/27/13
// * Time: 3:26 PM
// *
// */
//class MessageResponseDaoSpec extends Specification with DigakuTest {
//
//    def is ={
//        sequential ^
//            "PrivateMessageDao should" ^
//            p ^
//            Step(cleanUp()) ^
//            "create message response" ! trees.create ^
//            "get message response by id" ! trees.getById ^
//            "remove message response non permanent" ! trees.removeMessageResponse ^
//            "deleted message response by id, delete permanent" ! trees.deleteMessageResponseById ^
//            "delete message response, deleted permanent" ! trees.deleteMessageResponse ^
//            Step(tearDown()) ^
//            end
//    }
//
//
//    object trees {
//        val user1 = genUser
//        val user2 = genUser
//        val user3 = genUser
//
//        val msResponse1 = MessageResponse.create(user1, "Response message 1")
//        val msResponse1Id = msResponse1.getId
//        val msResponse2 = MessageResponse.create(user1, "Response message 2")
//        val msResponse2Id = msResponse2.getId
//
//        def create = {
//            (msResponse1 must beAnInstanceOf[MessageResponse]) and
//                (msResponse2 must beAnInstanceOf[MessageResponse])
//        }
//
//        def getById = {
//            (MessageResponse.getById(msResponse1Id).get must beEqualTo(msResponse1)) and
//                (MessageResponse.getById(msResponse2Id).get must beEqualTo(msResponse2))
//        }
//
//        def removeMessageResponse = {
//            MessageResponse.remove(msResponse1)
//            MessageResponse.remove(msResponse2)
//
//            (msResponse1.reload().deleted must beTrue) and
//                (msResponse2.reload().deleted must beTrue)
//        }
//
//        def deleteMessageResponseById = {
//            MessageResponse.deleteById(msResponse1Id)
//
//            MessageResponse.getById(msResponse1Id) must beEmpty
//        }
//
//        def deleteMessageResponse = {
//            MessageResponse.delete(msResponse2)
//            MessageResponse.getById(msResponse2Id) must beEmpty
//        }
//
//    }
//}
