/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao


import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import com.ansvia.digaku.model.{EmailKind, EmailDraft}

/**
 * Author: robin
 *
 */
class EmailDraftSpec extends Specification with DigakuTest {

    def is = "Email draft dao harusnya" ^
        sequential ^
        p ^
        "bisa create new draft" ! trees.create ^
        "bisa get by id" ! trees.getById ^
        "bisa get list" ! trees.getList ^
    end

    object trees {

        import com.ansvia.digaku.Types._

        var id:IDType = _

        EmailDraft.clear()

        def create = {
            val emd = EmailDraft.create("robin","<EMAIL>","hello test", "hello test juga", EmailKind.TEXTILE)
            id = emd.getId
            emd must beAnInstanceOf[EmailDraft]
        }

        def getById = {
            EmailDraft.getById(id) must beAnInstanceOf[Some[EmailDraft]]
        }

        def getList = {
            EmailDraft.getList(0, 10).toList.map(_.senderAddress).head must beEqualTo("<EMAIL>")
        }

    }
}
