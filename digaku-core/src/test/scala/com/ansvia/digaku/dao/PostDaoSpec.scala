/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.exc.PermissionDeniedException
import com.ansvia.digaku.model._
import org.specs2.Specification
import org.specs2.execute.Result
import org.specs2.specification.Step

/**
 * Author: nadir
 *
 */
class PostDaoSpec extends Specification with DigakuTest {

    def is =
        sequential ^
            "Post dao simple creation should" ^
//            p ^
//                "create simple post" ! postSimpleCreation.create ^
//                "create simple post match creator" ! postSimpleCreation.matchCreator ^
//                "create simple post match creator option" ! postSimpleCreation.matchCreatorOption ^
//                "create simple post match origin" ! postSimpleCreation.matchOrigin ^
//                "create simple post match origin name" ! postSimpleCreation.matchOriginName ^
//                "prevent duplicates" ! postSimpleCreation.preventDuplicates ^
//                "simple text creation affect user published content and simple text count" ! postSimpleCreation.affectUserCounter ^
//                "blocked user cannot create simple post" ! postSimpleCreation.cannotPostIsBlocked ^
//            p ^
            "Post dao article creation should" ^
            p ^
            "create article" ! postArticleCreation.create ^
            "create post match creator" ! postArticleCreation.matchCreator ^
            "create post match origin" ! postArticleCreation.matchOrigin ^
            "create post match origin name" ! postArticleCreation.matchOriginName ^
            "article creation affect user published content and article count" ! postArticleCreation.affectUserCounter ^
            "blocked user cannot create article" ! postArticleCreation.cannotPostIsBlocked ^
            p ^
            "Post dao article harusnya" ^
            "bisa dapetin popular article" ! postArticle.getPopular ^
//            "bisa dapetin popular video" ! postVideo.getPopular ^
            p ^
            "add root vertex to post dao should" ^
            p ^
            "exists in article root vertex when content is article" ! rootVertexSpec.inArticleRoov ^
//            "exists in simple post root vertex  when content is simple post" ! rootVertexSpec.inSimplePostRoov ^
//            "exists in question root vertex when content is question" ! rootVertexSpec.inQuestionRoov ^
//            "exists in video root vertex when content contains video link" ! rootVertexSpec.inVideoRoov ^
            p ^
            //        "Post dao question creation should" ^
            //        p ^
            //            "create question" ! postQuestionCreation.create ^
            //            "create post match creator" ! postQuestionCreation.matchCreator ^
            //            "create post match origin" ! postQuestionCreation.matchOrigin ^
            //            "create post match origin name" ! postQuestionCreation.matchOriginName ^
            //        p ^
            //        "Post dao deal creation should" ^
            //        p ^
            //            "create deal" ! postDealCreation.create ^
            //            "create post match creator" ! postDealCreation.matchCreator ^
            //            "create post match origin" ! postDealCreation.matchOrigin ^
            //            "create post match origin name" ! postDealCreation.matchOriginName ^
            //        p ^
            //        "Post getter should" ^
            //        p ^
            //            "get polymorphic post by id simple" ! postGetter.getByIdSimple ^
            //            "get polymorphic post by id article" ! postGetter.getByIdArticle ^
            //            "get polymorphic post by id question" ! postGetter.getByIdQuestion ^
            //           // "get list for post" ! postGetter.getList ^
            //        p ^
            //        "Post deletion should" ^
            //        p ^
            //            "delete simple post by id" ! postDeletion.deleteById ^
            //            "delete post by model" ! postDeletion.deleteBymodel ^
            //            "delete post also delete responses" ! postDeletion.deletePostAlsoDeleteResponses ^
        end



//    object postSimpleCreation {
//
//        val u1Name = genUserName
//        val ch1Name = ChannelNameGenerator.nextName
//
//        val u1 = genUser //User.create(u1Name,"<EMAIL>", SexType.FEMALE, "01/01/1986")
//        val u2 = genUser
//        val ch1 = genChannelWithOwner(u1) //Group.create(ch1Name, "ch1 desc")
//        ch1.addMembers(u2)
//        var p1 = Post.createSimple(u1, "hello :)", ch1)
//
//        def create = {
//            p1 must beAnInstanceOf[SimplePost]
//        }
//        def matchCreator = {
//            p1.creator.name must beEqualTo(u1.name)
//        }
//
//        def matchCreatorOption = {
//            (p1.creatorOption.isDefined must beTrue) and (p1.creatorOption.get must beEqualTo(u1))
//        }
//
//        def matchOrigin = {
//            p1.origin must beAnInstanceOf[Forum]
//        }
//        def matchOriginName = p1.origin.asInstanceOf[Forum].name must beEqualTo(ch1.name)
//
//        def preventDuplicates = {
//            val ch2 = genChannel
//            ch2.addMembers(u1)
//            Post.createSimple(u1, "no dups", ch1)
//            (Post.createSimple(u1, "no dups", ch1) must throwAn[DigakuException]) and
//            (Post.createSimple(u1, "no dups", ch2) must beAnInstanceOf[SimplePost])
//        }
//
//        def affectUserCounter = {
//            (u1.getSimpleTextCount must_== 3L) and
//            (u1.getPublishedContentCount must_== 3L)
//        }
//
//        def cannotPostIsBlocked = {
//            ch1.blockUser(u2, BlockedType.FULL)
//
//            Post.createSimple(u2, "hello 2 ", ch1) must throwAn[PermissionDeniedException]
//        }
//    }

    object postArticleCreation {

        //        val u1Name = User.NameGenerator.nextName
        //        val u2Name = User.NameGenerator.nextName
        //        val ch1Name = Group.NameGenerator.nextName

        def genData() = {
            val u = genUser // User.create(u1Name, u1Name + "@mail.com", SexType.MALE, "01/01/1986")
            val ch = genForum // Group.create(ch1Name, ch1Name + " desc")
//            ch.setAllPermission(ForumPermission.ALL)
            val p = Article.create(u, "title",  "hello :)", "", ch)
            (u, ch, p)
        }

        val (u1, ch1, p1) = genData()

        val u2 = genUser
        val ch2 = genForumWithOwner(u1)

        def create = {
            p1 must beAnInstanceOf[Article]
        }
        def matchCreator = {
            p1.creator.name must beEqualTo(u1.name)
        }
        def matchOrigin = {
            p1.origin must beAnInstanceOf[Forum]
        }
        def matchOriginName = p1.origin.asInstanceOf[Forum].name must beEqualTo(ch1.name)

        def affectUserCounter = {
            // @TODO(robin): benerin ini
            skipped
//            val (u, ch, p) = genData()
//
//            Article.create(u, genRandomString, genRandomString, "", ch)
//            Post.createSimple(u, genRandomString, ch)
//
//            (u.getArticleCount must_== 2L) and
//                (u.getPublishedContentCount must_== 3L)
        }

        def cannotPostIsBlocked = {
            ch2.addMembers(u2)
            ch2.blockUser(u2, BlockedType.FULL)

            Article.create(u2, genRandomString, genRandomString, "", ch2) must throwAn[PermissionDeniedException]

        }


    }

//    object postQuestionCreation {
//
//        //        val u1Name = User.NameGenerator.nextName
//        //        val ch1Name = Group.NameGenerator.nextName
//
//        val u1 = genUser //User.create(u1Name,"<EMAIL>", SexType.MALE, "01/01/1986")
//        u1.reload()
//        val ch1 = genChannel // Group.create(ch1Name, "ch2 desc")
//        u1.reload()
//        ch1.reload()
//        var p1 = Post.createQuestion(u1, "title",  "hello :)", ch1)
//        u1.reload()
//        ch1.reload()
//        p1.reload()
//
//        def create = {
//            p1 must beAnInstanceOf[Question]
//        }
//        def matchCreator = {
//            p1.creator.name must beEqualTo(u1.name)
//        }
//        def matchOrigin = {
//            p1.origin must beAnInstanceOf[Forum]
//        }
//        def matchOriginName = p1.origin.asInstanceOf[Forum].name must beEqualTo(ch1.name)
//    }

//    object postDealCreation {
//
//        val u2 = genUser // User.create(User.NameGenerator.nextName, "<EMAIL>", SexType.MALE, "01/01/1992")
//        u2.reload()
//        val ch20 = genChannel // Group.create(Group.NameGenerator.nextName, "ch20 desc")
//        u2.reload()
//        ch20.reload()
//        var p1 = Deal.create(u2, "name deal", 5.77, "desc", Currency.USD, "locale", "condition", "location", ch20)
//        p1.reload()
//        u2.reload()
//        ch20.reload()
//
//        def create = {
//            p1 must beAnInstanceOf[Deal]
//        }
//        def matchCreator = {
//            p1.creator.name must beEqualTo(u2.name)
//        }
//        def matchOrigin = {
//            p1.origin must beAnInstanceOf[Forum]
//        }
//        def matchOriginName = p1.origin.asInstanceOf[Forum].name must beEqualTo(ch20.name)
//    }

    lazy val postGetter = new {
//
//        val u1 = genUser // User.create(User.NameGenerator.nextName,"<EMAIL>", SexType.MALE, "01/01/1986")
//        u1.reload()
//        val ch1 = genChannel // Group.create(Group.NameGenerator.nextName, "ch3 desc")
//        u1.reload()
//        ch1.reload()
//        val p1 = Post.createSimple(u1, "hello :)", ch1)
//        p1.reload()
//        u1.reload()
//        ch1.reload()
//        val p2 = Article.create(u1, "title",  "hello :)", "", ch1)
//        p1.reload()
//        p2.reload()
//        u1.reload()
//        ch1.reload()
//        val p3 = Post.createQuestion(u1, "title",  "hello :)", ch1)
//        p1.reload()
//        p2.reload()
//        u1.reload()
//        ch1.reload()
//        p3.reload()

        //val posts:Iterable[Post] = Post.getList(0, 10)

        def getByIdSimple = {
            // @TODO(robin): benerin ini
            skipped
//            val p = Post.getPostById(p1.getId)
//            p.get must beAnInstanceOf[SimplePost]
        }
        def getByIdArticle = {
            // @TODO(robin): benerin ini
            skipped
//            val p = Post.getPostById(p2.getId)
//            p.get must beAnInstanceOf[Article]
        }
        def getByIdQuestion = {
            // @TODO(robin): benerin ini
            skipped
//            val p = Post.getPostById(p3.getId)
//            p.get must beAnInstanceOf[Question]
        }
        /*def getList = {
            Post.getList(0, 10).size must beGreaterThan(0)
        }*/
    }

    object postDeletion {
//        val u1 = genUser // User.create(User.NameGenerator.nextName, "<EMAIL>", SexType.MALE, "13/01/1992")
//        val ch1 = genChannel // Group.create(Group.NameGenerator.nextName, "ch4 desc")
//        val p1 = Post.createSimple(u1, "hayyy", ch1)
//        val idP1 = p1.getId
//        val p2 = Article.create(u1, "title", "hayyy ;)", "", ch1)
//        val idP2 = p2.getId
//        val p3 = Post.createSimple(u1, "hayyy 3", ch1)
//        val u2 = genUser
//        val respOfp3 = p3.addResponse(u2, "hello yayyy")
//        val respOfp3Id = respOfp3.getId

        def deleteById = {
            // @TODO(robin): benerin ini
            skipped
//            Post.deletePostById(p1.getId)
//            Post.getPostById(idP1).isEmpty must beTrue
        }

        def deleteBymodel = {
            // @TODO(robin): benerin ini
            skipped
//            Post.deletePost(p2)
//            Post.getPostById(idP2).isEmpty must beTrue
        }

        def deletePostAlsoDeleteResponses = {
            // @TODO(robin): benerin ini
            skipped
//            val respsBeforeCount = p3.getResponseCount
//            Post.deletePost(p3)
//            (respsBeforeCount must beEqualTo(1)) and
//                (Response.getById(respOfp3Id) must beEmpty)
        }
    }

//    object postVideo {
//        val u = genUser
//        val ch = genChannel
//        ch.setAllPermission(SubForumPermission.ALL)
//
//        u.reload()
//        ch.reload()
//
//        val vid1 = Post.createSimple(u, "https://www.youtube.com/watch?v=sfuGhGNW8qE", ch)
//        EmbeddedLink.create("https://www.youtube.com/watch?v=sfuGhGNW8qE", "balawan",
//            LinkKind.VIDEO, "", "desc", Some(vid1))
//        Thread.sleep(5000L)
//        u.reload()
//        ch.reload()
//
//        val vid2 = Post.createSimple(u, "https://www.youtube.com/watch?v=aAb7hSCtvGw", ch)
//        EmbeddedLink.create("https://www.youtube.com/watch?v=aAb7hSCtvGw", "API",
//            LinkKind.VIDEO, "", "desc", Some(vid2))
//        Thread.sleep(1000L)
//        u.reload()
//        ch.reload()
//
//        val vid3 = Post.createSimple(u, "https://www.youtube.com/watch?v=34_xXMXaFcc", ch)
//        EmbeddedLink.create("https://www.youtube.com/watch?v=34_xXMXaFcc", "6String",
//            LinkKind.VIDEO, "", "desc", Some(vid3))
//        Thread.sleep(1000L)
//
//        val vid4 = Post.createSimple(u, "http://www.youtube.com/watch?v=NijTv846Nxo", ch)
//        EmbeddedLink.create("http://www.youtube.com/watch?v=NijTv846Nxo", "API",
//            LinkKind.VIDEO, "", "desc", Some(vid4))
//        Thread.sleep(1000L)
//        u.reload()
//        ch.reload()
//
//        val vid5 = Post.createSimple(u, "http://www.youtube.com/watch?v=8Z0VN7wHJms", ch)
//        EmbeddedLink.create("http://www.youtube.com/watch?v=8Z0VN7wHJms", "API",
//            LinkKind.VIDEO, "", "desc", Some(vid5))
//        Thread.sleep(1000L)
//        u.reload()
//        ch.reload()
//
//        val vid6 = Post.createSimple(u, "http://www.youtube.com/watch?v=O1qIb0asLdo", ch)
//        EmbeddedLink.create("http://www.youtube.com/watch?v=O1qIb0asLdo", "API",
//            LinkKind.VIDEO, "", "desc", Some(vid6))
//        Thread.sleep(1000L)
//        u.reload()
//        ch.reload()
//
//        vid4.setBlocked(true, u)
//        Thread.sleep(1000L)
//
//        vid5.setBlocked(true, u)
//        Thread.sleep(1000L)
//
//        vid6.setBlocked(true, u)
//        Thread.sleep(1000L)
//
//        def getPopular:Result = {
//            // sementara skip dulu karena builtin popular builder tidak ada
//            // telah dipindah ke digaku-shell
//            // @TODO(robin): buat step untuk build popular sebelum testing
//            return skipped
//
//            val u2 = genUser
//            val u3 = genUser
//            val u4 = genUser
//            val u5 = genUser
//
//
//            vid6.reload(); u2.reload()
//            vid6.addResponse(u2, "foo bar")
////            vid6.likesCount = 3
//            vid6.incrementViewsBy(9)
//            vid6.save()
//
//            vid5.reload(); u2.reload()
//            vid5.addResponse(u2, "foo bar")
////            vid5.likesCount = 3
//            vid5.incrementViewsBy(8)
//            vid5.save()
//
//            vid4.reload(); u2.reload()
//            vid4.addResponse(u2, "foo bar")
////            vid4.likesCount = 3
//            vid4.incrementViewsBy(7)
//            vid4.save()
//
//            vid2.reload()
//            u2.reload()
//
//            vid2.addLike(u2)
//            vid2.incrementViewsBy(5)
//            vid2.save()
//            u3.reload()
//            vid2.addLike(u3)
//            u4.reload()
//            vid2.addLike(u4)
//            u5.reload()
//            vid2.addLike(u5)
//
//            vid3.reload(); u2.reload()
//            vid3.addResponse(u2, "foo bar")
////            vid3.likesCount = 3
//            vid3.incrementViewsBy(5)
//            vid3.save()
//
//            vid2.reload(); u2.reload()
//            vid2.addResponse(u2, "foo bar")
//
//            vid1.reload()
////            vid1.likesCount = 1
//            vid1.incrementViewsBy(1)
//            vid1.save()
//
//            val ars = Video.getPopularVideos(0, 10, false).map(_._1).toList
//
//            ars must contain(vid2, vid3, vid1).only.inOrder
//
//        }
//
//    }

    object postArticle {

        val u = genUser
        val ch = genForum
//        ch.setAllPermission(ForumPermission.ALL)

        u.reload()
        ch.reload()

        val ar1 = Article.create(u, "ar1", "ar1 content", "ar1 tags", ch)
        Thread.sleep(5000L)
        u.reload()
        ch.reload()
        val ar2 = Article.create(u, "ar2", "ar2 content", "ar2 tags", ch)
        Thread.sleep(1000L)
        u.reload()
        ch.reload()
        val ar3 = Article.create(u, "ar3", "ar3 content", "ar3 tags", ch)
        Thread.sleep(1000L)
        u.reload()
        ch.reload()
        val ar4 = Article.create(u, "ar4", "ar4 content", "ar4 tags", ch)
        Thread.sleep(1000L)
        u.reload()
        ch.reload()
        val ar5 = Article.create(u, "ar5", "ar5 content", "ar5 tags", ch)
        Thread.sleep(1000L)
        u.reload()
        ch.reload()
        val ar6 = Article.create(u, "ar6", "ar6 content", "ar6 tags", ch)
        Thread.sleep(1000L)

        ar4.setBlocked(true, u)
        Thread.sleep(1000L)

        ar5.setBlocked(true, u)
        Thread.sleep(1000L)

        ar6.setBlocked(true, u)
        Thread.sleep(1000L)

        def getPopular:Result = {
            // sementara skip dulu karena builtin popular builder tidak ada
            // telah dipindah ke digaku-shell
            // @TODO(robin): buat step untuk build popular sebelum testing
            return skipped

            val u2 = genUser
            val u3 = genUser
            val u4 = genUser
            val u5 = genUser

            ar6.reload(); u2.reload()
            ar6.addResponse(u2, "foo bar")
//          ar6.likesCount = 9
            ar6.incrementViewsBy(6)
            ar6.save()

            ar5.reload(); u2.reload()
            ar5.addResponse(u2, "foo bar")
//            ar5.likesCount = 7
            ar5.incrementViewsBy(6)
            ar5.save()

            ar4.reload(); u2.reload()
            ar4.addResponse(u2, "foo bar")
//            ar4.likesCount = 4
            ar4.incrementViewsBy(6)
            ar4.save()

            ar2.reload()
            u2.reload()

            ar2.addLike(u2)
            ar2.incrementViewsBy(5)
            ar2.save()
            u3.reload()
            ar2.addLike(u3)
            u4.reload()
            ar2.addLike(u4)
            u5.reload()
            ar2.addLike(u5)

            ar3.reload(); u2.reload()
            ar3.addResponse(u2, "foo bar")
//            ar3.likesCount = 2
            ar3.incrementViewsBy(5)
            ar3.save()

            ar2.reload(); u2.reload()
            ar2.addResponse(u2, "foo bar")

            ar1.reload()
//            ar1.likesCount = 1
            ar1.incrementViewsBy(1)
            ar1.save()

            val ars = Article.getPopularArticles(0, 10, false).map(_._1).toList

            ars must contain(ar2, ar3, ar1).only.inOrder

        }

    }

    object rootVertexSpec {
        val u1 = genUser
        val ch1 = genForumWithOwner(u1)
//        val sp = Post.createSimple(u1, "hello", ch1)
//        val vid = Post.createSimple(u1, "hello http://www.youtube.com/watch?v=2XkoKQoCNgg", ch1)
        val arti = Article.create(u1, "ini article", "article content", "tags", ch1)
//        val question = Post.createQuestion(u1, "question", "content of question", ch1)

        def inArticleRoov = {
            val rv = Article.getListRight(None, None, 10).toList
            (rv must contain(arti))// and
//                (rv must be not contain(sp)) and
//                (rv must be not contain(question))
        }

//        def inSimplePostRoov = {
//            val rv = SimplePost.getListRight(None, None, 10).toList
//            (rv must contain(sp)) and
//                (rv must be not contain(arti)) and
//                (rv must be not contain(question))
//        }
//
//        def inQuestionRoov = {
//            val rv = Question.getListRight(None, None, 10).toList
//            (rv must contain(question)) and
//                (rv must be not contain(arti)) and
//                (rv must be not contain(sp))
//        }
//
//        def inVideoRoov = {
//            val rv = Video.getListRight(None, None, 10).toList
//            (rv must contain(vid)) and
//                (rv must be not contain(arti)) and
//                (rv must be not contain(sp)) and
//                (rv must be not contain(question))
//        }



    }

}
