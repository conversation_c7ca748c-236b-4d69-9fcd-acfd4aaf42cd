/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model.{Polling, PollingChoice}
import org.specs2.Specification

/**
* Author: robin
*
*/
class PollingDaoSpec extends Specification with DigakuTest {

    def is =
        sequential ^
        "PollingDao should" ^
        p ^
            "create polling" ! trees.create ^
            "get polling by id" ! trees.getById ^
            "get list" ! trees.getList ^
            "delete polling by id" ! trees.deleteById ^
            "delete polling by object" ! trees.deleteByObj ^
        end

    object trees {

        val poll1 = Polling.create("pilihlah nomor 1!", "abc")
        val poll2 = Polling.create("pilihlah nomor 2!", "def")

        val poll1get = Polling.getById(poll1.getId)
        val poll2get = Polling.getById(poll2.getId)

        def create = {

            // create with choices
            val choices = Seq(PollingChoice("mario", "http://2.images.southparkstudios.com/default/image.jpg?quality=0.8"),
                PollingChoice("luigi"))
            val poll3 = Polling.create("multiple choices, please choose", "ghi", choices)

            (poll1 must beAnInstanceOf[Polling]) and
                (poll1.title must beEqualTo("pilihlah nomor 1!")) and
                (poll1.desc must beEqualTo("abc")) and
                (poll2.desc must beEqualTo("def")) and
                (Polling.exists(poll1.getId) must beTrue) and
                (poll3 must beAnInstanceOf[Polling]) and
                (poll3.getChoices must containAllOf(choices)) and
                (poll3.desc must_== "ghi")
        }

        def getById = {
            (poll1get.get must beAnInstanceOf[Polling]) and
                (poll1get.get.title must beEqualTo("pilihlah nomor 1!"))
        }

        def getList = {
            Polling.getListRight(None, None, 10).toList.map(_.title) must be contain("pilihlah nomor 1!", "pilihlah nomor 2!")
        }

        def deleteById = {
            Polling.deleteById(poll1get.get.getId)
            Polling.exists(poll1get.get.getId) must beFalse
        }

        def deleteByObj = {
            Polling.delete(poll2get.get)
            Polling.exists(poll2get.get.getId) must beFalse
        }

    }

}
