///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.dao
//
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.model.{Currency, Deal, Locale}
//import com.ansvia.graph.BlueprintsWrapper._
//import org.specs2.Specification
//import org.specs2.specification.Step
//
///**
// * Author: robin
// *
// */
//class DealDaoSpec extends Specification with DigakuTest {
//
//    def is =
//        "Deal Dao harusnya" ^
//        sequential ^
//        p ^
//        "get popular deal harus 0 apabila belum ada response dan likes" ! trees.getPopularZeroWhenNoRespNLikes ^
//        "bisa get popular deal" ! trees.getPopular ^
//        Step { tearDown() } ^
//        end
//
//    object trees {
//
//        val u = genUser
//        val ch = genChannelWithOwner(u)
//
////        ch.setOwner(u)
//
//        val deal = Deal.create(u, "a", 500, "barang a" * 160, Currency.IDR, Locale.id_ID, "new", "Jogja", ch)
//
//        def getPopularZeroWhenNoRespNLikes = {
//            val deals = Deal.getPopular(0, 2)
//            deals.toList must be contain((deal, 0.0))
//        }
//
//        def getPopular = {
////            transact {
//                val v = deal.getVertex
////                v.set("responseCount", 2)
//                v.set("likesCount", 2)
////            }
//            db.commit()
//            Deal.clearCache()
//            val deals = Deal.getPopular(0, 3)
//            deals.toList.map( x => (x._1, if (x._2 > 0) 1 else 0) ) must be contain((deal, 1))
//        }
//    }
//}
