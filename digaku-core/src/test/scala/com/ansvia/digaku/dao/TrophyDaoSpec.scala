/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.model.{Trophy, TrophyGroup}
import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import org.specs2.specification.Fragments

/**
 * Author: nadir, robin
 * 
 */
class TrophyDaoSpec extends Specification with DigakuTest {
    def is: Fragments = {
        "Trophy dao should" ^
            p ^
            sequential^
            "create trophy correctly" ! trees.create ^
            "get by name correctly" ! trees.getByName ^
            "exist by name correctly" ! trees.isExistByName ^
            end
    }


    object trees {

        val trophy1 = Trophy.createGeneral("trophy name", "desc trophy","http:digaku.com/icon_sprite_trophy.png", TrophyGroup.ARTICLE)
        val user1 = genUser

        def create = {
            trophy1.name must beEqualTo("trophy name")
        }

        def getByName = {
            Trophy.getByName("trophy name").get must beEqualTo(trophy1)
        }

        def isExistByName = {
            Trophy.isExistByName("trophy name") must beTrue
        }

    }

}
