package com.ansvia.digaku.dao

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model.{FAQ, Topic}
import org.specs2.Specification
import org.specs2.specification.Fragments

/**
 * Author: nadir (<EMAIL>)
 */

class FAQDaoSpec extends Specification with DigakuTest {
    override def is: Fragments = {
        sequential ^
            "faq creation and getter should" ^
            p ^
            "create FAQ must be instant of faq" ! FAQCreationAndGetter.create ^
            "get creator of FAQ" ! FAQCreationAndGetter.getCreator ^
            "get topic of FAQ" ! FAQCreationAndGetter.getTopic ^
            "get FAQ List from Topic" ! FAQCreationAndGetter.getFaqList ^
            "Delete FAQ" ! FAQCreationAndGetter.deleteFaq ^
            end
    }

    object FAQCreationAndGetter {

        val user = genUser

        val topic = Topic.create("Credit Card", "Tentang Credit Card")

        val faq = FAQ.create("Credit Card Hilang", "Apa saja persyaratan untuk mengganti", user, topic)

        def create = {
            faq must beAnInstanceOf[FAQ]
        }

        def getCreator = {
            faq.creator.get mustEqual user
        }

        def getTopic = {
            faq.topic.get mustEqual topic
        }

        def getFaqList = {
            val topic2 = Topic.create("Credit Card", "Tentang Credit Card")

            val faqs = (for (i <- 1 to 10) yield {
                Thread.sleep(1000)
                FAQ.create(s"FAQ Title $i", s"Faq Content $i", user, topic2)
            }).toSeq

            topic2.getFaqs(0, 100).toList.must(containAllOf(faqs.reverse).only.inOrder) and
                (topic2.getFaqCount must_== 10)
        }

        def deleteFaq = {
            val faq2 = FAQ.create(s"Membat Credit Card", "Apa Persyaratan membuat credit card", user, topic)

            val faqCount = topic.getFaqCount

            FAQ.delete(faq2)

            val faqAfterDelete = topic.getFaqCount

            (faqCount must_== 2) and (faqAfterDelete must_== 1)
        }
    }

}
