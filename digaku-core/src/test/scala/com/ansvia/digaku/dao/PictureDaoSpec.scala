///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.dao
//
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.model._
//import org.specs2.Specification
//import org.specs2.execute.Result
//import org.specs2.specification.Step
//
///**
// * Author: nadir
// *
// */
//class PictureDaoSpec extends Specification with DigakuTest {
//
////    Config.searchEngineProvider = "embedded-elastic-search"
////    Config.esIndexDir = "/tmp/digaku-es-index-test"
//
//    def is = {
//        sequential ^
//            Step {
//                cleanUp()
//            } ^
//            "picture dao creation should" ^
//            p ^
//                "create picture" ! pictureCreation.create ^
//                "creation picture match origin" ! pictureCreation.matchOrigin ^
//                "creation picture match origin name" ! pictureCreation.matchOriginName ^
//                "get picture by id picture" ! pictureCreation.getByIdPicture ^
//                "get list for picture" ! pictureCreation.getListPicture ^
//                "delete picture by picture id" ! pictureCreation.deleteByIdPicture ^
//                "delete picture by model" ! pictureCreation.deleteByModel ^
//                "bisa dapat picture jenis apapun yang diturunkan dari PictureBase " +
//                    "menggunakan getPictureFromBaseById" ! trees.getPoly ^
//            p ^
//            "post picture should" ^
//                "get popular picture" ! postPicture.popularPicture ^
//            Step {
//                tearDown()
//            } ^
//            end
//    }
//
//    object pictureCreation {
////        val ch1Name = ChannelNameGenerator.nextName
//
//
//        val u1 = genUser
//        val ch1 = genForumWithOwner(u1)
//        ch1.setWhoCanCreatePictures(SubForumPermission.ALL)
//        val pic1 = Picture.create(u1, "picture title " + genRandomString, ch1)
//        val pic1Id = pic1.getId
//        val pic2 = Picture.create(u1, "picture2 title", ch1)
//
//        def create = {
//            pic1 must beAnInstanceOf[Picture]
//        }
//        def matchOrigin = {
//            pic1.origin must beAnInstanceOf[Forum]
//        }
//        def matchOriginName = {
//            pic1.origin.asInstanceOf[Forum].name must beEqualTo(ch1.name)
//        }
//        def getByIdPicture = {
//            Picture.getById(pic1Id).get must beEqualTo(pic1)
//        }
//        def getListPicture = {
//            Picture.getList(0, 10).toList.length>0 must beTrue
//        }
//        def deleteByIdPicture = {
//            Picture.deleteById(pic1Id)
//            Picture.getList(0, 10).toList must not contain(pic1)
//        }
//        def deleteByModel = {
//            Picture.delete(pic2)
//            Picture.getList(0, 10).toList must be not contain(pic2)
//        }
//    }
//
//    object trees {
//
//        val u1 = genUser
//        val ch1 = genForumWithOwner(u1)
//
//        // standard pic
//        val pic1 = Picture.create(u1, "picture title", ch1)
//        val pic2 = FreePicture.create(genRandomString, "", u1)
////        val pic3 = PictureGroup.create(genRandomString, u1, ch1)
////        val nonPic = Post.createSimple(u1, genRandomString, ch1)
//
//        def getPoly = {
//
//            (Picture.getPictureFromBaseById(pic1.getId) must_== Some(pic1)) and
//            (Picture.getPictureFromBaseById(pic2.getId) must_== Some(pic2)) //and
////            (Picture.getPictureFromBaseById(pic3.getId) must_== Some(pic3)) and
////            (Picture.getPictureFromBaseById(nonPic.getId) must_== None)
//        }
//
//    }
//
//    object postPicture {
//        val u = genUser
//        val ch = genForumWithOwner(u)
//
//        u.reload()
//        ch.reload()
//
//        val pic1 =  Picture.create(u, "picture 1", ch)
//        Thread.sleep(5000L)
//        u.reload()
//        ch.reload()
//
//        val pic2 =  Picture.create(u, "picture 2", ch)
//        Thread.sleep(3000L)
//        u.reload()
//        ch.reload()
//
//        val pic3 =  Picture.create(u, "picture 3", ch)
//        Thread.sleep(1000L)
//        u.reload()
//        ch.reload()
//
//        val pic4 =  Picture.create(u, "picture 4", ch)
//        Thread.sleep(1000L)
//        u.reload()
//        ch.reload()
//
//        val pic5 =  Picture.create(u, "picture 5", ch)
//        Thread.sleep(1000L)
//        u.reload()
//        ch.reload()
//
//        val pic6 =  Picture.create(u, "picture 6", ch)
//        Thread.sleep(1000L)
//        u.reload()
//        ch.reload()
//
//        pic4.setBlocked(true, u)
//        Thread.sleep(1000L)
//
//        pic5.setBlocked(true, u)
//        Thread.sleep(1000L)
//
//        pic6.setBlocked(true, u)
//        Thread.sleep(1000L)
//
//        def popularPicture:Result = {
//
//            // sementara skip dulu karena builtin popular builder tidak ada
//            // telah dipindah ke digaku-shell
//            // @TODO(robin): buat step untuk build popular sebelum testing
//            return skipped
//
//            val u2 = genUser
//            val u3 = genUser
//            val u4 = genUser
//            val u5 = genUser
//
//            ch.reload()
//            ch.addMembers(u2, u3, u4, u5)
//
//            pic6.reload(); u2.reload()
//            pic6.addResponse(u2, "foo bar")
//            pic6.likesCount = 9
////            pic6.viewsCount = 6
//            pic6.incrementViewsBy(6)
//            pic6.save()
//
//            pic5.reload(); u2.reload()
//            pic5.addResponse(u2, "foo bar")
//            pic5.likesCount = 7
////            pic5.viewsCount = 6
//            pic5.incrementViewsBy(6)
//            pic5.save()
//
//            pic4.reload(); u2.reload()
//            pic4.addResponse(u2, "foo bar")
//            pic4.likesCount = 4
////            pic4.viewsCount = 6
//            pic4.incrementViewsBy(6)
//            pic4.save()
//
//            pic2.reload()
//            u2.reload()
//
//            pic2.addLike(u2)
////            pic2.viewsCount = 5
//            pic2.incrementViewsBy(5)
//            pic2.save()
//            u3.reload()
//            pic2.addLike(u3)
//            u4.reload()
//            pic2.addLike(u4)
//            u5.reload()
//            pic2.addLike(u5)
//
//            pic3.reload(); u2.reload()
//            pic3.addResponse(u2, "foo bar")
//            pic3.likesCount = 2
////            pic3.viewsCount = 5
//            pic3.incrementViewsBy(5)
//            pic3.save()
//
//            pic2.reload(); u2.reload()
//            pic2.addResponse(u2, "foo bar")
//
//            pic1.reload()
//            pic1.likesCount = 1
////            pic1.viewsCount = 1
//            pic1.incrementViews()
//            pic1.save()
//
//            db.commit()
//
//            val ars = Picture.getPopularPictures(0, 10, false).map(_._1).toList
//
//            ars must contain(pic2, pic3, pic1).only.inOrder
//        }
//
//    }
//
//}
