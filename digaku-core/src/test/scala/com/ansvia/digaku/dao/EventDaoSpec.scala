/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.DigakuTest
import org.specs2.Specification
import com.ansvia.digaku.model._
import org.specs2.specification.Step
import java.util.{Date, Calendar}

/**
 * Author: nadir
 *
 */
class EventDaoSpec extends Specification with DigakuTest {
    def is =
        sequential ^
            "event dao creation should" ^
            p ^
                "create event" ! eventCreation.create ^
                "create event match origin" ! eventCreation.matchOrigin ^
                "create event match origin name" ! eventCreation.matchOriginName ^
                "add Attend event" ! eventCreation.addAttend ^
            p ^
            "event get and deletion should" ^
            p ^
                "get list Event" ! eventGetAndDeletion.getListEvent ^
                "get event by id" ! eventGetAndDeletion.getEventById ^
                "close event by model" ! eventGetAndDeletion.closeEvent ^
                "open event by model" ! eventGetAndDeletion.openEvent ^
                "delete even by id" ! eventGetAndDeletion.deleteEventById ^
                "delete event by model" ! eventGetAndDeletion.deleteEventByModel ^
            end

    private def dueDate = {
        val cal = Calendar.getInstance()
        cal.setTime(new Date())
        cal.add(Calendar.DATE, 1)
        cal.getTime
    }

    private def finishDate = {
        val cal = Calendar.getInstance()
        cal.setTime(new Date())
        cal.add(Calendar.DATE, 2)
        cal.getTime
    }

    object eventCreation {
//        val u1Name = User.NameGenerator.nextName
//        val ch1Name = Group.NameGenerator.nextName

        val u1 = genUser // User.create(u1Name, "<EMAIL>", SexType.FEMALE, "12/12/1990")
        val ch1 = genForum // Group.create(ch1Name, "ch1 desc")
//        ch1.setAllPermission(ForumPermission.ALL)
        val e1 = Event.create(u1, "title Event", "content Event", "Wonosobo", ch1, dueDate, finishDate)


        def create = {
            e1 must beAnInstanceOf[Event]
        }
        def matchOrigin = {
            e1.origin must beAnInstanceOf[Forum]
        }
        def matchOriginName = e1.origin.asInstanceOf[Forum].name must beEqualTo(ch1.name)

        def addAttend = {
            e1.addAttenders(AttenderKind.POSITIVE, u1)
            e1.getAttenders(AttenderKind.POSITIVE).length > 0 must beTrue
        }
    }

    object eventGetAndDeletion {

        val u2 = genUser
        val ch2 = genForumWithOwner(u2)
//        ch2.setAllPermission(ForumPermission.ALL)
        val e2 = Event.create(u2, "title Event2", "content Event2", "Jakarta", ch2, dueDate, finishDate)
        val e2Id = e2.getId
        val e3 = Event.create(u2, "title Event3", "content Event3", "Jakarta", ch2, dueDate, finishDate)
        val e3Id = e3.getId

        def getListEvent = {
            Event.getListRight(None, None, 10).size must beGreaterThan(0)
        }

        def getEventById = {
            Event.getById(e2Id).get.title must beEqualTo("title Event2")
        }

        def closeEvent = {
            Event.close(e3)
            e3.reload().closed must beTrue
        }

        def openEvent = {
            Event.open(e3)
            e3.reload().closed must beFalse
        }

        def deleteEventById = {
            Event.deleteById(e2Id)
            Event.getById(e2Id) must beEmpty
        }

        def deleteEventByModel = {
            Event.delete(e3)
            Event.getById(e3Id) must beEmpty
        }



    }

}
