///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.dao
//
//import org.specs2.specification.Fragments
//import com.ansvia.digaku.model.TwitterInfo
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//
///**
// * Author: nadir
// * Date: 11/27/13
// * Time: 3:35 PM
// *
// */
//class TwitterInfoDaoSpec extends Specification with DigakuTest {
//
//    def is: Fragments = {
//        "Twitter connect id dao should" ^
//            p ^
//            sequential^
//            "create twitter info" ! trees.createTwitterInfo ^
//            "get TwitterInfo by FbId correctly" ! trees.getByTwId ^
//            "exist TwitterInfo by FbId correctly" ! trees.existByTwId ^
//            end
//    }
//
//    object trees {
//
//        val tci1 = TwitterInfo.create("222")
//
//        def createTwitterInfo = {
//
//            tci1.twId must beEqualTo("222")
//
//        }
//
//        def getByTwId = {
//            TwitterInfo.getByTwid("222").get must beEqualTo(tci1)
//        }
//
//        def existByTwId = {
//            TwitterInfo.existsByTwId("222") must beTrue
//        }
//
//    }
//
//}
