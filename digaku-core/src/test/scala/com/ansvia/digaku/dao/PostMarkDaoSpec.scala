/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import org.specs2.Specification
import com.ansvia.digaku.DigakuTest
import org.specs2.specification.Step
import com.ansvia.digaku.model.PostMark

/**
 * Author: nadir
 * Date: 6/28/13
 * Time: 9:59 AM
 * 
 */
class PostMarkDaoSpec extends Specification with DigakuTest {

    def is = {
        sequential ^
        "post mark dao creation should" ^
        p ^
        "create post mark" ! postMarkCreation.create ^
        "getting post mark by title" ! postMarkCreation.getPostMarkByTitle ^
        "get list post mark" ! postMarkCreation.getListForPostMark ^
        "delete post mark" ! postMarkCreation.deletePostMark ^
        Step(postMarkCreation.cleanUp()) ^
        end
    }

    object postMarkCreation {

        cleanUp()

        val postmark1 = PostMark.create("solved", "#cacaca")
        val postmark2 = PostMark.create("todo", "#cacaff")
        val postmark3 = PostMark.create("feature", "#22caff")

        println("getList: " + PostMark.getList(0, 10).toList)


        def cleanUp(){
            PostMark.getDaoListIndex.reset()
        }


        def create = {
            postmark1 must beAnInstanceOf[PostMark]
        }

        def getPostMarkByTitle = {
            PostMark.getByPostMarkTitle("solved").get must beEqualTo(postmark1)
        }

        def getListForPostMark = {
            PostMark.getList(0, 10).toList must be contain(postmark1, postmark2, postmark3)
        }

        def deletePostMark = {

            val pms = PostMark.getList(0, 10).toList

            pms.foreach { postMark =>
                PostMark.delete(postMark)
            }

            PostMark.getList(0, 10).toList must not contain(pms.head)
        }

    }


}
