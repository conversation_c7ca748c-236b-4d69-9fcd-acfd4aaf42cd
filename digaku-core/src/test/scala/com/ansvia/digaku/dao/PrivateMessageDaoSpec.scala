///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.dao
//
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.model.PrivateMessage
//import com.ansvia.digaku.exc.{PermissionDeniedException, LimitationReachedException}
//import com.ansvia.digaku.stats.model.DigakuStats
//
///**
// * Author: nadir
// *
// */
//class PrivateMessageDaoSpec extends Specification with DigakuTest {
//
//    def is ={
//        sequential ^
//            "PrivateMessageDao should" ^
//            p ^
////            Step(cleanUp()) ^
//            "create private message" ! trees.create ^
//            "bisa kirim pm ke semua user (tanpa harus saling support)" ! trees.sendPmToNotSupportingNotSupportedUser ^
////            "hanya bisa kirim pm ke supported user" ! trees.sendPmToNotSupportingUser ^
//            "create private message affect count stats" ! trees.createAffectCountStats ^
//            "get private message by Id" ! trees.getById ^
//            "remove non permanent private message" ! trees.removePrivateMessage ^
//            "delete private message by id deleted permanent" ! trees.deletePrivateMessageById ^
//            "delete private message, deleted permanent" ! trees.deletePrivateMessage ^
//            "user gak bisa nulis duplicate pm" ! trees.cantDuplicate ^
//            "user gak bisa nulis duplicate pm tapi user lain bisa" ! trees.cantDuplicate2 ^
////            Step(tearDown()) ^
//        end
//    }
//
//    object trees {
//        val user1 = genUser
//        val user2 = genUser
//        val user3 = genUser
//
//        user1.support(user2)
//        user2.support(user1)
//        user1.support(user3)
//        user3.support(user1)
//
//        user2.support(user3)
//        user3.support(user2)
//
//        val subject1 = genRandomString
//        val subject2 = genRandomString
//
//        val privateMessage1 = PrivateMessage.create(user1, subject1, "send private message to user2, user3", Array(user2, user3))
//        val pM1Id = privateMessage1.getId
//        val privateMessage2 = PrivateMessage.create(user2, subject2, "send private message to user2, user3", Array(user3, user1))
//        val pM2Id = privateMessage2.getId
//
//        def create = {
//            (privateMessage1 must beAnInstanceOf[PrivateMessage]) and
//                (privateMessage2 must beAnInstanceOf[PrivateMessage]) and
//                (privateMessage1.subject must beEqualTo(subject1)) and
//                (privateMessage2.subject must beEqualTo(subject2))
//        }
//
//        def createAffectCountStats = {
////            Stats.getCountStats.privateMessageCount must greaterThan(0L)
//            DigakuStats.counter.get("private_message") must greaterThan(0L)
//        }
//
//
//        def getById = {
//            (PrivateMessage.getById(pM1Id).get must beEqualTo(privateMessage1)) and
//                (PrivateMessage.getById(pM2Id).get must beEqualTo(privateMessage2))
//        }
//
//        def removePrivateMessage = {
//            PrivateMessage.remove(privateMessage1)
//            PrivateMessage.remove(privateMessage2)
//
//            (privateMessage1.reload().deleted must beTrue) and
//                (privateMessage2.reload().deleted must beTrue)
//        }
//
////        def sendPmToNotSupportingUser = {
////            val user4 = genUser
////            val user5 = genUser
////            val user6 = genUser
////
////            user1.support(user4)
////            user1.support(user5)
////
////            val subject = genRandomString
////
////            /**
////             * karena user6 tidak disupport user1 maka PermissionDeniedException
////             * kondisi yang benar : user
////             */
////            PrivateMessage.create(user1, subject,"hello message one",Array(user4, user5, user6)) must throwA[PermissionDeniedException]
////
////        }
//
//        def sendPmToNotSupportingNotSupportedUser = {
//            val user4 = genUser
//            val user5 = genUser
//            val user6 = genUser
//
//            val subject = genRandomString
//
//            val privateMessage1 = PrivateMessage.create(user1, subject,"hello message one",Array(user4, user5, user6))
//
//            /**
//             * Walaupun tidak saling support harus bisa create whisper
//             */
//            (privateMessage1.subject must beEqualTo(subject1)) and
//                    (privateMessage1 must beAnInstanceOf[PrivateMessage])
//                    (privateMessage1.creator must beEqualTo(user1))
//
//        }
//
//
//        def deletePrivateMessageById = {
//            PrivateMessage.deleteById(pM1Id)
//            PrivateMessage.getById(pM1Id) must beEmpty
//        }
//
//        def deletePrivateMessage = {
//            PrivateMessage.delete(privateMessage2)
//            PrivateMessage.getById(pM2Id) must beEmpty
//        }
//
//        def cantDuplicate = {
//            val subject = genRandomString
//            PrivateMessage.create(user1, subject,"hello message one",Array(user2))
//            PrivateMessage.create(user1, subject,"hello message one",Array(user2)) must throwA[LimitationReachedException]
//        }
//
//        def cantDuplicate2 = {
//            val subject = genRandomString
//            PrivateMessage.create(user2, subject, "hello message one",Array(user1))
//            PrivateMessage.create(user3, subject, "hello message one",Array(user2)) must be not throwA[LimitationReachedException]
//        }
//
//
//    }
//
//}
