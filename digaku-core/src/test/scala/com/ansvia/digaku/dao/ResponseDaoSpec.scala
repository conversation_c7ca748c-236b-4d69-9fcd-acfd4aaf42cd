/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model.Response
import com.ansvia.util.idgen.PrefixedIncrementalNumIdGenerator
import org.specs2.Specification

/**
 * Author: nadir
 *
 */
class ResponseDaoSpec extends Specification with DigakuTest {
    def is = {
//        Step(cleanUp()) ^
        sequential ^
        "response creation getter and deletion should" ^
        p ^
            "create response must be instant of Response" ! responseCreateGetDelete.create ^
            "create response expected data content" ! responseCreateGetDelete.matchContent ^
            "delete response by id" ! responseCreateGetDelete.deleteResponseById ^
            "delete response by model" ! responseCreateGetDelete.deleteResponeByModel ^
//            Step(tearDown()) ^
            end
    }

    object responseCreateGetDelete extends PrefixedIncrementalNumIdGenerator(1L, "test") {

        val user = genUser
        val res = Response.create(user, "helloo")
        val resId = res.getId
        val res2 = Response.create(user, "hayy")
        val res2Id = res2.getId
        val res3 = Response.getById(resId).get

        def create = {
            res must beAnInstanceOf[Response]
        }
        def matchContent = {
            res3.content must beEqualTo("helloo")
        }
        def deleteResponseById = {
            Response.deleteById(resId)
            Response.getById(resId).isEmpty must beTrue
        }
        def deleteResponeByModel = {
            Response.delete(res2)
            Response.getById(res2Id).isEmpty must beTrue
        }
    }
}
