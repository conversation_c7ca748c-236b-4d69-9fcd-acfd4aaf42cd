/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.DigakuTest
import com.ansvia.digaku.model.App
import org.specs2.mutable.Specification

/**
* Author: nadir, robin
*
*/
class AppDaoSpec extends Specification with DigakuTest {

    sequential
//    cleanUp()

    "App data access object" should{
        var app: App = null
        var app2: App = null
        var app3: App = null
        var app4: App = null
        var app5: App = null
        var id:Long = 0L

//        object appNameGen extends PrefixedIncrementalNumIdGenerator(1L, "app")

        val app1Name = genRandomString

        val u1 = genUser

        step {
            app = App.create(app1Name, "test test", "1", "", u1, true)
            app2 = App.getByName(app1Name).getOrElse(null)
            id = app2.getId
            app3 = App.getById(id).getOrElse(null)

        }

        "app creation not return null" in{
            app must not equalTo(null)
        }

        "app creator return excepted data" in{
            app.name must beEqualTo(app1Name)
        }

        "get app by name" in {
           app2.name must beEqualTo(app1Name)
        }

        "get app by id" in {
           app3.getId must beEqualTo(id)
        }

        "list app" in {
            App.getListRight(None, None, 10).size must beGreaterThan(0)
        }

        "get by dev access token" in {
            println("App.getByDevAccessToken(app.devAccessToken): " + App.getByDevAccessToken(app.devAccessToken))
            App.getByDevAccessToken(app.devAccessToken) must_== Some(app)
        }

        val app2Name = genRandomString
        val app3Name = genRandomString

        step {
            App.create(app2Name, "test delete by Id", "1", "", u1, true)
            App.create(app3Name, "test delete by model", "1", "", u1, true)
            app4 = App.getByName(app2Name).get
            app5 = App.getByName(app3Name).get
        }

        "delete app by id" in {
            App.deleteById(app4.getId)
            App.getByName(app2Name).isEmpty must beTrue
        }

        "delete app by model" in {
            App.delete(app5)
            App.getByName(app3Name).isEmpty must beTrue
        }

    }

}
