///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.dao
//
//import org.specs2.mutable.Specification
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.model.{UserRole, Grouped, Forum}
//import com.ansvia.digaku.model.Grouped.GroupKind
//
///**
// * Author: fajrhf
// * Date: 12/09/14
// * Time: 15:59
// *
// */
//class GroupedDaoSpec extends Specification with DigakuTest {
//
//    sequential
//
//    "Grouped data access object" should {
//        var group1:Grouped = null
//        var group2:Grouped = null
//        var group3:Grouped = null
//        var group4:Grouped = null
//
//        val group1Name = genRandomString.toLowerCase
//
//        val u1 = genUser
//
//        u1.setRole(UserRole.SUPER_ADMIN)
//
//        var id:Long = 0L
//
//        val ch1 = genChannelWithOwner(u1)
//
//        step {
//            group1 = Grouped.create(u1, ch1, GroupKind.SUB_FORUM, group1Name, "")
//
//            id = group1.getId
//            group2 = Grouped.getById(id).getOrElse(null)
//        }
//
//        "group creation not return null" in {
//            group1 must not equalTo(null)
//        }
//
//        "group creation return expected data" in {
//            group1.name must beEqualTo(group1Name)
//        }
//
//        "get group by id" in {
//            group2.getId must beEqualTo(id)
//        }
//
//        "get app by name" in {
//            group2.name must beEqualTo(group1Name)
//        }
//
//        "check if group is exist" in {
//            Grouped.isExists(group1.getId, GroupKind.SUB_FORUM) must beEqualTo(false)
//        }
//
//        val group3Name = genRandomString.toLowerCase
//        val group4Name = genRandomString.toLowerCase
//
//        val ch2 = genChannelWithOwner(u1)
//        val ch3 = genChannelWithOwner(u1)
//
//        step {
//            Grouped.create(u1, ch2, GroupKind.SUB_FORUM, group3Name, "")
//            Grouped.create(u1, ch3, GroupKind.SUB_FORUM, group4Name, "")
//
//            group3 = Grouped.getByGroupName(group3Name, GroupKind.SUB_FORUM).get
//            group4 = Grouped.getByGroupName(group4Name, GroupKind.SUB_FORUM).get
//        }
//
//        "delete group by id" in {
//            Grouped.deleteById(group3.getId)
//            Grouped.getByGroupName(group3Name, GroupKind.SUB_FORUM).isEmpty must beTrue
//        }
//
//        "delete group by model" in {
//            Grouped.delete(group4)
//            Grouped.getByGroupName(group4Name, GroupKind.SUB_FORUM).isEmpty must beTrue
//        }
//
//        val group5Name = genRandomString.toLowerCase
//
//        var group5:Grouped = null
//        var group6:Grouped = null
//
//        val ch4 = genChannelWithOwner(u1)
//        val ch5 = genChannelWithOwner(u1)
//
//        step {
//            group5 = Grouped.create(u1, ch4, GroupKind.SUB_FORUM, group5Name, "")
//            group6 = Grouped.create(u1, ch5, GroupKind.SUB_FORUM, group5Name, "")
//        }
//
//        "get group by object" in {
//            Grouped.getGroupObjects[Forum](group5Name.trim, 0, 2, GroupKind.SUB_FORUM).toList must contain(ch4)
//        }
//    }
//
//}
