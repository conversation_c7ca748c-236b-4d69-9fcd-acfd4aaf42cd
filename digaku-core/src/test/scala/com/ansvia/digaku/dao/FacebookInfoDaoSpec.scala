///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.dao
//
//import org.specs2.specification.Fragments
//import com.ansvia.digaku.model.FacebookInfo
//import org.specs2.Specification
//import com.ansvia.digaku.DigakuTest
//
///**
// * Author: nadir
// * Date: 11/26/13
// * Time: 4:03 PM
// *
// */
//class FacebookInfoDaoSpec extends Specification with DigakuTest {
//    def is: Fragments = {
//        "Facebook connect id should" ^
//            p ^
//            sequential^
//            "create facebook info" ! trees.createFacebookInfo ^
//            "get FacebookInfo by FbId correctly" ! trees.getByFbId ^
//            "exist FacebookInfo by FbId correctly" ! trees.existByFbId ^
//        end
//    }
//
//    object trees {
//
//        val fci1 = FacebookInfo.create("222")
//
//        def createFacebookInfo = {
//
//            fci1.fbId must beEqualTo("222")
//
//        }
//
//        def getByFbId = {
//            FacebookInfo.getByFbid("222").get must beEqualTo(fci1)
//        }
//
//        def existByFbId = {
//            FacebookInfo.existsByFbId("222") must beTrue
//        }
//
//    }
//}
