/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import java.util.{Calendar, Date}

import com.ansvia.digaku.DigakuTest.ChannelNameGenerator
import com.ansvia.digaku.model.{Article, Forum}
import com.ansvia.digaku.{DigakuTest, model}
import com.ansvia.perf.PerfTiming
import org.specs2.Specification
import org.specs2.specification.Step


class ChannelDaoSpec extends Specification with DigakuTest {

    def is = {
        sequential ^
            Step {
                Forum.getDaoListIndex.reset()
            } ^
        "forum dao creation should" ^
            "create forum not null" ! channelCreation.create ^
//            "create forum no quota" ! channelCreation.createUserNoQuota ^
            "forum return data must instantof Group" ! channelCreation.matchReturnedData ^
            "create forum expected data name" ! channelCreation.matchChannelName ^
            "create forum expected data desc" ! channelCreation.matchChannelDesc ^
        p ^
        "forum dao getter should" ^
            "get forum by id forum" ! channelGetter.getByIdChannel ^
            "get forum list" ! channelGetter.getList ^
        p ^
        "forum dao setter should" ^
            "set locked forum by Id" ! channelSetter.setLockedById ^
        p ^
        "forum dao deletion should" ^
            "delete forum by id" ! channelDeletion.deleteByIdChannel ^
            "delete forum by model" ! channelDeletion.deleteByModel ^
            "remove forum non permanent" ! channelDeletion.removeChannel ^
            "restore non permanent deleted forum" ! channelDeletion.restoreChannel ^
//            "can't restore forum because insufficient quota" ! channelDeletion.restoreChNoQuota ^
            "permanent delete forum should delete forum and all object origin to it permanently" ! channelDeletion.permanentDelete ^
        p ^
        "forum popular should" ^
            "get popular forum" ! popularChannel.getPopularChannel ^
        p ^
        "forum misc functionality" ^
            "is member checking" ! miscChannel.isMemberChecking ^
        end
    }

    object channelCreation {
        // forum
        val ch1Name = ChannelNameGenerator.nextName
        val ch2Name = ChannelNameGenerator.nextName
        val ch3Name = ChannelNameGenerator.nextName
        val ch4Name = ChannelNameGenerator.nextName

        // user
        val user1 = genUser // User.create(user1Name, "<EMAIL>", SexType.MALE, "13/01/1992")
//        user1.increasePoints(15) // increase point to user1 for set forum quota

        val ch1 = Forum.create(ch1Name, "ch1 desc", user1)

        def create = {
            ch1 != null must beTrue
        }

//        def createUserNoQuota = {
//            Forum.create(ch2Name, "ch2 desc", user1)
//            Forum.create(ch3Name, "ch3 desc", user1)
//
//            user1.channelQuota must beEqualTo(3) and
//                (Forum.create(ch4Name, "ch4 desc", user1) must throwAn[PermissionDeniedException])
//        }

        def matchReturnedData = {
            ch1.isInstanceOf[Forum] must beTrue
        }

        def matchChannelName = {
            ch1.name must beEqualTo(ch1Name)
        }

        def matchChannelDesc = {
            ch1.desc must beEqualTo("ch1 desc")
        }
    }

    object channelGetter {
        val ch3Name = ChannelNameGenerator.nextName

        val ch3 = Forum.create(ch3Name, "ch3 desc", genUser)
        val ch3a = Forum.getById(ch3.getId).get

        def getByIdChannel = {
            ch3a.name must beEqualTo(ch3Name)
        }

        def getList = {
            Forum.getListRight(None, None, 10).length must beGreaterThan(0)
        }
    }

    object channelSetter {
        val ch1Name = ChannelNameGenerator.nextName
        val ch1 = Forum.create(ch1Name, "ch1 desc", genUser)
        val ch2 = ch1.getId
        def setLockedById() = {
            Forum.setLockedChannelById(ch2, locked = true)
            Forum.getById(ch2).get.isLocked must beTrue
        }
    }

    object channelDeletion {

        import com.ansvia.digaku.utils.RichDate._

        val ch4Name = ChannelNameGenerator.nextName
        val ch5Name = ChannelNameGenerator.nextName
        val ch6Name = ChannelNameGenerator.nextName

        val user1 = genUser
        val user2 = genUser

//        user1.increasePoints(15)
//        user2.increasePoints(15)

        val ch4 = Forum.create(ch4Name, "ch4 desc", user1)
        val ch5 = Forum.create(ch5Name, "ch5 desc", user1)
        val ch6 = Forum.create(ch6Name, "ch6 desc", user1)
        val ch7 = genForumWithOwner(user2)

        val ch4Id = ch4.getId

        def deleteByIdChannel() = {
            Forum.deleteById(ch4Id)
            Forum.isExist(ch4) must beFalse
        }

        def deleteByModel() = {
            Forum.delete(ch5)
            Forum.isExist(ch5) must beFalse
        }

        def removeChannel() = {
            ch6.setDeleted(model.DeletedRole.POST_CREATOR, user1, "ada deh")

            val chx = Forum.getById(ch6.getId).get
            val nowTime = new Date().toStdFormat
            val dTime = new Date(chx.deletedTime).toStdFormat
            (chx.deleted must beTrue) and
                (chx.deletedReason must beEqualTo("ada deh")) and
                (dTime must beEqualTo(nowTime))
        }

        def restoreChannel = {
            var chx = Forum.getById(ch6.getId).get
            Forum.restore(chx)
            chx = Forum.getById(ch6.getId).get
            chx.deleted must beFalse
        }

//        def restoreChNoQuota = {
//            genForumWithOwner(user2)
//            genForumWithOwner(user2)
//
//            ch7.setDeleted(model.DeletedType.SOFT, model.DeletedRole.POST_CREATOR, user2)
//
//            Thread.sleep(1000)
//
//            genForumWithOwner(user2)
//
//            user1.channelQuota must beEqualTo(3) and
//                (Forum.restore(ch7) must throwAn[PermissionDeniedException])
//        }

        def permanentDelete = {
            // @TODO(robin): benerin ini
            skipped
//            val chx = Forum.getById(ch6.getId).get
//
//            val user = genUser
//            chx.addMembers(user)
//
//            val p = Post.createSimple(user, "hello", chx)
//
//            val pExists = Post.getPostById(p.getId).isDefined must beTrue
//
//            Forum.delete(chx)
//
//            pExists and (Post.getPostById(p.getId).isDefined must beFalse)
        }

    }

    object popularChannel {


        val user1 = genUser
//        user1.increasePoints(25000)

        val channel1Name = genForumName
        val channel1 = Forum.create(channel1Name, "forum 1 desc", user1)
        val channel2Name = genForumName
        val channel2 = Forum.create(channel2Name, "forum 2 desc", user1)
        val channel3Name = genForumName
        val channel3 = Forum.create(channel3Name, "forum 3 desc", user1)
        val channel4Name = genForumName
        val channel4 = Forum.create(channel4Name, "forum 4 desc", user1)

        val channel5 = genForumWithOwner(user1)
        val channel6 = genForumWithOwner(user1)
        val channel7 = genForumWithOwner(user1)
        val channel8 = genForumWithOwner(user1)
        val channel9 = genForumWithOwner(user1)

        channel5.setBlocked(block = true, user1)
        channel6.setBlocked(block = true, user1)
        channel7.setBlocked(block = true, user1)
        channel8.setBlocked(block = true, user1)
        channel9.setBlocked(block = true, user1)


        //buat post di forum yang di block
        for (n <- 0 to 5) {
            val p6 = Article.create(user1, "channels post " + n, "forum content post "+n, "tags post"+n, channel5)
            p6.addResponse(user1, "responses post"+n)
            val p7 = Article.create(user1, "channels post " + n, "forum content post "+n, "tags post"+n, channel6)
            p7.addResponse(user1, "responses post"+n)
            val p8 = Article.create(user1, "channels post " + n, "forum content post "+n, "tags post"+n, channel7)
            p8.addResponse(user1, "responses post"+n)
            val p9 = Article.create(user1, "channels post " + n, "forum content post "+n, "tags post"+n, channel8)
            p9.addResponse(user1, "responses post"+n)
            val p10 = Article.create(user1, "channels post " + n, "forum content post "+n, "tags post"+n, channel9)
            p10.addResponse(user1, "responses post"+n)
        }

        // channel1 memiliki 15 post dan setiap post memiliki 15 response dalam 30 hari terakhir
        for (n <- 0 to 15) {
            val p = Article.create(user1, "channel1 post " + n, "forum content post "+n, "tags post"+n, channel1)

            for (m <- 0 to 15) {
                p.addResponse(user1, "response post"+m)
            }

        }

        // channel2 memiliki 14 post dan setiap post memiliki 14 response dalam waktu 30 hari terakhir
        for (n <- 0 to 14) {
            val p = Article.create(user1, "channel2 post " + n, "forum content post "+n, "tags post"+n, channel2)

            for (m <- 0 to 14) {
                p.addResponse(user1, "response post"+m)
            }
        }

        // channel3 memiliki 13 post dan setiap post memiliki 13 response dalam waktu 30 hari terakhir
        for (n <- 0 to 13) {
            val p = Article.create(user1, "channel3 post " + n, "forum content post " + n, "tags post"+n, channel3)

            for (m <- 0 to 13) {
                p.addResponse(user1, "response post"+m)
            }
        }

        // channel4 memiliki 16 post dan setiap post memiliki 16 response dalam waktu sebelum 30 hari terakhir
//        transact {
            for (n <- 0 to 16) {

                val cal = Calendar.getInstance()
                cal.setTime(new Date())
                cal.add(Calendar.MONTH, -5)
                val date = cal.getTime

                val p = Article.create(user1, "channel4 post " + n, "forum 4 content post "+n, "tags post"+n, channel4)
                p.creationTime = date.getTime
                p.save()
                for (m <- 0 to 16) {
                    val resp = p.addResponse(user1, "response 4 post"+m)
                    resp.creationTime = date.getTime
                    resp.save()
                }
            }
//        }
        db.commit()

        def getPopularChannel = {


            /**
             * mock-up aja, karena popular channels
             * di-generate oleh digaku-shell `build-popular-channels`
             */
            def makePopular(ch:Forum, score:Double){
//                val ed = Group.rootVertex(0,"-Popular") --> POPULAR --> ch <()
//                ed.setProperty("score", score)
                Forum.addPopular(ch.getVertex, score)
            }

            channel1.reload()
            channel2.reload()
            channel3.reload()

            makePopular(channel1, 0.2)
            makePopular(channel2, 0.5)
            makePopular(channel3, 0.1)
            db.commit()

            val popch = Forum.getPopularChannels(0, 10, cached=false).toList

            popch must contain(channel2, channel1, channel3).inOrder
        }

    }

    object miscChannel extends PerfTiming {
        val ch1 = genForum
        val ch2 = genForum

        val u1 = genUser
        val u2 = genUser

        ch1.addMembers(u1)
        val users = for (i <- 0 to 100) yield genUser
        ch1.addMembers(users: _*)

        def isMemberChecking = {
            timing("isMemberChecking"){
                (Forum.isMember(u1, ch1) must beTrue) and
                (Forum.isMember(users.last, ch1) must beTrue) and
                    (Forum.isMember(u1, ch2) must beFalse) and
                    (Forum.isMember(u2, ch1) must beFalse) and
                    (Forum.isMember(u2, ch2) must beFalse)
            }
        }

    }

}
