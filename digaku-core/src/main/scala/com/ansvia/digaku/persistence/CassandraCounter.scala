/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.persistence

import com.ansvia.commons.logging.Slf4jLogger
import com.netflix.astyanax.model.ColumnFamily
import com.netflix.astyanax.retry.ExponentialBackoff
import com.netflix.astyanax.serializers.{ComparatorType, StringSerializer}

import scala.collection.mutable

/**
 * Author: robin
 *
 */

/**
 * Base counter provider trait,
 * semua counter provider harus extends dari sini.
 */
abstract class CounterProvider {

    def apply(key:String):CounterProviderOperator

}

abstract class CounterProviderOperator {

    /**
     * Get counter value.
     * @param key reference.
     * @return
     */
    def get(key:String):Long

    /**
     * Incerment counter by :by.
     * @param key reference.
     * @param by amount to increment.
     */
    def incrementBy(key:String, by:Long)


    /**
     * Increment counter by one.
     * @param key reference.
     */
    def increment(key:String){
        incrementBy(key, 1)
    }

    /**
     * Decrement counter by :by.
     * @param key reference.
     * @param by amount to decrement.
     */
    def decrementBy(key:String, by:Long){
        incrementBy(key, -by)
    }

    /**
     * Decrement counter by one.
     * @param key reference.
     */
    def decrement(key:String){
        incrementBy(key, -1)
    }


}

/**
 * Cassandra counter internal service.
 * @param clusterName cassandra cluster name.
 * @param keyspaceName cassandra keyspace name.
 * @param replicationStrategy replication strategy used.
 * @param replicationStrategyOpts replication strategy options.
 */
class CassandraCounter(seeds:String, clusterName:String, keyspaceName:String,
                       replicationStrategy:String, replicationStrategyOpts:String,
                       cfNameCounter:String = "counter") {

    import scala.collection.JavaConversions._

    lazy private val ctx = CassandraDriver.getContext(clusterName, keyspaceName, seeds,
        replicationStrategy, replicationStrategyOpts)
    lazy val keyspace = ctx.keyspace.getClient

    lazy val columnFamilyCounter = new ColumnFamily[java.lang.String, java.lang.String](
        cfNameCounter,
        StringSerializer.get(),
        StringSerializer.get())

    lazy val ensureReady = {
        val client = ctx.cluster.getClient
        val ksDef = client.describeKeyspace(keyspaceName)

        if (!ksDef.getColumnFamilyList.exists(_.getName.equalsIgnoreCase(cfNameCounter))){
            val cfDef = client.makeColumnFamilyDefinition()
                .setName(cfNameCounter)
                .setKeyspace(keyspaceName)
                .setKeyValidationClass(ComparatorType.UTF8TYPE.getClassName)
                .setDefaultValidationClass(ComparatorType.COUNTERTYPE.getClassName)
                .setCompressionOptions(Map("sstable_compression" -> ""))
            client.addColumnFamily(cfDef)
        }
    }

    def prepareRowQuery(rowKey:String) = {
        ensureReady
        keyspace.prepareQuery(columnFamilyCounter)
            .withRetryPolicy(new ExponentialBackoff(250L, 20))
            .getKey(rowKey)
    }


    def prepareMutation(rowKey:String, column:String) = {
        ensureReady
        keyspace.prepareColumnMutation(columnFamilyCounter, rowKey, column)
            .withRetryPolicy(new ExponentialBackoff(250L, 20))
    }




}


/**
 * Counter provider yang di-backing oleh Cassandra
 * menggunakan Cassandra counter column feature.
 */
trait CassandraCounterProvider extends CounterProvider with Slf4jLogger {

    private var counterService:CassandraCounter = _

    def setup(seeds:String, clusterName:String, keyspaceName:String,
              replicationStrategy:String, replicationStrategyOpts:String) = {

//        assert(counterService == null, "counter service not null, already setup?")

        if (counterService == null){
            info("setup CassandraCounterProvider, use settings: " +
                ("seeds: %s, cluster name: %s, keyspace: %s, " +
                    "replication strategy: %s, replication opts: %s").format(seeds, clusterName,
                        keyspaceName, replicationStrategy, replicationStrategyOpts))
            counterService = new CassandraCounter(seeds, clusterName, keyspaceName,
                replicationStrategy, replicationStrategyOpts)
        }

        this
    }


    def apply(rowKey:String) = {
        require(counterService != null, "counter service is null, please setup first")
        new CounterProviderOperator {
            def get(key: String): Long = {
                counterService.prepareRowQuery(rowKey)
                    .execute().getResult.getLongValue(key, 0L)
            }

            def incrementBy(key: String, by: Long){
                counterService.prepareMutation(rowKey, key).incrementCounterColumn(by)
                    .execute()
//                    .executeAsync()
            }
        }
    }

}

object CassandraCounterProvider extends CassandraCounterProvider


/**
 * Counter provider yang tidak melakukan apa-apa.
 *
 */
object NopCounterProvider extends CounterProvider with Slf4jLogger {
    def apply(key: String): CounterProviderOperator =
        new CounterProviderOperator {
            def get(key: String): Long = {
                warn("Nop counter provider will always return 0, " +
                    "please check your counter provider settings")
                0L
            }

            def incrementBy(key: String, by: Long){
                warn("no incremental performed, please check your counter provider settings")
            }
        }
}

/**
 * Hanya digunakan untuk testing saja.
 */
object DummyCounterProvider extends CounterProvider with Slf4jLogger {

    private val reg = new mutable.HashMap[String, CounterProviderOperator]
        with mutable.SynchronizedMap[String, CounterProviderOperator]

    warn("you are using DummyCounterProvider")

    def apply(key: String): CounterProviderOperator = {
        reg.getOrElseUpdate(key, new CounterProviderOperator {
            private val subCounterReg = new mutable.HashMap[String, Long]
                with mutable.SynchronizedMap[String, Long]


            /**
             * Get counter value.
             * @param key reference.
             * @return
             */
            def get(key: String): Long = subCounterReg.getOrElse(key, 0L)

            /**
             * Incerment counter by :by.
             * @param key reference.
             * @param by amount to increment.
             */
            def incrementBy(key: String, by: Long){
                subCounterReg.update(key, get(key) + by)
            }
        })
    }
}


trait CounterProviderComponent {
    val counterProvider:CounterProvider
}
