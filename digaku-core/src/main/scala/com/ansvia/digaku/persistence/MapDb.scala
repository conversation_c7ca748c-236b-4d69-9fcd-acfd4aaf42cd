/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.persistence

import org.mapdb.DBMaker

/**
 * Author: robin (<EMAIL>)
 *
 */
trait MapDb {

    lazy val mapDb = MapDb.uncompressed

}

object MapDb {

    lazy val uncompressed = DBMaker.newMemoryDirectDB()
        .sizeLimit(2)
        .transactionDisable()
        .asyncWriteEnable()
        .cacheLRUEnable()
        .make()

    lazy val compressed = DBMaker.newMemoryDirectDB()
        .sizeLimit(2)
        .compressionEnable()
        .transactionDisable()
        .asyncWriteEnable()
        .cacheLRUEnable()
        .make()

//    def printStats(){
////        uncompressed.close()
////        compressed.close()
////        println(" ---- MapDb stats -----")
////        println("   -- uncompressed --")
////        val store = Store.forEngine(uncompressed.getEngine.asInstanceOf[EngineWrapper].getWrappedEngine)
////        println("     free: %d".format(store.getFreeSize))
////        println("   -- compressed --")
////        val store2 = Store.forDB(compressed)
////        println("     free: %d".format(store2.getFreeSize))
//    }


}



