/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.stats

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types._
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.{Label, User}
import com.thinkaurelius.titan.core.attribute.Decimal
import com.tinkerpop.blueprints.Vertex
import com.tinkerpop.gremlin.Tokens.T
import scala.collection.JavaConversions._
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.graph.gremlin._


/**
 * Author: robin (<EMAIL>)
 */
object UserRelationEngine extends DbAccess with Slf4jLogger {


    /**
     * Digunakan untuk menandai relasi sebagai user mengetahui user lain
     * menggunakan sistem scoring.
     * @param userA user yang akan di-set sebagai mengetahui.
     * @param userB user target yang akan dianggap sebagai diketahui oleh user A.
     */
    def updateKnowsScore(userA:User, userB:User, manualScore:Double = -1.0){

        val ed = {
            val it = userA.reload().getVertex.pipe.outE(Label.KNOWS).as("ed")
                .inV().has("id", userB.getId).back("ed")
                .asInstanceOf[GremPipeEdge]
                .iterator()

            if (it.hasNext){
                it.next()
            }else{
                userA.reload().getVertex
                    .addEdge(Label.KNOWS, userB.reload().getVertex)
            }
        }

        val score:Double = if (manualScore < 0.0){
            // automatic by increment from previous score
            if (ed.getProperty[Decimal]("score") != null) {
                ed.getProperty[Decimal]("score").doubleValue() + 0.01
            }else {
                0.01
            }
        }else{
            manualScore
        }

        ed.setProperty("score", score)
        debug(" %s -> knows -> %s score: %.02f".format(userA, userB, score))
    }

    /**
     * Remove knows link and attributes from user.
     * @param user user to remove attributes.
     * @param knowsUsers knows users.
     */
    def removeKnowsEdges(user:User, knowsUsers:User*) = {

        val targetUserIds:java.util.List[IDType] = knowsUsers.map(_.getId)

        user.getVertex.pipe.outE(Label.KNOWS).as("ed")
            .inV().has("id", T.in, targetUserIds).asInstanceOf[GremPipeVertex]
            .sideEffect {
                (v:Vertex) => debug(s" removed: $v")
            }.back("ed").remove()
        db.commit()
    }

}
