/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.stats.model

import com.ansvia.digaku.Digaku

//
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.exc.DigakuException
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.model.{BaseModel, User, VertexLabels}
//import com.ansvia.digaku.utils.DateUtils
//import com.ansvia.graph.IdGraphTitanDbWrapper._
//import com.ansvia.graph.annotation.Persistent
//import com.tinkerpop.blueprints.Edge
//import com.tinkerpop.gremlin.Tokens.T
//import com.tinkerpop.pipes.PipeFunction
//
///**
// * Author: robin
// *
// */
//abstract class BaseStats(_name:String) extends BaseModel[IDType] {
//    @Persistent var name = _name
//}
//
////case class CountStats() extends BaseStats("count-stats") {
////
////    @Persistent var activeUserCount:Long = 0L
////    @Persistent var inactiveUserCount:Long = 0L
////    @Persistent var maleUserCount:Long = 0L
////    @Persistent var femaleUserCount:Long = 0L
////    @Persistent var articleCount:Long = 0L
////    @Persistent var simplePostCount:Long = 0L
////    @Persistent var pictureCount:Long = 0L
////    @Persistent var channelCount:Long = 0L
////    @Persistent var activeChannelCount:Long = 0L
////    @Persistent var inactiveChannelCount:Long = 0L
////    @Persistent var responseCount:Long = 0L
////    @Persistent var userSignupViaWebCount:Long = 0L
////    @Persistent var userSignupViaFBCount:Long = 0L
////    @Persistent var userSignupViaTWCount:Long = 0L
////    @Persistent var privateMessageCount:Long = 0L
////
////    def userCount:Long = maleUserCount + femaleUserCount
////    def postCount:Long = articleCount + simplePostCount + pictureCount
////
////}
////
////case class UserCountStats() extends BaseStats("user-count-stats"){
////    @Persistent var articleCount:Long = 0L
////    @Persistent var simpleCount:Long = 0L
////    @Persistent var pictureCount:Long = 0L
////
////    def postCount = articleCount + simpleCount + pictureCount
////}
//
//object UrlTrackerStats {
//    object kinds {
//        val URL = 3
//    }
//}
//case class UrlTrackerStats() extends BaseStats("url-tracker-stats") with DbAccess {
//
//    import UrlTrackerStats.kinds._
//    import com.ansvia.digaku.model.Label.{DAO_LIST, VISIT_URL}
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    import scala.collection.JavaConversions._
//
//    /**
//     * track url
//     * @param user user yang akan di-track.
//     * @param url url target yang di-access oleh user.
//     * @return
//     */
//    def trackUrl(user:User, url:String) = {
//        db.transactIdGraph(Digaku.engine.idFactory) { trx =>
//
//            // pastikan kalo dah ada url tersebut di db gunakan itu aja.
//
//            //                val _v = db.getVertices("stats.track-url", url.toLowerCase.trim)
//            val _v = trx.query().has("label", VertexLabels.STATS)
//                .has("stats.track-url", url.toLowerCase.trim).vertices()
//
//
//            if (_v.iterator().hasNext)
//                _v.iterator().next()
//            else {
//                val urlV = trx.addVertexWithLabel(VertexLabels.STATS)
//
//                urlV.setProperty("kind", URL)
//                urlV.setProperty("url", url)
//                urlV.setProperty("stats.track-url", url.toLowerCase.trim)
//            }
//
//
//        }
//        db.transactIdGraph(Digaku.engine.idFactory) { trx =>
//
//            val urlV = {
//                // pastikan kalo dah ada url tersebut di db gunakan itu aja.
//
////                val _v = db.getVertices("stats.track-url", url.toLowerCase.trim)
//                val _v = trx.query().has("label", VertexLabels.STATS)
//                    .has("stats.track-url", url.toLowerCase.trim).vertices()
//
//
//                if (_v.iterator().hasNext)
//                    _v.iterator().next()
//                else {
//                    throw new IllegalStateException("base vertex not created yet!")
//                }
//
//            }
//            val now = Digaku.engine.dateUtils.nowMilis
//
////            urlV.setProperty("kind", URL)
////            urlV.setProperty("url", url)
////            if (urlV.getProperty[String]("stats.track-url") == null)
////                urlV.setProperty("stats.track-url", url.toLowerCase.trim)
//            urlV.setProperty("creationTime", now)
//
//            val userV = trx.getVertex(user.getId) //user.reload()(trx).getVertex
//
////            val ed = userV --> VISIT_URL --> urlV <()
////            val edUrlOut = urlV --> VISIT_URL --> userV <()
//
//            // karena menggunakan transaction sendiri maka tidak bisa menggunakan wrapper -->
//            // yang mana wrapper tersebut menggunakan implicit db (non local transaction)
//            val ed = userV.addEdge(VISIT_URL, urlV)
//            val edUrlOut = urlV.addEdge(VISIT_URL, userV)
//
//            ed.setProperty("timeOrder", now)
//            ed.setProperty("creationTime", now)
//
//            edUrlOut.setProperty("timeOrder", now)
//            edUrlOut.setProperty("creationTime", now)
//
////            val ed2 = this.reload()(trx).getVertex --> DAO_LIST --> urlV <()
//            val ed2 = trx.getVertex(this.getId).addEdge(DAO_LIST, urlV)
//
//            ed2.setProperty("url", url)
//            ed2.setProperty("userId", user.getId)
//            ed2.setProperty("timeOrder", now)
//            ed2.setProperty("creationTime", now)
//        }
//    }
//
//    /**
//     * get url vertex.
//     * @param url url to get.
//     * @return
//     */
//    def getUrl(url:String) = {
////        db.getVertices("stats.track-url", url.toLowerCase.trim).headOption
//        db.query().has("label", VertexLabels.STATS).has("stats.track-url", url.toLowerCase.trim).vertices().headOption
//    }
//
//    def getTopMostVisitedUrls = {
////        import com.tinkerpop.pipes.util.structures.{Pair => BpPair}
//
//        val map:java.util.Map[_, Number] = new java.util.LinkedHashMap[String, Number]
//
//        this.reload().getVertex.pipe.outE(DAO_LIST).range(0, 2000)
//            .groupCount(map, new PipeFunction[Edge, java.lang.String]{
//                def compute(ed: Edge) = {
//                    ed.getOrElse("url", "-")
//                }
//            }/*,new PipeFunction[BpPair[_, java.lang.Number], java.lang.Number]{
//                def compute(argument: BpPair[_, java.lang.Number]) = argument.getB
//            }*/).cap()
//            .orderMap(T.decr)
//            //.asInstanceOf[GremlinPipeline[Edge, Map[String, java.lang.Long]]]
//            .iterate()
//
////        rv.foreach( x => println(x + " : " + x.getClass.getName) )
//        val rv = map.toList.sortBy(_._2.intValue()).reverse
//
////        rv.flatMap(_.ke)
//        rv
//    }
//
//}
////
object DigakuStats { //extends DaoBase[GraphType, UrlTrackerStats] with DbAccess {
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    import scala.collection.JavaConversions._
////    import com.ansvia.digaku.model.Label.USER_STATS
////
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.stats.model.StatsRootVertex"
//
    lazy val counter = Digaku.engine.counterProvider("digaku_stats")

//    /**
//     * penyimpanan counter yang dibutuhkan untuk stats
//     * ex : List Departemen yg paling banyak membuat thread
//     * see [[com.ansvia.digaku.shell.commands.analytics.StatsGenerator]]
//     */
//    lazy val departmentCounter = Digaku.engine.kvStoreProvider.build("department_counter")

    /**
     * penyimpanan untuk counter create message di bisik-bisik
     * penggunaan :
     * see com.ansvia.digaku.shell.commands.analytics.TotalCreateMessage
     */
    lazy val chatMessageCounter = Digaku.engine.seqStoreProvider.build("chat_message_counter", () => 0L)

    /**
     * Penyimpanan id users pada sequence store
     * yang digunakan untuk optimasi ketika generate
     * file analytic seluruh user yang telah ter registrasi.
     */
    lazy val registeredUserSeqStore = Digaku.engine.seqStoreProvider.build("registered_user", () => Digaku.engine.idFactory.createId().asInstanceOf[Long])
//
////
////    private def createCountStats():CountStats = {
////        transact {
////            val cs = CountStats().save().toCC[CountStats].getOrElse {
////                throw new DigakuException("Cannot create CountStats, persistent error?")
////            }
////
////            addToRoot(cs.getVertex)
////
////            cs
////        }
////    }
////
////
////
//    def getByName(name:String):Option[BaseStats] = {
//        rootVertex.pipe.out(rootVertexLabel)
//            .has("name", name).asInstanceOf[GremPipeVertex]
//            .headOption.flatMap(_.toCC[BaseStats])
//    }
////
////    def getCountStats:CountStats = {
////        getByName("count-stats").getOrElse {
////            createCountStats()
////        }.asInstanceOf[CountStats]
////    }
////
////    val countStatsLock = new Lock()
////
////    /**
////     * Safely use count stats in threading environment.
////     * @param func
////     * @tparam T
////     * @return
////     */
////    def useCountStats[T](func: CountStats => T):T = {
////        Stats.countStatsLock.acquire()
////        try {
////            func(getCountStats)
////        }finally{
////            Stats.countStatsLock.release()
////        }
////    }
////
////
////    /**
////     * get user count stats.
////     * @param user [[com.ansvia.digaku.model.User]]
////     * @return
////     */
////    def getUserCountStats(user:User):UserCountStats = {
////            user.reload().getVertex.pipe.in(USER_STATS)
////            .iterator().asInstanceOf[GremPipeVertex]
////            .headOption
////            .flatMap(_.toCC[UserCountStats])
////            .getOrElse {
////
////                val newUcs = UserCountStats().save()
////                val fNucs = newUcs.toCC[UserCountStats].getOrElse {
////                    throw new DigakuException("Cannot create UserCountStats, persistent error?")
////                }
////                user.reload()
////                newUcs --> USER_STATS --> user
////
////                db.commit()
////
////                fNucs
////            }
////    }
////
//    /**
//     * Get stats for tracking urls.
//     * @return
//     */
//    def getUrlTrackerStats:UrlTrackerStats = {
//        getByName("url-tracker-stats").getOrElse {
//            val v = UrlTrackerStats().save()
//            db.commit()
//            v.toCC[UrlTrackerStats].getOrElse {
//                throw new DigakuException("Cannot create UrlTrackerStats")
//            }
//        }.asInstanceOf[UrlTrackerStats]
//    }
//
}
//
//
