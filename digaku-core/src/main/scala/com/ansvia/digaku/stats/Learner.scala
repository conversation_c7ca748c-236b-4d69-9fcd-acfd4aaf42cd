///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.stats
//
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.ml.TextNormalizer
//import com.ansvia.digaku.model._
//import com.ansvia.digaku.persistence.CassandraDriver
//import com.ansvia.digaku.utils.DateUtils
//import com.ansvia.digaku.utils.RichString._
//import com.netflix.astyanax.connectionpool.exceptions.NotFoundException
//
///**
// * Author: robin (<EMAIL>)
// */
//
//object Learner extends Slf4jLogger {
//
//    private lazy val cassandraDriver:CassandraDriver[String, java.lang.Long] = Digaku.engine.analytic.cassandraDriver
//    private lazy val analyticCounter = Digaku.engine.counterProvider("analytic_counter")
//
//    private val A_YEAR_IN_SECONDS = 31556926
//    private val A_30_DAYS_IN_SECONDS = 2592000
//
//    private def year = Digaku.engine.dateUtils.getCurrentYear
//    private def month = Digaku.engine.dateUtils.getCurrentMonth
//
//    val ML_BLOCKED_PREFIX = "ml-training-data-blocked-"
//    val ML_TRAINING_DATA_PREFIX = "ml-training-data-"
//    val ML_TRAINING_CLASSIFICATION_PREFIX = "ml-training-classification-"
//    val ML_TRAINING_DATA_ALL = "ml-training-data-all"
//
//    def learnView(viewer:User, obj:Viewable[IDType]){
//        if (Digaku.config.enableMachineLearning) {
//
//            val key = "user-view-%s-%s-%s".format(viewer.getId, year, month)
//            val lastVisitCount = cassandraDriver.usingQuery { q =>
//                try {
//                    q.getRow(key).getColumn(obj.getId).execute().getResult.getStringValue match {
//                        case l:String if l.length > 0 && l.matches("^\\$\\d+.*$") =>
//                            l.substring(1).split(" ").apply(0).toInt
//                        case _ => 0
//                    }
//                } catch {
//                    case e: NotFoundException => 0
//                }
//            }
//            cassandraDriver.usingMutator { (cf, mb) =>
//                val textO = obj match {
//                    case article: Article => Some(TextNormalizer(article.title).trim)
////                    case sp: SimplePost =>
////                        val t = TextNormalizer(sp.content).trim
////                        val embeddedText = sp.getEmbeddedObjects.flatMap {
////                            case el: EmbeddedLink =>
////                                Some(TextNormalizer(el.title))
////                            case _ =>
////                                None
////                        }.headOption
////                        if (t.length > 3) {
////
////                            Some(embeddedText.filter(_.length > 3).map(tt => t + " " + tt).getOrElse(t))
////
////                        } else {
////                            embeddedText
////                        }
//                    case pic: Picture => Some(TextNormalizer(pic.title).trim)
//                    case _ => None
//                }
//                textO.filterNot(_.length < 3).map { _text =>
//
//
//                    val originName = obj match {
//                        case ho: HasOrigin[GraphType] => ho.origin.getName
//                        case _ => ""
//                    }
//
//                    val text = _text + " " + originName
//
//                    val count = lastVisitCount + 1
//                    val value = "$" + count + " " + originName + "$ " + text.trim
//
//                    debug(s"log user view event using key: $key\n   with text: $value")
//                    mb.withRow(cf, key).putColumn(obj.getId, value, A_YEAR_IN_SECONDS) // expires in year
//
//                    // save for raw training data
//                    val trainId = text.trim.crc32
//                    cassandraDriver.usingQuery { q =>
//                        try {
//                            q.getKey(ML_BLOCKED_PREFIX + year + "-" + month).getColumn(trainId).execute().getResult
//                        } catch {
//                            case e: NotFoundException =>
//                                try {
//                                    q.getKey("ml-training-data-" + year + "-" + month).getColumn(trainId).execute().getResult
//                                } catch {
//                                    case e: NotFoundException =>
//                                        val metaData = "$origin=" + originName + "$"
//                                        mb.withRow(cf, ML_TRAINING_DATA_PREFIX + year + "-" + month)
//                                            .putColumn(trainId, metaData + " " + text.trim, A_30_DAYS_IN_SECONDS)
//                                        mb.withRow(cf, ML_TRAINING_CLASSIFICATION_PREFIX + year + "-" + month)
//                                            .putColumn(trainId, "", A_30_DAYS_IN_SECONDS)
//                                        analyticCounter.increment("ml-training-data-all")
//                                }
//
//
//                        }
//                    }
//
//                    //                    mb.execute()
//                }
//            }
//        }
//    }
//
//
//    def learnLike(liker:User, obj:Likable){
//        if (Digaku.config.enableMachineLearning) {
//            val key = "user-like-%s-%s-%s".format(liker.getId, year, month)
//            cassandraDriver.usingMutator { (cf, mb) =>
//                val textO = obj match {
//                    case article: Article => Some(TextNormalizer(article.title).trim)
////                    case sp: SimplePost =>
////                        val t = TextNormalizer(sp.content).trim
////                        val embeddedText = sp.getEmbeddedObjects.flatMap {
////                            case el: EmbeddedLink =>
////                                Some(TextNormalizer(el.title))
////                            case _ =>
////                                None
////                        }.headOption
////                        if (t.length > 3) {
////
////                            Some(embeddedText.filter(_.length > 3).map(tt => t + " " + tt).getOrElse(t))
////
////                        } else {
////                            embeddedText
////                        }
//                    case pic: Picture => Some(TextNormalizer(pic.title).trim)
//                    case resp: Response => Some(TextNormalizer(resp.content).trim)
//                    case _ => None
//                }
//                textO.filterNot(_.length < 3).map { text =>
//
//                    val originName = obj match {
//                        case ho: HasOrigin[GraphType] => ho.origin.getName
//                        case _ => ""
//                    }
//
//                    val value = "$0.2 " + originName + "$ " + text.trim
//
//                    debug(s"log user like event using key: $key\n   with text: $value")
//                    mb.withRow(cf, key).putColumn(obj.getId, value, A_YEAR_IN_SECONDS) // expires in year
//
//                }
//            }
//        }
//    }
//
//
//    // submit to machine learning engine
//    def learnResponse(creator:User, response:Response){
//
//        if (Digaku.config.enableMachineLearning) {
//
//            val key = "user-response-%s-%s-%s".format(creator.getId, year, month)
//            cassandraDriver.usingMutator { (cf, mb) =>
//                val text = TextNormalizer(response.content).trim
//
//                if (text.length > 3) {
//                    debug(s"log user response event using key: $key, with text: $text")
//                    val metaData = "$0.3 :response:$ "
//                    mb.withRow(cf, key).putColumn(response.getId, metaData + text, A_YEAR_IN_SECONDS) // expires in year
//
//                    // save for raw training data
//                    val trainId = text.trim.crc32
//                    cassandraDriver.usingQuery { q =>
//                        try {
//                            q.getKey(ML_BLOCKED_PREFIX + year + "-" + month).getColumn(trainId).execute().getResult
//
//                        } catch {
//                            case e: com.netflix.astyanax.connectionpool.exceptions.NotFoundException =>
//
//                                try {
//                                    q.getKey(ML_TRAINING_DATA_PREFIX + year + "-" + month).getColumn(trainId).execute().getResult
//                                } catch {
//                                    case e: com.netflix.astyanax.connectionpool.exceptions.NotFoundException =>
//                                        // untuk training data di respon formatnya $:response:$
//                                        val metaData = "$:response:$"
//                                        mb.withRow(cf, ML_TRAINING_DATA_PREFIX + year + "-" + month)
//                                            .putColumn(trainId, metaData + " " + text.trim, A_30_DAYS_IN_SECONDS) // 30 days in seconds
//                                        mb.withRow(cf, ML_TRAINING_CLASSIFICATION_PREFIX + year + "-" + month)
//                                            .putColumn(trainId, "", A_30_DAYS_IN_SECONDS) // 30 days in seconds
//                                        analyticCounter.increment(ML_TRAINING_DATA_ALL)
//                                }
//
//
//                        }
//                    }
//
//                }
//            }
//        }
//    }
//
//
//
//    // submit to machine learning engine
//    def learnPost(creator:User, post:Post){
//
//        if (Digaku.config.enableMachineLearning) {
//
//            val key = "user-post-%s-%s-%s".format(creator.getId, year, month)
//            cassandraDriver.usingMutator { (cf, mb) =>
//                val content = post match {
//                    case p: Article => p.title
//                    case he: HasEmbeddedObject[IDType] =>
//                        val embs = he.getEmbeddedObjects
//                        if (embs.nonEmpty)
//                            embs.headOption.map {
//                                case vl: EmbeddedLink =>
//                                    vl.title.truncate(100)
//                                case _ =>
////                                    he match {
////                                        case sp: SimplePost =>
////                                            sp.content.truncate(100)
////                                        case _ =>
//                                            post.getContent.truncate(100)
////                                    }
//                            }.getOrElse(post.getContent.truncate(100))
//                        else
//                            post.getContent.truncate(100)
//                    case _ =>
//                        post.getContent.truncate(100)
//                }
//                val text = TextNormalizer(content + " " + post.origin.getName).trim
//
//                if (text.length > 3) {
//                    debug(s"log user post event using key: $key, with text: $text")
//                    val metaData = "$0.5 :post:$ "
//                    mb.withRow(cf, key).putColumn(post.getId, metaData + text, A_YEAR_IN_SECONDS) // expires in year
//
//                    // save for raw training data
//                    val trainId = text.trim.crc32
//                    cassandraDriver.usingQuery { q =>
//                        try {
//                            q.getKey(ML_BLOCKED_PREFIX + year + "-" + month).getColumn(trainId).execute().getResult
//                        } catch {
//                            case e: com.netflix.astyanax.connectionpool.exceptions.NotFoundException =>
//                                try {
//                                    q.getKey(ML_TRAINING_DATA_PREFIX + year + "-" + month).getColumn(trainId).execute().getResult
//                                } catch {
//                                    case e: com.netflix.astyanax.connectionpool.exceptions.NotFoundException =>
//
//                                        // untuk training data di post formatnya $:post:$
//                                        val metaData = "$:post:$"
//                                        mb.withRow(cf, ML_TRAINING_DATA_PREFIX + year + "-" + month).putColumn(trainId, metaData + " " + text.trim, A_30_DAYS_IN_SECONDS)
//                                        mb.withRow(cf, ML_TRAINING_CLASSIFICATION_PREFIX + year + "-" + month).putColumn(trainId, "", A_30_DAYS_IN_SECONDS)
//                                        analyticCounter.increment(ML_TRAINING_DATA_ALL)
//                                }
//
//                        }
//                    }
//
//                }
//            }
//        }
//    }
//
//    // submit to machine learning engine
//    def learnPic(creator:User, pic:PictureBase){
//
//        if (Digaku.config.enableMachineLearning) {
//
//            // pake -post- bukan -pic-
//            val key = "user-post-%s-%s-%s".format(creator.getId, year, month)
//            cassandraDriver.usingMutator { (cf, mb) =>
//                val text = TextNormalizer(pic.getTitle + " " + pic.origin.getName).trim
//
//                if (text.length > 3) {
//                    debug(s"log user post event using key: $key, with text: $text")
//                    val metaData = "$0.5 :pic:$ "
//                    mb.withRow(cf, key).putColumn(pic.getId, metaData + text, A_YEAR_IN_SECONDS) // expires in year
//
//                    // save for raw training data
//                    val trainId = text.trim.crc32
//                    cassandraDriver.usingQuery { q =>
//                        try {
//                            q.getKey("ml-training-data-blocked-" + year + "-" + month).getColumn(trainId).execute().getResult
//                        } catch {
//                            case e: com.netflix.astyanax.connectionpool.exceptions.NotFoundException =>
//                                try {
//                                    q.getKey("ml-training-data-" + year + "-" + month).getColumn(trainId).execute().getResult
//                                } catch {
//                                    case e: com.netflix.astyanax.connectionpool.exceptions.NotFoundException =>
//                                        // untuk training data di pic formatnya $:pic:$
//                                        val metaData = "$:pic:$"
//                                        mb.withRow(cf, ML_TRAINING_DATA_PREFIX + year + "-" + month).putColumn(trainId, metaData + " " + text.trim, A_30_DAYS_IN_SECONDS)
//                                        mb.withRow(cf, ML_TRAINING_CLASSIFICATION_PREFIX + year + "-" + month).putColumn(trainId, "", A_30_DAYS_IN_SECONDS)
//                                        analyticCounter.increment(ML_TRAINING_DATA_ALL)
//                                }
//                        }
//                    }
//
//                }
//            }
//        }
//    }
//}
