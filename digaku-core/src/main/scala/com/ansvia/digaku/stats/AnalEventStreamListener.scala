/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.stats

import java.text.SimpleDateFormat
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.event.impl.{CreateForumEvent, CreatePostEvent, CreateUserEvent, ResponseEvent, _}
import com.ansvia.digaku.event.{AsyncEventStreamListener, EventStreamListener, StreamEvent}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label.SHOUT
import com.ansvia.digaku.model.PostKind._
import com.ansvia.digaku.model._
import com.ansvia.digaku.stats.model.DigakuStats._
import com.ansvia.digaku.utils.TextExtractor
import com.ansvia.graph.BlueprintsWrapper._

/**
 * Author: robin
 *
 * Merupakan Event Stream Listener spesial untuk data analisis (analytics).
 * melisten semua event dan mencatatnya di count stats, user count stats, etc.
 *
 * Event stream listener ini juga menghandle user points dan level.
 */
private[digaku] object AnalEventStreamListener extends AsyncEventStreamListener with Slf4jLogger with DbAccess {



    val actorName = "anal"

    var enabled = true




    /**
     * Check is event handled by this listener.
     * WARNING: don't use `transact` inside this method
     * to avoid dead-lock.
     * @param eventName name of event.
     * @return
     */
    def isEventHandled(eventName: String) = true

    def asyncDispatch:PartialFunction[StreamEvent, Unit] = {
        if (enabled){
            db.commit()
            asyncDispatchInternal
        }else
        {case _ =>}
    }


    private def asyncDispatchInternal:PartialFunction[StreamEvent, Unit] = {
        case CreateUserEvent(user) =>

            registeredUserSeqStore.insert(user.getId, user.getName)

            user.sex match {
                case SexType.MALE =>
                    counter.increment("user.male")
                case SexType.FEMALE =>
                    counter.increment("user.female")
                case _ =>
            }

            counter.increment("user")

        case CreatePostEvent(user, post) =>

            post.kind match {
                case SIMPLE_POST =>
                    counter.increment("content.simple_post")
                case ARTICLE =>
                    counter.increment("content.article")
                case PICTURE =>
                    counter.increment("content.picture")
                case QUESTION =>
                    counter.increment("content.question")
            }
            counter.increment("content")
//            learnPost(user, post)

        case CreatePictureEvent(user, pic) =>

            counter.increment("content.picture")

//            learnPic(user, pic)

        case CreateForumEvent(ch) =>

            counter.increment("group")
            counter.increment("group.active")

        case DeleteChannelEvent(ch) =>

            counter.decrement("group.active")

        case RestoreChannelEvent(ch) =>

            counter.increment("group.active")

        case ResponseEvent(user, resp, _) =>

            counter.increment("content.response")

            // build popularity user by mention

            TextExtractor.extractUserIds(resp.content).flatMap(id => User.getById(id))
                .foreach { user =>
                user.getCounter.increment("popularity")
                Digaku.engine.searchEngine.indexUser(user)
            }

            // submit for machine learning
//            learnResponse(user, resp)

        case ShoutEvent(user, post, message, _) =>

            db.commit()

            /**
             * update user point apabila dibutuhkan.
             * increase 1 points setiap 2 shout unik.
             * yang didapatkan oleh post creator.
             */
            post.reload()
            val shoutCount = post.getVertex.pipe.in(SHOUT).dedup().count().toInt
//            if (shoutCount > post.shoutCount && shoutCount % 2 == 0){
//                debug("user point increased +1: " + post.creator)
//                post.creator.increasePoints()
//            }

            post.reload()
            post.getVertex.setProperty("shoutCount", shoutCount)
            post.shoutCount = shoutCount
            db.commit()

        case _ =>
    }


}

/**
 * Hanya digunakan untuk unittest.
 */
object BlockingAnalEventStreamListener extends EventStreamListener {
    /**
     * Check is event handled by this listener.
     * WARNING: don't use `transact` inside this method
     * to avoid dead-lock.
     * @param eventName name of event.
     * @return
     */
    def isEventHandled(eventName: String): Boolean = AnalEventStreamListener.isEventHandled(eventName)

    def dispatch: PartialFunction[StreamEvent, Unit] = AnalEventStreamListener.asyncDispatch
}

