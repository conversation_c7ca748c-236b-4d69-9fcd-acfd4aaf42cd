/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.se

import org.elasticsearch.client.transport.TransportClient
import org.elasticsearch.common.settings.ImmutableSettings
import org.elasticsearch.common.transport.InetSocketTransportAddress
import org.elasticsearch.node.NodeBuilder


class DistributedElasticSearchEngine(host:String, clusterName:String) extends AbstractElasticSearchEngine {

    private lazy val settings = ImmutableSettings.settingsBuilder()
        .put("http.enabled", false)
        .put("cluster.name", clusterName)
        .put("gateway", "none")

    protected lazy val node =  NodeBuilder.nodeBuilder()
        //        .local(true)
        .settings(settings.build())
//        .clusterName(clusterName)
        .node()

    override lazy val client = {
        val _client = new TransportClient(settings)
        val s = host.split(":")
        val _host = s(0)
        val port = s(1).toInt
        _client.addTransportAddress(new InetSocketTransportAddress(_host, port))
        _client
    }

    override def toString = "DistributedElasticSearchEngine(" + host + ")"
}


