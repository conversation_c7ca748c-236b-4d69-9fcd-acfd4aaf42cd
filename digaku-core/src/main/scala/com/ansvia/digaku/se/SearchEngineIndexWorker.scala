/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.se

import akka.actor.SupervisorStrategy.{Restart, Resume}
import akka.actor.{Actor, OneForOneStrategy}
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.event.impl.IndexEvent
import com.ansvia.digaku.exc.IgnoredException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model._

import scala.collection.mutable
import scala.concurrent.{ExecutionContext, Future}


/**
 * Author: robin
 *
 */


object SearchEngineIndexWorker extends DbAccess with Slf4jLogger {

    implicit val ec:ExecutionContext = Digaku.engine.actorSystem.dispatcher

    sealed case class GetStatus()
    sealed case class Status(idx:Long, of:Long, current:String){
        var lastIndexed = "-"
        var inProcessing = false
    }
    sealed case class Start(mode:Int)
    sealed case class IndexModels(models:List[String])
//    sealed case class SetPuller(dataPuller:Iterator[Any])
    sealed case class Stop()
    sealed case class SetSearchEngine(se:DigakuSearchEngine)


    protected val ROOT_VERTEX_POS = "rootVertexPos"
    protected val LAST_TIME_ORDER = "lastTimeOrder"
    protected val LAST_ID = "lastId"
    protected val WORK_INDEX = "workIndex"


    /**
     * configurable flag untuk memberitahukan bahwa
     * perlu atau tidak-nya index worker menyimpan state
     * yang berguna untuk resume apa terjadi kegagalan.
     * by default `false` yang artinya menyimpan state.
     */
    @volatile
    var noState = false



    private lazy val stateStore = {
//        assert(Config.dbConf != null, "Config.dbConf is null, not configured yet?")

//        val strategyOpts = Digaku.engine.config.dbConf.getStringArray("storage.cassandra.replication-strategy-options")
//        val strategyOptsStr = strategyOpts.toList.grouped(2).map(a => s"${a(0)}:${a(1)}").mkString(",")

//        object _stateStore extends CassandraBackedKVStore(Global.STANDARD_CF_NAME,
//            Digaku.engine.config.dbConf.getString("storage.cassandra.keyspace"),
//            Digaku.engine.config.dbConf.getString("storage.cluster-name"),
//            Digaku.engine.config.dbConf.getString("storage.hostname"),
//            Digaku.engine.config.dbConf.getString("storage.cassandra.replication-strategy-class"),
//            strategyOptsStr
//        )
//        _stateStore

        Digaku.engine.kvStoreProvider.build("indexingstate")
    }

    private def getPrevState():List[String] = {
        if (!noState)
            stateStore.getOption[String]("indexing_state").map(_.split(" ").toList)
                .getOrElse(List.empty[String])
        else
            Nil
    }

    private def saveLatestState(ids:List[String]){
        if (!noState)
            stateStore.set("indexing_state", ids.mkString(" "))
    }

    def resetState(){
        if (!noState)
            stateStore.delete("indexing_state")
    }

    def isCanResume = getPrevState().length > 0

//    def resetIncState(){
//        resetState()
//    }

    private var workIndex:Long = 0L
    private var total:Long = 0
    private var currentWork = ""
    private var _stop = false

    @volatile
    private var _working = false

    /**
     * Start reindexing processes.
     * @param _se search engine used.
     * @param mode see [[ReindexMode]]
     */
    def start(_se:DigakuSearchEngine, mode:Int, selectedModels:List[String]){
        assert(_se != null, "Search engine not set")

        import com.ansvia.graph.BlueprintsWrapper._

        if (!_working){

            currentWork = ""


            val state = getPrevState()

            debug("state: " + state)

            var rootVertexPos = mode match {
                case ReindexMode.FULL =>
                    if (state.length > 0){
                        state(0).toInt
                    }else
                        0
                case ReindexMode.FIX_STALED =>
                    0 // fix staled akan men-scan dari root vertex pos 0 sampai akhir
            }
            workIndex = mode match {
                case ReindexMode.FULL =>
                    if (state.length > 0){
                        try {
                            state(2).toInt
                        }catch{
                            case _:NumberFormatException =>
                                0
                        }
                    }else
                        0
                case ReindexMode.FIX_STALED =>
                    0 // fix staled akan men-scan dari root vertex pos 0 sampai akhir
            }

            var roots:List[DaoBase[GraphType, _]] =
                List(Forum, User, App, Post, Article, Response,
                    UserGroup, FAQ, Topic).slice(rootVertexPos, 10)

            if (selectedModels.nonEmpty){

                roots = roots.filter(c => selectedModels.contains(c.getClass.getName.replaceAll("\\$","")))

                debug("roots (filtered using select models): " + roots)
            }

            total =  roots.map(_.getCount).sum

            debug("will indexing at total " + total + " vertices")

            _stop = false
            _working = true
            var processing:Option[BaseModel[IDType]] = None
            var rvIndexed = new mutable.HashMap[String, (Int, Int)]()


            Future {
                // scalastyle:off regex.line
                println("Start indexing ----------------------------------------------")
                try {

                    for (root <- roots){

                        debug("indexing model " + root)

                        rootVertexPos = rootVertexPos + 1

                        val lastIdO = mode match {
                            case ReindexMode.FULL =>
                                val state = getPrevState()
                                if (state.length > 1 && state(1).toLong > 0){
                                    Some(state(1).toLong)
                                }else
                                    None
                            case ReindexMode.FIX_STALED =>
                                val _lastStaledId = _se.getLastIndexed.collect {
                                    case model:BaseModel[IDType] =>
                                        model
                                }.map(_.getId)
                                info("FIX_STALED detect last staled vertex id: " + _lastStaledId)
                                _lastStaledId
                        }

                        debug("last id: " + lastIdO)

                        var itemCount = 0

                        try {
                            // menggunakan graph collection vertex
                            root.paging(lastIdO, None, 500) { case (page, vertices) =>
                                vertices.foreach { v =>
                                    v.toCC[BaseModel[IDType]] match {
                                        case dd@Some(d) =>

                                            processing = dd

                                            workIndex = workIndex + 1
                                            currentWork = d.toString

                                            _se.index(d)

                                            itemCount += 1

                                            if (workIndex % 50 == 0) {
                                                saveLatestState(List((rootVertexPos - 1).toString, d.getId.toString, workIndex.toString))
                                            }

                                            Digaku.engine.eventStream.emit(IndexEvent(workIndex, total, currentWork,
                                                (workIndex.toDouble * 100D) / total.toDouble))

                                        case _ =>
                                            throw IgnoredException("cannot toCC %s for expected model `%s`".format(v, v.getOrElse("_class_", "unknown")))
                                    }
                                }

                                !_stop
                            }
                        }catch{
                            case e:java.lang.ClassNotFoundException =>
                                error("%s, ignore model processing".format(e))
                        }



                        // kalo ingin gunakan dli comment-out ini
//
//                        root.getDaoListIndex match {
//                            case dli:CassandraPartitionedDaoListIndex =>
//
//                                dli.pagingRaw(lastIdO, None, 500, None, reverse = false) {
//                                    case (page, vertices) =>
//
//                                        vertices.foreach { v =>
//                                            v.toCC[BaseModel[IDType]] map { d =>
//
//
//                                                processing = Some(d)
//
//                                                //                                            Thread.sleep(5)
//
//                                                workIndex = workIndex + 1
//                                                currentWork = d.toString
//
//                                                _se.index(d)
//
//                                                itemCount += 1
//
//                                                if (workIndex % 50 == 0) {
//                                                    saveLatestState(List((rootVertexPos - 1).toString, d.getId.toString, workIndex.toString))
//                                                }
//
//                                                Digaku.engine.eventStream.emit(IndexEvent(workIndex, total, currentWork,
//                                                    (workIndex.toDouble * 100D) / total.toDouble))
//
//                                                //                                    } getOrElse {
//                                                //                                        warn("cannot serialize vertex " + v)
//                                                //                                    }
//                                            } getOrElse {
//                                                throw IgnoredException("cannot toCC %s for expected model `%s`".format(v, v.getOrElse("_class_", "unknown")))
//                                            }
//                                        }
//
//                                        !_stop
//                                }
//
//                            case x =>
//                                throw new NotImplementedException("reindex for %s not supported yet".format(x))
//                        }

                        rvIndexed += (root.getClass.getCanonicalName -> (root.getCount.toInt, itemCount))
                        debug("%d object indexed".format(itemCount))

                        saveLatestState(List(rootVertexPos.toString,0.toString,workIndex.toString))

                        if (_stop)
                            throw IgnoredException()
                    }

                    // reset state
                    resetState()

                    info("total indexed: %d".format(workIndex))
                    rvIndexed.foreach { case (cls, (expectedCount, actualCount)) =>
                        info(s"    + $cls -> $actualCount (expected: $expectedCount)")
                    }
                    info("Done indexing --------------------------------------------------------------------")

                }catch{
                    case e:IgnoredException =>
                        debug("ignored: " + e.getMessage)

                    case e:Exception =>
                        error("%s".format(e))
                        error("error \"" + e.getMessage + "\" in/after processing: " + processing)
                        error(e.getStackTraceString)
                        warn("staled indexing.")

                }finally{
                    _working = false
                }

                // scalastyle:on regex.line
            }

            debug("dispatching to future, let future take the rest.")

        }
        else {
            warn("batch indexing already running.")
        }
    }

    def stop(){
        _stop = true
    }

}



class SearchEngineIndexWorker extends Actor with DbAccess with Slf4jLogger {

    import com.ansvia.digaku.se.SearchEngineIndexWorker._

//    private var _stop = false


    import scala.concurrent.duration._

    override val supervisorStrategy =
        OneForOneStrategy(maxNrOfRetries = 15, withinTimeRange = 30.seconds) {
            case _: ArithmeticException      => Resume
            case _: NullPointerException     => Restart
            case _: IllegalArgumentException => akka.actor.SupervisorStrategy.Stop
            case _: Exception                => Restart
        }




    private var _se:DigakuSearchEngine = null


    def receive = {

        case SetSearchEngine(se) =>
            _se = se

//        case SetPuller(dp) =>
//            this.dataPuller = dp
//            debug("data puller set.")


        case Start(mode:Int) =>
            debug("start batch indexing...")
            try {
                start(_se, mode, Nil) // Nil assume all
            }catch{
                case e:Exception =>
                    error(e.getMessage)
                    error(e.getStackTraceString)
            }

        case IndexModels(models) =>
            debug("start per models indexing...")
            try {
                start(_se, ReindexMode.FULL, selectedModels = models)
            }catch{
                case e:Exception =>
                    error(e.getMessage)
                    error(e.getStackTraceString)
            }

        case GetStatus() =>
            val status = Status(workIndex, total, currentWork)

            status.inProcessing = _working

            info("indexing [%d of %d] %s".format(workIndex, total, currentWork))

            sender ! status

        case Stop() =>
//            this._stop = true
            stop()
            debug("stopping batch indexing operation...")
    }



}


