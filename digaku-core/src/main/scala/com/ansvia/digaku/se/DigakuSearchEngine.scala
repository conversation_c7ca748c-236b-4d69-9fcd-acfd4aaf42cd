/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.se

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types._
import com.ansvia.digaku.model._

/**
 * Author: robin
 *
 */

trait SearchEngineComponent {
    def searchEngine: DigakuSearchEngine
}

abstract class DigakuSearchEngine extends Slf4jLogger {

    type PF = PartialFunction[Any, Unit]

    def getVersion: String

    def index: PF = {
        case x =>
            try {
                indexInternal(x)
                afterIndexCallback(x)
            } catch {
                case e: Exception =>
                    error(e.getMessage)
                    error(e.getStackTraceString)
                    onIndexError(x, e)
            }

    }

    private def indexInternal: PF =
        (indexArticle orElse indexEvent
            orElse indexUser orElse indexForum
            orElse indexApp orElse indexResponse
            orElse indexUserGroup orElse indexFaq
            orElse indexTopic
            /*orElse { case x => warn("unknown type to index: " + x) }*/)


    private var lastIndexed: Option[Any] = None

    /**
     * Callback for customizing your action
     * This method will called after indexing operation.
     * Override this as you wish.
     * @return
     */
    def afterIndexCallback(obj: Any) {
        lastIndexed = Some(obj)
    }

    /**
     * Callback called when index error.
     * Override this to handle exception with your custom logic.
     * @param obj the indexable object.
     * @param e exception object.
     */
    def onIndexError(obj: Any, e: Exception) {

    }


    /**
     * Get last indexed document
     * @return
     */
    def getLastIndexed: Option[Any] = lastIndexed


    /** **********************************************
      * INDEX SECTION
      * **********************************************/

    def indexForum: PF

    /**
     * Index article into search engine index engine.
     * @return
     */
    def indexArticle: PF

    /**
     * Index response to search engine
     * @return
     */
    def indexResponse: PF

    def indexEvent: PF

//    def indexPicture: PF

    def indexUser: PF

//
//    def indexPrivateMessage: PF

    /**
     * Digunakan untuk meng-index App.
     * @see [[com.ansvia.digaku.model.App]]
     * @return
     */
    def indexApp: PF

    /**
     * Index group mentions
     * @return
     */
    def indexUserGroup:PF

    /**
     * Index untuk model FAQ
     * @return
     */
    def indexFaq:PF

    /**
     * Index untuk model Topic
     * @return
     */
    def indexTopic:PF


    protected def getEmpty[T] = {
        SpecificSearchResult[T](Seq.empty[T],0,0L,"0ms")
    }

    /** **********************************************
      * SEARCH SECTION
      * **********************************************/

    /**
     * Search article
     * @param query term query to search.
     * @param offset starting offset.
     * @param limit ends limit.
     * @param includeFromPrivateOrigin whether to search also from private origin (default false)
     * @param scope origin scope, whether to search scope limited to origin.
     *              used in local forum search.
     * @return
     */
    def searchArticle(query: String, offset: Int = 0, limit: Int = 10,
                      includeFromPrivateOrigin: Boolean = false,
                      scope: Option[Origin[GraphType]] = None,
                      searcher:Option[User] = None): SpecificSearchResult[Article]

    /**
     * Search article with highlighted content
     * @param query term query to search.
     * @param offset starting offset.
     * @param limit ends limit.
     * @param includeAllPrivate whether to search also from private origin (default false)
     * @param scope origin scope, whether to search scope limited to origin.
     *              used in local forum search.
     * @param threadType Filter thread berdasarkan type thread nya.
     *                   Set `Some("polling")` filter thread yang embed polling
     *                   Set `Some("event")` filter thread yang embed event
     *                   Set `Some("standard")` filter thread yang tidak embed polling atau event
     *                   Set `None` tanpa filter
     * @param label Filter thread yang hanya memiliki label tertentu.
     * @param minRating Filter thread yang memiliki rating lebih dari atau
     *                  sama dengan parameter yang telah ditentukan
     * @param maxRating Filter thread yang memiliki rating kurang dari atau
     *                  sama dengan parameter yang telah ditentukan
     * @param searcher user who search.
     * @return see[[com.ansvia.digaku.se.SpecificSearchResult]]
     */
    def searchHighlighterArticle(query: String, offset: Int, limit: Int,
                                 includeAllPrivate:Boolean=false,
                                 scope:Option[Origin[GraphType]]=None,
                                 threadType:Option[String] = None,
                                 label:Option[String] = None,
                                 minRating:Option[Double] = None,
                                 maxRating:Option[Double] = None,
                                 searcher:Option[User] = None,
                                 searchAsAdmin:Boolean = true):SpecificSearchResult[HighlightResult[Article]]

    /**
     * Search article berdasarkan creator-nya dan presfektif searcher-nya.
     * article di close subforum tidak akan tampil selama
     * searcher tidak join ke close subforum tersebut
     * @param query term query to search.
     * @param creator creator
     * @param searcher user searcher
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchUserArticle(query:String, creator:User, searcher:User, offset:Int, limit:Int):SpecificSearchResult[Article]

    /**
     * Search article with a specific tag
     * @param query term query to search.
     * @param offset starting offset.
     * @param limit ends limit.
     * @param includeFromPrivateOrigin whether to search also from private origin (default false)
     * @param scope origin scope, whether to search scope limited to origin.
     *              used in local forum search.
     * @return
     */
    def searchArticleByTag(query:String, offset:Int = 0, limit:Int = 10,
                     includeFromPrivateOrigin:Boolean = false,
                     scope: Option[Origin[GraphType]] = None,
                     searcher:Option[User] = None): SpecificSearchResult[Article]

    /**
     * search blocked article
     * @param query term query to search.
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchBlockedArticle(query: String, offset: Int, limit: Int): SpecificSearchResult[Article]

    /**
     * Search closed article
     * @param query term query to search.
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchClosedArticle(query: String = "", originId: Long, offset: Int, limit: Int): SpecificSearchResult[Article]

    /**
     * Search user.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @param jobTitle Filter User yang memiliki job title sesuai dengan parameter yang ditentukan
     *                 Set `None` ketika tidak ingin di filter berdasarkan job title.
     * @param department Filter User yang memiliki department sesai dengan parameter yang diberikan.
     *                   Set `None` ketika tidak ingin di filter berdasarkan department.
     * @param rank Filter User yang memiliki rank sesuai dengan parameter yang ditentukan
     *              Set `None` ketika tidak ingin di filter berdasarkan rank.
     * @param locked include user locked or not.
     * @param inactive include user deactivated or not.
     * @return
     */
    def searchUser(query: String, offset: Int = 0, limit: Int = 10,
                   jobTitle:Option[String] = None, department:Option[String] = None,
                   rank:Option[String] = None, locked:Boolean = false,
                   inactive:Boolean = false): SpecificSearchResult[User]

    /**
      * Search user agent.
      * @param query query term
      * @param offset starting offset.
      * @param limit end limit.
      * @return
      */
    def searchUserAgent(query: String, offset: Int = 0, limit: Int = 10): SpecificSearchResult[User]

    /**
     * digunakan untuk search member/moderator pada sebuah forum.
     * @param query query term.
     * @param forumId forum id target untuk search membership
     * @param subForumFlag see [[SubForumStateFlags]]
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchForumMembers(query:String, forumId:Long, subForumFlag:Int, offset:Int, limit:Int): SpecificSearchResult[User]

    /**
     * Search forum.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchForum(query: String, offset: Int = 0, limit: Int = 10,
                      includePrivate: Boolean = false, searcher: Option[User] = None,
                      onlyHasArticleFeature:Boolean = false, withDeleted:Boolean = false,
                      searchAsAdmin:Boolean = true): SpecificSearchResult[Forum]


    /**
     * Advance search forum.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @param searcher referensi ke user / siapa yang melakukan search
     *                 ini penting apabila kita ingin melakukan search ke forum-forum
     *                 dengan akses tertentu yang hanya dipegang oleh searcher.
     * @param isPrivate Set `Some(true)` ketika hanya ingin mendapatkan forum yang private
     *                  Set `Some(false)` ketika hanya ingin mendapatkan forum yang public
     *                  Set `None` ketika ingin mengambil yang public atau private
     * @param parentForum Filter untuk hanya mengambil SubForum yang berada di forum tertentu
     * @param minThreadCount Filter forum yang memiliki jumlah thread lebih dari atau sama dengan minThreadCount
     * @param maxThreadCount Filter forum yang memiliki jumlah thread kurang dari atau sama dengan minThreadCount
     * @param hasArticleFeature Set `Some(true)` ketika ingin filter forum yang memiliki fitur article(Filter hanya SubForum)
     *                          Set `Some(false)` ketika ingin filter forum yang memiliki tidak memiliki fitur article(parent SubForum)
     *                          Set `None` ketika tidak ingin filter atau mendapatkan parent forum dan SubForum nya
     * @param searchAsAdmin Set `true` ketika ingin mendapatkan forum sebagai user admin atau mendapatkan semua forum baik yang
     *                          public atau semua forum yang private meskipun searcher tidak join ke forum private tersebut
     * @return
     */
    def advanceSearchForum(query: String, offset: Int, limit: Int,
                           isPrivate:Option[Boolean] = None, searcher:Option[User]=None,
                           parentForum:Option[Forum] = None, minThreadCount:Option[Int] = None,
                           maxThreadCount:Option[Int] = None, hasArticleFeature:Option[Boolean] = Some(false),
                           searchAsAdmin:Boolean = true): SpecificSearchResult[Forum]

    /**
     * search blocked forum
     * @param query term query to search.
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchBlockedChannel(query: String, offset: Int, limit: Int): SpecificSearchResult[Forum]

    /**
     * Search event.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @param scope search scope.
     * @return
     */
    def searchEvent(query: String, offset: Int = 0, limit: Int = 10,
                    includePrivate: Boolean = false,
                    scope: Option[Origin[GraphType]] = None): SpecificSearchResult[Event]

    /**
     * Search User Group
     * @param query search term query
     * @param offset starting offset
     * @param limit ends limit
     */
    def searchUserGroup(query:String, offset:Int, limit:Int, postAllowedFor:Option[User] = None, withEmptyUser:Boolean = false): SpecificSearchResult[UserGroup]

//    /**
//     * Search picture.
//     * @param query query term.
//     * @param offset starting offset.
//     * @param limit end limit.
//     * @return
//     */
//    def searchPicture(query: String, offset: Int = 0, limit: Int = 10,
//                      includePrivate: Boolean = false,
//                      scope: Option[Origin[GraphType]] = None): SpecificSearchResult[Picture]
//
//    /**
//     * search blocked picture
//     * @param query term query to search.
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchBlockedPicture(query: String, offset: Int, limit: Int): SpecificSearchResult[Picture]
//
//    /**
//     * Search closed picture
//     * @param query term query to search.
//     * @param originId
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchClosedPicture(query: String = "", originId: Long, offset: Int, limit: Int): SpecificSearchResult[Picture]

    //
    //    /**
    //     * search blocked video
    //     * @param query term query to search.
    //     * @param offset
    //     * @param limit
    //     * @return
    //     */
    //    def searchBlockedVideo(query: String, offset: Int, limit: Int):SpecificSearchResult[SimplePost]
    //
    //    /**
    //     * Search closed video
    //     * @param query term query to search.
    //     * @param originId
    //     * @param offset
    //     * @param limit
    //     * @return
    //     */
    //    def searchClosedVideo(query:String = "", originId:Long, offset:Int, limit:Int):SpecificSearchResult[SimplePost]
    //
    //    /**
    //     * Search deal
    //     * @param query query term.
    //     * @param offset starting offset.
    //     * @param limit end limit.
    //     * @return
    //     */
    //    def searchDeal(query:String, offset:Int=0, limit:Int=10,
    //                   includePrivate:Boolean=false,
    //                   scope:Option[Origin[GraphType]]=None):SpecificSearchResult[Deal]

    /**
     * Untuk mencari post.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @param includePrivate whether to include private.
     * @param scope search scope.
     * @return
     */
    def searchPost(query: String, offset: Int, limit: Int,
                   includePrivate: Boolean = false,
                   scope: Option[Origin[GraphType]] = None): SearchPostResult

    //    /**
    //     * Untuk mencari ads
    //     * @param query search term query
    //     * @param offset starting offset.
    //     * @param limit ends limit.
    //     * @return
    //     */
    //    def searchAd(query:String, offset:Int, limit:Int):SpecificSearchResult[Advertisement]

    //    /**
    //     * Cari sponsor berdasarkan kriteria yang diinginkan.
    //     * @param query term query.
    //     * @param timeRange range waktu aktif.
    //     * @param ageRange range umur target.
    //     * @param genderTarget
    //     * @param showTimeRange
    //     * @param targetCountry
    //     * @param targetProvince
    //     * @param targetCity
    //     * @param containsClosed
    //     * @param offset
    //     * @param limit
    //     * @return
    //     */
    //    def searchSponsorPost(query: String,
    //                      offset: Int, limit: Int,
    //                      timeRange:Option[(Date,Date)]=None,
    //                      ageRange:Option[(Int,Int)]=None,
    //                      genderTarget:Option[Int]=None,
    //                      showTimeRange:Option[(Int,Int)]=None,
    //                      targetCountry:Option[String]=None,
    //                      targetProvince:Option[String]=None,
    //                      targetCity:Option[String]=None,
    //                      containsClosed:Boolean=false): SpecificSearchResult[SponsorPost]

//    /**
//     * Search private message in context of user.
//     * @param query query term.
//     * @param user user context (only search from this user context).
//     * @param key private message key. @see [[com.ansvia.digaku.se.DigakuSearchEngine.PrivateMessageKey]]
//     * @param offset starting offset.
//     * @param limit ends limit.
//     */
//    def searchPrivateMessage(query: String, user: User,
//                             key: DigakuSearchEngine.PrivateMessageKey,
//                             offset: Int, limit: Int): SpecificSearchResult[PrivateMessage]

    /**
     * Search app
     * @see [[com.ansvia.digaku.model.App]]
     * @param query search term query.
     * @param offset starting offset.
     * @param limit ends limit.
     * @return
     */
    def searchApp(query: String, offset: Int, limit: Int): SpecificSearchResult[App]

    /**
     * Search FAQ
     * see [[com.ansvia.digaku.model.FAQ]]
     * @param query search term query.
     * @param offset starting offset.
     * @param limit ends limit.
     * @return
     */
    def searchFaq(query:String, offset:Int, limit:Int, includeArchived:Boolean = false): SpecificSearchResult[HighlightResult[FAQ]]

    /**
     * Search FAQ
     * see [[com.ansvia.digaku.model.Topic]]
     * @Param includeArchivedTopic true jika ingin menapatkan topic yang diarsipkan.
     * @param query search term query.
     * @param offset starting offset.
     * @param limit ends limit.
     * @return
     */
    def searchTopic(query:String, includeArchivedTopic:Boolean, offset:Int, limit:Int): SpecificSearchResult[Topic]


    /**
     * Search all.
     * For specific search use:
     * + [[com.ansvia.digaku.se.DigakuSearchEngine.searchArticle]]
     * + [[com.ansvia.digaku.se.DigakuSearchEngine.searchUser]]
     *
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def search(query: String, offset: Int, limit: Int,
               includePrivate: Boolean = false,
               scope: Option[Origin[GraphType]] = None,
               searcher: Option[User] = None): SearchAllResult

    /**
     * Digunakan untuk mencari department dari semua user
     * @param query search term query.
     * @param limit
     * @return
     */
    def searchUserDepartments(query:String, limit:Int):List[String]

    /**
     * Digunakan untuk mencari job title dari semua user
     * @param query search term query.
     * @param limit
     * @return
     */
    def searchUserJobTitles(query:String, limit:Int):List[String]


    //    def reindex(dataPuller:Iterator[Any])

    /**
     * Reindex search engine data.
     * @param mode see [[ReindexMode]]
     */
    def reindex(mode: Int)

    def reindexModels(models: String*)

    def stopIndexing() {}

    // reset/clear index
    def reset() {}

    /** **********************************************
      * DELETE SECTION
      * **********************************************/

    def deleteChannel(ch: Forum)

    def deleteUser(user: User)

    //    def deleteAd(ad: Advertisement)

    //    /**
    //     * Delete sponsor post index
    //     * @param sp sponsor post to delete.
    //     */
    //    def deleteSponsorPost(sp: SponsorPost)

    /**
     * Remove app from index.
     * @see [[com.ansvia.digaku.model.App]]
     * @param app app to remove.
     */
    def deleteApp(app: App)

    /**
     * Delete user group dari index elasticsearch
     * @param userGroup
     */
    def deleteUserGroup(userGroup: UserGroup)

    /**
     * Delete faq dari index elasticsearch
     * @param faq
     */
    def deleteFaq(faq: FAQ)

    /**
     * Delete topic dari index elasticsearch
     * @param topic
     */
    def deleteTopic(topic: Topic)


//    /**
//     * Delete private message index.
//     * @param pm private message to delete.
//     */
//    def deletePrivateMessage(pm: PrivateMessage)

    def close() {}
}

object ReindexMode {
    val FULL = 1
    val FIX_STALED = 2
}

case class SpecificSearchResult[T](entries: Seq[T], count: Long, took: Long, tookStr: String)

/**
 * Result untuk SpecificSearchResult entries yang memiliki highlight search result
 * @param entry Entry search result
 * @param highlighted Highlighted search result fields
 *                    ex: Map("content" -> "result of <em>highlighted</em> content")
 */
case class HighlightResult[T](entry:T, highlighted:Map[String, String])

case class SearchAllResult(articles: SpecificSearchResult[HighlightResult[Article]],
                           users: SpecificSearchResult[User],
                           forums: SpecificSearchResult[Forum],
                           events: SpecificSearchResult[Event],
                           apps: SpecificSearchResult[App])

case class SearchPostResult(articles: SpecificSearchResult[Article],
                            events: SpecificSearchResult[Event])


//object DigakuSearchEngine {
//
//    trait PrivateMessageKey
//
//    object PrivateMessageKey {
//
//        object FROM extends PrivateMessageKey
//
//        object PARTICIPANTS extends PrivateMessageKey
//
//        object MESSAGE extends PrivateMessageKey
//
//    }
//
//}

//
//class NativeLuceneSearchEngine extends DigakuSearchEngine {
//    def indexArticle = {
//        case article:Article =>
//
//    }
//
//    def indexUser = null
//}



