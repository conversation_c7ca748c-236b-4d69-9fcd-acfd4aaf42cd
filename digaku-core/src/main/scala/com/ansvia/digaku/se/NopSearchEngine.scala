/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.se

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types._
import com.ansvia.digaku.model.{Article, _}


/**
 * Author: robin
 *
 * Search engine abstraction that doing nothing.
 */
object NopSearchEngine extends DigakuSearchEngine with Slf4jLogger {

    def getVersion = ""

    def indexArticle = {
        case _ =>
    }

    def indexUser = {
        case _ =>
    }

//    /**
//     * index simple post
//     * @return
//     */
//    def indexSimplePost = {
//        case _ =>
//    }


    def indexForum = {
        case _ =>
    }

    def indexEvent = {
        case _ =>
    }

    def indexResponse = {
        case _ =>
    }

//    def indexPicture = {
//        case _ =>
//    }

//    def indexDeal = {
//        case _ =>
//    }


//    def indexAd = { case _ => }

//    def indexSponsorPost = { case _ => }

//    def indexPrivateMessage = { case _ => }

    def indexApp = { case _ => }

    /**
     * Index group mentions
     * @return
     */
    def indexUserGroup = {case _ => }

    /**
     * Index untuk model FAQ
     * @return
     */
    def indexFaq = {case _ => }

    /**
     * Index untuk model Topic
     * @return
     */
    def indexTopic = {case _ => }

    /**
     * Search all.
     * For specific search use:
     * [[com.ansvia.digaku.se.DigakuSearchEngine.searchArticle]]
     * [[com.ansvia.digaku.se.DigakuSearchEngine.searchUser]]
     *
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def search(query: String, offset: Int, limit: Int,
               includePrivate:Boolean=false,
               scope:Option[Origin[GraphType]]=None,
               searcher:Option[User]=None) =
        SearchAllResult(
            getEmpty[HighlightResult[Article]],
            getEmpty[User],
            getEmpty[Forum],
            getEmpty[Event],
            getEmpty[App]
        )

    /**
     * Search all kind of post includes Article, Event, and Picture.
     * @param query term query.
     * @param offset starting offset.
     * @param limit ends limit.
     * @param includeFromPrivateOrigin whether return also from private origin.
     * @param scope origin search scope, if set will only search from that scope.
     * @return
     */
    def searchPost(query:String, offset:Int, limit:Int,
                   includeFromPrivateOrigin:Boolean=false,
                   scope:Option[Origin[GraphType]]=None): SearchPostResult = {
        SearchPostResult(
            getEmpty[Article],
            getEmpty[Event]
        )
    }


    /**
     * Reindex search engine data.
     * @param mode see [[ReindexMode]]
     */
    override def reindex(mode: Int): Unit = ()


    override def reindexModels(models: String*){}

    def searchArticle(query: String, offset: Int, limit: Int,
                      includeFromPrivateOrigin:Boolean=false,
                      scope:Option[Origin[GraphType]]=None,
                      userRef:Option[User] = None) =
        getEmpty[Article]

    /**
     * Search article with highlighted content
     * @param query term query to search.
     * @param offset starting offset.
     * @param limit ends limit.
     * @param includeAllPrivate whether to search also from private origin (default false)
     * @param scope origin scope, whether to search scope limited to origin.
     *              used in local forum search.
     * @param threadType Filter thread berdasarkan type thread nya.
     *                   Set `Some("polling")` filter thread yang embed polling
     *                   Set `Some("event")` filter thread yang embed event
     *                   Set `Some("standard")` filter thread yang tidak embed polling atau event
     *                   Set `None` tanpa filter
     * @param label Filter thread yang hanya memiliki label tertentu.
     * @param minRating Filter thread yang memiliki rating lebih dari atau
     *                  sama dengan parameter yang telah ditentukan
     * @param maxRating Filter thread yang memiliki rating kurang dari atau
     *                  sama dengan parameter yang telah ditentukan
     * @param searcher user who search.
     * @return see[[com.ansvia.digaku.se.SpecificSearchResult]]
     */
    def searchHighlighterArticle(query: String, offset: Int, limit: Int,
                                 includeAllPrivate:Boolean = false,
                                 scope:Option[Origin[GraphType]] = None,
                                 threadType:Option[String] = None,
                                 label:Option[String] = None,
                                 minRating:Option[Double] = None,
                                 maxRating:Option[Double] = None,
                                 searcher:Option[User] = None,
                                 searchAsAdmin:Boolean = true):SpecificSearchResult[HighlightResult[Article]] = {
        getEmpty[HighlightResult[Article]]
    }

    /**
     * Search article berdasarkan creator-nya dan presfektif searcher-nya.
     * article di close subforum tidak akan tampil selama
     * searcher tidak join ke close subforum tersebut
     * @param query term query to search.
     * @param creator creator
     * @param searcher user searcher
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchUserArticle(query:String, creator:User, searcher:User, offset:Int, limit:Int):SpecificSearchResult[Article] = {
        getEmpty[Article]
    }

    def searchArticleByTag(query: String, offset: Int, limit: Int,
                      includeFromPrivateOrigin:Boolean=false,
                      scope:Option[Origin[GraphType]]=None,
                      userRef:Option[User] = None) =
        getEmpty[Article]

    /**
     * search blocked article
     * @param query term query to search.
     * @param offset
     * @param limit
     * @return
     */
    def searchBlockedArticle(query: String, offset: Int, limit: Int) = {
        getEmpty[Article]
    }

    /**
     * Search closed article
     * @param query term query to search.
     * @param originId
     * @param offset
     * @param limit
     * @return
     */
    def searchClosedArticle(query:String = "", originId:Long, offset: Int, limit: Int) =
        getEmpty[Article]

    /**
     * Search user.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @param jobTitle Filter User yang memiliki job title sesuai dengan parameter yang ditentukan
     *                 Set `None` ketika tidak ingin di filter berdasarkan job title.
     * @param department Filter User yang memiliki department sesai dengan parameter yang diberikan.
     *                   Set `None` ketika tidak ingin di filter berdasarkan department.
     * @param rank Filter User yang memiliki rank sesuai dengan parameter yang ditentukan
     *              Set `None` ketika tidak ingin di filter berdasarkan rank.
     * @param locked include user locked or not.
     * @param inactive include user deactivated or not.
     * @return
     */
    def searchUser(query: String, offset: Int = 0, limit: Int = 10,
                   jobTitle:Option[String] = None, department:Option[String] = None,
                   rank:Option[String] = None, locked:Boolean = false,
                   inactive:Boolean = false) =
        getEmpty[User]

    /**
      * Search user agent.
      * @param query query term
      * @param offset starting offset.
      * @param limit end limit.
      * @return
      */
    def searchUserAgent(query: String, offset: Int = 0, limit: Int = 10): SpecificSearchResult[User] = getEmpty[User]

    /**
     * digunakan untuk search member/moderator pada sebuah forum.
     * @param query query term.
     * @param forumId forum id target untuk search membership
     * @param subForumFlag see [[SubForumStateFlags]]
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchForumMembers(query:String, forumId:Long, subForumFlag:Int, offset:Int, limit:Int) =
        getEmpty[User]


    /**
     * Search group.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchForum(query: String, offset: Int, limit: Int, includePrivate:Boolean=false,
                      searcher:Option[User]=None,
                      onlyHasArticleFeature:Boolean = false, withDeleted:Boolean = false,
                      searchAsAdmin:Boolean = true) =
        getEmpty[Forum]

    /**
     * Advance search forum.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @param searcher referensi ke user / siapa yang melakukan search
     *                 ini penting apabila kita ingin melakukan search ke forum-forum
     *                 dengan akses tertentu yang hanya dipegang oleh searcher.
     * @param isPrivate Set `Some(true)` ketika hanya ingin mendapatkan forum yang private
     *                  Set `Some(false)` ketika hanya ingin mendapatkan forum yang public
     *                  Set `None` ketika ingin mengambil yang public atau private
     * @param parentForum Filter untuk hanya mengambil SubForum yang berada di forum tertentu
     * @param minThreadCount Filter forum yang memiliki jumlah thread lebih dari atau sama dengan minThreadCount
     * @param maxThreadCount Filter forum yang memiliki jumlah thread kurang dari atau sama dengan minThreadCount
     * @param hasArticleFeature Set `Some(true)` ketika ingin filter forum yang memiliki fitur article(Filter hanya SubForum)
     *                          Set `Some(false)` ketika ingin filter forum yang memiliki tidak memiliki fitur article(parent SubForum)
     *                          Set `None` ketika tidak ingin filter atau mendapatkan parent forum dan SubForum nya
     * @param searchAsAdmin Set `true` ketika ingin mendapatkan forum sebagai user admin atau mendapatkan semua forum baik yang
     *                          public atau semua forum yang private meskipun searcher tidak join ke forum private tersebut
     * @return
     */
    def advanceSearchForum(query: String, offset: Int, limit: Int,
                           isPrivate:Option[Boolean] = None, searcher:Option[User]=None,
                           parentForum:Option[Forum] = None, minThreadCount:Option[Int] = None,
                           maxThreadCount:Option[Int] = None, hasArticleFeature:Option[Boolean] = Some(false),
                           searchAsAdmin:Boolean = true) : SpecificSearchResult[Forum] =
        getEmpty[Forum]

    /**
     * search blocked group
     * @param query term query to search.
     * @param offset
     * @param limit
     * @return
     */
    def searchBlockedChannel(query: String, offset: Int, limit: Int):SpecificSearchResult[Forum] =
        getEmpty[Forum]

    /**
     * Search event.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchEvent(query: String, offset: Int, limit: Int,
                    includePrivate:Boolean=false,
                    scope:Option[Origin[GraphType]]=None) =
        getEmpty[Event]

    /**
     * Search Group Mentions
     * @param query search term query
     * @param offset starting offset
     * @param limit ends limit
     */
    def searchUserGroup(query:String, offset:Int, limit:Int, postAllowedFor:Option[User] = None, withEmptyUser:Boolean = false): SpecificSearchResult[UserGroup] = getEmpty[UserGroup]

//    /**
//     * Search picture.
//     * @param query query term.
//     * @param offset starting offset.
//     * @param limit end limit.
//     * @return
//     */
//    def searchPicture(query: String, offset: Int, limit: Int,
//                      includePrivate:Boolean=false,
//                      scope:Option[Origin[GraphType]]=None) =
//        getEmpty[Picture]

//    /**
//     * search blocked picture
//     * @param query term query to search.
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchBlockedPicture(query: String, offset: Int, limit: Int) =
//        getEmpty[Picture]
//
//    /**
//     * Search closed picture
//     * @param query term query to search.
//     * @param originId
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchClosedPicture(query:String = "", originId:Long, offset: Int, limit: Int) =
//        getEmpty[Picture]

//    /**
//     * search blocked video
//     * @param query term query to search.
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchBlockedVideo(query: String, offset: Int, limit: Int) =
//        getEmpty[SimplePost]
//
//    /**
//     * Search closed video
//     * @param query term query to search.
//     * @param originId
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchClosedVideo(query:String = "", originId:Long, offset: Int, limit: Int) =
//        getEmpty[SimplePost]
//
//    /**
//     * Search deal
//     * @param query query term.
//     * @param offset starting offset.
//     * @param limit end limit.
//     * @return
//     */
//    def searchDeal(query: String, offset: Int, limit: Int,
//                   includePrivate:Boolean=false,
//                   scope:Option[Origin[GraphType]]=None) =
//        getEmpty[Deal]
//
//
//    /**
//     * Untuk mencari ads
//     * @param query
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchAd(query: String, offset: Int, limit: Int) = getEmpty[Advertisement]
//
//
//    /**
//     * Cari sponsor berdasarkan kriteria yang diinginkan.
//     * @param query term query.
//     * @param offset
//     * @param limit
//     * @param timeRange range waktu aktif.
//     * @param ageRange range umur target.
//     * @param genderTarget
//     * @param showTimeRange
//     * @param targetCountry
//     * @param targetProvince
//     * @param targetCity
//     * @param containsClosed
//     * @return
//     */
//    def searchSponsorPost(query: String,
//                      offset: Int, limit: Int,
//                      timeRange:Option[(Date,Date)]=None,
//                      ageRange:Option[(Int,Int)]=None,
//                      genderTarget:Option[Int]=None,
//                      showTimeRange:Option[(Int,Int)]=None,
//                      targetCountry:Option[String]=None,
//                      targetProvince:Option[String]=None,
//                      targetCity:Option[String]=None,
//                      containsClosed:Boolean=false): SpecificSearchResult[SponsorPost] = getEmpty[SponsorPost]

//    /**
//     * Search private message in context of user.
//     * @param query query term.
//     * @param user user context (only search from this user context).
//     * @param key private message key. @see [[com.ansvia.digaku.se.DigakuSearchEngine.PrivateMessageKey]]
//     * @param offset starting offset.
//     * @param limit ends limit.
//     */
//    def searchPrivateMessage(query: String,
//                             user: User,
//                             key:DigakuSearchEngine.PrivateMessageKey,
//                             offset:Int, limit:Int) = getEmpty[PrivateMessage]


    /**
     * Search app
     * @see [[com.ansvia.digaku.model.App]]
     * @param query search term query.
     * @param offset starting offset.
     * @param limit ends limit.
     * @return
     */
    def searchApp(query: String, offset: Int, limit: Int): SpecificSearchResult[App] =
        getEmpty[App]

    /**
     * Search FAQ
     * see [[com.ansvia.digaku.model.FAQ]]
     * @param query search term query.
     * @param offset starting offset.
     * @param limit ends limit.
     * @return
     */
    def searchFaq(query:String, offset:Int, limit:Int, includeArchived:Boolean = false): SpecificSearchResult[HighlightResult[FAQ]] =
        getEmpty[HighlightResult[FAQ]]

    /**
     * Search FAQ
     * see [[com.ansvia.digaku.model.Topic]]
     * @param query search term query.
     * @Param includeArchivedTopic true jika ingin menapatkan topic yang diarsipkan.
     * @param offset starting offset.
     * @param limit ends limit.
     * @return
     */
    def searchTopic(query:String, includeArchivedTopic:Boolean, offset:Int, limit:Int): SpecificSearchResult[Topic] =
        getEmpty[Topic]

    /**
     * Digunakan untuk mencari department dari semua user
     * @param query search term query.
     * @param limit
     * @return
     */
    def searchUserDepartments(query:String, limit:Int):List[String] = List.empty[String]

    /**
     * Digunakan untuk mencari job title dari semua user
     * @param query search term query.
     * @param limit
     * @return
     */
    def searchUserJobTitles(query:String, limit:Int):List[String] = List.empty[String]

    def deleteChannel(ch: Forum) {}

    def deleteUser(user: User) {}

//    def deleteAd(ad: Advertisement) {}
//
//    /**
//     * Delete sponsor post index
//     * @param sp sponsor post to delete.
//     */
//    def deleteSponsorPost(sp: SponsorPost){}

    /**
     * Remove app from index.
     * @see [[com.ansvia.digaku.model.App]]
     * @param app app to remove.
     */
    def deleteApp(app: App){}

    /**
     * Delete user group dari index elasticsearch
     * @param userGroup
     */
    def deleteUserGroup(userGroup: UserGroup) = {}

    /**
     * Delete faq dari index elasticsearch
     * @param faq
     */
    def deleteFaq(faq: FAQ) = {}

    /**
     * Delete topic dari index elasticsearch
     * @param topic
     */
    def deleteTopic(topic: Topic) = {}

//    /**
//     * Delete private message index.
//     * @param pm private message to delete.
//     */
//    def deletePrivateMessage(pm: PrivateMessage) = {}
}
