/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.se

import org.elasticsearch.common.settings.ImmutableSettings
import org.elasticsearch.node.{Node, NodeBuilder}

/**
 * Author: robin
 *
 */
class EmbeddedElasticSearchEngine(val storeDir:String="/tmp/digaku-es-index")
    extends AbstractElasticSearchEngine {

    private lazy val settings = ImmutableSettings.settingsBuilder()
        .put("http.enabled", false)
        .put("path.data", storeDir)
        .put("gateway", "none")

    protected val node:Node = NodeBuilder.nodeBuilder()
        .local(true)
        .settings(settings.build())
        .node()

    override def toString = "EmbeddedElasticSearchEngine(" + storeDir + ")"
}
