/*
 * Copyright (c) 2013-2015 Ans<PERSON>, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.se

import java.util.{Date, NoSuchElementException}
import java.util.concurrent.TimeUnit
import akka.actor.{<PERSON><PERSON><PERSON>ill, Props}
import akka.pattern.ask
import akka.util.Timeout
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.dao.{DaoListIndex, DaoListIndexFactory}
import com.ansvia.digaku.exc.{InvalidParameterException, NotExistsException}
import com.ansvia.digaku.model.{Article, _}
import com.ansvia.digaku.utils.TextCompiler
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest
import org.elasticsearch.action.admin.indices.exists.indices.IndicesExistsRequest
import org.elasticsearch.action.search.{SearchResponse, SearchType}
import org.elasticsearch.client.Client
import org.elasticsearch.common.settings.ImmutableSettings
import org.elasticsearch.common.xcontent.XContentBuilder
import org.elasticsearch.common.xcontent.XContentFactory._
import org.elasticsearch.index.query.functionscore.ScoreFunctionBuilders
import org.elasticsearch.index.query._
import org.elasticsearch.indices.IndexMissingException
import org.elasticsearch.node.Node
import org.elasticsearch.search.aggregations.AggregationBuilders
import org.elasticsearch.search.aggregations.bucket.terms.Terms
import org.elasticsearch.search.sort.SortOrder
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.digaku.utils.ForumSettings._
import scala.concurrent.Await
import scala.concurrent.duration._
import com.ansvia.digaku.utils.UserSettings._
import com.ansvia.digaku.database.GraphCompat._
import com.ansvia.digaku.helpers.TypeHelpers._
import scala.collection.mutable.ListBuffer

/**
 * Author: robin
 *
 */

abstract class AbstractElasticSearchEngine() extends DigakuSearchEngine with Slf4jLogger {

    import com.ansvia.digaku.Types._
    import com.ansvia.digaku.se.SearchEngineIndexWorker._
    import org.elasticsearch.common.xcontent.XContentFactory._

    import scala.collection.JavaConversions._

    private implicit def db:GraphType = Digaku.engine.database.getRaw

    protected val node:Node

    lazy val client:Client = node.client()

    private lazy val version = {
        client.admin().cluster().prepareNodesInfo()
            .all().execute().get().getNodes.head.getVersion.toString
    }

    private var lastIndexed = "-"
    private var indexWorkerActorName = "se-index-worker"

    lazy val worker = {
        val rv = Digaku.engine.actorSystem.actorOf(Props[SearchEngineIndexWorker], indexWorkerActorName)
        rv ! SetSearchEngine(this)
        rv
    }
    private var checkIndicesEnsured = false
    private val indexNames = Seq(
        ("digaku1", "article" :: "event" :: "picture" :: Nil),
        ("digaku2", "user" :: Nil),
        ("digaku3", "forum" :: Nil),
//        ("digaku4", "ads" :: Nil),
//        ("digaku5", "pm" :: Nil),
        ("digaku6", "app" :: Nil),
        ("digaku7", "simple-post" :: Nil),
//        ("digaku8", "ads_sp" :: Nil),
        ("digaku9", Nil), // digaku9 index for custom and general purpose search.
        ("digaku10", "response" :: Nil), // digaku10 digunakan untuk indexing response.
        ("digaku11",  "user-group" :: Nil),
        ("digaku12", "faq" :: "topic" :: Nil)
//        ("digaku13", "sc-chat" :: "sc-chat-tags" :: Nil) // digaku13 SUDAH DIGUNAKAN untuk ScChat
    )


    lazy val indexObjectBuilder = new IndexObjectBuilder


    def setIndexWorkerActorName(name:String) = {
        indexWorkerActorName = name
        this
    }


    def getVersion = version

    lazy val unindexedDLI:DaoListIndex = DaoListIndexFactory
        .build("es_failed_index", partitioned = true)

    /**
     * Error callback ini digunakan untuk men-track object-object yang gagal di-index
     * dengan maksud agar memudahkan untuk me-reindex-nya kembali secara delta, sehingga menghindari
     * kehilangan index data dan tidak perlu melakukan operasi mahal full reindexing.
     *
     * @param obj the indexable object.
     * @param e exception object.
     */
    override def onIndexError(obj:Any, e: Exception){
        obj match {
            case model:BaseModel[IDType] =>
                unindexedDLI.store(model.getVertex)
        }
    }

    /**
     * Untuk memeriksa apakah index exists.
     * @param indexName nama index yang akan diperiksa.
     * @return
     */
    def indexExists(indexName:String) =
        client.admin().indices().exists(new IndicesExistsRequest(indexName)).actionGet().isExists

    /**
     * Pastikan semua index sudah ada/created.
     * apabila belum ada maka akan di-create otomatis.
     */
    def ensureIndicesCreated(){
        if (!checkIndicesEnsured){

            var logNoticed = false

            def _index(_indexName:String, _types:List[String]){
                if (!indexExists(_indexName)){

                    if (!logNoticed){
                        info("search engine indices not exists, build first...")
                        logNoticed = true
                    }

                    val createIndexReqBuilder = client.admin().indices().prepareCreate(_indexName)

                    for (_t <- _types){
                        val mappingBuilder = jsonBuilder()
                            .startObject()
                                .startObject(_t)
                                    .startObject("_timestamp").field("enabled", true).field("store", true).endObject()

                                    // Digunakan untuk aggregate supaya bisa phrase
                                    .startObject("properties")
                                        .startObject("department").field("type", "string")
                                            .startObject("fields")
                                                .startObject("exact").field("type", "string").field("index", "not_analyzed").endObject()
                                            .endObject()
                                        .endObject()
                                        .startObject("via").field("type", "string")
                                            .startObject("fields")
                                                .startObject("exact").field("type", "string").field("index", "not_analyzed").endObject()
                                            .endObject()
                                        .endObject()
                                        .startObject("jobTitle").field("type", "string")
                                            .startObject("fields")
                                                .startObject("exact").field("type", "string").field("index", "not_analyzed").endObject()
                                            .endObject()
                                        .endObject()
                                        .startObject("rank").field("type", "string")
                                            .startObject("fields")
                                                .startObject("exact").field("type", "string").field("index", "not_analyzed").endObject()
                                            .endObject()
                                        .endObject()
                                        .startObject("label").field("type", "string")
                                            .startObject("fields")
                                                .startObject("exact").field("type", "string").field("index", "not_analyzed").endObject()
                                            .endObject()
                                        .endObject()
                                    .endObject()
                                .endObject()
                            .endObject()

                        createIndexReqBuilder.addMapping(_t, mappingBuilder)

//                        println("mappingBuilder: " + mappingBuilder.prettyPrint().string())
                    }

                    createIndexReqBuilder.setSettings(ImmutableSettings.settingsBuilder().loadFromSource(
                    jsonBuilder().startObject()
                        .startObject("analysis")
                            .startObject("analyzer")
                                .startObject("standard")
                                    .field("type", "custom")
                                    .field("tokenizer", "standard")
                                    .field("filter", Array("snowball", "standard", "whitespace", "lowercase"))
                                .endObject()
                            .endObject()
                        .endObject()
                    .endObject().string()
                    ))

                    try {
                        createIndexReqBuilder.execute().actionGet()
                    }catch{
                        case _:org.elasticsearch.indices.IndexAlreadyExistsException =>
                            warn(s"index ${_indexName} already exists")
                    }

                }
            }

            for ((idxName, _types) <- indexNames){
                _index(idxName, _types)
            }


            client.admin().cluster().prepareHealth().setWaitForYellowStatus().execute().actionGet()
            Thread.sleep(5000)

            checkIndicesEnsured = true
        }
    }


    /**
     * Get last indexed document
     * @return
     */
    override def getLastIndexed: Option[Any] = {

        import com.ansvia.graph.BlueprintsWrapper._


        try {
            // digaku9 di-exclude karena tidak menyertakan tipe
            // hal ini untuk menghindari error "no sorting map for _timestamp" di ES service-nya
            val resp = client.prepareSearch(indexNames.filterNot(_._1 == "digaku9").map(_._1): _*)
                .setSearchType(SearchType.DEFAULT)
                .setQuery(QueryBuilders.matchAllQuery())
                .setSize(1)
                .addSort("_timestamp", SortOrder.DESC)
                .execute()
                .actionGet()

            resp.getHits.iterator().flatMap { hit =>
                if (hit.getSource.get("vid") != null){
                    val id:Long = hit.getSource.get("vid").toString.toLong
                    val _v = db.getVertex(id)
                    if (_v != null){
                        _v.toCC[BaseModel[IDType]]
                    }else{
                        error("cannot toCC vertex with id %d, got null".format(id))
                        None
                    }
                }else
                    None
            }.toList.headOption
        }catch{
            case e:org.elasticsearch.indices.IndexMissingException =>
                None
        }
    }


    /************************************************
      * INDEX SECTION
      ***********************************************/

    override def index = {
        case x =>
            // ensure all indices created
            ensureIndicesCreated()
            super.index(x)
    }




    /**
     * Index article into search engine index engine.
     * @return
     */
    override def indexArticle: PF = {
        case article:Article => {
            ensureIndicesCreated()

            debug("indexing " + article)

            if (article.origin != null){
                client.prepareIndex("digaku1", "article", article.getId.toString)
                    .setSource(indexObjectBuilder.build(article, x => x )).execute()
                    .actionGet()

                lastIndexed = article.toString
            }else{
                warn("object %s has no origin/origin is null".format(article))
            }
        }
    }

    /**
     * Index response to search engine
     * @return
     */
    def indexResponse: PF = {
        case response:Response =>
            ensureIndicesCreated()

            debug("indexing " + response)

            val responseId = response.getId.toString

            // Hanya response yang memiliki origin yang di-index
            try {
                client.prepareIndex("digaku10", "response", responseId)
                    .setSource(indexObjectBuilder.buildResponse(response, x => x)).execute()
                    .actionGet()
            } catch {
                case e:NotExistsException =>
                    warn(e.getMessage)
            }

            lastIndexed = response.toString
    }

    def indexUser:PF = {
        case user:User =>
            ensureIndicesCreated()

            debug("indexing " + user)
            val popularityScore = user.getCounter.get("popularity") * 0.1
            debug("popularity score: " + popularityScore)

            val department = if (!user.department.trim.isEmpty) {
                user.department
            } else {
                "No Department"
            }
            val jobTitle = if (!user.title.trim.isEmpty) {
                user.title
            } else {
                "No Job Title"
            }

            client.prepareIndex("digaku2", "user", user.getId.toString)
                .setSource(jsonBuilder()
                .startObject()
                .field("vid", user.getId.toString)
                .field("name", user.name)
                .field("fullName", user.fullName)
                .field("joinTime", user.joinTime)
                .field("inactive", user.isInactive)
                .field("locked", user.isLocked)
                .field("popularity", popularityScore)
                .field("jobTitle", jobTitle)
                .field("department", department)
                .field("rank", user.getRank.map(_.name).getOrElse("Undefined"))
                .field("joinedForumIds", user.getJoinedForumIds.toList.map(_._1.toString).mkString(" "))
                .field("moderatedForumIds", user.getModeratedForumIds.toList.map(_._1.toString).mkString(" "))
                .field("isAgent", user.isAgentTopic)
//                .field("abilities", user.abilities.mkString(" "))
                .endObject()
            ).execute()
                .actionGet()

            lastIndexed = user.toString

    }


    def indexForum:PF = {
        case ch:Forum =>
            ensureIndicesCreated()

            info("indexing " + ch)
            client.prepareIndex("digaku3", "forum", ch.getId.toString)
                .setSource(jsonBuilder()
                .startObject()
                .field("vid", ch.getId.toString)
                .field("name", ch.name)
                .field("desc", ch.desc)
                .field("blocked", ch.blocked)
                .field("privated", ch.privated)
                .field("deleted", ch.deleted)
                .field("articleCount", ch.getArticleCount)
                .field("contentCount", ch.getContentCount)
                .field("hasArticleFeature", ch.hasFeature(ForumFeatures.ARTICLE))
                .endObject()
            ).execute()
                .actionGet()

            lastIndexed = ch.toString
    }

    def indexEvent:PF = {
        case event:Event =>
            ensureIndicesCreated()

            info("indexing " + event)
            client.prepareIndex("digaku1", "event", event.getId.toString)
                .setSource(jsonBuilder()
                .startObject()
                .field("vid", event.getId.toString)
                .field("title", event.title)
                .field("content", event.content)
                .field("originIsPrivate", event.originIsPrivate)
                .field("originId", event.origin.getId)
                .field("originName", event.origin.getName)
                .endObject()
            ).execute()
                .actionGet()

            lastIndexed = event.toString
    }

//    def indexPicture = {
//        case pic:Picture =>
//            ensureIndicesCreated()
//
//            info("indexing " + pic)
//            client.prepareIndex("digaku1", "picture", pic.getId.toString)
//                .setSource(jsonBuilder()
//                .startObject()
//                .field("vid", pic.getId.toString)
//                .field("title", pic.title)
//                .field("originIsPrivate", pic.originIsPrivate)
//                .field("originId", pic.origin.getId)
//                .field("originName", pic.origin.getName)
//                .field("tags", pic.tags)
//                .field("blocked", pic.blocked)
//                .field("closed", pic.closed)
//                .endObject()
//            ).execute()
//                .actionGet()
//
//            lastIndexed = pic.toString
//    }

//    /**
//     * index simple post
//     * @return
//     */
//    def indexSimplePost = {
//        case simplePost:SimplePost =>
//            ensureIndicesCreated()
//
//            info("indexing " + simplePost)
//
//            if (simplePost.origin != null){
//                client.prepareIndex("digaku7", "simple-post", simplePost.getId.toString)
//                    .setSource(jsonBuilder()
//                    .startObject()
//                    .field("vid", simplePost.getId.toString)
//                    .field("content", simplePost.content)
//                    .field("originIsPrivate", simplePost.originIsPrivate)
//                    .field("originId", simplePost.origin.getId)
//                    .field("originName", simplePost.origin.getName)
//                    .field("containsVideoLink", simplePost.containsVideoLink)
//                    .field("blocked", simplePost.blocked)
//                    .field("closed", simplePost.closed)
//                    .field("tags", simplePost.tags)
//                    .field("containsPic", simplePost.containsPic)
//                    .field("containsLink", simplePost.containsLink)
//                    .endObject()
//                ).execute()
//                    .actionGet()
//
//                lastIndexed = simplePost.toString
//            }else{
//                warn("object %s origin is null".format(simplePost))
//            }
//
//
//    }
//
//
//    /**
//     * Index deal.
//     * @return
//     */
//    def indexDeal = {
//        case deal:Deal =>
//            ensureIndicesCreated()
//
//            info("indexing " + deal)
//
//            client.prepareIndex("digaku1", "deal", deal.getId.toString)
//                .setSource(jsonBuilder()
//                .startObject()
//                .field("vid", deal.getId.toString)
//                .field("name", deal.name)
//                .field("desc", deal.desc)
//                .field("originIsPrivate", deal.originIsPrivate)
//                .field("originId", deal.origin.getId)
//                .field("originName", deal.origin.getName)
//                .endObject()
//            ).execute()
//                .actionGet()
//
//            lastIndexed = deal.toString
//    }
//
//    /**
//     * Index Ad.
//     * @return
//     */
//    def indexAd = {
//        case ad:Advertisement =>
//            ensureIndicesCreated()
//
//            info("indexing " + ad)
//
//            client.prepareIndex("digaku4", "ads", ad.getId.toString)
//                .setSource(jsonBuilder()
//                .startObject()
//                .field("vid", ad.getId.toString)
//                .field("title", ad.title)
//                .field("tags", ad.tagStr)
//                .endObject()
//            ).execute()
//                .actionGet()
//
//            lastIndexed = ad.toString
//    }
//
//    /**
//     * Index sponsor post
//     * @see [[com.ansvia.digaku.model.SponsorPost]].
//     * @return
//     */
//    def indexSponsorPost = {
//        case sp:SponsorPost =>
//            ensureIndicesCreated()
//
//            info("indexing " + sp)
//
//            client.prepareIndex("digaku8", "ads_sp", sp.getId.toString)
//                .setSource(jsonBuilder()
//                .startObject()
//                .field("vid", sp.getId.toString)
//                .field("name", sp.name)
//                .field("content", sp.content)
//                .field("minAge", sp.minAge)
//                .field("maxAge", sp.maxAge)
//                .field("startTime", sp.startTime)
//                .field("endsTime", sp.endsTime)
//                .field("genderTarget", sp.genderTarget)
//                .field("targetCountry", sp.targetCountry)
//                .field("targetProvince", sp.targetProvince)
//                .field("targetCity", sp.targetCity)
//                .field("tags", sp.tags)
//                .field("showTimeStart", sp.showTimeStart)
//                .field("showTimeEnds", sp.showTimeEnds)
//                .field("closed", sp.isClosed)
//                .endObject()
//            ).execute()
//                .actionGet()
//
//            lastIndexed = sp.toString
//    }

//    def indexPrivateMessage = {
//        case pm:PrivateMessage =>
//            ensureIndicesCreated()
//
//            info("indexing " + pm)
//
//            val participants = pm.getParticipants.map( u => u.getId.toString +  " " + u.lowerName)
//                .reduceLeftOption(_ + " " + _).getOrElse("") + " " + pm.creator.lowerName
//
//            client.prepareIndex("digaku5", "pm", pm.getId.toString)
//                .setSource(jsonBuilder()
//                .startObject()
//                .field("vid", pm.getId.toString)
//                .field("message", pm.content)
//                .field("from", pm.creator.name)
//                .field("participants", participants)
//                .endObject()
//            ).execute()
//                .actionGet()
//
//            lastIndexed = pm.toString
//    }


    /**
     * Digunakan untuk meng-index App.
     * @see [[com.ansvia.digaku.model.App]]
     * @return
     */
    def indexApp:PF = {
        case app:App => {
            ensureIndicesCreated()

            info("indexing " + app)

            client.prepareIndex("digaku6", "app", app.getId.toString)
                .setSource(

                jsonBuilder().startObject()
                    .field("vid", app.getId.toString)
                    .field("name", app.name)
                    .field("desc", app.desc)
                .endObject()

            ).execute()
                .actionGet()

            lastIndexed = app.toString
        }
    }

    /**
     * Index user group
     * @return
     */
    def indexUserGroup = {
        case userGroup:UserGroup =>
            ensureIndicesCreated()

            info("indexing " + userGroup)

            val postPermissionUserIds = userGroup.getUserCanPostAsGroup().mkString(" ")

            client.prepareIndex("digaku11", "user-group", userGroup.getId.toString)
                .setSource(
                    jsonBuilder()
                        .startObject()
                        .field("vid", userGroup.getId.toString)
                        .field("name", userGroup.name)
                        .field("lowerName", userGroup.getLowerName)
                        .field("memberCount", userGroup.getMemberCount())
                        .field("members", userGroup.getAllMemberIds.mkString(" "))
                        .field("postPermissionUserIds", postPermissionUserIds)
                        .endObject()
                ).execute().actionGet()

            lastIndexed = userGroup.toString
    }


    /**
     * Index untuk model FAQ
     * @return
     */
    def indexFaq = {
        case faq:FAQ =>
            ensureIndicesCreated()

            info("indexing " + faq)

            client.prepareIndex("digaku12", "faq", faq.getId.toString)
                .setSource(
                    jsonBuilder()
                        .startObject()
                        .field("vid", faq.getId.toString)
                        .field("title", faq.title)
                        .field("content", faq.content)
                        .field("topicId", faq.topic.map(_.getId).getOrElse(0L))
                        .field("topicIsArchived", faq.topic.exists(_.archived))
                        .field("creationTime", new Date(faq.creationTime))
                        .endObject()
                ).execute().actionGet()

            lastIndexed = faq.toString
    }

    /**
     * Index untuk model Topic
     * @return
     */
    def indexTopic = {
        case topic:Topic =>
            ensureIndicesCreated()

            info("indexing " + topic)

            client.prepareIndex("digaku12", "topic", topic.getId.toString)
                .setSource(
                    jsonBuilder()
                        .startObject()
                        .field("vid", topic.getId.toString)
                        .field("name", topic.name)
                        .field("creationTime", new Date(topic.creationTime))
                        .field("archived", topic.archived)
                        .endObject()
                ).execute().actionGet()

            lastIndexed = topic.toString
    }

    def reindex(mode:Int){
        // non-blocking
        worker ! Start(mode)

        // blocking
//        SearchEngineIndexWorker.start(this)

    }


    def reindexModels(models:String*){
        worker ! IndexModels(models.toList)
    }

    override def reset(){
        // reset state
        SearchEngineIndexWorker.resetState()
        // clear unindexed dli registry
        unindexedDLI.reset()
        try {
            val rv = client.admin().indices().delete(new DeleteIndexRequest(
                indexNames.map(_._1): _*)).actionGet()

            if (!rv.isAcknowledged){
                error("search index deletion failed")
            }
        }catch{
            case e:IndexMissingException =>
                error(e.getMessage)
        }finally{
            checkIndicesEnsured = false
            ensureIndicesCreated()
        }
    }


    override def stopIndexing(){
        worker ! Stop()
    }

    def indexingStatus:Status = {

        implicit val tm:Timeout = Timeout(Duration(5, TimeUnit.MINUTES))

        val status = try {
            Await.result(worker ? GetStatus(), tm.duration).asInstanceOf[Status]
        }catch{
            case e:Exception =>
                error(e.getMessage)
                e.printStackTrace()
                val rv = Status(0L, 0L, "unknown: " + e.getMessage)
                rv.lastIndexed = "unknown: " + e.getMessage
                rv.inProcessing = true
                rv
        }

        status.lastIndexed = lastIndexed
        status
        //
        //        status.lastIndexed = lastIndexed
        //        status
    }

    override def close() {
        worker ! PoisonPill
        client.close()
        node.close()
        debug("closed.")
    }

    /************************************************
      * SEARCH SECTION
      ***********************************************/

    /**
     * Search all.
     * For specific search use:
     * [[com.ansvia.digaku.se.DigakuSearchEngine.searchArticle()]]
     * [[com.ansvia.digaku.se.DigakuSearchEngine.searchUser]] etc.
     *
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @param includePrivate return include private object also.
     * @param scope return only in scope of origin.
     * @return
     */
    def search(query:String, offset: Int, limit: Int,
               includePrivate:Boolean=false,
               scope:Option[Origin[GraphType]]=None,
               searcher:Option[User]=None) = {

        ensureIndicesCreated()

        SearchAllResult(
            searchHighlighterArticle(query, offset, limit, includeAllPrivate = includePrivate, scope = scope, searcher = searcher),
            searchUser(query, offset, limit),
            searchForum(query, offset, limit, includePrivate, searcher),
            // Pada mc2 event merupakan embeded object dari post jadi sudah termasuk dalam searchHighlighterArticle
            getEmpty[Event],
            // Pada mc2 all search tidak support untuk search Apps
            getEmpty[App]
        )
    }

    /**
     * Search post.
     * For specific search use:
     * [[com.ansvia.digaku.se.DigakuSearchEngine.searchArticle()]]
     * [[com.ansvia.digaku.se.DigakuSearchEngine.searchUser]]
     *
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchPost(query:String, offset: Int, limit: Int,
                   includePrivate:Boolean=false,
                   scope:Option[Origin[GraphType]]=None) = {
        SearchPostResult(
            searchArticle(query, offset, limit, includePrivate, scope),
            searchEvent(query, offset, limit, includePrivate, scope)
//            searchPicture(query, offset, limit, includePrivate, scope)
        )
    }


    // override this as you wish
    protected def customArticleQuery(q: BoolQueryBuilder, query:String) = q

    /**
     * Search article
     * @param query term query to search.
     * @param offset starting offset.
     * @param limit ends limit.
     * @param includeFromPrivateOrigin whether to search also from private origin (default false)
     * @param scope origin scope, whether to search scope limited to origin.
     *              used in local forum search.
     * @param searcher user who search.
     * @return
     */
    def searchArticle(query: String, offset: Int, limit: Int,
                      includeFromPrivateOrigin:Boolean=false,
                      scope:Option[Origin[GraphType]]=None,
                      searcher:Option[User] = None) = {

        ensureIndicesCreated()

        val mainQuery = query.trim

        if (mainQuery.length < 1)
            getEmpty[Article]
        else {

            val q = {
                val q1 = QueryBuilders.boolQuery()
                    .minimumNumberShouldMatch(1)
                    .must(QueryBuilders.termQuery("deleted", false))
                    .should(QueryBuilders.commonTerms("title", mainQuery).boost(2.0F))
                    .should(QueryBuilders.commonTerms("tags", mainQuery).boost(1.5F))
                    .should(QueryBuilders.commonTerms("content", mainQuery).boost(0.2F))
                    .must(QueryBuilders.termQuery("originIsDeleted", false))

                if (!includeFromPrivateOrigin){
                    if (searcher.isEmpty){
                        q1.must(QueryBuilders.termQuery("originIsPrivate", false))
                    }
                }

                scope map { origin =>
                    q1.must(QueryBuilders.termQuery("originId", origin.getId.toString))
                }

                customArticleQuery(q1, query)

                // custom scoring

                QueryBuilders.functionScoreQuery(q1)
                    .add(ScoreFunctionBuilders
                        .scriptFunction("(_score * doc['popularity'].value) + (0.08 / ((5.16*pow(10,-11)) * abs(DateTime.now().getMillis() - doc['creationTime'].date.getMillis()) + 0.05)) + 1.0"))
                    .boostMode("sum")
                    .scoreMode("multiply")
            }


            try {


                def doSearch(_offset:Int, _limit:Int, gathered:Int):(List[Article], SearchResponse) = {
                    val resp = client.prepareSearch("digaku1")
                        .setTypes("article")
                        .setSearchType(SearchType.DEFAULT)
                        .addFields("vid")
                        .setQuery(q).setFrom(_offset).setSize(_limit)
                        .execute()
                        .actionGet()

                    //                println("resp: " + resp.toString)

                    // sebagai penanda apabila ada yang terfilter oleh masalah akses
                    var filtered = 0

                    val entries = resp.getHits.iterator().map { hit =>

                        val vid = hit.getFields.get("vid")
                        if (vid != null){
                            val id:IDType = vid.getValue[String].toLong
                            Post.getPostById(id).filter {
                                case article:Article =>
                                    if (article.originIsPrivate && searcher.isDefined){
                                        if (searcher.isEmpty){
                                            false
                                        }else{
                                            article.origin match {
                                                case forum:Forum =>
                                                    val authorized = forum.isMember(searcher.get)
                                                    if (!authorized){
                                                        filtered += 1
                                                    }
                                                    authorized
                                                case _ =>
                                                    false
                                            }
                                        }
                                    }else{
                                        true
                                    }
                                case _ => false
                            }
                        }else {
                            None
                        }

                    }.toList.filter(_.isDefined).map(_.get.asInstanceOf[Article])


                    // if any filtered?
                    // if yes then search more n filtered items
                    if (filtered > 0 && gathered < limit){
                        val (_entries:List[Article], _) = doSearch(_offset + _limit, _limit, entries.length)
                        (entries ++ _entries, resp)
                    }else{
                        (entries, resp)
                    }
                }

                val (entries, resp) = doSearch(offset, limit, 0)

                SpecificSearchResult[Article](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
            }catch{
                case e:IndexMissingException =>
                    getEmpty[Article]
            }
        }
    }

    /**
     * Search article with highlighted content
     * @param query term query to search.
     * @param offset starting offset.
     * @param limit ends limit.
     * @param includeAllPrivate whether to search also from private origin (default false)
     * @param scope origin scope, whether to search scope limited to origin.
     *              used in local forum search.
     * @param threadType Filter thread berdasarkan type thread nya.
     *                   Set `Some("polling")` filter thread yang embed polling
     *                   Set `Some("event")` filter thread yang embed event
     *                   Set `Some("standard")` filter thread yang tidak embed polling atau event
     *                   Set `None` tanpa filter
     * @param label Filter thread yang hanya memiliki label tertentu.
     * @param minRating Filter thread yang memiliki rating lebih dari atau
     *                  sama dengan parameter yang telah ditentukan
     * @param maxRating Filter thread yang memiliki rating kurang dari atau
     *                  sama dengan parameter yang telah ditentukan
     * @param searcher user who search.
     * @return see[[com.ansvia.digaku.se.SpecificSearchResult]]
     */
    def searchHighlighterArticle(query: String, offset: Int, limit: Int,
                             includeAllPrivate:Boolean=false,
                             scope:Option[Origin[GraphType]]=None,
                             threadType:Option[String] = None,
                             label:Option[String] = None,
                             minRating:Option[Double] = None,
                             maxRating:Option[Double] = None,
                             searcher:Option[User] = None,
                             searchAsAdmin:Boolean = true):SpecificSearchResult[HighlightResult[Article]] = {

        ensureIndicesCreated()

        val mainQuery = query.trim

        val stdQuery = {
            val q1 = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("deleted", false))

            if (mainQuery.length > 0) {
                // Title harus mengandung semua kata yang ada pada mainQuery
                q1.must(QueryBuilders.simpleQueryString(mainQuery.replaceAll("\\s+", "+")).field("title")
                    .flags(SimpleQueryStringFlag.AND))
            }

            q1.must(QueryBuilders.termQuery("originIsDeleted", false))

            scope map {
                case f:Forum =>
                    val forumIds = if (f.isSubForum) {
                        f.getId.toString
                    } else {
                        searcher.flatMap { user =>
                            f.getSubForums.toSeq.map(_.getId.toString).reduceLeftOption(_ + " " + _)
                        }.getOrElse(f.getId.toString)
                    }

                    q1.must(QueryBuilders.matchQuery("originId", forumIds))

                case _ =>
                    throw InvalidParameterException("Invalid Origin Type")
            }

            label foreach { l =>
                q1.must(QueryBuilders.matchQuery("label.exact", l.trim))
            }

            threadType foreach {
                case "polling" =>
                    q1.must(QueryBuilders.matchQuery("hasPolling", true))
                case "event" =>
                    q1.must(QueryBuilders.matchQuery("hasEvent", true))
                case "standard" =>
                    q1.must(QueryBuilders.matchQuery("hasEvent", false))
                    q1.must(QueryBuilders.matchQuery("hasPolling", false))
                case _ =>
            }

            val rangeQuery = FilterBuilders.rangeFilter("averageRating")

            if (minRating.isDefined && maxRating.isDefined) {
                val min = scala.math.min(minRating.get, maxRating.get)
                val max = scala.math.max(minRating.get, maxRating.get)

                q1.must(QueryBuilders.filteredQuery(null, rangeQuery.gte(min).lte(max)))

            } else if (minRating.isDefined && maxRating.isEmpty) {
                q1.must(QueryBuilders.filteredQuery(null, rangeQuery.gte(minRating.get).lte(5.0)))
            } else if (minRating.isEmpty && maxRating.isDefined) {
                q1.must(QueryBuilders.filteredQuery(null, rangeQuery.gte(1.0).lte(maxRating.get)))
            }

            if (!includeAllPrivate){
                q1.must(QueryBuilders.termQuery("originIsPrivate", false))
            }

            customArticleQuery(q1, query)
        }

        val prepareSearch = client.prepareSearch("digaku1")
            .setTypes("article")
            .setSearchType(SearchType.DEFAULT)
            .addFields("vid")
            .addFields("content")
            .setQuery(stdQuery)
            .addHighlightedField("content", 100, 1)
            .setHighlighterPreTags("<em>")
            .setHighlighterPostTags("</em>")
            .addSort("creationTime", SortOrder.DESC)


        val resp = {
            // include private forum when searcher not empty and include private is true
            if (searcher.nonEmpty && includeAllPrivate && (!searchAsAdmin ||
                !searcher.exists(u => UserRole.ADMIN == u.role || UserRole.SUPER_ADMIN == u.role))) {

                val user = searcher.get
                val qUser = QueryBuilders.boolQuery()
                    .must(QueryBuilders.matchQuery("vid", user.getId.toString))

                val respUser = client.prepareSearch("digaku2")
                    .setTypes("user")
                    .setSearchType(SearchType.DEFAULT)
                    .addFields("joinedForumIds")
                    .setQuery(qUser)
                    .execute()
                    .actionGet()

                val joinedForumIds = respUser.getHits.iterator().map { hit =>
                    if (hit.getFields.get("joinedForumIds") != null) {
                        hit.getFields.get("joinedForumIds").getValue[java.lang.String]
                    } else
                        ""
                }.toSeq.headOption.getOrElse("")

                val memberFilter = FilterBuilders.orFilter(FilterBuilders.termFilter("originIsPrivate", false),
                    FilterBuilders.queryFilter(QueryBuilders.matchQuery("originId", joinedForumIds)))

                prepareSearch.setPostFilter(memberFilter)
                    .setFrom(offset).setSize(limit)
                    .execute()
                    .actionGet()
            } else {
                prepareSearch.setFrom(offset).setSize(limit)
                    .execute()
                    .actionGet()
            }
        }

        try {
            val entries = resp.getHits.iterator().flatMap { hit =>
                val vid = hit.getFields.get("vid")
                if (vid != null){
                    val id:IDType = vid.getValue[String].toLong
                    Article.getById(id).map { rv =>
                        val highlightContent = Option(hit.highlightFields().get("content")).flatMap { hContent =>
                                hContent.fragments().headOption.map(_.toString)
                            }.getOrElse("")

                        HighlightResult[Article](rv, Map("content" -> highlightContent))
                    }
                } else {
                    None
                }

            }.toList

            SpecificSearchResult[HighlightResult[Article]](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        }catch{
            case e:IndexMissingException =>
                getEmpty[HighlightResult[Article]]
        }
    }

    /**
      * Get list user group id dimana user telah menjadi member
      * @param user user target
      * @param offset
      * @param limit
      * @return
      */
    private def getUserGroupIds(user:User, offset:Int, limit:Int):Seq[Long] = {
        val qUserGroup = QueryBuilders.boolQuery()
            .must(QueryBuilders.termQuery("members", user.getId.toString))

        val resp = client.prepareSearch("digaku11")
            .setTypes("user-group")
            .setSearchType(SearchType.DEFAULT)
            .addFields("vid")
            .setQuery(qUserGroup)
            .setFrom(offset).setSize(limit)
            .execute()
            .actionGet()

        resp.getHits.iterator().map { hit =>
            val vid = hit.getFields.get("vid")
            if (vid != null) {
                vid.getValue[java.lang.String].toLongOr(0L)
            } else
                0L
        }.toSeq.filter(_ > 0)
    }

    /**
     * Search article berdasarkan creator-nya dan presfektif searcher-nya.
     * article di close subforum tidak akan tampil selama
     * searcher tidak join ke close subforum tersebut
     * @param query term query to search.
     * @param creator creator
     * @param searcher user searcher
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchUserArticle(query:String, creator:User, searcher:User, offset:Int, limit:Int):SpecificSearchResult[Article] = {

        ensureIndicesCreated()

        val mainQuery = query.trim

        val creatorIsSearcher = creator.getId == searcher.getId

        val q1 = QueryBuilders.boolQuery()
            .minimumNumberShouldMatch(1)
            .must(QueryBuilders.matchQuery("deleted", false))
            .must(QueryBuilders.matchQuery("creatorId", creator.getId.toString))

        if (mainQuery.nonEmpty) {
            q1.should(QueryBuilders.commonTerms("title", mainQuery).boost(2.0F))
            .should(QueryBuilders.commonTerms("tags", mainQuery).boost(1.5F))
            .should(QueryBuilders.commonTerms("content", mainQuery).boost(0.2F))
            .must(QueryBuilders.termQuery("originIsDeleted", false))
        }

        val prepareSearch = client.prepareSearch("digaku1")
            .setTypes("article")
            .setSearchType(SearchType.DEFAULT)
            .addFields("vid")
            .setQuery(q1)
            .addSort("creationTime", SortOrder.DESC)
            .setFrom(offset).setSize(limit)

        val resp = if (!creatorIsSearcher) {
            val qUser = QueryBuilders.boolQuery()
                .must(QueryBuilders.matchQuery("vid", searcher.getId.toString))

            val respUser = client.prepareSearch("digaku2")
                .setTypes("user")
                .setSearchType(SearchType.DEFAULT)
                .addFields("joinedForumIds")
                .setQuery(qUser)
                .execute()
                .actionGet()

            val joinedForumIds = respUser.getHits.iterator().map { hit =>
                if (hit.getFields.get("joinedForumIds") != null) {
                    hit.getFields.get("joinedForumIds").getValue[java.lang.String]
                } else
                    ""
            }.toSeq.headOption.getOrElse("")

            var memberFilter:FilterBuilder = FilterBuilders.orFilter(FilterBuilders.termFilter("originIsPrivate", false),
                FilterBuilders.queryFilter(QueryBuilders.matchQuery("originId", joinedForumIds)))

            if (!creatorIsSearcher) {
                val searcherIsAdmin = searcher.role == UserRole.SUPER_ADMIN || searcher.role == UserRole.ADMIN

                if (!searcherIsAdmin) {
                    val userGroupIdsBuff = new ListBuffer[Long]

                    userGroupIdsBuff.append(0L)

                    var offset = 0
                    var userGroupIds = getUserGroupIds(searcher, offset, 100)

                    while (userGroupIds.nonEmpty) {
                        userGroupIdsBuff.appendAll(userGroupIds)
                        offset = offset + 100
                        userGroupIds = getUserGroupIds(searcher, offset, 100)
                    }

                    if (userGroupIdsBuff.nonEmpty) {
                        memberFilter = FilterBuilders.andFilter(memberFilter, FilterBuilders.orFilter(FilterBuilders.missingFilter("postAs"),
                            FilterBuilders.queryFilter(QueryBuilders.matchQuery("postAs", userGroupIdsBuff.mkString(" ")))))
                    }
                }
            }

            prepareSearch.setPostFilter(memberFilter)
                .execute()
                .actionGet()

        } else {
            prepareSearch
                .execute()
                .actionGet()
        }

        try {
            val entries = resp.getHits.iterator().flatMap { hit =>
                val vid = hit.getFields.get("vid")
                if (vid != null){
                    val id:IDType = vid.getValue[String].toLong
                    Article.getById(id)
                } else {
                    None
                }

            }.toList

            SpecificSearchResult[Article](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        }catch{
            case e:IndexMissingException =>
                getEmpty[Article]
        }
    }

    /**
     * digunakan untuk search member/moderator pada sebuah forum.
     * @param query query term.
     * @param forumId forum id target untuk search membership
     * @param subForumFlag see [[SubForumStateFlags]]
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchForumMembers(query:String, forumId:Long, subForumFlag:Int, offset:Int, limit:Int): SpecificSearchResult[User] = {
        ensureIndicesCreated()
        try {

            val searchKey = subForumFlag match {
                case SubForumStateFlags.STAFF =>
                    "moderatedForumIds"
                case SubForumStateFlags.JOINED =>
                    "joinedForumIds"
            }

            val q1 = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery(searchKey, forumId.toString))

            if (query.trim.nonEmpty) {
                q1.should(QueryBuilders.matchQuery("name", query.toLowerCase).boost(2.0F))
                    .should(QueryBuilders.prefixQuery("name", query.toLowerCase).boost(1.5F))
                    .should(QueryBuilders.wildcardQuery("name", "*" + query.toLowerCase + "*").boost(1.0F))
                    .should(QueryBuilders.matchQuery("fullName", query.toLowerCase).boost(2.0F))
                    .should(QueryBuilders.wildcardQuery("fullName", "*" + query.toLowerCase + "*").boost(1.5F))
            }
            q1.minimumNumberShouldMatch(1)

            val resp = client.prepareSearch("digaku2")
                .setTypes("user")
                .setSearchType(SearchType.DEFAULT)
                .addFields("vid")
                .setQuery(
                    q1
                ).setFrom(offset).setSize(limit)
                .execute()
                .actionGet()

            val entries = resp.getHits.iterator().flatMap { hit =>

                if (hit.getFields.get("vid") != null){
                    val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong
                    User.getById(id)
                }else
                    None

            }.toSeq

            SpecificSearchResult[User](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        }catch{
            case e:IndexMissingException =>
                getEmpty[User]
        }
    }

    /**
     * Search article with a specific tag
     * @param query term query to search.
     * @param offset starting offset.
     * @param limit ends limit.
     * @param includeFromPrivateOrigin whether to search also from private origin (default false)
     * @param scope origin scope, whether to search scope limited to origin.
     *              used in local forum search.
     * @param searcher user who search.
     * @return
     */
    def searchArticleByTag(query: String, offset: Int, limit: Int,
                      includeFromPrivateOrigin:Boolean=false,
                      scope:Option[Origin[GraphType]]=None,
                      searcher:Option[User] = None) = {

        ensureIndicesCreated()

        val mainQuery = query.trim

        if (mainQuery.length < 1)
            getEmpty[Article]
        else {

            val q = {
                val q1 = QueryBuilders.boolQuery()
                    .minimumNumberShouldMatch(1)
                    .must(QueryBuilders.termQuery("deleted", false))
                    .should(QueryBuilders.matchQuery("tags", mainQuery.toLowerCase).boost(2.0F))
                    .should(QueryBuilders.commonTerms("tags", mainQuery.toLowerCase).boost(1.5F))
                    .should(QueryBuilders.wildcardQuery("tags", "*" + mainQuery.toLowerCase + "*").boost(1.0F))

                if (!includeFromPrivateOrigin){
                    q1.must(QueryBuilders.termQuery("originIsPrivate", false))
                }

                scope map { origin =>
                    q1.must(QueryBuilders.termQuery("originId", origin.getId.toString))
                }

                q1

//                customArticleQuery(q1, query)
//
//                // custom scoring
//
//                QueryBuilders.functionScoreQuery(q1)
//                    .add(ScoreFunctionBuilders
//                        .scriptFunction("(_score * doc['popularity'].value) + (0.08 / ((5.16*pow(10,-11)) * abs(DateTime.now().getMillis() - doc['creationTime'].date.getMillis()) + 0.05)) + 1.0"))
//                    .boostMode("sum")
//                    .scoreMode("multiply")
            }

            val prepareSearch = client.prepareSearch("digaku1")
                .setTypes("article")
                .setSearchType(SearchType.DEFAULT)
                .addFields("vid")
                .setQuery(q)


            val resp = {
                // include private forum when searcher not empty and include private is true
                if (searcher.nonEmpty && includeFromPrivateOrigin &&
                    (!searcher.exists(u => UserRole.ADMIN == u.role || UserRole.SUPER_ADMIN == u.role))
                ) {

                    val user = searcher.get
                    val qUser = QueryBuilders.boolQuery()
                        .must(QueryBuilders.matchQuery("vid", user.getId.toString))

                    val respUser = client.prepareSearch("digaku2")
                        .setTypes("user")
                        .setSearchType(SearchType.DEFAULT)
                        .addFields("joinedForumIds")
                        .setQuery(qUser)
                        .execute()
                        .actionGet()

                    val joinedForumIds = respUser.getHits.iterator().map { hit =>
                        if (hit.getFields.get("joinedForumIds") != null) {
                            hit.getFields.get("joinedForumIds").getValue[java.lang.String]
                        } else
                            ""
                    }.toSeq.headOption.getOrElse("")

                    val memberFilter = FilterBuilders.orFilter(FilterBuilders.termFilter("originIsPrivate", false),
                        FilterBuilders.queryFilter(QueryBuilders.matchQuery("originId", joinedForumIds)))

                    prepareSearch.setPostFilter(memberFilter)
                        .setFrom(offset).setSize(limit)
                        .execute()
                        .actionGet()
                } else {
                    prepareSearch.setFrom(offset).setSize(limit)
                        .execute()
                        .actionGet()
                }
            }


            try {

                val entries = resp.getHits.iterator().flatMap { hit =>
                    val vid = hit.getFields.get("vid")
                    if (vid != null){
                        val id:IDType = vid.getValue[String].toLong
                        Article.getById(id)
                    } else {
                        None
                    }

                }.toSeq

                SpecificSearchResult[Article](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
            }catch{
                case e:IndexMissingException =>
                    getEmpty[Article]
            }
        }
    }

    /**
     * search blocked article
     * @param query term query to search.
     * @param offset
     * @param limit
     * @return
     */
    def searchBlockedArticle(query: String, offset: Int, limit: Int) = {

        ensureIndicesCreated()

        val mainQuery = query.trim

        val q = {
            QueryBuilders.boolQuery()
                .minimumNumberShouldMatch(1)
                .should(QueryBuilders.commonTerms("title", mainQuery))
                .must(QueryBuilders.termQuery("blocked", true))
        }

        try {
            val resp = client.prepareSearch("digaku1")
                .setTypes("article")
                .setSearchType(SearchType.DEFAULT)
                .setQuery(q).setFrom(offset).setSize(limit)
                .execute()
                .actionGet()

            val entries = resp.getHits.iterator().map { hit =>

                if (hit.getSource.get("vid") != null){
                    val id:Long = hit.getSource.get("vid").toString.toLong
                    Post.getPostById(id)
                }else
                    None

            }.filter(_.isDefined).map(_.get.asInstanceOf[Article]).toSeq

            SpecificSearchResult[Article](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        }catch{
            case e:IndexMissingException =>
                getEmpty[Article]
        }

    }

    /**
     * Search closed article
     * @param query term query to search.
     * @param originId forum id
     * @param offset
     * @param limit
     * @return
     */
    def searchClosedArticle(query:String, originId:Long, offset:Int, limit:Int) = {
        ensureIndicesCreated()

        val spQuery = query.toLowerCase.split("\\W+")

        val mainQuery = spQuery(0)

        val q = {
            val bq = QueryBuilders.boolQuery()
                .minimumNumberShouldMatch(1)

            if (mainQuery.trim.length > 0) {
                bq.should(QueryBuilders.prefixQuery("title", mainQuery))
            }

            bq.must(QueryBuilders.termQuery("closed", true))
                .must(QueryBuilders.termQuery("originId", originId))

            if(spQuery.length > 1) {
                val spQueryToken = spQuery.toIterator
                spQueryToken.next()
                while(spQueryToken.hasNext) {
                    bq.should(QueryBuilders.prefixQuery("title", spQueryToken.next()).boost(1.1F))
                }
            }
            bq
        }

        try {
            val resp = client.prepareSearch("digaku1")
                .setTypes("article")
                .setSearchType(SearchType.DEFAULT)
                .setQuery(q).setFrom(offset).setSize(limit)
                .execute()
                .actionGet()

            val entries = resp.getHits.iterator().map { hit =>

                if (hit.getSource.get("vid") != null) {
                    val id:Long = hit.getSource.get("vid").toString.toLong
                    Post.getPostById(id)
                } else
                    None

            }.filter(_.isDefined).map(_.get.asInstanceOf[Article]).toSeq

            SpecificSearchResult[Article](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        } catch {
            case e:IndexMissingException =>
                getEmpty[Article]
        }

    }

    /**
     * Search user.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @param jobTitle Filter User yang memiliki job title sesuai dengan parameter yang ditentukan
     *                 Set `None` ketika tidak ingin di filter berdasarkan job title.
     * @param department Filter User yang memiliki department sesai dengan parameter yang diberikan.
     *                   Set `None` ketika tidak ingin di filter berdasarkan department.
     * @param rank Filter User yang memiliki rank sesuai dengan parameter yang ditentukan
     *              Set `None` ketika tidak ingin di filter berdasarkan rank.
     * @param locked include user locked or not.
     * @param inactive include user deactivated or not.
     * @return
     */
    def searchUser(query: String, offset: Int = 0, limit: Int = 10,
                   jobTitle:Option[String] = None, department:Option[String] = None,
                   rank:Option[String] = None, locked:Boolean = false,
                   inactive:Boolean = false) = {

        ensureIndicesCreated()

        try {
            val queryL = query.trim.toLowerCase

            val q1 = QueryBuilders.boolQuery()
                .must(QueryBuilders.matchQuery("inactive", inactive))

            if (queryL.length > 0) {
                q1.should(QueryBuilders.matchQuery("name", queryL).boost(2.0F))
                    // support phrase prefix misal pencarian: "maria sa" untuk mencari "maria salsa"
                    .should(QueryBuilders.matchPhrasePrefixQuery("fullName", queryL).boost(2.5F))
                    // supaya support pencarian first name dan prefix last name tanpa menggunakan middle name,
                    // misal: "maria ana" untuk mencari "maria salsa ananta"
                    .should(QueryBuilders.simpleQueryString(queryL.replaceAll("\\s+", "+") + "*").field("fullName", 1.5F)
                    .flags(SimpleQueryStringFlag.AND, SimpleQueryStringFlag.PREFIX))
                    .minimumNumberShouldMatch(1)
            }


            if (locked)
                q1.must(QueryBuilders.termQuery("locked", locked))

            jobTitle.foreach(rv => q1.must(QueryBuilders.matchQuery("jobTitle.exact", rv)))
            department.foreach(rv => q1.must(QueryBuilders.matchQuery("department.exact", rv)))
            rank.foreach(rv => q1.must(QueryBuilders.matchQuery("rank.exact", rv)))

            // custom scoring
            val q2 =
                QueryBuilders.functionScoreQuery(q1)
                    .add(ScoreFunctionBuilders.scriptFunction("_score * doc['popularity'].value"))
                    .boostMode("sum")
                    .scoreMode("multiply")


//            println(q2.toString)

            val resp = client.prepareSearch("digaku2")
                .setTypes("user")
                .setSearchType(SearchType.DEFAULT)
                .addFields("vid")
                .setQuery(
                    q2
                ).setFrom(offset).setSize(limit)
                .execute()
                .actionGet()

            val entries = resp.getHits.iterator().flatMap { hit =>

                if (hit.getFields.get("vid") != null){
                    val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong
                    User.getById(id)
                }else
                    None

            }.toSeq

//            println("resp: " + resp.toString)

            SpecificSearchResult[User](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        }catch{
            case e:IndexMissingException =>
                getEmpty[User]
        }

    }

    /**
      * Search user agent.
      * @param query query term
      * @param offset starting offset.
      * @param limit end limit.
      * @return
      */
    def searchUserAgent(query: String, offset: Int = 0, limit: Int = 10) = {

        ensureIndicesCreated()

        try {
            val queryL = query.trim.toLowerCase

            val q1 = QueryBuilders.boolQuery()
                .must(QueryBuilders.matchQuery("isAgent", true))

            if (queryL.length > 0) {
                q1.should(QueryBuilders.matchQuery("name", queryL).boost(2.0F))
                    // support phrase prefix misal pencarian: "maria sa" untuk mencari "maria salsa"
                    .should(QueryBuilders.matchPhrasePrefixQuery("fullName", queryL).boost(2.5F))
                    // supaya support pencarian first name dan prefix last name tanpa menggunakan middle name,
                    // misal: "maria ana" untuk mencari "maria salsa ananta"
                    .should(QueryBuilders.simpleQueryString(queryL.replaceAll("\\s+", "+") + "*").field("fullName", 1.5F)
                    .flags(SimpleQueryStringFlag.AND, SimpleQueryStringFlag.PREFIX))
                    .minimumNumberShouldMatch(1)
            }

            // custom scoring
            val q2 =
                QueryBuilders.functionScoreQuery(q1)
                    .add(ScoreFunctionBuilders.scriptFunction("_score * doc['popularity'].value"))
                    .boostMode("sum")
                    .scoreMode("multiply")


            //            println(q2.toString)

            val resp = client.prepareSearch("digaku2")
                .setTypes("user")
                .setSearchType(SearchType.DEFAULT)
                .addFields("vid")
                .setQuery(
                    q2
                ).setFrom(offset).setSize(limit)
                .execute()
                .actionGet()

            val entries = resp.getHits.iterator().flatMap { hit =>

                if (hit.getFields.get("vid") != null){
                    val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong
                    User.getById(id)
                }else
                    None

            }.toSeq

            SpecificSearchResult[User](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        }catch{
            case e:IndexMissingException =>
                getEmpty[User]
        }

    }


//    private lazy val vidsCache = Cache.steady[String,Seq[String]]("vids_cache",1000,1,TimeUnit.DAYS){ userId =>
//
//
//        User.getById(userId.toLong).map { u =>
//
////            println("loading vids for " + u + "...")
//
//            u.getVertex.pipe.outE(JOIN).iterator().map(_.getOrElse("targetId",0L))
//                .toSeq.filterNot(_ == 0L).map(_.toString)
//
//        }.getOrElse(Seq.empty[String]).toSeq
//    }

    /**
     * Search forum.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @param searcher referensi ke user / siapa yang melakukan search
     *                 ini penting apabila kita ingin melakukan search ke forum-forum
     *                 dengan akses tertentu yang hanya dipegang oleh searcher.
     * @return
     */
    def searchForum(query: String, offset: Int, limit: Int,
                    includeAllPrivate:Boolean=false, searcher:Option[User]=None,
                       onlyHasArticleFeature:Boolean = false, withDeleted:Boolean = false, searchAsAdmin:Boolean = true) = {

        ensureIndicesCreated()

        try {

            val stdQuery = QueryBuilders.boolQuery()
                .minimumNumberShouldMatch(1)
                .should(QueryBuilders.matchQuery("name", query.toLowerCase).boost(3.0F))
                .should(QueryBuilders.matchPhrasePrefixQuery("name", query.toLowerCase).boost(2.5F))
                .should(QueryBuilders.prefixQuery("name", query.toLowerCase).boost(2.0F))
                .should(QueryBuilders.prefixQuery("desc", query.toLowerCase).boost(1.0F))

            if (!withDeleted) {
                stdQuery.must(QueryBuilders.termQuery("deleted", false))
            }

            if (onlyHasArticleFeature) {
                stdQuery.must(QueryBuilders.termQuery("hasArticleFeature", true))
            }

            val resp = {
                // include private forum when searcher not empty and include private is true
                if (searcher.nonEmpty && includeAllPrivate && (!searchAsAdmin ||
                    !searcher.exists(u => UserRole.ADMIN == u.role || UserRole.SUPER_ADMIN == u.role))) {

                    val user = searcher.get
                    val qUser = QueryBuilders.boolQuery()
                        .must(QueryBuilders.matchQuery("vid", user.getId.toString))

                    val respUser = client.prepareSearch("digaku2")
                        .setTypes("user")
                        .setSearchType(SearchType.DEFAULT)
                        .addFields("joinedForumIds")
                        .setQuery(qUser)
                        .execute()
                        .actionGet()

                    val joinedForumIds = respUser.getHits.iterator().map { hit =>
                        if (hit.getFields.get("joinedForumIds") != null) {
                            hit.getFields.get("joinedForumIds").getValue[java.lang.String]
                        } else
                            ""
                    }.toSeq.headOption.getOrElse("")

                    val memberFilter = FilterBuilders.orFilter(FilterBuilders.termFilter("privated", false),
                        FilterBuilders.queryFilter(QueryBuilders.matchQuery("vid", joinedForumIds)))

                    client.prepareSearch("digaku3")
                        .setTypes("forum")
                        .setSearchType(SearchType.DEFAULT)
                        .addFields("vid")
                        .setQuery(stdQuery)
                        .setPostFilter(memberFilter)
                        .setFrom(offset).setSize(limit)
                        .execute()
                        .actionGet()
                } else {

                    // filter privated with false value when not include private forum
                    if (!includeAllPrivate) {
                        stdQuery.must(QueryBuilders.termQuery("privated", false))
                    }

                    client.prepareSearch("digaku3")
                        .setTypes("forum")
                        .setSearchType(SearchType.DEFAULT)
                        .addFields("vid")
                        .setQuery(stdQuery)
                        .setFrom(offset).setSize(limit)
                        .execute()
                        .actionGet()
                }
            }

            val entries = resp.getHits.iterator().flatMap { hit =>
                if (hit.getFields.get("vid") != null) {
                    val id: IDType = hit.getFields.get("vid").getValue[java.lang.String].toLong
                    Forum.getById(id)
                } else {
                    None
                }
            }.toList

            SpecificSearchResult[Forum](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        }catch{
            case e:IndexMissingException =>
                getEmpty[Forum]
        }


    }


    /**
     * Advance search forum.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @param searcher referensi ke user / siapa yang melakukan search
     *                 ini penting apabila kita ingin melakukan search ke forum-forum
     *                 dengan akses tertentu yang hanya dipegang oleh searcher.
     * @param isPrivate Set `Some(true)` ketika hanya ingin mendapatkan forum yang private
     *                  Set `Some(false)` ketika hanya ingin mendapatkan forum yang public
     *                  Set `None` ketika ingin mengambil yang public atau private
     * @param parentForum Filter untuk hanya mengambil SubForum yang berada di forum tertentu
     * @param minThreadCount Filter forum yang memiliki jumlah thread lebih dari atau sama dengan minThreadCount
     * @param maxThreadCount Filter forum yang memiliki jumlah thread kurang dari atau sama dengan minThreadCount
     * @param hasArticleFeature Set `Some(true)` ketika ingin filter forum yang memiliki fitur article(Filter hanya SubForum)
     *                          Set `Some(false)` ketika ingin filter forum yang memiliki tidak memiliki fitur article(parent SubForum)
     *                          Set `None` ketika tidak ingin filter atau mendapatkan parent forum dan SubForum nya
     * @param searchAsAdmin Set `true` ketika ingin mendapatkan forum sebagai user admin atau mendapatkan semua forum baik yang
     *                          public atau semua forum yang private meskipun searcher tidak join ke forum private tersebut
     * @return
     */
    def advanceSearchForum(query: String, offset: Int, limit: Int,
                    isPrivate:Option[Boolean] = None, searcher:Option[User]=None,
                    parentForum:Option[Forum] = None, minThreadCount:Option[Int] = None,
                    maxThreadCount:Option[Int] = None, hasArticleFeature:Option[Boolean] = Some(false),
                    searchAsAdmin:Boolean = true) = {

        ensureIndicesCreated()

        try {

            val stdQuery = QueryBuilders.boolQuery()
                .minimumNumberShouldMatch(1)
                .should(QueryBuilders.matchQuery("name", query.toLowerCase).boost(3.0F))
                .should(QueryBuilders.matchPhrasePrefixQuery("name", query.toLowerCase).boost(2.5F))
                .should(QueryBuilders.prefixQuery("name", query.toLowerCase).boost(2.0F))
                .should(QueryBuilders.prefixQuery("desc", query.toLowerCase).boost(1.0F))
                .must(QueryBuilders.matchQuery("deleted", false))

            hasArticleFeature.foreach { rv =>
                stdQuery.must(QueryBuilders.termQuery("hasArticleFeature", rv))
            }

            parentForum.find(!_.isSubForum).foreach { f =>
                val forumIds = f.getSubForums.toSeq.map(_.getId).mkString(" ")

                stdQuery.must(QueryBuilders.matchQuery("vid", forumIds))
            }

            val rangeQuery = FilterBuilders.rangeFilter("articleCount")

            if (minThreadCount.isDefined && maxThreadCount.isDefined) {
                val min = scala.math.min(minThreadCount.get, maxThreadCount.get)
                val max = scala.math.max(minThreadCount.get, maxThreadCount.get)

                if (min != max) {
                    stdQuery.must(QueryBuilders.filteredQuery(null, rangeQuery.gte(min).lte(max)))
                } else {
                    stdQuery.must(QueryBuilders.matchQuery("articleCount", min))
                }
            } else if (minThreadCount.isDefined && maxThreadCount.isEmpty) {
                stdQuery.must(QueryBuilders.filteredQuery(null, rangeQuery.gte(minThreadCount.get)))
            } else if (minThreadCount.isEmpty && maxThreadCount.isDefined) {
                stdQuery.must(QueryBuilders.filteredQuery(null, rangeQuery.lte(maxThreadCount.get)))
            }

            val resp = {
                // include private forum when searcher not empty and include private is true
                if (searcher.nonEmpty && (!searchAsAdmin ||
                    !searcher.exists(u => UserRole.ADMIN == u.role || UserRole.SUPER_ADMIN == u.role))) {

                    val user = searcher.get
                    val qUser = QueryBuilders.boolQuery()
                        .must(QueryBuilders.matchQuery("vid", user.getId.toString))

                    val respUser = client.prepareSearch("digaku2")
                        .setTypes("user")
                        .setSearchType(SearchType.DEFAULT)
                        .addFields("joinedForumIds")
                        .setQuery(qUser)
                        .execute()
                        .actionGet()

                    val joinedForumIds = respUser.getHits.iterator().map { hit =>
                        if (hit.getFields.get("joinedForumIds") != null) {
                            hit.getFields.get("joinedForumIds").getValue[java.lang.String]
                        } else
                            ""
                    }.toSeq.headOption.getOrElse("")

                    val respInternal = client.prepareSearch("digaku3")
                        .setTypes("forum")
                        .setSearchType(SearchType.DEFAULT)
                        .addFields("vid")

                    isPrivate.map { rv =>
                        if (rv) {
                            val memberFilter = FilterBuilders.andFilter(FilterBuilders.termFilter("privated", rv),
                                FilterBuilders.queryFilter(QueryBuilders.matchQuery("vid", joinedForumIds)))

                            respInternal.setPostFilter(memberFilter)
                        } else {
                            stdQuery.must(QueryBuilders.termQuery("privated", rv))
                        }
                    }.getOrElse {
                        val memberFilter = FilterBuilders.orFilter(FilterBuilders.termFilter("privated", false),
                            FilterBuilders.queryFilter(QueryBuilders.matchQuery("vid", joinedForumIds)))

                        respInternal.setPostFilter(memberFilter)
                    }

                    respInternal.setQuery(stdQuery)
                        .setFrom(offset).setSize(limit)
                        .execute()
                        .actionGet()
                } else {

                    // filter privated with false value when not include private forum
                    isPrivate.map { rv =>
                        stdQuery.must(QueryBuilders.termQuery("privated", rv))
                    }

                    client.prepareSearch("digaku3")
                        .setTypes("forum")
                        .setSearchType(SearchType.DEFAULT)
                        .addFields("vid")
                        .setQuery(stdQuery)
                        .setFrom(offset).setSize(limit)
                        .execute()
                        .actionGet()
                }
            }

            val entries = resp.getHits.iterator().flatMap { hit =>
                if (hit.getFields.get("vid") != null) {
                    val id: IDType = hit.getFields.get("vid").getValue[java.lang.String].toLong
                    Forum.getById(id)
                } else {
                    None
                }
            }.toList

            SpecificSearchResult[Forum](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        }catch{
            case e:IndexMissingException =>
                getEmpty[Forum]
        }
    }

    /**
     * search blocked forum
     * @param query term query to search.
     * @param offset
     * @param limit
     * @return
     */
    def searchBlockedChannel(query: String, offset: Int, limit: Int) = {
        ensureIndicesCreated()

        try {

            val q = QueryBuilders.boolQuery()
                .minimumNumberShouldMatch(1)
                .should(QueryBuilders.commonTerms("name", query.toLowerCase).boost(2.0F))
                .must(QueryBuilders.termQuery("blocked", true))

            val resp = client.prepareSearch("digaku3")
                .setTypes("forum")
                .setSearchType(SearchType.DEFAULT)
                .addFields("vid")
                .setQuery(q).setFrom(offset).setSize(limit)
                .execute()
                .actionGet()

            val entries = resp.getHits.iterator().flatMap { hit =>

                if (hit.getFields.get("vid") != null){
                    val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong
                    Forum.getById(id)
                }else
                    None

            }.toSeq

            SpecificSearchResult[Forum](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        }catch{
            case e:IndexMissingException =>
                getEmpty[Forum]
        }
    }

    /**
     * Search event.
     * @param query query term.
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def searchEvent(query: String, offset: Int, limit: Int,
                    includePrivate:Boolean=false,
                    scope:Option[Origin[GraphType]]=None) = {

        ensureIndicesCreated()

        val spQuery = query.toLowerCase.split("\\W+")

        if (spQuery.length < 1)
            getEmpty[Event]
        else {
            val mainQuery = spQuery(0)
            val q = {
                val bq = QueryBuilders.boolQuery()
                    .minimumNumberShouldMatch(1)
                    .should(QueryBuilders.prefixQuery("title", mainQuery).boost(2.0F))
                    .should(QueryBuilders.prefixQuery("content", mainQuery).boost(1.0F))

                if (!includePrivate)
                    bq.must(QueryBuilders.termQuery("originIsPrivate", false))

                scope map { origin =>
                    bq.must(QueryBuilders.termQuery("originId", origin.getId.toString))
                }


                bq
            }

            try {

                val resp = client.prepareSearch("digaku1")
                    .setTypes("event")
                    .setSearchType(SearchType.DEFAULT)
                    .addFields("vid")
                    .setQuery(q).setFrom(offset).setSize(limit)
                    .execute()
                    .actionGet()

                val entries = resp.getHits.iterator().flatMap { hit =>

                    if (hit.getFields.get("vid") != null){
                        val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong
                        Event.getById(id)
                    }else
                        None

                }.toSeq

                SpecificSearchResult[Event](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
            }catch{
                case e:IndexMissingException =>
                    getEmpty[Event]
            }

        }
    }

    /**
     * Search User Group
     * @param query search term query
     * @param offset starting offset
     * @param limit ends limit
     */
    def searchUserGroup(query:String, offset:Int, limit:Int, postAllowedFor:Option[User] = None, withEmptyUser:Boolean = false) = {
        ensureIndicesCreated()

        try {
            val queryL = query.toLowerCase
            val q1 =
                if (queryL.trim.length > 0) {
                    QueryBuilders.boolQuery()
                        .minimumNumberShouldMatch(1)
                        .should(QueryBuilders.matchQuery("name", queryL).boost(3.0F))
                        .should(QueryBuilders.matchPhrasePrefixQuery("name", queryL).boost(2.5F))
                        .should(QueryBuilders.commonTerms("name", queryL).boost(1.0F))
                        .should(QueryBuilders.wildcardQuery("name", "*" + queryL + "*").boost(1.0F))
                        .should(QueryBuilders.prefixQuery("name", queryL).boost(2.0F))
                } else {
                    QueryBuilders.boolQuery()
                }

            postAllowedFor.foreach { user =>
                q1.must(QueryBuilders.termQuery("postPermissionUserIds", user.getId.toString))
            }

            val q2 = if (!withEmptyUser) {
                QueryBuilders.filteredQuery(q1, FilterBuilders.rangeFilter("memberCount").gt(0))
            } else {
                q1
            }

            val resp = client.prepareSearch("digaku11")
                .setTypes("user-group")
                .setSearchType(SearchType.DEFAULT)
                .addFields("vid")
                .setQuery(q2)
                .setFrom(offset).setSize(limit)
                .execute()
                .actionGet()

            val entries = resp.getHits.iterator().flatMap { hit =>
                if (hit.getFields.get("vid") != null) {
                    val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong

                    tx { t =>
                        val v = t.getVertex(id)
                        if (v != null) {
                            v.toCC[UserGroup]
                        } else {
                            None
                        }
                    }
                } else {
                    None
                }
            }.toSeq

            SpecificSearchResult[UserGroup](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        } catch{
            case e:IndexMissingException =>
                getEmpty[UserGroup]
        }
    }

//    /**
//     * Search picture.
//     * @param query query term.
//     * @param offset starting offset.
//     * @param limit end limit.
//     * @return
//     */
//    def searchPicture(query: String, offset: Int, limit: Int,
//                      includePrivate:Boolean=false,
//                      scope:Option[Origin[GraphType]]=None) = {
//
//        ensureIndicesCreated()
//
//        val spQuery = query.toLowerCase.split("\\W+")
//
//        if (spQuery.length < 1)
//            getEmpty[Picture]
//        else {
//            val mainQuery = spQuery(0)
//
//            val q = {
//                val bq = QueryBuilders.boolQuery()
//                    .minimumNumberShouldMatch(1)
//                    .should(QueryBuilders.commonTerms("tags", mainQuery).boost(2.0F))
//                    .should(QueryBuilders.commonTerms("title", mainQuery).boost(1.0F))
//
//                bq.must(QueryBuilders.termQuery("blocked", false))
//                bq.must(QueryBuilders.termQuery("closed", false))
//
//              /*
//                if(spQuery.length > 1){
//                    val spQueryToken = spQuery.toIterator
//                    spQueryToken.next()
//                    while(spQueryToken.hasNext){
//                        bq.should(QueryBuilders.prefixQuery("title", spQueryToken.next()).boost(1.1F))
//                    }
//                }
//               */
//
//                if (!includePrivate)
//                    bq.must(QueryBuilders.termQuery("originIsPrivate", false))
//
//                scope map { origin =>
//                    bq.must(QueryBuilders.termQuery("originId", origin.getId.toString))
//                }
//
//                bq
//            }
//
//            try {
//
//                val resp = client.prepareSearch("digaku1")
//                    .setTypes("picture")
//                    .setSearchType(SearchType.DEFAULT)
//                    .addFields("vid")
//                    .setQuery(q).setFrom(offset).setSize(limit)
//                    .execute()
//                    .actionGet()
//
//                val entries = resp.getHits.iterator().flatMap { hit =>
//
//                    if (hit.getFields.get("vid") != null){
//                        val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong
//                        Picture.getById(id)
//                    }else
//                        None
//
//                }.toSeq
//
//                SpecificSearchResult[Picture](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
//            }catch{
//                case e:IndexMissingException =>
//                    getEmpty[Picture]
//            }
//        }
//    }
//
//    /**
//     * search blocked picture
//     * @param query term query to search.
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchBlockedPicture(query: String, offset: Int, limit: Int) = {
//        ensureIndicesCreated()
//
//        val spQuery = query.toLowerCase.split("\\W+")
//
//        val mainQuery = spQuery(0)
//
//        val q =
//            QueryBuilders.boolQuery()
//                .minimumNumberShouldMatch(1)
//                .should(QueryBuilders.commonTerms("title", mainQuery).boost(2.0F))
//                .must(QueryBuilders.termQuery("blocked", true))
////
////            if(spQuery.length > 1){
////                val spQueryToken = spQuery.toIterator
////                spQueryToken.next()
////                while(spQueryToken.hasNext){
////                    bq.should(QueryBuilders.prefixQuery("title", spQueryToken.next()).boost(1.1F))
////                }
////            }
//
////            bq
////        }
//
//        try {
//
//            val resp = client.prepareSearch("digaku1")
//                .setTypes("picture")
//                .setSearchType(SearchType.DEFAULT)
//                .addFields("vid")
//                .setQuery(q).setFrom(offset).setSize(limit)
//                .execute()
//                .actionGet()
//
//            val entries = resp.getHits.iterator().flatMap { hit =>
//
//                if (hit.getFields.get("vid") != null){
//                    val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong
//                    Picture.getById(id)
//                }else
//                    None
//
//            }.toSeq
//
//            SpecificSearchResult[Picture](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
//        }catch{
//            case e:IndexMissingException =>
//                getEmpty[Picture]
//        }
//
//    }
//
//    /**
//     * Search closed picture
//     * @param query term query to search.
//     * @param originId forum id
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchClosedPicture(query: String, originId:Long, offset: Int, limit: Int) = {
//        ensureIndicesCreated()
//
//        val spQuery = query.toLowerCase.split("\\W+")
//
//        if (spQuery.length < 1)
//            getEmpty[Picture]
//        else {
//            val mainQuery = spQuery(0)
//
//            val q = {
//                val bq = QueryBuilders.boolQuery()
//                    .minimumNumberShouldMatch(1)
//                    .should(QueryBuilders.prefixQuery("title", mainQuery).boost(2.0F))
//                    .must(QueryBuilders.termQuery("closed", true))
//                    .must(QueryBuilders.termQuery("originId", originId))
//
//                if (spQuery.length > 1) {
//                    val spQueryToken = spQuery.toIterator
//                    spQueryToken.next()
//                    while(spQueryToken.hasNext) {
//                        bq.should(QueryBuilders.prefixQuery("title", spQueryToken.next()).boost(1.1F))
//                    }
//                }
//                bq
//            }
//
//            try {
//
//                val resp = client.prepareSearch("digaku1")
//                    .setTypes("picture")
//                    .setSearchType(SearchType.DEFAULT)
//                    .addFields("vid")
//                    .setQuery(q).setFrom(offset).setSize(limit)
//                    .execute()
//                    .actionGet()
//
//                val entries = resp.getHits.iterator().flatMap { hit =>
//
//                    if (hit.getFields.get("vid") != null) {
//                        val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong
//                        Picture.getById(id)
//                    } else
//                        None
//
//                }.toSeq
//
//                SpecificSearchResult[Picture](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
//            } catch {
//                case e:IndexMissingException =>
//                    getEmpty[Picture]
//            }
//        }
//    }

//    /**
//     * search blocked video
//     * @param query term query to search.
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchBlockedVideo(query: String, offset: Int, limit: Int) = {
//
//        ensureIndicesCreated()
//
//        val spQuery = query.toLowerCase.split("\\W+")
//
//
//        val mainQuery = spQuery(0)
//
//        val q = {
//            val bq = QueryBuilders.boolQuery()
//                .minimumNumberShouldMatch(1)
//                .should(QueryBuilders.commonTerms("content", mainQuery))
//                .must(QueryBuilders.termQuery("containsVideoLink", true))
//                .must(QueryBuilders.termQuery("blocked", true))
//
////            if(spQuery.length > 1){
////                val spQueryToken = spQuery.toIterator
////                spQueryToken.next()
////                while(spQueryToken.hasNext){
////                    bq.should(QueryBuilders.prefixQuery("content", spQueryToken.next()).boost(1.1F))
////                }
////            }
//
//            bq
//        }
//
//        try {
//            val resp = client.prepareSearch("digaku7")
//                .setTypes("simple-post")
//                .setSearchType(SearchType.DEFAULT)
//                .setQuery(q).setFrom(offset).setSize(limit)
//                .execute()
//                .actionGet()
//
//            val entries = resp.getHits.iterator().map { hit =>
//
//                if (hit.getSource.get("vid") != null){
//                    val id:Long = hit.getSource.get("vid").toString.toLong
//                    Post.getPostById(id)
//                }else
//                    None
//
//            }.filter(_.isDefined).map(_.get.asInstanceOf[SimplePost]).toSeq
//
//            SpecificSearchResult[SimplePost](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
//        }catch{
//            case e:IndexMissingException =>
//                getEmpty[SimplePost]
//        }
//
//    }

//    /**
//     * Search closed video
//     * @param query term query to search.
//     * @param originId forum id
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchClosedVideo(query:String, originId:Long, offset:Int, limit:Int) = {
//
//        ensureIndicesCreated()
//
//        val spQuery = query.toLowerCase.split("\\W+")
//
//        if (spQuery.length < 1)
//            getEmpty[SimplePost]
//        else {
//            val mainQuery = spQuery(0)
//
//            val q = {
//                val bq = QueryBuilders.boolQuery()
//                    .minimumNumberShouldMatch(1)
//                    .should(QueryBuilders.prefixQuery("content", mainQuery))
//                    .must(QueryBuilders.termQuery("containsVideoLink", true))
//                    .must(QueryBuilders.termQuery("closed", true))
//                    .must(QueryBuilders.termQuery("originId", originId))
//
//                if (spQuery.length > 1) {
//                    val spQueryToken = spQuery.toIterator
//                    spQueryToken.next()
//                    while(spQueryToken.hasNext) {
//                        bq.should(QueryBuilders.prefixQuery("content", spQueryToken.next()).boost(1.1F))
//                    }
//                }
//                bq
//            }
//
//            try {
//                val resp = client.prepareSearch("digaku7")
//                    .setTypes("simple-post")
//                    .setSearchType(SearchType.DEFAULT)
//                    .setQuery(q).setFrom(offset).setSize(limit)
//                    .execute()
//                    .actionGet()
//
//                val entries = resp.getHits.iterator().map { hit =>
//
//                    if (hit.getSource.get("vid") != null) {
//                        val id:Long = hit.getSource.get("vid").toString.toLong
//                        Post.getPostById(id)
//                    } else
//                        None
//
//                }.filter(_.isDefined).map(_.get.asInstanceOf[SimplePost]).toSeq
//
//                SpecificSearchResult[SimplePost](entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
//            } catch {
//                case e:IndexMissingException =>
//                    getEmpty[SimplePost]
//            }
//        }
//    }
//
//    /**
//     * Search deal
//     * @param query query term.
//     * @param offset starting offset.
//     * @param limit end limit.
//     * @return
//     */
//    def searchDeal(query: String, offset: Int, limit: Int,
//                   includePrivate:Boolean=false,
//                   scope:Option[Origin[GraphType]]=None) = {
//
//        ensureIndicesCreated()
//
//        try {
//
//            val q = QueryBuilders.boolQuery()
//
//            query.toLowerCase.split("\\W+").map(_.trim).foreach { term =>
//                q.should(QueryBuilders.prefixQuery("name", term).boost(2.0F))
//                    .should(QueryBuilders.prefixQuery("desc", term).boost(1.0F))
//            }
//
//            q.minimumNumberShouldMatch(1)
//
//            if (!includePrivate)
//                q.must(QueryBuilders.termQuery("originIsPrivate", false))
//
//            scope map { origin =>
//                q.must(QueryBuilders.termQuery("originId", origin.getId))
//            }
//
//            val resp = client.prepareSearch("digaku1")
//                .setTypes("deal")
//                .setSearchType(SearchType.DEFAULT)
//                .setQuery(q).setFrom(offset).setSize(limit)
//                .execute()
//                .actionGet()
//
//            val entries = resp.getHits.iterator().flatMap { hit =>
//
//                if (hit.getSource.get("vid") != null){
//                    val id:Long = hit.getSource.get("vid").toString.toLong
//                    Deal.getById(id)
//                }else
//                    None
//
//            }.toSeq
//
//            SpecificSearchResult[Deal](entries, resp.getHits.getTotalHits,
//                resp.getTookInMillis, resp.getTook.format())
//        }catch{
//            case e:IndexMissingException =>
//                getEmpty[Deal]
//        }
//
//
//    }
//
//
//    /**
//     * Untuk mencari iklan
//     * @param query
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchAd(query: String, offset: Int, limit: Int) = {
//        ensureIndicesCreated()
//
//        val q = QueryBuilders.boolQuery()
//
//
//        query.toLowerCase.split("\\W+").map(_.trim).foreach { term =>
//            q.should(QueryBuilders.prefixQuery("title", term).boost(2.0F))
//                .should(QueryBuilders.prefixQuery("tags", term).boost(1.0F))
//        }
//
//        q.minimumNumberShouldMatch(1)
//
//        val resp = client.prepareSearch("digaku4")
//            .setTypes("ads")
//            .setSearchType(SearchType.DEFAULT)
//            .setQuery(q).setFrom(offset).setSize(limit)
//            .execute()
//            .actionGet()
//
//        val entries = resp.getHits.iterator().flatMap { hit =>
//
//            if (hit.getSource.get("vid") != null){
//                val id:Long = hit.getSource.get("vid").toString.toLong
//                Ads.getById(id)
//            }else
//                None
//
//        }.toSeq
//
//        SpecificSearchResult[Advertisement](entries, resp.getHits.getTotalHits,
//            resp.getTookInMillis, resp.getTook.format())
//    }
//
//    /**
//     * Cari sponsor berdasarkan kriteria yang diinginkan.
//     * @param query term query.
//     * @param timeRange range waktu aktif.
//     * @param ageRange range umur target.
//     * @param genderTarget target gender yang boleh melihat sponsor.
//     * @param showTimeRange waktu tampil yang diperbolehkan.
//     * @param targetCountry target negara.
//     * @param targetProvince target provinsi.
//     * @param targetCity target kota.
//     * @param containsClosed termasuk closed sponsor.
//     * @param offset starting offset.
//     * @param limit ends limit.
//     * @return
//     */
//    def searchSponsorPost(query: String,
//            offset: Int, limit: Int,
//            timeRange:Option[(Date,Date)]=None,
//            ageRange:Option[(Int,Int)]=None,
//            genderTarget:Option[Int]=None,
//            showTimeRange:Option[(Int,Int)]=None,
//            targetCountry:Option[String]=None,
//            targetProvince:Option[String]=None,
//            targetCity:Option[String]=None,
//            containsClosed:Boolean=false): SpecificSearchResult[SponsorPost] = {
//
//        ensureIndicesCreated()
//
//        val q = QueryBuilders.boolQuery()
//
//
////        query.toLowerCase.split("\\W+").map(_.trim).foreach { term =>
////            q.should(QueryBuilders.commonTerms("name", query) /*.boost(2.0F)*/)
//
//        if (query.trim.length > 0){
//            q.should(QueryBuilders.matchQuery("name", query))
//        }
//
////                .should(QueryBuilders.prefixQuery("tags", term) /*.boost(1.0F)*/)
////                .should(QueryBuilders.prefixQuery("content", term) //.boost(0.3F))
////        }
//        targetCountry.map { tc =>
////            ft.should(FilterBuilders.termFilter("targetCountry", tc))
//            q.must(QueryBuilders.termQuery("targetCountry", tc))
//        }
//        targetProvince.map { tp =>
////            ft.should(FilterBuilders.termFilter("targetProvince", tp))
//            q.must(QueryBuilders.commonTerms("targetProvince", tp))
//        }
//        targetCity.map { tc =>
////            ft.should(FilterBuilders.termFilter("targetCity", tc))
//            q.must(QueryBuilders.termQuery("targetCity", tc))
//        }
//
//        var hasFilter = false
//        val ft = FilterBuilders.boolFilter().cache(true)
//        timeRange.map { tr =>
//            if (tr._1 != null){
//                ft.must(FilterBuilders.rangeFilter("startTime").lte(tr._1.getTime).gt(0))
//                hasFilter = true
//            }
//            if (tr._2 != null){
//                ft.must(FilterBuilders.rangeFilter("endsTime").gte(tr._2.getTime))
//                hasFilter = true
//            }
//        }
//        ageRange.map { ar =>
//            if (ar._1 > 0){
//                ft.must(FilterBuilders.rangeFilter("minAge").lte(ar._1))
//                hasFilter = true
//            }
////            else
////                ft.must(FilterBuilders.termFilter("minAge", 0))
//            if (ar._2 > 0){
//                ft.must(FilterBuilders.boolFilter()
//                    .should(FilterBuilders.rangeFilter("maxAge").gte(ar._2))
//                    .should(FilterBuilders.termFilter("maxAge", 0))
//                )
//                hasFilter = true
//            }
////            else
////                ft.must(FilterBuilders.termFilter("maxAge", 0))
//
//        }
//        genderTarget.map { gt =>
//            ft.must(FilterBuilders.boolFilter()
//                .should(FilterBuilders.termFilter("genderTarget", gt))
//                .should(FilterBuilders.termFilter("genderTarget", -1))
//            )
//            hasFilter = true
//        }
//        showTimeRange.map { str =>
//            if (str._1 > 0){
////                ft.must(FilterBuilders.termFilter("showTimeStart", str._1))
//                ft.must(FilterBuilders.rangeFilter("showTimeStart").lte(str._1))
//                hasFilter = true
//            }
//            if (str._2 > 0){
////                ft.must(FilterBuilders.termFilter("showTimeEnds", str._2))
//                ft.must(FilterBuilders.rangeFilter("showTimeEnds").gte(str._2))
//                hasFilter = true
//            }
//        }
//        if (!containsClosed){
//            ft.must(FilterBuilders.termFilter("closed", false))
//            hasFilter = true
////            q.must(QueryBuilders.matchQuery("closed", false))
//        }
//
//
//        val q2 = if (hasFilter)
//            QueryBuilders.constantScoreQuery(QueryBuilders.filteredQuery(q, ft))
//        else
//            QueryBuilders.constantScoreQuery(q)
//
//
//        q.minimumNumberShouldMatch(1)
//
//        try {
//            val resp = client.prepareSearch("digaku8")
//                .setTypes("ads_sp")
//                .setSearchType(SearchType.DEFAULT)
//                .setQuery(q2).setFrom(offset).setSize(limit)
//                .execute()
//                .actionGet()
//
//            val entries = resp.getHits.iterator().flatMap {
//                hit =>
//
//                    if (hit.getSource.get("vid") != null) {
//                        val id: Long = hit.getSource.get("vid").toString.toLong
//                        SponsorPost.getById(id)
//                    } else
//                        None
//
//            }.toSeq
//
//            SpecificSearchResult[SponsorPost](entries, resp.getHits.getTotalHits,
//                resp.getTookInMillis, resp.getTook.format())
//        }
//        catch {
//            case e:org.elasticsearch.indices.IndexMissingException =>
//                warn(e.getMessage)
//                getEmpty[SponsorPost]
//            case e:ElasticsearchException =>
//                e.printStackTrace()
//                getEmpty[SponsorPost]
//        }
//    }


//    private val PARTICIPANTS = "participants"
//
//    /**
//     * Search private message in context of user.
//     * @param query query term.
//     * @param user user context (only search from this user context).
//     * @param key private message key. @see [[com.ansvia.digaku.se.DigakuSearchEngine.PrivateMessageKey]]
//     * @param offset starting offset.
//     * @param limit ends limit.
//     */
//    def searchPrivateMessage(query: String,
//                             user: User,
//                             key:PrivateMessageKey,
//                             offset:Int, limit:Int) = {
//
//        ensureIndicesCreated()
//
//        val q = QueryBuilders.boolQuery()
//            .must(QueryBuilders.queryString(user.name).field(PARTICIPANTS))
//
//        query.toLowerCase.split("\\W+").map(_.trim).foreach { term =>
//            key match {
//                case PrivateMessageKey.MESSAGE =>
//                    q.should(QueryBuilders.prefixQuery("message", term).boost(1.0F))
//                case PrivateMessageKey.FROM =>
//                    q.should(QueryBuilders.prefixQuery("from", term).boost(1.0F))
//                case PrivateMessageKey.PARTICIPANTS =>
//                    q.should(QueryBuilders.prefixQuery(PARTICIPANTS, term).boost(1.0F))
//            }
//        }
//
//        q.minimumNumberShouldMatch(1)
//
//        val resp = client.prepareSearch("digaku5")
//            .setTypes("pm")
//            .setSearchType(SearchType.DEFAULT)
//            .setQuery(q).setFrom(offset).setSize(limit)
//            .execute()
//            .actionGet()
//
//        val entries = resp.getHits.iterator().flatMap { hit =>
//
//            if (hit.getSource.get("vid") != null){
//                val id:Long = hit.getSource.get("vid").toString.toLong
//                PrivateMessage.getById(id)
//            }else
//                None
//
//        }.toSeq
//
//        SpecificSearchResult[PrivateMessage](entries, resp.getHits.getTotalHits,
//            resp.getTookInMillis, resp.getTook.format())
//    }


    /**
     * Search app
     * @see [[com.ansvia.digaku.model.App]]
     * @param query search term query.
     * @param offset starting offset.
     * @param limit ends limit.
     * @return
     */
    def searchApp(query: String, offset: Int, limit: Int): SpecificSearchResult[App] = {

        ensureIndicesCreated()

        val q = QueryBuilders.boolQuery()

        query.toLowerCase.split("\\W+").map(_.trim).foreach { term =>

            q.should(QueryBuilders.prefixQuery("name", term).boost(1.0F))
                .should(QueryBuilders.prefixQuery("desc", term).boost(0.5F))

        }

        q.minimumNumberShouldMatch(1)

        val resp = client.prepareSearch("digaku6")
            .setTypes("app")
            .setSearchType(SearchType.DEFAULT)
            .addFields("vid")
            .setQuery(q).setFrom(offset).setSize(limit)
            .execute()
            .actionGet()

        val entries = resp.getHits.iterator().flatMap { hit =>

            if (hit.getFields.get("vid") != null){
                val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong
                App.getById(id)
            }else
                None

        }.toSeq

        SpecificSearchResult[App](entries, resp.getHits.getTotalHits,
            resp.getTookInMillis, resp.getTook.format())
    }

    /**
     * Search FAQ
     * see [[com.ansvia.digaku.model.FAQ]]
     * @param query search term query.
     * @param offset starting offset.
     * @param limit ends limit.
     * @return
     */
    def searchFaq(query:String, offset:Int, limit:Int, includeArchived:Boolean = false): SpecificSearchResult[HighlightResult[FAQ]] = {
        val queryL = query.trim.toLowerCase

        val q1 = QueryBuilders.boolQuery()
            .should(QueryBuilders.commonTerms("title", queryL).boost(1.5F))
            .minimumNumberShouldMatch(1)

        if (!includeArchived) {
            q1.must(QueryBuilders.matchQuery("topicIsArchived", false))
        }

        val resp = client.prepareSearch("digaku12")
            .setTypes("faq")
            .setSearchType(SearchType.DEFAULT)
            .addFields("vid", "title")
            .setQuery(q1)
            .setFrom(offset).setSize(limit)
            .addHighlightedField("title", 300, 1)
            .setHighlighterPreTags("<em>")
            .setHighlighterPostTags("</em>")

        if (queryL.trim.isEmpty) {
            resp.addSort("creationTime", SortOrder.DESC)
        }

        val _resp = resp.execute().actionGet()

        val entries = _resp.getHits.iterator().flatMap { hit =>
            val vid = hit.getFields.get("vid")
            if (vid != null){
                val id:IDType = vid.getValue[String].toLong
                FAQ.getById(id).map { rv =>
                    val highlightContent = Option(hit.highlightFields().get("title")).flatMap { hContent =>
                        hContent.fragments().headOption.map(_.toString)
                    }.getOrElse(rv.title)

                    HighlightResult[FAQ](rv, Map("content" -> highlightContent))
                }
            } else {
                None
            }

        }.toList

        SpecificSearchResult[HighlightResult[FAQ]](entries, _resp.getHits.getTotalHits, _resp.getTookInMillis, _resp.getTook.format())
    }

    /**
     * Search FAQ
     * see [[com.ansvia.digaku.model.Topic]]
     * @param query search term query.
     * @Param includeArchivedTopic true jika ingin menapatkan topic yang diarsipkan.
     * @param offset starting offset.
     * @param limit ends limit.
     * @return
     */
    def searchTopic(query:String, includeArchivedTopic:Boolean, offset:Int, limit:Int): SpecificSearchResult[Topic] = {
        val queryL = query.trim.toLowerCase

        val q1 = QueryBuilders.boolQuery()
            .should(QueryBuilders.matchQuery("name", queryL).boost(2.0F))
            .should(QueryBuilders.matchPhrasePrefixQuery("name", queryL).boost(1.5F))
            .should(QueryBuilders.prefixQuery("name", queryL).boost(1.0F))
            .minimumNumberShouldMatch(1)

        if (!includeArchivedTopic) {
            q1.must(QueryBuilders.termQuery("archived", false))
        }

        val resp = client.prepareSearch("digaku12")
            .setTypes("topic")
            .setSearchType(SearchType.DEFAULT)
            .addFields("vid")
            .setQuery(q1)
            .setFrom(offset).setSize(limit)

        if (queryL.trim.isEmpty) {
            resp.addSort("creationTime", SortOrder.DESC)
        }

        val _resp = resp.execute().actionGet()

        val entries = _resp.getHits.iterator().flatMap { hit =>
            if (hit.getFields.get("vid") != null){
                val id:Long = hit.getFields.get("vid").getValue[java.lang.String].toLong
                Topic.getById(id)
            } else {
                None
            }
        }.toSeq

        SpecificSearchResult[Topic](entries, _resp.getHits.getTotalHits,
            _resp.getTookInMillis, _resp.getTook.format())
    }

    /**
     * Digunakan untuk mencari job title dari semua user
     * @param query search term query.
     * @param limit
     * @return
     */
    def searchUserJobTitles(query:String, limit:Int):List[String] = {
        val q1 = QueryBuilders.boolQuery()
            .should(QueryBuilders.simpleQueryString(query.trim.toLowerCase.replaceAll("\\s+", "+") + "*").field("jobTitle", 2.0F)
            .flags(SimpleQueryStringFlag.AND, SimpleQueryStringFlag.PREFIX))
            .minimumNumberShouldMatch(1)

        val jobAggregation = AggregationBuilders.terms("jobTitles").field("jobTitle.exact").size(limit)

        val resp =  client.prepareSearch("digaku2")
            .setTypes("user")
            .setSearchType(SearchType.DEFAULT)
            .addFields("vid")
            .setQuery(q1)
            .addAggregation(jobAggregation)
            .execute()
            .actionGet()

        val jobTerms:Terms = resp.getAggregations.get("jobTitles")

        jobTerms.getBuckets.toList.map(x => x.getKeyAsText.toString)
    }

    /**
     * Digunakan untuk mencari department dari semua user
     * @param query search term query.
     * @param limit
     * @return
     */
    def searchUserDepartments(query:String, limit:Int):List[String] = {
        val q1 = QueryBuilders.boolQuery()
            .should(QueryBuilders.simpleQueryString(query.trim.toLowerCase.replaceAll("\\s+", "+") + "*").field("department", 2.0F)
            .flags(SimpleQueryStringFlag.AND, SimpleQueryStringFlag.PREFIX))
            .minimumNumberShouldMatch(1)

        val jobAggregation = AggregationBuilders.terms("departments").field("department.exact").size(limit)

        val resp =  client.prepareSearch("digaku2")
            .setTypes("user")
            .setSearchType(SearchType.DEFAULT)
            .addFields("vid")
            .setQuery(q1)
            .addAggregation(jobAggregation)
            .execute()
            .actionGet()

        val jobTerms:Terms = resp.getAggregations.get("departments")

        jobTerms.getBuckets.toList.map(x => x.getKeyAsText.toString)
    }

    /************************************************
      * DELETE SECTION
      ***********************************************/

    def deleteChannel(ch:Forum){
        ensureIndicesCreated()

        val resp = client.prepareDelete("digaku3", "forum", ch.getId.toString)
            .execute()
            .actionGet()
        debug("delete index forum rv: " + resp.toString)
    }

    def deleteUser(user:User){
        ensureIndicesCreated()

        val resp = client.prepareDelete("digaku2", "user", user.getId.toString)
            .execute()
            .actionGet()
        debug("delete index user rv: " + resp.toString)
    }

//    def deleteAd(ad: Advertisement) {
//        ensureIndicesCreated()
//
//        val resp = client.prepareDelete("digaku4", "ads", ad.getId.toString)
//            .execute()
//            .actionGet()
//        debug("delete index ad " + ad + " rv: " + resp.toString)
//    }
//
//    /**
//     * Delete sponsor post index
//     * @param sp sponsor post to delete.
//     */
//    def deleteSponsorPost(sp: SponsorPost){
//        ensureIndicesCreated()
//
//        val resp = client.prepareDelete("digaku8", "ads_sp", sp.getId.toString)
//            .execute()
//            .actionGet()
//        debug("delete index ad " + sp + " rv: " + resp.toString)
//    }
//
    /**
     * Remove app from index.
     * @see [[com.ansvia.digaku.model.App]]
     * @param app app to remove.
     */
    def deleteApp(app: App){
        ensureIndicesCreated()

        val resp = client.prepareDelete("digaku6", "app", app.getId.toString)
            .execute()
            .actionGet()
        debug("delete index app " + app + " rv: " + resp.toString)
    }

    /**
     * Delete user group dari index elasticsearch
     * @param userGroup
     */
    override def deleteUserGroup(userGroup: UserGroup): Unit = {
        ensureIndicesCreated()

        val resp = client.prepareDelete("digaku11", "user-group", userGroup.getId.toString)
            .execute()
            .actionGet()

        debug("delete index user group " + userGroup + " rv : " + resp.toString)
    }

    /**
     * Delete faq dari index elasticsearch
     * @param faq
     */
    def deleteFaq(faq: FAQ): Unit = {
        ensureIndicesCreated()

        val resp = client.prepareDelete("digaku12", "faq", faq.getId.toString)
            .execute()
            .actionGet()

        debug("delete index faq " + faq + " rv : " + resp.toString)
    }

    /**
     * Delete topic dari index elasticsearch
     * @param topic
     */
    def deleteTopic(topic: Topic): Unit = {
        ensureIndicesCreated()

        val resp = client.prepareDelete("digaku12", "topic", topic.getId.toString)
            .execute()
            .actionGet()

        debug("delete index topic " + topic + " rv : " + resp.toString)
    }

//    /**
//     * Delete private message index.
//     * @param pm private message to delete.
//     */
//    def deletePrivateMessage(pm: PrivateMessage) = {
//        ensureIndicesCreated()
//
//        val resp = client.prepareDelete("digaku5", "pm", pm.getId.toString)
//            .execute()
//            .actionGet()
//        debug("delete pm " + pm + " rv: " + resp.toString)
//    }
}


class IndexObjectBuilder extends Slf4jLogger {
    def build(article:Article, customBuilder:XContentBuilder => XContentBuilder):XContentBuilder = {
        val popularityScore = article.getCounter.get("popularity") * 0.1
        debug("popularity score: " + popularityScore)

        val via = article.getPostVia.map(_.name).getOrElse("web")

        // Menjumlahkan response count dengan response child-nya, digunakan untuk sorting.
        val responseCount = article.getAllResponseCount

        val rv = jsonBuilder()
            .startObject()
            .field("vid", article.getId.toString)
            .field("title", article.title)
            .field("content", TextCompiler.cleanHtmlTags(article.content))
            .field("label", article.mark.map(_.title).getOrElse(""))
            .field("tags", article.tags)
            .field("sticked", article.isSticked.toString)
            .field("creationTime", new Date(article.creationTime))
            .field("originIsPrivate", article.originIsPrivate)
            .field("originIsDeleted", article.originIsDeleted)
            .field("creatorId", article.creator.getId.toString)
            .field("department", article.creator.department)
            .field("originId", article.origin.getId.toString)
            .field("originName", article.origin.getName)
            .field("blocked", article.blocked)
            .field("closed", article.closed)
            .field("popularity", popularityScore)
            .field("rating", article.getRating)
            .field("averageRating", article.getRatingAverage)
            .field("views", article.viewsCount)
            .field("responses", responseCount)
            .field("lastPostTime", new Date(article.getLastResponse.map(_.creationTime).getOrElse(article.creationTime)))
            .field("deleted", article.isDeleted)
            .field("privated", article.originIsPrivate)
            .field("via", via)
            .field("postAs", article.getPostAs.map(_.getId).getOrElse(0L).toString)
            .field("hasPolling", article.hasPolling)
            .field("hasEvent", article.hasEvent)
            .field("articleIsDeleted", article.isDeleted)

        customBuilder(rv).endObject()
    }

    /**
     * Json Builder/XContentBuilder untuk model Response
     * @param response model Response yang akan di build
     * @param customBuilder
     * @return XContentBuilder
     */
    def buildResponse(response:Response, customBuilder:XContentBuilder => XContentBuilder):XContentBuilder = {
        val creator = response.creator
        val responseId = response.getId.toString

        val originId = try {
            response.origin.getId
        } catch {
            case e:NoSuchElementException =>
                throw NotExistsException(s"Response ${response.getId} has no origin")
        }

        val postIsDeleted = response.getRespondedObject exists {
            case article:Article =>
                article.isDeleted
            case resp:Response =>
                // Anggap sudah di delete ketika parent response ataupun threadnya sudah di delete.
                resp.isDeleted || resp.getRespondedObject.exists {
                    case article: Article =>
                        article.isDeleted
                    case _ =>
                        true
                }
            case _ =>
                true
        }

        val via = response.getPostVia.map(_.name).getOrElse("web")

        val respondedObjId = response.getRespondedObject.map {
            // yang diambil threadnya digunakan untuk data analytic
            case response: Response =>
                response.getRespondedObject.map(_.getId).getOrElse(0L)
            case responable:Responable =>
                responable.getId
        }.getOrElse(0L)

        val rv = jsonBuilder()
            .startObject()
            .field("vid", responseId)
            .field("content", response.content)
            .field("deleted", response.isDeleted)
            .field("creationTime", new Date(response.creationTime))
            .field("creatorId", creator.getId)
            .field("respondedObject", respondedObjId)
            .field("department", creator.department)
            .field("originId", originId.toString)
            .field("articleIsDeleted", postIsDeleted)
            .field("via", via)
            .field("postAs", response.getPostAs.map(_.getId).getOrElse(0L).toString)

        customBuilder(rv).endObject()
    }

}

