/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.se

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.event.impl._
import com.ansvia.digaku.event.{AsyncEventStreamListener, EventStreamListener, StreamEvent}
import com.ansvia.digaku.model.{Article, _}

/**
 * Author: robin
 *
 * Event Stream Listener untuk search engine indexing.
 *
 */
object SEEventStreamListener extends AsyncEventStreamListener with Slf4jLogger {

    val actorName = "search-engine"

    protected val handledEvents = Seq(
        "create-post", "delete-post", "edit-article", "sticked-post",
        "like", "object-view", "response", "delete-response",
        "create-user",
        "update-user",
        "delete-user",
        "create-event",
//        "create-picture",
        "create-forum",
        "update-forum",
        "delete-forum",
        "restore-forum",
        "move-post",
        "join-forum",
        "leave-forum",
        "add-staff",
        "remove-staff",
//        "create-deal",
//        "create-private-message",
//        "delete-private-message",
        "inactive-user",
//        "create-ad",
//        "edit-ad",
//        "delete-ad",
        "create-app",
        "delete-app",
        "blocked",
        "closed",
//        "sponsor-post-created",
//        "sponsor-post-updated",
        "locked-user",
        "index-attribute-update",
        "update-privated-forum"
        )

    debug("started.")

    /**
     * Check is event handled by this listener.
     * WARNING: don't use `transact` inside this method
     * to avoid dead-lock.
     * @param eventName name of event.
     * @return
     */
    def isEventHandled(eventName: String) = handledEvents.contains(eventName)

    def asyncDispatch: PartialFunction[StreamEvent, Unit] = {
        case x =>
            try {
                asyncDispatchInternal.apply(x)
            }catch{
                case e:Exception =>
                    error(e.getMessage)
                    error(e.getStackTraceString)
            }
    }

    private def searchEngine = Digaku.engine.searchEngine

    private def asyncDispatchInternal: PartialFunction[StreamEvent, Unit] = {
        case CreatePostEvent(_, post) =>
            post.kind match {
                case PostKind.ARTICLE =>
                    safeCall(searchEngine.indexArticle(post))
                case _ =>
            }

        case DeletePostEvent(_, postId) =>
            safeCall {
                Post.getPostById(postId).foreach { p =>
                    p.kind match {
                        case PostKind.ARTICLE =>
                            searchEngine.indexArticle(p)

                            var count = 0
                            val limit = 100
                            var responses = p.getResponses(0, limit).toIterator

                            while (responses.hasNext) {
                                responses.foreach { resp =>
                                    searchEngine.indexResponse(resp)
                                    count += 1
                                }

                                responses = p.getResponses(count, limit).toIterator
                            }

                        case _ =>
                    }
                }
            }

        case JoinChannelEvent(user, _) =>
            safeCall(searchEngine.indexUser(user))

        case LeaveChannelEvent(user, _) =>
            safeCall(searchEngine.indexUser(user))

        case AddStaffEvent(user, _) =>
            safeCall(searchEngine.indexUser(user))

        case RemoveStaffEvent(user, _) =>
            safeCall(searchEngine.indexUser(user))

        case EditArticleEvent(_, article) =>
            safeCall(searchEngine.indexArticle(article))

        case LikeEvent(_, likable) =>
            //digunakan untuk rate a thread
            safeCall(searchEngine.index(likable))

        case ObjectViewEvent(Some(viewer), obj) if !obj.isViewer(viewer) =>
            safeCall(searchEngine.index(obj))

        case ObjectViewEvent(_, _) =>
            // anonymous di-ignore
//            safeCall(searchEngine.index(obj))

        case StickPostEvent(post) =>
            safeCall(searchEngine.index(post))

        case ResponseEvent(_, resp, obj) =>
            safeCall {
                searchEngine.index(resp)
                resp.getRespondedObject.foreach {
                    case resp:Response =>
                        resp.getRespondedObject.foreach(p => searchEngine.index(p))

                    case _ =>
                        searchEngine.index(obj)
                }
            }

        case DeleteResponseEvent(_, obj) =>
            safeCall {
                searchEngine.indexResponse(obj)
                obj.getRespondedObject.foreach(x => searchEngine.index(x))
            }

        case CreateUserEvent(user) =>
            safeCall(searchEngine.indexUser(user))

        case UpdateUserEvent(user) =>
            safeCall(searchEngine.indexUser(user))

        case InactiveUserEvent(user) =>
            searchEngine.indexUser(user)

        case LockedUserEvent(user) =>
            searchEngine.indexUser(user)

        case CreateEventEvent(_, event) =>
            safeCall(searchEngine.indexEvent(event))

//        case CreatePictureEvent(_, pic) =>
//            safeCall(searchEngine.indexPicture(pic))

        case CreateForumEvent(ch) =>
            safeCall(searchEngine.indexForum(ch))
            safeCall(searchEngine.indexUser(ch.owner))

        case UpdateForumEvent(ch) =>
            safeCall(searchEngine.indexForum(ch))

//        case CreatePrivateMessageEvent(_, pm) =>
////            db.commit()
////            pm.reload()
//            safeCall(searchEngine.indexPrivateMessage(pm))

        case CreateAppEvent(app) => {
            safeCall(searchEngine.indexApp(app))
        }

        case UpdatePrivatedForumEvent(forum, state) =>
            safeCall {
                // Update seluruh post nya ketika update privated sebuah forum
                var count = 0
                var posts = forum.contentQuery().get(0, 100).entries.toIterator
                while (posts.hasNext) {

                    posts.foreach { p =>
                        count = count + 1
                        searchEngine.indexArticle(p)
                    }

                    posts = forum.contentQuery().get(count, 100).entries.toIterator
                }

                searchEngine.indexForum(forum)
            }

        case DeleteChannelEvent(ch) =>
            safeCall(searchEngine.deleteChannel(ch))

        case MovePostEvent(post, _, _) =>
            safeCall(searchEngine.indexArticle(post))

        case RestoreChannelEvent(ch) =>
            safeCall(searchEngine.indexForum(ch))

        case DeleteUserEvent(user) =>
            safeCall(searchEngine.deleteUser(user))

//        case CreateDealEvent(_, deal) =>
//            safeCall(searchEngine.indexDeal(deal))
//
//        case CreateAdEvent(ad) =>
//            safeCall(searchEngine.indexAd(ad))
//
//        case EditAdEvent(ad) =>
//            safeCall(searchEngine.indexAd(ad))
//
//        case DeleteAdEvent(ad) =>
//            safeCall(searchEngine.deleteAd(ad))

//        case DeletePrivateMessageEvent(pm) =>
//            safeCall(searchEngine.deletePrivateMessage(pm))

        case DeleteAppEvent(app) =>
            safeCall(searchEngine.deleteApp(app))

        case BlockedEvent(blockable) =>
            blockable match {
//                case simplePost:SimplePost =>
//                    safeCall(searchEngine.indexSimplePost(simplePost))
                case article:Article =>
                    safeCall(searchEngine.indexArticle(article))
//                case picture:Picture =>
//                    safeCall(searchEngine.indexPicture(picture))
                case group:Forum =>
                    safeCall(searchEngine.indexForum(group))
                case _ =>
            }

        case ClosedEvent(closable) =>
            safeCall(searchEngine.index(closable))

//        case SponsorPostCreatedEvent(sp) =>
//            searchEngine.index(sp)
//
//        case SponsorPostUpdatedEvent(sp) => // reindex when updated
//            searchEngine.index(sp)

        case IndexAttributeUpdate(obj, _) =>
//            println(s"lazy indexing $obj")
            searchEngine.index(obj)

    }

    private def safeCall(func: => Unit){
        try {
            func
        }catch{
            case e:Exception =>
                e.printStackTrace()
        }
    }

}

/**
 * Event stream listener untuk search engine indexing
 * yang versi blocking.
 * Hanya digunakan untuk unittest.
 */
object BlockingSEEventStreamListener extends EventStreamListener {
    /**
     * Check is event handled by this listener.
     * WARNING: don't use `transact` inside this method
     * to avoid dead-lock.
     * @param eventName name of event.
     * @return
     */
    def isEventHandled(eventName: String) = SEEventStreamListener.isEventHandled(eventName)

    def dispatch = SEEventStreamListener.asyncDispatch
}