package com.ansvia.digaku.se

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types.IDType
import com.ansvia.digaku.model.{Article, BaseModel, Post, Response}
import com.ansvia.digaku.utils.{AsInt, AsLong, SortDirection}
import org.elasticsearch.action.search.SearchType
import org.elasticsearch.index.query.{FilterBuilder, FilterBuilders, QueryBuilders}
import org.elasticsearch.indices.IndexMissingException
import org.elasticsearch.search.sort.{FieldSortBuilder, SortBuilder, SortOrder}
import scala.collection.JavaConversions._


/**
 * Author: robin (<EMAIL>)
 */

abstract class ContentQueryBuilder[T](index:Seq[String], _type:Seq[String]) {
    type CQB

    protected val qb: FilterBuilder

    def setOrigin(name:String):CQB
    def setOrigin(id:IDType):CQB

    private var sorting:(String, SortDirection) = ("*", SortDirection.ASC)

    protected def hasCustomSorting = sorting._1 != "*"

    protected def getSortingBuilder():SortBuilder = {
        val order = sorting._2 match {
            case SortDirection.ASC => SortOrder.ASC
            case SortDirection.DESC => SortOrder.DESC
        }
        new FieldSortBuilder(sorting._1).order(order).missing("_last").unmappedType("long")
    }

    def setSorting(key:String, order:SortDirection):this.type = {
        sorting = (key, order)
        this
    }

    def get(qb:FilterBuilder, offset:Int, limit:Int):QueryResult[T]
    def get(offset:Int, limit:Int):QueryResult[T] = get(qb, offset, limit)
}


abstract class ContentBoolQueryBuilder[T](protected val index:Seq[String], protected val _type:Seq[String])
    extends ContentQueryBuilder[T](index, _type){

    type CQB = ContentBoolQueryBuilder[T]

    protected val qb = FilterBuilders.boolFilter()

    override def setOrigin(name: String): CQB = {
        qb.must(FilterBuilders.termFilter("originName", name))
        this
    }

    override def setOrigin(id: IDType): CQB = {
        qb.must(FilterBuilders.termFilter("originId", id))
        this
    }

    def terms(key:String, value:String) = {
        qb.should(FilterBuilders.termFilter(key, value))
        this
    }

    def matchQuery(key:String, value:String) = {
        qb.must(FilterBuilders.queryFilter(QueryBuilders.matchQuery(key, value)))
        this
    }

    def termsMust(key:String, value: Any) = {
        qb.must(FilterBuilders.termFilter(key, value))
        this
    }

    def commonTerms(key:String, value:String) = {
        qb.should(FilterBuilders.termFilter(key, value))
        this
    }
}


case class QueryResult[T](entries: Seq[T], count: Long, took: Long, tookStr: String)


class ArticleQueryBuilder() extends ContentBoolQueryBuilder[Article](Seq("digaku1"), Seq("article")) {
    lazy val searchEngine = Digaku.engine.searchEngine match {
        case es:AbstractElasticSearchEngine => es
    }

    private lazy val client = searchEngine.client

    private def getQuery(_qb:FilterBuilder, offset:Int, limit:Int) = {
        val prep = client.prepareSearch(index:_*)
            .setTypes(_type:_*)
            .setSearchType(SearchType.DEFAULT)
            .addFields("vid")
            .setQuery(QueryBuilders.filteredQuery(null, _qb))
            .setFrom(offset).setSize(limit)

        if (hasCustomSorting) {
            prep.addSort(getSortingBuilder())
        }

        prep
    }

    override def get(_qb:FilterBuilder, offset:Int, limit:Int): QueryResult[Article] = {
        try {
            val resp = getQuery(_qb, offset, limit).execute().actionGet()
            val entries = resp.getHits.iterator().map { hit =>
                if (hit.getFields.get("vid") != null) {
                    val id:Long = hit.getFields.get("vid").getValue[java.lang.String] match {
                        case AsLong(_id) => _id
                        case AsInt(_id) => _id.toLong
                    }

                    Post.getPostById(id)
                } else
                    None

            }.filter(_.isDefined).map(_.get.asInstanceOf[Article]).toSeq

            QueryResult(entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        } catch {
            case e:IndexMissingException =>
                QueryResult[Article](Nil, 0, 0, "")
        }
    }
}


class ArticleAndResponseQueryBuilder()
    extends ContentBoolQueryBuilder[BaseModel[IDType]](Seq("digaku1","digaku10"), Seq("article","response")) {

    private val searchEngine = Digaku.engine.searchEngine match {
        case es:AbstractElasticSearchEngine => es
    }

    private lazy val client = searchEngine.client

    private def getQuery(_qb:FilterBuilder, offset:Int, limit:Int) = {
        val prep = client.prepareSearch(index:_*)
            .setTypes(_type:_*)
            .setSearchType(SearchType.DEFAULT)
            .addFields("vid")
            .setQuery(QueryBuilders.filteredQuery(null, _qb))
            .setFrom(offset).setSize(limit)

        if (hasCustomSorting){
            prep.addSort(getSortingBuilder())
        }

        prep
    }

    override def get(_qb:FilterBuilder, offset:Int, limit:Int): QueryResult[BaseModel[IDType]] = {
        try {
            val resp = getQuery(_qb, offset, limit).execute().actionGet()
            val entries = resp.getHits.iterator().map { hit =>
                if (hit.getFields.get("vid") != null) {
                    val id: Long = hit.getFields.get("vid").getValue[java.lang.String] match {
                        case AsLong(_id) => _id
                        case AsInt(_id) => _id.toLong
                    }

                    hit.getType match {
                        case "article" => Post.getById(id)
                        case "response" => Response.getById(id)
                    }
                } else
                    None
            }.filter(_.isDefined).map(_.get).toSeq

            QueryResult(entries, resp.getHits.getTotalHits, resp.getTookInMillis, resp.getTook.format())
        } catch {
            case e:IndexMissingException =>
                QueryResult[BaseModel[IDType]](Nil, 0, 0, "")
        }
    }
}
