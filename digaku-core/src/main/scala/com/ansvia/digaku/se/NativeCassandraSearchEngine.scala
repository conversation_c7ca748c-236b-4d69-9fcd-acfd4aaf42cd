///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.se
//
//import com.ansvia.digaku.Types
//import com.ansvia.digaku.model._
//import com.ansvia.digaku.persistence.CassandraDriver
//import com.netflix.astyanax.model.ColumnFamily
//import com.netflix.astyanax.serializers.{ComparatorType, LongSerializer, StringSerializer}
//import com.netflix.astyanax.util.RangeBuilder
//
///**
// * Author: robin
// *
// */
//class NativeCassandraSearchEngine extends DigakuSearchEngine {
//
//    import scala.collection.JavaConversions._
//
//    val ksName = "digaku_test"
//    val cfName = "search_index"
//
//    val USER_KEY = "user"
//
//    private lazy val cassCtx = CassandraDriver.getContext("digaku", ksName, "127.0.0.1:9160", "SimpleStrategy",
//        "replication_factor=1")
//    private lazy val ks = cassCtx.keyspace.getClient
//
//    private val columnFamily = new ColumnFamily[String, String](
//        cfName,
//        StringSerializer.get(),
//        StringSerializer.get(),
//        LongSerializer.get()
//    )
//
//    private lazy val ensureReady = {
//        val client = cassCtx.cluster.getClient
//        val ksDef = client.describeKeyspace(ksName)
//
//        if (!ksDef.getColumnFamilyList.exists(_.getName.equalsIgnoreCase(cfName))){
//            val cfDef = client.makeColumnFamilyDefinition()
//                .setName(cfName)
//                .setKeyspace(ksName)
//                .setKeyValidationClass(ComparatorType.UTF8TYPE.getClassName)
//                .setCompressionOptions(Map("sstable_compression" -> "org.apache.cassandra.io.compress.SnappyCompressor",
//                    "chunk_length_kb" -> "64"))
//            client.addColumnFamily(cfDef)
//        }
//    }
//
//    def getVersion = ""
//
//    /** **********************************************
//      * INDEX SECTION
//      * **********************************************/
//    def indexForum: NativeCassandraSearchEngine#PF = null
//
//    def indexArticle: NativeCassandraSearchEngine#PF = null
//
//    def indexEvent: NativeCassandraSearchEngine#PF = null
//
//    def indexPicture: NativeCassandraSearchEngine#PF = null
//
//    /**
//     * Index deal.
//     * @return
//     */
//    def indexDeal: NativeCassandraSearchEngine#PF = null
//
//    def indexUser: NativeCassandraSearchEngine#PF = {
//        case user:User =>
//
//        ensureReady
//
//        val mb = ks.prepareMutationBatch()
//        mb.withRow(columnFamily, USER_KEY + ".name").putColumn(user.name.toLowerCase, user.getId)
//        val fullNameRow = mb.withRow(columnFamily, USER_KEY + ".fullName")
//        user.fullName.toLowerCase.split("\\W+").foreach(n => fullNameRow.putColumn(n,user.getId))
//        mb.withRow(columnFamily, USER_KEY + ".email").putColumn(user.emailLogin, user.emailLogin)
//        mb.execute()
//
//    }
//
//    def indexAd: NativeCassandraSearchEngine#PF = null
//
//
//    def indexSponsorPost: NativeCassandraSearchEngine#PF = null
//
//    /**
//     * index simple post
//     * @return
//     */
//    def indexSimplePost: NativeCassandraSearchEngine#PF = null
//
////    def indexPrivateMessage: NativeCassandraSearchEngine#PF = null
//
//    /**
//     * Digunakan untuk meng-index App.
//     * @see [[com.ansvia.digaku.model.App]]
//     * @return
//     */
//    def indexApp: NativeCassandraSearchEngine#PF = null
//
//    /**
//     * Search article
//     * @param query term query to search.
//     * @param offset starting offset.
//     * @param limit ends limit.
//     * @param includeFromPrivateOrigin whether to search also from private origin (default false)
//     * @param scope origin scope, whether to search scope limited to origin.
//     *              used in local group search.
//     * @return
//     */
//    def searchArticle(query: String, offset: Int, limit: Int, includeFromPrivateOrigin: Boolean, scope: Option[Origin[Types.GraphType]]): SpecificSearchResult[Article] = null
//
//    /**
//     * search blocked article
//     * @param query term query to search.
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchBlockedArticle(query: String, offset: Int, limit: Int): SpecificSearchResult[Article] = null
//
//    /**
//     * Search closed article
//     * @param query term query to search.
//     * @param originId
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchClosedArticle(query: String, originId: Long, offset: Int, limit: Int): SpecificSearchResult[Article] = null
//
//
//    private def timing[T](func: => T):(Long,T) = {
//        val start = System.currentTimeMillis()
//        val rv = func
//        val time = System.currentTimeMillis() - start
//        (time, rv)
//    }
//
//    /**
//     * Search user.
//     * @param query query term.
//     * @param offset starting offset.
//     * @param limit end limit.
//     * @param locked include user locked or not.
//     * @param inactive include user deactivated or not.
//     * @return
//     */
//    def searchUser(query: String, offset: Int, limit: Int, locked: Boolean, inactive:Boolean = false): SpecificSearchResult[User] = {
//        ensureReady
//
//        val normQuery = query.toLowerCase.trim
//
//        val (time,rv) = timing {
//            val _rv1 = ks.prepareQuery(columnFamily)
//                .getKey(USER_KEY + ".name")
//                .withColumnRange(new RangeBuilder().setStart(normQuery).setEnd(normQuery + "z").setLimit(limit).build())
//                .execute().getResult
//            val _rv2 = ks.prepareQuery(columnFamily)
//                .getKey(USER_KEY + ".fullName")
//                .withColumnRange(new RangeBuilder().setStart(normQuery).setEnd(normQuery + "z").setLimit(limit).build())
//                .execute().getResult
//
//            (_rv1.map(_.getLongValue).flatMap(User.getById).toSeq ++
//                _rv2.map(_.getLongValue).flatMap(User.getById).toSeq).distinct
//        }
//
//        SpecificSearchResult(rv, rv.length, time, + time + "ms")
//    }
//
//    /**
//     * Search group.
//     * @param query query term.
//     * @param offset starting offset.
//     * @param limit end limit.
//     * @return
//     */
//    def searchForum(query: String, offset: Int, limit: Int, includePrivate: Boolean, searcher: Option[User]): SpecificSearchResult[Forum] = null
//
//    /**
//     * search blocked group
//     * @param query term query to search.
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchBlockedChannel(query: String, offset: Int, limit: Int): SpecificSearchResult[Forum] = null
//
//    /**
//     * Search event.
//     * @param query query term.
//     * @param offset starting offset.
//     * @param limit end limit.
//     * @param scope
//     * @return
//     */
//    def searchEvent(query: String, offset: Int, limit: Int, includePrivate: Boolean, scope: Option[Origin[Types.GraphType]]): SpecificSearchResult[Event] = null
//
//    /**
//     * Search picture.
//     * @param query query term.
//     * @param offset starting offset.
//     * @param limit end limit.
//     * @return
//     */
//    def searchPicture(query: String, offset: Int, limit: Int, includePrivate: Boolean, scope: Option[Origin[Types.GraphType]]): SpecificSearchResult[Picture] = null
//
//    /**
//     * search blocked picture
//     * @param query term query to search.
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchBlockedPicture(query: String, offset: Int, limit: Int): SpecificSearchResult[Picture] = null
//
//    /**
//     * Search closed picture
//     * @param query term query to search.
//     * @param originId
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def searchClosedPicture(query: String, originId: Long, offset: Int, limit: Int): SpecificSearchResult[Picture] = null
//
////    /**
////     * search blocked video
////     * @param query term query to search.
////     * @param offset
////     * @param limit
////     * @return
////     */
////    def searchBlockedVideo(query: String, offset: Int, limit: Int): SpecificSearchResult[SimplePost] = null
////
////    /**
////     * Search closed video
////     * @param query term query to search.
////     * @param originId
////     * @param offset
////     * @param limit
////     * @return
////     */
////    def searchClosedVideo(query: String, originId: Long, offset: Int, limit: Int): SpecificSearchResult[SimplePost] = null
////
////    /**
////     * Search deal
////     * @param query query term.
////     * @param offset starting offset.
////     * @param limit end limit.
////     * @return
////     */
////    def searchDeal(query: String, offset: Int, limit: Int, includePrivate: Boolean, scope: Option[Origin[Types.GraphType]]): SpecificSearchResult[Deal] = null
//
//    /**
//     * Untuk mencari post.
//     * @param query
//     * @param offset
//     * @param limit
//     * @param includePrivate
//     * @param scope
//     * @return
//     */
//    def searchPost(query: String, offset: Int, limit: Int, includePrivate: Boolean, scope: Option[Origin[Types.GraphType]]): SearchPostResult = null
//
////    /**
////     * Untuk mencari ads
////     * @param query search term query
////     * @param offset starting offset.
////     * @param limit ends limit.
////     * @return
////     */
////    def searchAd(query: String, offset: Int, limit: Int): SpecificSearchResult[Advertisement] = null
////
////
////    /**
////     * Cari sponsor berdasarkan kriteria yang diinginkan.
////     * @param query term query.
////     * @param offset
////     * @param limit
////     * @param timeRange range waktu aktif.
////     * @param ageRange range umur target.
////     * @param genderTarget
////     * @param showTimeRange
////     * @param targetCountry
////     * @param targetProvince
////     * @param targetCity
////     * @param containsClosed
////     * @return
////     */
////    def searchSponsorPost(query: String, offset: Int, limit: Int, timeRange: Option[(Date, Date)],
////                      ageRange: Option[(Int, Int)], genderTarget: Option[Int],
////                      showTimeRange: Option[(Int, Int)], targetCountry: Option[String],
////                      targetProvince: Option[String], targetCity: Option[String],
////                      containsClosed: Boolean): SpecificSearchResult[SponsorPost] = null
//
////    /**
////     * Search private message in context of user.
////     * @param query query term.
////     * @param user user context (only search from this user context).
////     * @param key private message key. @see [[com.ansvia.digaku.se.DigakuSearchEngine.PrivateMessageKey]]
////     * @param offset starting offset.
////     * @param limit ends limit.
////     */
////    def searchPrivateMessage(query: String, user: User, key: PrivateMessageKey, offset: Int, limit: Int): SpecificSearchResult[PrivateMessage] = null
//
//    /**
//     * Search app
//     * @see [[com.ansvia.digaku.model.App]]
//     * @param query search term query.
//     * @param offset starting offset.
//     * @param limit ends limit.
//     * @return
//     */
//    def searchApp(query: String, offset: Int, limit: Int): SpecificSearchResult[App] = null
//
//    /**
//     * Search all.
//     * For specific search use:
//     * + [[com.ansvia.digaku.se.DigakuSearchEngine.searchArticle]]
//     * + [[com.ansvia.digaku.se.DigakuSearchEngine.searchUser]]
//     *
//     * @param query query term.
//     * @param offset starting offset.
//     * @param limit end limit.
//     * @return
//     */
//    def search(query: String, offset: Int, limit: Int, includePrivate: Boolean, scope: Option[Origin[Types.GraphType]], searcher: Option[User]): SearchAllResult = null
//
//
//    /**
//     * Reindex search engine data.
//     * @param mode see [[ReindexMode]]
//     */
//    override def reindex(mode: Int): Unit = ()
//
//
//    override def reindexModels(models: String*){}
//
//    /** **********************************************
//      * DELETE SECTION
//      * **********************************************/
//    def deleteChannel(ch: Forum): Unit = ()
//
//    def deleteUser(user: User): Unit = ()
//
////    def deleteAd(ad: Advertisement): Unit = ()
////
////    def deleteSponsorPost(sp: SponsorPost){}
//
//    /**
//     * Remove app from index.
//     * @see [[com.ansvia.digaku.model.App]]
//     * @param app app to remove.
//     */
//    def deleteApp(app: App): Unit = ()
//
////    /**
////     * Delete private message index.
////     * @param pm private message to delete.
////     */
////    def deletePrivateMessage(pm: PrivateMessage): Unit = ()
//}
