/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.stream

import java.util.concurrent.TimeUnit

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types.{GraphType, IDType}
import com.ansvia.digaku.model.{Origin, Streamable}
import com.ansvia.digaku.persistence.CassandraDriver
import com.netflix.astyanax.connectionpool.exceptions.NotFoundException
import com.netflix.astyanax.connectionpool.impl.ConnectionPoolConfigurationImpl
import com.netflix.astyanax.model.{ColumnFamily, ConsistencyLevel}
import com.netflix.astyanax.serializers.{ObjectSerializer, StringSerializer}
import com.netflix.astyanax.thrift.ThriftFamilyFactory
import org.mapdb.DBMaker

/**
 * Author: robin
 *
 * Merupakan logika untuk engine stream duplicate detector.
 */

/**
 * Trait stream duplicate detector.
 */
trait DuplicateDetector {

    /**
     * Periksa apakah stream sudah ada untuk origin.
     * @param origin origin.
     * @param streamable object yang bisa di-stream-kan.
     * @return true apabila exists, false apabila tidak.
     */
    def isStreamExists(origin:Origin[GraphType], streamable:Streamable[IDType]):Boolean

    /**
     * Tandai sebuah stream sebagai sudah ada pada origin.
     * @param origin origin.
     * @param streamable object yang bisa di-stream-kan.
     */
    def markStreamExists(origin:Origin[GraphType], streamable:Streamable[IDType])
}

/**
 * Implementasi stream duplicate detector ini selalu return false
 * seperti nop, tidak melakukan pengecekan sama sekali, digunakan untuk
 * default value sebelum di-set.
 */
class AlwaysFalseDuplicateDetector extends DuplicateDetector {

    /**
     * Periksa apakah stream sudah ada untuk origin.
     * This method always return false.
     * @param origin origin.
     * @param streamable object yang bisa di-stream-kan.
     * @return false.
     */
    def isStreamExists(origin: Origin[GraphType], streamable: Streamable[IDType]): Boolean = false

    /**
     * Tandai sebuah stream sebagai sudah ada pada origin.
     * @param origin origin.
     * @param streamable object yang bisa di-stream-kan.
     */
    def markStreamExists(origin: Origin[GraphType], streamable: Streamable[IDType]){}
}

/**
 * Implementasi stream duplicate detector menggunakan backend cassandra.
 * @param clusterName nama cassandra cluster.
 * @param keyspaceName nama cassandra keyspace.
 * @param seeds cassandra hosts/seeds.
 */
class CassandraBackedDuplicateDetector(clusterName:String, keyspaceName:String, seeds:String)
    extends DuplicateDetector with Slf4jLogger {

    private lazy val COLUMN_FAMILY = new ColumnFamily[String, String](
        "Standard1",
        StringSerializer.get(),
        StringSerializer.get(),
        ObjectSerializer.get())

    private def rowKey(month:Int) = "stream_builder_duplicate_detector-" + month

    private val port = {
        val s = seeds.split(":")
        if (s.length > 1)
            s(1).toInt
        else
            9160
    }
    private lazy val csBuilder = CassandraDriver.createBuilder(clusterName, keyspaceName, seeds)
        .withConnectionPoolConfiguration(new ConnectionPoolConfigurationImpl("streambuilder-conn-pool")
        .setPort(port)
        .setMaxConnsPerHost(3)
        .setMaxConns(100)
        .setSeeds(seeds)
    )
    private lazy val cluster = {
        val _cluster = csBuilder.buildCluster(ThriftFamilyFactory.getInstance())
        _cluster.start()
        _cluster
    }

    // operasi ini tidak ada operasi untuk ensure keyspace exists
    // karena seharusnya bukan di sini yang memastikan-nya
    private lazy val keyspace = {
        cluster
        val _ks = csBuilder.buildKeyspace(ThriftFamilyFactory.getInstance())
        _ks.start()
        _ks
    }


    /**
     * Digunakan untuk local cache, sehingga data harus secara langsung ter-update
     * ketika read di client local ini tanpa perlu baca ke Cassandra.
     */
    private lazy val _db = DBMaker.newMemoryDirectDB()
        .compressionEnable()
        .sizeLimit(1)
        .transactionDisable()
        .closeOnJvmShutdown()
        .make()
    private lazy val _localCache = _db.createHashSet("cass_dup_detector")
        .expireAfterAccess(10, TimeUnit.MINUTES)
        .expireAfterWrite(20, TimeUnit.MINUTES)
        .expireMaxSize(50000)
        .make[String]()

    /**
     * Periksa apakah stream sudah ada untuk origin.
     * @param origin origin.
     * @param streamable object yang bisa di-stream-kan.
     * @return true apabila exists, false apabila tidak.
     */
    def isStreamExists(origin: Origin[GraphType], streamable: Streamable[IDType]): Boolean = {

        synchronized {
            val colKey = "%s-%s".format(origin.getId, streamable.hash)
            val keyById = "%s-%s".format(origin.getId, streamable.getId)

            if (_localCache.contains(colKey) || _localCache.contains(keyById)) {
                true
            }else{
                var curMonth = Digaku.engine.dateUtils.getCurrentMonth

                try {
                    def checkExisting(month:Int) = {
                        val columnList = keyspace.getClient.prepareQuery(COLUMN_FAMILY)
                        //                    .setConsistencyLevel(ConsistencyLevel.CL_LOCAL_QUORUM)
                        .getKey(rowKey(month))
                        //.getColumn(colKey)
                        .withColumnSlice(colKey, keyById)
                            .execute().getResult //.getIntegerValue == 1

                        val a = columnList.getColumnByName(colKey)
                        val b = columnList.getColumnByName(keyById)


                        (a != null && a.getIntegerValue == 1) || (b != null && b.getIntegerValue == 1)
                    }

                    var exists = checkExisting(Digaku.engine.dateUtils.getCurrentMonth)

                    if (!exists){
                        if (Digaku.engine.dateUtils.getCurrentDayOfMonth < 10){ // jika tanggal muda
                            // check pada bulan sebelumnya
                            curMonth = curMonth - 1
                            if (curMonth > 0){
                                exists = checkExisting(curMonth)
                                //                            // jika masih tidak exists, coba check pada bulan depannya
                                //                            if (!exists){
                                //                                exists = checkExisting(Digaku.engine.dateUtils.getCurrentMonth + 1)
                                //                            }
                            }
                        }
                    }

                    exists

                } catch {
                    case e: NotFoundException =>
                        false
                    case e: Exception =>
                        error(e.getMessage)
                        error(e.getStackTraceString)
                        false
                }
            }


        }

    }

    /**
     * Tandai sebuah stream sebagai sudah ada pada origin.
     * @param origin origin.
     * @param streamable object yang bisa di-stream-kan.
     */
    def markStreamExists(origin: Origin[GraphType], streamable: Streamable[IDType]){

        synchronized {
            val curMonth = Digaku.engine.dateUtils.getCurrentMonth

            val colKey = "%s-%s".format(origin.getId, streamable.hash)
            val keyById = "%s-%s".format(origin.getId, streamable.getId)

            if (!_localCache.contains(colKey) || !_localCache.contains(keyById)) {
                _localCache.add(colKey)
                _localCache.add(keyById)

                val m = keyspace.getClient.prepareMutationBatch()
                m.withRow(COLUMN_FAMILY, rowKey(curMonth))
                    .putColumn(colKey, 1, 259200) // 3 days
                m.withRow(COLUMN_FAMILY, rowKey(curMonth))
                    .putColumn(keyById, 1, 259200) // 3 days
                m.withConsistencyLevel(ConsistencyLevel.CL_LOCAL_QUORUM)
                //                m.executeAsync()

                m.execute()
            }


        }

    }

    def close(){
        keyspace.shutdown()
        cluster.shutdown()
    }

}
