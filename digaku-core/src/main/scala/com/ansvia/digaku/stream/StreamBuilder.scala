/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.stream

import java.util.concurrent.CountDownLatch

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.event._
import com.ansvia.digaku.event.impl._
import com.ansvia.digaku.exc.{DigakuException, IgnoredException, NotExistsException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.model.{HasOrigin, _}
import com.ansvia.graph.BlueprintsWrapper.{vertexWrapper, _}
import com.ansvia.perf.PerfTiming
import com.tinkerpop.blueprints.{Direction, Edge, Vertex}
import com.tinkerpop.gremlin.java.GremlinPipeline
import org.joda.time.DateTime

import scala.collection.JavaConversions._
import scala.collection.mutable
import scala.collection.mutable.ArrayBuffer
import scala.concurrent.{ExecutionContext, Future}


/**
 * Author: robin
 *
 */

// scalastyle:off file.size.limit

/** Event stream listener yang digunakan untuk build per-user stream (local)
  * Untuk yg distributed bisa menggunakan minion stream builder com.ansvia.digaku.minion.MinionStreamBuilder
  *
  * @param dispatchers list of dispatcher to use.
  */
class StreamBuilder(dispatchers:StreamBuilderDispatcher*)
    extends AsyncLBEventStreamListenerGroup(dispatchers: _*) with DbAccess with Slf4jLogger {

    def getDispatchers = dispatchers

    /**
     * Check is event handled by this listener.
     * WARNING: don't use `transact` inside this method
     * to avoid dead-lock.
     *
     * @param eventName name of event.
     * @return
     */
    def isEventHandled(eventName: String) = StreamBuilder.handledObjects.contains(eventName)

}

/**
 * Blocking event stream listener yang digunakan untuk build per-user stream.
 * Sama dengan [[com.ansvia.digaku.stream.StreamBuilder]] hanya saja yang ini blocking
 * digunakan untuk unittesting.
 */
class BlockingStreamBuilder(dispatchers:StreamBuilderDispatcher*)
    extends BlockingLBEventStreamListenerGroup(dispatchers: _*) with DbAccess with Slf4jLogger {

    /**
     * Check is event handled by this listener.
     * WARNING: don't use `transact` inside this method
     * to avoid dead-lock.
     * @param eventName name of event.
     * @return
     */
    def isEventHandled(eventName: String) = StreamBuilder.handledObjects.contains(eventName)

}

trait StreamBuilderEngine {
    var duplicateDetector:DuplicateDetector
}

trait StreamBuilderComponent {
    val streamBuilder:StreamBuilderEngine

}

/**
 * stream builder dispatcher ini digunakan
 * untuk membuild per user stream
 * yang ini lebih untuk menjadi worker-nya.
 * @param id worker id.
 */
class StreamBuilderDispatcher(id:Int) extends Dispatcher with DbAccess with Slf4jLogger {

    override def toString = "stream-builder-dispatcher-" + id

    var linkUserItemTimeFromCreation = false
    var linkChannelItemTimeFromCreation = false

    def dispatch: PartialFunction[StreamEvent, Unit] = {
        case streamEvent:StreamEvent =>
            try {
                debug("got stream-event: " + streamEvent)
                db.commit()
                dispatchInternal.apply(streamEvent)
            }
            catch {
                case e:Exception =>
                    error(e.getMessage)
                    error(e.getStackTraceString)
            }
    }

    import com.ansvia.digaku.stream.StreamBuilderDispatcher._

    // scalastyle:off cyclomatic.complexity
    private def dispatchInternal: PartialFunction[StreamEvent, Unit] = {
        case CreatePostEvent(creator, post) =>
            linkUserItem(creator, post, linkUserItemTimeFromCreation)

        case CreatePictureEvent(creator, pic) =>

            // tidak perlu ini lagi, karena sudah langsung diproses ketika create di PictureDao.create
//            pic.origin match {
//                case ch:Group =>
//                    linkChannelItem(ch, pic, linkChannelItemTimeFromCreation)
//                case x =>
//                    warn(s"not linking to any group, pic.origin got: $x")
//            }


            linkUserItem(creator, pic, linkUserItemTimeFromCreation)

//        case CreatePictureGroupEvent(creator, picGroup) =>
//
//            // ini gak lagi diperlukan karena sudah langsung diproses ketika create di PictureGroup.create
////            picGroup.origin match {
////                case ch:Group =>
////                    linkChannelItem(ch, picGroup, linkChannelItemTimeFromCreation)
////                case _ =>
////            }
//
//            linkUserItem(creator, picGroup, linkUserItemTimeFromCreation)

        case ReplacePictureEvent(creator, replacerPic) =>

            /**
             * for usage example see: [[com.ansvia.digaku.model.Picture#setDeleted]]
             * picture group sudah didelete sebelum link user item dilakukan
             */
            //masukkan single picture penggantinya ke stream
            linkUserItem(creator, replacerPic, timeFromCreation = true)

//        case CreateEventEvent(creator, event) => linkUserItem(creator, event, linkUserItemTimeFromCreation)
//        case CreateDealEvent(creator, deal) => linkUserItem(creator, deal, linkUserItemTimeFromCreation)
//        case ShoutEvent(user, post, message, _) => {
//            StreamBuilder.linkShout(user, post, message)
//            debug("link shout: %s --> \"%s\" --> %s".format(user, message, post))
//        }
//        case RetalkEvent(user, retalked, group) =>
//        {
//            // link ke stream user (supporter dan member group tujuan retalk)
//            StreamBuilder.linkRetalk(user, retalked)
////            Group.getByName(group) map {ch=>
////                post match {
////                    case p:BaseModel[IDType] with HasOrigin[ GraphType] =>
////                        // link ke stream group
////                        linkChannelItem(p, false, ch)
////                }
////            }
//        }
//        case SupportUserEvent(userA, userB) =>
//            linkUserSupport(userA, userB)

        case JoinChannelEvent(user, group) =>
            StreamBuilder.linkUserJoin(user, group)

        case RestoreContentEvent(user, content) =>

            content match {
                case pic:Picture =>
                    if (pic.isEmbeddedPicture) {
                        pic.getVertex.pipe.in(EMBED).out(EMBED).hasNot("deleted", true).count() match {
                            case 0 =>
                                linkUserAndChannelItem(user, content.asInstanceOf[Streamable[IDType]], timeFromCreation = true)
                        }
                    } else {
                        linkUserAndChannelItem(user, content.asInstanceOf[Streamable[IDType]], timeFromCreation = true)
                    }
                case _ =>
                    linkUserAndChannelItem(user, content.asInstanceOf[Streamable[IDType]], timeFromCreation = true)
            }


        /**
         * move event
         */
        case MovePostEvent(post, group, moveType) =>
            //remove stream dari setiap user dan group
            deleteLinkUserItem(post.getId)
            //add to group
            linkChannelItem(group, post, timeFromCreation = true)
            //add stream ke user
            linkUserItem(post.creator, post, timeFromCreation = true)

//
//        case MoveEventEvent(event, group, moveType) =>
//            //remove stream dari setiap user dan group
//            deleteLinkUserItem(event.getId)
//            //add to group
//            linkChannelItem(group, event, timeFromCreation = true)
//            //add stream ke group target
//            linkUserItem(event.creator, event, timeFromCreation = true)

        case MovePictureEvent(picture, group, moveType) =>
            //remove stream dari setiap user dan group
            deleteLinkUserItem(picture.getId)
            //add to group
            linkChannelItem(group, picture, timeFromCreation = true)
            //add stream ke group target
            linkUserItem(picture.creator, picture, timeFromCreation = true)

        /**
         * delete event
         */
        case DeletePostEvent(user, postId) =>
            deleteLinkUserItem(postId)

        case DeleteEventEvent(user, event) =>
            deleteLinkUserItem(event.getId)

        case DeletePictureEvent(user, picture) =>
            deleteLinkUserItem(picture.getId)
//        case DeletePictureGroupEvent(user, pictureGroup) =>
//            deleteLinkUserItem(pictureGroup.getId)
        case BlockUserEvent(user, userTarget) =>
            deleteStreamFor(user, userTarget)
            deleteStreamFor(userTarget, user)
    }
    // scalastyle:on cyclomatic.complexity
}

private[stream] object StreamBuilderDispatcher extends DbAccess with Slf4jLogger {


    /**
     * digunakan untuk menambahkan stream content ke user dan group
     * ini digunakan untuk restore sebuah content
     * @param creator creator content
     * @param content content
     * @param timeFromCreation false jika creation stream menggunakan currentTime, true jika ingin disamakan dengan creation content
     */
    def linkUserAndChannelItem(creator:User, content:Streamable[IDType], timeFromCreation:Boolean=false) {

        linkUserItem(creator, content, timeFromCreation)
        linkChannelItem(null, content, timeFromCreation = true)

    }

    /**
     * menambahkan stream content ke group, tidak termasuk stream ke member-member-nya,
     * untuk stream ke membernya gunakan linkUserItem.
     * @param content content
     * @param timeFromCreation false jika creation stream menggunakan currentTime, true jika ingin disamakan dengan creation content
     * @param channelTarget optional group yang mau di hubungkan (default group origin nya)
     */
    def linkChannelItem(channelTarget:Forum, content:Streamable[IDType], timeFromCreation:Boolean=false) {

        StreamBuilder.linkChannelItem(channelTarget, content, timeFromCreation)

    }

    /**
     * Menghubungkan user --> post menjadi sebuah post stream.
     * Termasuk stream ke user yang mana member dari group post tersebut.
     * @param creator initiatior.
     * @param post post yang akan dihubungkan.
     */
    def linkUserItem(creator:User, post:Streamable[IDType], timeFromCreation:Boolean=false) {
        StreamBuilder.linkUserItem(creator, post, timeFromCreation)
    }

    /**
     * digunakan untuk menghapus edge stream untuk content tertentu
     * baik yang di-group maupun di home stream
     * @param postId content id
     */
    def deleteLinkUserItem(postId:IDType) {
        val postOp = db.getVertex(postId).toCC[BaseModel[IDType] with HasOrigin[GraphType]]
        postOp.foreach { post =>
            post.getVertex.pipe.inE(STREAM).remove()
            db.commit()
        }
    }

    /**
     * digunakan untuk menghapus edge stream untuk content tertentu di group stream
     * @param postId content id
     */
    def deleteLinkChannelItem(postId:IDType) {

        val postOp = db.getVertex(postId).toCC[BaseModel[IDType] with HasOrigin[GraphType]]
        postOp.foreach { post =>
            post.getVertex.pipe.inE(STREAM).as("se")
                .outV().has("kind", OriginKind.SUB_FORUM)
                .back("se")
                .asInstanceOf[GremlinPipeline[Vertex, Edge]].iterator().foreach(db.removeEdge)
            db.commit()
        }
    }

    def deleteStreamFor(user:User, userTarget:User) = {
        StreamBuilder.deleteStreamFor(user, userTarget)
    }

}


/**
 * Core engine untuk build stream untuk user dan channels.
 */
object StreamBuilder extends StreamBuilderEngine with DbAccess with Slf4jLogger with PerfTiming {


    val handledObjects = Seq("create-post", "create-deal",
        "create-picture", /*"create-event",*/ "shout", "join-group",
        "delete-post", "delete-picture", "delete-event", "restore-content",
        "move-post", "move-event", "move-picture", "retalk", "create-picture-group",
        "delete-picture-group", "block-user")

    /**
     * enable untuk mengaktifkan callback.
     */
    var enableCallback = false

    var useMultiThreading = true

    /**
     * apabila true maka proses linking/add-stream akan
     * dijalankan secara asynchronous.
     */
    var asyncLinking = true

    /**
     * callback ini akan dipanggil setiap ada aktivitas
     * ketika proses linking.
     * bisa diaktifkan/nonaktifkan melalui `enableCallback`
     */
    var callbacks = ArrayBuffer[(String, Vertex, Int, Streamable[IDType]) => Unit]()

    var duplicateDetector:DuplicateDetector = new AlwaysFalseDuplicateDetector()

    val postKindText = "post.kind"
    val targetIdText = "targetId"
    val creatorIdText = "creatorId"

    /**
     * menambahkan stream content ke group, ini tidak termasuk stream ke
     * member-membernya, untuk stream ke membernya ada di linkUserItem.
     * @param timeFromCreation false jika creation stream menggunakan currentTime,
     *                         true jika ingin disamakan dengan creation content
     * @param streamable streamable object to link with.
     * @param channelTarget optional group yang mau di hubungkan (default group origin nya)
     */
    def linkChannelItem(channelTarget:Forum, streamable:Streamable[IDType],
                        timeFromCreation:Boolean=false,
                        noTx:Boolean=false) {

        try {

            var postV = db.getVertex(streamable.getId)
            var originIsChannel = false
            var originV = channelTarget match {
                case null =>
                    streamable.origin match {
                        case ch: Forum =>
                            originIsChannel = true
                            ch.getVertex
                        case user: User =>
                            user.getVertex
                        case null =>
                            throw NotExistsException("No origin for " + streamable)
                    }
                case _ =>
                    originIsChannel = true
                    channelTarget.getVertex
            }

            if (originIsChannel) {
                originV = db.getVertex(originV.getId)
                postV = db.getVertex(postV.getId)

                originV.toCC[Origin[GraphType] with CollectionVertex].foreach { _origin =>
                    postV.toCC[Streamable[IDType]].foreach { _streamable =>
                        val streamE = StreamBuilder.addToStream(_origin, _streamable, timeFromCreation)

                        streamE.setProperty("post.sticky", false)
                        streamE.setProperty("locked", false)
                        streamE.setProperty(postKindText, PostKind.kindStrClassToInt(streamable.getClass.getName))
                        streamE.setProperty("originId", channelTarget.getId)
                        streamE.setProperty(targetIdText, streamable.getId)

                    }
                }

                if (!noTx) {
                    db.commit()
                }
            }
        }catch{
            case e:DigakuException =>
                error(e.getMessage)
                error(e.getStackTraceString)
        }
    }

    var beforeAddToStreamHandler:(Origin[GraphType] with CollectionVertex, Streamable[IDType], Boolean) => Unit
        = (_, _, _) => Unit

    /**
     * menambahkan content tertentu kedalam user stream.
     * non transaction, harus dijalankan dalam transaction.
     * @param origin origin
     * @param streamable streamable object.
     * @param timeFromCreation false jika creationTime stream menggunakan currentTime, true jika ingin disamakan dengan creation content
     * @return
     */
    def addToStream(origin:Origin[GraphType] with CollectionVertex, streamable:Streamable[IDType], timeFromCreation:Boolean=false) = {
        StreamBuilder.duplicateDetector.markStreamExists(origin, streamable)
        addToStreamEx(origin, streamable, timeFromCreation, db)
    }

    /**
     * Add to stream routine with custom transactional graph context.
     * @param origin content origin.
     * @param streamable streamable object.
     * @param timeFromCreation whether to use time order from content creation.
     * @param t transactional graph context.
     * @return added stream edge.
     */
    def addToStreamEx(origin:Origin[GraphType] with CollectionVertex, streamable:Streamable[IDType],
                    timeFromCreation:Boolean=false, t:TransactionalGraphType) = {

        beforeAddToStreamHandler(origin, streamable, timeFromCreation)

        val ed = timeFromCreation match {
            case true =>
                val dateTime = new DateTime(streamable.creationTime)
                val year = dateTime.getYear
                val month = dateTime.getMonthOfYear

//                origin.reload()
                val _cv = t.getVertex(origin.getYMPartEx(CollectionVertex.Kind.STREAM, year, month, t).getId)
                _cv.addEdge(STREAM, streamable.getVertex)
            case _ =>
//                origin.reload()
                val _cv = t.getVertex(origin.getYMPartEx(CollectionVertex.Kind.STREAM,
                    Digaku.engine.dateUtils.getCurrentYear, Digaku.engine.dateUtils.getCurrentMonth, t).getId)
                _cv.addEdge(STREAM, streamable.getVertex)
        }

        // digunakan untuk filtering stream tertentu dari edge-nya
        // penggunaan see [[com.ansvia.digaku.model.Streamable#removeStreamItem]]
        ed.setProperty(targetIdText, streamable.getId)

        if (timeFromCreation){
            ed.setProperty("timeOrder", streamable.getVertex.getOrElse("creationTime", 0L))
        }else{
            ed.setProperty("timeOrder", System.currentTimeMillis())
        }

        // set property streamKind untuk filtering stream
        ed.setProperty("streamKind", StreamKind.streamableToStreamKindInt(streamable))

        streamable match {
            case x:Post =>
                val postKind = PostKind.kindStrClassToInt(t.getVertex(streamable.getId).getOrElse("_class_", ""))
                ed.setProperty(postKindText, postKind)
                ed.setProperty(creatorIdText, x.creator.getId)
//            case x:Event =>
//                val postKind = PostKind.kindStrClassToInt(t.getVertex(streamable.getId).getOrElse("_class_", ""))
//                ed.setProperty(postKindText, postKind)
//                ed.setProperty(creatorIdText, x.creator.getId)
            case x:PictureBase =>
                val postKind = PostKind.kindStrClassToInt(t.getVertex(streamable.getId).getOrElse("_class_", ""))
                ed.setProperty(postKindText, postKind)
                ed.setProperty(creatorIdText, x.creator.getId)
            case _ =>
        }
        origin match {
            case ch:Forum =>
                ch.incrementContentCount()
            case _ =>
        }

        ed
    }

    /**
     * Menghubungkan user --> post menjadi sebuah post stream
     * tetapi diambil dari group.
     *
     * Digunakan untuk me-merge stream dari group ke user stream
     * terutama apabila user baru stream masih kosong dan ketika
     * dia join maka akan otomatis muncul stream-nya.
     *
     * @param user user yang join.
     * @param group group yang di-join-i.
     */
    def linkUserJoin(user:User, group:Forum) {

        require(user != null, "user cannot be null")
        require(group != null, "group cannot be null")

        info("linking user join stream %s <-- %s".format(user, group))

        group.reload()

        val postStreamV:Iterator[Vertex] = group.getVertex.pipe.in(ORIGIN)
            .has("deleted", false)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .range(0, 99)
            .iterator()

        user.reload()
        val userV = user.getVertex
        var count = 0

        for (pV <- postStreamV){

            // add hanya kalo belum ada

                userV.toCC[Origin[GraphType] with CollectionVertex].map { _user =>
                    pV.toCC[Streamable[IDType]].map { _streamable =>

                        if (!duplicateDetector.isStreamExists(_user, _streamable)){

                            StreamBuilder.duplicateDetector.markStreamExists(_user, _streamable)

                            StreamBuilder.addToStreamEx(_user, _streamable, timeFromCreation = true, db)

                        }

                    }
                }

                count = count + 1
                if (count > 20){
                    count = 0
                    db.commit()
                }

        }


        db.commit()

    }

    /**
     * Implement ini apabila ingin menghandle
     * operasi link stream sebelum melakukan ke semua members-nya.
     */
    var handleChannelStreamBefore:(User, Origin[GraphType], Streamable[IDType]) => List[IDType] = (_, _, _) => Nil
    var handleSupportingStreamBefore:(User, Origin[GraphType], Streamable[IDType]) => List[IDType] = (_, _, _) => Nil


    implicit private lazy val ec:ExecutionContext = ExecutionContext.fromExecutor(new scala.concurrent.forkjoin.ForkJoinPool)

    // scalastyle:off cyclomatic.complexity
    /**
     * Menghubungkan user --> post menjadi sebuah post stream.
     * Ini digunakan untuk membuat stream home user.
     *
     * @param creator initiatior.
     * @param streamable streamable object yang akan dihubungkan.
     * @param timeFromCreation flags to link using creation time as the time of the edge stamp.
     * @param noTx don't commit to db internally.
     */
    def linkUserItem(creator:User, streamable:Streamable[IDType],
                     timeFromCreation:Boolean=false, noTx:Boolean=false) {

        debug("linking user stream: " + creator + " --> " + streamable + " , TFC: " + timeFromCreation)

        try {

            var postV:Vertex = db.getVertex(streamable.getId)

            if (postV == null) {
                throw NotExistsException("cannot get streamable with id " + streamable.getId +
                    ", may object didn't exists")
            }

            val _streamable = postV.toCC[Streamable[IDType]].getOrElse {
                throw new DigakuException("Cannot serialize Streamable from " + postV)
            }
            var creatorV:Vertex = null

            var originIsChannel = false
            var originIsPrivate = false

            val origin = _streamable.origin

            if (origin == null) {
                throw NotExistsException("No origin for " + streamable + ". Creator: " + creator)
            }

            var originV:Vertex = null

            def reloadVertices(){
                originV = _streamable.origin match {
                    case ch: Forum =>
                        originIsChannel = true
                        val _v = ch.reload().getVertex
                        originIsPrivate = _v.getOrElse("privated", true)
                        _v
                    case user: User =>
                        user.reload().getVertex
                }
                creatorV = db.getVertex(creator.getId)
                postV = db.getVertex(streamable.getId)
            }

            reloadVertices()


//          Ini tidak diperlukan lagi untuk creator karena seharusnya sudah dihandle oleh Dao ketika create-nya
//            if (creatorV != null){
//
//                // jangan masukkan ke stream user tersebut kalo udah ada
//                if (!creatorV.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM)
//                    .range(0,1).out(STREAM).range(0,20).retain(List(postV)).iterator().hasNext){
//
//                    creatorV.toCC[Origin[GraphType] with CollectionVertex].map { _creator =>
//                        postV.toCC[Streamable[IDType]].map { _streamable =>
//                            debug("adding data stream " + _streamable + " in " + _creator)
//                            addToStream(_creator, _streamable, timeFromCreation)
//                        }
//                    }
//
//                }
//            }else{
//                warn("Cannot retrieve post creator for " + postV + ", ignored building stream for creator.")
//            }

            var offset = 0
            val limit = 500

            var processedUsersId = new mutable.HashSet[IDType]()

            try {
                // panggil "before" hook
                handleSupportingStreamBefore(creator, origin, _streamable)
                    .foreach { id => processedUsersId += id }
            }catch{
                case e:Exception =>
                    error(e.getMessage)
                    error(e.getStackTraceString)
            }

            if (!noTx) {
                db.commit()
            }

            var count = 0
            // berdasarkan kesepakatan terakhir bahwa semua stream berasal dari user yg disupport dan group,
            while(creatorV.pipe.inE(SUPPORT).range(offset, offset + 1).hasNext){
                creatorV.pipe.inE(SUPPORT).range(offset, offset + limit).outV().iterator().foreach { supV =>

                    try {

                        /**
                         * kalo kebetulan post ada di group yang privat
                         * sementara supporter bukan member dari group tersebut
                         * maka jangan masukkan post ke stream user tersebut.
                         */
                        if (originIsChannel && originIsPrivate &&
                            !supV.pipe.outE(JOIN).has("targetId", _streamable.origin.getId).iterator().hasNext) {
                            throw IgnoredException()
                        }

                        if (processedUsersId.contains(supV.getId)) {
                            throw IgnoredException()
                        }

                        if (enableCallback){
                            callbacks.foreach(_.apply("supporters", supV, count, streamable))
                        }

                        supV.toCC[Origin[GraphType] with CollectionVertex].map { _sup =>
                            postV.toCC[Streamable[IDType]].map { _streamable =>
                                if (!duplicateDetector.isStreamExists(_sup, _streamable)){
                                    processedUsersId += _sup.getId
                                    debug(s"adding data stream by supporting ${_sup} -> $creator")
                                    addToStream(_sup, _streamable, timeFromCreation)
                                }
                            }
                        }

                        count = count + 1

                        if (count % limit == 0){
                            if (!noTx) {
                                db.commit()
                            }
                        }

                    }catch{
                        case e:IgnoredException =>
                    }

                }
                db.commit()
                offset = offset + limit
            }
            // EOL stream from supported users

            /**
             * Broadcast ke member group kalo origin-nya adalah group
             */
            if (originIsChannel){
                originV = db.getVertex(originV.getId)
                postV = db.getVertex(postV.getId)

                if (originV == null) {
                    throw NotExistsException("Cannot retrieve origin with id " + originV.getId)
                }
                if (postV == null) {
                    throw NotExistsException("Cannot retrieve post with id " + postV.getId)
                }

                debug(s"processing user stream from group $origin ...")



                try {
                    // panggil "before" hook
                    handleChannelStreamBefore(creator, origin, _streamable)
                        .foreach { id => processedUsersId += id }
                }catch{
                    case e:Exception =>
                        error(e.getMessage)
                        error(e.getStackTraceString)
                }

                offset = 0
                val membersIds = originV.query().direction(Direction.IN)
                    .has("label", JOIN).vertices().map(_.getId).toList

                val totalCount = membersIds.length


                if (useMultiThreading && totalCount > 20){

                    val divider = {
                        if (totalCount > 5000) {
                            6
                        } else {
                            4
                        }
                    }
                    val div = totalCount / divider

                    lazy val latch = new CountDownLatch(divider + 1)

                    debug("processing stream with these distribution loads:")
                    for ( di <- 0 to divider ){
                        Future {

                            try {

                                val start = di * div
                                val until = start + div

                                debug(s" $di sliced from $start to $until")
                                Thread.sleep(100)

                                val currentWorkerId = di
                                debug(s"#$di starting calculation $start to $until ...")

                                try {


                                    val _postV = db.getVertex(postV.getId)

                                    assert(_postV != null, "_postV is null")

                                    val _streamable = _postV.toCC[Streamable[IDType]].get

                                    var count = 0

                                    def processInternal(memberV:Vertex){

                                        assert(memberV != null, "memberV is null")

                                        val isBlocked = memberV.toCC[User].exists(u => u.isBlocked(creator) || creator.isBlocked(u))

                                        // jangan proses kalo member-nya adalah user yang create itu sendiri
                                        // dan jangan proses jika member diblock atau memblock creator
                                        processedUsersId.synchronized {
                                            if (!(creatorV != null && memberV.getId == creatorV.getId) && !isBlocked
                                                && !processedUsersId.contains(memberV.getId)){

                                                memberV.toCC[Origin[GraphType] with CollectionVertex].map { member =>
                                                    if (!duplicateDetector.isStreamExists(member, _streamable)){

                                                        // didisable karena sudah ada di dalam `addToStream`
//                                                        duplicateDetector.markStreamExists(member, _streamable)

                                                        processedUsersId += member.getId
                                                        debug("#" + currentWorkerId + " adding data stream " + _streamable.getId + " in " + member + " from #" + origin.getName)
                                                        addToStream(member, _streamable, timeFromCreation)

                                                    }
                                                }

                                            }
                                        }

                                    }

                                    for (memberId <- membersIds.slice(start, until)){
                                        processInternal(db.getVertex(memberId))

                                        count = count + 1
                                        //
                                        if (!noTx){
                                            if (count % 100 == 0){
                                                debug(s"#$currentWorkerId committing...")
                                                db.commit()
                                                _streamable.reload()
                                                //                                        reloadVertices()
                                            }
                                        }
                                        Thread.sleep(5)

                                    }

                                    // finishing
                                    for (memberId <- membersIds.slice(until, until + totalCount)){
                                        processInternal(db.getVertex(memberId))

                                        count = count + 1
                                        //
                                        if (!noTx){
                                            if (count % limit == 0){
                                                debug(s"#$currentWorkerId committing...")
                                                db.commit()
                                                _streamable.reload()
                                                //                                        reloadVertices()
                                            }
                                        }

                                    }

                                }catch{
                                    case e:Exception =>
                                        error(e.getMessage)
                                        error(e.getStackTraceString)
                                }

                                db.commit()
                                debug(s"#$currentWorkerId processing user stream from group $origin done.")

                            }catch{
                                case e:Exception =>
                                    error(e.getMessage)
                                    error(e.getStackTraceString)
                            }finally{
                                if (!asyncLinking) {
                                    latch.countDown()
                                }
                            }
                        }
                        Thread.sleep(500) // pastikan Future dapat index yang benar sebelum di-overwrite sama main thread loop

                    }

                    if (!asyncLinking) {
                        latch.await()
                    }

                    debug(s"stream processing for group $origin done.")
                }else{

                    var count = 0
                    val _postV = db.getVertex(postV.getId)

                    assert(_postV != null, "_postV is null")

                    val _streamable = _postV.toCC[Streamable[IDType]].get

                    for (memberId <- membersIds){

                        val memberV = db.getVertex(memberId)

                        assert(memberV != null, "memberV is null")

                        val isBlocked = memberV.toCC[User].exists(u => u.isBlocked(creator) || creator.isBlocked(u))

                        // jangan proses kalo member-nya adalah user yang create itu sendiri
                        // dan jangan proses jika member diblock atau memblock creator
                        if (!(creatorV != null && memberV.getId == creatorV.getId) && !isBlocked
                            && !processedUsersId.contains(memberV.getId)){

                            // pastikan member belum punya stream-nya
                            //                            if (!memberV.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM)
                            //                                .range(0,1).out(STREAM).range(0,20).retain(List(postV))
                            //                                //.has("id", postV.getId)
                            //                                .iterator().hasNext){

                            //                                if (enableCallback){
                            //                                    callbacks.foreach(_.apply("channel_members " + originV.getOrElse("name",""),
                            //                                        memberV, count, streamable))
                            //                                }

                            //                                timing("inserting stream"){
                            memberV.toCC[Origin[GraphType] with CollectionVertex].map { member =>
                                if (!duplicateDetector.isStreamExists(member, _streamable)){

//                                    duplicateDetector.markStreamExists(member, _streamable)

                                    processedUsersId += member.getId
                                    debug("#main adding data stream " + _streamable.getId + " in " + member + " from #" + origin.getName)
                                    addToStream(member, _streamable, timeFromCreation)
                                }
                            }
                            //                                }

                            count = count + 1
                            //
                            if (!noTx){
                                if (count % 100 == 0){
                                    debug("committing...")
                                    db.commit()
                                    _streamable.reload()
                                    //                                        reloadVertices()
                                }
                            }

                        }
                    }

                    db.commit()
                }

                if (!noTx) {
                    db.commit()
                }
            }

        }catch{
            case e:DigakuException =>
                error(e.getMessage)
                error(e.getStackTraceString)
        }
    }
    // scalastyle:on cyclomatic.complexity


    /**
     * Menghubungkan user --> post menjadi sebuah post stream.
     * @param user initiatior (orang yang nge-shout).
     * @param post post yang akan dihubungkan.
     */
    def linkShout(user:User, post:Post, shoutMessage:String) {

        /**
         * Broadcast ke semua supporter-nya user
         */

        var count = 0
        var postV = db.getVertex(post.getId)
        val userV = db.getVertex(user.getId)
        var originIsChannel = false
        var originIsPrivate = false
        val originId: IDType = post.origin.getId
        var originV = post.origin match {
            case ch: Forum =>
                originIsChannel = true
                originIsPrivate = ch.privated
                ch.getVertex
            case user: User =>
                user.getVertex
        }

        val nowMilis = Digaku.engine.dateUtils.nowMilis

        if (userV != null){
            userV.toCC[Origin[GraphType] with CollectionVertex].foreach { _origin =>
                postV.toCC[Streamable[IDType]].foreach { _streamable =>
                    if (!duplicateDetector.isStreamExists(_origin, _streamable)){

//                        duplicateDetector.markStreamExists(_origin, _streamable)

                        val ed = StreamBuilder.addToStream(_origin, _streamable)
                        ed.setProperty("isShout", true)
                        ed.setProperty("shout.message", shoutMessage)
                        ed.setProperty("shout.userId", user.getId)
                        ed.setProperty("shout.timestamp", nowMilis)
                    }
                }
            }
        }

        userV.pipe.in(SUPPORT).iterator().foreach { supV =>

            try {

                /**
                 * kalo kebetulan post ada di group yang privat
                 * sementara supporter bukan member dari group tersebut
                 * maka jangan masukkan post ke stream user tersebut.
                 */
                if (originIsChannel && originIsPrivate &&
                    !supV.pipe.outE(JOIN).has("targetId", originId).iterator().hasNext) {
                    throw IgnoredException()
                }

                supV.toCC[Origin[GraphType] with CollectionVertex].foreach { _origin =>
                    postV.toCC[Streamable[IDType]].foreach { _streamable =>

                        if (!duplicateDetector.isStreamExists(_origin, _streamable)){

//                            duplicateDetector.markStreamExists(_origin, _streamable)

                            val ed = StreamBuilder.addToStream(_origin, _streamable)

                            ed.setProperty("isShout", true)
                            ed.setProperty("shout.message", shoutMessage)
                            ed.setProperty("shout.userId", user.getId)
                            ed.setProperty("shout.timestamp", nowMilis)
                        }
                    }
                }

                count = count + 1
                if (count > 100){
                    count = 0
                    db.commit()
                }

            }catch{
                case e:IgnoredException =>
            }

        }

        db.commit()

        count = 0

        /**
         * Broadcast ke member group kalo origin-nya adalah group
         */
        if (originIsChannel) {
            val isShoutText = "isShout"
            val shoutUserIdText = "shout.userId"
            originV = db.getVertex(originV.getId)
            postV = db.getVertex(postV.getId)

            originV.pipe.in(JOIN).iterator().foreach { memberV =>

            // pastikan sebelumnya belum ada, untuk menghindari double
            // shout berturut-turut

                if (!memberV.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM).range(0,1).outE(STREAM)
                    .has(isShoutText, true)
                    .has(shoutUserIdText, user.getId)
                    .range(0,1)
                    .inV().has("id", postV.getId).iterator().hasNext)
                {

                    memberV.toCC[Origin[GraphType] with CollectionVertex].foreach { _origin =>
                        postV.toCC[Streamable[IDType]].foreach { _streamable =>
                            if (!duplicateDetector.isStreamExists(_origin, _streamable)){

//                                duplicateDetector.markStreamExists(_origin, _streamable)

                                val ed = StreamBuilder.addToStream(_origin, _streamable)
                                ed.setProperty(isShoutText, true)
                                ed.setProperty("shout.message", shoutMessage)
                                ed.setProperty(shoutUserIdText, user.getId)
                                ed.setProperty("shout.timestamp", nowMilis)
                            }
                        }
                    }

                    count = count + 1
                    if (count > 100){
                        count = 0
                        db.commit()
                    }

                }

            }
            db.commit()
        }

    }

    /**
     * digunakan untuk delete seluruh stream dari userTarget
     * ini di gunakan ketika block sebuah user yang mengharuskan men-delete
     * seluruh stream yang ada dari user yang di block
     * @param user user yang memiliki stream
     * @param userTarget user target yang akan di hapus dari stream
     */
    def deleteStreamFor(user:User, userTarget:User) = {
        db.getVertex(user.getId).pipe
            .out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM)
            .outE(STREAM).has("creatorId", userTarget.getId).remove()

        db.commit()
    }

}

