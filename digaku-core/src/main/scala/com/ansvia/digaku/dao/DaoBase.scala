/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.Types.IDType
import com.ansvia.digaku.exc.{NotExistsException, RestrictedException}
import com.ansvia.digaku.helpers.{DbAccess, RootVertex}
import com.ansvia.digaku.model._
import com.tinkerpop.blueprints.{Edge, Graph, Vertex}


/**
 * Author: robin
 *
 */
trait AbstractDaoBase[idT, dbT <: Graph, modelT <: BaseModel[idT]] extends DbAccess with RootVertex {

    import com.ansvia.digaku.model.Label.DAO_LIST
    import com.ansvia.graph.BlueprintsWrapper._

    /**
     * override this as your wish.
     */
    val rootVertexLabel = DAO_LIST

    def getById[B >: modelT](id:idT)(implicit m:Manifest[B]):Option[B] = {
        val x = db.getVertex(id)
        if (x != null) {
            x.toCC[B]
        } else {
            None
        }
    }


    def delete(obj:modelT)(implicit m:Manifest[modelT]){
        val id = obj.getId
        val v = db.getVertex(id)

        if (v == null){
            throw NotExistsException("Object didn't exists.")
        }
        val className = m.runtimeClass.getName

        if (v.getOrElse("_class_", "") != className)
            throw RestrictedException("Cannot delete model object " +
                "with class " + className + ", this Dao (" + this.getClass.getName + ") is not for that model")

        v.pipe.bothE().remove()
        db.removeVertex(v)

        db.commit()


    }

    def deleteById(id:idT)(implicit m:Manifest[modelT]){

        val v = db.getVertex(id)

        if (v == null){
            throw NotExistsException("Vertex object with id %s didn't exists.".format(id))
        }else if (v.getOrElse("_class_","") != m.runtimeClass.getName){
            throw RestrictedException("Cannot delete model object " +
                "with class " + m.runtimeClass.getName + ", this Dao (" + this.getClass.getName + ") is not for that model")
        }

        v.pipe.bothE().remove()

        db.removeVertex(v)

        db.commit()
    }

    def exists(id:idT):Boolean = {
        db.getVertex(id) != null
    }


}

trait DaoBase[dbT <: Graph, modelT <: BaseModel[IDType]]
    extends AbstractDaoBase[IDType, dbT, modelT] with DbAccess with RootVertex {



    lazy val daoListIndexName:String = getClass.getCanonicalName.replaceAll("\\$$","")

    protected lazy val daoListIndex:DaoListIndex = DaoListIndexFactory
            .build(daoListIndexName, partitioned = true)

    var disableDaoListIndex = false

    /**
     * add vertex to root, all dao create operation should
     * use this method to add their ref to root vertex.
     * non transactional, should be executed inside of
     * transact.
     * @param vertex vertex to add.
     * @return
     */
    override def addToRoot(vertex: Vertex): Edge = {
        require(vertex != null, "vertex cannot be null")

        if (!disableDaoListIndex){
            assert(daoListIndex != null)

            // store to dao list index
            daoListIndex.store(vertex)
        }

        super.addToRoot(vertex)
    }


    def getListLeft(sinceId:Option[IDType], toId:Option[IDType], limit:Int)(implicit m:Manifest[modelT]) = {
        require(!disableDaoListIndex, "dao list index are disabled")
        assert(daoListIndex != null)
        daoListIndex.slice[modelT](sinceId, toId, Some(limit), reverse = false)
    }


    def getListRight(sinceId:Option[IDType], toId:Option[IDType], limit:Int)(implicit m:Manifest[modelT]) = {
        require(!disableDaoListIndex, "dao list index are disabled")
        assert(daoListIndex != null)
        daoListIndex.slice[modelT](sinceId, toId, Some(limit), reverse = true)
    }


    override def delete(obj: modelT)(implicit m: Manifest[modelT]){
        if (!disableDaoListIndex)
            assert(daoListIndex != null, "dao list index not initialized")

        val id = obj.getId
        super.delete(obj)(m)

        if (!disableDaoListIndex){
            // remove from dao list index
            daoListIndex.removeById(id)
        }
    }

    override def deleteById(id: IDType)(implicit m:Manifest[modelT]){
        if (!disableDaoListIndex)
            assert(daoListIndex != null, "dao list index not initialized")

        super.deleteById(id)

        if (!disableDaoListIndex){
            // remove from dao list index
            daoListIndex.removeById(id)
        }
    }

    override def getCount = {
        require(!disableDaoListIndex, "dao list index are disabled")
        assert(daoListIndex != null, "dao list index not initialized")
        daoListIndex.getCount
    }

    def getDaoListIndex = daoListIndex


}
