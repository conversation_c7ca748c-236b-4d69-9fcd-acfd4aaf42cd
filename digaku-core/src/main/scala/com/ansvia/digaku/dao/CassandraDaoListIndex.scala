/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.persistence.CassandraDriver
import com.netflix.astyanax.model.{Column, ColumnFamily}
import com.netflix.astyanax.serializers.{ComparatorType, LongSerializer, StringSerializer}
import com.ansvia.digaku.config.Config
import com.tinkerpop.blueprints.Vertex
import com.netflix.astyanax.MutationBatch
import com.tinkerpop.blueprints.util.wrappers.id.IdVertex
import com.ansvia.digaku.Types._
import com.netflix.astyanax.util.RangeBuilder
import com.ansvia.digaku.model.BaseModel
import scala.collection.mutable
import com.netflix.astyanax.retry.ExponentialBackoff

/**
 * Author: robin
 *
 */


case class CassandraDaoListIndex(seeds:String, clusterName:String, keyspaceName:String, keyName:String,
                                 replicationStrategy:String, replicationStrategyOpts:String)
    extends DaoListIndex with Paging with DbAccess with Slf4jLogger {

    import scala.collection.JavaConversions._

    private val cfName = "daolistindex"
    private val cfNameCounter = cfName + "_counter"
    private val keyNameIdRaw = keyName + "|RawId"
    private val keyNameIdGraph = keyName + "|IdGraph"
    private val keyNameCounter:java.lang.String = keyName
    lazy private val ctx = CassandraDriver.getContext(clusterName, keyspaceName, seeds,
        replicationStrategy, replicationStrategyOpts)
    lazy private val keyspace = ctx.keyspace.getClient
    private val COUNTER_KEY = "count"

    private lazy val columnFamily = new ColumnFamily[String, java.lang.Long](
        cfName,
        StringSerializer.get(),
        LongSerializer.get(),
        StringSerializer.get())

    private lazy val columnFamilyCounter = new ColumnFamily[java.lang.String, java.lang.String](
        cfNameCounter,
        StringSerializer.get(),
        StringSerializer.get())


    lazy val ensureReady = {
        //        ctx.ensureColumnFamilyExists(cfName,"digaku_test","org.apache.cassandra.db.marshal.LongType")

        // ensure keyspace exists

        val ksDef = ctx.cluster.getClient.describeKeyspace(keyspaceName)

        //        var found = false
        //        if (ksDef != null){
        //            for (cdef <- ksDef.getColumnFamilyList){
        //                found |= cdef.getName.equals(cfName)
        //            }
        //        }

        val gcGraceSeconds = if (Config.testMode) 0 else 60*60*24 // sehari

        if (!ksDef.getColumnFamilyList.exists(_.getName.equalsIgnoreCase(cfName))){
            //            println("gcGraceSeconds: " + gcGraceSeconds)
            val cfDef = ctx.cluster.getClient.makeColumnFamilyDefinition()
                .setName(cfName)
                .setKeyspace(keyspaceName)
                // apabila dalam mode test grace seconds gak diperlukan disable dengan 0
                // tapi apabila tidak maka set gc grace seconds dalam 1 hari
                .setGcGraceSeconds(gcGraceSeconds)
                .setKeyValidationClass(ComparatorType.UTF8TYPE.getClassName)
                .setComparatorType("org.apache.cassandra.db.marshal.LongType")
                .setCompressionOptions(Map("sstable_compression" -> "org.apache.cassandra.io.compress.SnappyCompressor",
                "chunk_length_kb" -> "64"))
            ctx.cluster.getClient.addColumnFamily(cfDef)
        }

        //        ksDef.getColumnFamilyList.foreach(println)
        if (!ksDef.getColumnFamilyList.exists(_.getName.equalsIgnoreCase(cfNameCounter))){
            val cfDef = ctx.cluster.getClient.makeColumnFamilyDefinition()
                .setName(cfNameCounter)
                .setKeyspace(keyspaceName)
                .setGcGraceSeconds(gcGraceSeconds)
                //                .setComparatorType(ComparatorType.UTF8TYPE.getClassName)
                .setKeyValidationClass(ComparatorType.UTF8TYPE.getClassName)
                .setDefaultValidationClass(ComparatorType.COUNTERTYPE.getClassName)
                .setCompressionOptions(Map("sstable_compression" -> ""))
            ctx.cluster.getClient.addColumnFamily(cfDef)
        }




    }


    def normalize(){
        // @TODO(*): remove this backward compatibility
        // safe to remove after 08 Apr 2014
        // begin backward compatibility ---------------------------

        val keyNameNoS = keyName.replaceAll("\\$$","")
        val _keyNameRawId = keyNameNoS + "|RawId"
        val _keyNameIdGraph = keyNameNoS + "|IdGraph"

        if (keyspace.prepareQuery(columnFamily).getKey(_keyNameIdGraph).getCount.execute().getResult.toInt == 0){

            pagingRaw(None, None, 1000, None, false){ case (page, data) =>
                def _store(v:Vertex, mb:MutationBatch){
                    val idFromIdGraph = v.getId.asInstanceOf[java.lang.Long]
                    val idOriginalGraph = getOriginalGraphId(v)
                    mb.withRow(columnFamily, _keyNameIdGraph).putColumn(idFromIdGraph,"")
                    mb.withRow(columnFamily, _keyNameRawId).putColumn(idOriginalGraph,"")
                }
                val mb:MutationBatch = keyspace.prepareMutationBatch()
                mb.withRetryPolicy(new ExponentialBackoff(250L,10))
                data.foreach { v =>
                    val repr = v.getProperty[String]("_class_") match {
                        case "com.ansvia.digaku.model.User" => v.getProperty[String]("name")
                        case "com.ansvia.digaku.model.Forum" => v.getProperty[String]("name")
                        case "com.ansvia.digaku.model.Article" => v.getProperty[String]("title")
//                        case "com.ansvia.digaku.model.SimplePost" => v.getProperty[String]("content")
                        case "com.ansvia.digaku.model.App" => v.getProperty[String]("name")
                        case "com.ansvia.digaku.model.GeneralTrophy" => v.getProperty[String]("name")
                        case "com.ansvia.digaku.model.PrivateMessage" => v.getProperty[String]("content")
                        case x => x
                    }
//                    debug("processing " + v + " - " + repr)
                    _store(v, mb)
                }
//                debug("committing...")
                mb.execute()
                true
            }

        }

        // end of backward compatibility ---------------------------
    }



    private def getOriginalGraphId(v:Vertex):java.lang.Long = {
        v match {
            case iv:IdVertex =>
                iv.getBaseVertex.getId.asInstanceOf[java.lang.Long]
            case nv:Vertex =>
                nv.getId.asInstanceOf[java.lang.Long]
        }
    }



    def store(v: Vertex){
        ensureReady
        val idFromIdGraph = v.getId.asInstanceOf[java.lang.Long]
        val idOriginalGraph = getOriginalGraphId(v)
        val mb = keyspace.prepareMutationBatch()
        mb.withRetryPolicy(new ExponentialBackoff(250L,10))
        mb.withRow(columnFamily, keyNameIdGraph).putColumn(idFromIdGraph,"")
        mb.withRow(columnFamily, keyNameIdRaw).putColumn(idOriginalGraph,"")
        mb.withRow(columnFamilyCounter, keyNameCounter).incrementCounterColumn(COUNTER_KEY,1)
        mb.execute()
    }


    def pagingRaw(sinceId: Option[IDType],
                  toId:Option[IDType],
                  limitPerPage:Int,
                  limit: Option[Int],
                  reverse:Boolean)(func: (Int, Iterator[Vertex]) => Boolean){

        import scala.collection.JavaConversions._
        ensureReady

        lazy val rv1 = {
            keyspace.prepareQuery(columnFamily)
                .getRow(keyNameIdGraph)
                .withColumnRange(new RangeBuilder().setLimit(1).setReversed(reverse).build())
                .execute().getResult
        }


        val rb = new RangeBuilder()

        if (sinceId.isEmpty)
            rv1.map(c => rb.setStart(c.getRawName))
        else
            sinceId.map(rb.setStart)

        if (toId.isDefined)
            toId.map(rb.setEnd)


        rb.setLimit(limitPerPage)
        rb.setReversed(reverse)

        val query = keyspace.prepareQuery(columnFamily)
            .getRow(keyNameIdGraph)
            .autoPaginate(true)
            .withColumnRange(rb.build())

        var rv = query.execute().getResult
        var count = 0
        var page = 1
        var _done = false
        while(!rv.isEmpty && !_done){

            if (rv.size() > 0){
                val slicedData = rv.iterator().map(c => db.getVertex(c.getName)).filterNot(_==null)

                limit.map { _limit =>
                    if (rv.size() + count > _limit){
                        val delta = limit.getOrElse(100) - count
//                        println("delta: " + delta)
                        _done = !func(page, slicedData.slice(0,delta))
                        count = (count + rv.size()) - delta
                    }else{
                        _done = !func(page, slicedData)
                        count = count + rv.size()
                    }
                }.getOrElse {
                    _done = !func(page, slicedData)
                    count = count + rv.size()
                }
            }

            page = page + 1



            if (!_done && limit.isDefined)
                _done = count >= limit.getOrElse(100)

            if (!_done){
                rv = query.execute().getResult
                _done = rv.size() == 0
            }
        }

    }

    def paging[T <: BaseModel[IDType] : Manifest](sinceId: Option[IDType],
                                                  toId:Option[IDType],
                                                  limitPerPage:Int,
                                                  limit: Option[Int],
                                                  reverse:Boolean)(func: (Int, Iterator[T]) => Boolean){
        import scala.collection.JavaConversions._
        ensureReady

        lazy val rv1 = {
            keyspace.prepareQuery(columnFamily)
                .getRow(keyNameIdGraph)
                .withColumnRange(new RangeBuilder().setLimit(1).setReversed(reverse).build())
                .execute().getResult
        }


        val rb = new RangeBuilder()

        if (sinceId.isEmpty)
            rv1.map(c => rb.setStart(c.getRawName))
        else
            sinceId.map(rb.setStart)

        if (toId.isDefined)
            toId.map(rb.setEnd)


        rb.setLimit(limitPerPage)
        rb.setReversed(reverse)

        val query = keyspace.prepareQuery(columnFamily)
            .getRow(keyNameIdGraph)
            .autoPaginate(true)
            .withColumnRange(rb.build())

        var rv = query.execute().getResult
        var count = 0
        var page = 1
        var _done = false
        while(!rv.isEmpty && !_done){

            val rvSize = rv.size()

            if (rvSize > 0){
                val slicedData = rv.iterator().flatMap(c => getById[T](c.getName))

//                limit.map { _limit =>
//                    if (rv.size() + count > _limit){
//                        val delta = limit.getOrElse(100) - count
//                        println("delta: " + delta)
//                        _done = !func(page, slicedData.slice(0,delta))
//                        count = (count + rv.size()) - delta
//                    }else{
//                        _done = !func(page, slicedData)
//                        count = count + rv.size()
//                    }
//                }.getOrElse {
//                    _done = !func(page, slicedData)
//                    count = count + rv.size()
//                }

                limit.map {
                    case _limit if rvSize + count > _limit =>

                        val delta = _limit - count
                        debug(s"delta: $delta")

                        _done = !func(page, slicedData.slice(0,delta))
                        count = count + delta

                    case _ =>
                        _done = !func(page, slicedData)
                        count = count + rvSize
                }.getOrElse {
                    _done = !func(page, slicedData)
                    count = count + rvSize
                }
            }

            page = page + 1


            if (!_done && limit.isDefined)
                _done = count >= limit.getOrElse(100)

            if (!_done){
                rv = query.execute().getResult
                _done = rv.size() == 0
            }
        }

    }

    def slice[T <: BaseModel[IDType] : Manifest](sinceId: Option[IDType], toId:Option[IDType], limit: Option[Int], reverse:Boolean): Iterator[T] = {
        import scala.collection.JavaConversions._
        ensureReady

        //        val nowUUID = UUIDGen.getTimeUUID
        lazy val rv1 = {
            keyspace.prepareQuery(columnFamily)
                .getRow(keyNameIdGraph)
                .withColumnRange(new RangeBuilder().setLimit(1).setReversed(reverse).build())
                .execute().getResult
        }



        val rv2 = {

            val rb = new RangeBuilder()

            if (sinceId.isEmpty){
                //                rv1.map( c => println("c.getName: " + c.getName) )
                rv1.map( c => rb.setStart(c.getRawName) )
            }else
                sinceId.map(rb.setStart)

            if (toId.isDefined)
                toId.map(rb.setEnd)


            rb.setLimit(100)
            rb.setReversed(reverse)

            val query = keyspace.prepareQuery(columnFamily)
                .getRow(keyNameIdGraph)
                .autoPaginate(true)
                .withColumnRange(rb.build())
            //                    .withColumnRange(col.getName, null, true, 1000)

            var buff = mutable.ArrayBuilder.make[Column[java.lang.Long]]()
            var rv = query.execute().getResult
            var count = 0
            var _done = false
            while(!rv.isEmpty && !_done){
                rv.foreach { c =>
                    if (count < limit.getOrElse(100)){
                        buff += c
                        count = count + 1
                    }
                }

                if (count >= limit.getOrElse(100)){
                    _done = true
                }

                if (!_done){
                    rv = query.execute().getResult
                    _done = rv.size() == 0
                }
            }

            buff.result()
        }

        //        val rv3 = rv2.map(_.getName).toList
        //
        //        println("rv3: " + rv3)

        rv2.iterator.flatMap(c => getById[T](c.getName)) //.toIterator
    }

    def remove(v:Vertex){
        removeById(v.getId.asInstanceOf[IDType])
    }


    def removeById(id:IDType){
        ensureReady
//        val counter = keyspace.prepareQuery(columnFamilyCounter).getKey(keyNameCounter).getColumn(COUNTER_KEY).execute().getResult.getLongValue
        val mb = keyspace.prepareMutationBatch()
        mb.withRow(columnFamily, keyNameIdGraph).deleteColumn(id.asInstanceOf[java.lang.Long])
        mb.withRow(columnFamily, keyNameIdRaw).deleteColumn(id.asInstanceOf[java.lang.Long])
        if (getCount > 0){
            mb.withRow(columnFamilyCounter, keyNameCounter).incrementCounterColumn(COUNTER_KEY, -1)
        }
        mb.execute()
    }


    private def getById[T <: BaseModel[IDType] : Manifest](id:IDType) = {
        import com.ansvia.graph.BlueprintsWrapper._
        db.getVertex(id) match {
            case null => None
            case v:Vertex =>
                v.toCC[T]
        }
    }

    def getCount = {
        ensureReady
        keyspace.prepareQuery(columnFamilyCounter).getKey(keyNameCounter)
            .execute().getResult.getLongValue(COUNTER_KEY,0L)
    }

    def setCount(count:Long) = {
        ensureReady
        val prevCount = getCount
        keyspace.prepareColumnMutation(columnFamilyCounter, keyNameCounter, COUNTER_KEY).incrementCounterColumn(-prevCount).execute()
        keyspace.prepareColumnMutation(columnFamilyCounter, keyNameCounter, COUNTER_KEY).incrementCounterColumn(count).execute()
    }


    def reset(){
        val ksDef = ctx.cluster.getClient.describeKeyspace(keyspaceName)

        if (ksDef != null && ksDef.getColumnFamilyList.exists(_.getName.equalsIgnoreCase(cfName))){
            val m = keyspace.prepareMutationBatch()
            m.withRow(columnFamily, keyNameIdGraph).delete()
            m.withRow(columnFamily, keyNameIdRaw).delete()
            m.withRow(columnFamilyCounter, keyNameCounter).delete()
            m.execute()
        }
    }

}

case class CassandraPartitionedDaoListIndex(seeds:String, clusterName:String, keyspaceName:String, keyName:String,
                                            replicationStrategy:String, replicationStrategyOpts:String)
    extends DaoListIndex with Paging with DbAccess with Slf4jLogger {

    import scala.collection.JavaConversions._

    var MAX_DATA_PER_ROW = 100000

    private val cfName = "dli"
    private val cfNameCounter = "counter"
    private val keyNameIdRaw = keyName + "|IdRaw"
    private val keyNameIdGraph = keyName + "|IdGraph"
    private val keyConfig = keyName + "|Config"
    private val keyNameCounter:java.lang.String = keyName
    lazy private val ctx = CassandraDriver.getContext(clusterName, keyspaceName, seeds,
        replicationStrategy, replicationStrategyOpts)
    lazy private val keyspace = ctx.keyspace.getClient
    private val COUNTER_KEY = "count"
    private val PARTITION_KEY = 1L
    private val ID_ROUTER_KEY = 2L

    private lazy val columnFamily = new ColumnFamily[String, java.lang.Long](
        cfName,
        StringSerializer.get(),
        LongSerializer.get(),
        StringSerializer.get())

    private lazy val columnFamilyCounter = new ColumnFamily[java.lang.String, java.lang.String](
        cfNameCounter,
        StringSerializer.get(),
        StringSerializer.get())


    lazy val ensureReady = {
        val client = ctx.cluster.getClient
        val ksDef = client.describeKeyspace(keyspaceName)


        // apabila dalam mode test grace seconds gak diperlukan disable dengan 0
        // tapi apabila tidak maka set gc grace seconds dalam 1 hari
        val gcGraceSeconds = if (Config.testMode) 0 else 60 * 60 * 24 * 3 // 3 hari

        if (!ksDef.getColumnFamilyList.exists(_.getName.equalsIgnoreCase(cfName))){
            val cfDef = client.makeColumnFamilyDefinition()
                .setName(cfName)
                .setKeyspace(keyspaceName)
                .setGcGraceSeconds(gcGraceSeconds)
                .setKeyValidationClass(ComparatorType.UTF8TYPE.getClassName)
                .setDefaultValidationClass(ComparatorType.UTF8TYPE.getClassName)
                .setComparatorType(ComparatorType.LONGTYPE.getClassName)
                //                .setCompressionOptions(Map("sstable_compression" -> "org.apache.cassandra.io.compress.SnappyCompressor",
                //                    "chunk_length_kb" -> "64"))
                .setCompressionOptions(Map[String,String]())
            client.addColumnFamily(cfDef)
        }

        if (!ksDef.getColumnFamilyList.exists(_.getName.equalsIgnoreCase(cfNameCounter))){
            val cfDef = client.makeColumnFamilyDefinition()
                .setName(cfNameCounter)
                .setKeyspace(keyspaceName)
                .setGcGraceSeconds(gcGraceSeconds)
                //                .setComparatorType(ComparatorType.UTF8TYPE.getClassName)
                .setKeyValidationClass(ComparatorType.UTF8TYPE.getClassName)
                .setDefaultValidationClass(ComparatorType.COUNTERTYPE.getClassName)
                .setCompressionOptions(Map("sstable_compression" -> ""))
            client.addColumnFamily(cfDef)
        }

        if ( keyspace.prepareQuery(columnFamily).getKey(keyConfig).getCount.execute().getResult == Int.box(0) ){
            keyspace.prepareColumnMutation(columnFamily, keyConfig, Long.box(PARTITION_KEY)).putValue("0", null).execute()
            keyspace.prepareColumnMutation(columnFamily, keyConfig, Long.box(ID_ROUTER_KEY)).putValue("0:0", null).execute()
        }

    }

    private def getAvailablePartition = {
        try {
            keyspace.prepareQuery(columnFamily)
                .withRetryPolicy(new ExponentialBackoff(500L, 20))
                .getKey(keyConfig)
                .getColumn(PARTITION_KEY).execute().getResult.getStringValue.toInt
        }
        catch {
            case e:com.netflix.astyanax.connectionpool.exceptions.NotFoundException => // apabila key not exists / gak ada
                val mb = keyspace.prepareMutationBatch()
                mb.withRow(columnFamily,keyConfig).putColumn(PARTITION_KEY, "0", null)
                mb.withRow(columnFamily,keyConfig).putColumn(ID_ROUTER_KEY, "0:0", null)
                mb.execute()
                0
        }
    }

    def getPartitionCount = {
        getAvailablePartition + 1
    }


    private def getOriginalGraphId(v:Vertex):java.lang.Long = {
        v match {
            case iv:IdVertex =>
                iv.getBaseVertex.getId.asInstanceOf[java.lang.Long]
            case nv:Vertex =>
                nv.getId.asInstanceOf[java.lang.Long]
        }
    }



    def store(v: Vertex){
        ensureReady
        val idFromIdGraph = v.getId.asInstanceOf[java.lang.Long]
        val idOriginalGraph:java.lang.Long = getOriginalGraphId(v)

        val partition = {
            val _curPart = getAvailablePartition
            val nextPart = getCount / MAX_DATA_PER_ROW
            if (nextPart > _curPart){
                val idRange = keyspace.prepareQuery(columnFamily).getKey(keyConfig).getColumn(ID_ROUTER_KEY)
                    .execute().getResult.getStringValue
                val idRangeS = idRange.split(",").filter(_.trim.length > 0).distinct
                val idRangeEntry = v.getId + ":" + nextPart
                val idRangeSNew = (idRangeS ++ Seq(idRangeEntry)).map(_.trim).filter(_.length > 2).distinct.mkString(",")
                val mb = keyspace.prepareMutationBatch()
                mb.withRow(columnFamily,keyConfig).putColumn(PARTITION_KEY, nextPart.toString, null)
                mb.withRow(columnFamily,keyConfig).putColumn(ID_ROUTER_KEY, idRangeSNew, null)
                mb.execute()
                nextPart
            }else
                _curPart
        }


        val mb = keyspace.prepareMutationBatch()
        mb.withRetryPolicy(new ExponentialBackoff(250L, 10))
        mb.withRow(columnFamily, keyNameIdGraph + "-" + partition).putColumn(idFromIdGraph,"")
        mb.withRow(columnFamily, keyNameIdRaw + "-" + partition).putColumn(idOriginalGraph,"")
        mb.withRow(columnFamilyCounter, keyNameCounter).incrementCounterColumn(COUNTER_KEY,1)
        mb.execute()
    }


    private def getIdRoute = {
        keyspace.prepareQuery(columnFamily)
            .withRetryPolicy(new ExponentialBackoff(500L,20))
            .getKey(keyConfig).getColumn(ID_ROUTER_KEY)
            .execute().getResult.getStringValue
            .split(",").map(_.trim).filter(_.length > 0)
            .map(_.split(":")).map(x => (x(0).toLong, x(1).toInt))
    }



    def pagingRaw(sinceId: Option[IDType],
                  toId:Option[IDType],
                  limitPerPage:Int,
                  limit: Option[Int],
                  reverse:Boolean)(func: (Int, Iterator[Vertex]) => Boolean){

        import scala.collection.JavaConversions._
        ensureReady

        val availablePartition = getAvailablePartition
        var partition = 0

        val rowKey = reverse match {
            case true =>
                partition = availablePartition
                keyNameIdGraph + "-" + availablePartition
            case _ =>
                partition = 0
                keyNameIdGraph + "-0"
        }

        lazy val rv1 = {
            keyspace.prepareQuery(columnFamily)
                .getRow(rowKey) // from partition 0
                .withColumnRange(new RangeBuilder().setLimit(1).setReversed(reverse).build())
                .execute().getResult
        }


        val rb = new RangeBuilder()

        if (sinceId.isEmpty)
            rv1.map(c => rb.setStart(c.getRawName))
        else{
            sinceId.map { id =>
                getIdRoute.reverse.find(_._1 < id).map { range =>
                    partition = range._2
                }
                rb.setStart(id)
            }
        }

        if (toId.isDefined)
            toId.map(rb.setEnd)


        rb.setLimit(limitPerPage)
        rb.setReversed(reverse)

        def createQuery() = {
            keyspace.prepareQuery(columnFamily)
                .getRow(keyNameIdGraph + "-" + partition)
                .withColumnRange(rb.build())
                .autoPaginate(true)
        }

        var query = createQuery()

        var rv = query.execute().getResult

        var _done = false
        while(rv.isEmpty && !_done){
            partition = if (!reverse) partition + 1 else partition - 1
            if ( (!reverse && partition > availablePartition) || (reverse && partition < availablePartition) )
                _done = true
            else {
                query = createQuery()
                rv = query.execute().getResult
            }
        }

        var count = 0
        var page = 1
        _done = false
        while(!rv.isEmpty && !_done){

            val rvSize = rv.size()

            if (rvSize > 0){
                val slicedData = rv.iterator().map(c => db.getVertex(c.getName)).filter(_ != null)

//                limit.map { _limit =>
//                    if (rv.size() + count > _limit){
//                        val delta = _limit - count
//                        println("delta: " + delta)
//                        _done = !func(page, slicedData.slice(0,delta))
//                        count = (count + rv.size()) - delta
//                    }else{
//                        _done = !func(page, slicedData)
//                        count = count + rv.size()
//                    }
//                }.getOrElse {
//                    _done = !func(page, slicedData)
//                    count = count + rv.size()
//                }

                limit.map {
                    case _limit if rvSize + count > _limit =>

                        val delta = _limit - count
                        debug(s"delta: $delta")

                        _done = !func(page, slicedData.slice(0,delta))
                        count = count + delta
                    case _ =>
                        _done = !func(page, slicedData)
                        count = count + rvSize
                }.getOrElse {
                    _done = !func(page, slicedData)
                    count = count + rvSize
                }


            }

            page = page + 1

            if (!_done && limit.isDefined)
                _done = count >= limit.getOrElse(100)

            if (!_done){
                rv = query.execute().getResult
                if (rv.size() == 0 && count < limit.getOrElse(100)){
                    // belum habis masih ada yang belum keambil
                    // pindah ke part berikutnya
                    partition = if (!reverse) partition + 1 else partition - 1
                    query = createQuery()
                    rv = query.execute().getResult
                }
                _done = rv.size() == 0
            }
        }

    }

    def paging[T <: BaseModel[IDType] : Manifest](sinceId: Option[IDType],
                                                  toId:Option[IDType],
                                                  limitPerPage:Int,
                                                  limit: Option[Int],
                                                  reverse:Boolean)(func: (Int, Iterator[T]) => Boolean){
        import com.ansvia.graph.BlueprintsWrapper._
        pagingRaw(sinceId, toId, limitPerPage, limit, reverse){ case (page, vx) =>
            func(page, vx.flatMap(_.toCC[T]))
        }
    }


    def slice[T <: BaseModel[IDType] : Manifest](sinceId: Option[IDType], toId:Option[IDType], limit: Option[Int], reverse:Boolean): Iterator[T] = {
        import scala.collection.JavaConversions._
        ensureReady

        val availablePartition = getAvailablePartition
        var partition = 0

        reverse match {
            case true =>
                partition = availablePartition
            case _ =>
                partition = 0
        }

        val rv2 = {

            val rb = new RangeBuilder()

            sinceId.map { id =>
                val idRangeS = getIdRoute
                idRangeS.reverse.find(_._1 < id).map { range =>
                    partition = range._2
                }
                rb.setStart(id)
            }

            if (toId.isDefined)
                toId.map(rb.setEnd)

            rb.setLimit(100)
            rb.setReversed(reverse)

            def createQuery() = {
                keyspace.prepareQuery(columnFamily)
                    .getRow(keyNameIdGraph + "-" + partition)
                    .autoPaginate(true)
                    .withColumnRange(rb.build())
            }

            var query = createQuery()

            var buff = mutable.ArrayBuilder.make[Column[java.lang.Long]]()
            var rv = query.execute().getResult
            var _done = false
            while(rv.isEmpty && !_done){
                partition = if (!reverse) partition + 1 else partition - 1
                if ( (!reverse && partition > availablePartition) || (reverse && partition < availablePartition) )
                    _done = true
                else {
                    query = createQuery()
                    rv = query.execute().getResult
                }
            }
            var count = 0
            _done = false
            while(!rv.isEmpty && !_done){
                rv.foreach { c =>
                    if (count < limit.getOrElse(100)){
                        buff += c
                        count = count + 1
                    }
                }

                if (count >= limit.getOrElse(100)){
                    _done = true
                }

                if (!_done){
                    rv = query.execute().getResult
                    if (rv.size() == 0 && count < limit.getOrElse(100)){
                        // belum habis masih ada yang belum keambil
                        // pindah ke part berikutnya
                        partition = if (!reverse) partition + 1 else partition - 1
                        rv = createQuery().execute().getResult
                    }
                    _done = rv.size() == 0
                }
            }

            buff.result()
        }

        rv2.iterator.flatMap(c => getById[T](c.getName)) //.toIterator
    }

    def remove(v:Vertex){
        removeById(v.getId.asInstanceOf[IDType])
    }


    def removeById(id:IDType){
        ensureReady

        try {

            val idRange = getIdRoute
            val partition = idRange.reverse.find(_._1 < id).map(_._2).getOrElse(0)

            val mb = keyspace.prepareMutationBatch()
            mb.withRow(columnFamily, keyNameIdGraph + "-" + partition).deleteColumn(id.asInstanceOf[java.lang.Long])
            mb.withRow(columnFamily, keyNameIdRaw + "-" + partition).deleteColumn(id.asInstanceOf[java.lang.Long])
            if (getCount > 0){
                mb.withRow(columnFamilyCounter, keyNameCounter).incrementCounterColumn(COUNTER_KEY, -1)
            }
            mb.execute()
        }
        catch {
            case e:com.netflix.astyanax.connectionpool.exceptions.NotFoundException =>
                // when not found occur just ignore it
        }
    }


    private def getById[T <: BaseModel[IDType] : Manifest](id:IDType) = {
        import com.ansvia.graph.BlueprintsWrapper._
        db.getVertex(id) match {
            case null => None
            case v:Vertex =>
                v.toCC[T]
        }
    }

    def getCount = {
        ensureReady
        try {
            keyspace.prepareQuery(columnFamilyCounter).getKey(keyNameCounter)
                .execute().getResult.getLongValue(COUNTER_KEY, 0L)
        }
        catch {
            case e:com.netflix.astyanax.connectionpool.exceptions.NotFoundException =>
                0L
        }
    }

    def setCount(count:Long) = {
        ensureReady
        val prevCount = getCount
        keyspace.prepareColumnMutation(columnFamilyCounter, keyNameCounter, COUNTER_KEY).incrementCounterColumn(-prevCount).execute()
        keyspace.prepareColumnMutation(columnFamilyCounter, keyNameCounter, COUNTER_KEY).incrementCounterColumn(count).execute()
    }


    def reset(){
        val ksDef = ctx.cluster.getClient.describeKeyspace(keyspaceName)

        if (ksDef != null && ksDef.getColumnFamilyList.exists(_.getName.equalsIgnoreCase(cfName))){
            val m = keyspace.prepareMutationBatch()
            m.withRow(columnFamily, keyConfig).putColumn(ID_ROUTER_KEY,"0:0")
            m.withRow(columnFamily, keyConfig).putColumn(PARTITION_KEY,"0")
            for (i <- 0 to getAvailablePartition){
                m.withRow(columnFamily, keyNameIdGraph + "-" + i).delete()
                m.withRow(columnFamily, keyNameIdRaw + "-" + i).delete()
            }
            m.withRow(columnFamilyCounter, keyNameCounter).delete()
            m.execute()
        }
    }

}

