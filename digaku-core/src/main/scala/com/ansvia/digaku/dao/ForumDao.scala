/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import java.util.concurrent.TimeUnit
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types._
import com.ansvia.digaku.event.impl.{CreateForumEvent, DeleteChannelEvent}
import com.ansvia.digaku.exc._
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model._
import com.ansvia.digaku.persistence.SeqStore
import com.ansvia.digaku.utils.ArrangeSettings
import com.ansvia.digaku.validator.ChannelValidator
import com.ansvia.digaku.{Digaku, Types, model}
import com.ansvia.perf.PerfTiming
import com.google.common.cache.CacheBuilder
import com.netflix.astyanax.retry.ExponentialBackoff
import com.tinkerpop.blueprints.Graph


trait ForumDao extends DbAccess with DaoBase[GraphType, Forum]
    with Slf4jLogger with PerfTiming with Popularable {

    import com.ansvia.digaku.model.Label._
    import com.ansvia.graph.BlueprintsWrapper._
    import com.ansvia.graph.IdGraphTitanDbWrapper._
    import scala.collection.JavaConversions._


    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.ChannelDaoRootVertex"

    val forumListStr = "forum-list"

    /**
     * throw [[com.ansvia.digaku.exc.InvalidParameterException]]
     * when group's name not valid.
     * @param name
     */
    def validateName(name: String) {
        if (!ChannelValidator.validName(name))
            throw InvalidParameterException(("Invalid group name \"%s\". " +
                "Name min 3 and max 30 characters").format(name))
    }

    /**
     * throw [[com.ansvia.digaku.exc.InvalidParameterException]]
     * when group's description not valid.
     * @param desc
     */
    def validateDesc(desc: String) {
        if (desc.trim.length < 3) {
            throw InvalidParameterException("Min group description 3 characters.")
        }
        if (desc.trim.length > 500) {
            throw InvalidParameterException("Max group description 500 characters.")
        }
    }

    /**
     * Buat group baru menggunakan name, desc,tags
     * @param name nama group
     * @param desc description
     * @param owner user who own this group.
     * @param tags tags name
     * @param noTx set true apabila tidak ingin dijalankan dalam mode transaction
     */
    def create(name:String, desc:String, owner:User,
               tags:Array[String] = Array.empty[String],
               noTx:Boolean = false) = {

        validateName(name)
        validateDesc(desc)

        if (isExist(name))
            throw AlreadyExistsException("group with name %s already exist".format(name))

        val tagsStr = tags.mkString(",")
        val ch = Forum(name, desc)

        ch.tags = tagsStr

        val vertex = ch.saveWithLabel(VertexLabels.GROUP)
        val newChannel = vertex.toCC[Forum].getOrElse {
            throw new DigakuException("Cannot create group with name: %s, desc: %s".format(name, desc))
        }

        addToRoot(vertex)

        newChannel.setOwner(owner, noTx = true)

        if (!noTx)
            db.commit()

        Digaku.engine.eventStream.emit(CreateForumEvent(newChannel))

        newChannel
    }

    /**
     * get group dengan menggunakan Id
     * @param id Id group
     * @return
     */
    def getById(id:IDType): Option[Forum] = {
        val x = db.getVertex(id)
        if (x != null) {
            x.toCC[Forum]
        } else {
            None
        }
    }

    /**
     * get semua list dari group
     * @param limit limit
     * @return
     */
    def getListRight(sinceId: Option[Types.IDType], toId: Option[Types.IDType],
                              limit: Int, withPrivate: Boolean = false): Iterator[Forum] = {

        var lst = getListRight(sinceId, toId, limit).toSeq
        if (withPrivate) {
            lst
        } else {
            var _done = false
            lst = lst.filterNot(_.privated)
            while (lst.length < limit && !_done) {
                if (lst.length > 0 && lst.length < limit){
                    val lastId = Some(lst.last.getId)
                    lst ++= getListRight(lastId, None, limit).filterNot(_.privated).toSeq
                    lst = lst.distinct
                } else {
                    _done = lst.length == 0
                }
            }
        }

        lst.toIterator
    }

    /**
     * delete group permanently
     * for non permanent deletion use remove instead.
     * WARNING: this will also delete post inside (has origin to this group).
     * @param group see [[com.ansvia.digaku.model.Forum]]
     */
    def delete(group: model.Forum) {
        val id = group.getId

        val v = Forum.getById(id).getOrElse {
            throw NotExistsException("Group didn't exists.")
        }

        val channelV = v.getVertex

        // hapus semua yang origin-nya mengarah ke group
        // delete cascade?
        channelV.pipe.in(ORIGIN).remove()

        channelV.pipe.bothE().remove()

        db.commit()

        super.deleteById(id)

        Digaku.engine.eventStream.emit(DeleteChannelEvent(group))
    }

    /**
     * digunakan untuk mendapatkan popular group secara langsung
     * @param offset
     * @param limit
     * @return
     */
    private def loadPopularChannelInternal(offset: Int, limit: Int): Seq[Forum] = {

        db.rollback()

        popularRootVertex.pipe.out(POPULAR)
            // sementara di-range dulu sebelum dedup, karena ada bug yang membuat terjadi duplicate yang banyak
            // sehingga hasil offsetting-nya hanya mendapatkan jumlah popular yang sangat sedikit
            // @TODO(robin): periksa ini apakah masih terjadi bug gagal clearPopular menggunakan identifier di Popularable.clearPopular
            //               asumsi sementara bug terjadi di Titan SNAPSHOT
            .hasNot("blocked", true)
            .range(0,100)
            .dedup()
            .range(offset, offset + limit - 1)
            .iterator().flatMap(_.toCC[Forum])
            .toSeq

    }

    /**
     * Get popular group.
     * @param offset starting offset.
     * @param limit ends limit.
     * @param cached whether return from cache if any, default true.
     * @return
     */
    def getPopularChannels(offset: Int, limit: Int, cached: Boolean = true) = {
        loadPopularChannelInternal(offset, limit)
    }

    /**
     * digunakan untuk mengambil group yang diblock dari popular
     * @param offset
     * @param limit
     * @param query query untuk search blocked group dari nama group
     * @return
     */
    def getBlockedChannels(offset: Int, limit: Int, query: String = "") = {
        Digaku.engine.searchEngine.searchBlockedChannel(query, offset, limit)
    }

    /**
     * Delete group dengan param ID,
     * this operation realy delete object from database.
     * to only mark as deleted.
     * @param id Id group
     */
    def deleteById(id: IDType) {
        val v = db.getVertex(id)
        if (v == null) {
            throw NotExistsException("Group didn't exists.")
        }

        val ch = v.toCC[Forum]
        v.pipe.bothE().iterator().foreach(x => db.removeEdge(x))
        db.removeVertex(v)

        db.commit()

        ch.foreach(group => Digaku.engine.eventStream.emit(DeleteChannelEvent(group)))
    }

    /**
     * Set locked group by id.
     * @param id group id to lock.
     * @param locked lock state.
     */
    def setLockedChannelById(id: IDType, locked: Boolean) {
        getById(id) map { ch =>
            ch.setLocked(locked)
            db.getVertex(ch.getId).set("locked", locked)
        }
    }

    /**
     * Check whether group is exists
     * by name.
     * @param name group name.
     * @return
     */
    def isExist(name: String) = {
        db.query().has("label", VertexLabels.FORUM)
            .has("forum.lower-name", name.toLowerCase)
            .vertices().iterator().hasNext
    }

    /**
     * Check is group exists by group it self.
     * @param ch
     * @return
     */
    def isExist(ch: model.Forum) = {
        assert(ch.getId != 0L, "Group model haven't id, unsaved data?")
        db.getVertex(ch.getId) != null
    }

    /**
     * Restore non permanent deleted group,
     * @see [[com.ansvia.digaku.model.Forum.setDeleted]]
     * @param ch group to restore.
     */
    def restore(ch:Forum) {
        if (ch.deleted) {
            val owner = ch.owner
            ch.deletedRole = ""
            ch.deleted = false
            ch.deletedByUserId = 0L
            ch.deletedTime = 0L
            ch.deletedReason = ""
            ch.save()

            owner.incrementOwnedChannelCount()

            db.commit()
        }
    }

    // adalah label yang merujuk pada "forum member you may know"
    // diguntakan di getMemberMayUserKnow
    // dibuat oleh cmmyk.groovy
    val CMMYK = "cmmyk"

    lazy val activeUsersCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build[String,Seq[User]]()

    def isOwner[T <: Graph](user: User, ch: Forum) = {
        ch.owner == user
    }

    val COMMA = ","

    /**
     * Untuk optimasi, menggunakan KV style untuk sebuah state user join/tidak join
     * pada forum, operasi ini kompleksitasnya konstan O(1).
     * @param ch forum yang akan ditandai membership-nya.
     * @param users daftar user yg akan dimasukkan.
     * @param state posisi join, true = join, false = not join.
     * @tparam T
     */
    def markMembers[T <: Graph](ch: Forum, users: Seq[User], state: Boolean) = {
        for (user <- users) {
            Digaku.engine.commonCassandraCf.usingMutator { case (cf, mb) =>
                mb.withRetryPolicy(new ExponentialBackoff(250L,10))
                    .withRow(cf, "forum-" + ch.getId + "-member-" + user.getId)
                    .putColumn("0", state match {
                        case true => "1"
                        case _ => "0"
                    })
            }
        }
    }


    private def isMemberInternal[T <: Graph](user: User, ch: Forum) = {
        try {
            Digaku.engine.commonCassandraCf.usingQuery { q =>
                q.withRetryPolicy(new ExponentialBackoff(250L,10))
                    .getKey("forum-" + ch.getId + "-member-" + user.getId)
                    .getColumn("0").execute().getResult.getStringValue == "1"
            }
        } catch {
            case e: com.netflix.astyanax.connectionpool.exceptions.NotFoundException =>
                false
        }
    }

    def isMember[T <: Graph](user: User, ch: Forum, cached: Boolean = true) = {
        isMemberInternal[T](user, ch)
    }

    def isStaff[T <: Graph](user: User, ch: Forum) = {
        ch.reload().getRawVertex.pipe.in(STAFF_AT).has("id", user.getRawId).iterator.hasNext
    }

    /**
     * Untuk optimasi, menggunakan KV style untuk sebuah state user follow atau tidak
     * pada forum, operasi ini kompleksitasnya konstan O(1).
     * @param ch forum yang akan ditandai follower-nya.
     * @param users daftar user yg akan dimasukkan.
     * @param state posisi join, true = join, false = not join.
     * @tparam T
     */
    def markFollower[T <: Graph](ch: Forum, users: Seq[User], state: Boolean) = {
        for (user <- users) {
            Digaku.engine.commonCassandraCf.usingMutator { case (cf, mb) =>
                mb.withRetryPolicy(new ExponentialBackoff(250L,10))
                    .withRow(cf, "forum-" + ch.getId + "-follower-" + user.getId)
                    .putColumn("0", state match {
                    case true => "1"
                    case _ => "0"
                })
            }
        }
    }

    /**
     * Check apakah user follow sebuah forum atau tidak
     * @param user User yang akan di cek
     * @param ch
     * @tparam T
     * @return
     */
    def isFollower[T <: Graph](user: User, ch: Forum) = {
        try {
            Digaku.engine.commonCassandraCf.usingQuery { q =>
                q.withRetryPolicy(new ExponentialBackoff(250L,10))
                    .getKey("forum-" + ch.getId + "-follower-" + user.getId)
                    .getColumn("0").execute().getResult.getStringValue == "1"
            }
        } catch {
            case e: com.netflix.astyanax.connectionpool.exceptions.NotFoundException =>
                false
        }
    }

    /**
     * These methods are used to arrange forum, currently possible usage:
     * forum list arrangement and sub forum list arrangement
     */
    val forumListStore = ArrangeSettings.seqStore.build("forum-list-store", () => ArrangeSettings.generateId())
    def subforumListStore(forum: model.Forum) = {
        val key = "subforum-list-store-forum-" + forum.getId
        ArrangeSettings.seqStore.build(key, () => ArrangeSettings.generateId())
    }

    /**
     * get forum arrangement based on listStr
     * @param seqStore
     * @return
     */
    def getForumArrangement[T](seqStore: SeqStore[T]): List[Forum] = {
        ArrangeSettings.getArrangedList(seqStore).map( x => Forum.getById(x.toLong)).filter(_.isDefined).map(_.get)
    }

    /**
     * move forum up one level on forum list based on listStr
     * @param seqStore
     * @param forum
     * @return
     */
    def arrangeForumUp[T](seqStore: SeqStore[T], forum: Forum): List[Forum] = {
        ArrangeSettings.arrangeUp(seqStore, forum.getId)
        getForumArrangement(seqStore)
    }

    /**
     * move forum down one level on forum list based on listStr
     * @param seqStore
     * @param forum
     * @return
     */
    def arrangeForumDown[T](seqStore: SeqStore[T], forum: Forum): List[Forum] = {
        ArrangeSettings.arrangeDown(seqStore, forum.getId)
        getForumArrangement(seqStore)
    }

    /**
     * move forum to top on forum list based on listStr
     * @param seqStore
     * @param forum
     * @return
     */
    def moveForumToTop[T](seqStore: SeqStore[T], forum: Forum): List[Forum] = {
        ArrangeSettings.moveToTop(seqStore, forum.getId)
        getForumArrangement(seqStore)
    }

    /**
     * move forum to bottom in forum list based on listStr
     * @param seqStore
     * @param forum
     * @return
     */
    def moveForumToBottom[T](seqStore: SeqStore[T], forum: Forum): List[Forum] = {
        ArrangeSettings.moveToBottom(seqStore, forum.getId)
        getForumArrangement(seqStore)
    }

    /**
     * remove forum from list arrangement based on listStr
     * @param seqStore
     * @param forum
     * @return
     */
    def removeFromForumList[T](seqStore: SeqStore[T], forum: Forum): List[Forum] = {
        ArrangeSettings.removeFromList(seqStore, forum.getId)
        getForumArrangement(seqStore)
    }

    def setArrangedList[T](seqStore: SeqStore[T], objList: List[String]) = {
        ArrangeSettings.setArrangedList(seqStore, objList)
    }
}
