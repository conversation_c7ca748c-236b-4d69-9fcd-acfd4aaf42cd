/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

//package com.ansvia.digaku.dao
//
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.helpers.{RootVertex, DbAccess}
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.model.Label._
//import com.ansvia.digaku.model.{FacebookInfo,User}
//import com.ansvia.digaku.exc.DigakuException
//import scala.collection.JavaConversions._
//
///**
// * Author: nadir
// * Date: 11/25/13
// * Time: 5:02 PM
// *
// */
//trait FacebookInfoDao[idT, dbT <: GraphType] extends DbAccess with RootVertex with Slf4jLogger {
//
//    import com.ansvia.digaku.database.GraphCompat.transact
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.FacebookInfoDaoRootVertex"
//    val rootVertexLabel = DAO_LIST
//
//    /**
//     * digunakan untuk create FacebookInfo
//     * @param fbId facebook id
//     * @return see [[com.ansvia.digaku.model.FacebookInfo]]
//     *         save to database ketika belum exist dan mengembalikan FacebookInfo
//     *         ketika sudah exist hanya akan mengembalikan FacebookInfo yang sudah ada
//     */
//    def create(fbId:String): FacebookInfo ={
//        if (!existsByFbId(fbId)) {
//            val facebookInfo = FacebookInfo(fbId)
//
//            var newFacebookInfo:FacebookInfo = null
//
//            try {
//                transact {
//
//                    val vertex = facebookInfo.save()
//
//                    addToRoot(vertex)
//
//                    newFacebookInfo = vertex.toCC[FacebookInfo].getOrElse {
//                        throw new DigakuException("Cannot create facebookInfo : "+FacebookInfo)
//                    }
//
//                    newFacebookInfo
//
//                }
//            } catch{
//                case e:Exception =>
//                    throw new DigakuException("Cannot create facebookInfo " + fbId)
//            }
//            newFacebookInfo
//        } else {
//            getByFbid(fbId).getOrElse(throw new DigakuException("Cannot getting FacebookInfo with id: " + fbId))
//        }
//    }
//
//    /**
//     * get FacebookInfo by facebook id
//     * @param fbId facebook id
//     * @return
//     */
//    def getByFbid(fbId: String): Option[FacebookInfo] = {
//        db.getVertices("fb.id", fbId).headOption.flatMap(_.toCC[FacebookInfo])
//    }
//
//    /**
//     * cek apakah FacebookInfo sudah ada atau belum.
//     * @param fbId
//     * @return
//     */
//    def existsByFbId(fbId: String): Boolean = {
//        getByFbid(fbId).isDefined
//    }
//
//}
