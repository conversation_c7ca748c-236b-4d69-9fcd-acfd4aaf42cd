/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.Types._
import com.ansvia.digaku.model.BaseModel
import com.tinkerpop.blueprints.Vertex

/**
 * Author: robin
 *
 */
abstract class DaoListIndex {
    def store(v:Vertex)

    def slice[T <: BaseModel[IDType] : Manifest](sinceId: IDType, limit: Int, reverse:Boolean): Iterator[T] = {
        val sidO = sinceId match {
            case 0L => None
            case n => Some(n)
        }
        slice[T](sidO, None, Some(limit), reverse)
    }

    def slice[T <: BaseModel[IDType] : Manifest](sinceId: IDType, toId:IDType, limit: Int, reverse:Boolean): Iterator[T] = {
        val sidO = sinceId match {
            case 0L => None
            case n => Some(n)
        }
        slice[T](sidO, Some(toId), Some(limit), reverse)
    }

    def slice[T <: BaseModel[IDType] : Manifest](sinceId: Option[IDType], toId:Option[IDType], limit: Option[Int], reverse:Boolean): Iterator[T]
    def remove(v:Vertex)
    def removeById(id:IDType)
    def getCount:Long
    def reset()
}



object DaoListIndexFactory {

    var clusterName:String = _
    var keyspaceName:String = _
    var seeds:String = _
    var replicationStrategy:String = _
    var replicationStrategyOpts:String = _

    def build(keyName:String, partitioned:Boolean) = {
        assert(seeds != null, "seeds not configured")
        assert(clusterName != null, "clusterName not configured")
        assert(keyspaceName != null, "keyspaceName not configured")
        assert(keyName != null, "invalid keyName, got null")
        assert(replicationStrategy != null, "replicationStrategy not configured")
        assert(replicationStrategyOpts != null, "replicationStrategyOpts not configured")

        partitioned match {
            case true => CassandraPartitionedDaoListIndex(seeds, clusterName, keyspaceName, keyName,
                replicationStrategy, replicationStrategyOpts)
            case _ => CassandraDaoListIndex(seeds, clusterName, keyspaceName, keyName,
                replicationStrategy, replicationStrategyOpts)
        }
    }

}



trait Paging {
    dli: DaoListIndex =>

    def paging[T <: BaseModel[IDType] : Manifest](sinceId: Option[IDType],
                                                  toId:Option[IDType],
                                                  limitPerPage:Int,
                                                  limit: Option[Int],
                                                  reverse:Boolean)(func: (Int, Iterator[T]) => Boolean)
}

