/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.Types._
import com.ansvia.digaku.model.PostMarkCategory
import com.ansvia.digaku.exc.{NotExistsException, DigakuException, AlreadyExistsException, InvalidParameterException}
import scala.collection.JavaConversions._

/**
 * Author: nadir
 * Date: 4/18/13
 * Time: 11:30 AM
 * 
 */
trait PostMarkCategoryDao[idT, dbT <: GraphType] extends DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._
//    import com.ansvia.digaku.database.GraphCompat.transact

    /**
     * create new post mark category
     * @param category category post mark
     * @return
     */
    def create(category:String):PostMarkCategory={
        if (category.length < 2)
            throw InvalidParameterException("Minimal title label category 3 karakter.")

        if (category.length > 20)
            throw InvalidParameterException("Maksimal title label category 3 karakter.")

        if (getByCategoryTitle(category).isDefined)
            throw AlreadyExistsException("Label category %s sudah ada.".format(category))

//        transact{
            val vertex = new PostMarkCategory(category).save()
            val rv = vertex.toCC[PostMarkCategory].getOrElse(
                throw new DigakuException("Cannot create post mark category with title: %s".format(category)))
//        }
        db.commit()

        rv

    }

    /**
     * get category post mark by title
     * @param category category post mark
     * @return
     */
    def getByCategoryTitle(category:String):Option[PostMarkCategory]={
        val vertices = db.getVertices("_class_", "com.ansvia.digaku.model.PostMarkCategory")
        try {
            vertices.filter(_.getOrElse("category", "") == category)
                .head.toCC[PostMarkCategory]
        } catch {
            case e:NoSuchElementException => None
        }
    }

    /**
     * get category post mark by id
     * @param id id category post mark
     * @return
     */
    def getById(id:idT):Option[PostMarkCategory]={
        val vx = db.getVertex(id)
        if (vx!=null){
            vx.toCC[PostMarkCategory]
        }else{
            None
        }
    }

    /**
     * delete permanently post mark category by title
     * @param categoryTitle post mark category title
     */
    def deleteByCategoryTitle(categoryTitle:String){
        val pMCategory = getByCategoryTitle(categoryTitle)

        if (!pMCategory.isDefined)
            throw NotExistsException("Post Mark category didn't exists.")

        transact{
            pMCategory.map{ pMC =>
                pMC.getVertex.pipe.bothE().iterator().foreach(x => db.removeEdge(x))
                db.removeVertex(pMC.getVertex)
            }
        }

    }

    /**
     * delete permanently post mark category
     * @param pMCategory see [[com.ansvia.digaku.model.PostMarkCategory]]
     */
    def delete(pMCategory:PostMarkCategory){
        val vx = db.getVertex(pMCategory.getId)

        if (vx == null){
            throw NotExistsException("Post Mark category didn't exists.")
        }

        transact{
            vx.pipe.bothE().iterator().foreach(x => db.removeEdge(x))
            db.removeVertex(vx)
        }
    }



    def getList:Iterable[PostMarkCategory]={

        db.getVertices("_class_", "com.ansvia.digaku.model.PostMarkCategory")
            .flatMap(_.toCC[PostMarkCategory])
    }

}
