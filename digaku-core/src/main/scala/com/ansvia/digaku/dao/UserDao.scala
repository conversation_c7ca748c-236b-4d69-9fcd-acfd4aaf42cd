/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import java.util
import java.util.concurrent.{Callable, TimeUnit}
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.event.impl.{UpdateUserEvent, CreateUserEvent, DeleteUserEvent}
import com.ansvia.digaku.exc.{AlreadyExistsException, NotExistsException, _}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.model._
import com.ansvia.digaku.stats.model.DigakuStats
import com.ansvia.digaku.validator.{EmailValidator, UserValidator}
import com.ansvia.graph.IdGraphTitanDbWrapper._
import com.ansvia.perf.PerfTiming
import com.google.common.cache.CacheBuilder
import com.tinkerpop.blueprints.Vertex
import com.ansvia.digaku.database.GraphCompat.tx
import com.ansvia.digaku.helpers.GremlinHelpers._
import com.ansvia.graph.BlueprintsWrapper._
import scala.collection.JavaConversions._
import com.ansvia.digaku.utils.UserSettings._


trait UserDao[idT, dbT <: GraphType] extends DaoBase[GraphType, User]
        with DbAccess with Slf4jLogger with PerfTiming {

    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.UserDaoRootVertex"

    val supportingText = "supporting"
    val labelText = "label"
    val edText = "ed"
    val classText = "_class_"
    val idText = "id"

    /**
     * Check is user exists by name.
     * @param name user's name.
     * @return
     */
    def existsByName(name: String): Boolean = {
        getByName(name).isDefined
    }

    /**
     * Check apakah email sudah terdaftar atau belum.
     * @param email email yang akan dicheck.
     * @return
     */
    def isEmailRegistered(email: String): Boolean = {
        db.query().has(labelText, VertexLabels.USER)
            .has("user.email", EmailValidator.normalizedEmail(email))
            .vertices().nonEmpty
    }

    /**
     * create new User
     * @param name nama user
     * @param email email user
     * @param sex see [[com.ansvia.digaku.model.SexType]]
     * @param activated whether user is verified [Boolean, default false]
     * @return
     */
    def create(name: String, email: String, sex: Int, birthDate: String,
               password: String, mobilePhone: String = "", activated: Boolean = false,
               emailValidator: Boolean = true,
               noTx: Boolean = false): User = {

        UserValidator.validateName(name)

        if (emailValidator) {
            EmailValidator.validate(email)
        }

        // check apakah user dah ada?
        if (existsByName(name)) {
            throw AlreadyExistsException("User already exists: " + name)
        }

        if (email.trim.isEmpty) {
            throw InvalidParameterException("Please enter a valid email address.")
        }

        val emailLower =
            if (email.trim.isEmpty) {
                "<EMAIL>".format(name.toLowerCase)
            } else {
                email.toLowerCase
            }

        if (emailValidator && !email.trim.isEmpty) {
            EmailValidator.validate(emailLower)

            if (isEmailRegistered(email)) {
                throw AlreadyExistsException("Email already exists: " + email)
            }
        }

        val user = User(name, "", emailLower, sex, "", "", birthDate)
        user.activated = activated

        // set hasGetStarted true apabila user sudah activated
        if (activated) {
            user.hasGetStarted = activated
        }

        var newUser:Option[User] = None

        try {
            val vertex = user.saveWithLabel(VertexLabels.USER)

            // set join time
            vertex.setProperty("joinTime", Digaku.engine.dateUtils.nowMilis)

            // apabila activated = true maka email-nya juga anggap sudah active
            vertex.setProperty("user.emailActivated", activated)

            val ed = addToRoot(vertex)

            val emailDomain = email.split("@").apply(1)
            ed.setProperty("user.email-domain", emailDomain)

            newUser = Some(vertex.toCC[User].getOrElse {
                throw new DigakuException("Cannot create user: " + name)
            })

            if (!noTx) {
                db.commit()
            }
        } catch {
            case e:Exception =>
                error("Cannot create user with name: %s, email: %s, sex: %s, birthDate: %s, activated: %s"
                        .format(name, email, sex, birthDate, activated))
                e.printStackTrace()
                throw new DigakuException("Cannot create user " + name + ". Error: " + e.getMessage)
        }

        newUser.foreach { u =>
            u.setPassword(password, autoCommit = !noTx)
            if (!noTx)
                Digaku.engine.eventStream emit CreateUserEvent(u)
        }

        newUser.orNull
    }

    /**
     * get user dari database berdasarkan namanya.
     * @param name nama user
     * @return
     */
    def getByName(name: String): Option[User] = {
        db.query().has(labelText, VertexLabels.USER)
            .has("user.lower-name", name.trim.toLowerCase)
            .vertices()
            .headOption.flatMap(_.toCC[User])
    }

    /**
     * Digunakan untuk mengubah username
     * @param user user yang akan diubah username nya
     * @param userName username baru
     */
    def setUserName(user: User, userName: String): Unit = {
        if (userName != user.name) {
            if (!UserValidator.validName(userName)) {
                throw InvalidParameterException("Username not valid: Min 3 and max 25 characters and not allowed space and special character")
            }

            if (User.existsByName(userName)) {
                throw InvalidParameterException("Username sudah ada.")
            }

            tx { t =>
                val v = t.getVertex(user.getId)
                v.setProperty("name", userName)
                v.removeProperty("user.lower-name")
                v.setProperty("user.lower-name", userName.toLowerCase)
                val uR = user.reload()(t)
                user.joinedForums.update(user.getId, uR.getName)
                user.moderatedForums.update(user.getId, uR.getName)
                DigakuStats.registeredUserSeqStore.update(user.getId, uR.getName)
            }

            Digaku.engine.eventStream.emit(UpdateUserEvent(user))
        }
    }

    /**
     * get user dari database berdasarkan email login
     * @param email email user
     * @return
     */
    def getByEmailLogin(email: String): Option[User] = {
        db.query().has(labelText, VertexLabels.USER)
            .has("user.email", EmailValidator.normalizedEmail(email))
            .vertices()
            .headOption.flatMap(_.toCC[User])
    }

    /**
     * get user dari database berdasarkan mobile phone
     *
     * @param mobilePhone mobile phone user
     * @return
     */
    def getByMobilePhone(mobilePhone: String): Option[User] = {
        db.query().has(labelText, VertexLabels.USER)
            .has("user.mobilePhone", mobilePhone)
            .vertices()
            .headOption.flatMap(_.toCC[User])
    }

    /**
     * get all User list
     * @param toId ends id.
     * @param limit limit
     * @return
     */
    def getList(sinceId: IDType, toId: IDType, limit: Int): Iterator[User] = {
        super.getListLeft(Some(sinceId), Some(toId), limit)
    }

    /**
     * Mendapatkan List user yang di suspend (isLocked=true)
     *
     * @param offset offset
     * @param limit limit
     * @return
     */
    def getSuspendedList(offset: Int, limit: Int): Iterator[User] = {
        if (limit > 0) {
            rootVertex.pipe.out(rootVertexLabel)
                .has(classText, "com.ansvia.digaku.model.User")
                .has("locked", true)
                .range(offset, offset + limit)
                .iterator()
                .flatMap { v =>

                try {
                    v.toCC[User]
                } catch {
                    case e: java.lang.ClassNotFoundException =>
                        error("bad serialization, cannot serialize vertex. e: " + e.getMessage + "\n" +
                            "vertex should be deleted for safety: " + v.getId +
                            ", class: " + v.getOrElse("_class_","<unknown>"))
                        None
                }
            }
        } else {
            rootVertex.pipe.out(rootVertexLabel)
                .has(classText, "com.ansvia.digaku.model.User")
                .has("locked", true)
                .iterator()
                .flatMap { v =>

                try {
                    v.toCC[User]
                } catch {
                    case e: java.lang.ClassNotFoundException =>
                        error("bad serialization, cannot serialize vertex. e: " + e.getMessage + "\n" +
                            "vertex should be deleted for safety: " + v.getId +
                            ", class: " + v.getOrElse("_class_","<unknown>"))
                        None
                }
            }
        }
    }

    /**
      * Digunakan untuk mendapatkan user list yang memiliki role
      * admin dan super admin
      * @return
      */
    def getUsersWithAdminRole = {
        rootVertex.pipe.out(rootVertexLabel)
            .hasNot("role", UserRole.USER)
            .iterator().flatMap(_.toCC[User])
    }

    /**
     * Dapatkan daftar rekomendasi untuk user `forUser`.
     * Fungsi ini pertamakali akan mencari user dengan email domain yang sama
     * selain domain public seperti gmail, yahoo, dll.
     * Lalu mendapatkan dari hasil prosesing PYMK yang dibuat oleh script:
     * contrib/faunus/map/user_recommendation.groovy
     *
     * Fungsi ini tidak akan mengembalikan return apabila:
     *
     *  1. tidak ada user dengan domain private yang sama yang belum disupport.
     *  2. PYMK belum di-proses/kalkulasikan menggunakan faunus atau tools external lainnya.
     *
     * @param forUser user yang akan mendapatkan rekomendasi.
     * @param limit end limit.
     * @return
     */
    def getRecommendationList(forUser: User, limit: Int): Iterator[User] = {
        if (forUser.emailLogin.startsWith("-deleted-")) // jangan proses deleted user.
            Iterator[User]()
        else {
            val ignored = new util.LinkedList[Vertex]()

            // menggunakan trick ini untuk meng-ignore user recommendation
            // yang telah diignore oleh user
            // `iterate()` untuk mengeksekusi gremline pipeline
            // dan mem-populate `ignored` map.
            forUser.getVertex.pipe.out(IGNORE_RECOMMENDATION).aggregate(ignored).iterate()

            // ignore user yang telah di-support ma :forUser
            forUser.getVertex.pipe.aggregate(ignored)
                .out(SUPPORT).aggregate(ignored).iterate()

            // check kesamaan email dulu
            val emailDomain = EmailValidator.getDomain(forUser.emailLogin)

            val userSameEmail = {
                if (!EmailValidator.isPublicEmailDomain(emailDomain)) {
                    rootVertex.pipe.outE().has("user.email-domain", emailDomain).inV()
                        .except(ignored).range(0, limit-1).iterator().flatMap(_.toCC[User]).toList
                } else
                    List[User]()
            }

            if (userSameEmail.length > 2) {
                userSameEmail.toIterator
            } else {
                val rv = userSameEmail ++ forUser.getVertex.pipe.out(PYMK).except(ignored)
                    .range(0,limit-1) //.asInstanceOf[GremPipeVertex]
                    .iterator().flatMap(_.toCC[User]).toList

                rv.toIterator
            }
        }
    }

    /**
     * Untuk mendapatkan list rekomendasi user `forUser` by name
     * @param name nama user
     * @param forUser user yang akan medapatkan rekomendasi
     * @param offset starting offset
     * @param limit end limit
     * @return
     */
    def getRecommendationByName(name: String, forUser: User, offset: Int, limit: Int): Iterator[User] = {
        val lowerName = name.toLowerCase
        getRecommendationList(forUser, limit).filter(_.lowerName.contains(lowerName))
    }

    /**
     * Get users by email domain.
     * @param domain email domain to search.
     * @return
     */
    def getUsersByEmailDomain(domain: String) = {
        rootVertex.pipe.outE().has("user.email-domain", domain)
            .inV().iterator().flatMap(_.toCC[User])
    }

    /**
     * daftarkan rekomendasi group untuk user `forUser`
     * @param forUser user yang akan mendapat rekomendasi group.
     * @param offset starting offset.
     * @param limit end limit
     * @return
     */
    def getChannelRecommendationList(forUser: User, offset: Int, limit: Int): Seq[Forum] = {
        Nil
    }

    /**
     * Get mutual supporter.
     * maksud mutual supporter ini adalah irisan antara suppoter kita dan supporter seseorang.
     * @param userA left user.
     * @param userB right user.
     * @return daftar orang-orang yang men-support user1 dan men-support user2.
     */
    def getMutualSupporter(userA: User, userB: User, offset: Int, limit: Int): Seq[User] = {
        // @TODO(robin): optimize this setelah tanpa cache
        userA.getRawVertex.pipe.in(SUPPORT).as("supporter")
            .out(SUPPORT)
            .has(idText, userB.getRawId) // NOTICE: jangan rubah ini, ini optimasi untuk super nodes
            // kalo gak muncul mutual-nya tolong di-reindex dulu database-nya.
            .back("supporter").asInstanceOf[GremPipeVertex]
            .range(offset, offset + limit - 1)
            .toIdVertex
            .flatMap(_.toCC[User]).toSeq
    }

    /**
     * Get mutual supporting.
     * maksud mutual supporting ini adalah irisan antara user yg kita support dan seseorang support.
     * @param userA left user.
     * @param userB right user.
     * @return daftar orang-orang yang user1 support dan user2 support juga.
     */
    def getMutualSupporting(userA: User, userB: User, offset: Int, limit: Int): Seq[User] = {
        // @TODO(robin): optimize this setelah tanpa cache
        userA.getRawVertex.pipe.out(SUPPORT)//.range(0,3000)
            .as(supportingText)
            .in(SUPPORT)//.range(0,3000)
            .has(idText, userB.getRawId) // NOTICE: jangan rubah ini, ini optimasi untuk super nodes
            // kalo gak muncul mutual-nya tolong di-reindex dulu database-nya.
            .back(supportingText).asInstanceOf[GremPipeVertex]
            .range(offset, offset + limit - 1)
            .toIdVertex
            .flatMap(_.toCC[User]).toSeq
    }

    /**
     * Get mutual support
     * berguna untuk mendapatkan list user yang saling support dengan user ini.
     * contoh penggunaannya adalah untuk mendapatkan list user yang dapat dikirim whisper.
     * @param user user target
     * @param offset offset
     * @param limit limit
     * @return
     */
    def getMutualSupport(user: User, offset: Int, limit: Int): Seq[User] = {
        // @TODO(robin): optimize this setelah tanpa cache
        timing("UserDao.getMutualSupport") {
            user.getRawVertex.pipe.out(SUPPORT).as(supportingText)
                .outE(SUPPORT)
                .has("targetId", user.getId)
                .back(supportingText)
                .asInstanceOf[GremPipeVertex]
                .dedup()
                .range(offset, offset + limit -1)
                .toIdVertex
                .flatMap(_.toCC[User]).toSeq
        }
    }

    /**
     * Get mutual support count
     * berguna untuk mendapatkan jumlah user yang saling support dengan user ini.
     * @param user
     * @return
     */
    def getMutualSupportCount(user: User): Long = {
        // @TODO(robin): optimize this setelah tanpa cache
        timing("UserDao.getMutualSupportCount") {
            user.getRawVertex.pipe.out(SUPPORT).as(supportingText)
                .outE(SUPPORT)
                .has("targetId", user.getId)
                .back(supportingText)
                .asInstanceOf[GremPipeVertex]
                .dedup()
                .count()
        }
    }

    /**
     * Dapatkan jumlah mutual supporter.
     * @param user1 user A.
     * @param user2 user B.
     * @return
     */
    def getMutualSupporterCount(user1: User, user2: User):Int = tx { t =>
        t.getVertex(user1.getId).pipe.outE(PYMK).as(edText).inV().has("id", user2.getId)
            .back(edText).asInstanceOf[GremPipeEdge]
            .headOption.map(_.getOrElse("supportedByCount", 0)).getOrElse(0)
    }

    /**
     * Dapatkan jumlah mutual supporting.
     * @param user1 user A.
     * @param user2 user B.
     * @return
     */
    def getMutualSupportingCount(user1: User, user2: User): Int = {
        tx { t =>
            t.getVertex(user1.getId).pipe.inE(PYMK).as(edText).outV()
                .has(idText, user2.getId).back(edText).asInstanceOf[GremPipeEdge]
                .headOption.map(_.getOrElse("supportedByCount", 0)).getOrElse(0)
        }
    }

    private val supMayKnowCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(30, TimeUnit.MINUTES)
        .build[String,Seq[User]]()

    def getSupportingIMayKnow(me: User, targetUser: User, limit: Int): Seq[User] = {
        supMayKnowCache.get("supporting-" + me.getId.toString + "-" + targetUser.getId.toString + "-" + limit,
            new Callable[Seq[User]] {
                def call(): Seq[User] = {
                    timing("UserDao.getSupportingIMayKnow") {
                        targetUser.reload()

                        // menggunakan raw vertex, karena entah kenapa sangat lambat apabila menggunakan IdVertex
                        // untuk query.
                        targetUser.getRawVertex
                            .pipe.out(SUPPORT).as("his_support").range(0,1000)
                            .in(SUPPORT).has(idText, me.getRawId).random(0.5).range(0,limit - 1)
                            .back("his_support")
                            .asInstanceOf[GremPipeVertex]
                            .toIdVertex
                            .flatMap(_.toCC[User])
                            .toSeq
                    }
                }
        })
    }

    def getSupportersIMayKnow(me: User, targetUser: User, limit: Int): Seq[User] = {
        supMayKnowCache.get("supporters-" + me.getId.toString + "-" + targetUser.getId.toString + "-" + limit,
            new Callable[Seq[User]] {
                def call(): Seq[User] = {
                    timing("UserDao.getSupportersIMayKnow") {
                        me.reload()

                        // menggunakan raw vertex, karena entah kenapa sangat lambat apabila menggunakan IdVertex
                        // untuk query.
                        me.getRawVertex
                            .pipe.out(SUPPORT).as("me_support").range(0,1000)
                            .out(SUPPORT).has(idText, targetUser.getRawId).random(0.5).range(0,limit - 1)
                            .back("me_support")
                            .asInstanceOf[GremPipeVertex]
                            .toIdVertex
                            .flatMap(_.toCC[User])
                            .toSeq
                    }
                }
            })
    }

    /**
     * untuk menghapus semua post yang ditulis user
     * @param user
     */
    def deleteAllPost(user: User) {
        info("deleting all %s's posts".format(user.name))
        user.getVertex.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT)
            .out(PUBLISH_CONTENT).iterator().foreach { vertex =>

            // remove response ketika ada
            vertex.pipe.in(RESPONSE_OF).iterator().foreach { respV =>
                respV.pipe.bothE().remove()
                db.removeVertex(respV)
            }

            // check apakah ada referensi origin ke sini?
            // misalnya FreePicture?
            vertex.pipe.in(ORIGIN).iterator().foreach { inOriginV =>
                inOriginV.pipe.bothE().remove()
                db.removeVertex(inOriginV)
            }
            // remove seluruh edge yang datang ke post
            // misal like
            vertex.pipe.bothE().remove()

            // remove post/event/picture
            db.removeVertex(vertex)
        }

        db.commit()
    }

    /**
     * untuk menghapus semua reesponse yang ditulis oleh user
     * @param user
     */
    def deleteAllResponse(user: User) {
        info("deleting all %s's responses".format(user.name))
        user.getVertex.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.RESPONSE)
            .out(RESPONSE_WITH).iterator().foreach { vertex =>
            vertex.pipe.bothE().remove()
            db.removeVertex(vertex)
        }

        db.commit()
    }

    /**
     * menghapus semua edge yang berhubungan dengan user.
     * @param user
     */
    def removeAllEdges(user: User) {
        info("deleting all %s's connected edges".format(user.name))
        user.getVertex.pipe.bothE().remove()

        db.commit()
    }

    /**
     * delete user
     * @param user see [[com.ansvia.digaku.model.User]]
     */
    def delete(user: User) {
        if (user.reload().getOwnedChannelCount > 0) {
            throw PermissionDeniedException("%s still have group".format(user.name))
        }

        deleteAllPost(user)
        deleteAllResponse(user)

        removeAllEdges(user)

        val v = User.getById(user.getId).getOrElse {
            throw NotExistsException("user didn't exist")
        }

        val userV = v.getVertex
        userV.pipe.bothE().remove()
        db.removeVertex(userV)

        db.commit()

        Digaku.engine.eventStream.emit(DeleteUserEvent(user))
    }
}
