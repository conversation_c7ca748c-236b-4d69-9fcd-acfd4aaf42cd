///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.dao
//
//import com.ansvia.digaku.persistence.ExpiringMap
//
///**
// * Author: robin
// *
// */
//private[digaku] object Throttle {
//    val lastWriteRecords = new ExpiringMap[String,String](10,5)
//}
