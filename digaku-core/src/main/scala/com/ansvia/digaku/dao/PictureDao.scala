/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.event.impl.CreatePictureEvent
import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException, PermissionDeniedException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model._
import com.ansvia.digaku.stream.StreamBuilder
import com.ansvia.graph.IdGraphTitanDbWrapper._
import com.ansvia.perf.PerfTiming


/**
 * Author: nadir, robin
 *
 */
trait PictureDao[idT, dbT <: GraphType] extends DbAccess
    with DaoBase[dbT, Picture]
    with <PERSON>lf4jLogger
    with PerfTiming {

    import com.ansvia.digaku.model.Label._
    import com.ansvia.graph.BlueprintsWrapper._


    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.PictureDaoRootVertex"


    /**
     * create post picture
     * @param creator creator see [[com.ansvia.digaku.model.User]]
     * @param title picture title
     * @param origin trait origin
     * @param smallUrl small cropped url (thumb)
     * @param mediumUrl medium cropped url
     * @param originalUrl original size url
     * @param noEmitEvent jangan di-emit ke event stream listener.
     * @param noTx lakukan commit
     * @param autoStreamToChannel langsung dimasukkan ke dalam stream group
     * @param autoStreamToCreator langsung dimasukkan ke dalam stream creator
     * @param publishedContent dijadikan PublishedContent atau tidak, false bila
     *                         picture ini merupakan bagian dari picture group
     * @param smallScaledUrl small scaled url
     * @param mediumScaledUrl medium scaled url
     * @return
     */
    def create(creator: User, title: String, origin: Origin[dbT],
               smallUrl:String="", mediumUrl:String="", originalUrl:String="",
               noEmitEvent:Boolean=false, noTx:Boolean=false, autoStreamToChannel:Boolean=true,
               autoStreamToCreator:Boolean=true, publishedContent:Boolean=true, smallScaledUrl:String="", mediumScaledUrl:String=""):Picture = {

        assert(creator != null, "no creator")
        assert(origin != null, "no origin")

        origin match {
            case ch:Forum =>

                if (ch.isBlockedUser(creator)){
                    throw PermissionDeniedException("you has been blocked in this group")
                }

            case _ =>
        }

        if (creator.isLocked)
            throw PermissionDeniedException("You can not write a post picture because it has been suspended")

        // sementara didisable, lakukan validation di layer atas-nya
//        if(!PostValidator.validArticleTitle(title))
//            throw InvalidParameterException("Min title 3 and max title 100 characters")
        if (title.length > 500)
            throw InvalidParameterException("Max title length 500 characters")


        val picture = Picture(title).saveWithLabel(VertexLabels.CONTENT).toCC[Picture].getOrElse {
            throw new DigakuException("Cannot create picture")
        }

        picture --> ORIGIN --> origin.reload().asInstanceOf[DbObject]

        val now = Digaku.engine.dateUtils.nowMilis

        val publishE = creator.getYMPart(CollectionVertex.Kind.PUBLISH_CONTENT) --> PUBLISH_CONTENT --> picture <()
        publishE.setProperty("timeOrder", now)
        publishE.setProperty("post.kind", PostKind.PICTURE)
        publishE.setProperty("targetId", picture.getId)
        publishE.setProperty("publish", publishedContent)
        creator.incrementPublishedContentCount()


        creator.getVertex.setProperty("lastPostTime", now)

        picture.smallUrl = smallUrl
        picture.smallScaledUrl = smallScaledUrl
        picture.mediumUrl = mediumUrl
        picture.mediumScaledUrl = mediumScaledUrl
        picture.largeUrl = originalUrl
        picture.save()

        addToRoot(picture.getVertex)


        origin match {
            case ch:Forum =>

                if (autoStreamToCreator){
                    // langsung aja masukkan ke creator-nya dan group-nya
                    // biar langsung muncul dan lebih reliable + realtime dibanding
                    // mengandalkan event stream
                    StreamBuilder.addToStream(creator, picture, timeFromCreation = true)
                }

                if (autoStreamToChannel){
                    // langsung build stream untuk forum/origin-nya gak perlu ngandalin StreamBuilder

                    val streamE = StreamBuilder.addToStream(ch, picture, timeFromCreation = true)

                    streamE.setProperty("post.sticky", false)
                    streamE.setProperty("locked", false)
                    streamE.setProperty("post.kind", PostKind.kindStrClassToInt(picture.getClass.getName))
                    streamE.setProperty("originId", origin.getId)
                    streamE.setProperty("targetId", picture.getId)
                }


                ch.incrementPictureContentCount()

            case _ =>
                if (autoStreamToCreator){
                    // langsung aja masukkan ke creator-nya dan group-nya
                    // biar langsung muncul dan lebih reliable + realtime dibanding
                    // mengandalkan event stream
                    StreamBuilder.addToStream(creator, picture, timeFromCreation = true)
                }
        }

        if (!noTx)
            db.commit()

        val newPicture = db.getVertex(picture.getId).toCC[Picture].get

        if (!noEmitEvent)
            Digaku.engine.eventStream emit CreatePictureEvent(creator, newPicture)

        newPicture
    }


    /**
     * get post picture dengan id
     * @param id pic id
     * @return
     */
    def getById(id:idT): Option[Picture] = {
        val x = db.getVertex(id)
        if (x!=null){
            x.toCC[Picture]
        } else {
            None
        }
    }



//    /**
//     * digunakan untuk mendapatkan picture yang di block
//     * @param query query untuk mencari blocked picture berdasarkan title nya
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def getBlockedPictures(offset:Int, limit:Int, query:String = "") = {
//        Digaku.engine.searchEngine.searchBlockedPicture(query, offset, limit)
//    }

    /**
     * get list picture
     * @param offset offset
     * @param limit limit
     * @return
     */
    def getList(offset:Int, limit:Int):Iterator[Picture] = {

        getListRight(None, None, offset + limit)
            .slice(offset, offset + limit)

    }

}
