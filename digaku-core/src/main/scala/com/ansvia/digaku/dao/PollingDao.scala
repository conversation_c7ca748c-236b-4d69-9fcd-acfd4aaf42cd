/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model._
import com.ansvia.digaku.validator.PollingValidator
import com.tinkerpop.blueprints.Graph


/**
 * Author: nadir, robin
 *
 */
trait PollingDao[dbT <: Graph, pollT <: Polling]
    extends DaoBase[dbT, pollT] with DbAccess {


    import com.ansvia.graph.BlueprintsWrapper._


    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.PollingDaoRootVertex"

    /**
     * Membuat polling baru.
     * @param title polling title (mandatory)
     * @param desc polling description (mandatory)
     * @param choices pilihan-pilihan yg available (optional)
     * @return
     */
    def create(title:String, desc:String, choices:Seq[PollingChoice]=Nil) ={

        PollingValidator.validateTitle(title)

        val poll = Polling(title, desc)

        val vertex = poll.save()

        val rv = vertex.toCC[Polling].getOrElse{
            throw new DigakuException("Cannot create polling with title %s".format(title))
        }

        addToRoot(rv.getVertex)

        db.commit()


        poll.addChoices(choices)

        rv
    }

}
