///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.dao
//
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.helpers.{RootVertex, DbAccess}
//import com.ansvia.digaku.exc.{NotExistsException, DigakuException, InvalidParameterException}
//import com.ansvia.digaku.model.{Label, User, MessageResponse}
//import scala.collection.JavaConversions._
//import com.ansvia.digaku.event.EventStream
//import com.ansvia.digaku.event.impl.CreatePrivateMessageResponseEvent
//import com.ansvia.commons.logging.Slf4jLogger
//import com.tinkerpop.blueprints.Vertex
//
//
///**
// * Author: nadir
// *
// */
//trait MessageResponseDao[idT, dbT <: GraphType] extends DbAccess
//    with DaoBase[GraphType, MessageResponse] with Slf4jLogger {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//    import Label._
//
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.MessageResponseDaoRootVertex"
////    val rootVertexLabel = DAO_LIST
//
//    /**
//     * create message response
//     * @param participant user
//     * @param content content (String)
//     * @return
//     */
//    @deprecated("gak diperlukan lagi, sudah inline di PrivateMessage.addResponse","29 jan 2014")
//    def create(participant:User, content:String):MessageResponse={
//        if (content.trim.length < 2)
//            throw InvalidParameterException("Min content length 2 characters")
//
//        if (content.trim.length > 20000)
//            throw InvalidParameterException("Max content length 20000 characters")
//
//        val msResponse = MessageResponse(content)
//
//        val vertex = msResponse.save()
//
//        val messageResponse = vertex.toCC[MessageResponse].getOrElse{
//            throw new DigakuException("Cannot create message response with content: %s".format(content))
//        }
//        participant.reload() --> REPLY_WITH --> messageResponse
//
//        addToRoot(vertex)
//
//        db.commit()
//
//            messageResponse
//    }
//
////    /**
////     * Register vertex to root vertex.
////     * @param v
////     * @return
////     */
////    def registerObject(v:Vertex) = {
////        addToRoot(v)
////    }
////
//
//    /**
//     * get message response by message response Id
//     * @param id param message response Id
//     * @return
//     */
//    def getById(id:idT): Option[MessageResponse] = {
//        val x =db.getVertex(id)
//        if (x != null) {
//            x.toCC[MessageResponse]
//        } else {
//            None
//        }
//    }
//
////
////    /**
////     * delete permanent message response
////     * @param id message response Id
////     **/
////    def deleteById(id:idT){
////        val v = db.getVertex(id)
////        if (v == null){
////            throw NotExistsException("Message didn't exists.")
////        }
////
////        transact {
////
////            // hapus linked MessageResponseNotification
////            v.pipe.out(NOTIFICATION).remove()
////            v.pipe.bothE().remove()
////            db.removeVertex(v)
////
//////            db.getVertices("messageResponseId", v.getId).iterator().foreach { notifV =>
//////
//////                notifV.pipe.bothE(NOTIFICATION).remove() //.iterator().map(ed => db.removeEdge(ed))
//////
//////                db.removeVertex(notifV)
//////
//////            }
//////
//////            v.pipe.bothE().iterator().foreach(x => db.removeEdge(x))
//////            db.removeVertex(v)
////        }
////    }
//
//    /**
//     * delete permanent message response
//     * @param messageResponse see[[com.ansvia.digaku.model.MessageResponse]]
//     */
//    override def delete(messageResponse: MessageResponse)(implicit m: Manifest[MessageResponse]){
//        val v = MessageResponse.getById(messageResponse.getId).getOrElse {
//            throw NotExistsException("Message response didn't exists.")
//        }
//
//        //        transact {
//        val messageResponseV = v.getVertex
//
//        messageResponseV.pipe.out(NOTIFICATION).remove()
//
//        messageResponseV.pipe.bothE().remove()
//        //            db.removeVertex(messageResponseV)
//        //        }
//
//        super.delete(messageResponse)(m)
//    }
//
//    /**
//     * Remove message response, this operation not really delete message response object
//     * from database, just marking as deleted.
//     * For permanent deletion use [[com.ansvia.digaku.dao.MessageResponseDao.deleteById]]
//     * [[com.ansvia.digaku.dao.MessageResponseDao.delete]] instead.
//     * @param messageResponse messageResponse to remove.
//     */
//
//    def remove(messageResponse:MessageResponse){
//
//        val v = messageResponse.reload().getVertex
//        v.setProperty("deleted", true)
//
//        db.commit()
//    }
//
//}
