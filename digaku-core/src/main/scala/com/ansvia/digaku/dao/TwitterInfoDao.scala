/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

//package com.ansvia.digaku.dao
//
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.helpers.{RootVertex, DbAccess}
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.model.Label._
//import scala.collection.JavaConversions._
//import com.ansvia.digaku.model.{VertexLabels, TwitterInfo}
//import com.ansvia.digaku.exc.DigakuException
//
///**
// * Author: nadir
// * Date: 11/27/13
// * Time: 11:11 AM
// *
// */
//trait TwitterInfoDao[idT, dbT <: GraphType] extends DbAccess with RootVertex with Slf4jLogger  {
//
//    import com.ansvia.digaku.database.GraphCompat.transact
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.TwitterInfoDaoRootVertex"
//    val rootVertexLabel = DAO_LIST
//
//    /**
//     * digunakan untuk create TwitterInfo
//     * akan selalu mengembalikan TwitterInfo meskipun create twitter info yang sudah ada (tidak throw kan dan
//     * tidak di create hanya mengembalikan TwitterInfo yang sudah ada
//     * @param twId
//     * @return
//     */
//    def create(twId:String): TwitterInfo ={
//        if (!existsByTwId(twId)) {
//            val twitterInfo = TwitterInfo(twId)
//
//            var newTwitterInfo:TwitterInfo = null
//
//            try {
////                transact {
//
//
//                    val vertex = twitterInfo.save()
//
//                    addToRoot(vertex)
//
//                    newTwitterInfo = vertex.toCC[TwitterInfo].getOrElse {
//                        throw new DigakuException("Cannot create twitterInfo : " + twitterInfo)
//                    }
//
//                    newTwitterInfo
//
////                }
//                db.commit()
//            } catch{
//                case e:Exception =>
//                    throw new DigakuException("Cannot create twitterInfo " + twId)
//            }
//            newTwitterInfo
//        } else {
//            getByTwid(twId).getOrElse(throw new DigakuException("Cannot getting TwitterInfo with id: " + twId))
//        }
//    }
//
//    /**
//     * get TwitterInfo berdasarkan twitter id nya
//     * @param twId Twitter ID.
//     * @return
//     */
//    def getByTwid(twId: String): Option[TwitterInfo] = {
//
//        db.query().has("label", VertexLabels.THIRDPARTY_ACCOUNT)
//            .has("tw.id", twId)
//            .vertices()
//            .headOption.flatMap(_.toCC[TwitterInfo])
//
////        db.getVertices("tw.id", twId).headOption.flatMap(_.toCC[TwitterInfo])
//    }
//
//    /**
//     * cek apakah TwitterInfo sudah ada dengan twId di parameter
//     * @param twId
//     * @return
//     */
//    def existsByTwId(twId: String): Boolean = {
//        getByTwid(twId).isDefined
//    }
//
//
//}
