/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.Types._
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.{VertexLabels, PostMark}
import com.ansvia.digaku.validator.PostMarkValidator
import com.ansvia.digaku.exc.{AlreadyExistsException, DigakuException}
import scala.collection.JavaConversions._
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.graph.IdGraphTitanDbWrapper._

/**
 * Author: nadir
 * Date: 4/18/13
 * Time: 10:22 AM
 * 
 */

trait PostMarkDao [idT, dbT <: GraphType] extends DbAccess with DaoBase[GraphType, PostMark] with Slf4jLogger {

    import com.ansvia.graph.BlueprintsWrapper._

    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.PostMarkDaoRootVertex"

    /**
     * create new post Mark
     * @param title title post mark
     * @param color color post mark
     * @return
     */
    def create(title:String, color:String):PostMark = {
        PostMarkValidator.validate(title, color)

        if (getByPostMarkTitle(title).isDefined)
            throw AlreadyExistsException("post mark with title \"%s\" already exists: ".format(title))

        val postMark = new PostMark(title, color)
        //        transact {\
        val vertex = postMark.saveWithLabel(VertexLabels.POSTMARK)
        val newPostMark = vertex.toCC[PostMark].getOrElse{
            throw new DigakuException("Cannot create post mark with title: %s, color: %s".format(title, color))
        }
        addToRoot(vertex)
        db.commit()
        newPostMark
        //        }
    }

    /**
     * get post mark by title
     * @param title title post mark
     * @return
     */
    def getByPostMarkTitle(title:String):Option[PostMark] ={

//        val vx = db.getVertices("title", title)
//            .filter(_.get[String]("_class_")
//            .map(_ == "com.ansvia.digaku.model.PostMark").getOrElse(false))
//            .flatMap(_.toCC[PostMark])
//
//        vx.headOption

        db.query().has("label", VertexLabels.POSTMARK)
            .has("post-mark.title", title.toLowerCase)
            .vertices()
            .headOption.flatMap(_.toCC[PostMark])

//        db.getVertices("post-mark.title", title.toLowerCase)
//            .headOption.flatMap(_.toCC[PostMark])

    }


//
//    private def getViaRootVertex(offset:Int, limit:Int):Iterator[PostMark] = {
//        rootVertex(getRootVertexPartitionCount-1).pipe.out(DAO_LIST)
//            .range(offset, offset + limit)
//            .iterator()
//            .flatMap(_.toCC[PostMark])
//    }

    def getList(offset:Int, limit:Int):Iterator[PostMark] = {

        getListRight(None, None, offset + limit) //.slice(offset, offset + limit)

    }

//    def delete(postMark:PostMark){
//        val vx = db.getVertex(postMark.getId)
//        if (vx == null){
//            throw NotExistsException("Post Mark didn't exists.")
//        }
//
//        transact{
//            vx.pipe.bothE().remove() //.iterator().foreach(x => db.removeEdge(x))
//            db.removeVertex(vx)
//        }
//
//    }
}
