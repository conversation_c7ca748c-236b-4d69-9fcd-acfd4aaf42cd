/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.model._
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types.GraphType
import com.ansvia.digaku.exc.InvalidParameterException

/**
 * Author: nadir, robin
 *
 */
trait ResponseDao[idT] extends DbAccess with DaoBase[GraphType, Response] with Slf4jLogger  {
    import com.ansvia.graph.BlueprintsWrapper._
    import Label._


    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.ResponseDaoRootVertex"

    var maxContentLength = 500000

    /**
     * create response
     * @param creator creator see [[com.ansvia.digaku.model.User]]
     * @param content content of response
     * @return
     */
    def create(creator: User, content: String):Response = {

        if (content.trim.length < 3)
            throw InvalidParameterException("Response min 3 characters.")

        if (content.trim.length > maxContentLength)
            throw InvalidParameterException("Response max " + maxContentLength + " characters.")

        val rs = Response(content)


        creator.reload()

        val vertex = rs.save()

        addToRoot(vertex)

        val response = vertex.toCC[Response].getOrElse{
            throw new DigakuException("Cannot create response with content: %s, desc: %s".format(content))
        }
        creator.getYMPart(CollectionVertex.Kind.RESPONSE) --> RESPONSE_WITH --> response

        db.commit()

        response
    }

    /**
     * get response by response id
     * @param id response id
     * @return
     */
    def getById(id:idT): Option[Response] = {
        val x = db.getVertex(id)
        if (x != null) {
            x.toCC[Response]
        } else {
            None
        }
    }

    //    /**
    //     * delete response by model
    //     * @param response see [[com.ansvia.digaku.model.Response]]
    //     */
    //    def delete(response:Response){
    //        val v = Response.getById(response.getId).getOrElse {
    //            throw NotExistsException("response didn't exists.")
    //        }
    ////        transact{
    //            val responseV = v.getVertex
    //            responseV.pipe.bothE().remove() //.iterator().foreach(x => db.removeEdge(x))
    //            db.removeVertex(responseV)
    ////        }
    //        db.commit()
    //    }
    //
    //    /**
    //     * delete response by response id
    //     * @param id response id
    //     */
    //    def deleteById(id:idT){
    //        val v = db.getVertex(id)
    //        if (v == null){
    //            throw NotExistsException("response didn't exists.")
    //        }
    //        transact(db.removeVertex(v))
    //    }

}
