/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import com.ansvia.digaku.exc._
import com.ansvia.digaku.model._
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.event.impl.{DeleteUserEvent, CreateUserEvent}
import com.ansvia.digaku.helpers.{RootVertex, DbAccess}
import com.ansvia.digaku.Types._
import com.ansvia.commons.logging.Slf4jLogger
import scala.collection.JavaConversions._
import scala.Some

/**
 * Author: temon
 * Date: 11/20/13
 * Time: 11:20 PM
 * 
 */
trait UserConnectDao[idT, dbT <: GraphType] extends DbAccess with RootVertex with Slf4jLogger {

    import com.ansvia.graph.BlueprintsWrapper._


    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.UserConnectDaoRootVertex"
    val rootVertexLabel = DAO_LIST

    def create(uid:String, via:String) = {

        val userConnect = UserConnect(uid, via)

        val vertex = userConnect.save()

        addToRoot(vertex)

        db.commit()

        val newUser = vertex.toCC[UserConnect].getOrElse {
            throw new DigakuException("Cannot create user connect")
        }

        newUser
    }

    /**
     * get user dari database berdasarkan namanya.
     * @param uid nama user
     * @return
     */
    def getByUid(uid: String): Option[UserConnect] = {
        db.getVertices("user-connect.uid", uid).headOption.flatMap(_.toCC[UserConnect])
    }

    /**
     * Check is user exists by name.
     * @param uid user's name.
     * @return
     */
    def existsByUid(uid: String): Boolean = {
        getByUid(uid).isDefined
    }

}
