/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import java.util

import com.ansvia.digaku.Types._
import com.ansvia.digaku.exc.NotExistsException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model._
import com.ansvia.digaku.{Digaku, model}
import com.ansvia.perf.PerfTiming
import com.tinkerpop.blueprints.{Edge, Vertex}
import com.tinkerpop.gremlin.Tokens.T

import scala.collection.JavaConversions._

trait PostDao[dbT <: GraphType] extends DbAccess with DaoBase[GraphType, Post]
    with Popularable with PerfTiming {


    import com.ansvia.digaku.helpers.GremlinHelpers._
    import com.ansvia.digaku.model.Label._
    import com.ansvia.graph.BlueprintsWrapper._

    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.PostDaoRootVertex"
    //    val rootVertexLabel = DAO_LIST





//
//    /**
//     * create simple post
//     * @param creator creator see [[com.ansvia.digaku.model.User]]
//     * @param content content simple post
//     * @param origin trait Origin
//     */
//    def createSimple(creator: User, content: String,
//                     origin: Origin[ dbT], maxLength:Int=160):SimplePost = {
//
//        creator.reload()
//        if (creator.isLocked)
//            throw PermissionDeniedException("You can not write a post because it has been suspended")
//
//        if(maxLength > 0 && content.length > maxLength)
//            throw InvalidParameterException("Max post length " + maxLength + " characters")
//
//        if(content.length < 3)
//            throw InvalidParameterException("Min content 3 characters")
//
//        //        if (!PostValidator.isValidSimpleContent(content))
//        //            throw InvalidParameterException("Invalid content")
//
//
//        origin match {
//            case ch:Group =>
//                if (ch.isDeleted) {
//                    throw PermissionDeniedException("Can't create simple post, channels has removed")
//                }
//                if (!ch.userCanCreateSimplePost_?(creator)) {
//                    val groups = ch.getWhoCanCreateSimplePost
//                    val more = if (groups.length > 0){
//                        "Only `" + groups.mkString(", ") +
//                            "` can create simple post in this group"
//                    } else {
//                        "Group's policy makes Simple post not available for you"
//                    }
//                    throw PermissionDeniedException(more)
//                }
//                if (ch.isBlockedUser(creator)) {
//                    throw PermissionDeniedException("cannot create post, creator is blocked on this group")
//                }
//
//            case _ =>
//        }
//
//        //        var post: SimplePost = null
//
//        //        transact {
//
//        creator.reload()
//        // prevent duplicates/double post
//        creator.getVertex.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT)
//            .out(PUBLISH_CONTENT)
//            .has("_class_", "com.ansvia.digaku.model.SimplePost").asInstanceOf[GremPipeVertex]
//            //                .sort(Comparator.ByCreationTime(Comparator.SortDirection.DESC))
//            .range(0, 2)
//            .headOption map { v =>
//
//            v.get[String]("content") map { oldContent =>
//                if (oldContent.trim.equalsIgnoreCase(content.trim) && v.pipe.out(ORIGIN).has("id", origin.getId).hasNext )
//                    throw new LimitationReachedException("Possibly double post")
//            }
//
//        }
//
//        val newPost = SimplePost(content).saveWithLabel(VertexLabels.CONTENT).toCC[SimplePost].getOrElse {
//            throw new DigakuException("Cannot create simple post")
//        }
//
//        creator.reload()
//
//        // merge conflict
//        //        val publishE = creator.getYearBasedCV(CollectionVertex.Kind.PUBLISH_CONTENT) --> PUBLISH_CONTENT --> newPost <()
//        //        newPost --> ORIGIN --> origin.reload().asInstanceOf[DbObject]
//        // end of merge conflict
//
//        val publishE = creator.getYMPart(CollectionVertex.Kind.PUBLISH_CONTENT) --> PUBLISH_CONTENT --> newPost <()
//        newPost --> ORIGIN --> origin.reload().asInstanceOf[DbObject]
//
//
//        val now = Digaku.engine.dateUtils.nowMilis
//
//        val originV = origin.getVertex
//
//        //        originV.setProperty("simplePostCount", originV.getOrElse("simplePostCount", 0) + 1)
//        //        creatorV.setProperty("publishedContentCount", creatorV.getOrElse("publishedContentCount", 0) + 1)
//
//
//        // langsung aja masukkan ke creator-nya dan group-nya
//        // biar langsung muncul dan lebih reliable + realtime dibanding
//        // mengandalkan event stream
//        StreamBuilder.addToStream(creator, newPost)
//
//
//        // /**
//        //  * Link-kan stream-nya ke group terlebih dahulu apabila originnya adalah group.
//        //  */
//        // if (origin.kind == OriginKind.GROUP){
//        //     val postV = newPost.getVertex
//        //
//        //     val streamE = StreamBuilder.addToStream(origin.asInstanceOf[Origin[GraphType] with CollectionVertex],
//        //         newPost, timeFromCreation = true)
//        //     streamE.setProperty("post.sticky", false)
//        //     streamE.setProperty("locked", false)
//        //     streamE.setProperty("post.kind", PostKind.SIMPLE_POST)
//        //     streamE.setProperty("originId", originV.getId)
//        //     streamE.setProperty("targetId", postV.getId)
//        //
//        // }
//
//        origin match {
//            case ch:Group =>
//                ch.incrementSimplePostCount()
//                // sudah ada di com.ansvia.digaku.stream.StreamBuilder.addToStreamEx
//                // ch.incrementContentCount()
//
//                val postV = newPost.getVertex
//
//                origin.reload()
//                val streamE = StreamBuilder.addToStream(origin.asInstanceOf[Origin[GraphType] with CollectionVertex],
//                    newPost, timeFromCreation = true)
//
//                streamE.setProperty("post.sticky", false)
//                streamE.setProperty("locked", false)
//                streamE.setProperty("post.kind", PostKind.SIMPLE_POST)
//                streamE.setProperty("originId", originV.getId)
//                streamE.setProperty("targetId", postV.getId)
//            case _ =>
//        }
//
//        // hanya digunakan untuk ordering
//        // untuk dapetin waktu pembuatan gunakan creationTime instead.
//        //            wroteE.setProperty(timeOrderKey, now)
//        publishE.setProperty(timeOrderKey, now)
//        publishE.setProperty("post.kind", PostKind.SIMPLE_POST)
//        publishE.setProperty("targetId", newPost.getId)
//
//
//        val postV = newPost.getVertex
//
//        // check for contains video link
//        TextExtractor.extractLinks(content).foreach { link =>
//            if (WebLink.isVideoLink(link)){
//                postV.setProperty("containsVideoLink", true)
//            }
//        }
//
//        val rootEdge = addToRoot(postV)
//        rootEdge.setProperty("kind", PostKind.SIMPLE_POST)
//
//        creator.getVertex.setProperty("lastPostTime", now)
//
//
//        //        }
//        db.commit()
//
//        // update counter and creator's vertex attributes
//        {
//            val contentCount = creator.incrementPublishedContentCount()
//            val simpleTextCount = creator.incrementSimpleTextCount()
//
//            val v = creator.getVertex
//            v.setProperty("publishedContentCount", contentCount)
//            v.setProperty("publishedSimpleTextCount", simpleTextCount)
//        }
//
//
//        Digaku.engine.eventStream.emit(CreatePostEvent(creator, newPost))
//
//        newPost
//    }



//    /**
//     * create question
//     * @param creator creator question dengan User class
//     * @param title title question
//     * @param content content question
//     * @return
//     */
//    def createQuestion(creator: User, title: String, content: String, origin:Origin[ dbT]): Question = {
//
//        assert(creator != null, "no creator")
//        assert(origin != null, "no origin")
//
//        if(content.length > 20000)
//            throw InvalidParameterException("Max question length 500 characters")
//
//        //        transact {
//
//        val post = Question(title, content).saveWithLabel(VertexLabels.CONTENT).toCC[Question].getOrElse {
//            throw new DigakuException("Cannot create question")
//        }
//
//        val rootEdge = addToRoot(post.getVertex)
//        rootEdge.setProperty("kind", PostKind.QUESTION)
//
//        val originV = db.getVertex(origin.getId)
//        val postV = db.getVertex(post.getId)
//
//        //            val wroteE = creator --> WROTE --> postV <()
//        val publishE = creator.getYMPart(CollectionVertex.Kind.PUBLISH_CONTENT) --> PUBLISH_CONTENT --> postV <()
//        post --> ORIGIN --> originV
//
//        //            val creatorV = db.getVertex(creator.getId)
//
//        //            creatorV.setProperty("publishedContentCount", creatorV.getOrElse("publishedContentCount", 0) + 1)
//
//
//        // langsung aja masukkan ke creator-nya dan group-nya
//        // biar langsung muncul dan lebih reliable + realtime dibanding
//        // mengandalkan event stream
//        StreamBuilder.addToStream(creator, post)
//
//
//        if (origin.kind == OriginKind.GROUP){
//            val streamE = StreamBuilder.addToStream(origin.asInstanceOf[Origin[GraphType] with CollectionVertex], post)
//            streamE.setProperty("post.sticky", false)
//            streamE.setProperty("locked", false)
//            streamE.setProperty("post.kind", PostKind.QUESTION)
//            streamE.setProperty("originId", originV.getId)
//        }
//
//        val now = Digaku.engine.dateUtils.nowMilis
//
//        // hanya digunakan untuk ordering
//        // untuk dapetin waktu pembuatan gunakan creationTime instead.
//        //            wroteE.setProperty(timeOrderKey, now)
//        publishE.setProperty(timeOrderKey, now)
//        publishE.setProperty("post.kind", PostKind.QUESTION)
//        publishE.setProperty("targetId", post.getId)
//
//        //            wroteE.setProperty("creationTime", now)
//
//        creator.getVertex.setProperty("lastPostTime", now)
//
//        //            post
//
//        //        }
//
//        db.commit()
//
//        creator.incrementPublishedContentCount()
//
//        origin match {
//            case ch:Group =>
//                ch.incrementContentCount()
//            case _ =>
//        }
//
//        post
//
//    }



    //    private val cal = Calendar.getInstance()

    /**
     * add vertex to root, all dao create operation should
     * use this method to add their ref to root vertex.
     * non transactional, should be executed inside of
     * transact.
     * @param vertex vertex to add.
     * @return
     */
    override def addToRoot(vertex: Vertex): Edge = {
        //        super.addToRoot(vertex)
        vertex.getOrElse("_class_", "") match {
//            case "com.ansvia.digaku.model.SimplePost" =>
//                SimplePost.addToRoot(vertex)
//
//                // apabila terdapat link ke video, masukkan juga ke
//                // Video root vertex
//                if (vertex.getOrElse("containsVideoLink", false)){
//                    Video.addToRoot(vertex)
//                }
            case "com.ansvia.digaku.model.Article" =>
                Article.addToRoot(vertex)
//            case "com.ansvia.digaku.model.Question" =>
//                Question.addToRoot(vertex)
        }
        super.addToRoot(vertex)
    }

    //    def addToStream(channelV:Vertex, targetItemV:Vertex, timeFromCreation:Boolean=false) = {
    //        val ed = channelV --> STREAM --> targetItemV <()
    //
    //        // gak perlu pake targetId karena gak terlalu berguna?
    //        ed.setProperty("targetId", targetItemV.getId)
    //
    //        val crTime =
    //            if (timeFromCreation){
    //                targetItemV.getOrElse("creationTime", 0L)
    //            }else{
    //                Digaku.engine.dateUtils.nowMilis
    //            }
    //        // set property streamKind untuk filtering stream
    //        targetItemV.toCC[Post].foreach { post =>
    //            ed.setProperty("streamKind", StreamKind.streamableToStreamKindInt(post))
    //        }
    //
    //        ed.setProperty("timeOrder", crTime)
    ////        cal.setTime(new Date(crTime))
    ////        val year = cal.get(Calendar.YEAR)
    ////        val month = cal.get(Calendar.MONTH)
    ////        ed.setProperty("year", year)
    ////        ed.setProperty("month", month)
    //
    //        // jumlah content total yang ada di stream group ini
    //        val inc = channelV.getOrElse("contentCount", L) + 1
    //        channelV.setProperty("contentCount", inc)
    //
    //        ed.setProperty("offset", inc)
    //
    //        ed
    //    }




    /**
     * get Post dengan param Id
     * @param id id post
     * @return
     */
    def getPostById(id:IDType):Option[Post] = {
        import com.ansvia.digaku.model.PostKind._
        val v = db.getVertex(id)
        if (v != null){
            val p = v.toCC[Post]
            if (p.isDefined){
                p.get.kind match {
                    case ARTICLE =>
                        v.toCC[Article]
//                    case SIMPLE_POST =>
//                        v.toCC[SimplePost]
//                    case QUESTION =>
//                        v.toCC[Question]
//                    case DEAL =>
//                        v.toCC[Deal]
                }
            }else{
                None
            }
        }else {
            None
        }
    }

    /**
     * digunakan untuk mendapatkan article yang di block
     * @param query query untuk mencari article yang di block berdasarkan title article
     * @param offset
     * @param limit
     * @return
     */
    def getBlockedArticles(offset:Int, limit:Int, query:String = "") = {
        Digaku.engine.searchEngine.searchBlockedArticle(query, offset, limit)
    }

//    /**
//     * digunakan untuk mendapatkan video yang di block
//     * @param query query content dari simple post yang embed video
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def getBlockedVideos(offset:Int, limit:Int, query:String = "") = {
//        Digaku.engine.searchEngine.searchBlockedVideo(query, offset, limit)
//    }


    def getRecommendedArticles(forUser:User, offset:Int, limit:Int) = {

        timing("PostDao.getRecommendedArticles"){
            import com.ansvia.digaku.model.Label.{LIKES, SUPPORT}

            val ignored = new util.LinkedList[Vertex]()

            forUser.getRawVertex.pipe.out(COLLECTION_VERTEX).has("kind",CollectionVertex.Kind.LIKES)
                .out(LIKES).has("_class_","com.ansvia.digaku.model.Article")
                .asInstanceOf[GremPipeVertex]
                .aggregate(ignored).iterate()

            forUser.getRawVertex.pipe.out(SUPPORT).range(0,100).out(COLLECTION_VERTEX)
                .has("kind", CollectionVertex.Kind.LIKES).out(LIKES).range(0,100)
                .has("_class_","com.ansvia.digaku.model.Article").as("article")
                .out(ORIGIN).hasNot("privated", true).back("article")
                .asInstanceOf[GremPipeVertex]
                .except(ignored)
                .range(0, 100)
                .groupCount.cap()
                .orderMap(T.decr)
                .range(offset, offset + limit - 1)
                .asInstanceOf[GremPipeVertex]
                .toIdVertex
                .flatMap(_.toCC[Article])
        }
    }


    /**
     * delete post menggunakan Id
     * Ini akan menghapus post secara permanent.
     * @param id id post
     */
    def deletePostById(id:IDType) {
        val v = db.getVertex(id)
        if (v == null){
            throw NotExistsException("post didn't exist")
        }

        // also remove responses linked to this post
        v.pipe.in(RESPONSE_OF).iterator().foreach { respV =>
            respV.pipe.bothE().remove()
            db.removeVertex(respV)
        }

        // check apakah ada referensi origin ke sini?
        // misalnya FreePicture?
        // apabila ada hapus semuanya.
        v.pipe.in(ORIGIN).iterator().foreach { refV =>
            refV.pipe.bothE().remove()
            db.removeVertex(refV)
        }

        v.pipe.bothE().remove()

        db.removeVertex(v)

        db.commit()

        daoListIndex.removeById(id)

        // refresh cache
        //        vertexCache.refresh("popular-articles:0:5")
    }

    /**
     * delete post
     * @param post post
     */
    def deletePost(post: model.Post) {
        deletePostById(post.getId)
    }

    //
    //    /**
    //     * Get all post count includes Article and SimplePost
    //     * and all model that inherits [[com.ansvia.digaku.model.Post]]
    //     * @return
    //     */
    //    def getCount = {
    //        rootVertex.getOrElse("count", 0L)
    //    }



}

