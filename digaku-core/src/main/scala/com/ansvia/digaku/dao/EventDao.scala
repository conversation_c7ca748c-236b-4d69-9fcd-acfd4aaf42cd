/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.dao

import java.util.Date

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types.GraphType
import com.ansvia.digaku.event.impl.CreateEventEvent
import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException, NotExistsException, PermissionDeniedException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model._
import com.ansvia.digaku.stream.StreamBuilder
import com.tinkerpop.blueprints.Graph
import org.joda.time.DateTime

import scala.collection.JavaConversions._
import com.ansvia.digaku.utils.ForumSettings._

/**
 * Author: nadir
 *
 */
trait EventDao[dbT <: Graph] extends DbAccess with DaoBase[dbT, Event] with Slf4jLogger {

    import com.ansvia.digaku.model.Label._
    import com.ansvia.graph.BlueprintsWrapper._


    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.EventDaoRootVertex"
//    val rootVertexLabel = DAO_LIST

    /**
     * Create new event
     * @param user creator.
     * @param title event title.
     * @param content event description / content.
     * @param location where event will be performed.
     * @param origin event origin.
     * @param startDate due date, when event will be performed.
     * @param finishDate finish date, when event will be finished.
     * @return
     */
    def create(user:User, title:String, content:String, location:String, origin:Origin[dbT], startDate:Date, finishDate:Date):Event = {
        assert(user != null, "no creator")
        assert(origin != null, "no origin")

        if (user.isLocked) {
            throw PermissionDeniedException("You can not write a event because it has been suspended")
        }

        origin match {
            case ch:Forum =>
                if (!ch.userCanCreateEvent_?(user)) {
                    if (ch.getWhoCanCreateEvents.isEmpty) {
                        throw PermissionDeniedException("Only the owner can create event")
                    } else {
                        throw PermissionDeniedException("you should be a %s to create event"
                            .format(ch.getWhoCanCreateEvents.reduceLeftOption(_ + " or " + _).getOrElse("")))
                    }
                }

                if (ch.isBlockedUser(user)) {
                    throw PermissionDeniedException("You has been blocked in this Group")
                }

            case _ =>
        }

        if (title.trim.length > 160 || title.trim.length < 3) {
            throw InvalidParameterException("length of event title, min 3 and max 160 character")
        }

        if (content.trim.length < 3 || content.trim.length > 2000) {
            throw InvalidParameterException("length of content, min 3 and max 2000 character")
        }

        if (startDate.getTime < new DateTime().plusDays(1).getMillis) {
            throw InvalidParameterException("Waktu mulai event minimal 24 jam dari waktu saat ini.")
        }

        if (finishDate.getTime <= startDate.getTime) {
            throw InvalidParameterException("Finish date should be more than start date")
        }

        if (location.trim.length < 3 || location.trim.length > 100) {
            throw InvalidParameterException("length of location, min 3 and max 100 character")
        }

        val event = Event(title, content, location, startDate.getTime, finishDate.getTime).save().toCC[Event].getOrElse {
            throw new DigakuException("Cannot create event")
        }

        val originV = origin.getVertex
        val eventV = event.getVertex
        val publishE = user.getYMPart(CollectionVertex.Kind.CREATE) --> CREATE --> eventV <()

        event --> ORIGIN --> originV

        val now = Digaku.engine.dateUtils.nowMilis

        publishE.setProperty("creationTime", now)
        publishE.setProperty("timeOrder", now)

        addToRoot(event.getVertex)

        originV.setProperty("eventCount", originV.getOrElse("eventCount", 0) + 1)

        /**
         * Add event reminder to creator
         * this allowing us to iterate reminder globally in easy way.
         */
        EventReminder.create(event, user, noTx = true)

        db.commit()

        Digaku.engine.eventStream.emit(CreateEventEvent(user, event))

        event
    }


    def getById(id:GraphType): Option[Event] = {
        val x = db.getVertex(id)
        if (x!=null){
            x.toCC[Event]
        } else {
            None
        }
    }

    def deleteById(id:GraphType) {
        val v = db.getVertex(id)
        if (v == null){
            throw NotExistsException("event didn't exists.")
        }
        v.pipe.bothE().iterator().foreach(x => db.removeEdge(x))
        db.removeVertex(v)

        db.commit()
    }

    def delete(event: Event) {
        val _event:Event = Event.getById(event.getId).getOrElse {
            throw NotExistsException("event didn't exists.")
        }
//        transact {
            val eventV = _event.getVertex

            // hapus semua respon-nya dan edges ke respon tersebut.
            eventV.pipe.in(RESPONSE_OF).iterator().foreach { v =>
                v.pipe.bothE().iterator().foreach(db.removeEdge)
                db.removeVertex(v)
            }

            // hapus semua edges yang menghubungkan-nya.
            eventV.pipe.bothE().iterator().foreach(db.removeEdge)

            db.removeVertex(eventV)
//        }
        db.commit()
    }

    /**
     * Close event.
     * @param event event to close.
     */
    def close(event:Event){
        val eventV = db.getVertex(event.getVertex.getId)
        eventV.setProperty("closed", true)
        eventV.setProperty("lastUpdate", System.currentTimeMillis())

        db.commit()
    }

    /**
     * Open closed event.
     * @param event event to open.
     */
    def open(event:Event){
        val eventV = db.getVertex(event.getVertex.getId)
        eventV.setProperty("closed", false)
        eventV.setProperty("lastUpdate", System.currentTimeMillis())

        db.commit()
    }

//    /**
//     * Get all events count registered to this root vertex.
//     * @return
//     */
//    def getCount = {
//        rootVertex.getOrElse("count", 0L)
//    }

}
