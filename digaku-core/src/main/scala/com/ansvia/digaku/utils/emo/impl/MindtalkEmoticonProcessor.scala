/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils.emo.impl

import com.ansvia.digaku.utils.emo.{EmoMapper, BaseDirEmoticonGroupProcessor, BaseDirEmoticonProcessor}

/**
 * Author: robin
 *
 */
class MindtalkEmoticonProcessor() extends BaseDirEmoticonGroupProcessor("BCA MC2", "mindtalk") with EmoMapper {


    private lazy val emoMap = Map(
        ":)" -> "smile",
        ":(" -> "sad",
        ":omg" -> "holymother",
        ":p" -> "gotyou",
        ":o" -> "holymother",
        ":d" -> "grin",
        ":sip" -> "jempol"
    )


    override protected val hiddenEmos: List[String] = List(
        "jempol", "banana"
    )

    def mapEmo(emoCode: String) = {
        emoMap.getOrElse(emoCode.toLowerCase, emoCode.substring(prefixSign.length))
    }

    override lazy val emos: Seq[BaseDirEmoticonProcessor] = getEmos.sortBy(_.name)


}

