/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import scala.util.Random

/**
 * Author: nadir, robin
 * 
 */
object StringCodeGenerator {

    private var inc:Long = 0L
    private val chars = ('a' to 'z') ++ ('A' to 'Z') ++ ('1' to '9') ++ Seq('_','-','.')

    private def uniqueRandomKey(length: Int, uniqueFunc: String=>Boolean) : String = {
        synchronized {
            val newKey = (1 to length).map(x => chars(Random.nextInt(chars.length))).mkString
            if (uniqueFunc(newKey)){
                inc += 1
                val millis = System.currentTimeMillis().toString
                newKey + millis.substring(9,13) + inc.toString
            }else
                uniqueRandomKey(length, uniqueFunc)
        }
    }

    protected def isUnique(key:String):Boolean = {
        true
    }

    def nextId():String = {
        uniqueRandomKey(1, isUnique)
    }

}