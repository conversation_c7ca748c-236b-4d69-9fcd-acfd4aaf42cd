package com.ansvia.digaku.utils

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.{Global, Digaku}
import com.ansvia.digaku.Types._
import com.ansvia.digaku.persistence.{SeqStore, SnowFlakeIdFactory, CassandraBackedSeqStore}
import com.netflix.astyanax.model.ColumnFamily
import com.netflix.astyanax.serializers.{ByteSerializer, LongSerializer, StringSerializer}

/**
 * Author: fajr
 *
 * Untuk menyimpan urutan dari object object yang dapat di arrange
 *
 */


object ArrangeSettings extends Slf4jLogger {

    protected val idGen = new SnowFlakeIdFactory(Digaku.engine.config.machineId)
    def generateId(): Long = idGen.createId().asInstanceOf[Long]

    private lazy val CF = new ColumnFamily[String, java.lang.Long](
        Global.STANDARD_CF_NAME,
        StringSerializer.get(),
        LongSerializer.get(),
        ByteSerializer.get())

    lazy val seqStore = new CassandraBackedSeqStore(CF,
        Digaku.config.mainDatabase.keyspaceName,
        Digaku.config.mainDatabase.clusterName,
        Digaku.config.mainDatabase.hostName,
        Digaku.config.mainDatabase.replStrategy,
        Digaku.config.mainDatabase.replStrategyOpts)

    private lazy val arrangeList = Digaku.engine.kvStoreProvider.build("arrange-list")

    private def swapWithNext[T](l: List[T], e : T) : List[T] = l match {
        case Nil => Nil
        case `e`::next::tl => next::e::tl
        case hd::tl => hd::swapWithNext(tl, e)
    }

    private def swapWithPrev[T](l: List[T], e : T) : List[T] = swapWithNext(l.reverse, e).reverse

    private def swapToTop[T](l: List[T], e : T) : List[T] = {
        l.indexOf(e) match {
            case -1 => l
            case pos => List(e) ++ l.take(pos) ++ l.drop(pos+1)
        }
    }

    private def remove[T](list: List[T], e:T) = list diff List(e)

    def setArrangedListNew(objList:List[String]) = {
        val newObjList = objList.map(_.toLong)
    }

    /**
     * save new arranged list
     * @param strText
     * @param objList
     */
    def setArrangedList(strText:String, objList:List[String]) = {
        val arrangedList = objList.mkString(",")
        arrangeList.set(strText, arrangedList)
    }

    def setArrangedList[T](seqStore: SeqStore[T], objList:List[String]) = {
        seqStore.clear()
        objList.map { obj =>
            seqStore.insert(obj)
        }
    }

    /**
     * get arranged list
     * @param seqStore
     * @return
     */
    def getArrangedList[T](seqStore:SeqStore[T]):List[String] = {
        getArrangedList(seqStore, 20)
    }

    def getArrangedList[T](seqStore:SeqStore[T], limit:Int):List[String] = {
        seqStore.getStream(None, None, limit).toList.map(_._2)
    }

    /**
     * move an object to move up one step
     * @param seqStore
     * @param objId
     */
    def arrangeUp[T](seqStore: SeqStore[T], objId:IDType) = {
        val newArranged = swapWithPrev(getArrangedList(seqStore), objId.toString)
        setArrangedList(seqStore, newArranged)
    }

    /**
     * move an object down one step
     * @param seqStore
     * @param objId
     */
    def arrangeDown[T](seqStore: SeqStore[T], objId:IDType) = {
        val newArranged = swapWithNext(getArrangedList(seqStore), objId.toString)
        setArrangedList(seqStore, newArranged)
    }

    /**
     * add an object to the list and send a object to the top if not exists yet
     * @param seqStore
     * @param objId
     */
    def moveToTop[T](seqStore: SeqStore[T], objId:IDType) = {
        if (getArrangedList(seqStore).contains(objId.toString)) {
            if (getArrangedList(seqStore).head == objId.toString) {
                getArrangedList(seqStore)
            } else {
                val newArranged = swapToTop(getArrangedList(seqStore), objId.toString)
                setArrangedList(seqStore, newArranged)
            }
        } else {
            val newArranged = List(objId.toString) ++ getArrangedList(seqStore)
            setArrangedList(seqStore, newArranged)
        }
    }

    /**
     * add an object to the list and send a object to the bottom if not exists yet
     * @param seqStore
     * @param objId
     */
    def moveToBottom[T](seqStore: SeqStore[T], objId:IDType) = {
        if (getArrangedList(seqStore).contains(objId.toString)) {
            val newArranged = swapToTop(getArrangedList(seqStore).reverse, objId.toString).reverse
            setArrangedList(seqStore, newArranged)
        } else {
            val newArranged = getArrangedList(seqStore) ++ List(objId.toString)
            setArrangedList(seqStore, newArranged)
        }
    }

    /**
     * remove an object from list if object exists
     * @param seqStore
     * @param objId
     * @return
     */
    def removeFromList[T](seqStore: SeqStore[T], objId:IDType) = {
        if (getArrangedList(seqStore).contains(objId.toString)) {
            val newArranged = remove(getArrangedList(seqStore), objId.toString)
            setArrangedList(seqStore, newArranged)
        }
    }
}
