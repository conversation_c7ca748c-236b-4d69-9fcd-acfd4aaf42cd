/*
 * Copyright (c) 2013-2017 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

/**
 * Author: nadir (<EMAIL>)
 */

/**
 * Hapus semua html tags, sehingga element seperti <span>/<div> akan hilang semua
 * tersisa hanya plain text saja.
 */
abstract class HtmlTagsCleaner {
    def compile(text:String):String
}

object NopHtmlTagsCleaner extends HtmlTagsCleaner {
    def compile(text: String) = {
        text
    }
}
