/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import com.ansvia.util.idgen.RandomStringGenerator

/**
 * Author: robin
 * Date: 10/15/13
 * Time: 12:47 PM
 *
 */
object PasswordGenerator extends RandomStringGenerator {
    override protected val stringLength = 8

    def generate() = nextId()
}
