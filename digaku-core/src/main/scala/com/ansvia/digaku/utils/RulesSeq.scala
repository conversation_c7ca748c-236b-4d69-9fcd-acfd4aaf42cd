/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils


/**
 * Author: robin
 *
 */
class RulesSeq[T] {

    @volatile private var rules:List[T] = Nil

    protected def safe_?(f: => Any) {
        f
//        doneBoot match {
//            case false => f
//            case _ => throw new IllegalStateException("Cannot modify after boot.");
//        }
    }

    def prepend(r: T): RulesSeq[T] = {
        safe_? {
            rules = r :: rules
        }
        this
    }

    def remove(f: T => Boolean) {
        safe_? {
            rules = rules.filterNot(f)
        }
    }

    def append(r: T): RulesSeq[T] = {
        safe_? {
            rules = rules ::: List(r)
        }
        this
    }

    def clear(){
        rules = Nil
    }

    def getRules = rules
}

object RulesSeq {
    def apply[T]:RulesSeq[T] = new RulesSeq[T]()
}