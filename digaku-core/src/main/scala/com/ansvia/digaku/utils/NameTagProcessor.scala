/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

/**
 * Author: robin
 * 
 */
abstract class NameTagProcessor {
    def compile(text:String):String = {
        var rv = NameTagProcessor.CHANNEL_TAG_RE.replaceAllIn(text, (z) => handleBefore(z.group(1)))
        rv = NameTagProcessor.USER_TAG_RE.replaceAllIn(rv, z => handleBefore(z.group(1)))
        rv
    }
    private def handleBefore(tag:String):String = handle(tag.trim)
    def handle(tag:String):String
}

abstract class NameCompiler {
    def compileUserName(str:String):String
    def compileChannelName(str:String):String
}

object NameTagProcessor {
    val CHANNEL_TAG_RE = """(?<![\w/])(#[a-zA-Z0-9\-\_\.]{2,30}[^[\w]])""".r
    val USER_TAG_RE = """(?<![\w/@#^%&*])(@[a-zA-Z0-9\-\_\.]{2,30}[^[\w]])""".r
}

object NopNameTagProcessor extends NameTagProcessor {
    def handle(tag: String) = {
        tag
    }
}

object NopNameCompiler extends NameCompiler {
    def compileUserName(str: String) = str
    def compileChannelName(str: String) = str
}
//
//abstract class LinkProcessor {
//    def compile(text:String):String
//}
//
//class NopLinkCompiler extends LinkProcessor {
//    override def compile(str:String) = str
//}
//
