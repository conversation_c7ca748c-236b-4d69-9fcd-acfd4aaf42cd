/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

/**
 * Author: uba<PERSON>, robin
 * 
 */

/**
 * digunakan untuk mentransformasi link pada text.
 */
abstract class LinkTransformer {
    def compile(text:String):String = {
        LinkTransformer.LINK_NODE_RE.replaceAllIn(text, { (z) =>
            handleBefore(z.group(0))
        })
    }
    private def handleBefore(elm:String):String = handle(elm.trim)
    def handle(linkStr:String):String
}

object LinkTransformer {
    val LINK_NODE_RE = """<a[\s]+[^>]*?href[\s]?=[\s\"\']*(.*?)[\"\']*.*?>([^<]+|.*?)?<\/a>""".r
}

class NopLinkTransformer extends LinkTransformer {
    def handle(text: String) = {
        text
    }
}
