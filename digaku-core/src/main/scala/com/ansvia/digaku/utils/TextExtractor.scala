/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import com.ansvia.digaku.validator
import java.net.URLDecoder

/**
 * Author: nadir, robin
 *
 * class ini digunakan untuk mengextract data dari suatu text.
 * sebagai contoh extract user names yang pake prefix `@`
 * extract group name yang pake prefix `#` extract url dlsb.
 */

case class RegexResult(result:String, var startIndex:Int)

object TextExtractor {

    // scalastyle:off line.size.limit
    private val LINK_REGEX = """https?\://[\w\.,\\\-_=\+\(\)/\?&;~\|!:%\\[\\]]+(?<!!)""".r
    private val USER_NAME_REGEX =
        """(?<![\w#\!@\$%\^&\*\(\)\{\}\-\+\/\\\|<>\=])(@(?:[\w\.^\s\-])*?)(?![\w\.#@\$%\^&\*\(\)\{\}\-\+\/\\\|<>\=])""".r
    private val CHANNEL_NAME_REGEX =
        """(?<![\w#\!@\$\%\^\&\*\-\+\/\\\|<>\=])#(?=[a-zA-Z])([\w\.\-]+[^\.\(\)\{\}\-\n\s;\!\+\?\"<>\[\],\:])(?![\w#@\$\%\^\&\*\-\+\/\\\|<>\=;])(?!\".*?>)""".r
    private val COMMON_PIC_LINK = """https?\://[\w\.,\\\-_=\+\(\)/]+?\.(png|jpg|jpeg|tga|gif)|!(https?\://[\w\.,\\\-_=\+\(\)/]+)!""".r
    private val LINK_TEXTILE = """(\[.*?\]|"[^""]+":)(https?\://[a-zA-Z0-9][a-zA-Z0-9\.\-_/\?%#\(\)\+;&=,!\:]+)""".r
    private val USER_ID_REGEX = """<span\s+class=\"mentioned\"\s+[^>]*data\-user\-id="(\d+)"[^>]*/?.*?>@(?:[\w\.^\s\-]*?)</span>""".r
    private val USER_GROUP_ID_REGEX = """<span\s+class=\"mentioned\sgroup"\s+[^>]*data\-user\-group\-id="(\d+)"[^>]*/?.*?>@(?:[\w\.^\s\-]*?)</span>""".r
    val USER_ID_FROM_APP_REGEX = """\@\[(?!group\:)([^\]\:]+):(\d+)\]""".r
    // regex untuk extract user group name dan id yang digunakan pada api
    val USER_GROUP_ID_FROM_APP_REGEX = """\@\[group:([^\]\:]+):(\d+)\]""".r
    private val USER_DOMAIN_REGEX = """(?<![\w#\!@\$%\^&\*\(\)\{\}\-\+\/\\\|<>\=])((?:[\w\.^\s\-])*?)(?![\w\.#@\$%\^&\*\(\)\{\}\-\+\/\\\|<>\=])""".r
    // scalastyle:on line.size.limit

    /**
     * Extract user names from given text,
     * i.e: user name like @robin @Danny etc.
     * this will return names without `@` sign.
     * @param text text to extract.
     * @param includeQuote true jika text menyertakan quote
     * @return
     */
    def extractUserNames(text:String, includeQuote:Boolean = true): Iterator[String] = {
        var rv = text
        if (!includeQuote) {
            rv = rv.replaceAll("""(?s)\[quote=.*\][\r\n]?(.*)[\r\n]?\[\/quote\]""", "")
        }
        for (rawUname <- USER_NAME_REGEX.findAllIn(rv))
            yield rawUname.substring(1).trim
    }

    /**
     * Digunakan untuk meng-extract user id dari html text span
     * e.g: <span class="mentioned" data-user-id="659592351424974800">@andrie</span>
     * @param html content html string
     * @return id dari user yang di-mention
     */
    def extractUserIds(html:String):Iterator[Long] = {
        for (rawUid <- USER_ID_REGEX.findAllIn(html).matchData.map(re => re.group(1)))
            yield rawUid.toLong
    }

    /**
     * Digunakan untuk meng-extract user group id dari html text span
     * e.g: <span class="mentioned group" data-user-group-id="905028041691160577" data-user-name="BOD">@BOD</span>
     * @param html content html string
     * @return id dari user group yang di-mention
     */
    def extractUserGroupIds(html:String):Iterator[Long] = {
        for (rawUid <- USER_GROUP_ID_REGEX.findAllIn(html).matchData.map(re => re.group(1)))
            yield rawUid.toLong
    }


    /**
     * Digunakan untuk meng-extract user id yang dikirim dari apps dengan bentuk data seperti berikut
     * "halooo @[fajr hidayat febriansyah:659592351424974800] apa kabar"
     * hanya akan diambil user id saja, yaitu berupa 659592351424974800
     * @param text
     * @return
     */
    def extractUserIdFromApp(text:String):Iterator[Long] = {
        for (rawId <- USER_ID_FROM_APP_REGEX.findAllIn(text).matchData.map( re => re.group(2))) yield rawId.toLong
    }

    /**
     * Extract group names from given text,
     * i.e: group name like #info #mindtalk etc.
     * this will return names without `#` sign.
     * @param text text to extract.
     * @return
     */
    def extractChannelNames(text:String) = {
        CHANNEL_NAME_REGEX.findAllIn(text).map(_.substring(1).trim).toSeq
    }


    /**
     * Extract links from given text.
     * @param text text to search and extract.
     * @return
     */
    def extractLinks(text:String) = {
        for( url <- LINK_REGEX.findAllIn(text); if (validator.UrlValidator.isValid(url)) )
            yield url.trim
    }

    /**
     * Extract links from given text with startIndex.
     * @param text text to search and extract
     * @return see [[com.ansvia.digaku.utils.RegexResult]]
     */
    def extractResultLink(text:String): Iterator[RegexResult]  = {
        for( url <- LINK_REGEX.findAllIn(text).matchData.map(re => RegexResult(re.group(0), re.start(0)));
             if (validator.UrlValidator.isValid(url.result)) )
        yield url
    }

    /**
     * Extract links created by textile from given text with startIndex.
     * @param text text to search and extract
     * @return see [[com.ansvia.digaku.utils.RegexResult]]
     */
    def extractResultLinksTextile(text:String): Iterator[RegexResult] = {
        for( url <- LINK_TEXTILE.findAllIn(text).matchData.map(re => RegexResult(re.group(2), re.start(2)));
             if (validator.UrlValidator.isValid(url.result)) )
            yield  url
    }

    /**
     * Extract picture link dari text.
     * @param text text yang akan diextract.
     * @return
     */
    def extractPicLinks(text:String) = {
        COMMON_PIC_LINK.findAllIn(text).map { url =>
            if (url.startsWith("!") && url.endsWith("!")) {
                url.substring(1, url.length - 1)
            }else {
                url
            }
        }.filter(validator.UrlValidator.isValid)
    }

    /**
     * Periksa apakah suatu text mengandung link ke picture.
     * @param text
     * @return
     */
    def containsPicLinks(text:String) =
        COMMON_PIC_LINK.findFirstIn(text).isDefined

    /**
     * Digunakan untuk mendapatkan link dari format textile
     * contoh link content yang sudah dalam bentuk textile:
     * "http://mindtalk.com":http://digakusite.com/url-go?url=http%3A%2F%2Fmindtalk.com%2Fu%2Fusername
     * setelah di-extract akan menjadi: http://mindtalk.com/u/username
     * @param str
     * @return
     */
    def extractLinkTextile(str:String) = {
        LINK_TEXTILE.replaceAllIn(str, { re =>
            val rv = re.group(0)
            val url = re.group(2)
            val caption = re.group(1).substring(1, re.group(1).length -2)

            if (caption.trim.startsWith("http")) {
                val safeUrl = url.split("/url-go\\?url=")
                if (safeUrl.size > 1) {
                    URLDecoder.decode(safeUrl.apply(1), "UTF-8")
                } else {
                    url
                }
            } else {
                rv
            }
        })
    }

    def userDomainValidation(name: String) = USER_DOMAIN_REGEX.findFirstIn(name).isEmpty
}
