/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import java.util.zip.CRC32

import org.apache.commons.codec.digest.DigestUtils
import scala.language.implicitConversions

/**
 * Author: robin
 *
 */
object RichString {

    private val STRIP_RE = """\W+""".r
    private val TRIMMER_RE = """^[\-_=]|[\-_=]$""".r

    sealed case class RichString(str:String){

        /**
         * Generate nice bot friendly url.
         * Ex:
         *
         *      mindtalk! is cool
         *
         * become:
         *
         *
         *      mindtalk-is-cool
         *
         * @return
         */
        def urlize:String = {
            if (str == null)
                return ""

            var rv = str
            if (rv.length > 50)
                rv = rv.substring(0, 50)
            TRIMMER_RE.replaceAllIn(STRIP_RE.replaceAllIn(rv,"-").trim, "")
        }

        def camelize = {
            STRIP_RE.split(str).map(x => x.substring(0,1).toUpperCase + x.substring(1))
                .foldLeft("")(_ + _)
        }


        /**
         * Truncate / potong string apabila melebih batas :max.
         * @see [[com.ansvia.digaku.utils.RichString.RichString.truncateRight]]
         * @param max max karakter untuk di-truncate.
         * @param withTrailingDot jika di-set false maka tiak akan ditambahkan 3 titik
         *                        di pas potongannya.
         * @return
         */
        def truncate(max:Int, withTrailingDot:Boolean=true):String = {
            if (str == null)
                return ""

            if (str.length > max)
                str.substring(0, max - 3) + (if(withTrailingDot) "..." else "")
            else
                str
        }

        /**
         * Get md5 hash for this string.
         * @return
         */
        def md5 = DigestUtils.md5Hex(str)

        /**
         * Get CRC32 hash for this string,
         * don't use this for high security requirement.
         * @return Long
         */
        def crc32 = {
            val checksum = new CRC32()
            checksum.update(str.getBytes("UTF-8"))
            Math.abs(checksum.getValue)
        }

        /**
         * Sama seperti truncate tapi ini dari kanan.
         * @see [[com.ansvia.digaku.utils.RichString.RichString.truncate)]]
         * @param max max karakter untuk di-truncate.
         * @return
         */
        def truncateRight(max:Int) = {
            if (str == null)
                ""
            else if (str.length > max)
                "..." + str.substring((str.length-max) + 4, str.length)
            else
                str
        }


        def alphaNormalize =
            STRIP_RE.replaceAllIn(str," ")

    }

    implicit def stringToRichString(str:String) = RichString(str)

}
