/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import com.ansvia.util.idgen.RandomStringGenerator

/**
 * Author: nadir
 *
 */
object StringIdGenerator {

    private val strIdGen = new RandomStringGenerator()

    def generate(prefix:String):String = prefix + strIdGen.nextId()
    def generate(prefix:String, postFix:String):String = generate(prefix) + postFix
    def generate:String = strIdGen.nextId()

}
