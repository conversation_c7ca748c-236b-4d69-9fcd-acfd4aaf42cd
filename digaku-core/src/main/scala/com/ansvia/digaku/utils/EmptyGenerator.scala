/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import com.ansvia.util.idgen.PrefixedIncrementalNumIdGenerator
import java.util.Random

/**
 * Author: nadir
 * Date: 11/15/13
 * Time: 1:59 PM
 * 
 */


/**
 * generate phone number
 * untuk ketika user tidak memasukan phone number (empty)
 */
object EmptyPhoneNumberGenerator  {

    private object _generator extends PrefixedIncrementalNumIdGenerator(1L, "+000") {
        private val rnd = new Random()
        rnd.setSeed(System.currentTimeMillis())
        def nextNumber = "%s%s%s".format(math.round(System.currentTimeMillis()), nextId(), rnd.nextInt())
    }

    def nextNumber = {
        _generator.nextNumber
    }

}

/**
 * generate empty email untuk menggenerasikan email
 * bagi user yang belum ada email-nya.
 */
object EmptyEmailGenerator {
    private object _generator extends PrefixedIncrementalNumIdGenerator(1L, "") {
        private val rnd = new Random()
        rnd.setSeed(System.currentTimeMillis())
        override def nextId(): String = {
            "%<EMAIL>".format(math.round(System.currentTimeMillis()), super.nextId(), rnd.nextInt())
        }
    }

    /**
     * generate new unique dummy email
     * @return
     */
    def generate() = {
        _generator.nextId()
    }
}