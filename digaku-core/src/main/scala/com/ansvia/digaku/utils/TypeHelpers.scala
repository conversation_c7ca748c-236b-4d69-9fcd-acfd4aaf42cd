/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

/**
 * Author: robin (<EMAIL>)
 */
/**
 * Author: robin
 *
 */
object AsLong {
    def unapply(x:String) = {
        try {
            Some(x.toLong)
        }
        catch {
            case e:NumberFormatException =>
                None
        }
    }
}

object AsInt {
    def unapply(x:String) = {
        try {
            Some(x.toInt)
        }
        catch {
            case e:NumberFormatException =>
                None
        }
    }
}

