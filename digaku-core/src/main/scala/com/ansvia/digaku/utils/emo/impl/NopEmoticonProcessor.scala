/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils.emo.impl

import com.ansvia.digaku.utils.emo.EmoticonProcessor

/**
 * Author: robin
 *
 */
object NopEmoticonProcessor extends EmoticonProcessor("nop") {
    def compile(text: String, save:Boolean) = text


    def getUrl(text: String): String = text

    /**
     * Show registered emo
     * @return
     */
    def list = Seq.empty[String]
}

