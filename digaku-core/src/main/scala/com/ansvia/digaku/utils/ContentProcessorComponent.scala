/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import com.ansvia.digaku.utils.emo.EmoticonProcessor
import com.ansvia.digaku.utils.emo.impl.NopEmoticonProcessor

/**
 * Author: robin (<EMAIL>)
 */

trait ContentProcessorComponent {

    def contentProcessor:ContentProcessor

    trait ContentProcessor {

        var emoticonProcessor:EmoticonProcessor = NopEmoticonProcessor
        val textTagProcessor:NameTagProcessor = NopNameTagProcessor
        val textNameCompiler:NameCompiler = NopNameCompiler
        val htmlTagCleaner:HtmlTagsCleaner = NopHtmlTagsCleaner
        val linkTransformer:LinkTransformer
    }

    object NopContentProcessor extends ContentProcessor {
        override val linkTransformer: LinkTransformer = new NopLinkTransformer
    }
}
