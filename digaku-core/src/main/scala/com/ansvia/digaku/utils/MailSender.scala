/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserve
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import java.util.Properties
import javax.mail._
import javax.mail.internet.{InternetAddress, MimeMessage}
import com.ansvia.digaku.model.User
import com.ansvia.digaku.Digaku
import com.ansvia.commons.logging.Slf4jLogger
import akka.actor.{Props, Actor}
import java.io.FileOutputStream

/**
 * Author: nadir
 * 
 */


case class Smtp(host:String, port:Int)
sealed case class SendEmail(smtp:Smtp, from:String, subject:String, message:String, contentType:String, recipients:Seq[User])
sealed case class SendEmailByEmailAddress(smtp:Smtp, from:String, subject:String, message:String, contentType:String, recipients:Seq[String])

sealed class MailSenderActor extends Actor with Slf4jLogger {
    def receive:PartialFunction[Any, Unit] = {
        case SendEmail(smtp, from, subject, message, contentType, recipients) =>
            MailSender.sendBlocking(smtp, from, subject, message, contentType, recipients: _*)

        case SendEmailByEmailAddress(smtp, from, subject, message, contentType, address) =>
            MailSender.sendBlockingByAddress(smtp, from, subject, message, contentType, address: _*)
    }
}

/**
 * Mail sender class digunakan untuk
 * mengirimkan email menggunakan smtp host yang
 * telah ditentukan
 * @param smtpHost smtp host name or IP address.
 * @param smtpPort smtp port
 */
case class MailSender(smtpHost:String, smtpPort:Int){

    /**
     * Send email asynchronously by default
     * @param from pair sender email address and user name in this format: [EMAIL-ADDRESS]:[USER-NAME]
     * @param subject email subject.
     * @param message email message.
     * @param recipients target recipients.
     */
    def send(from:String, subject:String, message:String, recipients: User*){
        MailSender.sendNonBlocking(Smtp(smtpHost, smtpPort), from, subject, message, "text/html", recipients: _*)
    }

    /**
     * Send email asynchronously by default
     * @param from pair sender email address and user name in this format: [EMAIL-ADDRESS]:[USER-NAME]
     * @param subject email subject.
     * @param message email message.
     * @param contentType mime kind.
     * @param recipients target recipients.
     */
    def send(from:String, subject:String, message:String, contentType:String, recipients: User*){
        MailSender.sendNonBlocking(Smtp(smtpHost, smtpPort), from, subject, message, contentType, recipients: _*)
    }

    /**
     * Send email asynchronously by default, send email using email address
     * @param from pair sender email address and user name in this format: [EMAIL-ADDRESS]:[USER-NAME]
     * @param subject email subject.
     * @param message email message.
     * @param contentType mime kind.
     * @param address target recipients.
     */
    def sendByAddress(from:String, subject:String, message:String, contentType:String, address: String*){
        MailSender.sendNonBlockingByAddress(Smtp(smtpHost, smtpPort), from, subject, message, contentType, address: _*)
    }
}

object MailSender extends Slf4jLogger {

    var debug = false

    private lazy val asyncWorker = {
        Digaku.engine.actorSystem.actorOf(Props[MailSenderActor], "asynch-email-sender")
    }

    /**
     * Blocking send mail operation
     * jangan panggil ini dari blocking environment.
     * @param subject
     * @param message
     * @param recipients
     */
    def sendBlocking(smtp:Smtp, from:String, subject:String, message:String, contentType:String, recipients: User*){
        val emailAddress = recipients.map(_.emailLogin)
        sendNonBlockingByAddress(smtp, from, subject, message, contentType,emailAddress:_* )
    }

    /**
     * Blocking send mail operation menggunakan email address (String)
     * jangan panggil ini dari blocking environment.
     * @param subject
     * @param message
     * @param emailAddress
     */
    def sendBlockingByAddress(smtp:Smtp, from:String, subject:String, message:String, contentType:String, emailAddress: String*){
        try {
            val props = new Properties()

            props.put("mail.smtp.host", smtp.host)
            props.put("mail.smtp.port", smtp.port.toString)
            props.put("mail.smtp.auth", "false")
            props.put("mail.debug", "true")
            props.put("mail.smtp.ssl.enable", "false")

            //            val session = Session.getInstance(props, new SocialAuth())
            val session = Session.getDefaultInstance(props, null) // tanpa user name dan password

            val msg = new MimeMessage(session)

            val fromL = from.split(":")

            val fromAddr = fromL(0)
            val fromName = fromL(1)

            msg.setFrom(new InternetAddress(fromAddr, fromName))

            val toAddresses = emailAddress.map(address => new InternetAddress(address).asInstanceOf[Address]).toArray

            msg.setRecipients(Message.RecipientType.TO, toAddresses)

            msg.setSubject(subject)
            msg.setContent(message, contentType + "; charset=utf-8")

            if (debug){

                val outPath = "/tmp/digaku-mail-debug.html"
                info("mail sender running in debug mode, all email output goes to " + outPath)

                val os = new FileOutputStream(outPath)
//                msg.setHeader("Content-Transfer-Encoding", "8bit")
                msg.writeTo(os)
                os.close()

            }else{
                Transport.send(msg)
            }


        } catch {
            case e:Exception => error(e.getMessage)
        }
    }

    def sendNonBlocking(smtp:Smtp, from:String, subject:String, message:String, contentType:String, recipients: User*){
        asyncWorker ! SendEmail(smtp, from, subject, message, contentType, recipients)
    }

    def sendNonBlockingByAddress(smtp:Smtp, from:String, subject:String, message:String, contentType:String, recipients: String*){
        asyncWorker ! SendEmailByEmailAddress(smtp, from, subject, message, contentType, recipients)
    }

    case class SocialAuth(fromAddr:String, password:String) extends Authenticator {
        override def getPasswordAuthentication() = {
            new PasswordAuthentication(fromAddr, password)
        }
    }
}
