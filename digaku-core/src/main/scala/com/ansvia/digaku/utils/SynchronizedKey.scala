package com.ansvia.digaku.utils

import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.locks.ReentrantLock

import com.ansvia.commons.logging.Slf4jLogger

import scala.concurrent.duration._
import scala.collection.JavaConversions._
import com.ansvia.digaku.Digaku

/**
  * Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
  */


/**
  * Untuk synchronized berdasarkan key dari sebuah object
  * ref:
  * - https://stackoverflow.com/questions/5639870/simple-java-name-based-locks/20306423#20306423
  * - https://docs.oracle.com/javase/tutorial/essential/concurrency/syncmeth.html
  * @tparam T Key data type
  */
trait SynchronizedKey[T] extends Slf4jLogger {

    case class History(rl:ReentrantLock, var lastCall:Long) {
        def updatLastCall() = {
            this.lastCall = Digaku.engine.dateUtils.nowMilis
            this
        }
    }

    private val chm: ConcurrentHashMap[T, History] = new ConcurrentHashMap[T, History]()

    private val dropDuration = 30.minutes.toMillis

    /**
      * Menghapus values hashmap yang sudah expired (30 menit)
      */
    private def cleanHashMap(): Unit = {
        chm.synchronized {
            var freed = 0

            val toRemove = this.chm.clone().filter { case (key, h) =>
                h.lastCall + dropDuration <= System.currentTimeMillis()
            }
            toRemove.foreach { x =>
                this.chm.remove(x._1)
                freed = freed + 1
            }

            if (freed > 0) {
                info(s"Maintenance synchronizedKey done, freed $freed object(s)")
            }
        }
    }

    /**
      * Mensinkronisasikan operasi berdasarkan key tertentu
      * @param key
      * @param operation
      * @tparam R
      * @return
      */
    def synchronizedKey[R](key:T)(operation: => R): R = {

        val initValue: History = History(new ReentrantLock(), Digaku.engine.dateUtils.nowMilis)

        var hist: History = chm.putIfAbsent(key, initValue)

        if (hist == null) {
            hist = initValue
        }

        try {
            hist.rl.lock()

            operation

        } finally {
            hist.rl.unlock()

            chm.update(key, hist.updatLastCall())
            cleanHashMap()
        }
    }
}
