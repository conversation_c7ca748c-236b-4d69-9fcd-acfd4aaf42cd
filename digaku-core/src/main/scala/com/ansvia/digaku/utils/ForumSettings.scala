/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import com.ansvia.digaku.exc.{PermissionDeniedException, UnsupportedException}
import com.ansvia.digaku.model.{FileAttachable, ForumFeatures, User}
import com.ansvia.digaku.{Digaku, model}

/**
 * Author: robin
 *
 * Untuk penyimpanan forum settings yang sifatnya dynamic,
 * disimpan menggunakan Cassandra.
 */
sealed trait ForumSettings {

    implicit class ForumSettingsWrapper(forum:model.Forum){
        lazy val settings = Digaku.engine.kvStoreProvider.build("forum-settings-" + forum.getId)

        private val featuresText = "features"
        private val maximumFileSizeAllowedText = "max-size-allowed"
        private val allowedFileTypesText = "allowed-file-types"

        /**
         * Digunakan untuk menyimpan berapa maximum ukuran file yang di izin kan untuk
         * di-attach pada artikel dalam forum ini.
         * Juga informasi dari type file apa saja yang diperbolehkan
         */
        def getMaximumFileSizeAllowed = {
            settings.get(maximumFileSizeAllowedText, 0L)
        }

        /**
         * @return see [[com.ansvia.digaku.model.FileAttachable#allowedFileTypeGroups]]
         */
        def getAllowedFileType:String = {
            settings.get(allowedFileTypesText, "")
        }

        /**
         * @return List [[com.ansvia.digaku.model.FileAttachable#allowedFileTypeGroups]]
         */
        def getAllowedFileTypeList:List[String] = {
            settings.get(allowedFileTypesText, "").split(",").filter(_.trim.nonEmpty).toList
        }

        def getAllowedMimeType = {
            getAllowedFileTypeList.flatMap(mime => FileAttachable.allowedFileTypeGroups.getOrElse(mime, List(mime)))
                .flatMap(FileAttachable.allowedMimeTypes.get)
        }

        private def toStr(features:Seq[String]) = {
            features.map(_.trim.toLowerCase).filter(_.length > 0).distinct.mkString(",")
        }


        /**
         * Add features to this forum.
         * @param features features to add, @see [[com.ansvia.digaku.model.ForumFeatures]]
         */
        def addFeatures(features: String*) {
            for (feature <- features) {
                if (!ForumFeatures.isSupported(feature))
                    throw UnsupportedException("feature not supported: " + feature)
            }
            settings.set(featuresText, toStr(features))
        }

        def getFeatures = {
            settings.get(featuresText, "").split(",")
        }


        /**
         * Remove features from this forum.
         * @param features features to remove, @see [[com.ansvia.digaku.model.ForumFeatures]]
         */
        def removeFeatures(features: String*) {
            val supportedFeatures = this.getFeatures.filterNot(features.contains)
            settings.set(featuresText, toStr(supportedFeatures))
        }


        /**
         * Check whether forum support feature.
         * @param feature to check, @see [[com.ansvia.digaku.model.ForumFeatures]]
         * @return
         */
        def hasFeature(feature: String) = {
            this.getFeatures.contains(feature.trim.toLowerCase)
        }


        private val permissionArticleText = "permission.who-can-create-article"
        private val permissionEventText = "permission.who-can-create-event"
        private val permissionResponseText = "permission.who-can-response"

        /**
         * Set permission forum for who can create post in this forum.
         * @param forumPermissions permission forum @see [[com.ansvia.digaku.model.ForumPermission]]
         */
        def setWhoCanCreateArticle(forumPermissions: String*) {
//            this.whoCanPosts = forums.toArray
            settings.set(permissionArticleText, toStr(forumPermissions))
        }

        /**
         * Set permission forum for who can create event in this forum.
         * @param forumPermissions permission forum @see [[com.ansvia.digaku.model.ForumPermission]]
         */
        def setWhoCanCreateEvents(forumPermissions: String*) {
//            this.whoCanCreateEvents = forums.toArray
            settings.set(permissionEventText, toStr(forumPermissions))
        }


        /**
         * Set permission forum for who can create response in this forum.
         * @param forumPermissions permission forum @see [[com.ansvia.digaku.model.ForumPermission]]
         */
        def setWhoCanResponses(forumPermissions: String*) {
//            this.whoCanResponses = forums.toArray
            settings.set(permissionResponseText, toStr(forumPermissions))
        }

        /**
         * get permission forum for who can create post in this forum.
         * @return Array[String]
         */
        def getWhoCanCreateArticle = settings.get(permissionArticleText, "").split(",")

        /**
         * get permission forum for who can create event in this forum.
         * @return Array[String]
         */
        def getWhoCanCreateEvents =  settings.get(permissionEventText, "").split(",")

        /**
         * get permission forum for who can create response in this forum.
         * @return Array[String]
         */
        def getWhoCanResponses =  settings.get(permissionResponseText, "").split(",")

        /**
         * remove permission forum for who can create post.
         */
        def removeWhoCanCreateArticle(forumPermission: String) {
            val perms = getWhoCanCreateArticle.filterNot(_.equalsIgnoreCase(forumPermission))
            settings.set(permissionArticleText, toStr(perms))
        }

        /**
         * remove permission forum for who can create event.
         * @param forumPermission permission forum @see [[com.ansvia.digaku.model.ForumPermission]]
         */
        def removeWhoCanCreateEvent(forumPermission: String) {
            val perms = getWhoCanCreateEvents.filterNot(_.equalsIgnoreCase(forumPermission))
            settings.set(permissionEventText, toStr(perms))
        }

        /**
         * remove permission forum for who can create response.
         * @param forumPermission permission forum @see [[com.ansvia.digaku.model.ForumPermission]]
         */
        def removeWhoCanResponse(forumPermission: String) {
            val perms = getWhoCanResponses.filterNot(_.equalsIgnoreCase(forumPermission))
            settings.set(permissionResponseText, toStr(perms))
        }

        /**
         * digunakan untuk check apakah user bisa create post dalam forum
         * @param user @see [[com.ansvia.digaku.model.User]]
         * @return
         */
        def userCanCreateArticle_?(user: User): Boolean = {
            true // currently always true, implement this as you wish
        }

        /**
         * digunakan untuk check apakah user bisa create event dalam forum
         * @param user @see [[com.ansvia.digaku.model.User]]
         * @return
         */
        def userCanCreateEvent_?(user: User): Boolean = {
            true // currently always true, implement this as you wish
        }

        /**
         * digunakan untuk check apakah user bisa create response dalam forum
         * @param user see [[com.ansvia.digaku.model.User]]
         * @return
         */
        def userCanResponse_?(user: User): Boolean = {
            true // currently always true, implement this as you wish
        }

        /**
         * Set maximum file size
         * @param size
         */
        def setMaximumFileAttachmentSize(size:Long) = {
            settings.set(maximumFileSizeAllowedText, size)
        }

        /**
         * set file mime types that allowed to be attached in this forum
         * @param fileTypes
         */
        def setFileTypeAllowed(fileTypes:List[String]) = {
            fileTypes.foreach { fileType =>
                if (!(FileAttachable.allowedFileTypeGroups.exists(fileType == _._1) ||
                    FileAttachable.allowedMimeTypes.exists(fileType == _._1))) {
                    throw PermissionDeniedException("Not allowed attachment with mime type: " + fileType)
                }
            }

            settings.set(allowedFileTypesText, fileTypes.mkString(","))
        }

        /**
         * add file mime types that allowed to be attached in this forum
         * @param fileType
         */
        def addFileTypeAllowed(fileType:String) = {
            val currentAllowed = settings.get(allowedFileTypesText, "")
            if (FileAttachable.allowedMimeTypes.keys.toList.contains(fileType) ||
                !currentAllowed.split(",").contains(fileType)
            ) {
                val allowed = currentAllowed + "," + fileType
                settings.set(allowedFileTypesText, allowed)
            }
        }

    }

}

object ForumSettings extends ForumSettings


