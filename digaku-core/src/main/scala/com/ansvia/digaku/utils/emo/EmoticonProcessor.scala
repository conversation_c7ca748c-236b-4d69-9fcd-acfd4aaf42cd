/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils.emo

import java.util.UUID
import scala.collection.mutable

/**
 * Author: robin
 *
 */
abstract class EmoticonProcessor(val name:String) {

    /**
     * Compile text to spawn emoticon.
     * @param text text to compile.
     * @return
     */
    def compile(text:String, save:Boolean=false):String

    def getUrl(text:String):String

    /**
     * Show registered emo
     * @return compilable available emoticon name.
     */
    def list:Seq[String]


    def genSavedCode =
        "[saved-code-" + UUID.randomUUID().toString + "]"

    private var savedData = mutable.HashMap[String, String]()

    protected def saveData(id:String, data:String) = {
        savedData.synchronized {
            savedData += id -> data
        }
        this
    }

    protected def eachSavedData(func: (String, String) => Unit){
        savedData.synchronized {
            savedData.foreach { case (id, data) =>
                func.apply(id, data)
            }
            savedData.clear()
        }
    }

}

abstract class EmoticonGroupProcessor(name:String) extends EmoticonProcessor(name){
    val emos:Seq[BaseDirEmoticonProcessor]

    def groupList:Map[String, Seq[String]]
}
