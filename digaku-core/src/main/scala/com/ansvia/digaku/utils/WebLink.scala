/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import com.ansvia.digaku.model.LinkKind

/**
 * Author: robin
 * Date: 1/26/14
 * Time: 12:05 AM
 *
 */
object WebLink {

    // @TODO(robin): add to unittest

    import LinkKind._

    private val VIDEO_LINK_RE =
        ("""(https?\://(www\.)?(m\.)?youtube\.com?(\/\?\#)?/watch\?[\w\.\-_\=&]+|""" + // untuk youtube
            """https?\://(www\.)?youtu\.be/[\w\.\-_\=&]+|""" + // untuk youtu.be
            """https?\://(v\.)?youku\.com/v_show/[\w\.\-\=&]+|""" + // untuk youku
            """^https?\://(v\.)?vimeo\.com/?(channels/staffpicks/)?(m/)?\d+""" + // untuk vimeo
            ")").trim.r

    private val OEMBED_LINK_RE =
        (
            """(https?\://(www\.)?slideshare\.net/(.*)/(.*)+|""" + // untuk slideshare
            """https?\://(www\.)?scribd\.com/(.*)+|""" + // untuk scribd
            """https?\://(www\.)?kickstarter\.com/projects/(.*)+""" + // untuk kickstarter.com
            ")").trim.r

    /**
     * Cek apakah link merupakan video link
     * @param link
     * @return
     */
    def isVideoLink(link:String) = {
        VIDEO_LINK_RE.pattern.matcher(link).matches()
    }

    /**
     * cek apakah link merupakan oembed link (ex: slideshare, scribd, kickstarter)
     * punya widget untuk di embed, di handle di jquery.oembedall.js untuk web nya
     * @param link
     * @return
     */
    def isOEmbedLink(link:String) = {
//        println (OEMBED_LINK_RE.pattern.matcher(link).matches().toString)
//        println (link)
        OEMBED_LINK_RE.pattern.matcher(link).matches()
    }

    /**
     * Dapatkan jenis link.
     * @see [[com.ansvia.digaku.model.LinkKind]]
     * @param link link url string.
     * @return [[com.ansvia.digaku.model.LinkKind]]
     */
    def getLinkKind(link:String) = {
        link match {
            case l if isVideoLink(l) => VIDEO
            case s if isOEmbedLink(s) => OEMBED
            case _ => TEXT
        }
    }

}
