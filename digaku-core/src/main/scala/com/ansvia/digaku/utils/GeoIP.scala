///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.utils
//
//import com.maxmind.geoip2.DatabaseReader
//import java.net.InetAddress
//import com.maxmind.geoip2.record.Country
//import java.io.IOException
//import com.maxmind.geoip2.model.OmniResponse
//
///**
// * Author: alamybs, robin
// * Date: 11/16/13
// * Time: 2:45 PM
// *
// */
//object GeoIP {
//
//    import collection.mutable
//
//    private val database = //new File("etc/GeoLite2-Country.mmdb")
//        getClass.getClassLoader.getResourceAsStream("geoip/GeoLite2-City.mmdb")
//
//    private val reader = new DatabaseReader.Builder(database).build()
//
//
//    private val cache = new mutable.HashMap[String, OmniResponse] with mutable.SynchronizedMap[String, OmniResponse]
//
//
//    def getLocation(ipAddress:String) = {
//        cache.getOrElseUpdate(ipAddress, {
//            reader.omni(InetAddress.getByName(ipAddress))
//        })
//    }
//
//
//    /**
//     * untuk mendapatkan class Country dari IP address
//     * yang nantinya bisa diambil countryname dan country code nya
//     * @param ipAddress ip address
//     * @return
//     */
//    def getCountry(ipAddress:String):Option[Country] = {
//        try {
//            Some(getLocation(ipAddress).getCountry)
//        } catch {
//            case e:IOException =>
//                e.printStackTrace()
//                None
//        }
//    }
//
//    /**
//     * digunakan untuk mendapatkan country name dari IP address
//     * @param ipAddress ip address
//     * @return
//     */
//    def getCountryName(ipAddress:String): String = {
//        getCountry(ipAddress).map(_.getName).getOrElse("") // 'Indonesia'
//    }
//
//    /**
//     * digunakan untuk mendapatkan country code
//     * @param ipAddress ip address
//     * @return String
//     */
//    def getCountryCode(ipAddress:String):String = {
//        getCountry(ipAddress).map(_.getIsoCode).getOrElse("") // 'ID'
//    }
//
//    def getCity(ipAddress:String) = {
//        try {
//            Some(getLocation(ipAddress).getCity)
//        }
//        catch {
//            case e:IOException =>
//                e.printStackTrace()
//                None
//        }
//    }
//
//
//}
