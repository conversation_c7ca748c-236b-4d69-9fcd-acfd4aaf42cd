/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import java.text.SimpleDateFormat
import java.util.{Calendar, Date}

import org.joda.time.DateTime
import org.ocpsoft.prettytime.PrettyTime

/**
 * Author: robin
 *
 */
trait DateUtils {


    /**
     * Get current date.
     * @return
     */
    @deprecated("gunakan getCurrentTime", "15 okt 2015")
    def now = new Date()

    /**
     * Get current time millis.
     * @return
     */
    def nowMilis = getCurrentTime().getMillis


    def getCurrentYear = {
        new DateTime().getYear
    }
    def getCurrentMonth = {
        new DateTime().getMonthOfYear
    }
    def getCurrentDayOfMonth = {
        new DateTime().getDayOfMonth
    }

    /**
     * Get previous date with decrement interval.
     * eg: if we need previous 2 days then use:
     *
     *    getLastDay(interval=2)
     *
     * @param interval interval.
     * @return
     */
    def getLastDay(interval:Int=1) = {
        new DateTime().minusDays(interval).toDate
    }

    /**
     * Get next month in interval.
     * eg: if we need to get next 3 month
     * then use:
     *
     *    getAddMonth(interval=3)
     *
     * @param interval interval.
     * @return
     */
    def getAddMonth(interval:Int=1) = {
        new DateTime().plusDays(interval).toDate
    }


    def getCurrentTime() = {
        new DateTime()
    }

    def isToday(dateMillis:Long): Boolean = {
        val now = Calendar.getInstance()
        val cal = Calendar.getInstance()
        cal.setTime(new Date(dateMillis))
        now.get(Calendar.YEAR) == cal.get(Calendar.YEAR) &&
            now.get(Calendar.DAY_OF_YEAR) == cal.get(Calendar.DAY_OF_YEAR)
    }

    def isYesterday(dateMillis:Long):Boolean = {
        val now = Calendar.getInstance()
        now.add(Calendar.DAY_OF_YEAR, -1)

        val cal = Calendar.getInstance()
        cal.setTime(new Date(dateMillis))
        now.get(Calendar.YEAR) == cal.get(Calendar.YEAR) &&
            now.get(Calendar.DAY_OF_YEAR) == cal.get(Calendar.DAY_OF_YEAR)
    }

}

object RichDate {

    /**
     * Standard date format without time: yyyy/MM/dd
     */
    val standardDateFormat = new SimpleDateFormat("yyyy/MM/dd")
    val standardDateFormatWithDays = new SimpleDateFormat("EEEE, MMMM dd, yyyy")
    private val standardDateFormatWithTime = new SimpleDateFormat("dd MMMM yyyy 'at' HH:mm")
    private val standardDateFormatWithoutDaysName = new SimpleDateFormat("dd MMMM yyyy")
    val yearAndMonthDF = new SimpleDateFormat("yyyy/MM")
    val onlyTime = new SimpleDateFormat("HH:mm")


    class WrapRichDate(d:Date){

        /**
         * Convert date to standard format
         * in yyyy/MM/dd
         * @return
         */
        def toStdFormat = standardDateFormat.format(d)

        /**
         * Convert date to standard format
         * in EEEE, MMMM dd, yyyy
         * ex: Wednesday, December 18, 2013
         * @return
         */
        def toSdtFormatWithDays = standardDateFormatWithDays.format(d)

        /**
         * Convert date to standard format
         * in dd MMMM yyyy HH:mm
         * ex: 29 April 2015 18:00
         * @return
         */
        def toStdFormatWithTime = standardDateFormatWithTime.format(d)

        /**
         * Convert date to standard format
         * in dd MMMM yyyy
         * ex: 07 January 2014
         * @return
         */
        def toStdFormatWithoutDays = standardDateFormatWithoutDaysName.format(d)

        /**
         * hanya convert untuk jam nya saja
         * ex: 08:00
         * @return
         */
        def toStdFormatOnlyTime = onlyTime.format(d)

        /**
         * Convert date to relative time age
         * ex: 2 hours ago.
         * @param simplified if true, ex: 2h
         * @return
         */
        def toRelative(simplified:Boolean = false) = {
            val prettyTime = new PrettyTime(new Date()).format(d)

            if (simplified) {
                val time = prettyTime.split(" ")
                val timeValue = time.apply(0)
                time.apply(1) match {
                    case "minutes" =>
                        timeValue + "m"
                    case "hour" | "hours" =>
                        timeValue + "h"
                    case "day" | "days" =>
                        timeValue + "d"
                    case "week" | "weeks" =>
                        timeValue + "w"
                    case "month" =>
                        "4w"
                    case "months" =>
                        (timeValue.toInt * 4) + "w"
                    case "year" | "years" =>
                        timeValue + "y"
                    case _ =>
                        //                        cal.setTime(d)
                        //                        cal.get(Calendar.SECOND).toString + "s"
                        new DateTime().getSecondOfMinute + "s"
                }
            } else
                prettyTime
        }
    }

    implicit def dateToRichDate(d:Date) = new WrapRichDate(d)

}

//object Digaku.engine.dateUtils extends Digaku.engine.dateUtils
