/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import java.net.{MalformedURLException, URI, URLEncoder}
import java.util.UUID
import java.util.regex.Matcher

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.utils.RichString._
import com.ansvia.digaku.validator.UrlValidator
import net.liftmodules.textile.TextileParser
import org.apache.commons.lang.StringEscapeUtils

import scala.collection.mutable.ListBuffer
import scala.xml._
import com.ansvia.digaku.model.User
import org.apache.commons.lang3.StringEscapeUtils.unescapeHtml4

/**
 * Author: nadir, robin, temon, surya, andrie
 *
 */

/**
 * Utiliti yang digunakan untuk mentransform plain text
 * menjadi formatted text, sebagai contoh :) akan menjadi emoticon element `img`
 * user name tag @robin akan menjadi link ke user profile-nya,
 * channel name tag #channel akan menjadi link ke channel-nya, dll.
 */
object TextCompiler {

    // untuk format url umum pakai ini, agar regex urlnya benar2 valid
    private val VALID_URL = """https?\://[a-zA-Z0-9][a-zA-Z0-9\.\-_/\?%#@~\(\)\+;&=,!\:]+"""

    private val CODE_RE = """(?s)```([a-z-A-Z0-9]+)(.*?)```""".r
    private val CODE_NO_LANG_RE = """(?s)```(.*?)```""".r
    private val INLINE_CODE_RE = """`(.*?)`""".r
    private val IMG_CODE_RE = """\[img\](.*?)\[/img\]""".r
    private val HTML_HEADER_RE = """<?h[1-6]>(.*?)</?h[1-6]>""".r
    private val QUOTE_RE = """(?s)\[quote=(.*?);([0-9]*?)\](.*?)\[/quote\]""".r
    private val QUOTE_NONAME_RE = """(?s)\[quote\](.*?)\[/quote\]""".r
    //tidak untuk link yang menggunakan textile
    private val LINK_RE = ("""(?<!\!)(?<!\])(?<!\":)""" + VALID_URL) .r
    private val TEXTILE_IMG_LINK = ("""!(""" + VALID_URL + """)!""").r
    private val TEXTILE_OEMBED_LINK = ("""oembed!(""" + VALID_URL + """)!oembed""").r
    private val OEMBED_AT_EDITOR = """(<a class=\"embedded-object\" target=\"_blank\"\s+[^>]*href="([^"]*)"[^>]*/?.*?></a>|<a rel=\"nofollow\"\s+[^>]*href="([^"]*)" target="_blank" class="embedded-object"/>)""".r
    private val IMG_NODE_RE = """<img\s+[^>]*src="([^"]*)"[^>]*/?.*?>""".r
    private val IFRAME_NODE_RE = """<iframe\s+[^>]*src="([^"]*)"[^>]*.*?><\/iframe>""".r
    private val IMG_OEMBED_NODE_RE = """<img class=\"embedded-object\"\s+[^>]*src="([^"]*)"[^>]*/?.*?>""".r
    private val SPOILER_RE = """(?s)\[\[\[(.*?)\]\]\]""".r
    private val EMBEDDABLE_RE =
        ("""(https?\://(www\.)?youtube\.com/watch\?[\w\.\-\=&]+|""" +
        """https?\://(v\.)?youku\.com/v_show/[\w\.\-\=&]+|""" +
        """https?\://(www\.)?youtu\.be/[\w\.\-\=&]+|""" +
        """^https?\://(v\.)?vimeo\.com/\d+|""" +
        """https?\:\/\/(www\.)?slideshare\.net/.*?/.*?\-\d+|""" +
        """https?\:\/\/(www\.)?soundcloud\.com/.*?/[\w\.\-\=&]+)""").trim.r
    // untuk mendapatkan link biasa dan link yang berformat textile
    private val LINK_TEXTILE_RE = (VALID_URL + """|\[.*?\]|"[^""]+":(https?\://[a-zA-Z0-9][a-zA-Z0-9\.\-_/\?%#\(\)\+;&=,!\:]+)""").r

    private val TEXTILE_LINK = """(?:(")([^\"]+)("))(:)(https?\:\/\/[a-zA-Z0-9\.\-_\/\?%#@~\(\)\+;&=,!\:]+)""".r
    private val TEXTILE_BOLD = """(\*)([^\*]+)(\*)""".r
    private val TEXTILE_ITALIC = """(\_)([^\_]+)(\_)""".r
    private val TEXTILE_UNDERLINE = """(\+)([^\+]+)(\+)""".r

    private val SAVED_CODE_TAG = "[saved-code-%s]"
    private val recognizedLanguages = Seq("scala", "javascript", "java", "html", "css", "python", "c")

    private val UTF_8 = "UTF-8"

    private def nextId() = UUID.randomUUID().toString

    /**
     * Main method untuk mentransform text dari Anstile ke format html.
     * @param message input text.
     * @param editorMode compile untuk di tampilkan di editor (default : false)
     * @param showMedia untuk menampilkan button show image di response preview (default : false)
     * @return compiled text.
     */
    def compileMessage(message:String, editorMode:Boolean=false, showMedia:Boolean=false) = {

        assert(message != null, "got null message?")

        // save pre-code first
        var preCode = Map[String, String]()
        var rawCode = Map[String, String]()

        def cleanUpIds(ids:List[String]){
            ids.foreach { k =>
                preCode -= k
                rawCode -= k
            }
        }

        var rv = CODE_RE.replaceAllIn(message, {
            re =>
                val id = genSavedCode()
                val escapedHtml = StringEscapeUtils.escapeHtml(re.group(2)).trim

            val beg = re.group(1).trim match {
                case b if recognizedLanguages.contains(b) => b
                case b => ""
            }

                val compiled = if (beg == ""){
                    "<pre><code>%s %s</code></pre>".format(re.group(1).trim, escapedHtml)
                }else{
                    "<pre><code data-language=\"%s\">%s</code></pre>".format(beg, escapedHtml)
                }

                preCode += id -> compiled
                rawCode += id -> compiled
                id
        })

//        rv = TEXTILE_IMG_LINK.replaceAllIn(rv, { re =>
//            val id = genSavedCode()
//            preCode += id -> re.group(0)
//            id
//        })

        rv = CODE_NO_LANG_RE.replaceAllIn(rv, {
            re =>
                val id = genSavedCode()
                val escapedHtml = StringEscapeUtils.escapeHtml(re.group(1)).trim
                preCode += id -> "<pre><code>%s</code></pre>".format(escapedHtml)
                rawCode += id -> "<pre><code>%s</code></pre>".format(escapedHtml)
                id
        })

        /**
         * inline code tidak perlu di-apa-apain (raw)
         * kecuali apabila dalam bentuk xml/html.
         */
        rv = INLINE_CODE_RE.replaceAllIn(rv, { re =>
            val id = genSavedCode()
            val escapedHtml = StringEscapeUtils.escapeHtml(re.group(1))
            preCode += id -> "<code>%s</code>".format(escapedHtml)
            id
        })

        /**
         * butuh ini karena lift textile compiler lambat
         * kalo mroses text yang diawali dengan tag `@`.
         */
        rv = NameTagProcessor.USER_TAG_RE.replaceAllIn(rv, { re =>
            val id = genSavedCode()
            preCode += id -> compileNameTag(re.group(1))
            rawCode += id -> re.group(1)
            id
        })

        // compile common forum img
        rv = IMG_CODE_RE.replaceAllIn(rv, { re =>
            val id = genSavedCode()
            preCode += id -> """<img src="%s" />""".format(re.group(1))
            id
        })

        rv = TEXTILE_OEMBED_LINK.replaceAllIn(rv, { re =>
            val id = genSavedCode()
            val url = re.group(1).trim

            // lebih dari http://.
            if (url.length > 8){
                val link = new URI(url)
                //                val fancyUrl = getFancyUrl(link)
                if (editorMode) {
                    preCode += id -> """<img class="embedded-object icon icon-play-video" data-url="%s" />""".format(url)
                } else if (showMedia) {
                    preCode += id ->
                        """<div class="hidden-pic" style="display: none;"><a class="embedded-object"
                          | target="_blank" href="%s"></a></div>""".format(url)
                } else {
                    preCode += id -> """<a class="embedded-object" target="_blank" href="%s"></a>""".format(url)
                }
                id
            } else {
                ""
            }
        })

        rv = EMBEDDABLE_RE.replaceAllIn( rv, { re =>
            val id = genSavedCode()
            val url = if (UrlValidator.isValid(re.group(0))) re.group(0).trim else ""
            val link = new URI(url)
            val fancyUrl = getFancyUrl(link)
            preCode += id -> """<a href="%s" class="embedded-object" target="_blank" >%s</a>""".format(url, fancyUrl)
            id
        })

        // digunakan untuk mengubah dari simbol %C3%97 atau %D7 atau × ke huruf x agar url gambar tidak terpotong dan dapat muncul di respon
        rv = rv.replaceAll("""(%C3%97|%D7|×)""","x")
        rv = LINK_RE.replaceAllIn( rv, { re =>
            val id = genSavedCode()
            val url = re.group(0).trim

            try {
                val link = new URI(url)
                val fancyUrl = getFancyUrl(link)

                preCode += id -> """<a href="%s" target="_blank" title="%s">%s</a>""".format(url, url, fancyUrl)
                rawCode += id -> url

                id
            }
            catch {
                case e:MalformedURLException =>
                    rv
            }
        })

        // compile quotation
        rv = QUOTE_RE.replaceAllIn(rv, { re =>
            val id = genSavedCode()
            val userTag = compileUserName(re.group(1))
            // pop back saved code
            var innerContent = re.group(3)
            for( (k,v) <- rawCode ){
                innerContent = innerContent.replace(k, v)
            }
            preCode += id -> ("""<blockquote><strong>%s</strong>'s <a href="javascript://" """ +
                """onclick="openResponse('%s');">response</a>: %s</blockquote>""").format(userTag, re.group(2), innerContent)
            rawCode += id -> "[...]"
            id
        })

        rv = QUOTE_NONAME_RE.replaceAllIn(rv, { re =>
            val id = genSavedCode()
            var innerContent = re.group(1)
            for( (k,v) <- preCode ){
                innerContent = innerContent.replace(k, v)
            }
            preCode += id -> ("""<blockquote>%s</blockquote>""".format(innerContent))
            id
        })

        // normalize textile image link
        rv = TEXTILE_IMG_LINK.replaceAllIn(rv, { re =>
//            val x = URLEncodedUtils.parse(re.group(0),Charsets.UTF_8)
            val link = re.group(1)
            val s = link.split("/")
            if (s.length > 2){
                val ss = s.slice(3, s.length)
                "!" + s.slice(0,3).mkString("/") + "/" + ss.map(part => URLEncoder.encode(part, UTF_8)).mkString("/") + "!"
            }else{
                re.group(0)
            }
        })

        // @deprecated save to remove after dec 2013
//        rv = rv.replaceAll("\\[\\[\\[", "")
//        rv = rv.replaceAll("\\]\\]\\]", "").trim

//        println("compiling: " + rv)
        rv = TextileParser.toHtml(rv, disableLinks = false).toString()
        if (showMedia) {
            rv = IMG_NODE_RE.replaceAllIn(rv, {(re) =>
                try {

                    """<div class="hidden-pic" style="display: none;">"""+re+"""</div>"""

                } catch {
                    case e:Exception =>
                        e.getStackTraceString.replaceAll("\n", "\n - ")
                        re.group(0)
                }

            })
        }

//        println("text compilation done.")

        rv = rv.replaceAll("(<br/?><br/?>)+", "<br />")

        // hapus br antar saved code yang dibuat oleh TextileParser.
        rv = rv.replaceAll("(<br></br>)+", "<br />")

        // make link to target = "_blank"
//        rv = rv.replaceAll("""<a href="(.*)">(.*)</a>""", """<a href="$1" target="_blank">$2</a>""")

        // compile spoiler
        rv = SPOILER_RE.replaceAllIn(rv, { re =>
            val id = genSavedCode()
            var innerContent = re.group(1)
            var toRemoveIds = ListBuffer.empty[String]
            for( (k,v) <- preCode ){
                if (innerContent.contains(k)){
                    innerContent = innerContent.replace(k, v)
                    toRemoveIds += k
                }
            }
            cleanUpIds(toRemoveIds.toList)
            innerContent = compileEmoticon(innerContent)
            innerContent = compileNameTag(innerContent)
            innerContent = compileLink(innerContent)
            innerContent = processImages(innerContent)

            if (editorMode)
                preCode += id -> """<div class="spoiler-outer"><p>%s</p></div><p class=""><br></p>""".format(innerContent)
            else
                preCode += id -> """<div class="spoiler">%s</div>""".format(innerContent)
            id
        })

        // remove header specific markup
        rv = HTML_HEADER_RE.replaceAllIn(rv, { re =>
            Matcher.quoteReplacement("<h3>%s</h3>".format(re.group(1)))
//            if (re.groupCount > 1){
//                if (re.group(1).trim.length > 0)
//                    Matcher.quoteReplacement("<h3>%s</h3>".format(re.group(1)))
//                else
//                    ""
//            }else if (re.groupCount > 0){
//                if (re.group(0).trim.length > 0)
//                    Matcher.quoteReplacement("<h3>%s</h3>".format(re.group(0)))
//                else
//                    ""
//            }else{
//                "" // ignore
//            }
        })

        // compile emoticon
        rv = compileEmoticon(rv)
        rv = compileNameTag(rv)


        // pop back saved code
        for( (k,v) <- preCode ){
            rv = rv.replace(k, v)
        }

        // compile link
        rv = compileLink(rv)

        // compile alt image dari image name nya
        rv = processImages(rv)

        rv.trim
    }

    def genSavedCode() = SAVED_CODE_TAG.format(nextId())


    // see: http://apps.timwhitlock.info/emoji/tables/unicode
    private val emojiUnicode = for (i <- 1 to 78) yield 0x1F600 + i

    def compileEmoticon(str:String):String={
        var rv = processImages(Digaku.engine.contentProcessor.emoticonProcessor.compile(str, save = false))
        for (eu <- emojiUnicode){
            // @TODO(robin): ganti endpoint emoji ini, sementara pake host orang dulu :D
            rv = rv.replace(Character.toChars(eu),"""<img src="http://apps.timwhitlock.info/static/images/emoji/emoji-apple/%05x.png" style="width:30px; height:30px;" /> """.format(eu))
        }
        rv
    }

    def compileEmoji(str:String):String = {
        var rv = str
        for (eu <- emojiUnicode){
            rv = rv.replace(Character.toChars(eu), """<img class="emoji" src="/assets/img/emoticons/twemoji/36x36/%05x.png" alt="%s" />""".format(eu, Character.toChars(eu).mkString("")))
        }
        rv
    }

    def compileNameTag(str:String):String = {
        Digaku.engine.contentProcessor.textTagProcessor.compile(str)
    }

    // hanya digunakan untuk meng-compile mentions user/channel dan emoticon pada content
    def compileNameTagEmo(str:String):String = {
        var rv = Digaku.engine.contentProcessor.textTagProcessor.compile(str)
        rv = processImages(Digaku.engine.contentProcessor.emoticonProcessor.compile(rv, save = false))
        rv
    }

    def compileMediaEditor(str:String):String = {
        OEMBED_AT_EDITOR.replaceAllIn(str, {
            (re) =>
                val url =
                    if (re.group(3) != null)
                        re.group(3)
                    else
                        re.group(2)

                if (url.length > 8)
                    """<img class="embedded-object icon icon-play-video" data-url="%s" />""".format(url)
                else
                    ""
        })
    }

    def compileUserName(str:String):String = {
        Digaku.engine.contentProcessor.textNameCompiler.compileUserName(str)
    }

    def compileChannelName(str:String):String = {
        Digaku.engine.contentProcessor.textNameCompiler.compileChannelName(str)
    }

    def compileLink(str:String) = {
        Digaku.engine.contentProcessor.linkTransformer.compile(str)
    }

    /**
     * Hapus semua html tags, sehingga element seperti <span>/<div> akan hilang semua
     * tersisa hanya plain text saja.
     * @param str string yang akan diproses.
     * @return string yang sudah bersih tanpa html tags.
     */
    def cleanHtmlTags(str:String) = {
        Digaku.engine.contentProcessor.htmlTagCleaner.compile(str)
    }

    def compileEmbeddable(str:String):String = {
        EMBEDDABLE_RE.replaceAllIn(str, {
            (re) =>
                val url = re.group(0).trim
                val link = new URI(url)
                val fancyUrl = getFancyUrl(link)
                """<a href="%s" class="embedded-object">%s</a>""".format(url, fancyUrl)
        })
    }


    private def getFancyUrl(link:URI) = {
        val path = link.getPath

        val normHost = if (link.getPort != 80 && link.getPort != -1)
            link.getHost + ":" + link.getPort
        else
            link.getHost

        if (path.length > 0) {
            val truncatedPath = path.truncateRight(20)
            if (truncatedPath.startsWith("/"))
                "%s://%s%s".format(link.getScheme, normHost, truncatedPath)
            else
                "%s://%s/%s".format(link.getScheme, normHost, truncatedPath)
        } else
            "%s://%s".format(link.getScheme, normHost)
    }

    /**
     * Remove embeddable links if any.
     * @param str embeddable link to remove.
     * @return
     */
    def removeEmbeddableLinks(str:String):String = {
        EMBEDDABLE_RE.replaceAllIn(str, "")
    }

    /**
     * Hapus format textile jika ada.
     *
     * meliputi :
     * - link format ("link":http://google.com)
     * - bold format (*text*)
     * - italic format (_text_)
     * - underlined format (+text+)
     *
     * @param str content yang mau dibersihkan format textilenya
     * @return
     */
    def removeTextileFormat(str:String):String = {
        var rv = TEXTILE_LINK.replaceAllIn(str, "$2")
        rv = TEXTILE_BOLD.replaceAllIn(rv, "$2")
        rv = TEXTILE_ITALIC.replaceAllIn(rv, "$2")
        TEXTILE_UNDERLINE.replaceAllIn(rv, "$2")
    }

    private val YOUTUBE_SUFFIX = "- YouTube"
    private val GITHUB_SUFFIX = "· GitHub"

    /**
     * Untuk menghilangkan provider domain caption,
     * contoh ada title "Video keren nih - Youtube"
     * Maka setelah di-remove akan menjadi: "Video keren nih"
     * @param text text yang akan di-remove info provider-nya.
     */
    def removeProviderCaption(text:String) = {

        // remove youtube provider
//        if (text.trim.toLowerCase.endsWith(YOUTUBE_SUFFIX)){
        text.trim
            .stripSuffix(YOUTUBE_SUFFIX) // remove youtube provider
            .stripSuffix(GITHUB_SUFFIX) // remove github provider
            .trim
//        }else{
//            text
//        }
    }


    /**
     * digunakan untuk menambahkan alt image dari filename nya
     * ketika sebelumnya alt img masih kosong
     * @param str
     * @return
     */
    private def processImages(str:String):String = {
        IMG_NODE_RE.replaceAllIn(str, {(re) =>
            try {

                val link = re.group(0)
                val elm = XML.loadString(link)

                val src = elm.attribute("src").map(_.toString()).getOrElse("")
                val origAlt = elm.attribute("alt").map(_.toString()).getOrElse("")

                if (origAlt == ""){
                    val alt = src.split("/").lastOption
                        .getOrElse("")
                        .replaceAll("""%2\w""", " ")
                        .replaceAll("""[\W|\_]+""", " ")

                    (elm % new UnprefixedAttribute("alt", alt, Null)).toString()
                }else{
                    link
                }

            } catch {
                case e:Exception =>
                    e.getStackTraceString.replaceAll("\n", "\n - ")
                    re.group(0)
            }

        })
    }

    private val IMG_OEMBED_NODE_WITH_DATAURL_RE = """<img\s+class=\".*\"\s+[^>]*src="([^"]*)"\s+[^>]*data\-url="([^"]*)"[^>]*/?.*?>""".r
    def normHtml(htmlText:String) = {
        var rv = htmlText.replaceAll("""&nbsp;""", " ")

        rv = IMG_OEMBED_NODE_WITH_DATAURL_RE.replaceAllIn(rv, { re =>
            if (re.groupCount > 1) {
                val thumbUrl = re.group(1)
                val _url = re.group(2)
                if (UrlValidator.isValid(_url)) {
                    s"""<a href="${_url}" class="embedded-object" data-thumb-url="$thumbUrl">${_url}</a>"""
                } else {
                    "#"
                }
            } else {
                "#"
            }
        })

        rv = processEmoji(rv)

        rv
    }

    /**
     * Stand alone function yang digunakan untuk compile spoiler secara individual.
     * @param text text yang akan di-compile.
     * @param editorMode apakah pada mode read atau edit.
     * @return
     */
    def compileSpoiler(text:String, editorMode:Boolean=false) = {
        var preCode = Map[String, String]()
        var rawCode = Map[String, String]()

        var rv = text

        rv = SPOILER_RE.replaceAllIn(rv, { re =>
            val id = genSavedCode()
            var innerContent = re.group(1)
            var toRemoveIds = ListBuffer.empty[String]
            for( (k,v) <- preCode ){
                if (innerContent.contains(k)){
                    innerContent = innerContent.replace(k, v)
                    toRemoveIds += k
                }
            }
            toRemoveIds.toList.foreach { k =>
                preCode -= k
                rawCode -= k
            }
            innerContent = compileEmoticon(innerContent)
            innerContent = compileNameTag(innerContent)
            innerContent = compileLink(innerContent)
            innerContent = processImages(innerContent)

            if (editorMode)
                preCode += id -> """<div class="spoiler-outer"><p>%s</p></div><p class=""><br></p>""".format(innerContent)
            else
                preCode += id -> """<div class="spoiler">%s</div>""".format(innerContent)
            id
        })

        // pop back saved code
        for( (k,v) <- preCode ){
            rv = rv.replace(k, v)
        }

        rv
    }

    private val VALID_URL_RE = VALID_URL.r
    private val IMG_NODE_EMO = """<img\s+class=\"emoji\"\s+[^>]*src=['"].*?\/([^.\/<>]*)\.png[^.\/<>]*['"][^><]*?>""".r
    private val BLOCKQUOTE_NODE_RE = """<div>[\s\S]+?(?:<blockquote data\-id="\d+">)([\s\S]+?)(?:</blockquote>)[\s\S]+?</div>""".r
    private val USER_MENTIONED_RE = """<span\s+class=\"mentioned\"\s+[^>]*data\-user\-id="(\d+)"[^>]*/?.*?>@(?:[\w\.^\s\-]*?)</span>""".r

    /**
     * Remove all url exist in text.
     * @param text text where may contains url.
     * @return
     */
    def stripUrl(text:String) = {
        VALID_URL_RE.replaceAllIn(text, "")
    }

    /**
     * Digunakan untuk memproses img node emoji menjadi
     * emoji unicode
     * @param htmlText html text yang akan diproses
     * @param setUnicode set true jika ingin emoticon di unescapeHtml4
     *                   atau menjadikan hex emoticon ke unicode
     * @return
     */
    private def processEmoji(htmlText:String, setUnicode:Boolean = true) = {
        IMG_NODE_EMO.replaceAllIn(htmlText, { re =>
            if (re.groupCount > 0) {
                val emoji = re.group(1).split("-")
                val text = if (emoji.length > 1) {
                    "&#x" + emoji.apply(0) + ";&#x" + emoji.apply(1) + ";"
                } else {
                    "&#x" + re.group(1) + ";"
                }

                if (setUnicode) {
                    val ff = unescapeHtml4(text)
                    ff
                } else {
                    text
                }
            } else {
                re.group(0)
            }
        })
    }

    def cleanBlockquoteTag(htmlText:String) = {
        BLOCKQUOTE_NODE_RE.replaceAllIn(htmlText, "")
    }

    /**
     * Digunakan untuk replace media seperti img dan iframe
     * ke text image atau media
     * @param htmlText
     * @return
     */
    def replaceMediaToText(htmlText:String) = {
        val newStr = IMG_NODE_RE.replaceAllIn(htmlText, """<span class="label label-success">image</span>""")
        IFRAME_NODE_RE.replaceAllIn(newStr, """<span class="label label-success">media</span>""")
    }

    /**
     * Untuk memproses element html yang mengandung data-user-id
     * menjadi link menuju user
     * @param htmlText --
     * @return
     */
    def processUserFullName(htmlText:String) = {
        USER_MENTIONED_RE.replaceAllIn(htmlText, { rv =>
            User.getById(rv.group(1).toLong).map { user =>
                """<a data-hc-kind="user" class="mentioned" href="/u/%s">@%s</a>""".format(user.getId.toString, user.getName)
            }.getOrElse {
                rv.group(0)
            }
        })
    }
}
