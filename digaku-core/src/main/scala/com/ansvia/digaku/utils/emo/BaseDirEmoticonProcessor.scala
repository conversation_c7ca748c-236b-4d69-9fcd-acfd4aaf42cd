/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils.emo

import java.util.regex.{<PERSON><PERSON>, Pattern}
import java.io.{FilenameFilter, File}
import com.ansvia.digaku.exc.{NotExistsException, DigakuException}
import util.matching.Regex
import org.apache.commons.io.FilenameUtils
import com.ansvia.commons.logging.Slf4jLogger
import java.util
import scala.collection.mutable
import java.util.UUID

/**
 * Author: robin
 *
 */
abstract class BaseDirEmoticonProcessor(name:String, dirName:String)
    extends EmoticonProcessor(name) with EmoMapper with Slf4jLogger {

    val prefixSign = ":"
    val baseEmoDir = "assets/img/emoticons"
    val extensions = List("gif", "png")

    // taruh disini untuk hidden emoticon,
    // yakni apabila ada emotion yang digunakan tapi tidak perlu ditampilkan/
    // diexpose, tidak akan muncul di [[list]]
    protected val hiddenEmos = List[String]()

    private lazy val currentDir = {
        new File(new File(new File(new File(getClass.getProtectionDomain
            .getCodeSource.getLocation.getPath).getParent).getParent).getParent)
    }

    protected lazy val webappDir:String = {
        var webapp = currentDir.getAbsolutePath + "/webapp"

        if(!new File(webapp).isDirectory){
            webapp = currentDir.getAbsolutePath + "/digaku-web/src/main/webapp"
            if (!new File(webapp).exists())
                webapp = currentDir.getAbsolutePath + "/war"
                if (!new File(webapp).exists())
                    webapp = currentDir.getAbsolutePath + "/root"
                        if (!new File(webapp).exists())
                            error("Cannot getting root dir looking for webapp dir. Emoticon may not work correctly.")
//                            throw new DigakuException("Cannot find webapp dir, curdir: " + currentDir.getAbsolutePath)
        }
        webapp
    }

    // scan directories
    info("loaded emo dir: " + webappDir + "/" + baseEmoDir + "/" + dirName)
    private val emoDir = new File(webappDir + "/" + baseEmoDir + "/" + dirName)

    if(!emoDir.exists)
//        throw new DigakuException("emo dir not found: " + emoDir.getAbsolutePath)
        error("emo dir not exists: " + emoDir + ", emoticon may not work correctly.")

    protected lazy val RE = """(?<!")%s([a-zA-Z0-9\-\_\(\)]+)""".format(Pattern.quote(prefixSign)).r


    private lazy val compiledUrlCache = new mutable.HashMap[String, String]() with mutable.SynchronizedMap[String, String]

    def compile(text: String, save:Boolean) = {
        RE.replaceAllIn(text, replacer(save) _)
    }


    def getUrl(text: String): String =
        compiledUrlCache.getOrElseUpdate(text, RE.replaceAllIn(text, replacerUrl _))


    private def replacer(save:Boolean)(m:Regex.Match):String = {
        if(m.groupCount > 0){
            // get file by name
            try {
                val emoFile = getEmoFile(mapEmo(prefixSign + m.group(1)))
                val emoUrl = getEmoUrl(emoFile)

                val raw = prefixSign +  m.group(1)

                val compiledHtml = Matcher.quoteReplacement("""<img src="%s" title="%s" alt="%s" class="emo" />""".format(emoUrl, raw, raw))
                
                if (!save)
                    compiledHtml
                else{
                    val id = genSavedCode
                    saveData(id, compiledHtml)
                    id
                }
            }catch{
                case e:NotExistsException =>
                    prefixSign + m.group(1)
            }
        }else
//            """<img src="%s" title="%s" />""".format("/" + baseEmoDir + "/" + dirName + "/404.gif", prefixSign +  m.group(1))
            Matcher.quoteReplacement(m.group(0))

    }

    private def replacerUrl(m:Regex.Match):String = {
        if(m.groupCount > 0){
            // get file by name
            try {
                val emoFile = getEmoFile(mapEmo(prefixSign + m.group(1)))
                val emoUrl = getEmoUrl(emoFile)

                Matcher.quoteReplacement(emoUrl)
            }catch{
                case e:NotExistsException =>
                    prefixSign + m.group(1)
            }
        }else
//            """<img src="%s" title="%s" />""".format("/" + baseEmoDir + "/" + dirName + "/404.gif", prefixSign +  m.group(1))
            Matcher.quoteReplacement(m.group(0))

    }

    private lazy val emoFileCache = new mutable.HashMap[String, File]() with mutable.SynchronizedMap[String, File]

    protected def getEmoFile(emoName:String):File = {

        def getInternal():File = {
            for( ext <- extensions ){
                val f = new File(emoDir.getAbsolutePath + "/" + norm(emoName) + "." + ext)
                if(f.exists())
                    return f
            }

            throw NotExistsException("emo not found")
        }


        emoFileCache.getOrElseUpdate(emoName, {
            getInternal()
        })


    }

    protected def getEmoUrl(file:File):String = {
        "/" + baseEmoDir + "/" + dirName + "/" + file.getName
    }

    /**
     * Normalize emoticon, when has prefix then striped out.
     * @param emoWithPrefix emoticon code to normalize.
     * @return
     */
    def norm(emoWithPrefix:String) = {
        if (emoWithPrefix.startsWith(prefixSign))
            emoWithPrefix.substring(emoWithPrefix.indexOf(prefixSign) + 1)
        else
            emoWithPrefix
    }

    private lazy val hasCache = new mutable.HashMap[String, Boolean]() with mutable.SynchronizedMap[String, Boolean]

    /**
     * Check is has emo
     * @param emoName
     * @return
     */
    def has(emoName:String):Boolean = {
        import scala.util.control.Breaks._
        hasCache.getOrElseUpdate(emoName, {
            var rv = false
            breakable {
                for( ext <- extensions ){
                    val f = new File(emoDir.getAbsolutePath + "/" + norm(emoName) + "." + ext)
                    if(f.exists()){
                        rv = true
                        break
                    }
                }
            }
            rv
        })
    }


    /**
     * get registered emos
     * @return list of available emoticons.
     */
    lazy val list = {
        val files = emoDir.listFiles(new FilenameFilter {
            def accept(p1: File, p2: String) = {
                extensions.contains(FilenameUtils.getExtension(p2))
            }
        }).filterNot { f =>
            hiddenEmos.contains(nameWithoutExt(f.getName))
        }

        files.map(f => (prefixSign + nameWithoutExt(f.getName))).toSeq
    }

    protected def nameWithoutExt(name:String) = {
        FilenameUtils.removeExtension(name)
    }
}

trait EachSavedDataGetter {
    def getEachSavedData(func: (String, String) => Unit):Unit
}

abstract class BaseDirEmoticonGroupProcessor(name:String, dirName:String)
    extends EmoticonGroupProcessor(name) with Slf4jLogger {


    val prefixSign = ":"
    val baseEmoDir = "assets/img/emoticons"
    val extensions = List("gif", "png")

    /**
     * taruh disini untuk hidden emoticon,
     * yakni apabila ada emotion yang digunakan tapi tidak perlu ditampilkan/
     * diexpose, tidak akan muncul di [[list]]
     */
    protected val hiddenEmos = List[String]()

    private lazy val currentDir = {
        new File(new File(new File(new File(getClass.getProtectionDomain
            .getCodeSource.getLocation.getPath).getParent).getParent).getParent)
    }

    protected lazy val webappDir:String = {
        var webapp = currentDir.getAbsolutePath + "/webapp"

        if(!new File(webapp).isDirectory){
            webapp = currentDir.getAbsolutePath + "/digaku-web/src/main/webapp"
            if (!new File(webapp).exists())
                webapp = currentDir.getAbsolutePath + "/war"
            if (!new File(webapp).exists())
                webapp = currentDir.getAbsolutePath + "/root"
            if (!new File(webapp).exists())
                error("Cannot getting root dir looking for webapp dir. Emoticon may not work correctly.")
            //                            throw new DigakuException("Cannot find webapp dir, curdir: " + currentDir.getAbsolutePath)
        }
        webapp
    }

    // scan directories
//    println(webappDir + "/" + baseEmoDir + "/" + dirName)
    protected val emoDir = new File(webappDir + "/" + baseEmoDir + "/" + dirName)

    protected val me = this



    protected def getEmos:Seq[BaseDirEmoticonProcessor with EachSavedDataGetter] = {
        emoDir.listFiles().filter(_.isDirectory)
            .map { dir =>
            val _name = FilenameUtils.getBaseName(dir.getName)
            val subDirName = FilenameUtils.getBaseName(dir.getName)
            val _hiddenEmos = hiddenEmos
            new BaseDirEmoticonProcessor(_name, dirName + "/" + subDirName) with EachSavedDataGetter {

                override protected val hiddenEmos: List[String] = _hiddenEmos

                def mapEmo(emoCode: String): String = {
                    me match {
                        case em:EmoMapper => em.mapEmo(emoCode)
                        case _ => emoCode
                    }
                }

                def getEachSavedData(func: (String, String) => Unit): Unit = eachSavedData(func)
            }
        }
    }

    lazy val emos = getEmos.asInstanceOf[Seq[BaseDirEmoticonProcessor]]


    /**
     * Compile text to spawn emoticon.
     * @param text text to compile.
     * @return
     */
    def compile(text: String, save:Boolean): String = synchronized {
        var rv = text

        emos.foreach { emo =>
            if (save)
                rv = emo.compile(rv, save)
            else {
                rv = emo.compile(rv, save = true)
            }
        }

        if (!save){

            emos.foreach { emo =>
                emo.asInstanceOf[EachSavedDataGetter].getEachSavedData { (id, cdata) =>
                    rv = rv.replace(id, cdata)
                }
            }

        }

        rv
    }


    def getUrl(text: String): String = {
//        emos.find(_.has(text)).map { ep =>
//            ep.compileToUrl(text)
//        }.getOrElse(text)
        var rv = text

        emos.foreach(emo => rv = emo.getUrl(rv))

        rv
    }

    /**
     * Show registered emo
     * @return compilable available emoticon name.
     */
    def list: Seq[String] = emos.flatMap(_.list)

    def groupList = emos.map( ep => (ep.name -> ep.list)).toMap
}


