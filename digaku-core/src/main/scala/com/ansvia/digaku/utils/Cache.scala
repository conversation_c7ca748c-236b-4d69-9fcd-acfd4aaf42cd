/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import com.google.common.cache.{Loading<PERSON>ache, CacheLoader, CacheBuilder}
import java.util.concurrent.{Callable, TimeUnit}
import scala.collection.mutable
import com.google.common.util.concurrent.ListenableFutureTask
import com.ansvia.commons.logging.Slf4jLogger
import scala.concurrent.duration.FiniteDuration

/**
 * Author: robin
 *
 */
// @TODO(robin): deprecate this
object Cache extends Slf4jLogger {

    private val registry = mutable.HashMap[String,
        com.google.common.cache.Cache[java.lang.Object, java.lang.Object]]()

    def steady[KeyT <: java.lang.Object, ValT <: java.lang.Object](name:String, maxSize:Int,
                                                                   expTime:Long, expTimeUnit:TimeUnit)
                          (loaderFunc: (KeyT) => ValT):LoadingCache[KeyT, ValT] = {

        def build() = {
            debug("build brand new steady cache: " + name)
            CacheBuilder.newBuilder()
                .maximumSize(maxSize)
                .refreshAfterWrite(expTime, expTimeUnit)
                .build[KeyT, ValT](new CacheLoader[KeyT, ValT] {

                def load(key: KeyT) = {
                    loaderFunc.apply(key)
                }

                override def reload(key: KeyT, oldValue: ValT) = {
                    debug("[%s] reloading cache `%s`...".format(name, key))
                    val task = ListenableFutureTask.create(new Callable[ValT] {
                        def call() = {
                            val rv = load(key)
                            debug("[%s] reloading cache done: `%s`".format(name, key))
                            rv
                        }
                    })
                    NonBlockingExecutor.threadPollExecutor.execute(task)
                    task
                }
            })
        }

        registry.getOrElseUpdate(name,
            build().asInstanceOf[com.google.common.cache.Cache[java.lang.Object, java.lang.Object]])
                .asInstanceOf[LoadingCache[KeyT, ValT]]

    }


    def steady[KeyT <: java.lang.Object, ValT <: java.lang.Object](name:String, maxSize:Int, duration:FiniteDuration)
                          (loaderFunc: (KeyT) => ValT):LoadingCache[KeyT, ValT] = {

        steady[KeyT, ValT](name, maxSize, duration.toMillis, TimeUnit.MILLISECONDS)(loaderFunc)

    }


//    private val registry2 = mutable.HashMap[String,
//        com.google.common.cache.Cache[java.lang.Object, java.lang.Object]]()
//
//    def steady2[KeyT <: java.lang.Object, ValT <: java.lang.Object](name:String, maxSize:Int, duration:FiniteDuration)
//                          (loaderFunc: (KeyT) => ValT):LoadingCache[KeyT, ValT] = {
//
//        def build() = {
//            debug("build brand new steady cache: " + name)
//            CacheBuilder.newBuilder()
//                .maximumSize(maxSize)
//                .refreshAfterWrite(duration.toMillis, TimeUnit.MILLISECONDS)
//                .build[KeyT, ValT](new CacheLoader[KeyT, ValT] {
//
//                def load(key: KeyT) = {
//                    loaderFunc.apply(key)
//                }
//
//                override def reload(key: KeyT, oldValue: ValT) = {
//                    debug("[%s] reloading cache `%s`...".format(name, key))
//                    val task = ListenableFutureTask.create(new Callable[ValT] {
//                        def call() = {
//                            val rv = load(key)
//                            debug("[%s] reloading cache done: `%s`".format(name, key))
//                            rv
//                        }
//                    })
//                    NonBlockingExecutor.threadPollExecutor.execute(task)
//                    task
//                }
//            })
//        }
//
//        registry2.getOrElseUpdate(name,
//            build().asInstanceOf[com.google.common.cache.Cache[java.lang.Object, java.lang.Object]])
//                .asInstanceOf[LoadingCache[KeyT, ValT]]
//
//    }


//
//    def memoize[ValT <: java.lang.Object](name:String, maxSize:Int, expTime:Int, expTimeUnit:TimeUnit)
//                          (loaderFunc: => ValT):LoadingCache[Byte, ValT] = {
//
//        def build() = {
//            debug("build brand new steady cache: " + name)
//            CacheBuilder.newBuilder()
//                .maximumSize(maxSize)
//                .refreshAfterWrite(expTime, expTimeUnit)
//                .build[Byte, ValT](new CacheLoader[Byte, ValT] {
//
//                def load(key:Byte) = {
//                    loaderFunc
//                }
//
//                override def reload(key: Byte, oldValue: ValT) = {
//                    debug("[%s] reloading cache `%s`...".format(name, key))
//                    val task = ListenableFutureTask.create(new Callable[ValT] {
//                        def call() = {
//                            val rv = load(key)
//                            debug("[%s] reloading cache done: `%s`".format(name, key))
//                            rv
//                        }
//                    })
//                    NonBlockingExecutor.threadPollExecutor.execute(task)
//                    task
//                }
//            })
//        }
//
//        registry.getOrElseUpdate(name,
//            build().asInstanceOf[com.google.common.cache.Cache[java.lang.Object, java.lang.Object]])
//                .asInstanceOf[LoadingCache[Byte, ValT]]
//
//    }


}
