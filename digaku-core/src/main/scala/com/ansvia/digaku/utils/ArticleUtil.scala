/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

/**
 * Author: robin
 *
 */
object ArticleUtil {

    import RichString._
    import com.ansvia.digaku.utils.TextCompiler.removeTextileFormat

//    private lazy val scriptTagsRe = """<.*?script.*?>.*?</script>""".r
    private lazy val textPreviewRe = """\[img\].+?\[/img\]|\[\:[a-z0-9]+\]|#[a-z0-9]+|!https?\://.+?!|https?\://.[^\s]+\s|[#\*\+]|oembed!|!oembed""".r
    private lazy val xmlTags = """</?.*?>""".r

    def shortDesc(text:String, max:Int=120):String = {
//        var rv = scriptTagsRe.replaceAllIn(text, "")
        textPreview(text).truncate(max).trim
//        rv
    }
    
    def textPreview(text:String) = {
        var rv = text
        rv = removeTextileFormat(rv)
        rv = xmlTags.replaceAllIn(rv, " ")
        rv = textPreviewRe.replaceAllIn(rv, "")
        rv = rv.replaceAll("  +"," ")
        rv
    }
    
}
