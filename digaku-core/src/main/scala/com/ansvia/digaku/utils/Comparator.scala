/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import com.tinkerpop.blueprints.Vertex

/**
 * Author: robin
 *
 */


object Comparator {

    import com.ansvia.graph.BlueprintsWrapper._

    def ByCreationTime(sort:SortDirection)(va:Vertex, vb:Vertex) = {
        if (sort == SortDirection.DESC)
            -va.getOrElse("creationTime", 0L).compareTo(vb.getOrElse("creationTime", 0L))
        else
            va.getOrElse("creationTime", 0L).compareTo(vb.getOrElse("creationTime", 0L))
    }

    def TextField(fieldName:String, sort:SortDirection)(va:Vertex, vb:Vertex) = {
        if (sort == SortDirection.DESC)
            -va.getOrElse(fieldName, "").compareTo(vb.getOrElse("fieldName", ""))
        else
            va.getOrElse(fieldName, "").compareTo(vb.getOrElse(fieldName, ""))
    }

    def ByName(sort:SortDirection)(va:Vertex, vb:Vertex) = TextField("name", sort)(va, vb)

    val timeOrdererAsc = com.ansvia.graph.gremlin.gremlinPipeOrderFuncWrapper((a:Vertex, b:Vertex) =>
        a.getOrElse("creationTime", 0L).compareTo(b.getOrElse("creationTime", 0L)))
    val timeOrdererDesc = com.ansvia.graph.gremlin.gremlinPipeOrderFuncWrapper((a:Vertex, b:Vertex) =>
        -a.getOrElse("creationTime", 0L).compareTo(b.getOrElse("creationTime", 0L)))
}

trait SortDirection

object SortDirection {
    object ASC extends SortDirection
    object DESC extends SortDirection
}


