/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import java.util.concurrent.{LinkedBlockingQueue, TimeUnit, ThreadPoolExecutor}

/**
 * Author: robin
 *
 */
object NonBlockingExecutor {

    val threadPollExecutor = new ThreadPoolExecutor(2, 5, 10, TimeUnit.MINUTES, new LinkedBlockingQueue[Runnable]())

}


