/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.utils

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.model.Topic
import com.ansvia.digaku.persistence.{SnowFlakeIdFactory, CassandraBackedSeqStore}
import com.ansvia.digaku.{Global, Digaku, model}
import com.netflix.astyanax.model.{ConsistencyLevel, ColumnFamily}
import com.netflix.astyanax.serializers.{ByteSerializer, LongSerializer, StringSerializer}
import com.netflix.astyanax.util.RangeBuilder
import scala.collection.mutable.ListBuffer

/**
 * Author: robin
 *
 * Untuk penyimpanan user settings yang sifatnya dynamic,
 * disimpan menggunakan Cassandra.
 */
sealed trait UserSettings {

    import UserSettings.abilitiesText

    implicit class UserSettingsWrapper(user:model.User) {
//        object settings extends CassandraBackedKVStore("Standard1", "settings-" + user.getId,
//            Digaku.config.mainDatabase.keyspaceName, Digaku.config.mainDatabase.clusterName, Digaku.config.mainDatabase.hostName,
//            Digaku.config.mainDatabase.replStrategy, Digaku.config.mainDatabase.replStrategyOpts
//        )

        lazy private val loginHistorySeqStoreId = s"login-history-${user.getId}"
        lazy val settings = Digaku.engine.kvStoreProvider.build("settings-" + user.getId)

        /**
         * build joined and moderated forum SeqStore
         */
        lazy val joinedForums = UserForumIds.seqStore.build("joined-" + user.getId, () => UserForumIds.generateId())
        lazy val moderatedForums = UserForumIds.seqStore.build("moderated-" + user.getId, () => UserForumIds.generateId())

        // SeqStore untuk mencatat login history
        private val loginHistorySeqStore = Digaku.engine.seqStoreProvider.build(loginHistorySeqStoreId, ()=> Digaku.engine.idFactory.createId().asInstanceOf[Long])

        // SeqStore untuk mencatat user menjadi agent di topic
        lazy val topicAgentSeqStore = Digaku.engine.seqStoreProvider.build(s"user-agent-${user.getId}", ()=> Digaku.engine.idFactory.createId().asInstanceOf[Long])


        /**
         * get all joined forum id
         * @return
         */
        def getJoinedForumIds:Iterator[(Long, String)] = {
            UserForumIds.seqStore.csh.usingQuery { q =>
                val columns = q.getKey("joined-" + user.getId).execute().getResult

                var rv = new ListBuffer[(Long, String)]
                for ( i <- 0 to columns.size()-1 ){
                    val col = columns.getColumnByIndex(i)
                    rv += ((col.getName, col.getStringValue))
                }
                rv.toIterator
            }
        }

        /**
         * get all moderated forum id
         * @return
         */
        def getModeratedForumIds:Iterator[(Long, String)] = {
            UserForumIds.seqStore.csh.usingQuery { q =>
                val columns = q.getKey("moderated-" + user.getId).execute().getResult

                var rv = new ListBuffer[(Long, String)]
                for ( i <- 0 to columns.size()-1 ){
                    val col = columns.getColumnByIndex(i)
                    rv += ((col.getName, col.getStringValue))
                }
                rv.toIterator
            }
        }

        /**
         * Menambahkan login history untuk user.
         * @param dateMillis date time millis saat user login
         */
        def addLoginHistory(dateMillis:Long): Unit = {
            loginHistorySeqStore.insert(dateMillis, user.getId.toString)
        }

        /**
         * Get login histories dari user.
         * @param fromMillis Filtering start date millis
         * @param toMillis Filtering end date millis
         * @param limit Jumlah maximum list yang diambil
         * @return
         */
        def getLoginHistories(fromMillis:Option[Long], toMillis:Option[Long], limit:Int) = {
            loginHistorySeqStore.getStream(fromMillis.map(Long.box), toMillis.map(Long.box), limit)
        }

        /**
         * Mendapatkan jumlah login history user berdasarkan waktu tertentu.
         * @param fromMillis Filtering start date millis
         * @param toMillis Filtering end date millis
         * @return Total login user.
         */
        def getLoginHistoryCount(fromMillis:Long, toMillis:Long) = {
            Digaku.engine.commonCassandraCf.usingQuery { q =>
                val rowQuery = q.setConsistencyLevel(ConsistencyLevel.CL_QUORUM)
                    .getKey(loginHistorySeqStoreId)

                val range = new RangeBuilder()
                    .setStart(fromMillis)
                    .setEnd(toMillis)
                    .build()

                rowQuery.withColumnRange(range)
                    .getCount.execute()
                    .getResult
            }
        }

        def getAbilities = {
            settings.get(abilitiesText,"").split("\\,").map(_.trim).filter(_.length > 0)
        }

        def addAbilities(abilities:String*){
            val _old = getAbilities
            val _new = (_old ++ getAbilities).map(_.trim.toLowerCase).distinct
            settings.set(abilitiesText, _new.mkString(","))
        }

        def setAbilities(abilities:Array[String]){
            settings.set(abilitiesText, abilities.map(_.trim.toLowerCase).distinct)
        }


        def removeAbilities(abilities:String*){
            val _new = getAbilities.filterNot(abilities.map(_.trim.toLowerCase).contains)
            settings.set(abilitiesText, _new)
        }

        /**
         * Menambahkan topic faq pada user
         * @param topic
         */
        def addTopic(topic:Topic): Unit = {
            topicAgentSeqStore.insert(topic.getId, topic.getId.toString)
        }

        /**
         * Delete topic faq pada user
         * @param topic
         */
        def deleteTopic(topic: Topic): Unit ={
            topicAgentSeqStore.delete(topic.getId)
        }

        /**
         * Check apakah user sebagai agent pada sebuah faq topic ?
         * @return
         */
        def isAgentTopic:Boolean = {
            topicAgentSeqStore.getStream(None, None, 1).hasNext
        }

        def getTopicList:Iterator[Topic] = {
            topicAgentSeqStore.getStream(None, None, topicAgentSeqStore.getCount)
                .flatMap(rv => Topic.getById(rv._1))
        }

    }
}

object UserSettings extends UserSettings {
    val abilitiesText = "abilities"
}

object UserForumIds extends Slf4jLogger {

    private val idGen = new SnowFlakeIdFactory(Digaku.engine.config.machineId)

    def generateId(): Long = idGen.createId().asInstanceOf[Long]

    private lazy val CF = new ColumnFamily[String, java.lang.Long](
        Global.STANDARD_CF_NAME,
        StringSerializer.get(),
        LongSerializer.get(),
        ByteSerializer.get())

    val seqStore = new CassandraBackedSeqStore(CF,
        Digaku.config.mainDatabase.keyspaceName,
        Digaku.config.mainDatabase.clusterName,
        Digaku.config.mainDatabase.hostName,
        Digaku.config.mainDatabase.replStrategy,
        Digaku.config.mainDatabase.replStrategyOpts)
        .setComparatorType("LongType(reversed=true)")
}