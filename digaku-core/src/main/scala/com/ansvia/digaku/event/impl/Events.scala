/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.event.impl

import com.ansvia.digaku.event.StreamEvent
import com.ansvia.digaku.model
import com.ansvia.digaku.model._
import com.tinkerpop.blueprints.Edge
import com.ansvia.digaku.Types._
//import com.ansvia.digaku.model.ChannelInvitationCode
import com.ansvia.digaku.model.Article

// scalastyle:off number.of.types

/**
  * Author: robin
  *
  */
case class CreatePostEvent(user:User, post:Post) extends StreamEvent("create-post")
case class UpdatePostEvent(user:User, post:Post) extends StreamEvent("update-post")
case class LatestForumNewsEvent(post:Post) extends StreamEvent("latest-forum-news")
//case class CreatePictureGroupEvent(user:User, picGroup:PictureGroup) extends StreamEvent("create-picture-group")

/**
 * Digunakan untuk mereplace picture group dengan single picture
 * ie use case:
 * Apabila picture group hanya tinggal memiliki 1 picture tersisa,
 * sehingga posisi nya di stream akan diubah menjadi single picture yang tersisa tersebut
 * @param user
 * @param replacerPic
 */
case class ReplacePictureEvent(user:User, replacerPic:Picture) extends StreamEvent("delete-picture-group-replace-picture-event")
case class InvalidateCacheEvent(post:Streamable[IDType]) extends StreamEvent("invalidate-cache")

case class CreateUserEvent(user:User) extends StreamEvent("create-user")
case class UpdateUserEvent(user:User) extends StreamEvent("update-user")
case class InactiveUserEvent(user:User) extends StreamEvent("inactive-user")
case class LockedUserEvent(user:User) extends StreamEvent("locked-user")

// Di emit ketika update privated forum
case class UpdatePrivatedForumEvent(ch:Forum, state:Boolean) extends StreamEvent("update-privated-forum")

case class CreateEventEvent(user:User, event:Event) extends StreamEvent("create-event")
//case class CreateDealEvent(user:User, deal:Deal) extends StreamEvent("create-deal")
case class CreatePictureEvent(user:User, pic:Picture) extends StreamEvent("create-picture")
case class UpdatePictureEvent(user:User, pic:Picture) extends StreamEvent("update-picture")
case class CreateForumEvent(ch:Forum) extends StreamEvent("create-forum")
case class UpdateForumEvent(ch:Forum) extends StreamEvent("update-forum")
case class CreateAppEvent(app:App) extends StreamEvent("create-app")

//case class ChannelInviteMemberEvent(ch:Forum, invitor:User, invited:User, inv:ChannelInvitationCode) extends StreamEvent("forum-invite-member")
case class ChannelInviteMemberAcceptEvent(user: User, invitor: User, forum:Forum) extends StreamEvent("forum-invite-member-accept")
case class ChannelInviteMemberRejectEvent(user: User, invitor: User, forum:Forum) extends StreamEvent("forum-invite-member-reject")

//case class ChannelInviteStaffEvent(ch:Forum, invitor:User, invited:User, inv:ChannelInvitationCode) extends StreamEvent("forum-invite-staff")
case class ChannelInviteStaffAcceptEvent(user: User, invitor: User, forum:Forum) extends StreamEvent("forum-invite-staff-accept")
case class ChannelInviteStaffRejectEvent(user: User, invitor: User, forum:Forum) extends StreamEvent("forum-invite-staff-reject")

//case class ChannelInviteOwnerEvent(ch:Forum, invitor:User, invited:User, inv:ChannelInvitationCode) extends StreamEvent("forum-invite-owner")
case class ChannelInviteOwnerAcceptEvent(user: User, invitor: User, forum:Forum) extends StreamEvent("forum-invite-owner-accept")
case class ChannelInviteOwnerRejectEvent(user: User, invitor: User, forum:Forum) extends StreamEvent("forum-invite-owner-reject")

/**
 * Event ketika invitasi untuk supporting di reject
 * @see [[com.ansvia.digaku.notifications.impl.SupportApprovalInviteNotification.ignore]]
 * @param userB user yang mensupport.
 * @param userA user yang disupport.
 */
case class UserInviteSupportApprovalRejectEvent(userA: User, userB:User) extends StreamEvent("support-approval-reject")
case class UserInviteSupportApprovalAcceptEvent(userA: User, userB:User) extends StreamEvent("support-approval-accept")
case class UserInviteSupportApprovalEvent(invitor:User, invited:User, inv:UserInvitationCode) extends StreamEvent("support-approval")

case class DeletePostEvent(user:User, postId:Long) extends StreamEvent("delete-post")
case class DeletePictureEvent(user:User, pic:Picture) extends StreamEvent("delete-picture")
case class DeleteEventEvent(user:User, event:Event) extends StreamEvent("delete-event")
//case class DeletePictureGroupEvent(user:User, event:PictureGroup) extends StreamEvent("delete-picture-forum")
case class DeleteResponseEvent(user:User, resp:Response) extends StreamEvent("delete-response")

case class MovePostEvent(post:Post, forum:Forum, moveType:Int) extends StreamEvent("move-post")
case class MovePictureEvent(pic:Picture, forum:Forum, moveType:Int) extends StreamEvent("move-picture")
case class MoveEventEvent(event:Event, forum:Forum, moveType:Int) extends StreamEvent("move-event")

case class DeleteChannelEvent(forum:Forum) extends StreamEvent("delete-forum")
case class DeleteUserEvent(user:User) extends StreamEvent("delete-user")
case class DeleteAppEvent(app:App) extends StreamEvent("delete-app")

case class NotificationIgnoredEvent(ntfId:Long) extends StreamEvent("notification-ignore")
case class EditArticleEvent(editor:User, article:Article) extends StreamEvent("edit-article")
case class EditEventEvent(editor:User, edited:Array[String] , event:Event) extends StreamEvent("edit-event")
//case class CreatePrivateMessageEvent(user:User, privateMessage:PrivateMessage) extends StreamEvent("create-private-message")
//case class CreatePrivateMessageResponseEvent(user:User, messageResponse:MessageResponse, privateMessage:PrivateMessage) extends StreamEvent("pm-response")
//case class DeletePrivateMessageEvent(privateMessage:PrivateMessage) extends StreamEvent("delete-private-message")
case class UserCanEditPostEvent(creatorPost:User, userCanEdit:User, post:Post) extends StreamEvent("post-collaboration")
case class AttenderEvent(event:Event, attender:User, attenderKind:Int) extends StreamEvent("attender-event")
case class ShoutEvent(user:User, post:Post, message:String, shoutEdge:Edge) extends StreamEvent("shout")

///**
// * event ini akan di-dispatch apabila ada user yang meretalk suatu post (termasuk picture). Tipe data yang akan
// * di-retalk-an kan adalah retalked
// * @param user
// * @param retalked
// * @param forum
// */
//case class RetalkEvent(user:User, retalked:RetalkWrapper, forum:Forum) extends StreamEvent("retalk")
case class RestoreContentEvent(creator:User, post:BaseModel[IDType] with HasOrigin[ GraphType]) extends StreamEvent("restore-content")
case class RestoreChannelEvent(forum:Forum) extends StreamEvent("restore-forum")
//case class CreateAdEvent(ad:Advertisement) extends StreamEvent("create-ad")
//case class EditAdEvent(ad:Advertisement) extends StreamEvent("edit-ad")
//case class DeleteAdEvent(ad:Advertisement) extends StreamEvent("delete-ad")
//case class ClickAdEvent(viewer:User, ad:Advertisement) extends StreamEvent("click-ad")
case class CreateEndorseLabelEvent(endorsement:Endorsement) extends StreamEvent("create-endorse-label")
case class EndorseEvent(endorser:User, endorsement:Endorsement, target:Endorsable) extends StreamEvent("endorse")

/**
 * event ini akan di-dispatch apabila ada user yang join ke suatu forum.
 * @param user yang join.
 * @param forum yang di-joini.
 */
case class JoinChannelEvent(user:User, forum:Forum) extends StreamEvent("join-forum")
case class LeaveChannelEvent(user:User, forum:Forum) extends StreamEvent("leave-forum")
case class LikeEvent(liker:User, likableObjId:Likable) extends StreamEvent("like")
case class UnlikeEvent(liker:User, likableObjId:Likable) extends StreamEvent("unlike")

/**
* di-dispatch ketika user mendapatkan rank baru
* @param user
* @param rank
*/
case class NewRankEvent(user: User, rank: Rank) extends StreamEvent("new-rank")

/**
 * di-dispatch ketika user give reputation
 * @param user user yang memberi reputasi
 * @param userTarget user yang akan di beri reputasi
 * @param hasCreator bisa di-isi dengan [[Post]] atau [[Response]] object
 */
case class GiveReputationEvent(user: User, userTarget: User, hasCreator: HasCreator) extends StreamEvent("give-reputation")

/**
 * di dispatch ketika add moderator forum
 * @param user user target yang akan ditambahkan
 * @param forum forum target untuk moderatornya
 */
case class AddStaffEvent(user: User, forum: Forum) extends StreamEvent("add-staff")

/**
 * di dispatch ketika remove moderator forum
 * @param user user yang akan diremove
 * @param forum --
 */
case class RemoveStaffEvent(user: User, forum: Forum) extends StreamEvent("remove-staff")

/**
 * di dispatch ketika menambahkan promoted thread
 * @param post post yang akan di promosikan
 */
case class PromotedThreadEvent(post: Post) extends StreamEvent("promoted-thread")

/**
 * Event ini di-emit ketika response di-buat.
 * @param user user yang membuat respon.
 * @param response object respon-nya.
 * @param responded object yang di-respon.
 */
case class ResponseEvent(user:User, response:Response, responded:Responable) extends StreamEvent("response")
case class ResponseQuoteEvent(response:Response) extends StreamEvent("response-quote")

///**
// * Event ini di-dispatch ketika user mendapatkan extra point dari sebuah response.
// * @param user user yang mendapatkan point.
// * @param response response di mana user itu mendapatkan point.
// */
//case class UserGetExtraPointEvent(user:User, response:Response) extends StreamEvent("user-extra-point")

/**
 * Event yang di-dispatch ketika digaku engine jalan pertama kali.
 */
case class StartupEvent() extends StreamEvent("startup")

/**
 * di-dispatch ketika set blocked untuk saaat ini hanya digunakan untuk index search engine
 * @param blockable
 */
case class BlockedEvent(blockable:Blockable) extends StreamEvent("blocked")

/**
 * Di-dispatch ketika set closed, untuk saat ini hanya digunakan untuk index search engine
 * @param closable
 */
case class ClosedEvent(closable:Closable) extends StreamEvent("closed")

///**
// * di-dispatch ketika user di connectkan dengan facebook
// * see [[com.ansvia.digaku.model.Connectable3rdSite#connectFacebookInfo()]]
// * @param user
// */
//case class UserConnectFbEvent(user:User) extends StreamEvent("user-connect-fb")
//case class UserConnectTwEvent(user:User) extends StreamEvent("user-connect-tw")

case class UserHasTrophyEvent(user:User, trophy:Trophy) extends StreamEvent("user-has-trophy")

///**
// * Event ini di-dispatch ketika add friends FacebookInfo
// * see [[com.ansvia.digaku.model.FacebookInfo#addFriends(com.ansvia.digaku.model.FacebookInfo)]]
// * @param user
// */
//case class ConnectedFriendsFbEvent(user:User) extends StreamEvent("connect-friends-fb")
//case class ConnectedFriendsTwEvent(user:User) extends StreamEvent("connect-friends-tw")

/**
 * event ini di-dispatch ketika user mem-block user lain
 * @param user user yang memblock
 * @param userTarget user yang diblock
 */
case class BlockUserEvent(user:User, userTarget:User) extends StreamEvent("block-user")

/**
 * Event ini di-dispatch ketika user men-support user lainnya.
 * @see [[com.ansvia.digaku.model.User.supportInternal]]
 * @param userA user yang mensupport.
 * @param userB user yang disupport.
 */
case class FollowUserEvent(userA: User, userB:User)  extends StreamEvent("support-user")


//case class SponsorPostCreatedEvent(sp:SponsorPost) extends StreamEvent("sponsor-post-created")
//case class SponsorPostUpdatedEvent(sp:SponsorPost) extends StreamEvent("sponsor-post-updated")
//case class SponsorPostAppearEvent(sp:SponsorPost) extends StreamEvent("sponsor-post-appear")

case class IndexEvent(idx:Long, total:Long, working:String, percentage:Double, indexType:String = "") extends StreamEvent("index")

/**
 * Di-dispatch ketika set sticky dalam sebuah post
 * @param post see [[com.ansvia.digaku.model.Post]]
 */
case class StickPostEvent(post:Post) extends StreamEvent("sticked-post")
case class StickPictureEvent(pic:Picture) extends StreamEvent("sticked-picture")


/**
 * merupakan event ketika sebuah object model di-update pada bagian
 * atribut yang mempengaruhi behavior search engine ketika indexing.
 * @param obj model yang di-update.
 * @param whatAttributes daftar attribute yang telah di-update.
 */
case class IndexAttributeUpdate(obj:BaseModel[IDType], whatAttributes:List[String]) extends StreamEvent("index-attribute-update")


/**
 * Event yang di-dispatch ketika ada user yang view sebuah object seperti post dll yang Viewable.
 * @param viewer user yang view, set None untuk anonymous mode.
 * @param obj object yang di-view.
 */
case class ObjectViewEvent(viewer:Option[model.User], obj:model.Viewable[IDType]) extends StreamEvent("object-view")

case class CreateGlobalAnnouncementEvent(userId:Long) extends StreamEvent("create-global-announcement")
case class CreateForumAnnouncementEvent(userId:Long, chId:Long) extends StreamEvent("create-forum-announcement")

case class AddTopicAgentEvent(topic:Topic, user:User) extends StreamEvent("add-topic-agent")
// Event untuk pengubahan status arsip pada topic.
case class ArchiveTopicEvent(topicId:Long) extends  StreamEvent("archive-topic")

