/*
 * Copyright (c) 2013-2017 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.event.listener

import java.util.concurrent.CountDownLatch

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.event.impl.{LatestForumNewsEvent, CreatePostEvent}
import com.ansvia.digaku.event.{StreamEvent, AsyncEventStreamListener}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.model._
import com.ansvia.digaku.stats.model.DigakuStats
import com.tinkerpop.blueprints.Vertex
import org.joda.time.DateTime
import com.ansvia.digaku.database.GraphCompat._
import com.ansvia.graph.BlueprintsWrapper._

import scala.concurrent.{ExecutionContext, Future}

/**
 * Author: nadir (<EMAIL>)
 * Listener ini digunakan untuk menambahkan stream ke follower sebuah forum
 * ketika ada create post event.
 */

object AsyncForumFollowerStreamListener extends AsyncEventStreamListener with Slf4jLogger with DbAccess {
    override val actorName: String = "user-post-stream"

    /**
     * Check is event handled by this listener.
     * WARNING: don't use `transact` inside this method
     * to avoid dead-lock.
     * @param eventName name of event.
     * @return
     */
    override def isEventHandled(eventName: String): Boolean = eventName == "create-post"

    override def asyncDispatch: PartialFunction[StreamEvent, Unit] = {
        case CreatePostEvent(_, post) =>
            ForumFollowerStreamBuilder.addToStream(post)

    }
}


object ForumFollowerStreamBuilder extends DbAccess with Slf4jLogger {

    implicit private lazy val ec:ExecutionContext = ExecutionContext.fromExecutor(new scala.concurrent.forkjoin.ForkJoinPool)

    /**
     * Menambahkan stream ke follower dari post origin
     * @param post
     */
    def addToStream(post:Post):Unit = {

        val origin = post.origin
        val postId = post.getId

        origin match {
            case forum:Forum =>

                val _forum = db.getVertex(forum.getId).toCC[Forum].get

                val totalCount = _forum.getFollowerCount()

                val divider = {
                    if (totalCount > 5000) {
                        6
                    } else {
                        4
                    }
                }

                val div = Math.ceil(totalCount.toDouble / divider).toInt

                lazy val latch = new CountDownLatch(divider)

                debug("processing stream with these distribution loads:")

                for ( di <- 0 until divider ) {

                    val offset = di * div

                    if (totalCount > offset) {
                        Future {

                            try {
                                debug(s" $di offset $offset limit $div")
                                Thread.sleep(100)

                                val currentWorkerId = di
                                debug(s"#$di starting calculation $offset to $div ...")

                                try {
                                    var count = 0

                                    val _postV = db.getVertex(postId)
                                    val dateTime = new DateTime(post.creationTime)
                                    val year = dateTime.getYear
                                    val month = dateTime.getMonthOfYear

                                    for (user <- _forum.getFollowers(offset, div)) {
                                        debug(s"add stream post :$postId to user :${user.getId}")

                                        val _cv = db.getVertex(user.getYMPartEx(CollectionVertex.Kind.USER_STREAM, year, month, db).getId)
                                        val ed = _cv.addEdge(USER_STREAM, _postV)

                                        ed.setProperty("timeOrder", _postV.getOrElse("creationTime", 0L))

                                        user.incrementForumStreamCount()

                                        count += 1

                                        if (count % 100 == 0) {
                                            db.commit()
                                        }
                                    }

                                } catch {
                                    case e: Exception =>
                                        error(e.getMessage)
                                        error(e.getStackTraceString)
                                }

                                db.commit()
                                debug(s"#$currentWorkerId processing user stream from group $origin done.")

                            } catch {
                                case e: Exception =>
                                    error(e.getMessage)
                                    error(e.getStackTraceString)
                            } finally {
                                latch.countDown()
                            }
                        }
                    } else {
                        latch.countDown()
                    }
                }

                latch.await()

                debug(s"stream processing for group $origin done.")

                Digaku.engine.eventStream.emit(LatestForumNewsEvent(post))

            case _ =>
        }
    }

}
