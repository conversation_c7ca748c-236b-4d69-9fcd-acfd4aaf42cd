/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.event


/**
 * Author: robin
 *
 * Base EventStreamListener, untuk contoh implementasinya lihat
 * [[com.ansvia.digaku.event.EventStreamLogger]]
 */
abstract class EventStreamListener {

    type PF = PartialFunction[StreamEvent, Unit]

    /**
     * Check is event handled by this listener.
     * WARNING: don't use `transact` inside this method
     * to avoid dead-lock.
     * @param eventName name of event.
     * @return
     */
    def isEventHandled(eventName:String):Boolean
    def dispatch:PF

    override def toString = this.getClass.getSimpleName
}



