/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.event

import akka.actor.SupervisorStrategy.{Restart, Resume, Stop}
import akka.actor.{Actor, OneForOneStrategy, PoisonPill, Props}
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku


/**
 * Author: robin
 *
 */

trait Dispatcher {
    def dispatch:PartialFunction[StreamEvent, Unit]
}

trait AsyncDispatcher extends Dispatcher {
    def asyncDispatch:PartialFunction[StreamEvent, Unit]
}

/**
 * Event stream listener untuk mode asynchronous
 * non-blocking menggunakan Akka actor.
 */
abstract class AsyncEventStreamListener extends EventStreamListener with AsyncDispatcher {

    val actorName:String

    lazy val worker = {
        Digaku.engine.actorSystem.actorOf(Props(new DispatcherActor(this)), "async-esld-" + actorName)
    }

    final def dispatch:PartialFunction[StreamEvent, Unit] = {
        case event =>
            worker ! event
    }

    def close(){
        worker ! PoisonPill
    }
}

/**
 * Like [[com.ansvia.digaku.event.AsyncEventStreamListener]] but support load balancing
 *
 * @param dispatchers list of dispatcher where will spawned as actors
 *                    and all message will be send to dispatcher
 *                    in round-robin fashion.
 */
abstract class AsyncLBEventStreamListenerGroup(dispatchers:Dispatcher*)
    extends EventStreamListener with Slf4jLogger {



    lazy val workers = {
        dispatchers.map { dp =>
            Digaku.engine.actorSystem.actorOf(Props(new DispatcherActor(dp)), "async-esld-" + dp)
        }
    }.toIndexedSeq

    private var idx = 0
    private val max = dispatchers.length

    def dispatch = {
        case event =>

            if (idx > max - 1){
                idx = 0
            }

            val worker = workers(idx)

            debug("dispatching to worker " + worker)

            worker ! event

            idx = idx + 1


    }
}

/**
 * Like [[com.ansvia.digaku.event.AsyncEventStreamListener]] but support load balancing
 *
 * @param dispatchers list of dispatcher where will spawned as actors
 *                    and all message will be send to dispatcher
 *                    in round-robin fashion.
 */
abstract class BlockingLBEventStreamListenerGroup(dispatchers:Dispatcher*)
    extends EventStreamListener with Slf4jLogger {


    private var idx = 0
    private val max = dispatchers.length

    def dispatch = {
        case event =>

            if (idx > max - 1){
                idx = 0
            }

            val worker = dispatchers(idx)

            debug("dispatching to worker " + worker)

            worker.dispatch(event)

            idx = idx + 1


    }
}

class DispatcherActor(_listener:Dispatcher) extends Actor with Slf4jLogger {

    import scala.concurrent.duration._

    override val supervisorStrategy =
        OneForOneStrategy(maxNrOfRetries = 15, withinTimeRange = 30.seconds) {
            case _: ArithmeticException      => Resume
            case _: NullPointerException     => Restart
            case _: IllegalArgumentException => Stop
            case _: Exception                => Restart
        }

    def receive = {

        case event:StreamEvent =>
            if(this._listener != null){
                try {
                    this._listener match {
                        case lsn:AsyncDispatcher =>
                            lsn.asyncDispatch.apply(event)
                        case lsn:Dispatcher =>
                            lsn.dispatch.apply(event)
                    }
                }catch{
                    case e:Throwable =>
                        error(e.getMessage)
                        error(e.getStackTraceString)
                }
            }else{
                error("cannot async dispatch while listener is null, may not registered yet??")
            }

        case _ =>

    }
}
