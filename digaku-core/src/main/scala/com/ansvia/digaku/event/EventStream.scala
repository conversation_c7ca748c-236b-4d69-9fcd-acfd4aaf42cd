/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.event


import com.ansvia.commons.logging.Slf4jLogger
import akka.actor.{Po<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Actor}
import com.ansvia.digaku.Digaku




/**
 * Author: robin
 *
 * EventStream adalah pattern
 * dalam sebuah service
 * yang memungkinkan sebuah
 * aplikasi mengalirkan setiap event
 * yang terjadi selama aplikasi berjalan,
 * cara kerjanya mirip logging patterns
 * bedanya EventStream bisa di-listen
 * oleh EventStreamListener
 * dan melakukan apapun yang match
 * dengan event tersebut.
 *
 * Salah satu contoh fungsi event stream adalah
 * untuk meng-dispatch notifikasi, cache invalidation, trophy, etc.
 *
 * Semua operasi event stream harus asynchronous.
 */

private[digaku] trait EventStreamComponent extends Slf4jLogger {

    val eventStream:EventStream

}

private[digaku] class EventStream extends Slf4jLogger {

    private var _listeners = Array.empty[EventStreamListener]
    private var ready = false
    private lazy val eventActor = {
        val rv = Digaku.engine.actorSystem.actorOf(Props[EventStreamActor], "event-stream")
        rv ! Ready()
        ready = true
        rv
    }
    private var blocking = false
    private var enabled = true


    def setBlocking(state:Boolean) = {
        this.blocking = state
        this
    }

    def setEnabled(state:Boolean) = {
        this.enabled = state
        this
    }


    /**
     * Add listeners.
     * @param listeners listener to add.
     */
    def addListeners(listeners:EventStreamListener*){
        synchronized {
            for(lsn <- listeners){
                if(!this._listeners.contains(lsn)){
                    this._listeners +:= lsn

                    info(lsn + " registered.")
                }
            }
        }
    }

    /**
     * Remove listeners.
     * @param lsn listener to remove.
     */
    def removeListeners(lsn:EventStreamListener){
        synchronized {
            this._listeners = this._listeners.filterNot(_ != lsn)
        }
    }

    /**
     * Clear all listeners.
     */
    def clearListeners(){
        synchronized {
            this._listeners = Array.empty
        }
    }

    /**
     * Emit event to stream.
     * @param event event to emit.
     */
    def emit(event:StreamEvent){
        if (!enabled)
            return

        synchronized {
            for (lsn <- _listeners){
                if (lsn.isEventHandled(event.name)){
                    if (!blocking){
                        eventActor ! Dispatch(lsn, event)
                    }else{
                        lsn.dispatch(event)
                    }
                }
            }
        }
    }

    /**
     * Check whether this event stream is ready.
     * @return
     */
    def isReady = ready || blocking

    /**
     * Get registered listeners.
     * @return
     */
    def listeners = _listeners

    /**
     * Close / Shutdown this event stream actor
     * and it listeners.
     */
    def close(){
        eventActor ! PoisonPill
        for( lsr <- _listeners ){
            lsr match {
                case a:AsyncEventStreamListener =>
                    a.close()
                case _ =>
            }
        }
    }
}

sealed case class Dispatch(listener:EventStreamListener, event:StreamEvent)
sealed case class Ready()

sealed class EventStreamActor extends Actor with Slf4jLogger {
    def receive = {

        case Ready() =>
            info("ready.")

        case Dispatch(listener, event) =>
            listener.dispatch(event)

    }
}





