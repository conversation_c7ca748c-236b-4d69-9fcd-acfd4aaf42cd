/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

// DISABLED: NOT USED YET
///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.event.listener
//
//import com.ansvia.digaku.event.{StreamEvent, AsyncEventStreamListener}
//import com.ansvia.digaku.event.impl.{CreateUserEvent, CreatePostEvent}
//import com.mongodb.casbah.{MongoClient, MongoConnection, MongoDB}
//import com.ansvia.digaku.model.User
//import com.mongodb.casbah.commons.{MongoDBList, MongoDBObject}
//
///**
// * Author: robin
// *
// */
//class MongoRedundantBackupListener(mongoHostNPort:String) extends AsyncEventStreamListener {
//
//    val actorName: String = "mongo-redundant-backup"
//
//    private lazy val events = Seq("create-post")
//
//    private lazy val connection = MongoClient(mongoHostNPort)
//    private lazy val mongoDb = connection("digaku")
//    private lazy val userCol = mongoDb("user")
//    private lazy val channelCol = mongoDb("group")
//    private lazy val postCol = mongoDb("user_post")
//    private lazy val pmCol = mongoDb("tunnel")
//    private lazy val responseCol = mongoDb("response")
//    private lazy val appCol = mongoDb("application")
//    private lazy val embeddedDataCol = mongoDb("embedded_data")
//    private lazy val userSettingsCol = mongoDb("user_settings")
//
//
//    /**
//     * Check is event handled by this listener.
//     * WARNING: don't use `transact` inside this method
//     * to avoid dead-lock.
//     * @param eventName name of event.
//     * @return
//     */
//    def isEventHandled(eventName: String): Boolean = events.contains(eventName)
//
//    def asyncDispatch: PartialFunction[StreamEvent, Unit] = {
//        case x =>
//            this.asyncDispatchInternal.apply(x)
//
//    }
//
//    private def asyncDispatchInternal: PartialFunction[StreamEvent, Unit] = {
//        case CreateUserEvent(user) => handleCreateUser(user)
//        case CreatePostEvent(creator, post) => {
//            // @TODO(robin): code here
//        }
//    }
//
//
//    private def handleCreateUser(user:User){
//        val birthDate = user.getBirthDate
//        val doc = MongoDBObject(
//            "name" -> user.name, "lower_name" -> user.lowerName,
//            "full_name" -> user.fullName, "birth_date" -> birthDate,
//            "email_login" -> user.emailLogin, "email_addresses" -> MongoDBList(user.emailLogin),
//            "personal_descs" -> MongoDBList(user.selfDescs.split(",").toList: _*),
//            "timezone" -> 7, "location" -> user.location, "verified" -> user.verified,
//            "title" -> user.title, "abilities" -> MongoDBList(user.abilities: _*),
//            "_level" -> user.level, "_point" -> user.point
//        )
//        userCol.insert(doc)
//    }
//}
