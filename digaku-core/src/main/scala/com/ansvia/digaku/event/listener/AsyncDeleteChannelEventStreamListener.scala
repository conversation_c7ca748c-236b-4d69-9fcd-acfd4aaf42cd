/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.event.listener

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.event.impl.DeleteChannelEvent
import com.ansvia.digaku.event.{AsyncEventStreamListener, StreamEvent}
import com.ansvia.digaku.exc.NotExistsException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model._

/**
 * Author: fajrhf
 *
 */

/**
 * Event stream listener yang digunakan untuk melakukan penanganan data pasca-delete group
 * Dilakukan secara async agar tidak menimbulkan blocking
 */
object AsyncDeleteChannelEventStreamListener extends AsyncEventStreamListener with Slf4jLogger with DbAccess {

    val actorName = "delete-group-esl"

    /**
     * Check is event handled by this listener.
     * WARNING: don't use `transact` inside this method
     * to avoid dead-lock.
     * @param eventName name of event.
     * @return
     */
    def isEventHandled(eventName: String) = eventName == "delete-forum"

    def asyncDispatch:PartialFunction[StreamEvent, Unit] = {
        case ev =>
                try {
                    debug("processing stream-event: " + ev)
                    asyncDispatchInternal.apply(ev)
                }catch{
                    case e:Exception =>
                        error(e.getMessage)
                        error(e.getStackTraceString)
                }
    }

    private def asyncDispatchInternal: PartialFunction[StreamEvent, Unit] = {
        case DeleteChannelEvent(ch) => {

            val members = ch.getMembers(0, ch.getMemberCount())
            val staffs = ch.getStaffs(0, ch.getStaffCount).map(_.user)

//            tx { trx =>
//            //remove member stream
//                members.map { user =>
//
//                    if(user.isBoorkmarked(ch)) {
//                        user.unbookmarkChannel(ch)
//                    }
//
//                    debug("removing stream user: " + user)
//                    ch.getStreamAll(None, None, 0, 100).toList.foreach { postToRemove =>
//                    //remove from member stream
//                        trx.getVertex(user.getId).pipe.out(COLLECTION_VERTEX)
//                            .has("kind",CollectionVertex.Kind.STREAM)
//                            .range(0,1).outE(STREAM)
//                            .has("targetId", postToRemove.getId)
//                            .remove()
//
//                    }
//                }
//
//                //remove invitation
//                debug("removing invitation..")
//                ch.getVertex.pipe.outE(INVITE)
//                    .asInstanceOf[GremlinPipeline[Edge,Edge]]
//                    .headOption.map(e=>db.removeEdge(e))
//            }

            debug("removing member..")
            ch.removeMembers(members: _*)
            debug("unsetting staff..")
            ch.unsetStaff(staffs: _*)

            val chx = Forum.getById(ch.getId).getOrElse {
                throw NotExistsException("Group not exists")
            }
            debug(chx.getName + " successfully deleted")

        }
    }

}
