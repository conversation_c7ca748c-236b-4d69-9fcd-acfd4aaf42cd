/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.event

import com.ansvia.commons.logging.Slf4jLogger

/**
 * Contoh implementasi EventStreamListener
 * ini tidak melakukan apa-apa, hanya melakukan logging saja.
 */
case class EventStreamLogger() extends EventStreamListener with Slf4jLogger {

    def isEventHandled(eventName: String) = true

    def dispatch = {
        case event =>
            debug("got event %s".format(event))
    }
}
