/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */
//
//package com.ansvia.digaku.event.listener
//
//import com.ansvia.digaku.event.{StreamEvent, AsyncEventStreamListener, EventStreamListener}
//import com.ansvia.digaku.event.impl.CreatePostEvent
//import com.ansvia.digaku.model.Forum
//import com.ansvia.digaku.Types.GremPipeEdge
//import com.ansvia.digaku.helpers.DbAccess
//
///**
// * Author: robin
// *
// */
//
///**
// * Event stream listener yang kerjanya untuk mroses bookmark group
// * sebagai contoh untuk generasiin informasi scoop (jumlah content yang belum
// * dibaca oleh user yang meng-bookmar group).
// */
//class BlockingBookmarkChannelEventStreamListener extends EventStreamListener with DbAccess {
//
//    import scala.collection.JavaConversions._
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    import com.ansvia.digaku.model.Label.{BOOKMARK_CHANNEL, ORIGIN}
//
//    /**
//     * Check is event handled by this listener.
//     * WARNING: don't use `transact` inside this method
//     * to avoid dead-lock.
//     * @param eventName name of event.
//     * @return
//     */
//    def isEventHandled(eventName: String) = eventName == "create-post"
//
//    def dispatch: PartialFunction[StreamEvent, Unit] = {
//        case x =>
//            try {
//               dispatchInternal.apply(x)
//            }catch{
//                case e:Exception =>
//                    e.printStackTrace()
//            }
//    }
//
//    private def dispatchInternal: PartialFunction[StreamEvent, Unit] = {
//        case CreatePostEvent(creator, post) => {
//            post.getVertex.pipe.out(ORIGIN).range(0,1).headOption
//            .flatMap(_.toCC[Forum]).map { ch =>
//                ch.reload()
//                ch.getVertex.pipe.inE(BOOKMARK_CHANNEL).has("targetId", ch.getId)
//                    .asInstanceOf[GremPipeEdge]
//                    .iterator()
//                    .foreach { ed =>
//
//                    val unread = ed.getOrElse("unreadContent", 0) + 1
//                    ed.setProperty("unreadContent", unread)
//                }
//                db.commit()
//            }
//        }
//    }
//}
//
//
///**
// * Sama seperti [[com.ansvia.digaku.event.listener.BlockingBookmarkChannelEventStreamListener]]
// * hanya saja ini versi async-nya.
// */
//object AsyncBookmarkChannelEventStreamListener extends AsyncEventStreamListener {
//
//    val actorName = "bookmark-group"
//
//    private val _esl = new BlockingBookmarkChannelEventStreamListener
//
//    /**
//     * Check is event handled by this listener.
//     * WARNING: don't use `transact` inside this method
//     * to avoid dead-lock.
//     * @param eventName name of event.
//     * @return
//     */
//    def isEventHandled(eventName: String) = _esl.isEventHandled(eventName)
//
//    def asyncDispatch = {
//        case x =>
//            _esl.dispatch.apply(x)
//    }
//
//
//}
