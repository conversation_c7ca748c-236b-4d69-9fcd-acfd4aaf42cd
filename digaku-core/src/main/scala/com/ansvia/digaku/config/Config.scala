/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.config

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.persistence.BackedKVStoreIface
import org.apache.commons.configuration.PropertiesConfiguration


abstract class ConfigCore(val dbConf:PropertiesConfiguration, val nodeId:String, val machineId:Int){

    val eventStreamSupport:Boolean = true
    val useBlockingEventStream:Boolean = true
    val usingExternalStreamBuilder:Boolean = true
    val useBlockingEventStreamListener:Boolean = true
    val useBlockingSEEStreamListener:Boolean = false
    val withAnalEventStreamListener:Boolean = false
    val useBlockingAnalEventStreamListener = false
    val useBlockingNotifierSender = false
    val nsqLookupHost:String = ""
    val nsqPublisherHost:String = ""
    def supportNsq = nsqLookupHost != ""
    val nsqTopic = ""
    val testMode:Boolean
    lazy val searchEngineProvider:String = {
        Digaku.engine.systemConfig.get("search.engine", "embedded-elastic-search")
    }
    val enableMachineLearning = false

    trait CassandraDatabase {
        val clusterName:String
        val hostName:String
        val keyspaceName:String
        val replStrategy:String
        val replStrategyOpts:String
    }
    val mainDatabase:MainDatabase
    trait MainDatabase extends CassandraDatabase
    val analyticDatabase:AnalyticDatabase
    trait AnalyticDatabase extends CassandraDatabase

    override def toString: String = {
        s"""
          |eventStreamSupport: $eventStreamSupport
          |useBlockingEventStream: $useBlockingEventStream
          |usingExternalStreamBuilder: $usingExternalStreamBuilder
          |useBlockingEventStreamListener: $useBlockingEventStreamListener
          |useBlockingSEEStreamListener: $useBlockingSEEStreamListener
          |withAnalEventStreamListener: $withAnalEventStreamListener
          |useBlockingAnalEventStreamListener: $useBlockingAnalEventStreamListener
          |useBlockingNotifierSender: $useBlockingNotifierSender
          |supportNsq: $supportNsq
          |enableMachineLearning: $enableMachineLearning
          |nsqLookupHost: $nsqLookupHost
          |nsqPublisherHost: $nsqPublisherHost
          |nsqTopic: $nsqTopic
          |searchEngineProvider: $searchEngineProvider
          |testMode: $testMode
        """.stripMargin
    }
}

trait ConfigComponent {
    
    val config:ConfigCore

    // different with config, this is system config that persisted on storage backend
    // and can modify during runtime
    val systemConfig:BackedKVStoreIface


}


object Config extends Slf4jLogger {


//    var dbConf:PropertiesConfiguration = _
//    var idFactory:IdFactory = _
    var redisHost:String=""
    var zookeeperHost:String=""
    var emailSupport:Boolean=false
    var smtpServer:String=""
    var debugMode:Boolean=false
    var kafkaHost:String=""
    var autoCloseWhenDbNotUsed:Boolean = false
//    var supportEventStream = true


    // set true apabila di test mode (untuk acceptance/unittest)
    var testMode = false

    // supported:
    //      "embedded-elastic-search"
//    var searchEngineProvider:String = ""

    // for embedded elasticsearch
//    var esIndexDir:String = "/tmp/digaku-index-dir"

    // for distributed elasticsearch
    var esHost:String = "localhost:9300"

//    var emoticonProcessor:EmoticonProcessor = new NopEmoticonProcessor
//    var textTagProcessor:NameTagProcessor = new NopNameTagProcessor
//    var textNameCompiler:NameCompiler = new NopNameCompiler
//    var linkTransformer:LinkTransformer = new NopLinkTransformer
//    var useBlockingNotifierSender = false
//    var useBlockingEventStream = false
//    var useBlockingEventStreamListener = false
//    var withAnalEventStreamListener = true
//    var useBlockingSEEStreamListener = false
//    var useBlockingAnalEventStreamListener = false
//    var counterProvider:CounterProvider = NopCounterProvider
//    var usingExternalStreamBuilder = false

    val STANDARD_CF_NAME = com.ansvia.digaku.Global.STANDARD_CF_NAME
//    var standardCassandraDriver:CassandraDriver = null

    def init(){
//
//        if (Global.database == null){
//            assert(idFactory!=null,"idFactory not set, please set it first")
//
//            Global.database = {
//                TitanDatabase(dbConf, idFactory, useIdGraph = true)
////                dbConf match {
////                    case conf:TitanConfig =>
////                        TitanDatabase(conf)
////                    case _ =>
////                        throw NotSupportedException("%s not supported yet".format(dbConf))
////                }
//            }
//        }

//        Digaku.engine.searchEngine = searchEngineProvider match {
//            case "embedded-elastic-search" =>
//                new EmbeddedElasticSearchEngine(esIndexDir)
//            case "distributed-elastic-search" =>
//                new DistributedElasticSearchEngine(esHost)
//            case "" =>
//                NopSearchEngine
//            case x =>
//                throw NotSupportedException(("Unsupported search engine provider: \"%s\". " +
//                    "currently support only \"embedded-elastic-search\"").format(x))
//        }
//
//        // print info
////        info("database: " + dbConf)
//        info("search engine: " + Digaku.engine.searchEngine)


    }

    def reset(){
//        searchEngineProvider = ""
//        esIndexDir = ""
        autoCloseWhenDbNotUsed = false
    }



}

