/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.config

import org.apache.commons.configuration.PropertiesConfiguration

/**
 * Author: nadir
 * Date: 1/3/13
 * Time: 1:29 PM
 *
 */

///**
// * Configuration untuk Titan database.
// * @param storage tipe storage yang akan digunakan, mendukung:
// *                + memory:titan
// *                      in memory database, hanya disimpan dimemory, data akan
// *                      hilang begitu aplikasi di-restart/mati.
// *
// *                + cassandra:[HOST]:[PORT]
// *                      menggunakan Cassandra sebagai backend storage-nya.
// *                      contoh:
// *                          cassandra:localhost:9160
// *
// *                + hbase:[HOST]:[PORT]
// *                      menggunakan Hbase sebagai backend storage-nya.
// *                      contoh:
// *                          hbase:localhost:5000
// *
// *                + berkeleyje:[DIRECTORY]
// *                      menggunakan BerkeleyDB sebagai backend storage-nya.
// *                      contoh:
// *                          berkeleyje:/tmp/db-dir
// * @param sharedThread apakah akan jalan sebagai shared thread atau tidak, default = true.
// * @param indexingEngine set indexing engine, default `BuiltIn`.
// */
//case class TitanConfig(storage:String="memory:titan",
//                       idFactory:IdFactory = new LocalIdFactory(),
//                       sharedThread:Boolean = false,
//                       indexingEngine:IndexingEngine=BuiltIn,
//                       replicationFactor:Int=1,
//                       keyspace:String="titan",
//                       machineIdAppendix:Int=1,
//                       writeConsistency:String="QUORUM",
//                       readConsistency:String="QUORUM",
//                       nodeDiscoveryType:String="NONE"
//                          ) extends BaseDbConfig {
//
//    var batchLoading = false
//
//}

object TitanConfig {
    def getDefault() = {

        // setup database
        val dbConf = new PropertiesConfiguration()
        dbConf.setProperty("storage.backend", "cassandra")
        dbConf.setProperty("cache.db-cache", true)
        dbConf.setProperty("cache.db-cache-time", 60000) // 1 minutes

        dbConf.setProperty("storage.hostname", "127.0.0.1")
        dbConf.setProperty("storage.port", 9160)
        dbConf.setProperty("storage.cassandra.keyspace", "digaku_unset")
        dbConf.setProperty("storage.cassandra.replication-strategy-class", "SimpleStrategy")
        dbConf.setProperty("storage.cassandra.replication-strategy-options", "replication_factor,1")
        dbConf.setProperty("storage.replication-factor", 1)
        dbConf.setProperty("storage.cassandra.write-consistency-level", "QUORUM")
        dbConf.setProperty("storage.cassandra.read-consistency-level", "QUORUM")
        dbConf.setProperty("storage.cassandra.astyanax.node-discovery-type", "NONE")
        dbConf.setProperty("storage.cluster-name", "digaku")
        dbConf.setProperty("storage.batch-loading", false)
        dbConf.setProperty("schema.default", "none")

        dbConf
    }

}

trait IndexingEngine
object IndexingEngine {

    /**
     * Setting for indexing using elastic search.
     * see: https://github.com/thinkaurelius/titan/wiki/Using-Elastic-Search
     * @param hostname Comma-separated list of IP addresses or hostnames of the instances in the Elasticsearch cluster.
     * @param indexName Name of the index.
     * @param clusterName Name of the Elasticsearch cluster. If none is defined, the name will be ignored.
     * @param localMode Whether Titan should run Elasticsearch embedded.
     * @param directory Directory to store Elasticsearch data in. Only applicable when running Elasticsearch embedded.
     * @param yamlFile Filename of the Elasticsearch yaml file used to configure this instance.
     *                 Only applicable when running Elasticsearch embedded
     */
    case class ElasticSearch(hostname:String, indexName:String,
                             clusterName:String, localMode:Boolean,
                             directory:String, yamlFile:String) extends IndexingEngine {
        override def toString = "ES"
    }

    /**
     * Setting for indexing using native lucene.
     * @param dir lucene index dir.
     */
    case class Lucene(dir:String) extends IndexingEngine {
        override def toString = "Lucene"
    }

    object BuiltIn extends IndexingEngine {
        override def toString = "Built-in"
    }
}

