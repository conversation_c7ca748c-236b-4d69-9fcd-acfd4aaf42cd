/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.config

import java.util.regex.Pattern

import com.ansvia.digaku.Engine
import com.ansvia.digaku.database.TitanDatabase
import com.ansvia.digaku.event.AsyncEventStreamListener
import com.ansvia.digaku.exc.NotSupportedException
import com.ansvia.digaku.notifications.NotificationSender
import com.ansvia.digaku.persistence.{CassandraCounterProvider, CassandraDriver, CounterProvider, SnowFlakeIdFactory}
import com.ansvia.digaku.se._
import com.ansvia.digaku.stats.AnalEventStreamListener
import com.netflix.astyanax.connectionpool.NodeDiscoveryType
import com.netflix.astyanax.connectionpool.impl.ConnectionPoolType
import com.netflix.astyanax.model.ColumnFamily
import com.netflix.astyanax.serializers.{LongSerializer, StringSerializer}
import com.tinkerpop.blueprints.util.wrappers.id.IdGraph.IdFactory
import org.apache.commons.configuration.PropertiesConfiguration
import org.elasticsearch.client.transport.NoNodeAvailableException
import org.streum.configrity.Configuration

import scala.util.matching.Regex

/**
 * Author: robin (<EMAIL>)
 */

abstract class EngineWithConfig(val fileConf:Configuration) extends Engine {

    private val dbConf = new PropertiesConfiguration()

    /**
     * single point of access of NotificationSender
     * @return
     */
    def getNotificationSender = NotificationSender

    {
        if (config.testMode){
            // gunakan berkeley untuk test mode
//            dbConf.setProperty("storage.backend", "berkeleyje")
//            FileUtils.createDirectory("/tmp/digaku-test-db")
//
//            dbConf.setProperty("storage.directory", "/tmp/digaku-test-db/db_" + System.currentTimeMillis())

            dbConf.setProperty("storage.backend", "cassandrathrift")

            val cassandraHost = fileConf[String]("database.cassandra.host", "unset")
            val hostNPort = cassandraHost.split("\\:")

            val keyspaceName = fileConf[String]("database.cassandra.keyspace", "digaku_test")
            val clusterName = fileConf("database.cassandra.cluster-name", "digaku")
            val strategyClass = fileConf("database.cassandra.replication-strategy-class", "SimpleStrategy")
            val strategyOptions = fileConf("database.cassandra.replication-strategy-options", "replication_factor:1")


            // dibutuhkan untuk yg lainnya
            dbConf.setProperty("storage.hostname", hostNPort(0))
            dbConf.setProperty("storage.port", hostNPort(1))
            dbConf.setProperty("storage.cassandra.keyspace", keyspaceName)
            dbConf.setProperty("storage.cluster-name", clusterName)
            dbConf.setProperty("storage.cassandra.replication-strategy-class", strategyClass)
            dbConf.setProperty("storage.cassandra.replication-strategy-options",
                strategyOptions.replaceAll(Pattern.quote(":"), Regex.quoteReplacement(",")))
        }else{
            val cassandraHost = fileConf[String]("database.cassandra.host", "unset")
            val hostNPort = cassandraHost.split("\\:")
            val cassandraKeyspaceName = fileConf[String]("database.cassandra.keyspace", "titan")

            val driver = fileConf("database.cassandra.driver", "astyanax") match {
                case "thrift" =>
                    "cassandrathrift"
                case x => x
            }

            dbConf.setProperty("storage.backend", driver)
            dbConf.setProperty("storage.hostname", hostNPort(0))
            dbConf.setProperty("storage.port", hostNPort(1).toInt)
            dbConf.setProperty("storage.cassandra.keyspace", cassandraKeyspaceName)
            dbConf.setProperty("storage.cassandra.write-consistency-level", fileConf[String]("database.cassandra.write-consistency-level", "QUORUM"))
            dbConf.setProperty("storage.cassandra.read-consistency-level", fileConf[String]("database.cassandra.read-consistency-level", "QUORUM"))
            dbConf.setProperty("storage.cassandra.astyanax.node-discovery-type", fileConf("database.cassandra.node-discovery-type", "NONE"))
            dbConf.setProperty("storage.batch-loading", false)
            //        dbConf.setProperty("cache.db-cache", true)
            //        dbConf.setProperty("cache.db-cache-time", 15000)
            dbConf.setProperty("cache.db-cache", fileConf("database.cache", true))
            dbConf.setProperty("cache.db-cache-time", fileConf("database.cache-time", 600000))
            dbConf.setProperty("tx-cache-size", 1000)
            dbConf.setProperty("storage.cluster-name", fileConf("database.cassandra.cluster-name", "digaku"))
            // not Titan specific config
            dbConf.setProperty("storage.cassandra.replication-strategy-class",
                fileConf("database.cassandra.replication-strategy-class", "SimpleStrategy"))
            val strategyOptions = fileConf("database.cassandra.replication-strategy-options", "replication_factor:1")
                .replaceAll(Pattern.quote(":"), Regex.quoteReplacement(","))
            debug("strategyOpts: " + strategyOptions)
            dbConf.setProperty("storage.cassandra.replication-strategy-options", strategyOptions)
        }
    }

    private def parseDiscoType(discoType:String) = {
        discoType match {
            case "RING_DESCRIBE" => NodeDiscoveryType.RING_DESCRIBE
            case "NONE" => NodeDiscoveryType.NONE
            case "TOKEN_AWARE" => NodeDiscoveryType.TOKEN_AWARE
        }
    }

    private def parseConnectionType(connType:String) = {
        connType match {
            case "BAG" => ConnectionPoolType.BAG
            case "ROUND_ROBIN" => ConnectionPoolType.ROUND_ROBIN
            case "TOKEN_AWARE" => ConnectionPoolType.TOKEN_AWARE
        }
    }


    // ensure multipurpose common column family exists
    lazy val commonCassandraCf: CassandraDriver[String, String] = {
        val clusterName = fileConf[String]("database.cassandra.cluster-name")
        val cf = new ColumnFamily[String, String]("Standard1", StringSerializer.get(), StringSerializer.get())

        val csh = new CassandraDriver[String, String](fileConf[String]("database.cassandra.keyspace", "titan"),
                cf, clusterName, fileConf[String]("database.cassandra.host", "unset"),
                fileConf("database.cassandra.replication-strategy-class", "SimpleStrategy"),
                fileConf("database.cassandra.replication-strategy-options", "replication_factor:1"),
                parseDiscoType(fileConf("database.cassandra.node-discovery-type", "RING_DESCRIBE")),
                parseConnectionType(fileConf("database.cassandra.connection-pool-type", "ROUND_ROBIN")))
        csh.ensureKeyspaceExists
        csh.ensureColumnFamilyExists
        csh
    }

    class EWCConfigCore extends ConfigCore(dbConf, fileConf[String]("id"), fileConf[Int]("database.machine-id", 0)){

        override val testMode: Boolean = false
        override val useBlockingEventStream: Boolean = fileConf("engine.use-blocking-event-stream", false)

        override val eventStreamSupport: Boolean = fileConf[Boolean]("engine.support-event-stream", true)

        override val nsqLookupHost = fileConf[String]("nsq.lookup-host","")
        override val nsqPublisherHost = fileConf[String]("nsq.publisher-host","")
        override def supportNsq = nsqLookupHost != ""


        override val enableMachineLearning: Boolean = fileConf[Boolean]("engine.enable-machine-learning", false)

        override val usingExternalStreamBuilder: Boolean = if (fileConf("engine.use-minion-stream-builder", false)){
            require(supportNsq, "when using minion stream builder you need to use NSQ, " +
                "please set it in config")
            true
        }else
            false

        override val useBlockingEventStreamListener: Boolean = fileConf("engine.use-blocking-event-stream-listener", false)
        override val useBlockingSEEStreamListener: Boolean = false
        override val withAnalEventStreamListener: Boolean = fileConf("engine.anal-event-stream-listener", true)
        override val useBlockingAnalEventStreamListener: Boolean = fileConf("engine.use-blocking-anal-event-stream-listener", false)
        override val useBlockingNotifierSender: Boolean = fileConf("engine.use-blocking-notifier-sender", false)
        override val mainDatabase:MainDatabase = new MainDatabase {
            override val keyspaceName: String = fileConf[String]("database.cassandra.keyspace", "titan")
            override val clusterName: String = fileConf[String]("database.cassandra.cluster-name", "digaku")
            override val hostName: String = fileConf[String]("database.cassandra.host", "unset")
            override val replStrategy: String = fileConf[String]("database.cassandra.replication-strategy-class", "SimpleStrategy")
            override val replStrategyOpts: String = fileConf[String]("database.cassandra.replication-strategy-options", "replication_factor:1")
        }
        override val analyticDatabase: AnalyticDatabase = new AnalyticDatabase {
            private val c = fileConf.detach("database.analytics.cassandra")
            override val keyspaceName: String = c[String]("keyspace")
            override val clusterName: String = c[String]("cluster-name")
            override val hostName: String = c[String]("host")
            override val replStrategy: String = c[String]("replication-strategy-class")
            override val replStrategyOpts: String = c[String]("replication-strategy-options")
        }

        info("EWCConfigCore configured")
    }

    override lazy val config = new EWCConfigCore()


    override lazy val idFactory:IdFactory = new SnowFlakeIdFactory(config.machineId)

//    private lazy val _counterProvider = CassandraCounterProvider.setup(config.mainDatabase.hostName,
//        config.mainDatabase.clusterName,
//        config.mainDatabase.keyspaceName,
//        config.mainDatabase.replStrategy,
//        config.mainDatabase.replStrategyOpts)
    override lazy val counterProvider:CounterProvider = CassandraCounterProvider.setup(config.mainDatabase.hostName,
        config.mainDatabase.clusterName,
        config.mainDatabase.keyspaceName,
        config.mainDatabase.replStrategy,
        config.mainDatabase.replStrategyOpts)

    private lazy val _titanDatabase = new TitanDatabase(dbConf, idFactory, true)
    private val dbTls = new ThreadLocal[TitanDatabase]()

    override def database = {
        if (!config.testMode)
            _titanDatabase
        else {
            val _tlsDb = dbTls.get()
            if (_tlsDb == null){

                dbConf.setProperty("storage.directory", "/tmp/digaku-test-db/db_" + System.currentTimeMillis())
                val _tlsDb = new TitanDatabase(dbConf, idFactory, true)
                dbTls.set(_tlsDb)
                _tlsDb
            }else{
                _tlsDb
            }

        }
    }


    override lazy val analytic: Analytic = new Analytic {
        private val c = fileConf.detach("database.analytics.cassandra")

        val cf = new ColumnFamily[String, java.lang.Long](
            "analytic", StringSerializer.get(), LongSerializer.get())

        override val cassandraDriver: CassandraDriver[String, java.lang.Long] = new CassandraDriver[String, java.lang.Long](
            config.analyticDatabase.keyspaceName, cf, config.analyticDatabase.clusterName, config.analyticDatabase.hostName,
            c[String]("replication-strategy-class"), c[String]("replication-strategy-options"),
            parseDiscoType(c("node-discovery-type", "RING_DESCRIBE")),
            parseConnectionType(c("connection-pool-type", "ROUND_ROBIN"))
        )
        override val listener: AsyncEventStreamListener = AnalEventStreamListener

        cassandraDriver.ensureColumnFamilyExists
    }

    private lazy val _searchEngine: DigakuSearchEngine = {
        assert(systemConfig != null, "systemConfig not set, please implement systemConfig first")

        fileConf[String]("search.engine", "embedded-elastic-search") match {
            case "embedded-elastic-search" =>
                val _se = new EmbeddedElasticSearchEngine(fileConf[String]("search.engine.index-dir", "/tmp/digaku-index"))
                // terkadang buat error, jadi jangan dilakukan di sini
//                _se.ensureIndicesCreated()
                _se
            case "distributed-elastic-search" =>
//                val _se = new DistributedElasticSearchEngine(systemConfig.get("search.engine.host", "127.0.0.1:9300"))
                val _se = new DistributedElasticSearchEngine(
                    fileConf[String]("search.engine.host"),
                    fileConf[String]("search.engine.cluster")
                    )
                try {
                    _se.ensureIndicesCreated()
                } catch {
                    case e:NoNodeAvailableException =>
                        warn("No distributed-search-engine node available")
                }
                _se
            case "custom" =>
                // for custom search engine we only set by Nop
                // user custom implementation should be on top layer
                NopSearchEngine
            case "" =>
                NopSearchEngine
            case x =>
                throw NotSupportedException(("Unsupported search engine provider: \"%s\". " +
                    "currently support only \"embedded-elastic-search\"").format(x))
        }
    }

    override def searchEngine = _searchEngine

    override def print(){
        // scalastyle:off regex.line

        println("--:: CONFIGURATION ::--")
        println(config.toString)

        println(s"database: $database")

        def printDbConfig(m:String, _conf:EngineWithConfig.this.config.CassandraDatabase){
            println(m + " engine cassandra: \n" +
                "   cluster-name: " + _conf.clusterName +
                ", host: " + _conf.hostName +
                ", repl strategy: " + _conf.replStrategy +
                ", repl strategy opts: " + _conf.replStrategyOpts +
                ", keyspace: " + _conf.keyspaceName +
                ", machine-id: " + config.machineId) //+
//                ", write-consistency-level: " + conf[String]("database.cassandra.write-consistency-level", "QUORUM") +
//                ", read-consistency-level: " + conf[String]("database.cassandra.read-consistency-level", "QUORUM") +
//                ", discovery-type: " + conf("database.cassandra.node-discovery-type", "NONE"))
        }

        printDbConfig("main", config.mainDatabase)
        printDbConfig("analytic", config.analyticDatabase)

        println("search engine:")
        println("   " + searchEngine)

        println("auth provider:")
        println("   " + authProvider)

        // scalastyle:on regex.line
    }

    override def shutdown(){
        // tidak perlu di-shutdown cluster-nya
        // karena akan mengakibatkan error ketika multiple test
        // sementara solusinya dibiarkan shared dulu
//        commonCassandraCf.cluster.shutdown()
        super.shutdown()
    }
}

