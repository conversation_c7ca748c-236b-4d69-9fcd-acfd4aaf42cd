/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.model._
import com.ansvia.digaku.notifications.{NotificationSendHandler, PersistentNotification}
import com.ansvia.digaku.Types._
import com.ansvia.digaku.Global
import com.ansvia.digaku.helpers.DbAccess

/**
* Author: nadir
* Date: 2/25/13
* Time: 12:49 PM
*
*/
case class EditArticleNotification(userId:Long, articleId:Long)
    extends PersistentNotification(NotifKind.EDIT_ARTICLE) with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

//    private implicit def db:GraphType = Global.database.getRaw

//    type LikableType = Likable[IDType, GraphType]

    val partition = NotifPartition.LEVEL_2_NOTICE

    lazy val article:Option[Article] = {
        val v = db.getVertex(articleId)
        if(v != null)
            v.toCC[Article]
        else
            None
    }

    lazy val editor:Option[User] = {
        val v = db.getVertex(userId)
        if(v != null)
            v.toCC[User]
        else
            None
    }


    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable = false

    def isValid = article.isDefined && editor.isDefined

    override def getDefaultReceivers = {
        Seq(article.get.creator) ++ EditArticleNotification.extendedReceivers(this)
    }

    def userAccept(user: User) = editor.map(_ != user).getOrElse(false)

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    def renderFor(user: User) = {
        assert(isValid, "Notification not valid")

        editor map { eu =>
            if(eu != user){
                article map { art =>
                   if( art.creator == user){
                       "$[user:%s;%s] has edited your $[post:%s;%s;%s]".format(eu.name, eu.getId, art.getId, art.kind, art.origin.getName)
                   }else{
                       "$[user:%s;%s] has edited $[post:%s;%s;%s]".format(eu.name, eu.getId, art.getId, art.kind, art.origin.getName)
                   }
                } getOrElse("---")
            }else "---"
        } getOrElse("---")
    }

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler) = np.name == "edit-article"
}

object EditArticleNotification {
    var extendedReceivers = new ((EditArticleNotification) => Seq[User]){
        def apply(ean: EditArticleNotification) =
            Seq.empty[User]
    }
}

