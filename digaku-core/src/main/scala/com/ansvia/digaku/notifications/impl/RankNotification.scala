package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.model.User
import com.ansvia.digaku.notifications._
import com.ansvia.graph.BlueprintsWrapper._

/**
 * Author: nadir (<EMAIL>)
 *
 */
case class RankNotification(userId: Long, rankName:String) extends PersistentNotification(NotifKind.RANK) with GroupableObjectUserBuilder with EmailSession {
    override val partition: Int =  NotifPartition.LEVEL_1_NOTICE

    lazy val user:Option[User] = {
        val v = db.getVertex(userId)
        if (v != null)
            v.toCC[User]
        else
            None
    }

    /**
     * Untuk pengecheckan apakah user menerima notification ini.
     * @param user user yang akan dicheck.
     * @return
     */
    override def userAccept(user: User): Boolean = true

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    override def isSupport(np: NotificationSendHandler): Boolean = true

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * Return true apabila implementsi bisa di-group-kan.
     * Sebagai contoh lihat [[ResponseNotification]]
     * @return
     */
    override def isGroupable: Boolean = false

    override def isValid: Boolean = user.isDefined

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    override def renderFor(user: User): String = {
        if (isValid) {
            "Selamat, Anda telah mencapai rank <b>%s</b>!".format(rankName)
        } else {
            "----"
        }
    }

    /**
     * get default receivers for this notification.
     */
    override def getDefaultReceivers = {
        user.toSeq
    }

}
