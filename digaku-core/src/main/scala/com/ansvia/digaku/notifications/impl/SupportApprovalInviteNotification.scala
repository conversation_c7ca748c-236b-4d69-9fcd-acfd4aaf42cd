///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications.impl
//
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.event.impl.{NotificationIgnoredEvent, UserInviteSupportApprovalAcceptEvent, UserInviteSupportApprovalRejectEvent}
//import com.ansvia.digaku.exc.{DigakuException, PermissionDeniedException}
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.model.Label._
//import com.ansvia.digaku.model.User
//import com.ansvia.digaku.notifications.{AcceptRejectAttention, NotificationBase, NotificationSendHandler}
//
///**
// * Author: temon, robin
// *
// */
//
///**
// * Notifikasi jika ada pending supporting.
// * Kepada user yang mau di support
// *
// * @param invitorUserId id user yang meng-invite.
// * @param invitedUserId id user yang di-invite.
// * @param invitationCode code invitation yg digenerate see [[com.ansvia.digaku.model.UserBase#inviteToSupportApproval]]
// */
//case class SupportApprovalInviteNotification(invitorUserId:Long, invitedUserId:Long, invitationCode:String)
//    extends NotificationBase(NotifKind.SUPPORT_APPROVAL_INVITE)
//    with AcceptRejectAttention
//    with DbAccess {
//
//    import com.ansvia.digaku.database.GraphCompat._
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    import scala.collection.JavaConversions._
//
//
//    lazy val invitor = {
//        val v = db.getVertex(invitorUserId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val invited = {
//        val v = db.getVertex(invitedUserId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val inv = invitor.flatMap(_.getInvitationByCode(invitationCode))
//
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable = false
//
//    def isValid = invited.isDefined && invitor.isDefined && inv.isDefined
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User) = {
//        if(!isValid) {
//            throw new DigakuException("Notification not valid for this operation.")
//        }
//
//        "You are supported by $[user:%s;%s]".format(
//            invitor.get.name, invitor.get.getId)
//
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = true
//
//    override def getDefaultReceivers =
//        invited.map(u => Seq(u)).getOrElse(Seq.empty[User])
//
//    /**
//     * Whether this notification
//     * support ignore operation.
//     * @return
//     */
//    override def isIgnorable = true
//
//    /**
//     * Ignore operation
//     * called by dispatcher as request
//     * only called when isIgnorable return true.
//     * override this to fit your needs.
//     */
//    override def ignore() {
//        this.reload()
//
//        invited.foreach { user =>
//            if (user.getCounter.get("notif.pending") > 0)
//                user.getCounter.decrement("notif.pending")
//        }
//
//        invitor.foreach { u =>
//
//            tx { t =>
//                t.getVertex(u.getId).toCC[User].foreach { uR =>
//
//                // gunakan gremlin pipe langsung dari sini untuk mutasi edge
//                // tidak gunakan removeInvitation method dari User
//                // agar tidak terjadi nested transaction
//                //
////                    uR.removeInvitation(invitationCode)
//                    uR.getVertex.pipe.outE(INVITE).has("code", invitationCode).remove()
//
//                    Digaku.engine.eventStream.emit(UserInviteSupportApprovalRejectEvent(invited.get, u))
//                    Digaku.engine.eventStream.emit(NotificationIgnoredEvent(this.getId))
//                }
//            }
//
//        }
//        db.commit()
//        super.ignore()
//    }
//
//    def userAccept(user: User) = true
//
//    def accept() {
//
//        this.reload()
//
//        invited.foreach { user =>
//            if (user.getCounter.get("notif.pending") > 0)
//                user.getCounter.decrement("notif.pending")
//        }
//
//        invitor foreach { u =>
//            tx { t =>
//                t.getVertex(u.getId).toCC[User].foreach { uR =>
//                    try {
//                        uR.getInvitationByCode(invitationCode) map { inv =>
//                            val userTarget = invited.get.reload()
//
//                            uR.support(userTarget, force = true)
//
//                            Digaku.engine.eventStream.emit(UserInviteSupportApprovalAcceptEvent(invited.get, uR))
//
//                        } getOrElse(throw PermissionDeniedException("invalid invitation code, maybe aborted"))
//                    } finally {
////                        uR.removeInvitation(invitationCode)
//                        uR.getVertex.pipe.outE(INVITE).has("code", invitationCode).remove()
//
//                        remove()
//                    }
//                }
//            }
//        }
//
//    }
//
//    def reject() {
//        ignore()
//    }
//
//    /**
//     * Check apakah invitation code valid untuk user atau tidak.
//     * @param user user yang akan dicheck.
//     * @return
//     */
//    def isInvitationCodeValidFor(user:User) = {
//        user.getVertex.pipe.outE(INVITE).has("code", invitationCode).iterator().hasNext
//    }
//}
//
///**
// * Notifikasi kepada invitor (user ingin mensupport)
// * jika userA (invited) tidak menyetujui untuk disupport userB
// *
// * @param userAId yang di invite (invited)
// * @param userBId yang menginvite (invitor)
// **/
//case class SupportApprovalRejectNotification(userAId:Long, userBId:Long)
//    extends NotificationBase(NotifKind.SUPPORT_APPROVAL_REJECT) with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val partition = NotifPartition.LEVEL_2_NOTICE
//
//    lazy val userA = {
//        val v = db.getVertex(userAId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val userB = {
//        val v = db.getVertex(userBId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable = false
//
//    def isValid = userA.isDefined && userB.isDefined
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User) = {
//        if (!isValid)
//            throw new DigakuException("Invalid support notification data")
//
//        if (user == userB.get) {
//            "Sorry, $[user:%s;%s] refused to accept your supporting request now".format(userA.get.name, userA.get.getId)
//        } else {
//            "$[user:%s;%s] supporting request has been rejected by $[user:%s;%s]".format(userB.get.name, userB.get.getId, userA.get.name, userA.get.getId)
//        }
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = np.name == "support-approval-reject"
//
//    override def getDefaultReceivers = userB.flatMap(Some[User]).toSeq
//
//    override def hashCode = (userAId + userBId).hashCode()
//
//    def userAccept(user: User) = true
//}
//
///**
// * Notifikasi kepada invitor (user ingin mensupport)
// * jika userA (invited) menyetujui untuk disupport userB
// *
// * @param userAId yang di invite (invited)
// * @param userBId yang menginvite (invitor)
// **/
//case class SupportApprovalAcceptNotification(userAId:Long, userBId:Long)
//    extends NotificationBase(NotifKind.SUPPORT_APPROVAL_ACCEPT) with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val partition = NotifPartition.LEVEL_2_NOTICE
//
//    lazy val userA = {
//        val v = db.getVertex(userAId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val userB = {
//        val v = db.getVertex(userBId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable = false
//
//    def isValid = userA.isDefined && userB.isDefined
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User) = {
//        if (!isValid)
//            throw new DigakuException("Invalid support notification data")
//
//        if (user == userB.get) {
//            "Your supporting request has been accepted by $[user:%s;%s]".format(userA.get.name, userA.get.getId)
//        } else {
//            "$[user:%s;%s] supporting request has been accepted by $[user:%s;%s]".format(userB.get.name, userB.get.getId, userA.get.name, userA.get.getId)
//        }
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = np.name == "support-approval-accept"
//
//    override def getDefaultReceivers = userB.flatMap(Some[User]).toSeq
//
//    override def hashCode = (userAId + userBId).hashCode()
//
//    def userAccept(user: User) = true
//}
