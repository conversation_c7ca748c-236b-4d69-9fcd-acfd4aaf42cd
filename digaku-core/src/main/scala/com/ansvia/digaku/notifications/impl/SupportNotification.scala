/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.model.User
import com.ansvia.digaku.notifications.{NotificationSendHandler, PersistentNotification}
import com.ansvia.graph.annotation.Persistent
import com.ansvia.digaku.Types._
import com.ansvia.digaku.Global
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.helpers.DbAccess

/**
* Author: nadir, robin
*
*/

// @TODO(robin): buat ini nantinya groupable
case class SupportNotification(userAId:Long, userBId:Long)
    extends PersistentNotification(NotifKind.SUPPORT) with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    val partition = NotifPartition.LEVEL_2_NOTICE

    lazy val userA = {
        val v = db.getVertex(userAId)
        if(v != null)
            v.toCC[User]
        else
            None
    }

    lazy val userB = {
        val v = db.getVertex(userBId)
        if(v != null)
            v.toCC[User]
        else
            None
    }

    override val hash: String = "%s(%s-%s)".format(getClass.getSimpleName, userAId, userBId)

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable = false

    def isValid = userA.isDefined && userB.isDefined

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    def renderFor(user: User) = {
        if (!isValid)
            throw new DigakuException("Invalid support notification data")

        if (user == userB.get) {
            "$[user:%s;%s] is now supporting you".format(userA.get.name, userA.get.getId)
        } else {
            "$[user:%s;%s] is now support $[user:%s;%s]".format(userA.get.name, userA.get.getId, userB.get.name, userB.get.getId)
        }
    }

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler) = np.name == "support"

    override def getDefaultReceivers = userB.flatMap(Some[User]).toSeq

    override def hashCode = (userAId + userBId).hashCode()

    def userAccept(user: User) = true
}
