/*
 * Copyright (c) 2013-2017 Ansvia, Inc - All Rights Reserved
 *
 *  This file is part of Digaku project.
 *
 *  Unauthorized copying of this file, via any medium is strictly prohibited
 *  Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.model.User
import com.ansvia.digaku.notifications._
import com.ansvia.graph.BlueprintsWrapper._

/**
 * <PERSON> (<EMAIL>)
 *
 */
case class GlobalAnnouncementNotification(userId: Long) extends PersistentNotification(NotifKind.GLOBAL_ANNOUNCEMENT) with GroupableObjectUserBuilder {
    override val partition: Int =  NotifPartition.LEVEL_1_NOTICE

    override def autoSave = false

    lazy val user:Option[User] = {
        val v = db.getVertex(userId)
        if (v != null)
            v.toCC[User]
        else
            None
    }

    /**
     * Untuk pengecheckan apakah user menerima notification ini.
     * @param user user yang akan dicheck.
     * @return
     */
    override def userAccept(user: User): Boolean = true

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    override def isSupport(np: NotificationSendHandler): Boolean = true

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * Return true apabila implementsi bisa di-group-kan.
     * Sebagai contoh lihat [[ResponseNotification]]
     * @return
     */
    override def isGroupable: Boolean = false

    override def isValid: Boolean = user.isDefined

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    override def renderFor(user: User): String = {
        if (isValid) {
            "<b>Ada announcement baru.</b> Lihat Sekarang!"
        } else {
            "----"
        }
    }

}
