/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types.{GraphType, TransactionalGraphType}
import com.ansvia.digaku.exc.{DigakuException, NotExistsException}
import com.ansvia.digaku.model._
import com.ansvia.digaku.notifications.impl.{LikeNotification, ResponseNotification, SummonNotification, _}
import com.ansvia.digaku.utils.RulesSeq
import com.ansvia.graph.IdGraphTitanDbWrapper._
import com.tinkerpop.blueprints.Edge


///**
// * Author: robin
// *
// * Notification send handler digunakan untuk menyimpan (persist)
// * notification dan meng-link-kan ke user terkait.
// *
// * Di sini juga diset `read trigger event`-nya.
// */
//object PersistentNotificationHandler extends AsyncNotificationSendHandler with Slf4jLogger {
//
//    import com.ansvia.digaku.model.Label._
//
//    case class EdgeMutatorParam(ntf:NotificationBase, edge:Edge)
//    type EdgeMutator = PartialFunction[EdgeMutatorParam,Unit]
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.notifications.NotificationRootVertex"
//    val rootVertexLabel = DAO_LIST
//
//    @volatile private[notifications] var used = false
//    var edgeMutators = new RulesSeq[EdgeMutator] {
//        override protected def safe_?(f: => Any): Unit = {
//            used match {
//                case false => f
//                case _ => throw new IllegalStateException("cannot add edge mutator " +
//                    "when persistent notification handler already used")
//            }
//        }
//    }
//
//    var DELAY = 100
//
//    implicit def ec:ExecutionContext = Digaku.engine.actorSystem.dispatcher
//
//    private lazy val scheduler = Digaku.engine.actorSystem.scheduler
//
//    val name = "db-persistent"
//
//    var extendedHandlers = Array.empty[NotificationSendHandler]
//
//
//    // for testing purposes set it to true
//    // if true then no async performed using blocking style
//    var testMode = false
//
//    def asyncDispatch = {
//        case notif =>
//            debug("got notif: " + notif)
//
//            try {
//                if (testMode) {
//                    PersistentNotificationHandlerDbMutator.save(notif)
//                } else {
//                    scheduler.scheduleOnce(Duration(DELAY, TimeUnit.MILLISECONDS), new Runnable {
//                        def run() {
//                            PersistentNotificationHandlerDbMutator.save(notif)
//                        }
//                    })
//                }
//            }
//            catch {
//                case e:Exception =>
//                    error(e.getMessage)
//                    error(e.getStackTraceString)
//            }
//    }
//
//}

//
//object BlockingPersistentNotificationHandler extends NotificationSendHandler with Slf4jLogger with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    def dispatch: PartialFunction[NotificationBase, Unit] = {
//        case ntf =>
//            if (enabled)
//                save(ntf)
//    }
//
//    val name = "blocking-db-persistent"
//
//    private def save(notif:NotificationBase) = {
//        notif.saveWithLabel(VertexLabels.NOTIFICATION).toCC[NotificationBase] map { ntf =>
//            notif._setId(ntf.getId)
//
//            debug("got: " + notif + ", receivers: " + ntf.getReceivers.toList)
//            for (user <- ntf.getReceivers){
//                if(ntf.userAccept(user) && !user.isInactive){
////                        val edge = db.getVertex(ntf.getVertex.getId) --> NOTIFICATION --> db.getVertex(user.getId) <()
////                        edge.setProperty("read", false)
////                        edge.setProperty("time", -Digaku.engine.dateUtils.nowMilis)
//                    PersistentNotificationHandlerDbMutator.linkNotification(ntf, user, db)
////                        notif.reload()
////                        println("db: " + db)
////                        println(ntf + " ntf.isSaved #1: " + ntf.isSaved)
//                }
//            }
//
//            db.commit()
//        }
//    }
//
//    private var enabled = true
//    def setEnabled(state:Boolean){
//        this.enabled = state
//    }
//
//}

/**
 * Db mutator ini dibuat agar thread safe transaction.
 */
object PersistentNotificationHandlerDbMutator extends Slf4jLogger {


    import com.ansvia.graph.BlueprintsWrapper._

    private lazy val oDb =
            Digaku.engine.database.getRaw[GraphType]


    case class EdgeMutatorParam(ntf:PersistentNotification, edge:Edge)
    type EdgeMutator = PartialFunction[EdgeMutatorParam,Unit]

    var extendedHandlers = Array.empty[NotificationSendHandler]

    @volatile private[notifications] var used = false
    var edgeMutators = new RulesSeq[EdgeMutator] {
        override protected def safe_?(f: => Any): Unit = {
            used match {
                case false => f
                case _ => throw new IllegalStateException("cannot add edge mutator " +
                    "when persistent notification handler already used")
            }
        }
    }


    private lazy val _edgeMutatorsCompiled = {
        edgeMutators.append({case _ =>})
        used = true
        edgeMutators.getRules
            .reduceLeftOption(_ orElse _)
    }

    def save(notif:PersistentNotification) {
//        synchronized {
            saveInternal(notif)
//        }
    }

    def saveInternal(notif:PersistentNotification) {

        val id =
        oDb.transactIdGraph(Digaku.engine.idFactory){ implicit db =>

            val _ntfId = {
                val _ntf = notif.saveWithLabelTx(VertexLabels.NOTIFICATION, db.getBaseGraph)(oDb)
                        .toCC[PersistentNotification].getOrElse {
                    throw new DigakuException("Cannot save notification: " + notif)
                }
                _ntf.getId
            }


            val _ntfV = db.getVertex(_ntfId)
            if (_ntfV == null){
                throw new DigakuException("Cannot get notification after save: " + _ntfId)
            }

            val ntf = _ntfV.toCC[PersistentNotification].getOrElse {
                throw new DigakuException("Cannot serialize notification vertex: " + _ntfV)
            }

            debug("got: " + notif + ", receivers: " + ntf.getReceivers)

            // link-kan ke root vertex
//            val notifV = db.getVertex(ntf.getId)
//            addToRoot(notifV)



            for (user <- ntf.getReceivers){
                val _user = db.getVertex(user.getId).toCC[User].getOrElse {
                    throw new NotExistsException("no user with id " + user.getId)
                }
                if(ntf.userAccept(_user) && !_user.isInactive){
                    linkNotification(ntf, _user, db)
                    truncateNotif(_user)(db)
                }
            }

            _ntfId
        }


        val ntf = oDb.getVertex(id).toCC[PersistentNotification].getOrElse {
            throw new DigakuException("Cannot save notification: " + notif)
        }

        extendedHandlers.foreach(_.dispatch(ntf))

    }


    // @TODO(robin): semua setting untuk set readTriggerEvent dipindah implementasinya ke layer digaku-web-core
    //               menggunakan edge mutator [[com.ansvia.digaku.web.notification.EdgeMutator]]
    /**
     * Menghubungkan notification dengan user.
     * non transactional, harus dieksekusi di dalam scope transaction.
     * @param ntf notifikasi yang akan dihubungkan.
     * @param user user yang akan dihubungkan.
     */
    def linkNotification(ntf:PersistentNotification, user:User, db:TransactionalGraphType){

        import com.ansvia.digaku.model.Label.{NOTIFICATION, SUMMONED}

        val ntfId = ntf.getId

        val userV = user.getVertex //db.getVertex(user.getId)
        val ntfV = ntf.getVertex //db.getVertex(ntfId)

        assert(userV != null, "userV is null")
        assert(ntfV != null, "ntfV is null")

        val edge = userV.addEdge(NOTIFICATION, ntfV)

        edge.setProperty("notifId", ntfId)
        edge.setProperty("_target_userId", user.getId)
        edge.setProperty("read", false)
        edge.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)
        edge.setProperty("notification.partition", ntf.partition)


        if (ntf.isGroupable){
            // tandai kalo ini belum di-group-kan by default
            edge.setProperty("notification.groupMerged", false)
            ntf.regroup(user)
        }

        // Increment edge notification ke user.
        user.getCounter.increment("notification_edge")

        user.getCounter.increment("notif.all")

        /**
         * Setup event based notification mark read invalidator.
         */
        ntf match {
//            case PrivateMessageNotification(_, pmId) =>
//                edge.setProperty("notifKind", NotifGroup.PRIVATE_MESSAGE)
//
//                edge.setProperty("readTriggerEvent", "pm-view")
//                edge.setProperty("readTriggerEventRef", pmId)
//
//                user.getCounter.increment("notif.pm")
//
//            case MessageResponseNotification(_, respId, pmId) =>
//                edge.setProperty("notifKind", NotifGroup.PRIVATE_MESSAGE)
//
//                edge.setProperty("readTriggerEvent", "pm-view")
//                edge.setProperty("readTriggerEventRef", pmId)
//
//                val v = db.getVertex(respId)
//                if (v != null){
//                    v.addEdge(NOTIFICATION, ntfV)
//                }
//
//                user.getCounter.increment("notif.pm")


            case ntf:ResponseNotification => {
                val respId = ntf.responseId

                edge.setProperty("notifKind", NotifGroup.GENERAL_RESPONSE)

                // set trigger event at post-view

                edge.setProperty("readTriggerEvent", "object-view")
                Response.getById(respId).map { resp =>
                    resp.getPostAs.foreach { pa =>
                        edge.setProperty("userGroupRefId", pa.getId)
                    }

                    resp.getRespondedObject.map { respdObject =>
                        edge.setProperty("readTriggerEventRef", respdObject.getId)
                    }
                }

                user.getCounter.increment("notif.generic")


            }

            case ntf:QuoteNotification => {
                val respId = ntf.responseId

                edge.setProperty("notifKind", NotifGroup.QUOTE)

                // set trigger event at post-view

                edge.setProperty("readTriggerEvent", "object-view")
                Response.getById(respId).map { resp =>
                    resp.getRespondedObject.map { respdObject =>
                        edge.setProperty("readTriggerEventRef", respdObject.getId)
                    }
                }

                user.getCounter.increment("notif.generic")

            }

//            case ntf:UserGetExtraPointNotification => {
//                val respId = ntf.responseId
//                edge.setProperty("notifKind", NotifGroup.GET_EXTRA_POINT)
//
//                edge.setProperty("readTriggerEvent", "object-view")
//                Response.getById(respId).map { resp =>
//                    resp.getRespondedObject.map { respdObject =>
//                        edge.setProperty("readTriggerEventRef", respdObject.getId)
//                    }
//                }
//
//                // notif.generic ini digunakan untuk count jenis notif non dialogue dan non whisper
//                // misalnya like, support, user join group, etc.
//                user.getCounter.increment("notif.generic")
//            }

            case AddPostCollaborationNotification(_, _, postId) =>

                edge.setProperty("readTriggerEvent", "object-view")
                edge.setProperty("readTriggerEventRef", postId)

            case EditArticleNotification(_, articleId) =>

                edge.setProperty("notifKind", NotifGroup.EDIT_ARTICLE)

                edge.setProperty("readTriggerEvent", "object-view")
                edge.setProperty("readTriggerEventRef", articleId)

            case LikeNotification(_, objId) =>

                edge.setProperty("notifKind", NotifGroup.LIKE)

                edge.setProperty("readTriggerEvent", "object-view")

                Likable.getById(objId).map {
                    case p: Post =>
                        edge.setProperty("readTriggerEventRef", p.getId)
                    case event: Event =>
                        edge.setProperty("readTriggerEventRef", event.getId)
                    case picture: Picture =>
                        edge.setProperty("readTriggerEventRef", picture.getId)
                    case resp: Response =>
                        resp.getRespondedObject.map {
                            parentObj =>
                                edge.setProperty("readTriggerEventRef", parentObj.getId)
                        }
//                    case pictureGroup:PictureGroup =>
//                        edge.setProperty("readTriggerEventRef", pictureGroup.getId)
                    case _ =>
                }

                user.getCounter.increment("notif.generic")

            case ContentRecommendationNotification(userId, objId) =>
                user.getCounter.increment("notif.generic")

            case ShoutNotification(_, _) =>

                edge.setProperty("notifKind", NotifGroup.SHOUT)

                edge.setProperty("readTriggerEvent", "notification-view")

                user.getCounter.increment("notif.generic")

            case SummonNotification(_, objId) =>

                userV.addEdge(SUMMONED, db.getVertex(objId))

                edge.setProperty("notifKind", NotifGroup.SUMMON)

                edge.setProperty("readTriggerEvent", "object-view")

                // assume objId is response.id
                Response.getById(objId).map { resp =>
                    resp.getRespondedObject.map { respdObject =>
                        edge.setProperty("readTriggerEventRef", respdObject.getId)
                    }
                }.getOrElse {
                    edge.setProperty("readTriggerEventRef", objId)
                }

                user.getCounter.increment("notif.generic")

            case SupportNotification(_, _) =>

                edge.setProperty("notifKind", NotifGroup.SUPPORT)

                // di-trigger ketika user membuka halaman notifikasi (/updates)
                edge.setProperty("readTriggerEvent", "notification-view")

                user.getCounter.increment("notif.generic")

//            case SupportApprovalRejectNotification(_, _) |
//                 SupportApprovalAcceptNotification(_, _) =>
//
//                edge.setProperty("notifKind", NotifGroup.NOTICE)
//
//                // di-trigger ketika user membuka halaman notifikasi (/updates)
//                edge.setProperty("readTriggerEvent", "notification-view")
//
//                user.getCounter.increment("notif.generic")


//            case ChannelInviteNotification(_, _, _, _) |
//                 BecomeStaffInviteNotification(_, _, _, _) |
//                 BecomeOwnerInviteNotification(_, _, _, _)  =>
//
//                edge.setProperty("notifKind", NotifGroup.ATTENTION)
//
//                user.getCounter.increment("notif.pending")
//
//            case ChannelInviteAcceptNotification(_, _, _) |
//                 ChannelInviteRejectNotification(_, _, _) |
//                 BecomeStaffAcceptNotification(_, _, _) |
//                 BecomeStaffRejectNotification(_, _, _) |
//                 BecomeOwnerAcceptNotification(_, _, _) |
//                 BecomeOwnerRejectNotification(_, _, _) =>
//
//                edge.setProperty("notifKind", NotifGroup.NOTICE)
//
//                user.getCounter.increment("notif.generic")

//            case SupportApprovalInviteNotification(_, _, _) =>
//
//                edge.setProperty("notifKind", NotifGroup.ATTENTION)
//
//                user.getCounter.increment("notif.pending")

            case EndorsementNotification(_, _, _) =>
                edge.setProperty("notifKind", NotifGroup.ATTENTION)

                user.getCounter.increment("notif.pending")

            case JoinNotification(userWhoJoin, _) =>

                edge.setProperty("notifKind", NotifGroup.NOTICE)

                // di-trigger ketika user membuka halaman notifikasi (/updates)
                edge.setProperty("readTriggerEvent", "notification-view")

                user.getCounter.increment("notif.generic")

            case GiveReputationNotification(_, _ , _) =>

                edge.setProperty("notifKind", NotifGroup.REPUTATION)

                user.getCounter.increment("notif.generic")

            case UserHasTrophyNotification(_, _) =>
                edge.setProperty("notifKind", NotifGroup.HAS_TROPHY)

                user.getCounter.increment("notif.generic")

            case RankNotification(_, _) =>
                edge.setProperty("notifKind", NotifGroup.RANK)

                user.getCounter.increment("notif.generic")

            case GlobalAnnouncementNotification(_) =>
                edge.setProperty("notifKind", NotifGroup.GLOBAL_ANNOUNCEMENT)

                user.getCounter.increment("notif.generic")

            case ForumAnnouncementNotification(_, _) =>
                edge.setProperty("notifKind", NotifGroup.FORUM_ANNOUNCEMENT)

                user.getCounter.increment("notif.generic")

            case AddStaffNotification(_, _) =>
                edge.setProperty("notifKind", NotifGroup.ADD_STAFF)

                user.getCounter.increment("notif.generic")

            case PromotedThreadNotification(_) =>
                edge.setProperty("notifKind", NotifGroup.PROMOTED_THREAD)

                user.getCounter.increment("notif.generic")

            case _ =>

        }

        _edgeMutatorsCompiled.foreach(_.apply(EdgeMutatorParam(ntf, edge)))


    }

    /**
     * Digunakan untuk truncate notification
     * sehingga satu user hanya akan memiliki 1000
     * notification
     * @param user
     * @param db
     * @return
     */
    def truncateNotif(user:User)(implicit db: TransactionalGraphType): Unit = {
        if (user.getCounter.get("notification_edge") > 1000) {

            user.getVertex.pipe.outE(Label.NOTIFICATION)
                .range(1000, 1100).remove()

            val existingCount = user.getCounter.get("notification_edge")

            // Decrement sampai value 0
            user.getCounter.decrementBy("notification_edge", existingCount)

            // set notification_edge counter menjadi 1000
            user.getCounter.incrementBy("notification_edge", 1000)
        }
    }
}