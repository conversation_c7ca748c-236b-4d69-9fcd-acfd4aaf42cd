package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.Types._
import com.ansvia.digaku.database.GraphCompat._
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.model.{GroupableObject, User}
import com.ansvia.digaku.notifications._
import com.ansvia.graph.BlueprintsWrapper._
import com.tinkerpop.blueprints.Direction
import scala.collection.JavaConversions._

/**
 * Author: nadir (<EMAIL>)
 *
 */
case class GiveReputationNotification(userId:Long, userTargetId:Long, objectId:Long) extends PersistentNotification(NotifKind.REPUTATION)
    with GroupableObjectUserBuilder with EmailSession {
    override val partition: Int = NotifPartition.LEVEL_1_NOTICE

    lazy val user:Option[User] = {
        val v = db.getVertex(userId)
        if (v != null)
            v.toCC[User]
        else
            None
    }

    lazy val userTarget:Option[User] = {
        val v = db.getVertex(userTargetId)
        if (v != null)
            v.toCC[User]
        else
            None
    }

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * Return true apabila implementsi bisa di-group-kan.
     * Sebagai contoh lihat [[ResponseNotification]]
     * @return
     */
    override def isGroupable: Boolean = true

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    override def renderFor(user: User): String = {
        if (isValid) {
            val users = buildGroupableObjectString(user)
            "%s menambahkan reputasi untuk post Anda".format(users)
        } else
            "----"
    }

    /**
     * Jika isGroupable return true
     * maka regroup ini akan dipanggil
     * dan implementor harus meng-override
     * method ini untuk meng-group-kan
     * previous unread notification apabila ada.
     */
    override def regroup(forUser: User) = {

        var giverIds = Seq.empty[Long]

        forUser.getVertex.pipe.outE(NOTIFICATION)
            .has("notifKind", NotifGroup.REPUTATION)
            .has("read", false)
            .hasNot("notification.groupMerged", true).as("ed")
            .inV()
            .hasNot("id", getId)
            .has("objectId", objectId)
            .back("ed")
            .asInstanceOf[GremPipeEdge]
            .iterator().foreach { ed =>

            val ntfV = ed.getVertex(Direction.IN)

            giverIds +:= ntfV.getProperty[IDType]("userId")

            giverIds ++= {
                val ids = ed.getOrElse("groupableObjectIds", "").split(",")
                ids.map(_.trim).filter(_.length > 0).map(_.split(":").toList)
                    .filter(_.apply(0).toLong == forUser.getId)
                    .map(_.apply(1).toLong)
            }

//                ed.setProperty("notification.groupMerged", true)

            // remove Edge karena sudah dimerge ke yang terbaru.
            ed.remove()

            // kurangi count-nya, karena sudah disatukan
            forUser.getCounter.decrement("notif.generic")

        }

        giverIds +:= userId

        val giverIdsStr = giverIds.distinct.map(id => forUser.getId + ":" + id).mkString(",")

        this.getVertex.pipe.inE(NOTIFICATION)
            .has("_target_userId", forUser.getId)
            .hasNot("notification.groupMerged", true)
            .asInstanceOf[GremPipeEdge]
            .iterator().foreach { ed =>

            ed.setProperty("groupableObjectIds", giverIdsStr)
        }
    }

    /**
     * Jika isGroupable return true
     * maka ini harus di-override untuk mendapatkan
     * daftar object yang akan di-group-kan.
     * Ideal-nya ini digunakan di `render()` atau `renderFor(user)`.
     * @return
     */
    override def getGroupableObjects(forUser: User):Seq[GroupableObject] = tx { t =>

        t.getVertex(getId).pipe.inE(NOTIFICATION)
            .has("_target_userId", forUser.getId)
            .hasNot("notification.groupMerged", true)
            .asInstanceOf[GremPipeEdge]
            .headOption.map { ed =>

            val ids = ed.getOrElse("groupableObjectIds", "").split(",")
            val rv = ids.map(_.trim).filter(_.length > 0).map(_.split(":").toList)
                .filter(_.apply(0).toLong == forUser.getId)
                .map(_.apply(1).toLong)
                .flatMap(id => User.getById(id))
                .toSeq

            rv

            //                .split(",").filter(_.length > 0).map(_.toLong).flatMap(User.getById)
            //                .toSeq

        }.getOrElse(Seq.empty[GroupableObject])

    }

    /**
     * Untuk pengecheckan apakah user menerima notification ini.
     * @param user user yang akan dicheck.
     * @return
     */
    override def userAccept(user: User): Boolean = true

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    override def isSupport(np: NotificationSendHandler): Boolean = true

    override def isValid: Boolean = user.isDefined && userTarget.isDefined

    /**
     * get default receivers for this notification.
     */
    override def getDefaultReceivers = {
        userTarget.toSeq
    }

    /**
     * digunakan untuk mendapatkan type notifikasi yang sama pada group notifikasi
     * @return
     */
    override def subGroupId:String = kind + "-" + objectId

}
