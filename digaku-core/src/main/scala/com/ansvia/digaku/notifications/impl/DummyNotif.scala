/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.notifications.{NotificationSendHandler, PersistentNotification}
import com.ansvia.digaku.model.User

/**
 * Author: robin
 *
 * for unit testing purpose only.
 */
case class DummyNotif(text:String)
    extends PersistentNotification(NotifKind.DUMMY){

    val partition = NotifPartition.LEVEL_1_NOTICE

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable = false

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    def renderFor(user: User) = ""

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler) = false

    def isValid = true

    def userAccept(user: User) = true
}
