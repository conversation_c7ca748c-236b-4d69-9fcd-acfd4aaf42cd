///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications.impl
//
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.event.impl.{ChannelInviteStaffAcceptEvent, ChannelInviteStaffRejectEvent, NotificationIgnoredEvent}
//import com.ansvia.digaku.exc.{DigakuException, PermissionDeniedException}
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.model.Label._
//import com.ansvia.digaku.model.{Forum, User}
//import com.ansvia.digaku.notifications.{AcceptRejectAttention, NotificationBase, NotificationSendHandler}
//
///**
//* Author: nadir
//*
//*/
//case class BecomeStaffInviteNotification(invitorUserId:Long, invitedUserId:Long, channelId:Long, invitationCode:String)
//    extends NotificationBase(NotifKind.BECOME_STAFF_INVITE)
//    with AcceptRejectAttention
//    with DbAccess {
//
//    import com.ansvia.digaku.database.GraphCompat._
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    lazy val invitor = {
//        val v = db.getVertex(invitorUserId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val invited = {
//        val v = db.getVertex(invitedUserId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val forumO = {
//        val v = db.getVertex(channelId)
//        if(v != null)
//            v.toCC[Forum]
//        else
//            None
//    }
//
//    lazy val inv = forumO.flatMap(_.getInvitationByCode(invitationCode))
//
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable = false
//
//    def isValid = {
//        forumO.isDefined && invited.isDefined && invitor.isDefined && inv.isDefined
//    }
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User) = {
//        if(!isValid)
//            throw new DigakuException("Notification not valid for this operation.")
//
//
//        val title = inv.get.edge.getOrElse("title", "-")
//
//        val withTitle = {
//            if (title.length > 0)
//                "with title *%s*".format(title)
//            else
//                ""
//        }
//
////        "You are invited to join $[ch:%s;%s], click $[invitation:%s;%s;%s] to join".format(
////            group.get.name, group.get.getId, invCode, group.get.getId, group.get.name)
//        "$[user:%s;%s] has invited you to become staff on Group #$[ch:%s;%s] %s ".format(
//            invitor.get.name, invitor.get.getId, forumO.get.name, forumO.get.getId, withTitle)
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = true
//
//    override def getDefaultReceivers =
//        invited.map(u => Seq(u)).getOrElse(Seq.empty[User])
//
//    /**
//     * Whether this notification
//     * support ignore operation.
//     * @return
//     */
//    override def isIgnorable = true
//
//    /**
//     * Ignore operation
//     * called by dispatcher as request
//     * only called when isIgnorable return true.
//     * override this to fit your needs.
//     */
//    override def ignore() {
//        this.reload()
//
//        invited.foreach { user =>
//            if (user.getCounter.get("notif.pending") > 0)
//                user.getCounter.decrement("notif.pending")
//        }
//
//        forumO.map { ch =>
//
//            tx { t =>
//                t.getVertex(ch.getId).toCC[Forum].foreach { chR =>
//                    // tidak melakukan remove invitation di luar untuk menghindari nested transaction
////                    chR.removeInvitation(invitationCode)
//                    chR.getVertex.pipe.outE(INVITE).has("code", invitationCode).remove()
//
//                    Digaku.engine.eventStream.emit(NotificationIgnoredEvent(this.getId))
//                }
//            }
//
//            Digaku.engine.eventStream.emit(ChannelInviteStaffRejectEvent(invited.get, invitor.get, ch))
//        }
//        db.commit()
//        super.ignore()
//    }
//
//    def userAccept(user: User) = true
//
//    def accept() {
//
//        this.reload()
//
//        invited.foreach { user =>
//            if (user.getCounter.get("notif.pending") > 0)
//                user.getCounter.decrement("notif.pending")
//        }
//
//        forumO map { ch =>
//            ch.reload()
//
//            try{
//                ch.getInvitationByCode(invitationCode) map { inv =>
//                    val title = inv.edge.getOrElse("title", "-")
//                    val abilities = inv.edge.getOrElse("abilities","").split(",")
//                    val u = invited.get.reload()
//                    ch.addMembers(u)
//                    ch.reload()
//                    u.reload()
//                    ch.addStaff(u, title, abilities)
//                    Digaku.engine.eventStream.emit(ChannelInviteStaffAcceptEvent(invited.get, invitor.get, ch))
//                } getOrElse(throw PermissionDeniedException("invalid invitation code, maybe aborted"))
//            } finally {
//                ch.reload()
//                ch.removeInvitation(invitationCode)
//                remove()
//            }
//        }
//
//    }
//
//    def reject() {
//        ignore()
//    }
//}
//
//
//case class BecomeStaffAcceptNotification(userAId:Long, userBId:Long, chId:Long)
//    extends NotificationBase(NotifKind.BECOME_STAFF_ACCEPT) with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val partition = NotifPartition.LEVEL_2_NOTICE
//
//    lazy val invited = {
//        val v = db.getVertex(userAId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val invitor = {
//        val v = db.getVertex(userBId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val ch = {
//        val v = db.getVertex(chId)
//        if(v != null)
//            v.toCC[Forum]
//        else
//            None
//    }
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable = false
//
//    def isValid = invited.isDefined && invitor.isDefined
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User) = {
//        if (!isValid)
//            throw new DigakuException("Invalid support notification data")
//
//        if (user == invitor.get) {
//            "$[user:%s;%s] has accepted your #$[ch:%s;%s] staff request".format(invited.get.name, invited.get.getId, ch.get.getName, ch.get.getId)
//        } else {
//            "$[user:%s;%s] has accepted $[user:%s;%s] #$[ch:%s;%s] staff request".format(invited.get.name, invited.get.getId, invitor.get.name, invitor.get.getId, ch.get.getName, ch.get.getId)
//        }
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = true
//
//    override def getDefaultReceivers = invitor.flatMap(Some[User]).toSeq
//
//    override def hashCode = (userAId + userBId).hashCode()
//
//    def userAccept(user: User) = true
//}
//
//case class BecomeStaffRejectNotification(userAId:Long, userBId:Long, chId:Long)
//    extends NotificationBase(NotifKind.BECOME_STAFF_REJECT) with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val partition = NotifPartition.LEVEL_2_NOTICE
//
//    lazy val invited = {
//        val v = db.getVertex(userAId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val invitor = {
//        val v = db.getVertex(userBId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val ch = {
//        val v = db.getVertex(chId)
//        if(v != null)
//            v.toCC[Forum]
//        else
//            None
//    }
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable = false
//
//    def isValid = invited.isDefined && invitor.isDefined
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User) = {
//        if (!isValid)
//            throw new DigakuException("Invalid support notification data")
//
//        if (user == invitor.get) {
//            "Sorry, $[user:%s;%s] refused to accept your #$[ch:%s;%s] staff request now".format(invited.get.name, invited.get.getId, ch.get.getName, ch.get.getId)
//        } else {
//            "Sorry, $[user:%s;%s] refused to accept $[user:%s;%s] #$[ch:%s;%s] staff request now".format(invited.get.name, invited.get.getId, invitor.get.name, invitor.get.getId, ch.get.getName, ch.get.getId)
//        }
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = true
//
//    override def getDefaultReceivers = invitor.flatMap(Some[User]).toSeq
//
//    override def hashCode = (userAId + userBId).hashCode()
//
//    def userAccept(user: User) = true
//}
