/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications

import java.util.concurrent.{LinkedBlockingQueue, TimeUnit}

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.exc.InvalidParameterException
import com.ansvia.digaku.model.User

import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration

/**
 * Notification sender digunakan untuk mengirim-ngirimkan
 * notification ke user-user tertentu.
 * implementasinya ada di handler-nya.
 */
private[digaku] object NotificationSender extends Thread with Slf4jLogger {

    implicit def ec:ExecutionContext = Digaku.engine.actorSystem.dispatcher

    private var _stop = false
    private var handlers:List[NotificationSendHandler] = Nil
    private val queue = new LinkedBlockingQueue[NotificationBase]()
    private lazy val scheduler = Digaku.engine.actorSystem.scheduler //Global.actorSystem.scheduler
    private var blocking = false
    private var _started = false

    setDaemon(true)

    /**
     * Set menggunakan mode blocking.
     * @param state blocking state.
     */
    def setBlocking(state:Boolean){
        blocking = state
    }


    /**
     * Jalankan thread apabila tidak di blocking mode.
     * Apabila di blocking mode method ini akan
     * memunculkan exception InvalidParameterException.
     */
    override def start() {
        synchronized {
            if (_started)
                return

            if(blocking)
                throw new InvalidParameterException("Cannot start thread while in blocking mode." +
                    " Please set to non blocking mode if needed.")

            _started = true
            super.start()
        }
    }

    /**
     * Send notification to handlers.
     * @param notif notification to send.
     */
    def send(notif: NotificationBase){
        debug("adding to queue: " + notif)

        notif match {
            case persistNotif:PersistentNotification =>

                // save dulu untuk notif yang support autosave

                if (persistNotif.autoSave) {
                    PersistentNotificationHandlerDbMutator.save(persistNotif)
                }

            case _ =>
        }

        // setelah save/peristent baru call handler-handler-nya.

        if (blocking){
            for (nh <- handlers) {
                if ((notif.getFlags | NotifFlags.DONT_PROCESS) != notif.getFlags) {
                    nh.dispatch(notif)
                }
            }
        }else{
            /**
             * Gunakan ini agar tidak terjadi dead-locks
             * apabila ada handler yang menggunakan `transact`.
             */
            queue.offer(notif)
        }
    }

    /**
     * Send notification to specific users instead of
     * from `notification.getReceivers`.
     * this will overwrite `notification.receivers` attribute.
     * @param users list of user (receivers).
     * @param notif notification to send.
     */
    def sendTo(users:Seq[User], notif:PersistentNotification){
        notif.setReceivers(users: _*)

        /**
         * Gunakan ini agar tidak terjadi dead-locks
         * apabila ada handler yang menggunakan `transact`.
         */
        send(notif)
    }

    /**
     * Add handlers.
     * @param nhs handler to add.
     */
    def addHandlers(nhs: NotificationSendHandler*){
        for (nh <- nhs){
            handlers :+= nh
            info("%s registered.".format(nh))
        }

    }

    /**
     * get handlers
     * @return
     */
    def getHandlers = handlers

    /**
     * Reset / clear handlers.
     */
    def clearHandlers(){
        handlers = List.empty[NotificationSendHandler]
    }

    /**
     * Remove handler.
     * @param nh handler to remove.
     */
    def removeHandler(nh: NotificationSendHandler){
        handlers = handlers.filterNot(_ == nh)
    }

    override def run() {
        info("NotificationSender worker started.")
        while (!this._stop){
            val ntf = queue.poll(5, TimeUnit.SECONDS)
            if (ntf != null){
                debug("processing: " + ntf)
                for (nh <- handlers)
                    scheduler.scheduleOnce(Duration(100L, TimeUnit.MILLISECONDS), new Runnable() {
                        def run() {
                            if ((ntf.getFlags | NotifFlags.DONT_PROCESS) != ntf.getFlags) {
                                nh.dispatch(ntf)
                            }
                        }
                    })
            }
        }
    }

    /**
     * Stop this thread by flag.
     * @param state state true = stop, false = ignored.
     */
    def setStop(state:Boolean){
        this._stop = state
        if (this._stop){

            /**
             * Stop all async notif handler
             * worker actors.
             */
            for (h <- handlers){
                h match {
                    case a:AsyncNotificationSendHandler =>
                        a.close()
                    case _ =>
                }
            }

        }
    }
}


