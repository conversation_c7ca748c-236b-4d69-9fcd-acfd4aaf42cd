/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.notifications._
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model._
import scala.collection.JavaConversions._
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.Types._
import com.ansvia.digaku.model.Article
import com.tinkerpop.blueprints.Direction
import com.ansvia.digaku.database.GraphCompat.tx

/**
 * Author: temon
 *
 */

/**
 * implementasi untuk notifikasi shout.
 * @param userId id user yang men-shout.
 * @param objId object id yang di-shout.
 */
case class ShoutNotification (userId:Long, objId:Long) extends PersistentNotification(NotifKind.SHOUT) with DbAccess
    with GroupableObjectUserBuilder  {

    import com.ansvia.graph.BlueprintsWrapper._

    val partition = NotifPartition.LEVEL_3_NOTICE

    lazy val shouter:Option[User] = {
        val v = db.getVertex(userId)
        if (v != null)
            v.toCC[User]
        else
            None
    }

    lazy val obj:Option[Any] = {
        val v = db.getVertex(objId)
        if (v == null)
            None
        else
            v.getOrElse("_class_", "") match {
//                case "com.ansvia.digaku.model.SimplePost" =>
//                    v.toCC[SimplePost]
                case "com.ansvia.digaku.model.Article" =>
                    v.toCC[Article]
                case _ =>
                    None
            }
    }

    lazy val maker:Option[User] = {
        val v = db.getVertex(objId)
        if(v != null) {
            v.pipe.in(PUBLISH_CONTENT).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT).in(COLLECTION_VERTEX).headOption.flatMap(_.toCC[User])
        } else
            None
    }


    override val hash: String = getClass.getSimpleName + "-" + userId + "-" + objId

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable = true


    /**
     * Jika isGroupable return true
     * maka regroup ini akan dipanggil
     * dan implementor harus meng-override
     * method ini untuk meng-group-kan
     * previous unread notification apabila ada.
     */
    override def regroup(forUser: User) = {
        import scala.collection.JavaConversions._
        import com.ansvia.graph.BlueprintsWrapper._

        var shouterIds = Seq.empty[IDType]

        forUser.getVertex.pipe.outE(NOTIFICATION)
            .has("notifKind", NotifGroup.SHOUT)
            .has("read", false)
            .hasNot("notification.groupMerged", true).as("notif_edge")
            .inV()
            .hasNot("id", getId)
            .has("objId", objId)
            .back("notif_edge")
            .asInstanceOf[GremPipeEdge]
            .iterator().foreach { ed =>

            val ntfV = ed.getVertex(Direction.IN)
            shouterIds :+= ntfV.getProperty[IDType]("userId")

            shouterIds ++= ed.getOrElse("groupableObjectIds", "")
                .split(",").filter(_.length > 0).map(_.toLong).toSeq

            // tandai kalo edge notif ini dah di merged dengan yang terbaru
//                ed.setProperty("notification.groupMerged", true)

            // remove Edge karena sudah dimerge ke yang terbaru.
            ed.remove()

            // kurangi count-nya, karena sudah disatukan
            forUser.getCounter.decrement("notif.generic")

        }

        shouterIds :+= userId

        this.getVertex.pipe.inE(NOTIFICATION)
            .has("_target_userId", forUser.getId)
            .hasNot("notification.groupMerged", true)
            .asInstanceOf[GremPipeEdge]
            .iterator().foreach { ed =>

            ed.setProperty("groupableObjectIds", shouterIds.distinct.map(_.toString).mkString(","))

        }

    }


    /**
     * Jika isGroupable return true
     * maka ini harus di-override untuk mendapatkan
     * daftar object yang akan di-group-kan.
     * Ideal-nya ini digunakan di `render()` atau `renderFor(user)`.
     * @return
     */
    override def getGroupableObjects(forUser: User): Seq[User] = tx { t =>
        t.getVertex(getId).pipe.inE(NOTIFICATION)
            .has("_target_userId", forUser.getId)
            .hasNot("notification.groupMerged", true)
            .asInstanceOf[GremPipeEdge]
            .headOption.map { ed =>

            ed.getOrElse("groupableObjectIds", "")
                .split(",").filter(_.length > 0).map(_.toLong).flatMap(id => User.getById(id))
                .toSeq

        }.getOrElse(Seq.empty[User])
    }


    def isValid = shouter.isDefined && maker.isDefined && obj.isDefined

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    def renderFor(user: User) = {
        if (isValid) {
            lazy val userGroupStr = buildGroupableObjectString(user)

            obj.get match {
                case post:Post =>
                    if (user == maker.get){
                        "%s retalked your post: $[post:%s;%s;%s]".format(userGroupStr, post.getId, post.kindStr, post.origin.getName)
                    } else{
                        "%s retalked post: $[post:%s;%s;%s]".format(userGroupStr, post.getId, post.kindStr, post.origin.getName)
                    }

                case event:Event =>
                    if (user == maker.get){
                        "%s shout your event: $[event:%s;%s;%s]".format(userGroupStr, event.getId, event.kindStr, event.origin.getName)
                    } else{
                        "%s shout event: $[event:%s;%s;%s]".format(userGroupStr, event.getId, event.kindStr, event.origin.getName)
                    }

                case picture:Picture =>
                    if (user == maker.get){
                        "%s retalked your picture: $[picture:%s;%s;%s]".format(userGroupStr, picture.getId, picture.kindStr, picture.origin.getName)
                    } else{
                        "%s retalked picture: $[picture:%s;%s;%s]".format(userGroupStr, picture.getId, picture.kindStr, picture.origin.getName)
                    }

//                case picture:PictureGroup =>
//                    if (user == maker.get){
//                        "%s retalked your picture: $[picture-group:%s;%s;%s]".format(userGroupStr, picture.getId, picture.kind, picture.origin.getName)
//                    } else{
//                        "%s retalked picture: $[picture-group:%s;%s;%s]".format(userGroupStr, picture.getId, picture.kind, picture.origin.getName)
//                    }

                case _=> "----"
            }
        } else
            "----"

    }

    override def getDefaultReceivers = {
        if (isValid) {
            if (shouter != maker){
                Seq(maker.get)
            } else {
                Seq.empty[User]
            }
        } else
            Seq.empty[User]
    }

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler) = np.name == "shout"

    def userAccept(user: User) = true

    override def subGroupId:String = kind + "-" + objId

}

