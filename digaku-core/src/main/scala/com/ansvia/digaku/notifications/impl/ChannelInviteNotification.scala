/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */
//
//package com.ansvia.digaku.notifications.impl
//
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.event.impl.{ChannelInviteMemberAcceptEvent, ChannelInviteMemberRejectEvent, NotificationIgnoredEvent}
//import com.ansvia.digaku.exc.{DigakuException, PermissionDeniedException}
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.model.Label._
//import com.ansvia.digaku.model.{Forum, User}
//import com.ansvia.digaku.notifications.{AcceptRejectAttention, NotificationBase, NotificationSendHandler}
//import com.ansvia.digaku.database.GraphCompat._
//import com.ansvia.graph.BlueprintsWrapper._
//
//
///**
//* Author: nadir
//*
//*/
//case class ChannelInviteNotification(invitorUserId:Long, invitedUserId:Long, chId:Long, invitationCode:String)
//    extends NotificationBase(NotifKind.CHANNEL_INVITE) with AcceptRejectAttention with DbAccess {
//
//    lazy val invitor = {
//        val v = db.getVertex(invitorUserId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val invited = {
//        val v = db.getVertex(invitedUserId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val forum = {
//        val v = db.getVertex(chId)
//        if(v != null)
//            v.toCC[Forum]
//        else
//            None
//    }
//
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable = false
//
//    def isValid = forum.isDefined && invited.isDefined
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User) = {
//        if(!isValid)
//            throw new DigakuException("Notification not valid for this operation.")
//
//        "$[user:%s;%s] has invited you to join #$[ch:%s;%s]".format(
//            invitor.get.name, invitor.get.getId, forum.get.name, forum.get.getId)
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = true
//
//    override def getDefaultReceivers =
//        invited.map(u => Seq(u)).getOrElse(Seq.empty[User])
//
//    /**
//     * Whether this notification
//     * support ignore operation.
//     * @return
//     */
//    override def isIgnorable = true
//
//    /**
//     * Ignore operation
//     * called by dispatcher as request
//     * only called when isIgnorable return true.
//     * override this to fit your needs.
//     */
//    override def ignore() {
//        this.reload()
//        forum.foreach { ch =>
//
//            invited.foreach { user =>
//                if (user.getCounter.get("notif.pending") > 0)
//                    user.getCounter.decrement("notif.pending")
//            }
//
//            tx { t =>
//                t.getVertex(ch.getId).toCC[Forum].foreach { chR =>
//                    //                    chR.removeInvitation(invitationCode)
//                    chR.getVertex.pipe.outE(INVITE).has("code", invitationCode).remove()
//
//                    Digaku.engine.eventStream.emit(ChannelInviteMemberRejectEvent(invited.get, invitor.get, ch))
//                    Digaku.engine.eventStream.emit(NotificationIgnoredEvent(this.getId))
//                }
//            }
//        }
//        db.commit()
//        super.ignore()
//    }
//
//    def userAccept(user: User) = true
//
//    def accept() {
//
//        this.reload()
//
//        forum.foreach { ch =>
//            invited.foreach { user =>
//
//                if (user.getCounter.get("notif.pending") > 0)
//                    user.getCounter.decrement("notif.pending")
//
//                try {
//                    ch.getInvitationByCode(invitationCode) map { inv =>
//                        ch.reload()
//                        user.reload()
//
//                        ch.addMembers(user)
//
//                        Digaku.engine.eventStream.emit(ChannelInviteMemberAcceptEvent(user, invitor.get, ch))
//
//                    } getOrElse(throw PermissionDeniedException("invalid invitation code, maybe aborted"))
//                } finally {
//                    ch.reload()
//                    user.reload()
//
//                    // diremove ketika sudah disatukan dengan JoinNotification
//                    // see [[com.ansvia.digaku.notifications.impl.JoinNotification.regroup]]
////                    ch.removeInvitation(invitationCode)
//
//                    remove()
//                }
//            }
//        }
//
//    }
//
//    def reject() {
//        ignore()
//    }
//}
//
//case class ChannelInviteAcceptNotification(userAId:Long, userBId:Long, chId:Long)
//    extends NotificationBase(NotifKind.CHANNEL_INVITE_ACCEPT) with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val partition = NotifPartition.LEVEL_1_NOTICE
//
//    lazy val invited = {
//        val v = db.getVertex(userAId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val invitor = {
//        val v = db.getVertex(userBId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val ch = {
//        val v = db.getVertex(chId)
//        if(v != null)
//            v.toCC[Forum]
//        else
//            None
//    }
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable = false
//
//    def isValid = invited.isDefined && invitor.isDefined
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User) = {
//        if (!isValid)
//            throw new DigakuException("Invalid support notification data")
//
//        if (user == invitor.get) {
//            "$[user:%s;%s] has accepted your invitation to join #$[ch:%s;%s]".format(invited.get.name, invited.get.getId, ch.get.getName, ch.get.getId)
//        } else {
//            "$[user:%s;%s] has accepted $[user:%s;%s]'s invitation to join #$[ch:%s;%s]".format(invited.get.name, invited.get.getId, invitor.get.name, invitor.get.getId, ch.get.getName, ch.get.getId)
//        }
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = true
//
//    override def getDefaultReceivers = invitor.flatMap(Some[User]).toSeq
//
//    override def hashCode = (userAId + userBId).hashCode()
//
//    def userAccept(user: User) = true
//
////    override def subForumId:String = kind + "-" + chId
//}
//
//case class ChannelInviteRejectNotification(userAId:Long, userBId:Long, chId:Long)
//    extends NotificationBase(NotifKind.CHANNEL_INVITE_REJECT) with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val partition = NotifPartition.LEVEL_1_NOTICE
//
//    lazy val invited = {
//        val v = db.getVertex(userAId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val invitor = {
//        val v = db.getVertex(userBId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val ch = {
//        val v = db.getVertex(chId)
//        if(v != null)
//            v.toCC[Forum]
//        else
//            None
//    }
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable = false
//
//    def isValid = invited.isDefined && invitor.isDefined
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User) = {
//        if (!isValid)
//            throw new DigakuException("Invalid support notification data")
//
//        if (user == invitor.get) {
//            "Sorry, $[user:%s;%s] refused to accept your invitation to join #$[ch:%s;%s] now".format(invited.get.name, invited.get.getId, ch.get.getName, ch.get.getId)
//        } else {
//            "Sorry, $[user:%s;%s] refused to accept $[user:%s;%s]'s invitation to join #$[ch:%s;%s] now".format(invited.get.name, invited.get.getId, invitor.get.name, invitor.get.getId, ch.get.getName, ch.get.getId)
//        }
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = true
//
//    override def getDefaultReceivers = invitor.flatMap(Some[User]).toSeq
//
//    override def hashCode = (userAId + userBId).hashCode()
//
//    def userAccept(user: User) = true
//}
