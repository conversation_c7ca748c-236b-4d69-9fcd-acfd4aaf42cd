/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications

import com.ansvia.digaku.model._
import com.ansvia.graph.BlueprintsWrapper._
import com.tinkerpop.blueprints.{Direction, Edge}

/**
 * Author: robin (<EMAIL>)
 */
object NotifUtil {

    var customRecalcNotifCounter = (cls:String, user:User, edge:Edge) => ()

    /**
     * Used to recalculate notification counter per user.
     * non transactional, should be called inside of transaction.
     * @param user user to recalculate the notification count.
     * @param edge notification edge link user to notification.
     */
    def recalcNotifCounter(user:User, edge:Edge) = {
        val v = edge.getVertex(Direction.IN) // IN = Notif
        if (v != null){
            v.getOrElse("_class_", "") match {
                case "com.ansvia.digaku.notifications.impl.LikeNotification" |
                     "com.ansvia.digaku.notifications.impl.SupportNotification" |
                     "com.ansvia.digaku.notifications.impl.JoinNotification" |
                     "com.ansvia.digaku.notifications.impl.UserHasTrophyNotification" |
                     "com.ansvia.digaku.notifications.impl.RankNotification" |
                     "com.ansvia.digaku.notifications.impl.GiveReputationNotification" |
                     "com.ansvia.digaku.notifications.impl.AddStaffNotification" |
                     "com.ansvia.digaku.notifications.impl.PromotedThreadNotification" |
                     "com.ansvia.digaku.notifications.impl.ShoutNotification" |
                     "com.ansvia.digaku.notifications.impl.ContentRecommendationNotification" |
                     "com.ansvia.digaku.notifications.impl.ResponseNotification" |
                     "com.ansvia.digaku.notifications.impl.SummonNotification" |
                     "com.ansvia.digaku.notifications.impl.QuoteNotification" |
                     "com.ansvia.digaku.notifications.impl.GlobalAnnouncementNotification" |
                     "com.ansvia.digaku.notifications.impl.ForumAnnouncementNotification" =>

                    if (user.getCounter.get("notif.generic") > 0)
                        user.getCounter.decrement("notif.generic")
                    if (user.getCounter.get("notif.all") > 0)
                        user.getCounter.decrement("notif.all")

//                case "com.ansvia.digaku.notifications.impl.ResponseNotification" |
//                     "com.ansvia.digaku.notifications.impl.SummonNotification" =>
//
//                    if (user.getCounter.get("notif.dialogue") > 0)
//                        user.getCounter.decrement("notif.dialogue")
//                    if (user.getCounter.get("notif.all") > 0)
//                        user.getCounter.decrement("notif.all")

                case "com.ansvia.digaku.notifications.impl.PrivateMessageNotification" |
                     "com.ansvia.digaku.notifications.impl.MessageResponseNotification" =>

                    if (user.getCounter.get("notif.pm") > 0)
                        user.getCounter.decrement("notif.pm")
                    if (user.getCounter.get("notif.all") > 0)
                        user.getCounter.decrement("notif.all")

                case "com.ansvia.digaku.notifications.impl.ChannelInviteNotification" |
                     "com.ansvia.digaku.notifications.impl.BecomeStaffInviteNotification" |
                     "com.ansvia.digaku.notifications.impl.BecomeOwnerInviteNotification" |
                     "com.ansvia.digaku.notifications.impl.SupportApprovalInviteNotification" =>

                    if (user.getCounter.get("notif.pending") > 0)
                        user.getCounter.decrement("notif.pending")
                    if (user.getCounter.get("notif.all") > 0)
                        user.getCounter.decrement("notif.all")

                case x => customRecalcNotifCounter.apply(x, user, edge)
            }

        }
    }

    /**
     * jadikan user model ke standard string notif
     * @param user
     * @return
     */
    def stdUserNotifStr(user:User):String = {
        "$[user:%s;%s]".format(user.getName.replace(";", ""), user.getId)
    }

    /**
     * jadikan user model ke standard string notif
     * @param user
     * @return
     */
    def stdUserGroupNotifStr(userGroup:UserGroup):String = {
        "$[ug:%s;%s]".format(userGroup.name.replace(";", ""), userGroup.getId)
    }

    /**
     * jadikan user model ke standard string notif
     * @param post
     * @return
     */
    def stdPostNotifStr(post:Post):String = {
        "$[post:%s;%s;%s]".format(post.getId, post.origin.kind, post.origin.getName)
    }

    /**
     * jadikan response model ke standard string notif
     * @param resp
     * @return
     */
    def stdResponseNotifStr(resp:Response):String = {
        "$[response:%s;%s;%s]".format(resp.getId, resp.origin.kind, resp.origin.getName)
    }

    /**
     * jadikan forum model ke standard string notif
     * @param forum
     * @return
     */
    def stdForumNotifStr(forum: Forum):String = {
        "$[ch:%s;%s]".format(forum.name, forum.getId)
    }

}
