/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.notifications.{Attention, NotificationSendHandler, PersistentNotification}
import com.ansvia.digaku.model.User

/**
 * Author: robin
 * Date: 3/28/14
 * Time: 7:36 PM
 *
 */

// hanya digunakan untuk testing aja, lihat NotificationBaseSpec
class TestNotif() extends PersistentNotification(NotifKind.TEST) {
    val partition: Int = NotifPartition.ANY

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * Return true apabila implementsi bisa di-group-kan.
     * Sebagai contoh lihat [[com.ansvia.digaku.notifications.impl.ResponseNotification]]
     * @return
     */
    def isGroupable: Boolean = false

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    def renderFor(user: User): String = "hello test " + user.name

    /**
     * Untuk pengecheckan apakah user menerima notification ini.
     * @param user user yang akan dicheck.
     * @return
     */
    def userAccept(user: User): Boolean = true

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler): Boolean = true

    def isValid: Boolean = true
}

case class TestNotifAttention() extends TestNotif with Attention
