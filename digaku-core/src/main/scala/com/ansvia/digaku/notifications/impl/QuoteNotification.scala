/*
 * Copyright (c) 2013-2017 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.model.{ResponseQuotes, Response, User}
import com.ansvia.digaku.notifications.NotifUtil._
import com.ansvia.digaku.notifications._
import com.ansvia.graph.BlueprintsWrapper._
import ResponseQuotes._

/**
 * Author: nadir (<EMAIL>)
 *
 */
case class QuoteNotification(userId: Long, responseId:Long) extends PersistentNotification(NotifKind.QUOTE) with GroupableObjectUserBuilder {
    override val partition: Int =  NotifPartition.CONVERSATION

    lazy val user:Option[User] = {
        val v = db.getVertex(userId)
        if (v != null)
            v.toCC[User]
        else
            None
    }

    lazy val response:Option[Response] = {
        val v = db.getVertex(responseId)
        if (v != null)
            v.toCC[Response]
        else
            None
    }

    /**
     * Untuk pengecheckan apakah user menerima notification ini.
     * @param user user yang akan dicheck.
     * @return
     */
    override def userAccept(user: User): Boolean = true

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    override def isSupport(np: NotificationSendHandler): Boolean = true

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * Return true apabila implementsi bisa di-group-kan.
     * Sebagai contoh lihat [[ResponseNotification]]
     * @return
     */
    override def isGroupable: Boolean = false

    override def isValid: Boolean = user.isDefined

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    override def renderFor(user: User): String = {
        if (isValid) {
            "%s mengutip balasan anda.".format(stdUserNotifStr(this.user.get))
        } else {
            "----"
        }
    }

    /**
     * get default receivers for this notification.
     */
    override def getDefaultReceivers = {
        response.map { resp =>
            resp.getQuotedResponse().map { quoted =>
                quoted.creator
            }.filterNot(_.getId == userId)
        }.getOrElse(List.empty[User])
    }

}
