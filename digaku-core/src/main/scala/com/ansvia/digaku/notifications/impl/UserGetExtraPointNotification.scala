///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications.impl
//
//import com.ansvia.digaku.notifications.{NotificationSendHandler, GroupableObjectUserBuilder, PersistentNotification}
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.model._
//import com.ansvia.digaku.notifications.{NotificationBase, NotificationSendHandler}
//
///**
// * Author: ubai
// *
// */
//case class UserGetExtraPointNotification(userId:Long, responseId:Long)
//    extends PersistentNotification(NotifKind.USER_GET_EXTRA_POINT) with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val partition: Int = NotifPartition.LEVEL_1_NOTICE
//
//    lazy val response:Option[Response] = {
//        val v = db.getVertex(responseId)
//        if (v != null)
//            v.toCC[Response]
//        else
//            None
//    }
//    lazy val respondedObject = response.map(_.getRespondedObject).getOrElse(None)
//
//    lazy val user:Option[User] = {
//        val v = db.getVertex(userId)
//        if (v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * Return true apabila implementsi bisa di-group-kan.
//     * Sebagai contoh lihat [[com.ansvia.digaku.notifications.impl.ResponseNotification]]
//     * @return
//     */
//    def isGroupable: Boolean = false
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User): String = {
//        if (isValid) {
//            respondedObject.get match {
//                case post:Post =>
//                    "+1 point from your $[response:%s;%s;%s] on this $[post:%s;%s;%s]"
//                        .format(response.get.getId, post.kind, post.origin.getName, post.getId, post.kind, post.origin.getName)
//                case event:Event =>
//                    "+1 point from your $[response:%s;%s;%s] on this $[event:%s;%s;%s]"
//                        .format(response.get.getId, event.kind, event.origin.getName, event.getId, event.kind, event.origin.getName)
//                case picture:Picture =>
//                    "+1 point from your $[response:%s;%s;%s] on this $[picture:%s;%s;%s]"
//                        .format(response.get.getId, picture.kind, picture.origin.getName, picture.getId, picture.kind, picture.origin.getName)
////                case picture:PictureGroup =>
////                    "+1 point from your $[response:%s;%s;%s] on this $[picture-group:%s;%s;%s]"
////                        .format(response.get.getId, picture.kind, picture.origin.getName, picture.getId, picture.kind, picture.origin.getName)
//                case _ =>
//                    "-----"
//            }
//        } else {
//            ""
//        }
//    }
//
//    def userAccept(user: User): Boolean = true
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler): Boolean = np.name == "response"
//
//    def isValid: Boolean = response.isDefined && respondedObject.isDefined && user.isDefined
//
//    /**
//     * get default receivers for this notification.
//     */
//    override def getDefaultReceivers = {
//        if (isValid)
//            user.toSeq
//        else
//            Seq.empty[User]
//    }
//}
