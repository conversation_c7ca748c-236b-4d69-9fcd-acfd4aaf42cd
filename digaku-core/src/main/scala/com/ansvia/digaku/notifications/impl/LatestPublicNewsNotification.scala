/*
 * Copyright (c) 2013-2017 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.notifications.NewsNotifications

/**
 * Author: henky (<EMAIL>)
 */

case class LatestPublicNewsNotification(articleId: Long)
    extends NewsNotifications(articleId, NotifKind.GLOBAL_NEWS) {

    override val hash: String = getClass.getSimpleName + "-" +articleId
}
