///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.notifications.impl
//
//import com.ansvia.digaku.notifications.{EmailSession, NotificationSendHandler, NotificationBase}
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.model.{PrivateMessage, User}
//import com.tinkerpop.blueprints.Vertex
//
///**
// * Author: nadir
// *
// */
//case class PrivateMessageNotification(creatorId:Long, privateMessageId:Long)
//    extends NotificationBase(NotifKind.PM) with DbAccess with EmailSession {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val partition = NotifPartition.CONVERSATION
//
//    lazy val privateMessage:Option[PrivateMessage] = {
//        val v = db.getVertex(privateMessageId)
//        if (v != null){
//            v.toCC[PrivateMessage]
//        } else
//            None
//    }
//
//    lazy val creator:Option[User] = {
//        val v = db.getVertex(creatorId)
//        if (v != null){
//            v.toCC[User]
//        } else
//            None
//    }
//
////    private def pronounObject(user:User) = {
////        user.sex match {
////            case SexType.MALE => " his "
////            case SexType.FEMALE => " her "
////            case _ => " "
////        }
////    }
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable: Boolean = false
//
//    /**
//     * Render notification untuk user :user.
//     * @param tUser user yang akan membaca.
//     * @return
//     */
//    def renderFor(tUser: User): String = {
//        if (isValid){
//            creator.map{ user =>
//                "You have new message $[private-message:%s] from $[user:%s;%s]".format(privateMessage.get.getId, user.getName, user.getId)
//            }.getOrElse("---")
//        }else
//            "---"
//    }
//
//    def userAccept(user: User): Boolean = true
//
//    override def getDefaultReceivers = {
//        if (isValid){
//            privateMessage.map{ pM =>
//                pM.reload().getParticipants.distinct.filter(user => Some(user)!=creator)
//            }.getOrElse(Seq.empty[User])
//        } else
//            Seq.empty[User]
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = np.name == "private-message"
//
//    def isValid: Boolean = {
//        val a = creatorId > 0 && privateMessageId > 0 && creator.isDefined && privateMessage.isDefined
//        a
//    }
//
//    override def __save__(v: Vertex){
//
//        // set pm.id untuk meng-group-kan antara PrivateMessageNotification dan MessageResponseNotification
//        // see [[com.ansvia.digaku.model.UserBase.getMessageNotifications]]
//        v.setProperty("pm.id", privateMessageId)
//        super.__save__(v)
//        generateEmailSession()
//    }
//}
