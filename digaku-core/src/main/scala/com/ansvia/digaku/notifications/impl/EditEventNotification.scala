/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.exc.NotExistsException
import com.ansvia.digaku.notifications.{NotificationSendHandler, NotificationBase}
import com.ansvia.digaku.notifications.{NotificationSendHandler, PersistentNotification}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.{Article, AttenderKind, Event, User}

/**
 * Author: nadir
 *
 */
case class EditEventNotification(userId:Long, edited:String, editReason:String, eventId:Long)
    extends PersistentNotification(NotifKind.EDIT_EVENT) with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    val partition = NotifPartition.LEVEL_2_NOTICE

    lazy val event:Option[Event] = {
        val v = db.getVertex(eventId)
        if(v != null)
            v.toCC[Event]
        else
            None
    }

    lazy val editor:Option[User] = {
        val v = db.getVertex(userId)
        if(v != null)
            v.toCC[User]
        else
            None
    }

    override def getDefaultReceivers = {
        if (isValid)
            event.get.getAttenders(AttenderKind.ALL).filter(_ != editor.get)
        else
            Seq.empty[User]
    }

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable: Boolean = false

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    def renderFor(user: User): String = {
        if (isValid){

            "$[user:%s;%s] just updated %s on event $[event:%s;%s;%s]".format(editor.get.name, editor.get.getId , edited,
                event.get.getId, event.get.kind, event.get.origin.getName)

//            val article = event.get.getEmbedParentObject[Article].getOrElse {
//                throw NotExistsException("Article not exists")
//            }
//
//            if (user == article.creator)
//                "$[user:%s;%s] just updated %s on your event $[event:%s;%s;%s]".format(editor.get.name, editor.get.getId , edited,
//                    event.get.getId, event.get.kind, event.get.origin.getName)
//            else
//                "$[user:%s;%s] just updated %s on event $[event:%s;%s;%s]".format(editor.get.name, editor.get.getId , edited,
//                    event.get.getId, event.get.kind, event.get.origin.getName)
        } else
            "----"
    }

    def userAccept(user: User): Boolean = true

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler)= np.name == "edit-event"

    def isValid: Boolean = event.isDefined && editor.isDefined && (!edited.trim.isEmpty)
}
