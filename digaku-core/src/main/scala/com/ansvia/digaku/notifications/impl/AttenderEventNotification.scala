/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.exc.NotExistsException
import com.ansvia.digaku.notifications.{NotificationSendHandler, NotificationBase}
import com.ansvia.digaku.notifications.{NotificationSendHandler, PersistentNotification}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.{Article, AttenderKind, Event, User}

/**
 * Author: nadir
 *
 */
case class AttenderEventNotification(eventId:Long, attenderId:Long, attenderKind:Int)
    extends PersistentNotification(NotifKind.ATTENDER_EVENT) with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._


    val partition = NotifPartition.LEVEL_2_NOTICE

    /**
     * the event.
     */
    lazy val event:Option[Event] = {
        val v = db.getVertex(eventId)
        if (v != null)
            v.toCC[Event]
        else
            None
    }

    /**
     * user who attend the event.
     */
    lazy val attender:Option[User] = {
        val v = db.getVertex(attenderId)
        if (v != null)
            v.toCC[User]
        else
            None
    }

    lazy val renderKind:String = {
        if (attenderKind == AttenderKind.POSITIVE)
            "will attend"
        else
            "may be attending"
    }

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable: Boolean = false

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    def renderFor(user: User): String = {
        if (isValid){
//            if (user == event.get.creator){
//                "$[user:%s;%s] ".format(attender.get.getName, attender.get.getId) +
//                    renderKind + " your event $[event:%s;%s;%s]".format(event.get.getId, event.get.kind, event.get.origin.getName)
//            } else
//                "$[user:%s;%s] ".format(attender.get.getName, attender.get.getId) +
//                    renderKind + " the event $[event:%s;%s;%s]".format(event.get.getId, event.get.kind, event.get.origin.getName)
            "$[user:%s;%s] ".format(attender.get.getName, attender.get.getId) +
                renderKind + " the event $[event:%s;%s;%s]".format(event.get.getId, event.get.kind, event.get.origin.getName)
        } else
            "----"
    }

    def userAccept(user: User): Boolean = true

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler) = np.name == "attender-event"

    def isValid: Boolean = event.isDefined && attender.isDefined

    override def getDefaultReceivers = {
        if (isValid){
//            if (event.get.creator != attender.get){
//                Seq(event.get.creator)
//            } else {
//                Seq.empty[User]
//            }
            Seq.empty[User]
        } else
            Seq.empty[User]
    }
}
