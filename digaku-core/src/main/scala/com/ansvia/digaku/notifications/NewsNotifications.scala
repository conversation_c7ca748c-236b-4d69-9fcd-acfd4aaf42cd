/*
 * Copyright (c) 2013-2017 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications

import com.ansvia.digaku.Types._
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.{Article, Forum, User}
import com.ansvia.digaku.notifications.impl.NotifPartition

/**
 * Author: henky (<EMAIL>)
 */

abstract class NewsNotifications (articleId: Long, _kind:Int) extends NonPersistentNotification(_kind) with DbAccess {
    import com.ansvia.graph.BlueprintsWrapper._

    override def getId:IDType = 0L
    val partition = NotifPartition.LEVEL_2_NOTICE
    def getArticleId = articleId

    lazy val article:Option[Article] = {
        val v = db.getVertex(articleId)
        if(v != null)
            v.toCC[Article]
        else
            None
    }

    def isGroupable = false

    def isValid =  articleId > 0  && article.isDefined

    def renderFor(tUser: User) = {
        article.get.getContent
    }

    override def getDefaultReceivers = Seq.empty[User]

    def userAccept(user: User) = true
}
