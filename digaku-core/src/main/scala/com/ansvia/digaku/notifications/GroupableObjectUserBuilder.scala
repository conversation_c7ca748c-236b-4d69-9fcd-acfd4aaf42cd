/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications

import com.ansvia.digaku.model.{GroupableObject, User, UserGroup}
import NotifUtil._

/**
 * Author: robin
 * Date: 11/18/13
 * Time: 3:35 PM
 *
 * Untuk mempermudah group notification, contoh [[com.ansvia.digaku.notifications.impl.ResponseNotification]]
 * dan [[com.ansvia.digaku.notifications.impl.LikeNotification]]
 */
trait GroupableObjectUserBuilder {

    def getGroupableObjects(forUser: User): Seq[GroupableObject]

    private def stdNotifStr(groupableObject: GroupableObject):String = {
        groupableObject match {
            case u:User =>
                stdUserNotifStr(u)
            case userGroup: UserGroup =>
                stdUserGroupNotifStr(userGroup)
            case _ =>
                "--"
        }
    }

    /**
     * Digunakan untuk build groupable object
     * dalam hal ini string untuk user yang terlibat dalam group.
     * @param groupableObjects
     */
    protected def buildGroupableObjectString(forUser:User, groupableObjects:Seq[GroupableObject]): String = {
        var tail:GroupableObject = null
        var gos = groupableObjects

        if (gos.length > 1){
            tail = gos.tail.reverse.head
            gos = gos.slice(0,gos.length-1)
        }

        var users = gos.map(stdNotifStr)
            .reduceLeftOption(_ + ", " + _).getOrElse("peoples")

        if (tail != null){
            var comma = ""
            if (gos.length > 1)
                comma = ","

            if (gos.length < 3) {
                users = users + comma + " dan " + stdNotifStr(tail)
            } else if (gos.length > 3) {
                users = gos.take(2).map(stdNotifStr).reduceLeftOption(_ + ", " + _)
                    .getOrElse("peoples") + " dan " + "%d orang lainnya".format(gos.length - 1)
            }
        }

        users
    }

    def buildGroupableObjectString(forUser:User): String = {
        val gos: Seq[GroupableObject] = getGroupableObjects(forUser)
        buildGroupableObjectString(forUser, gos)
    }

}
