/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

/**
 * Author: robin
 *
 */

/**
 * Notification partition menggunakan bitwise style.
 */
object NotifPartition {

    val ANY = 0

    /**
     * jenis notifikasi yang tidak terlalu penting
     * seperti likes.
     */
    val LEVEL_1_NOTICE = 1

    /**
     * jenis notifikasi yang lumayan penting
     * seperti shout, support, dan edit.
     */
    val LEVEL_2_NOTICE = 2

    /**
     * jenis notifikasi yang penting seperti attention
     * pending-pending notif yang butuh user action.
     */
    val LEVEL_3_NOTICE = 4 // reserved

    val LEVEL_4_NOTICE = 8 // reserved

    /**
     * Semua notice level dari level 1-4
     * WARNING: Tidak digunakan untuk write, hanya untuk query saja.
     */
    val ALL_NOTICE_LEVEL = LEVEL_1_NOTICE | LEVEL_2_NOTICE | LEVEL_3_NOTICE | LEVEL_4_NOTICE

    /**
     * CONVERSATION a.k.a dialogue
     * untuk notification yang bersifat obrolan
     * seperti respon dan reply message, tidak termasuk
     * likes, shout, edit, dll.
     */
    val CONVERSATION = 32
}
