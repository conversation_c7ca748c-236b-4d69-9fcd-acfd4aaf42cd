/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications

import java.util.Date

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.database.GraphCompat.tx
import com.ansvia.digaku.exc.NotImplementedException
import com.ansvia.digaku.model.{BaseModel, Deletable, GroupableObject, User}
import com.ansvia.digaku.notifications.impl._
import com.ansvia.digaku.utils.RulesSeq
import com.ansvia.graph.BlueprintsWrapper.DbObject
import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.Vertex
import org.ocpsoft.prettytime.PrettyTime


/**
* Author: nadir
*
*/
abstract class NotificationBase(_kind: Int) {

    def getId: IDType

    def getKind = _kind
    def getDefaultReceivers: Seq[User]

    def getCreationTime:Long

    def renderFor(user: User): String

    def isValid: Boolean

    /**
     * see [[NotifFlags]]
     * @return
     */
    def getFlags: Int
}


abstract class NonPersistentNotification(_kind: Int) extends NotificationBase(_kind) {

    private val creationTime = Digaku.engine.dateUtils.nowMilis

    def getCreationTime = creationTime

    /**
     * see [[com.ansvia.digaku.notifications.NotifFlags]]
     * @return
     */
    override def getFlags: Int = NotifFlags.NONE

    /**
     * hash ini digunakan untuk menandai keunikan dari notification ini
     * berguna untuk mendeteksi duplicated notification dan mempermudah throttling
     * sehingga tidak disalahgunakan oleh orang-orang aneh untuk mem-flood notification
     * sebagai contoh user yang mengklik tombol support/unsupport berkali-kali harusnya
     * gak menyebabkan flooding notification SupportNotification ke user yang disupport.
     * biasanya hash ini dibentuk dari kombinasi parameter-parameter yang diperlukan untuk
     * notifikasi.
     *
     * Apabila tidak diset atau dibiarkan kosong ("") maka operasi keunikan notification diabaikan.
     *
     * Contoh implementasi lihat di [[com.ansvia.digaku.notifications.impl.SupportNotification.hash]]
     */
    val hash: String
}


/**
 * Base notification, semua jenis notification diturunkan dari sini.
 * @param _kind see [[com.ansvia.digaku.notifications.impl.NotifKind]]
 */
abstract class PersistentNotification(_kind: Int) extends NotificationBase(_kind) with DbObject with BaseModel[IDType] with Deletable {

    import com.ansvia.digaku.model.Label.NOTIFICATION
    import com.ansvia.graph.BlueprintsWrapper._
    import scala.collection.JavaConversions._

    private var receivers: Array[User] = Array.empty[User]


    /**
     * @see [[com.ansvia.digaku.notifications.impl.NotifKind]]
     *     bukan [[NotifGroup]]
     */
    @Persistent var kind = _kind

    /**
     * hash ini digunakan untuk menandai keunikan dari notification ini
     * berguna untuk mendeteksi duplicated notification dan mempermudah throttling
     * sehingga tidak disalahgunakan oleh orang-orang aneh untuk mem-flood notification
     * sebagai contoh user yang mengklik tombol support/unsupport berkali-kali harusnya
     * gak menyebabkan flooding notification SupportNotification ke user yang disupport.
     * biasanya hash ini dibentuk dari kombinasi parameter-parameter yang diperlukan untuk
     * notifikasi.
     *
     * Apabila tidak diset atau dibiarkan kosong ("") maka operasi keunikan notification diabaikan.
     *
     * Contoh implementasi lihat di [[com.ansvia.digaku.notifications.impl.SupportNotification.hash]]
     */
    val hash: String = ""


    /**
     * partisi notifikasi,
     * by default menggunakan level ANY.
     * @see [[com.ansvia.digaku.notifications.impl.NotifPartition]]
     */
    val partition: Int // = NotifPartition.ANY

    /**
     * Untuk memeriksa apakah notification ini sudah di-baca
     * oleh user belum.
     * @param user user target check.
     * @return
     */
    def isRead(user: User): Boolean = tx { t =>
        t.getVertex(this.getId).pipe.inE(NOTIFICATION)
            .has("_target_userId", user.getId)
            .asInstanceOf[GremPipeEdge]
            .headOption.flatMap(_.get("read")).getOrElse(true)
    }

    /**
     * Digunakan untuk optimal performance global query.
     */
    @Persistent var notification = true

    /**
     * Non persistent variable.
     *
     * Hanya digunakan untuk menandai notifikasi ini untuk dikenali oleh notif handler-handler
     * lainnya, misalnya untuk memberitahukan bahwa notif ini jangan diproses setelahnya.
     *
     * lihat [[NotifFlags]]
     */
    var flags:Int = 0

    def getFlags = flags


    override def getCreationTime: Long = creationTime

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * Return true apabila implementsi bisa di-group-kan.
     * Sebagai contoh lihat [[com.ansvia.digaku.notifications.impl.ResponseNotification]]
     * @return
     */
    def isGroupable: Boolean

    /**
     * Jika isGroupable return true
     * maka regroup ini akan dipanggil
     * dan implementor harus meng-override
     * method ini untuk meng-group-kan
     * previous unread notification apabila ada.
     */
    def regroup(forUser: User) {
        throw NotImplementedException("Not implemented, please override this")
    }

    /**
     * Jika isGroupable return true
     * maka ini harus di-override untuk mendapatkan
     * daftar object yang akan di-group-kan.
     * Ideal-nya ini digunakan di `render()` atau `renderFor(user)`.
     * @return
     */
    def getGroupableObjects(forUser: User): Seq[GroupableObject] = {
        throw NotImplementedException("Not implemented, please override this")
    }

    /**
     * digunakan untuk mendapatkan type notifikasi yang sama pada group notifikasi
     * @return
     */
    def subGroupId: String = {
        throw NotImplementedException("groupableKind not implemented, please override this in " +
            this.getClass.getCanonicalName)
    }

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    def renderFor(user: User): String

    /**
     * Untuk pengecheckan apakah user menerima notification ini.
     * @param user user yang akan dicheck.
     * @return
     */
    def userAccept(user: User): Boolean

    /**
     * Set notification receivers.
     * @param user user yang mau di add (variadic).
     */
    def setReceivers(user: User*) {
        this.receivers = user.toArray
    }

    def getReceivers = (getDefaultReceivers.toList ++ this.receivers.toList).distinct

    def getDefaultReceivers = Seq.empty[User]

    /**
     * Check apakah suatu user adalah receiver
     * dari notification ini.
     * @param user user untuk dicheck.
     * @return
     */
    def isReceiver(user: User): Boolean = {
        this.receivers.contains(user) && getDefaultReceivers.contains(user)
    }

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler): Boolean

    // dibuat menggunakan postfix _ karena biar tidak bentrok dengan
    // fungsi internal dari blueprints-scala wrapper.
    def _setId(id: IDType) {
        this.id = id
    }

    /**
     * Get pretty printed creation time age
     * relative to current time.
     * ex: 3 hours ago.
     * @return
     */
    def getCreationAge: String = {
        new PrettyTime(new Date()).format(new Date(creationTime))
    }

    /**
     * Whether this notification
     * support ignore operation.
     * @return
     */
    def isIgnorable = false

    /**
     * Ignore operation
     * called by dispatcher as request
     * only called when isIgnorable return true.
     * override this to fit your needs.
     * by default it's only remove vertex and it in edge.
     */
    def ignore() {
        remove()
    }

    /**
     * Remove this notification object
     * vertex and both edges.
     */
    def remove() {
        val v = db.getVertex(this.getId)
        if (v != null) {
            v.pipe.bothE().iterator().foreach(db.removeEdge)
            db.removeVertex(v)
        }
        db.commit()
    }

    /**
     * Override ini apabila tidak ingin di-save otomatis oleh system
     * see: [[NotificationSender]]
     * @return
     */
    def autoSave = true

    override def __save__(v: Vertex){
        super.__save__(v)
        v.setProperty("creationTime", Digaku.engine.dateUtils.nowMilis)
    }

    override def __load__(vertex: Vertex) {
        super.__load__(vertex)
        this.id = vertex.getId.asInstanceOf[IDType]
    }
}


object NotifGroup {
    val PRIVATE_MESSAGE = 1
    val GENERAL_RESPONSE = 3
    val EDIT_ARTICLE = 4
    val LIKE = 5
    val SUMMON = 6
    val SUPPORT = 7
    val ATTENTION = 8
    val NOTICE = 9
    val SHOUT = 10
    val GET_EXTRA_POINT = 11
    val REPUTATION = 12
    val HAS_TROPHY = 13
    val RANK = 14
    val GIVE_REPUTATION = 15
    val ADD_STAFF = 16
    val PROMOTED_THREAD = 17
    val QUOTE = 18
    val GLOBAL_ANNOUNCEMENT = 19
    val FORUM_ANNOUNCEMENT = 20
}


object NotificationMapper extends Slf4jLogger {

    import com.ansvia.graph.BlueprintsWrapper._

    type ExtendedMapper = PartialFunction[Vertex, Option[PersistentNotification with Attention]]

    @volatile
    private var extendedAttentionMapperUsed = false

    val extendedAttentionMapper = new RulesSeq[ExtendedMapper] {
        override protected def safe_?(f: => Any): Unit = {
            extendedAttentionMapperUsed match {
                case false => f
                case _ => throw new IllegalStateException("cannot add extendedMapper when attention mapper already used")
            }
        }
    }

    private lazy val _extendedAttentionMapperCompiled = {
        extendedAttentionMapper.append({case _ => None})
        extendedAttentionMapperUsed = true
        extendedAttentionMapper.getRules
            .reduceLeftOption(_ orElse _)
    }

    def attentionMapper(v: Vertex): Option[PersistentNotification with Attention] = {
        v.getOrElse("_class_", "") match {
            case "com.ansvia.digaku.notifications.impl.EndorsementNotification" =>
                v.toCC[EndorsementNotification]

            case x =>
                // trying using extended mapper
                _extendedAttentionMapperCompiled.map(_.apply(v)).getOrElse {
                    error(x + " is not handled by notificationWrapper in attentionMapper, " +
                        "please fix it!")

                    None
                }
        }
    }


    /**
     * internal helper for mapping vertex to case class.
     * used in getNotifications.
     * @param v
     * @return
     */
    def generalMapper(v: Vertex): Option[PersistentNotification] = {
        v.getOrElse("_class_", "") match {
            case "com.ansvia.digaku.notifications.impl.JoinNotification" =>
                v.toCC[JoinNotification]
            case "com.ansvia.digaku.notifications.impl.LikeNotification" =>
                v.toCC[LikeNotification]
            case "com.ansvia.digaku.notifications.impl.ResponseNotification" =>
                v.toCC[ResponseNotification]
            case "com.ansvia.digaku.notifications.impl.ShoutNotification" =>
                v.toCC[ShoutNotification]
            case "com.ansvia.digaku.notifications.impl.SupportNotification" =>
                v.toCC[SupportNotification]
            case "com.ansvia.digaku.notifications.impl.SummonNotification" =>
                v.toCC[SummonNotification]
            case "com.ansvia.digaku.notifications.impl.EditArticleNotification" =>
                v.toCC[EditArticleNotification]
            case "com.ansvia.digaku.notifications.impl.RankNotification" =>
                v.toCC[RankNotification]
            case "com.ansvia.digaku.notifications.impl.GiveReputationNotification" =>
                v.toCC[GiveReputationNotification]
            case "com.ansvia.digaku.notifications.impl.AddStaffNotification" =>
                v.toCC[AddStaffNotification]
            case "com.ansvia.digaku.notifications.impl.PromotedThreadNotification" =>
                v.toCC[PromotedThreadNotification]
            case "com.ansvia.digaku.notifications.impl.QuoteNotification" =>
                v.toCC[QuoteNotification]
            case "com.ansvia.digaku.notifications.impl.AddPostCollaborationNotification" =>
                v.toCC[AddPostCollaborationNotification]
            case "com.ansvia.digaku.notifications.impl.AttenderEventNotification" =>
                v.toCC[AttenderEventNotification]
            case "com.ansvia.digaku.notifications.impl.EditEventNotification" =>
                v.toCC[EditEventNotification]
            case "com.ansvia.digaku.notifications.impl.EndorsementNotification" =>
                v.toCC[EndorsementNotification]
            case "com.ansvia.digaku.notifications.impl.ConnectWithFacebookNotification"=>
                v.toCC[ConnectWithFacebookNotification]
            case "com.ansvia.digaku.notifications.impl.ConnectWithTwitterNotification" =>
                v.toCC[ConnectWithTwitterNotification]
            case "com.ansvia.digaku.notifications.impl.UserHasTrophyNotification" =>
                v.toCC[UserHasTrophyNotification]
            case "com.ansvia.digaku.notifications.impl.ContentRecommendationNotification" =>
                v.toCC[ContentRecommendationNotification]
            case "com.ansvia.digaku.notifications.impl.GlobalAnnouncementNotification" =>
                v.toCC[GlobalAnnouncementNotification]
            case "com.ansvia.digaku.notifications.impl.ForumAnnouncementNotification" =>
                v.toCC[ForumAnnouncementNotification]

            /**
             * FOR UNIT TESTING PURPOSE ONLY.
             */
            case "com.ansvia.digaku.notifications.impl.DummyNotif" =>
                v.toCC[com.ansvia.digaku.notifications.impl.DummyNotif]

            case x =>
                error(x + " not handled by notificationWrapper in generalMapper," +
                    "please fix it!")
                None
        }
    }
}


object NotifFlags {
    val NONE = 0

    /**
     * Tanda untuk handler-handler lainnya agar tidak memproses.
     */
    val DONT_PROCESS = 1
}
