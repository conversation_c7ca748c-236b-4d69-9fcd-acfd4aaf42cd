package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.model.{Post, User}
import com.ansvia.digaku.notifications._
import com.ansvia.graph.BlueprintsWrapper._
import NotifUtil._

/**
 * Author: nadir (<EMAIL>)
 *
 */
case class PromotedThreadNotification(postId:Long) extends PersistentNotification(NotifKind.PROMOTED_THREAD) with EmailSession {
    override val partition: Int =  NotifPartition.LEVEL_1_NOTICE

    lazy val promotedObject = {
        val v = db.getVertex(postId)
        if (v != null)
            v.toCC[Post]
        else
            None
    }

    lazy val creatorObject = {
        promotedObject.map(_.creator)
    }

    /**
     * Untuk pengecheckan apakah user menerima notification ini.
     * @param user user yang akan dicheck.
     * @return
     */
    override def userAccept(user: User): Boolean = true

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    override def isSupport(np: NotificationSendHandler): Boolean = true

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * Return true apabila implementsi bisa di-group-kan.
     * Sebagai contoh lihat [[ResponseNotification]]
     * @return
     */
    override def isGroupable: Boolean = false

    override def isValid: Boolean = promotedObject.isDefined && creatorObject.isDefined

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    override def renderFor(user: User): String = {
        if (isValid)
            "Selamat, Administrator telah mempromosikan thread Anda %s".format(stdPostNotifStr(promotedObject.get))
        else
            "----"
    }

    /**
     * get default receivers for this notification.
     */
    override def getDefaultReceivers =
        promotedObject.map(_.creator).toSeq
}
