/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.notifications.{NotificationSendHandler, NotificationBase}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.{Trophy, User}
import com.ansvia.digaku.notifications.{PersistentNotification, NotificationSendHandler}
import com.ansvia.digaku.utils.TextCompiler
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.exc.DigakuException

/**
* Author: nadir
*
*/
case class UserHasTrophyNotification(userId:Long, trophyId:Long)
    extends PersistentNotification(NotifKind.USER_HAS_TROPHY) with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    val partition: Int = NotifPartition.LEVEL_1_NOTICE

    lazy val user:Option[User] = {
        val v = db.getVertex(userId)
        if (v == null)
            None
        else
            v.toCC[User]
    }

    lazy val trophy:Option[Trophy] = {
        val v = db.getVertex(trophyId)
        if (v == null)
            None
        else
            v.toCC[Trophy]
    }

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * Return true apabila implementsi bisa di-group-kan.
     * Sebagai contoh lihat [[com.ansvia.digaku.notifications.impl.ResponseNotification]]
     * @return
     */
    def isGroupable: Boolean = false

    /**
     * Render notification untuk user :user.
     * @param _user user yang akan membaca.
     * @return
     */
    def renderFor(_user: User): String = {
        if (isValid)
            "Selamat, Anda berhasil mendapatkan <b>badge</b> baru!"
        else
            "-----"
    }

    def userAccept(user: User): Boolean = true

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler): Boolean = true

    def isValid: Boolean = user.isDefined && trophy.isDefined

    override def getDefaultReceivers = {
        db.commit()
        user.map { u =>
            Seq(u)
        }.getOrElse(Seq.empty[User])
    }
}
