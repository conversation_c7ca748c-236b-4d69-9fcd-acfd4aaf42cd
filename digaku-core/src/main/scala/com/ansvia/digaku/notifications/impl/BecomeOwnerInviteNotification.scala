/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */
//
//package com.ansvia.digaku.notifications.impl
//
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.event.impl.{ChannelInviteOwnerAcceptEvent, ChannelInviteOwnerRejectEvent, NotificationIgnoredEvent}
//import com.ansvia.digaku.exc.{DigakuException, PermissionDeniedException}
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.model.{Forum, User}
//import com.ansvia.digaku.notifications.{AcceptRejectAttention, NotificationBase, NotificationSendHandler}
//
///**
//* Author: nadir
//*
//*/
//
///**
// * Notifikasi jika ada invitation
// * kepada user yg mau diangkat menjadi owner
// *
// * @param invitorUserId id invitor
// * @param invitedUserId id invited
// * @param channelId id group yg di kirim
// * @param invitationCode code invitation yg digenerate see [[com.ansvia.digaku.model.BaseChannel#inviteToBecomeOwner]]
// */
//case class BecomeOwnerInviteNotification(invitorUserId:Long, invitedUserId:Long, channelId:Long, invitationCode:String)
//    extends NotificationBase(NotifKind.BECOME_OWNER_INVITE)
//    with AcceptRejectAttention
//    with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//
//    lazy val invitor = {
//        val v = db.getVertex(invitorUserId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val invited = {
//        val v = db.getVertex(invitedUserId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val forumO = {
//        val v = db.getVertex(channelId)
//        if(v != null)
//            v.toCC[Forum]
//        else
//            None
//    }
//
//    lazy val inv = forumO.flatMap(_.getInvitationByCode(invitationCode))
//
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable = false
//
//    def isValid = forumO.isDefined && invited.isDefined && invitor.isDefined && inv.isDefined
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User) = {
//        if(isValid) {
//            "You are offered by $[user:%s;%s] to become owner on group #$[ch:%s;%s]".format(
//                invitor.get.name, invitor.get.getId, forumO.get.name, forumO.get.getId)
//        } else {
//            "--------------"
//        }
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = true
//
//    override def getDefaultReceivers =
//        invited.map(u => Seq(u)).getOrElse(Seq.empty[User])
//
//    /**
//     * Whether this notification
//     * support ignore operation.
//     * @return
//     */
//    override def isIgnorable = true
//
//    /**
//     * Ignore operation
//     * called by dispatcher as request
//     * only called when isIgnorable return true.
//     * override this to fit your needs.
//     */
//    override def ignore() {
//        this.reload()
//
//        invited.foreach { user =>
//            if (user.getCounter.get("notif.pending") > 0)
//                user.getCounter.decrement("notif.pending")
//        }
//
//        forumO.map { ch =>
//            val chR = ch.reload()
//            chR.removeInvitation(invitationCode)
//            Digaku.engine.eventStream.emit(ChannelInviteOwnerRejectEvent(invited.get, invitor.get, ch))
//            Digaku.engine.eventStream.emit(NotificationIgnoredEvent(this.getId))
//        }
//
//        db.commit()
//
//        super.ignore()
//    }
//
//    def userAccept(user: User) = true
//
//    def accept() {
//
//        this.reload()
//
//        invited.foreach { user =>
//            if (user.getCounter.get("notif.pending") > 0)
//                user.getCounter.decrement("notif.pending")
//        }
//
//        forumO map { ch =>
//            ch.reload()
//
//            val u = invited.get.reload()
//            try {
//                ch.getInvitationByCode(invitationCode) map { inv =>
//                    ch.changeOwnerTo(u)
//                    Digaku.engine.eventStream.emit(ChannelInviteOwnerAcceptEvent(invited.get, invitor.get,  ch))
//                } getOrElse(throw PermissionDeniedException("invalid invitation code, maybe aborted"))
//            } finally {
//                ch.removeInvitation(invitationCode)
//                remove()
//            }
//        }
//
//    }
//
//    def reject() {
//        ignore()
//    }
//}
//
///**
// * Notifikasi kepada invitor
// * jika user accept invitation untuk menjadi owner
// *
// * @param userAId yang di invite (invited)
// * @param userBId yang menginvite (invitor)
// * @param chId group yang di kirim
// */
//case class BecomeOwnerAcceptNotification(userAId:Long, userBId:Long, chId:Long)
//    extends NotificationBase(NotifKind.BECOME_OWNER_ACCEPT) with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val partition = NotifPartition.LEVEL_2_NOTICE
//
//    lazy val invited = {
//        val v = db.getVertex(userAId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val invitor = {
//        val v = db.getVertex(userBId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val ch = {
//        val v = db.getVertex(chId)
//        if(v != null)
//            v.toCC[Forum]
//        else
//            None
//    }
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable = false
//
//    def isValid = invited.isDefined && invitor.isDefined
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User) = {
//        if (!isValid)
//            throw new DigakuException("Invalid support notification data")
//
//        if (user == invitor.get) {
//            "$[user:%s;%s] has accepted your #$[ch:%s;%s] owner request".format(invited.get.name, invited.get.getId, ch.get.getName, ch.get.getId)
//        } else {
//            "$[user:%s;%s] has accepted $[user:%s;%s] #$[ch:%s;%s] owner request".format(invited.get.name, invited.get.getId, invitor.get.name, invitor.get.getId, ch.get.getName, ch.get.getId)
//        }
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = true
//
//    override def getDefaultReceivers = invitor.flatMap(Some[User]).toSeq
//
//    override def hashCode = (userAId + userBId).hashCode()
//
//    def userAccept(user: User) = true
//}
//
///**
// * Notifikasi kepada invitor
// * jika user reject invitation untuk menjadi owner
// *
// * @param userAId yang di invite (invited)
// * @param userBId yang menginvite (invitor)
// * @param chId group yang di kirim
// */
//case class BecomeOwnerRejectNotification(userAId:Long, userBId:Long, chId:Long)
//    extends NotificationBase(NotifKind.BECOME_OWNER_REJECT) with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val partition = NotifPartition.LEVEL_2_NOTICE
//
//    lazy val invited = {
//        val v = db.getVertex(userAId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val invitor = {
//        val v = db.getVertex(userBId)
//        if(v != null)
//            v.toCC[User]
//        else
//            None
//    }
//
//    lazy val ch = {
//        val v = db.getVertex(chId)
//        if(v != null)
//            v.toCC[Forum]
//        else
//            None
//    }
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable = false
//
//    def isValid = invited.isDefined && invitor.isDefined
//
//    /**
//     * Render notification untuk user :user.
//     * @param user user yang akan membaca.
//     * @return
//     */
//    def renderFor(user: User) = {
//        if (!isValid)
//            throw new DigakuException("Invalid support notification data")
//
//        if (user == invitor.get) {
//            "Sorry, $[user:%s;%s] refused to accept your #$[ch:%s;%s] owner request now".format(invited.get.name, invited.get.getId, ch.get.getName, ch.get.getId)
//        } else {
//            "Sorry, $[user:%s;%s] refused to accept $[user:%s;%s] #$[ch:%s;%s] owner request now".format(invited.get.name, invited.get.getId, invitor.get.name, invitor.get.getId, ch.get.getName, ch.get.getId)
//        }
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler) = true
//
//    override def getDefaultReceivers = invitor.flatMap(Some[User]).toSeq
//
//    override def hashCode = (userAId + userBId).hashCode()
//
//    def userAccept(user: User) = true
//}
