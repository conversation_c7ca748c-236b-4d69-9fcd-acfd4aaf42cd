/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.model._
import com.ansvia.digaku.notifications.{GroupableObjectUserBuilder, NotifGroup, NotificationSendHandler, PersistentNotification}
import com.ansvia.digaku.Types._
import scala.collection.JavaConversions._
import Label._
import com.ansvia.digaku.helpers.DbAccess
import com.tinkerpop.blueprints.Direction
import com.ansvia.digaku.database.GraphCompat.tx
import com.ansvia.digaku.notifications.NotifUtil._

/**
* Author: nadir, robin
*
*/
case class LikeNotification(likerId:Long, likableObjId:Long) extends PersistentNotification(NotifKind.LIKE)
        with DbAccess with GroupableObjectUserBuilder {


    import com.ansvia.graph.BlueprintsWrapper._


    override val partition = NotifPartition.LEVEL_1_NOTICE

    type LikableType = Likable

    lazy val likableObj:Option[LikableType] = {
        val v = db.getVertex(likableObjId)
        if(v != null)
            v.toCC[LikableType]
        else
            None
    }

    lazy val maker = {
//        getVertex.get[IDType]("likableObjId") map { likableObjId =>
        val v = db.getVertex(likableObjId)
        if(v != null) {
            v.getOrElse("_class_", "") match {
                case "com.ansvia.digaku.model.Response" =>
                    v.pipe.in(RESPONSE_WITH).in(COLLECTION_VERTEX).headOption.flatMap(_.toCC[User])
                case "com.ansvia.digaku.model.Article" | "com.ansvia.digaku.model.SimplePost" |
                     "com.ansvia.digaku.model.Picture" | "com.ansvia.digaku.model.PictureGroup" | "com.ansvia.digaku.model.Deal" =>
                    v.pipe.in(PUBLISH_CONTENT).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT)
                        .in(COLLECTION_VERTEX).headOption.flatMap(_.toCC[User])
                case "com.ansvia.digaku.model.Event" =>
                    v.pipe.in(CREATE).has("kind", CollectionVertex.Kind.CREATE)
                        .in(COLLECTION_VERTEX).headOption.flatMap(_.toCC[User])
            }
        } else
            None
//        }
    }

    lazy val liker = {
//        getVertex.get[IDType]("likerId") map { likerId =>
            val v = db.getVertex(likerId)
            if(v != null)
                v.toCC[User]
            else
                None
//        }
    }


    override val hash: String = getClass.getSimpleName + "-" + likerId + "-" + likableObjId

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable = true


    /**
     * Jika isGroupable return true
     * maka regroup ini akan dipanggil
     * dan implementor harus meng-override
     * method ini untuk meng-group-kan
     * previous unread notification apabila ada.
     */
    override def regroup(forUser: User) = {
        import scala.collection.JavaConversions._
        import com.ansvia.graph.BlueprintsWrapper._

        var likerIds = List.empty[IDType]

        forUser.getVertex.pipe.outE(NOTIFICATION)
            .has("notifKind", NotifGroup.LIKE)
            .has("read", false)
            .hasNot("notification.groupMerged", true).as("ed")
            .inV()
            .hasNot("id", getId)
            .has("likableObjId", likableObjId)
            .back("ed")
            .asInstanceOf[GremPipeEdge]
            .iterator().foreach { ed =>

            val ntfV = ed.getVertex(Direction.IN)
            likerIds :+= ntfV.getProperty[IDType]("likerId")

            likerIds ++= ed.getOrElse("groupableObjectIds", "")
                .split(",").filter(_.length > 0).map(_.toLong).toSeq

            // tandai kalo edge notif ini dah di merged dengan yang terbaru
//            ed.setProperty("notification.groupMerged", true)

            // remove Edge karena sudah dimerge ke yang terbaru.
            ed.remove()

            // kurangi count-nya, karena sudah disatukan
            forUser.getCounter.decrement("notif.generic")

        }

        likerIds :+= likerId

        this.getVertex.pipe.inE(NOTIFICATION)
            .has("_target_userId", forUser.getId)
            .hasNot("notification.groupMerged", true)
            .asInstanceOf[GremPipeEdge]
            .iterator().foreach { ed =>

            ed.setProperty("groupableObjectIds", likerIds.distinct.map(_.toString).mkString(","))

        }

    }


    /**
     * Jika isGroupable return true
     * maka ini harus di-override untuk mendapatkan
     * daftar object yang akan di-group-kan.
     * Ideal-nya ini digunakan di `render()` atau `renderFor(user)`.
     * @return
     */
    override def getGroupableObjects(forUser: User): Seq[User] = {
        tx { t =>
            t.getVertex(getId).pipe.inE(NOTIFICATION)
                .has("_target_userId", forUser.getId)
                .hasNot("notification.groupMerged", true)
                .asInstanceOf[GremPipeEdge]
                .headOption.map { ed =>

                ed.getOrElse("groupableObjectIds", "")
                    .split(",").filter(_.length > 0).map(_.toLong).flatMap(id => User.getById(id))
                    .toSeq

            }.getOrElse(Seq.empty[User])
        }
    }



    def isValid = likableObj.isDefined && liker.isDefined && maker.isDefined


    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    def renderFor(user: User) = {
        if (isValid) {

            lazy val userGroupStr = buildGroupableObjectString(user)

            likableObj.get match {
                case post:Post =>

                    if (user == maker.get) {
                        "%s memberikan rating untuk thread Anda %s".format(userGroupStr, stdPostNotifStr(post))
                    } else {
                        "-----"
                    }

//                case event:Event =>
//                    if (user == maker.get){
//                        "%s thought your $[event:%s;%s;%s] was cool".format(userGroupStr, event.getId, event.kindStr, event.origin.getName)
//                    } else{
//                        "%s thought event: $[event:%s;%s;%s] was cool".format(userGroupStr, event.getId, event.kindStr, event.origin.getName)
//                    }
//
//                case picture:Picture =>
//                    if (user == maker.get){
//                        "%s thought your $[picture:%s;%s;%s] was cool".format(userGroupStr, picture.getId, picture.kindStr, picture.origin.getName)
//                    } else{
//                        "%s thought $[picture:%s;%s;%s] was cool".format(userGroupStr, picture.getId, picture.kindStr, picture.origin.getName)
//                    }

//                case pictureGroup:PictureGroup =>
//                    if (user == maker.get){
//                        "%s thought your $[picture-group:%s;%s;%s] was cool".format(userGroupStr, pictureGroup.getId, pictureGroup.kindStr, pictureGroup.origin.getName)
//                    } else {
//                        "%s thought $[picture-group:%s;%s;%s] was cool".format(userGroupStr, pictureGroup.getId, pictureGroup.kindStr, pictureGroup.origin.getName)
//                    }

                case response:Response =>
                    val rObj = response.getRespondedObject
                        .filter(obj => obj.isInstanceOf[Post] || obj.isInstanceOf[Response])

                    if (rObj.isDefined) {
                            "%s menyukai post Anda.".format(userGroupStr)
                    } else {
                        "----"
                    }


                case _=> "----"
            }
        } else
            "----"

    }

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler) = np.name == "like"

    override def getDefaultReceivers = {
        if (isValid) {
            if (liker != maker){
                Seq(maker.get)
            } else {
                Seq.empty[User]
            }
        } else
            Seq.empty[User]
    }

    def userAccept(user: User) = true

    override def subGroupId:String = kind + "-" + likableObjId

}
