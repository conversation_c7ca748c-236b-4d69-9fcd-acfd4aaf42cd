/*
 * Copyright (c) 2013-2017 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.notifications.NewsNotifications

/**
 * Author: henky (<EMAIL>)
 */

case class LatestForumNewsNotification(articleId: Long)
    extends NewsNotifications(articleId, NotifKind.FORUM_NEWS) {

    override val hash: String = getClass.getSimpleName + "-" + articleId
}
