/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

//package com.ansvia.digaku.notifications.impl

//import com.ansvia.digaku.notifications.{EmailSession, NotificationSendHandler, NotificationBase}
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.model._
//import com.tinkerpop.blueprints.Vertex
//import com.ansvia.graph.BlueprintsWrapper._
//
///**
// * Author: nadir, robin
// *
// *
// */
//
///**
// * MessageResponseNotification ini merupakan class yang digunakan untuk
// * notifikasi response atau reply pada sebuah message
// * @param creatorId user yang membuat response.
// * @param messageResponseId id dari response yang dibuat user.
// * @param privateMessageId id dari private message dimana response ditulis.
// */
//case class MessageResponseNotification(creatorId:Long,
//                                       messageResponseId:Long,
//                                       privateMessageId:Long)
//        extends NotificationBase(NotifKind.PM_RESPONSE) with DbAccess with EmailSession {
//
//    val partition = NotifPartition.CONVERSATION
//
//    lazy val messageResponse:Option[MessageResponse]={
//        val v = db.getVertex(messageResponseId)
//        if (v != null){
//            v.toCC[MessageResponse]
//        } else
//            None
//    }
//
//    lazy val privateMessageObject:Option[PrivateMessage]={
//        val v = db.getVertex(privateMessageId)
//        if (v != null){
//            v.toCC[PrivateMessage]
//        } else
//            None
//    }
//
//    lazy val privateMessageCreator = privateMessageObject.map(_.creator)
//
//    lazy val creator:Option[User] = {
//        val v = db.getVertex(creatorId)
//        if (v != null){
//            v.toCC[User]
//        } else
//            None
//    }
//
//    private def pronounObject(user:User):String = {
//        user.sex match {
//            case SexType.MALE => "his"
//            case SexType.FEMALE => "her"
//            case _ => " "
//        }
//    }
//
//    /**
//     * Check apakah notification ini bisa
//     * di-group-kan.
//     * @return
//     */
//    def isGroupable: Boolean = true
//
//
//    /**
//     * Jika isGroupable return true
//     * maka regroup ini akan dipanggil
//     * dan implementor harus meng-override
//     * method ini untuk meng-group-kan
//     * previous unread notification apabila ada.
//     */
//    override def regroup(forUser: User) = {
//        // @TODO(robin): code here
//    }
//
//
//    /**
//     * Jika isGroupable return true
//     * maka ini harus di-override untuk mendapatkan
//     * daftar object yang akan di-group-kan.
//     * Ideal-nya ini digunakan di `render()` atau `renderFor(user)`.
//     * @return
//     */
//    override def getGroupableObjects(forUser: User) = {
//        // @TODO(robin): code here
//        Seq.empty[GroupableObject]
//    }
//
//    /**
//     * Render notification untuk user :user.
//     * @param tUser user yang akan membaca.
//     * @return
//     */
//    def renderFor(tUser: User): String = {
//        if (isValid){
//            if (creator.get == privateMessageCreator.get){
//                "$[user:%s;%s] reply $[message-response:%s] on ".format(creator.get.getName,
//                    creator.get.getId, messageResponse.get.getId)+ pronounObject(privateMessageCreator.get) +
//                    " message $[private-message:%s]".format(privateMessageObject.get.getId)
//            } else {
//                if (tUser == privateMessageCreator.get){
//                    ("$[user:%s;%s] reply $[message-response:%s] on " +
//                        "your message $[private-message:%s]").format(creator.get.getName,
//                        creator.get.getId, messageResponse.get.getId, privateMessageObject.get.getId)
//                } else
//                    ("$[user:%s;%s] reply $[message-response:%s] on $[user:%s;%s]'s " +
//                        "message $[private-message:%s]").format(creator.get.getName,
//                        creator.get.getId, messageResponse.get.getId,
//                        privateMessageCreator.get.getName, privateMessageCreator.get.getId,
//                        privateMessageObject.get.getId)
//            }
//        } else
//            "---"
//    }
//
//    def userAccept(user: User): Boolean = true
//
//    override def getDefaultReceivers = {
//        if (isValid){
//
//            privateMessageObject.map{ pM =>
//                var seqUs = Seq.empty[User]
//                seqUs :+= pM.creator
//                seqUs ++= pM.getParticipants
//                seqUs.filter(x => x != creator.get)
//            }.getOrElse(Seq.empty[User])
//
//        } else
//            Seq.empty[User]
//    }
//
//    /**
//     * Check apakah mendukung suatu handler.
//     * @param np notification handler.
//     * @return
//     */
//    def isSupport(np: NotificationSendHandler): Boolean = np.name == "pm-response"
//
//    def isValid: Boolean = {
//        creatorId>0 && messageResponseId>0 && messageResponse.isDefined && privateMessageObject.isDefined &&
//        creator.isDefined && privateMessageCreator.isDefined
//    }
//
//    override def __save__(v: Vertex){
//
//        // set pm.id untuk meng-group-kan antara PrivateMessageNotification dan MessageResponseNotification
//        // see [[com.ansvia.digaku.model.UserBase.getMessageNotifications]]
//        v.setProperty("pm.id", privateMessageId)
//        super.__save__(v)
//        generateEmailSession()
//    }
//}
