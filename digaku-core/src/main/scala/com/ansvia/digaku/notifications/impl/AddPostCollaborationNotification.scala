/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.notifications.{NotificationS<PERSON><PERSON><PERSON><PERSON>, PersistentNotification}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.{Post, User}
import com.ansvia.digaku.exc.DigakuException

/**
 * Author: nadir, robin
 *
 */


case class AddPostCollaborationNotification(creationPostId:Long, userCanEditId:Long, postId:Long)
    extends PersistentNotification(NotifKind.POST_COLLABORATION) with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    val partition = NotifPartition.LEVEL_3_NOTICE

    lazy val creatorPost = {
        val v = db.getVertex(creationPostId)
        if(v != null)
            v.toCC[User]
        else
            None
    }

    lazy val userCanEdit = {
        val v = db.getVertex(userCanEditId)
        if(v != null)
            v.toCC[User]
        else
            None
    }

    lazy val post = {
        val v = db.getVertex(postId)
        if(v != null)
            v.toCC[Post]
        else
            None
    }

    def isValid: Boolean = creatorPost.isDefined && userCanEdit.isDefined && post.isDefined


    //yohan add you to collaborate in article
    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    def renderFor(user: User): String = {

        if (!isValid)
            throw new DigakuException("Invalid support notification data")

        if (user == userCanEdit.get)
            "$[user:%s;%s] has added you to collaborate on a Text $[post:%s;%s;%s]"
                .format(creatorPost.get.name, creatorPost.get.getId,post.get.getId, post.get.origin.kind, post.get.origin.getName)
        else
            "$[user:%s;%s] has added $[user:%s;%s] to collaborate on a Text $[post:%s;%s;%s]"
                .format(creatorPost.get.name, creatorPost.get.getId, userCanEdit.get.name, userCanEdit.get.getId,
                    post.get.getId, post.get.origin.kind, post.get.origin.getName)
    }

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable: Boolean = false

    def userAccept(user: User): Boolean = true

    override def getDefaultReceivers = userCanEdit.flatMap(Some[User]).toSeq

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler): Boolean = np.name == "post-collaboration"


}
