/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications

import com.ansvia.graph.annotation.Persistent
import com.ansvia.digaku.Types._

/**
 * Author: robin
 * Date: 11/18/13
 * Time: 4:25 PM
 *
 */
trait GroupableNotification[T] {

    @Persistent var subForumId = 0L

    def setGroupId(gid:IDType) = {
        subForumId = gid
        this.asInstanceOf[T]
    }
}
