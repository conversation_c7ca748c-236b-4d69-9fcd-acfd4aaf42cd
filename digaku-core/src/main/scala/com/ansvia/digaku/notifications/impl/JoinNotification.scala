/*
* Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
*
* This file is part of Digaku project.
*
* Unauthorized copying of this file, via any medium is strictly prohibited
* Proprietary and confidential.
*/

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.{User, _}
import com.ansvia.digaku.notifications.{NotificationSendHandler, PersistentNotification}
import com.ansvia.digaku.notifications.NotifUtil._

/**
* Author: nadir, robin
*
*/

/**
 * Channel join notification, di-dispatch ketika ada user yang join ke channel.
 * @param userId id dari user yang join.
 * @param chId channel yang di-join-i.
 */
case class JoinNotification(userId:Long, chId:Long) extends PersistentNotification(NotifKind.JOIN) with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    val partition = NotifPartition.LEVEL_1_NOTICE

    /**
     * user yang join.
     */
    lazy val user:Option[User] = {

        val v = db.getVertex(userId)
        if (v != null)
            v.toCC[User]
        else
            None

    }
    lazy val forum:Option[Forum] = {
        val v = db.getVertex(chId)
        if (v != null)
            v.toCC[Forum]
        else
            None
    }


    override val hash: String = getClass.getSimpleName + "-" + userId + "-" + chId

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable = false

    def isValid = userId > 0 && chId > 0 && user.isDefined && forum.isDefined


    /**
     * Render notification untuk user :user.
     * @param tUser user yang akan membaca.
     * @return
     */
    def renderFor(tUser: User) = {
            "Anda telah ditambahkan sebagai anggota di Sub Forum #%s".format(stdForumNotifStr(forum.get))
    }

    override def getDefaultReceivers = {
        user.toSeq
    }

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler) = np.name == "join"

    def userAccept(user: User) = true

    override def subGroupId:String = kind + "-" + chId
}
