/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications

import com.ansvia.digaku.notifications.impl.NotifPartition
import com.ansvia.graph.annotation.Persistent

/**
 * Author: robin
 *
 */
trait Attention {
    @Persistent var attention = true
}

trait AcceptRejectAttention extends Attention {

    val partition = NotifPartition.LEVEL_3_NOTICE

    /**
     * Accept this attention.
     * ini juga akan otomatis me-remove/
     * invalidate notification-nya.
     */
    def accept()

    /**
     * Reject this attention.
     * ini juga akan otomatis me-remove/
     * invalidate notification-nya.
     */
    def reject()
}

trait DoneAttention extends Attention {
    def done()
}

