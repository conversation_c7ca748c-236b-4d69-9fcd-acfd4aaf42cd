/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

/**
 * Author: robin
 *
 * Merupakan jenis-jenis notification
 * menggunakan integer karena lebih ngirit storage
 * dan optimal.
 *
 * <PERSON>lo ingin buat notification jenis baru harus nambah
 * jenis di sini.
 *
 * WARNING: jangan rubah urutan angka-nya!!
 */
object NotifKind {

    val DUMMY = 0
    val SUMMON = 1
    val ATTENDER_EVENT = 2
    val BECOME_OWNER_ACCEPT = 3
    val BECOME_STAFF_ACCEPT = 4
    val CHANNEL_INVITE_ACCEPT = 5
    val CHANNEL_INVITE_REJECT = 6
    val CONNECT_FRIENDS_FB = 7
    val CONNECT_FRIENDS_TW = 8
    val EDIT_ARTICLE = 9
    val EDIT_EVENT = 10
    val ENDORSEMENT = 11
    val JOIN = 12
    val PM_RESPONSE = 13
    val PM = 14
    val RESPONSE = 15
    val SHOUT = 16
    val SUPPORT_APPROVAL_ACCEPT = 17
    val SUPPORT = 18
    val POST_COLLABORATION = 19
    val USER_GET_EXTRA_POINT = 20
    val USER_HAS_TROPHY = 21
    val BECOME_OWNER_INVITE = 22
    val BECOME_OWNER_REJECT = 23
    val SUPPORT_APPROVAL_INVITE = 24
    val SUPPORT_APPROVAL_REJECT = 25
    val CHANNEL_INVITE = 26
    val LIKE = 27
    val BECOME_STAFF_INVITE = 28
    val BECOME_STAFF_REJECT = 29
    val FINISH_GET_STARTED = 30
    val CONTENT_RECOMMENDATION = 31
    val RANK = 32
    val REPUTATION = 33
    val PROMOTED_THREAD = 34
    val ADD_MODERATOR = 35
    val QUOTE = 36
    val GLOBAL_ANNOUNCEMENT = 37
    val FORUM_ANNOUNCEMENT = 38
    val GLOBAL_NEWS = 39
    val FORUM_NEWS = 40
    val TEST = 500

}
