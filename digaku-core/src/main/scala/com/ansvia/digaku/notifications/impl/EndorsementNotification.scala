/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.model.{Endorsement, User}
import com.ansvia.digaku.notifications.{AcceptRejectAttention, NotificationSendHandler, NotificationBase}
import com.ansvia.digaku.notifications.{NotifGroup, AcceptRejectAttention, NotificationSendHandler, PersistentNotification}
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.utils.TextCompiler
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.Types.GremPipeEdge
import scala.collection.JavaConversions._

/**
* Author: nadir
*
*/
case class EndorsementNotification(endorserId:Long, endorsementId:Long, targetUserId:Long)
    extends PersistentNotification(NotifKind.ENDORSEMENT)
    with AcceptRejectAttention
    with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    lazy val endorser = {
        val v = db.getVertex(endorserId)
        if(v != null)
            v.toCC[User]
        else
            None
    }

    lazy val endorsement = {
        val v = db.getVertex(endorsementId)
        if(v != null)
            v.toCC[Endorsement]
        else
            None
    }

    lazy val targetUser = {
        val v = db.getVertex(targetUserId)
        if(v != null)
            v.toCC[User]
        else
            None
    }

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable = false

    def isValid = endorser.isDefined && endorsement.isDefined && targetUser.isDefined

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    def renderFor(user: User) = {
        if(!isValid)
            throw new DigakuException("Notification not valid for this operation.")

//        "You are invited to join $[ch:%s;%s], click $[invitation:%s;%s;%s] to join".format(
//            group.get.name, group.get.getId, invCode, group.get.getId, group.get.name)
        val rv = "You are endorsed as **%s** by $[user:%s;%s]".format(
            endorsement.get.labelName, endorser.get.name, endorser.get.getId)

        TextCompiler.compileMessage(rv.trim)
    }

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler) = true

    override def getDefaultReceivers =
        targetUser.map(u => Seq(u)).getOrElse(Seq.empty[User])

    /**
     * Whether this notification
     * support ignore operation.
     * @return
     */
    override def isIgnorable = true

    /**
     * Ignore operation
     * called by dispatcher as request
     * only called when isIgnorable return true.
     * override this to fit your needs.
     */
    override def ignore() {
        this.reload()
        targetUser.foreach { u =>

            if (u.getCounter.get("notif.pending") > 0)
                u.getCounter.decrement("notif.pending")

            u.reload().getVertex.pipe.outE(ENDORSEMENT)
                .has("endorserId", endorserId)
                .has("endorsementId", endorsementId)
                .has("accepted", false)
                .asInstanceOf[GremPipeEdge]
                .iterator()
                .foreach(db.removeEdge)
        }
        db.commit()
        super.ignore()
    }

    def userAccept(user: User) = true


    /**
     * Accept this attention.
     */
    def accept() = {
        this.reload()
        targetUser.foreach { u =>

            if (u.getCounter.get("notif.pending") > 0)
                u.getCounter.decrement("notif.pending")

            u.getVertex.pipe.outE(ENDORSEMENT)
                .has("endorserId", endorserId)
                .has("endorsementId", endorsementId)
                .has("accepted", false)
                .asInstanceOf[GremPipeEdge]
                .iterator()
                .foreach(e => e.setProperty("accepted", true))
        }

        db.commit()

        remove()
    }

    def reject() {
        ignore()
    }
}
