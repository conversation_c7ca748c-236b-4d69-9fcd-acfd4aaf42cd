/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

//package com.ansvia.digaku.notifications
//
//import impl.BecomeStaffInviteNotification
//import impl.SummonNotification
//import impl.SupportNotification
//import impl.{BecomeStaffInviteNotification,SummonNotification, SupportNotification}
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.model._
//import scala.concurrent.duration
//import java.util.concurrent.TimeUnit
//import com.ansvia.digaku.Global
//
///**
// * Author: nadir
// * Date: 5/21/13
// * Time: 1:01 PM
// *
// */
//
//object EmailNotificationSendHandler extends AsyncNotificationSendHandler with <PERSON>lf4jLogger with DbAccess {
//
//
//    val name: String = "mail-sender"
//
//    var DELAY = 100
//
//    private lazy val scheduler = Global.actorSystem.scheduler
//
//    var extendedHandlers = Array.empty[NotificationSendHandler]
//
//    def sendMail(ntf:NotificationBase){
//        debug("Processing Mail notif")
//        ntf match {
//            case sN:SupportNotification =>
//                sN.getDefaultReceivers.filter{ user =>
//                    !user.getNoEmailNotif.contains(EmailRoleNotif.NEW_SUPPORTER)
//                }.foreach{ user =>
//
//                    MailSender.sendMail(sN.renderFor(user), sN.renderFor(user), user)
//                    debug("Send mail support for: " + user.name)
//                }
//
//            case sn:SummonNotification =>
//
//                if (sn.isValid){
//                    sn.obj.map{ ob =>
//                        ob match {
//                            case SimplePost(content) =>
//                                sn.getDefaultReceivers.filter{ user =>
//                                    !user.getNoEmailNotif.contains(EmailRoleNotif.MENTION_POST)
//                                }.foreach{ user =>
//                                    MailSender.sendMail(sn.renderFor(user), sn.renderFor(user), user)
//                                    debug("Send mail summon for: " + user.name)
//                                }
//
//                            case Response(content) =>
//                                sn.getDefaultReceivers.filter{ user =>
//                                    !user.getNoEmailNotif.contains(EmailRoleNotif.MENTION_RESPONSE)
//                                }.foreach{ user =>
//                                    MailSender.sendMail(sn.renderFor(user), sn.renderFor(user), user)
//                                    debug("Send mail summon for: " + user.name)
//                                }
//                        }
//                    }
//                } else
//                    debug("Summon is not valid")
//
//
//            case cs:BecomeStaffInviteNotification =>
//                cs.getDefaultReceivers.filter{ user =>
//                    !user.getNoEmailNotif.contains(EmailRoleNotif.INVITED_STAFF_CHANNEL)
//                }.foreach{ user =>
//                    MailSender.sendMail(cs.renderFor(user), cs.renderFor(user), user)
//                    debug("Send mail invited for: " + user.name)
//                }
//
//            case _ =>
//
//        }
//    }
//
//    protected def asyncDispatch: PartialFunction[NotificationBase, Unit] = {
//        case notif =>
//        scheduler.scheduleOnce(Duration(DELAY,TimeUnit.MILLISECONDS), new Runnable {
//            def run() {
//                sendMail(notif)
//            }
//        })
//    }
//}
//
//
//object BlockingEmailNotificationHandler extends NotificationSendHandler with Slf4jLogger with DbAccess {
//
//
//    val name: String = "blocking-mail-sender"
//
//    def dispatch: PartialFunction[NotificationBase, Unit] = {
//        case ntf =>
//            sendMail(ntf)
//    }
//
//    def sendMail(ntf:NotificationBase){
//        ntf match {
//            case sN:SupportNotification =>
//                val users = sN.getDefaultReceivers.filter{ user =>
//                    user.getNoEmailNotif.contains(EmailRoleNotif.NEW_SUPPORTER)
//                }.toArray
//                for (u <- users){
////                    MailSender.sendMail(Array(u), sN.renderFor(u), sN.renderFor(u))
//                }
//
//            case _ =>
//        }
//    }
//}
