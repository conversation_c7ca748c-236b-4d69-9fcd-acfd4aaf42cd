/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications


/**
 * Author: robin
 *
 */
abstract class NotificationSendHandler {
    val name: String
    def dispatch:PartialFunction[NotificationBase, Unit]
}

/**
 * Notification send handlers yang dihususkan untuk mengelompokkan notification handlers dan memproses
 * handlers secara sequential / berurutan (tidak acak).
 * @param handlers notification-notification send handlers yang akan dikelompokkan.
 */
case class GroupSequentialNotificationSendHandlers(handlers:NotificationSendHandler*) extends NotificationSendHandler {
    override val name: String = "group-seq-notif-send-handlers"

    override def dispatch: PartialFunction[NotificationBase, Unit] = {
        case ntf =>
            for ( nh <- handlers ){
                if ((ntf.getFlags | NotifFlags.DONT_PROCESS) != ntf.getFlags) {
                    nh.dispatch(ntf)
                }
            }
    }
}

