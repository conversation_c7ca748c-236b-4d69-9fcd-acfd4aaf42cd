/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.Types.IDType
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.{Article, BaseModel, Label, Picture, Post, Response, User}
import com.ansvia.digaku.notifications._
import com.ansvia.digaku.utils.TextCompiler
import com.tinkerpop.gremlin.Tokens.T

/**
* Author: robin
*
*/

/**
 * Implementasi notifikasi untuk rekomendasi konten ke user.
 * @param userId id user yang merekomendasikan.
 * @param objId id object dari content yang akan direkomendasikan.
 */
case class ContentRecommendationNotification(userId:Long, objId:Long)
    extends PersistentNotification(NotifKind.CONTENT_RECOMMENDATION) with DbAccess with EmailSession {

    import com.ansvia.graph.BlueprintsWrapper._

    import scala.collection.JavaConversions._

    val partition = NotifPartition.LEVEL_1_NOTICE


    lazy val user:Option[User] = {
        val v = db.getVertex(userId)
        if (v == null)
            None
        else
            v.toCC[User]
    }

    /**
     * Object content yang direkomendasikan.
     */
    lazy val obj:Option[BaseModel[IDType]] = {
        val v = db.getVertex(objId)
        if (v == null)
            None
        else
            v.getOrElse("_class_", "") match {
                case "com.ansvia.digaku.model.Response" =>
                    v.toCC[Response]
//                case "com.ansvia.digaku.model.SimplePost" =>
//                    v.toCC[SimplePost]
                case "com.ansvia.digaku.model.Article" =>
                    v.toCC[Article]
//                case "com.ansvia.digaku.model.PictureGroup" =>
//                    v.toCC[PictureGroup]
                case "com.ansvia.digaku.model.Picture" =>
                    v.toCC[Picture]
                case _ =>
                    None
            }
    }

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler) = true

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable = false

    /**
     * Render notification untuk user :user.
     * @param _user user yang akan membaca.
     * @return
     */
    def renderFor(_user: User) = {
        if (!isValid)
            throw new DigakuException("notification not valid, cannot render.")

        "$[user:%s;%s] published %s".format(user.get.name, user.get.getId, linkableObject)
    }

    private lazy val normalizedTitleO = {
        import com.ansvia.digaku.helpers.ContentHelpers._
        import com.ansvia.digaku.utils.RichString._

        obj.map {
            case p:Post => TextCompiler.stripUrl(p.normalizedTitle()).trim.truncate(70)
            case p:Picture => TextCompiler.stripUrl(p.title).trim.truncate(70)
//            case p:PictureGroup => TextCompiler.stripUrl(p.title).trim.truncate(70)
            //            case article: Article =>
            //                article.title
            //            case p: Post with HasEmbeddedObject[IDType] =>
            //                val embs = p.getEmbeddedObjects
            //                if (embs.length > 0)
            //                    embs.headOption.map {
            //                        case pic: Picture =>
            //                            pic.title
            //                        case vl: EmbeddedLink =>
            //                            vl.title.truncate(70)
            //                        case _ =>
            //                            p match {
            //                                case sp: SimplePost =>
            //                                    sp.content.truncate(70)
            //                                case _ =>
            //                                    p.getContent.truncate(70)
            //                            }
            //                    }.getOrElse(p.getContent.truncate(70))
            //                else
            //                    p.getContent.truncate(70)
            //            case p:Picture => p.getTitle.truncate(70)
            //            case p =>
            //                ""
        }
    }



    private def linkableObject = {

        if (normalizedTitleO.getOrElse("") != "")
            ": \"" + normalizedTitleO.getOrElse("") + "\""
        else
            "some post"
    }

    def isValid = user.isDefined && obj.isDefined


    /**
     * Ini digunakan untuk memeriksa apakah object layak untuk
     * direkomendasikan atau tidak.
     */
    def isValidRecommendationItem:Boolean = {
        normalizedTitleO.exists(_.length > 3)
    }


    def userAccept(user: User) = true


    override def getDefaultReceivers = {
        // untuk menotify user yg knows dg score tertentu

        user.map { user =>
            user.getVertex.pipe.inE(Label.KNOWS).has("score",T.gt,0.02).outV()
                .iterator().flatMap(_.toCC[User]).flatMap { u =>

                if (u.isSupport(user)){
                   Some(u)
                }else
                    None
            }
        }.getOrElse(Nil).toSeq

    }

    /**
     * Override ini apabila tidak ingin di-save otomatis oleh system
     * see: [[NotificationSender]]
     * @return
     */
    override def autoSave: Boolean = false
}

