/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.notifications.{NotificationS<PERSON><PERSON><PERSON><PERSON>, PersistentNotification}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.User
import com.ansvia.digaku.model.Label._
import scala.collection.JavaConversions._
import com.ansvia.digaku.exc.DigakuException

/**
 * Author: nadir
 *
 */
case class ConnectWithTwitterNotification(userId:Long)
    extends PersistentNotification(NotifKind.CONNECT_FRIENDS_TW) with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    val partition: Int = NotifPartition.LEVEL_1_NOTICE

    lazy val user:Option[User] = {
        val v = db.getVertex(userId)
        if (v == null)
            None
        else
            v.toCC[User]
    }

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * Return true apabila implementsi bisa di-group-kan.
     * Sebagai contoh lihat [[com.ansvia.digaku.notifications.impl.ResponseNotification]]
     * @return
     */
    def isGroupable: Boolean = false

    /**
     * Render notification untuk user :user.
     * @param _user user yang akan membaca.
     * @return
     */
    def renderFor(_user: User): String = {
        if (!isValid)
            throw new DigakuException("notification not valid, cannot render.")

        "$[user:%s;%s], your friend on twitter, has joined mindtalk".format(user.get.name, user.get.getId)
    }

    def userAccept(user: User): Boolean = true

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler): Boolean = true

    def isValid: Boolean = user.isDefined

    override def getDefaultReceivers = {
        db.commit()
        user.map { u =>
            u.getVertex.pipe.out(CONNECT_TW)
                .in(FOLLOW)
                .in(CONNECT_TW)
                .iterator()
                .flatMap(_.toCC[User])
                .toSeq
        }.getOrElse(Seq.empty[User])
    }

}
