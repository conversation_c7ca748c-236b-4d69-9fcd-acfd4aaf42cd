/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.Types._
import com.ansvia.digaku.database.GraphCompat.tx
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.model._
import com.ansvia.digaku.notifications._
import com.ansvia.digaku.utils.TextExtractor
import com.ansvia.graph.BlueprintsWrapper._
import com.tinkerpop.blueprints.{Direction, Vertex}
import NotifUtil._

import scala.collection.JavaConversions._

/**
* Author: nadir, robin
*
*/

/**
 * Notifikasi apabila ada user yang merespon pada post/content.
 * @param responderId id dari user yg merespon.
 * @param responseId id dari object respon-nya.
 * @param respondedObjectId id dari object yang direspon.
 */
case class ResponseNotification(responderId:Long, responseId:Long, respondedObjectId:Long)
    extends PersistentNotification(NotifKind.RESPONSE) with GroupableObjectUserBuilder with EmailSession {


    override val partition = NotifPartition.CONVERSATION

    lazy val response:Option[Response] = {
        val v = db.getVertex(responseId)
        if (v != null)
            v.toCC[Response]
        else
            None
    }
    lazy val respondedObject = response.flatMap(_.getRespondedObject)
    lazy val responder:Option[User] = {
        val v = db.getVertex(responderId)
        if (v != null)
            v.toCC[User]
        else
            None
    }


    override val hash: String = getClass.getSimpleName + "-" + responderId + "-" + responseId


    private def pronounObject(user:User):String = {
        user.sex match {
            case SexType.MALE => "his"
            case SexType.FEMALE => "her"
            case _ => " "
        }
    }

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable = true

    def isValid = responderId > 0 && responseId > 0 && response.isDefined &&
        responder.isDefined && respondedObject.isDefined

    private def renderRespondedObject(responable:Responable):String = {
        responable match {
            case post:Post =>
                stdPostNotifStr(post)
            case event:Event =>
                "$[event:%s;%s;%s]".format(event.getId, event.kind, event.origin.getName)
            case picture:Picture =>
                "$[picture:%s;%s;%s]".format(picture.getId, picture.kind, picture.origin.getName)
            case resp:Response =>
                "$[response:%s;%s;%s]".format(resp.getId, resp.origin.kind, resp.origin.getName)
//            case pictureGroup:PictureGroup =>
//                "$[picture-group:%s;%s;%s]".format(pictureGroup.getId, pictureGroup.kind, pictureGroup.origin.getName)
            case _ => "---------"
        }
    }

    /**
     * Render notification untuk user :user.
     * @param tUser user yang akan membaca.
     * @return
     */
    def renderFor(tUser: User) = {

        //
        // user -> $[user:name;id]
        // response -> $[response:id;origin.kind;origin.name]
        // post -> $[post:id;origin.kind;origin.name]
        // event -> $[event:id;origin.kind;origin.name]
        // picture -> $[picture:id;origin.kind;origin.name]
        //
        // gunakan format tersebut di atas untuk semua notifikasi (tidak terbatas pada ResponseNotification saja).

//        if (response.isDefined && responder.isDefined && respondedObject.isDefined)

        if (isValid) {

            val users = buildGroupableObjectString(tUser)

            respondedObject.get match {
                case resp:Response =>

                    val respObj = resp.getRespondedObject.map(renderRespondedObject).getOrElse("")

                    if (tUser.getId == resp.creator.getId) {
                        "%s mengirimkan balasan untuk post Anda".format(users, renderRespondedObject(respondedObject.get))
                    } else if (resp.getRespondedObject.exists(_.creator.getId == tUser.getId)) {
                        "%s mengirimkan balasan untuk thread Anda %s".format(users, respObj)
                    } else {
                        "%s mengirimkan balasan pada thread favorite Anda".format(users)
                    }

                case hasCreator:HasCreator =>
                    if (tUser == hasCreator.creator){
                        "%s mengirimkan balasan untuk thread Anda %s".format(users, renderRespondedObject(respondedObject.get))
                    } else {
                        "%s mengirimkan balasan pada thread favorite Anda".format(users)

//                        val users = buildGroupableObjectString(tUser)
//
//                        val grupable = getGroupableObjects(tUser)
//
//                        if (grupable.length == 1 && grupable.headOption.exists(_.asInstanceOf[User] == hasCreator.creator)) {
//                            "%s responded to %s %s".format(users, pronounObject(hasCreator.creator),
//                                renderRespondedObject(respondedObject.get))
//                        } else
//                            "%s responded to $[user:%s;%s]'s %s".format(users, hasCreator.creator.name, hasCreator.creator.getId,
//                                renderRespondedObject(respondedObject.get))
                    }
//                case event:Event =>
//                    if (tUser == event.creator){
//                        "$[user:%s;%s] response: $[response:%s;%s;%s] on your event: $[event:%s;%s;%s]".format(responder.get.name,
//                            responder.get.getId, response.get.getId, event.kind, event.origin.getName,
//                            event.getId, event.origin.kind, event.origin.getName)
//                    } else {
//                        "$[user:%s;%s] response $[response:%s;%s;%s] on $[event:%s;%s;%s]".format(responder.get.name,
//                            responder.get.getId, response.get.getId, event.kind, event.origin.getName,
//                            event.getId, event.origin.kind, event.origin.getName)
//                    }
//                case picture:Picture =>
//                    if (tUser == picture.creator){
//                        "$[user:%s;%s] response: $[response:%s;%s;%s] on your picture: $[picture:%s;%s;%s]".format(responder.get.name,
//                            responder.get.getId, response.get.getId, picture.kind, picture.origin.getName,
//                            picture.getId, picture.origin.kind, picture.origin.getName)
//                    } else {
//                        "$[user:%s;%s] response $[response:%s;%s;%s] on $[picture:%s;%s;%s]".format(responder.get.name,
//                            responder.get.getId, response.get.getId, picture.kind, picture.origin.getName,
//                            picture.getId, picture.origin.kind, picture.origin.getName)
//                    }
                case _ => "-----"
            }
        } else {
            "---"
        }
    }

    /**
     * get default receivers for this notification.
     */
    override def getDefaultReceivers = {
        if (isValid) {

            // hanya creator yang dapat notif ketika thread nya ada yang reply
//            val creator = respondedObject.flatMap {
//                case hasCreator:HasCreator =>
//                    hasCreator.creatorOption
//                case _ =>
//                    None
//            }.filterNot(_ == responder.get)
//            .toSeq

            val creators = respondedObject.map {
                case resp:Response =>
                    resp.getRespondedObject.map(_.creator).toSeq.:+(resp.creator)
                case post:Post =>
                    post.creatorOption.toSeq
            }.getOrElse(Seq.empty[User])

            val monitorists = respondedObject.flatMap {
                case post:Post =>
                    Some(post.getMonitorists(0, 100))

                case resp:Response =>
                    resp.getRespondedObject.flatMap {
                        case post:Post =>
                            Some(post.getMonitorists(0, 100))

                        case _ =>
                            None
                    }
                case _ =>
                    None
            }.getOrElse(Seq.empty[User])

            (creators ++ monitorists).filterNot(x => x == responder.get)

        } else
            Seq.empty[User]
    }

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler) = np.name == "response"

    def userAccept(user: User) = true

    override def hashCode = (responderId + responseId).hashCode()

    /**
     * Jika isGroupable return true
     * maka regroup ini akan dipanggil
     * dan implementor harus meng-override
     * method ini untuk meng-group-kan
     * previous unread notification apabila ada.
     *
     * non-transactional, perlu dieksekusi di dalam transactional scope.
     */
    override def regroup(forUser:User){

        import com.ansvia.graph.BlueprintsWrapper._
        import scala.collection.JavaConversions._


        var responderIds = Seq.empty[String]

        forUser.getVertex.pipe.outE(NOTIFICATION)
            .has("notifKind", NotifGroup.GENERAL_RESPONSE)
            .has("read", false)
            .hasNot("notification.groupMerged", true).as("ed")
            .inV()
            .hasNot("id", getId)
            .has("respondedObjectId", respondedObjectId)
            .back("ed")
            .asInstanceOf[GremPipeEdge]
            .iterator().foreach { ed =>

                val ntfV = ed.getVertex(Direction.IN)

                val userGroupId = ed.getOrElse("userGroupRefId", 0L)

                val responderId = ntfV.getProperty[IDType]("responderId")

                if (userGroupId > 0) {
                    responderIds +:= "ug:%s:%s:%s"
                        .format(forUser.getId.toString, responderId.toString, userGroupId.toString)
                } else {
                    responderIds +:= "%s:%s".format(forUser.getId.toString, responderId.toString)
                }

                responderIds ++= {
                    val ids = ed.getOrElse("groupableObjectIds", "").split(",")
                    ids.map(_.trim).filter(_.length > 0).map(_.split(":"))
                        .filter { arr =>
                            arr.length match {
                                case 2 =>
                                    arr.head.toLong == forUser.getId
                                case 4 =>
                                    arr.apply(1).toLong == forUser.getId
                                case _ =>
                                    false
                            }
                        }.map(_.mkString(":"))
                }

//                ed.setProperty("notification.groupMerged", true)

                // remove Edge karena sudah dimerge ke yang terbaru.
                ed.remove()

                // Kurangi counter edge notif.
                forUser.getCounter.decrement("notification_edge")

                val notifCount = forUser.getUnreadGenericNotificationCount
                if (notifCount > 0) {
                    // kurangi count-nya, karena sudah disatukan
                    forUser.getCounter.decrement("notif.generic")
                }

            }

        val thisResponderId = response.flatMap { resp =>
            resp.getPostAs.map { pa =>
                "ug:%s:%s:%s".format(forUser.getId.toString, responderId.toString, pa.getId.toString)
            }
        }.getOrElse {
            "%s:%s".format(forUser.getId.toString, responderId.toString)
        }

        responderIds +:= thisResponderId

        val responderIdsStr = responderIds.distinct.mkString(",")

        this.getVertex.pipe.inE(NOTIFICATION)
            .has("_target_userId", forUser.getId)
            .hasNot("notification.groupMerged", true)
            .asInstanceOf[GremPipeEdge]
            .iterator().foreach { ed =>

            ed.setProperty("groupableObjectIds", responderIdsStr)
        }
    }

    /**
     * Jika isGroupable return true
     * maka ini harus di-override untuk mendapatkan
     * daftar object yang akan di-group-kan.
     * Ideal-nya ini digunakan di `render()`
     * @return
     */
    override def getGroupableObjects(forUser:User):Seq[GroupableObject] = tx { t =>

        t.getVertex(getId).pipe.inE(NOTIFICATION)
            .has("_target_userId", forUser.getId)
            .hasNot("notification.groupMerged", true)
            .asInstanceOf[GremPipeEdge]
            .headOption.map { ed =>

            val ids = ed.getOrElse("groupableObjectIds", "").split(",")
            ids.map(_.trim).filter(_.length > 0).map(_.split(":").toList)
                .flatMap { arr =>
                    arr.length match {
                        case 2 =>
                            if (arr.head.toLong == forUser.getId) {
                                User.getById(arr.apply(1).toLong)
                            } else {
                                None
                            }
                        case 4 =>
                            if (arr.apply(1).toLong == forUser.getId) {
                                UserGroup.getById(arr.last.toLong)
                            } else {
                                None
                            }
                        case _ =>
                            None
                    }
                }.toSeq
        }.getOrElse(Seq.empty[GroupableObject])

    }

    override def subGroupId:String = kind + "-" + respondedObjectId

    override def __save__(v: Vertex){
        super.__save__(v)
        generateEmailSession()
    }
}
