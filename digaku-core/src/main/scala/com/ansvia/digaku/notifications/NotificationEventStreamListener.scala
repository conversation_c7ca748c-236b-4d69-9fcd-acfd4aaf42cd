/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications

import com.ansvia.digaku.notifications.impl.JoinNotification

import scala.collection.mutable.ListBuffer

//import impl.PrivateMessageNotification
//import impl.MessageResponseNotification
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.event.impl.{FollowUserEvent, JoinChannelEvent, LikeEvent, ResponseEvent, _}
import com.ansvia.digaku.event.{AsyncEventStreamListener, EventStreamListener, StreamEvent}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.notifications.impl.{LikeNotification, ResponseNotification, SummonNotification, SupportNotification, _}


/**
 * Author: robin
 *
 */

/**
 * Event stream listener yang mengurusi notifikasi.
 */


/**
 * Notification mapper.
 * me-map event ke notifikasi (bisa lebih dari satu notifikasi per event).
 */
trait EventToNotificationMapper {

    protected def debug(msg:String)

    // scalastyle:off cyclomatic.complexity
    def mapEvent(event:StreamEvent):Seq[NotificationBase] = {
        event match {
            case JoinChannelEvent(user, group) =>
                Seq(JoinNotification(user.getId, group.getId))

            case NewRankEvent(user, rank) =>
                Seq(RankNotification(user.getId, rank.name))

            case GiveReputationEvent(user, userTarget, hasCreator) =>
                Seq(GiveReputationNotification(user.getId, userTarget.getId, hasCreator.getVertex.getId.toString.toLong))

            case PromotedThreadEvent(post) =>
                Seq(PromotedThreadNotification(post.getId))

            case AddStaffEvent(user, forum) =>
                Seq(AddStaffNotification(user.getId, forum.getId))

            case ResponseEvent(userResponder, response, responded) =>
                Seq(ResponseNotification(userResponder.getId, response.getId, responded.getId),
                    SummonNotification(userResponder.getId, response.getId))

            case ResponseQuoteEvent(resp) =>
                Seq(QuoteNotification(resp.creator.getId, resp.getId))

            case CreatePostEvent(user, post) =>
                Seq(SummonNotification(user.getId, post.getId),
                    LatestPublicNewsNotification(post.getId))

            case FollowUserEvent(userA, userB) =>
                Seq(SupportNotification(userA.getId, userB.getId))

            case LikeEvent(liker, likableObj) =>

                val likableObjId = likableObj.getVertex.getId.asInstanceOf[Long]
                Seq(LikeNotification(liker.getId, likableObjId))

            case ShoutEvent(user, post, message, shoutEdge) =>

                val postId = post.getVertex.getId.asInstanceOf[Long]
                Seq(ShoutNotification(user.getId, postId))

            case EditArticleEvent(editor, article) =>

                val articleId = article.getVertex.getId.asInstanceOf[Long]
                Seq(EditArticleNotification(editor.getId, articleId))

            case EditEventEvent(editor, edited, _event) =>
                val eventId = _event.getVertex.getId.asInstanceOf[Long]
                Seq(EditEventNotification(editor.getId, edited.mkString(", "), _event.editReason, eventId))

//            /* group member invitation */
//            case ChannelInviteMemberEvent(ch, invitor, invited, inv) =>
//                Seq(ChannelInviteNotification(invitor.getId, invited.getId, ch.getId, inv.code))

//            case ChannelInviteMemberAcceptEvent(invited, invitor, ch) =>
//                Seq(ChannelInviteAcceptNotification(invited.getId, invitor.getId, ch.getId))

//            case ChannelInviteMemberRejectEvent(invited, invitor, ch) =>
//                Seq(ChannelInviteRejectNotification(invited.getId, invitor.getId, ch.getId))
            /* -- */

//            /* group staff invitation */
//            case ChannelInviteStaffEvent(ch, invitor, invited, invCode) =>
//                Seq(BecomeStaffInviteNotification(invitor.getId, invited.getId, ch.getId, invCode.code))

//            case ChannelInviteStaffAcceptEvent(invited, invitor, ch) =>
//                Seq(BecomeStaffAcceptNotification(invited.getId, invitor.getId, ch.getId))

//            case ChannelInviteStaffRejectEvent(invited, invitor, ch) =>
//                Seq(BecomeStaffRejectNotification(invited.getId, invitor.getId, ch.getId))
            /* -- */

//            /* group owner invitation */
//            case ChannelInviteOwnerEvent(ch, invitor, invited, invCode) =>
//                Seq(BecomeOwnerInviteNotification(invitor.getId, invited.getId, ch.getId, invCode.code))

//            case ChannelInviteOwnerAcceptEvent(invited, invitor, ch) =>
//                Seq(BecomeOwnerAcceptNotification(invited.getId, invitor.getId, ch.getId))

//            case ChannelInviteOwnerRejectEvent(invited, invitor, ch) =>
//                Seq(BecomeOwnerRejectNotification(invited.getId, invitor.getId, ch.getId))
            /* -- */

//            /* Support approval invitation */
//            case UserInviteSupportApprovalEvent(invitor, invited, invCode) =>
//                Seq(SupportApprovalInviteNotification(invitor.getId, invited.getId, invCode.code))
//
//            case UserInviteSupportApprovalRejectEvent(invited, invitor) =>
//                Seq(SupportApprovalRejectNotification(invited.getId, invitor.getId))
//
//            case UserInviteSupportApprovalAcceptEvent(invited, invitor) =>
//                Seq(SupportApprovalAcceptNotification(invited.getId, invitor.getId))
            /* -- */

//            case CreatePrivateMessageEvent(user, privateMessage) =>
//                Seq(PrivateMessageNotification(user.getId, privateMessage.getId))
//
//            case CreatePrivateMessageResponseEvent(user, messageResponse, privateMessage) =>
//                Seq(MessageResponseNotification(user.getId, messageResponse.getId, privateMessage.getId))

            case UserCanEditPostEvent(creatorPost, userCanEdit, post) =>
                Seq(AddPostCollaborationNotification(creatorPost.getId, userCanEdit.getId, post.getId))

            case AttenderEvent(_event, attender, attenderKind) =>
                Seq(AttenderEventNotification(_event.getId, attender.getId, attenderKind))

            case EndorseEvent(endorser, endorsement, target) =>
                Seq(EndorsementNotification(endorser.getId, endorsement.getId, target.getId))

//            case ConnectedFriendsFbEvent(user) =>
//                Seq(ConnectWithFacebookNotification(user.getId))

//            case ConnectedFriendsTwEvent(user) =>
//                Seq(ConnectWithTwitterNotification(user.getId))

            case UserHasTrophyEvent(user, trophy) =>
                Seq(UserHasTrophyNotification(user.getId, trophy.getId))

//            case UserGetExtraPointEvent(user, response) =>
//                Seq(UserGetExtraPointNotification(user.getId, response.getId))

//            case CreatePictureGroupEvent(user, picGroup) =>
//                Seq(SummonNotification(user.getId, picGroup.getId))

            case CreatePictureEvent(user, pic) =>
                Seq(SummonNotification(user.getId, pic.getId))

            case LatestForumNewsEvent(post) =>
                Seq(LatestForumNewsNotification( post.getId))

            case x:StartupEvent => Nil

            case x:StreamEvent =>
//                println(s"handle: $x")
//                Thread.currentThread().getStackTrace.foreach(x => println(x))
                _compiledCustomMappers.map(_.apply(x)).getOrElse(Nil)
        }
    }

    var customMappers:ListBuffer[PartialFunction[StreamEvent, Seq[NotificationBase]]] = new ListBuffer[PartialFunction[StreamEvent, Seq[NotificationBase]]]()
    private lazy val _compiledCustomMappers = {
        debug("compiling custom mappers...")
        customMappers.append({ case _ => Nil })
        customMappers.result().reduceOption(_ orElse _)
    }
}

/**
 * Event stream listener yang berguna menterjemahkan
 * digaku event ke dalam bentuk notification.
 */
case class NotificationEventStreamListener() extends AsyncEventStreamListener with Slf4jLogger
    with EventToNotificationMapper with DbAccess {

//    import com.ansvia.digaku.notifications.NotificationEventStreamListener._


    val actorName = "notif"

    def isEventHandled(eventName:String) = true //handledEvents.contains(eventName)


    def asyncDispatch = {
        case event =>
            mapEvent(event).foreach(NotificationSender.send)
    }
}

//object NotificationEventStreamListener {

//    /**
//     * Event-event yang dihandle.
//     */
//    val handledEvents = Array("response", "like", "attender-event",
//        "create-post", "edit-event",
////        "forum-invite-staff", "edit-article", "forum-invite-owner", "create-private-message", "pm-response", "support-user",
////        "post-collaboration", "support-approval", "support-approval-reject", "support-approval-accept",
////        "forum-invite-staff-reject", "forum-invite-staff-accept",
////        "forum-invite-owner-reject", "forum-invite-owner-accept",
////        "forum-invite-member-accept", "forum-invite-member-reject",
//        "join-forum", "new-rank", "give-reputation", "add-staff", "promoted-thread", "user-has-trophy"
////        "endorse", "shout", "connect-friends-fb", "connect-friends-tw",
////        "user-extra-point", "create-picture", "create-picture-group"
//    )
//}

/**
 * Just like [[com.ansvia.digaku.notifications.NotificationEventStreamListener]]
 * but in blocking mode, useful for testing, prevent multi threading debug headache.
 */
case class BlockingNotificationEventStreamListener() extends EventStreamListener with Slf4jLogger
    with EventToNotificationMapper with DbAccess {

//    import com.ansvia.digaku.notifications.NotificationEventStreamListener._

    def isEventHandled(eventName: String) = true //handledEvents.contains(eventName)

    def dispatch = {
        case event =>
            mapEvent(event).foreach(NotificationSender.send)
    }
}


