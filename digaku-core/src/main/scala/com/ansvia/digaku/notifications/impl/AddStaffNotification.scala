package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.model.{Forum, User}
import com.ansvia.digaku.notifications._
import com.ansvia.graph.BlueprintsWrapper._
import NotifUtil._

/**
 * Author: nadir (<EMAIL>)
 *
 */
case class AddStaffNotification(userId:Long, forumId:Long)
    extends PersistentNotification(NotifKind.ADD_MODERATOR) with EmailSession {

    override val partition: Int =  NotifPartition.LEVEL_1_NOTICE

    lazy val user = {
        val v = db.getVertex(userId)
        if (v != null)
            v.toCC[User]
        else
            None
    }

    lazy val forum = {
        val v = db.getVertex(forumId)
        if (v != null)
            v.toCC[Forum]
        else
            None
    }

    /**
     * Untuk pengecheckan apakah user menerima notification ini.
     * @param user user yang akan dicheck.
     * @return
     */
    override def userAccept(user: User): Boolean = true

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    override def isSupport(np: NotificationSendHandler): Boolean = true

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * Return true apabila implementsi bisa di-group-kan.
     * Sebagai contoh lihat [[ResponseNotification]]
     * @return
     */
    override def isGroupable: Boolean = false

    override def isValid: Boolean = user.isDefined && forum.isDefined

    /**
     * Render notification untuk user :user.
     * @param user user yang akan membaca.
     * @return
     */
    override def renderFor(user: User): String = {
        if (isValid)
            "Anda telah diangkat sebagai moderator di #%s".format(stdForumNotifStr(forum.get))
        else
            "----"
    }

    /**
     * get default receivers for this notification.
     */
    override def getDefaultReceivers = {
        user.toSeq
    }


}
