/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications

import com.ansvia.graph.{AbstractIDGetter, AbstractDbObject}
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.digaku.helpers.DbAccess
import com.tinkerpop.blueprints.Vertex
import com.ansvia.commons.logging.Slf4jLogger
import java.util.concurrent.atomic.AtomicLong
import java.util.{UUID, Random}
import com.ansvia.digaku.Types.IDType
import com.ansvia.digaku.model.VertexLabels

/**
 * Author: robin
 * Date: 3/10/14
 * Time: 5:10 PM
 *
 * Email session ini digunakan oleh notification yang support pengiriman
 * via email, dimana email tersebut bisa di-reply oleh user dan
 * diproses oleh digaku-mail-handler.
 *
 */
trait EmailSession extends AbstractDbObject with AbstractIDGetter[IDType] with <PERSON>lf4j<PERSON><PERSON><PERSON> {



    /**
     * Dapatkan email session id.
     * @return
     */
    def getEmailSession = getVertex.getOrElse("email.session.v2", "")


    /**
     * Set email session id.
     * @param sessionId email session id.
     */
    def setEmailSession(sessionId:String){
        getVertex.setProperty("email.session.v2", sessionId)
    }

    /**
     * Generate email session,
     * ini akan otomatis men-set `email.session.v2` apabila belum diset,
     * aman untuk dipanggil berkali-kali.
     * @return
     */
    def generateEmailSession() = {
        val es = getEmailSession
        val id =
        if (es == ""){
            val rv = EmailSession.generateToken()
            debug("email session generated: " + rv)
            setEmailSession(rv)
            rv
        }else{
            es
        }
        id
    }

}


object EmailSession extends DbAccess {

    import scala.collection.JavaConversions._

    @transient
    private val STRIPER_RE = """\W+""".r
//    private val atomicIdx = new AtomicLong(1L)
    private val sha1 = java.security.MessageDigest.getInstance("SHA-1")
//    private val rnd = new Random()

    def generateToken() = {

        val data = (System.currentTimeMillis().toString + UUID.randomUUID().toString).getBytes
        var rv = new sun.misc.BASE64Encoder()
            .encode(sha1.digest(data))

        rv = STRIPER_RE.replaceAllIn(rv, "")

        if (rv.length > 20)
            rv = rv.substring(0,20)

        rv

    }

    def get(sessionId:String):Option[Vertex] = {
        db.query().has("label", VertexLabels.NOTIFICATION)
            .has("email.session.v2", sessionId).vertices.headOption
    }
}
