/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications

import com.ansvia.digaku.Digaku
import akka.actor.{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Actor}
import com.ansvia.perf.Throttle
import com.ansvia.util.duration._


/**
 * Author: robin
 *
 */
abstract class AsyncNotificationSendHandler extends NotificationSendHandler with Throttle {
    // how long duplicated notification will be avoided
    protected val throttleDuration = 12.hours

    private class Worker extends Actor {
        def receive = {
            case ntf:PersistentNotification =>
                if (!ntf.hash.isEmpty) {
                    throttle(ntf.hash, 1, throttleDuration) {
                        asyncDispatch.apply(ntf)
                    }
                } else {
                    asyncDispatch.apply(ntf)
                }
            case ntf:NonPersistentNotification =>
                if (!ntf.hash.isEmpty) {
                    throttle(ntf.hash, 1, throttleDuration) {
                        asyncDispatch.apply(ntf)
                    }
                } else {
                    asyncDispatch.apply(ntf)
                }
        }
    }

    lazy val worker = Digaku.engine.actorSystem.actorOf(Props(new Worker))

    final def dispatch:PartialFunction[NotificationBase, Unit] = {
        case ntf:NotificationBase => worker ! ntf
    }

    protected def asyncDispatch:PartialFunction[NotificationBase, Unit]

    def close() {
        worker ! PoisonPill
    }
}


/**
 * Async notification handlers yang dikelompokkan,
 * Ini berguna apabila dikombinasikan dengan [[com.ansvia.digaku.notifications.GroupSequentialNotificationSendHandlers]]
 * Dimana ada yang diurutkan tapi tetap ingin menjaga yang lainnya async setelahnya.
 * @param handlers notification-notification handlers yang dikelompokkan.
 */
case class GroupAsyncNotificationSendHandlers(private val handlers:AsyncNotificationSendHandler*) extends AsyncNotificationSendHandler {

    override val name: String = "group-async-notif-send-handlers"

    override protected def asyncDispatch: PartialFunction[NotificationBase, Unit] = {
        case ntf =>
            synchronized {
                _internalHandlers.foreach { nh =>
                    if ((ntf.getFlags | NotifFlags.DONT_PROCESS) != ntf.getFlags) {
                        nh.dispatch(ntf)
                    }
                }
            }
    }

    private var _internalHandlers = handlers

    def addHandlers(_handlers:AsyncNotificationSendHandler*) = {
        synchronized {
            _internalHandlers = _internalHandlers ++ _handlers
        }
        this
    }
}
