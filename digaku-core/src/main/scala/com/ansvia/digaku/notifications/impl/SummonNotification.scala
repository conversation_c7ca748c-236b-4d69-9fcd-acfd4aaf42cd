/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.notifications.impl

import com.ansvia.digaku.model._
import com.ansvia.digaku.notifications._
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.utils.TextExtractor
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Article
import com.tinkerpop.blueprints.Vertex
import com.ansvia.digaku.Types.IDType
import NotifUtil._

/**
* Author: nadir, robin
*
*/

/**
 * Implementasi notifikasi untuk summon
 * @param userId id user yang men-summon.
 * @param objId object dimana user men-summon.
 */
case class SummonNotification(userId:Long, objId:Long)
    extends PersistentNotification(NotifKind.SUMMON) with DbAccess with EmailSession {

    import com.ansvia.graph.BlueprintsWrapper._

    val partition = NotifPartition.CONVERSATION


    lazy val user:Option[User] = {
        val v = db.getVertex(userId)
        if (v == null)
            None
        else
            v.toCC[User]
    }

    /**
     * Object dimana user di-summon.
     */
    lazy val obj:Option[BaseModel[IDType]] = {
        val v = db.getVertex(objId)
        if (v == null)
            None
        else
            v.getOrElse("_class_", "") match {
                case "com.ansvia.digaku.model.Response" =>
                    v.toCC[Response]
//                case "com.ansvia.digaku.model.SimplePost" =>
//                    v.toCC[SimplePost]
                case "com.ansvia.digaku.model.Article" =>
                    v.toCC[Article]
//                case "com.ansvia.digaku.model.PictureGroup" =>
//                    v.toCC[PictureGroup]
                case "com.ansvia.digaku.model.Picture" =>
                    v.toCC[Picture]
                case _ =>
                    None
            }
    }

    /**
     * Check apakah mendukung suatu handler.
     * @param np notification handler.
     * @return
     */
    def isSupport(np: NotificationSendHandler) = true

    /**
     * Check apakah notification ini bisa
     * di-group-kan.
     * @return
     */
    def isGroupable = false

    /**
     * Render notification untuk user :user.
     * @param _user user yang akan membaca.
     * @return
     */
    def renderFor(_user: User) = {
        if (!isValid)
            throw new DigakuException("notification not valid, cannot render.")

        val stdNotifStr = obj.map {
            case userGroupable: PostAsUserGroupable =>
                userGroupable.getPostAs.map { pa =>
                    stdUserGroupNotifStr(pa)
                }.getOrElse(stdUserNotifStr(user.get))

            case _ =>
                stdUserNotifStr(user.get)
        }.getOrElse(stdUserNotifStr(user.get))

        "%s menyebut Anda di dalam post".format(stdNotifStr)
    }

    // di-comment out karena gak ada bedanya dengan `renderFor` jadi kenapa dibuat lagi?
//    /**
//     * Render notification untuk user yang di summon pada respond.
//     * @param _user user yang akan membaca.
//     * @return
//     */
//    def renderForRespond(_user: User) = {
//        if (!isValid)
//            throw new DigakuException("notification not valid, cannot render.")
//
//        "$[user:%s;%s] talked about you on a %s".format(user.get.name, user.get.getId, linkableObject)
//    }

    private def linkableObject = {
        obj.map {
            case resp: Response =>
                stdResponseNotifStr(resp)
//            case sp: SimplePost =>
//                "$[simple-post:%s;%s;%s]".format(sp.getId, sp.origin.kind, sp.origin.getName)
            case p: Article =>
                stdPostNotifStr(p)
//            case p: PictureGroup =>
//                "$[picture-group:%s;%s;%s]".format(p.getId, p.origin.kind, p.origin.getName)
            case p: Picture =>
                "$[picture:%s;%s;%s]".format(p.getId, p.origin.kind, p.origin.getName)
        }.getOrElse("---")
    }

    def isValid: Boolean = user.isDefined && obj.isDefined

    def userAccept(user: User): Boolean = true

    /**
     * Extract user group and get members
     * @param content
     * @return
     */
    def getUserGroupMembers(content:String):Seq[User] = {
        TextExtractor.extractUserGroupIds(content).toSeq.distinct.flatMap(id => UserGroup.getById(id))
            .flatMap(ug => ug.getMembers(None, None, ug.getMemberCount()))
    }

    override def getDefaultReceivers = {
        (obj map {
            case Response(content) =>
                TextExtractor.extractUserIds(content).flatMap(id => User.getById(id)) ++ getUserGroupMembers(content)
//            case SimplePost(content) =>
//                TextExtractor.extractUserNames(content).flatMap(User.getByName)
            case article:Article =>
                val content = article.getVertex.getOrElse("contentHtml", "&lt;html content&gt;")
                TextExtractor.extractUserIds(content).flatMap(id => User.getById(id)) ++ getUserGroupMembers(content)
//            case PictureGroup(title) =>
//                TextExtractor.extractUserNames(title).flatMap(User.getByName)
            case Picture(title) =>
                TextExtractor.extractUserIds(title).flatMap(id => User.getById(id)) ++ getUserGroupMembers(title)
        } getOrElse Nil).toSeq.distinct.filterNot { u =>
            u == user.get || u.isBlocked(user.get)
        }
    }

    override def __save__(v: Vertex){
        super.__save__(v)
        generateEmailSession()
    }
}

