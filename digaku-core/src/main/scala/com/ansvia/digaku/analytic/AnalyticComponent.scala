/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.analytic

import com.ansvia.digaku.event.AsyncEventStreamListener
import com.ansvia.digaku.persistence.CassandraDriver

/**
 * Author: robin (<EMAIL>)
 */

trait AnalyticComponent {

    val analytic:Analytic

    trait Analytic {

        val cassandraDriver:CassandraDriver[String, java.lang.Long]
        val listener:AsyncEventStreamListener

    }
}
