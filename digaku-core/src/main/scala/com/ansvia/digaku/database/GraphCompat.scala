/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.database

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types.IDType
import com.ansvia.digaku.config.Config
import com.ansvia.digaku.exc.{DigakuException, NotSupportedException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.BaseModel
import com.ansvia.graph.AbstractDbObject
import com.thinkaurelius.titan.core.{TitanGraph, TitanTransaction}
import com.tinkerpop.blueprints.TransactionalGraph
import com.tinkerpop.blueprints.util.wrappers.id.IdGraph

import scala.reflect.ClassTag
import scala.collection.JavaConversions._
import com.ansvia.graph.BlueprintsWrapper._

object GraphCompat extends Slf4jLogger with DbAccess {


//    /**
//     * Nge-wrap transaction yang ada di Blueprints Scala.
//     * support transaction apabila support tapi jadi gak
//     * ngelakuin apa-apa apabila emang database-nya gak transactional.
//     * @param wrappedFunc
//     * @tparam T
//     * @return
//     */
//    @deprecated("jangan gunakan ini lagi, gunakan tx atau db.commit() sesuaikan dengan kebutuhannya", "12 Maret 2015")
//    def transact[T](wrappedFunc: => T):T = {
//
//        db match {
//            case dbt:TransactionalGraph =>
//                com.ansvia.graph.BlueprintsWrapper.transact {
//                    wrappedFunc
//                }(dbt)
//            case x =>
//                throw NotSupportedException("no transactional graph, cannot apply.")
//        }
//
//    }


    def tx[T](wrappedFunc: IdGraph[TitanTransaction] => T):T = {
        import com.ansvia.graph.IdGraphTitanDbWrapper._

        if (Config.testMode)
            wrappedFunc(db.asInstanceOf[IdGraph[TitanTransaction]])
        else
            db match {
                case dbt:IdGraph[TitanGraph] =>
                    dbt.transactIdGraph(Digaku.engine.idFactory){ trx =>
                        wrappedFunc(trx)
                    }
                case x =>
                    throw NotSupportedException(s"not supported tx db: $x")
            }
    }




    def getFromTx[T <: IDGetter[IDType] : ClassTag](model: T, t:IdGraph[TitanTransaction]):T = {
        getFromTx[T](model.getId, t)
    }

    def getFromTx[T : ClassTag](id:IDType, t:IdGraph[TitanTransaction]):T = {
        val v = t.getVertex(id)
        if (v != null){
            v.toCC[T] match {
                case Some(d) => d
                case _ =>
                    throw new DigakuException(s"Cannot convert vertex with id $id to model (toCC) for getting data from scoped tx")
            }
        }else{
            throw new DigakuException(s"Cannot get vertex with id $id for getting data from scoped tx")
        }
    }

    def txObj[T <: BaseModel[IDType] : ClassTag, R](obj:T)(func: T => R):R = tx { t =>
        func(getFromTx(obj, t))
    }



//    /**
//     * Digunakan untuk transaction di environment threaded.
//     * @param wrappedFunc
//     * @tparam T
//     * @return
//     */
//    def threadedTransact[T](wrappedFunc: GraphType => T):T = {
//
//        db match {
//            case dbt:TransactionalGraph =>
//
//                dbt match {
//                    case idg:IdGraph[TitanGraph] =>
//                        val ttg = idg.getBaseGraph.newTransaction()
//                        val newIdg:GraphType = new IdGraph[TitanTransaction with KeyIndexableGraph](ttg, true, false)
//                        val rv = wrappedFunc(newIdg)
//                        ttg.shutdown()
//                        rv
//                    case ttg:ThreadedTransactionalGraph =>
//                        val newIdg:GraphType = new IdGraph[ThreadedTransactionalGraph
//                            with KeyIndexableGraph](ttg, true, false)
//                        val rv = wrappedFunc(newIdg)
//                        ttg.shutdown()
//                        rv
//                    case x =>
//                        throw NotSupportedException("only support IdGraph with TitanGraph or " +
//                            "ThreadedTransactionalGraph for threaded transaction")
//                }
//
//            case x =>
//                throw new NotSupportedException("no transactional graph, cannot apply.")
//        }
//
//    }




}
