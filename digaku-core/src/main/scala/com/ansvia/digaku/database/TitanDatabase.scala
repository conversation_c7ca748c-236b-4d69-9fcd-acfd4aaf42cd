/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */
package com.ansvia.digaku.database

import java.util.concurrent.TimeUnit

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types._
import com.ansvia.digaku.model.VertexLabels
import com.ansvia.digaku.persistence.CassandraDriver
import com.thinkaurelius.titan.core._
import com.thinkaurelius.titan.core.attribute.Decimal
import com.thinkaurelius.titan.core.schema.TitanManagement
import com.tinkerpop.blueprints.util.wrappers.id.IdGraph
import com.tinkerpop.blueprints.util.wrappers.id.IdGraph.IdFactory
import com.tinkerpop.blueprints.{Edge, Vertex}
import org.apache.commons.configuration.PropertiesConfiguration
import com.ansvia.digaku.model.Label._
import com.ansvia.graph.IdGraphTitanDbWrapper._
import com.thinkaurelius.titan.core.Order._
import com.tinkerpop.blueprints.Direction._

/**
 * Interface untuk Titan database.
 */
class TitanDatabase(config:PropertiesConfiguration, idFactory:IdFactory, useIdGraph:Boolean=false) extends DigakuDatabase {

    protected implicit val db:GraphType = openDatabase()

    val isSharedThread = false

    private def openDatabase():GraphType = {

        if (config.getString("storage.backend") == "cassandra"){

            val strategyOpts = config.getStringArray("storage.cassandra.replication-strategy-options")
            val strategyOptsStr = strategyOpts.toList.grouped(2).map(a => s"${a(0)}:${a(1)}").mkString(",")

            // pastikan keyspace yang digunakan Titan telah di-pre-setup,
            // karena kita ingin memastikan settingan kita sendiri untuk keyspace-nya.
            // jangan biarkan Titan membuatkannya otomatis.
            // Sebenarnya ini sudah tidak dibutuhkan pada Titan 0.5
            val cassCtx =
                CassandraDriver.getContext(config.getString("storage.cluster-name"),
                    config.getString("storage.cassandra.keyspace"), config.getString("storage.hostname"),
                    config.getString("storage.cassandra.replication-strategy-class"),
                    strategyOptsStr
                )

            cassCtx.ensureKeyspaceExists(config.getString("storage.cassandra.keyspace"))
            /// end of compatibility mode -----------------------------------------------------------

            require(config.containsKey("storage.cassandra.replication-strategy-options"),
                "No storage.cassandra.replication-strategy-options defined, " +
                    "please define it first in your config file")

            config.setProperty("storage.cassandra.thrift.frame_size_mb", 512)
            config.setProperty("storage.cassandra.astyanax.connection-pool-type", "ROUND_ROBIN")
        }

        // perlu ini karena menyebabkan error
        // ini bisa dijadikan referensi: https://github.com/thinkaurelius/titan/issues/711
        System.setProperty("titan.load.cfg.opts", "false")

        val titanDb = TitanFactory.open(config)

        //        // pre-index for IdGraph.ID
        //        val IDKey = titanDb.getType(IdGraph.ID)
        //        if (IDKey == null){
        //            titanDb.makeKey(IdGraph.ID).single().unique().indexed(classOf[Vertex]).dataType(classOf[java.lang.Long]).make()
        //        }

        val mgmt = titanDb.getManagementSystem
        if (mgmt.getGraphIndex("IDGraphId") == null){
            val k1 = mgmt.makePropertyKey(IdGraph.ID).dataType(classOf[java.lang.Long]).make()
            mgmt.buildIndex("IDGraphId",classOf[Vertex]).addKey(k1).unique().buildCompositeIndex()
        }
        mgmt.commit()

        // @TODO(robin): fix this, untuk sementara kita hanya mensupport IdGraph tidak lainnya
        //        useIdGraph match {
        //            case true =>
        val rv = new IdGraph(titanDb, true, false)
        rv.setVertexIdFactory(idFactory)
        rv

    }

    /**
     * WARNING: index ini hanya boleh dipanggil sekali dalam satu installasi
     * mengapa? Karena bisa menyebabkan error apabila dieksekusi lebih
     * dari satu kali oleh lebih dari satu aplikasi.
     */
    override def index(){

        info("Indexing database types...")

        val TWO = 2
        val THREE = 3
        val FOUR = 4

        val _db = db.getBaseGraph

        val mgmt: TitanManagement = _db.getManagementSystem

        val storageBackend = config.getString("storage.backend")
        val supportTTL = storageBackend == "cassandra" || storageBackend == "hbase"

        /************************************************
          * Build labels
          ***********************************************/
        def getEnsureVertexLabel(lbl:String) = {
            if (!mgmt.containsVertexLabel(lbl)) {
                mgmt.makeVertexLabel(lbl).make()
            }else {
                mgmt.getVertexLabel(lbl)
            }
        }

        val USER = getEnsureVertexLabel(VertexLabels.USER)
        val FORUM_LBL = getEnsureVertexLabel(VertexLabels.FORUM)
        val TROPHY = getEnsureVertexLabel(VertexLabels.TROPHY)
        val APP = getEnsureVertexLabel(VertexLabels.APP)
        val THIRDPARTY_CONNECT = getEnsureVertexLabel(VertexLabels.THIRDPARTY_CONNECT)
        val THIRDPARTY_ACCOUNT_LBL = getEnsureVertexLabel(VertexLabels.THIRDPARTY_ACCOUNT)
        val CHANNEL_GALLERY = getEnsureVertexLabel(VertexLabels.CHANNEL_GALLERY)
        val CONTENT = getEnsureVertexLabel(VertexLabels.CONTENT)
        val CATEGORY_INTEREST = getEnsureVertexLabel(VertexLabels.CATEGORY_INTEREST)
        val POSTMARK = getEnsureVertexLabel(VertexLabels.POSTMARK)
        val EMBEDDED_OBJECT_LBL = getEnsureVertexLabel(VertexLabels.EMBEDDED_OBJECT)
        val PM_LBL = getEnsureVertexLabel(VertexLabels.PM)
        val NOTIFICATION_LBL = getEnsureVertexLabel(VertexLabels.NOTIFICATION)
        val STATS_LBL = getEnsureVertexLabel(VertexLabels.STATS)
        val GROUPABLE_LBL = getEnsureVertexLabel(VertexLabels.GROUP)
        val USER_GROUP_LBL = getEnsureVertexLabel(VertexLabels.USER_GROUP)
        val USER_LEADERBOARD_LBL = getEnsureVertexLabel(VertexLabels.USER_LEADERBOARD)


        /************************************************
          * PROPERTY KEY DEFINITION
          ***********************************************/

        val idh = new IdxHelper(mgmt)


        val fbIdKey = idh.getOrCreatePropKey("fb.id", classOf[java.lang.String])

        idh.ifGraphIdxNotExists("USERbyFbId"){ name =>
            mgmt.buildIndex(name,classOf[Vertex]).addKey(fbIdKey).indexOnly(USER).unique().buildCompositeIndex()
        }
        idh.ifGraphIdxNotExists("FBINFObyFbId"){ name =>
            mgmt.buildIndex(name,classOf[Vertex]).addKey(fbIdKey).indexOnly(THIRDPARTY_ACCOUNT_LBL).unique().buildCompositeIndex()
        }

        val fbAccessTokenKey = idh.getOrCreatePropKey("fb.access_token", classOf[java.lang.String])

        idh.ifGraphIdxNotExists("USERbyFBAccessToken"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(fbAccessTokenKey).indexOnly(USER).buildCompositeIndex()
        }

        val twIdKey = idh.getOrCreatePropKey("tw.id", classOf[java.lang.String])


        idh.ifGraphIdxNotExists("USERbyTWId"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(twIdKey).indexOnly(USER).unique().buildCompositeIndex()
        }
        idh.ifGraphIdxNotExists("TWINFObyTWId"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(twIdKey).indexOnly(THIRDPARTY_ACCOUNT_LBL).unique().buildCompositeIndex()
        }

        val twATKey = idh.getOrCreatePropKey("tw.access_token", classOf[java.lang.String])

        idh.ifGraphIdxNotExists("byTWAccessToken"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(twATKey).indexOnly(USER).unique().buildCompositeIndex()
        }
        val userEmailKey = idh.getOrCreatePropKey("user.email", classOf[java.lang.String])

        idh.ifGraphIdxNotExists("USERbyEmail"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(userEmailKey).indexOnly(USER).unique().buildCompositeIndex()
        }

        val userMobilePhoneKey = idh.getOrCreatePropKey("user.mobilePhone", classOf[java.lang.String])

        idh.ifGraphIdxNotExists("byUserMobilePhone"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(userMobilePhoneKey).indexOnly(USER).unique().buildCompositeIndex()
        }

        val trophyNameKey = idh.getOrCreatePropKey("trophy.name", classOf[java.lang.String])

        idh.ifGraphIdxNotExists("byTrophyName"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(trophyNameKey).indexOnly(TROPHY).unique().buildCompositeIndex()
        }
        val appLowerNameKey = idh.getOrCreatePropKey("app.lower-name", classOf[java.lang.String])

        idh.ifGraphIdxNotExists("byAppLowerName"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(appLowerNameKey).indexOnly(APP).unique().buildCompositeIndex()
        }
        idh.ifGraphIdxNotExists("byUserConnectFbId"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(fbIdKey).indexOnly(THIRDPARTY_ACCOUNT_LBL).buildCompositeIndex()
        }

        idh.ifGraphIdxNotExists("byUserConnectTwId"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(twIdKey).indexOnly(THIRDPARTY_ACCOUNT_LBL).buildCompositeIndex()
        }

        val appClientIdKey = idh.getOrCreatePropKey("app.client-id", classOf[java.lang.Long])
        idh.ifGraphIdxNotExists("byAppClientId"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(appClientIdKey).indexOnly(APP).unique().buildCompositeIndex()
        }

        val appClientSecretKey = idh.getOrCreatePropKey("app.client-secret", classOf[java.lang.String])
        idh.ifGraphIdxNotExists("byAppClientSecret"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(appClientSecretKey).indexOnly(APP).unique().buildCompositeIndex()
        }

        val devATKey = idh.getOrCreatePropKey("app.dev-access-token", classOf[java.lang.String])
        idh.ifGraphIdxNotExists("byAppDevAccessToken"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(devATKey).indexOnly(APP).buildCompositeIndex()
        }

        val targetIdKey = idh.getOrCreatePropKey("targetId", classOf[java.lang.Long])

        // used in JOIN and SUPPORT
        val sourceIdKey = idh.getOrCreatePropKey("sourceId", classOf[java.lang.Long])
        val creatorIdKey = idh.getOrCreatePropKey("creatorId", classOf[java.lang.Long])
        // Leaderboard Point
        val lbPointKey = idh.getOrCreatePropKey("lbPoint", classOf[java.lang.Integer])


        val lowerNameKey = idh.getOrCreatePropKey("lowerName", classOf[java.lang.String])


        idh.ifGraphIdxNotExists("USERbyLowerName"){ name =>
            val userLowerNameKey = idh.getOrCreatePropKey("user.lower-name", classOf[java.lang.String])
            mgmt.buildIndex(name, classOf[Vertex]).addKey(userLowerNameKey).indexOnly(USER).unique().buildCompositeIndex()
        }
        idh.ifGraphIdxNotExists("CHANNELbyLowerName"){ name =>
            val channelLowerNameKey = idh.getOrCreatePropKey("forum.lower-name", classOf[java.lang.String])
            mgmt.buildIndex(name, classOf[Vertex]).addKey(channelLowerNameKey).indexOnly(FORUM_LBL).unique().buildCompositeIndex()
        }

        idh.ifGraphIdxNotExists("UserGroupByLowerName"){ name =>
            val userGroupKey = idh.getOrCreatePropKey("user-group.lower-name", classOf[java.lang.String])
            mgmt.buildIndex(name, classOf[Vertex]).addKey(userGroupKey).indexOnly(USER_GROUP_LBL).unique().buildCompositeIndex()
        }

        idh.ifGraphIdxNotExists("UserLeaderboardByLowerName"){ name =>
            val leaderboardKey = idh.getOrCreatePropKey("leaderboardName", classOf[java.lang.String])
            mgmt.buildIndex(name, classOf[Vertex]).addKey(leaderboardKey).indexOnly(USER_LEADERBOARD_LBL).unique().buildCompositeIndex()
        }

        // used in JOIN and SUPPORT
        val targetNameKey = idh.getOrCreatePropKey("targetName", classOf[java.lang.String])

        idh.ifGraphIdxNotExists("byTargetName"){ name =>
            mgmt.buildIndex(name,classOf[Edge]).addKey(targetNameKey).buildCompositeIndex()
        }

        idh.ifGraphIdxNotExists("byReadTriggerEventRef"){ name =>
            val readTriggerEventRefKey = idh.getOrCreatePropKey("readTriggerEventRef", classOf[java.lang.Long])
            mgmt.buildIndex(name,classOf[Edge]).addKey(readTriggerEventRefKey).buildCompositeIndex()
        }

        // used in JOIN nad SUPPORT
        val sourceNameKey = idh.getOrCreatePropKey("sourceName", classOf[java.lang.String])

        idh.ifGraphIdxNotExists("bySourceName"){ name =>
            mgmt.buildIndex(name,classOf[Edge]).addKey(sourceNameKey).buildCompositeIndex()
        }

        val deletedKey = idh.getOrCreatePropKey("deleted", classOf[java.lang.Boolean])

        // digunakan di edges: PUBLISH, REVISION, DAO_LIST, EVENT_REMINDER, EMAIL_DRAFT,
        // HAS_PICTURE, SHOUT, RETALK, CREATE_AD, NOTIFICATION, WROTE, CREATE, ORIGIN, PUBLISH_CONTENT
        // STREMA, PM_STREAM, CLICK_AD, HAS_TROPHY, CREATE_STATUS, AD_CLICKER, VISIT_URL, HAS_PROFILE_PICTURE,
        // ALBUM, BOOKMARK_CHANNEL, REPLY_OF,
        // COLLECTION_VERTEX
        val timeOrderKey = idh.getOrCreatePropKey("timeOrder", classOf[java.lang.Long])

        val countKey = idh.getOrCreatePropKey("count", classOf[java.lang.Long])

        val notifKindKey = idh.getOrCreatePropKey("notifKind", classOf[java.lang.Integer])

        val notifIdKey = idh.getOrCreatePropKey("notifId", classOf[java.lang.Long])

        // digunakan di NOTIFICATION
        val targetUserIdKey = idh.getOrCreatePropKey("_target_userId", classOf[java.lang.Long])

        val groupMergedKey = idh.getOrCreatePropKey("notification.groupMerged", classOf[java.lang.Boolean])

        val notifPartitionKey = idh.getOrCreatePropKey("notification.partition", classOf[java.lang.Integer])

        idh.ifGraphIdxNotExists("NotifPartition"){ name =>
            mgmt.buildIndex(name,classOf[Edge]).addKey(notifPartitionKey).buildCompositeIndex()
        }

        val galleryLowerNameKey = idh.getOrCreatePropKey("gallery.lower-name", classOf[java.lang.String])

        idh.ifGraphIdxNotExists("byGalleryLowerName"){ name =>
            // ini unique karena ketika di-set dia ada prefix group id-nya, jadi tidak mungkin collision
            mgmt.buildIndex(name,classOf[Vertex]).addKey(galleryLowerNameKey)
                .indexOnly(CHANNEL_GALLERY).unique().buildCompositeIndex()
        }

//        idh.ifGraphIdxNotExists("byGroupCategoryName"){ name =>
//            val k1 = idh.getOrCreatePropKey("group-category.name", classOf[java.lang.String])
//            mgmt.buildIndex(name, classOf[Vertex]).addKey(k1).indexOnly(CATEGORY_INTEREST).unique().buildCompositeIndex()
//        }

        // used in edges: STREAM
        val postKindKey = idh.getOrCreatePropKey("post.kind", classOf[java.lang.Integer])

        val postStickKey = idh.getOrCreatePropKey("post.sticky", classOf[java.lang.Boolean])

        val postMarkKey = idh.getOrCreatePropKey("post.mark", classOf[java.lang.String])

        val postMarkTitleKey = idh.getOrCreatePropKey("post-mark.title", classOf[java.lang.String])

        val adLowerTitleKey = idh.getOrCreatePropKey("ad.lower-title", classOf[java.lang.String])

        val creationTimeKey = idh.getOrCreatePropKey("creationTime", classOf[java.lang.Long])

        val yearKey = idh.getOrCreatePropKey("year", classOf[java.lang.Integer])

        val monthKey = idh.getOrCreatePropKey("month", classOf[java.lang.Integer])

        val offsetKey = idh.getOrCreatePropKey("offset", classOf[java.lang.Long])

        // used in edges: COLLECTION_VERTEX, INVITE, DAO_LIST
        // vertices: EmbedLink
        val kindKey = idh.getOrCreatePropKey("kind", classOf[java.lang.Integer])

        val streamKindKey = idh.getOrCreatePropKey("streamKind", classOf[java.lang.Integer])

        val _class_Key = idh.getOrCreatePropKey("_class_", classOf[java.lang.String])

        idh.ifGraphIdxNotExists("by_class_"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(_class_Key).buildCompositeIndex()
        }

//        val moidKey = idh.getOrCreatePropKey("moid", classOf[java.lang.String])
//        idh.ifGraphIdxNotExists("bymoid"){ name =>
//            mgmt.buildIndex(name, classOf[Vertex]).addKey(moidKey).buildCompositeIndex()
//        }

        val lastWroteKey = idh.getOrCreatePropKey("lastWrote", classOf[java.lang.Long])
        idh.ifGraphIdxNotExists("lastWrote"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(lastWroteKey).indexOnly(CONTENT).buildCompositeIndex()
        }

        idh.ifGraphIdxNotExists("byCount") { name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(countKey).buildCompositeIndex()
        }

        // used in edges: POPULAR, PYMK
        // type data score adalah Double
        val scoreKey = idh.getOrCreatePropKey("score", classOf[Decimal])
        val keyIdentifierLongKey = idh.getOrCreatePropKey("keyIdentifierLong", classOf[java.lang.Long])

        /**
         * index ini digunakan untuk menyimpan informasi sesi dalam email,
         * sebagai contoh apabila ada user melakukan response ke sebuah post
         * maka beberapa user akan mendapatkan notification, dimana pada notification
         * tersebut terdapat juga informasi email session ini yang digunakan
         * untuk user membalas respon melalui email, dimana session ini
         * menjadikan key bagi smtp email receiver bahwa user melakukan aksi
         * ke sesi notif tersebut.
         */
        val emailSessionKey = idh.getOrCreatePropKey("email.session.v2", classOf[java.lang.String])

        idh.ifGraphIdxNotExists("NOTIFICATIONbyEmailSessionV2"){ name =>
            mgmt.buildIndex(name,classOf[Vertex]).addKey(emailSessionKey)
                .indexOnly(NOTIFICATION_LBL).unique().buildCompositeIndex()
        }

        idh.ifGraphIdxNotExists("byUserLowerName"){ name =>
            mgmt.buildIndex(name,classOf[Vertex]).addKey(lowerNameKey).indexOnly(USER).unique().buildCompositeIndex()
        }
        idh.ifGraphIdxNotExists("byChannelLowerName"){ name =>
            mgmt.buildIndex(name,classOf[Vertex]).addKey(lowerNameKey)
                .indexOnly(FORUM_LBL).unique().buildCompositeIndex()
        }

        idh.ifGraphIdxNotExists("byGalleryLowerName"){ name =>
            mgmt.buildIndex(name,classOf[Vertex]).addKey(lowerNameKey).indexOnly(CHANNEL_GALLERY).buildCompositeIndex()
        }

        /************************************************
          * INDEX FOR EDGE LABELS
          ***********************************************/




        val daoListLbl = idh.ifEdgeLabelNotExists(DAO_LIST){ name =>
            mgmt.makeEdgeLabel(DAO_LIST).sortKey(timeOrderKey).sortOrder(DESC).make()
        }
        idh.ifRelationIdxNotExists(DAO_LIST, "DAO_LISTbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(daoListLbl, name, BOTH, DESC, timeOrderKey)
        }
        idh.ifRelationIdxNotExists(DAO_LIST, "DAO_LISTbyTimeOrder2"){ name =>
            mgmt.buildEdgeIndex(daoListLbl, name, BOTH, DESC, timeOrderKey, targetIdKey)
        }
        idh.ifRelationIdxNotExists(DAO_LIST, "DAO_LISTbyTimeOrder3"){ name =>
            mgmt.buildEdgeIndex(daoListLbl, name, BOTH, DESC, targetIdKey)
        }
        idh.ifRelationIdxNotExists(DAO_LIST, "DAO_LISTbyTimeOrder4"){ name =>
            mgmt.buildEdgeIndex(daoListLbl, name, BOTH, DESC, kindKey)
        }


        val eventReminderLbl = idh.getEnsureEdgeLabel(EVENT_REMINDER)
        idh.ifRelationIdxNotExists(EVENT_REMINDER, "EVENT_REMINDERbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(eventReminderLbl, name, BOTH, DESC, timeOrderKey)
        }
        val emailDraftLbl = idh.getEnsureEdgeLabel(EMAIL_DRAFT)
        idh.ifRelationIdxNotExists(EMAIL_DRAFT, "EMAIL_DRAFTbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(emailDraftLbl, name, BOTH, DESC, timeOrderKey)
        }
        val hasPicLbl = idh.getEnsureEdgeLabel(HAS_PICTURE)
        idh.ifRelationIdxNotExists(HAS_PICTURE, "HAS_PICTUREbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(hasPicLbl, name, BOTH, DESC, timeOrderKey)
        }
        val shoutLbl = idh.getEnsureEdgeLabel(SHOUT)
        idh.ifRelationIdxNotExists(SHOUT, "SHOUTbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(shoutLbl, name, BOTH, DESC, timeOrderKey)
        }
        val retalkLbl = idh.getEnsureEdgeLabel(RETALK)
        idh.ifRelationIdxNotExists(RETALK, "RETALKbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(retalkLbl, name, BOTH, DESC, timeOrderKey)
        }
        val revisionLbl = idh.ifEdgeLabelNotExists(REVISION){ name =>
            mgmt.makeEdgeLabel(REVISION).sortKey(timeOrderKey).sortOrder(DESC).make()
        }
        idh.ifRelationIdxNotExists(REVISION, "REVISIONbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(revisionLbl, name, BOTH, DESC, timeOrderKey)
        }
        val createAdLbl = idh.getEnsureEdgeLabel(CREATE_AD)
        idh.ifRelationIdxNotExists(CREATE_AD, "CREATE_ADbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(createAdLbl, name, BOTH, DESC, timeOrderKey)
        }

        val notificationLbl = idh.ifEdgeLabelNotExists(NOTIFICATION){ name =>
            mgmt.makeEdgeLabel(name)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }

        idh.ifRelationIdxNotExists(NOTIFICATION, "NOTIFICATIONbyTimeOrderNotifKindNotifIdTargetUserId"){ name =>
            mgmt.buildEdgeIndex(notificationLbl, name,
                OUT, DESC, timeOrderKey, notifKindKey, groupMergedKey)
            mgmt.buildEdgeIndex(notificationLbl, name + TWO,
                OUT, DESC, timeOrderKey, notifKindKey, notifIdKey, targetUserIdKey, groupMergedKey)
            mgmt.buildEdgeIndex(notificationLbl, name + THREE,
                OUT, DESC, notifIdKey)
            mgmt.buildEdgeIndex(notificationLbl, name + FOUR,
                OUT, DESC, targetIdKey)
        }

        val wroteLbl = idh.getEnsureEdgeLabel(WROTE)
        idh.ifRelationIdxNotExists(WROTE, "WROTEbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(wroteLbl, name, BOTH, DESC, timeOrderKey)
        }

        val createLbl = idh.getEnsureEdgeLabel(CREATE)
        idh.ifRelationIdxNotExists(CREATE, "CREATEbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(createLbl, name, OUT, DESC, timeOrderKey)
        }

        val originLbl = idh.getEnsureEdgeLabel(ORIGIN)
        idh.ifRelationIdxNotExists(ORIGIN, "ORIGINbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(originLbl, name, OUT, DESC, timeOrderKey)
        }

        val publishContentLbl = idh.ifEdgeLabelNotExists(PUBLISH_CONTENT){ lbl =>
            mgmt.makeEdgeLabel(PUBLISH_CONTENT)
                .signature(postStickKey, targetIdKey,postMarkKey, postKindKey)
                //                .multiplicity(Multiplicity.ONE2MANY)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }

        idh.ifRelationIdxNotExists(PUBLISH_CONTENT, "PUBLISH_CONTENTbyTimeOrder"){ name =>

            mgmt.buildEdgeIndex(publishContentLbl, name, OUT, DESC, timeOrderKey)

        }

        val streamLbl = idh.ifEdgeLabelNotExists(STREAM){ name =>
            val lbl = mgmt.makeEdgeLabel(STREAM)
                .signature(postStickKey, postMarkKey, postKindKey, streamKindKey)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()

            // jangan diaktifkan dulu, karena ini juga membuat
            // stream di group ikut hilang
            //            if (supportTTL)
            //                mgmt.setTTL(lbl, 3 * 30, TimeUnit.DAYS)
            lbl
        }

        idh.ifRelationIdxNotExists(STREAM, "STREAMbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(streamLbl, name, OUT, DESC, timeOrderKey, offsetKey, targetIdKey)
            mgmt.buildEdgeIndex(streamLbl, name + TWO, OUT, DESC, timeOrderKey, postKindKey)
            mgmt.buildEdgeIndex(streamLbl, name + THREE, OUT, DESC, timeOrderKey, postKindKey, postStickKey)
        }

        val userStreamLbl = idh.ifEdgeLabelNotExists(USER_STREAM){ name =>
            mgmt.makeEdgeLabel(USER_STREAM)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }

        idh.ifRelationIdxNotExists(USER_STREAM, "USER_STREAMbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(userStreamLbl, name, OUT, DESC, timeOrderKey)
            mgmt.buildEdgeIndex(userStreamLbl, name + TWO, OUT, DESC, timeOrderKey, deletedKey)
        }

        val pmStreamLbl = idh.ifEdgeLabelNotExists(PM_STREAM){ name =>
            mgmt.makeEdgeLabel(name)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }
        idh.ifRelationIdxNotExists(PM_STREAM, "PM_STREAMbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(pmStreamLbl, name, OUT, DESC, timeOrderKey)
            mgmt.buildEdgeIndex(pmStreamLbl, name + TWO, OUT, DESC, timeOrderKey, deletedKey)
        }

        val clickAdLbl = idh.getEnsureEdgeLabel(CLICK_AD)
        idh.ifRelationIdxNotExists(CLICK_AD, "CLICK_ADbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(clickAdLbl, name, OUT, DESC, timeOrderKey, creationTimeKey)
        }

        val hasTrophyLbl = idh.getEnsureEdgeLabel(HAS_TROPHY)
        idh.ifRelationIdxNotExists(HAS_TROPHY, "HAS_TROPYbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(hasTrophyLbl, name, OUT, DESC, timeOrderKey)
        }

        val createStatusLbl = idh.ifEdgeLabelNotExists(CREATE_STATUS){ name =>
            mgmt.makeEdgeLabel(name).sortKey(timeOrderKey).sortOrder(DESC).make()
        }
        idh.ifRelationIdxNotExists(CREATE_STATUS, "CREATE_STATUSbyOrderKey"){ name =>
            val deleted = idh.getOrCreatePropKey("deleted", classOf[java.lang.Boolean])
            mgmt.buildEdgeIndex(createStatusLbl, name, OUT, DESC, timeOrderKey)
            mgmt.buildEdgeIndex(createStatusLbl, name + TWO, OUT, DESC, timeOrderKey, deleted)
        }


        val adClickerLbl = idh.getEnsureEdgeLabel(AD_CLICKER)
        idh.ifRelationIdxNotExists(AD_CLICKER, "AD_CLICKERbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(adClickerLbl, name, OUT, DESC, timeOrderKey, creationTimeKey)
        }

        val lblVisitUrl = idh.getEnsureEdgeLabel(VISIT_URL)
        idh.ifRelationIdxNotExists(VISIT_URL, "VISIT_URLbyTimeOrder"){ name =>
            val visitUrlKey = idh.ifEdgeLabelNotExists(VISIT_URL){ name =>
                val lbl = mgmt.makeEdgeLabel(name).sortKey(timeOrderKey).sortOrder(DESC).make()

                if (supportTTL)
                    mgmt.setTTL(lbl, 30 * 3, TimeUnit.DAYS)

                lbl
            }
            mgmt.buildEdgeIndex(visitUrlKey, name, BOTH, DESC, timeOrderKey)
        }

        val hasProfPicLbl = idh.getEnsureEdgeLabel(HAS_PROFILE_PICTURE)
        idh.ifRelationIdxNotExists(HAS_PROFILE_PICTURE, "HAS_PROFILE_PICTUREbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(hasProfPicLbl, name, OUT, DESC, timeOrderKey)
        }

        val albumLbl = idh.getEnsureEdgeLabel(ALBUM)
        idh.ifRelationIdxNotExists(ALBUM, "ALBUMbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(albumLbl, name, OUT, DESC, timeOrderKey)
        }

        val bookmarkChannelLbl = idh.ifEdgeLabelNotExists(BOOKMARK_CHANNEL){ name =>
            mgmt.makeEdgeLabel(BOOKMARK_CHANNEL).signature(targetIdKey).make()
        }
        idh.ifRelationIdxNotExists(BOOKMARK_CHANNEL, "BOOKMARK_CHANNELbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(bookmarkChannelLbl, name, OUT, DESC, timeOrderKey)
        }

        val likesLbl = idh.getEnsureEdgeLabel(LIKES)
        idh.ifRelationIdxNotExists(LIKES, "LIKESbySourceName"){ name =>
            mgmt.buildEdgeIndex(likesLbl, name, BOTH, ASC, sourceNameKey)
        }

        val supportLbl = idh.ifEdgeLabelNotExists(SUPPORT){ name =>
            mgmt.makeEdgeLabel(SUPPORT).signature(sourceIdKey, targetIdKey).make()
        }
        idh.ifRelationIdxNotExists(SUPPORT, "SUPPORTbyTargetName"){ name =>
            mgmt.buildEdgeIndex(supportLbl, name, BOTH, ASC, targetNameKey)
        }

        val pmIdKey = idh.getOrCreatePropKey("pm.id", classOf[java.lang.Long])
        idh.ifGraphIdxNotExists("PMbyPmId"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(pmIdKey).indexOnly(NOTIFICATION_LBL).buildCompositeIndex()
        }

        val joinLbl = idh.ifEdgeLabelNotExists(JOIN){ name =>
            mgmt.makeEdgeLabel(JOIN).signature(sourceIdKey, targetIdKey).make()
        }
        idh.ifRelationIdxNotExists(JOIN, "JOINbyTargetName"){ name =>
            mgmt.buildEdgeIndex(joinLbl, name, OUT, ASC, targetNameKey)
        }

        val replyOfLbl = idh.ifEdgeLabelNotExists(REPLY_OF){ name =>
            mgmt.makeEdgeLabel(REPLY_OF)
                .signature(sourceIdKey, targetIdKey)
                .sortKey(timeOrderKey)
                .sortOrder(ASC)
                .make()
        }
        idh.ifRelationIdxNotExists(REPLY_OF, "REPLY_OFbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(replyOfLbl, name, OUT, ASC, timeOrderKey)
        }

        val replyWithLbl = idh.ifEdgeLabelNotExists(REPLY_WITH){ name =>
            mgmt.makeEdgeLabel(REPLY_WITH)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }

        val postMarkLbl = idh.ifEdgeLabelNotExists(POST_MARK){ name =>
            mgmt.makeEdgeLabel(POST_MARK).unidirected().make()
        }
        idh.ifRelationIdxNotExists(POST_MARK, "POST_MARKbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(postMarkLbl, name, OUT, DESC, timeOrderKey)
        }

        val useAppLbl = idh.getEnsureEdgeLabel(USE_APP)
        idh.ifRelationIdxNotExists(USE_APP, "USE_APPbyTargetName"){ name =>
            mgmt.buildEdgeIndex(useAppLbl, name, OUT, ASC, targetNameKey)
        }

        val lblResponseOf = idh.getEnsureEdgeLabel(RESPONSE_OF)
        idh.ifRelationIdxNotExists(RESPONSE_OF, "RESPONSE_OFbyCount"){ name =>
            mgmt.buildEdgeIndex(lblResponseOf, name, OUT, ASC, countKey)
        }

        val lblPymk = idh.getEnsureEdgeLabel(PYMK)
        idh.ifRelationIdxNotExists(PYMK, "PYMKbyScore"){ name =>
            mgmt.buildEdgeIndex(lblPymk, name, BOTH, DESC, scoreKey)
        }

        /**
         * Pastikan default ordering untuk sub forum adalah berdasarkan
         * alphabet namanya.
         */
        val lblSubForum = idh.getEnsureEdgeLabel(SUB_FORUM)
        idh.ifRelationIdxNotExists(SUB_FORUM, "SubForumByNameAsc"){ name =>
            mgmt.buildEdgeIndex(lblSubForum, name, BOTH, ASC, lowerNameKey)
        }

        val lblUserGroupMember = idh.ifEdgeLabelNotExists(USER_GROUP_MEMBER){ name =>
            mgmt.makeEdgeLabel(USER_GROUP_MEMBER)
                .signature(sourceIdKey, targetIdKey)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }

        idh.ifRelationIdxNotExists(USER_GROUP_MEMBER, "UserGroupMemberByTimeOrder"){ name =>
            mgmt.buildEdgeIndex(lblUserGroupMember, name, BOTH, DESC, timeOrderKey)
        }

        val colVertexLbl = idh.ifEdgeLabelNotExists(COLLECTION_VERTEX){ name =>
            mgmt.makeEdgeLabel(COLLECTION_VERTEX)
                .signature(kindKey)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }
        idh.ifRelationIdxNotExists(COLLECTION_VERTEX, "COLLECTION_VERTEXbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(colVertexLbl, name, OUT, DESC, timeOrderKey)
        }
        idh.ifRelationIdxNotExists(COLLECTION_VERTEX, "COLLECTION_VERTEXbyTimeOrderKind"){ name =>
            mgmt.buildEdgeIndex(colVertexLbl, name, OUT, DESC, kindKey)
        }
        idh.ifRelationIdxNotExists(COLLECTION_VERTEX, "COLLECTION_VERTEXbyKindCreatorId"){ name =>
            mgmt.buildEdgeIndex(colVertexLbl, name, OUT, DESC, kindKey, creatorIdKey)
        }
        idh.ifRelationIdxNotExists(COLLECTION_VERTEX, "COLLECTION_VERTEXbyTimeOrderKindYear"){ name =>
            mgmt.buildEdgeIndex(colVertexLbl, name, OUT, DESC, kindKey, yearKey, timeOrderKey)
        }
        idh.ifRelationIdxNotExists(COLLECTION_VERTEX, "COLLECTION_VERTEXbyTimeOrderKindYearMonth"){ name =>
            mgmt.buildEdgeIndex(colVertexLbl, name, OUT, DESC, yearKey, monthKey)
            mgmt.buildEdgeIndex(colVertexLbl, name + TWO, OUT, DESC, kindKey, yearKey, monthKey)
        }

        val lblView = idh.ifEdgeLabelNotExists(VIEW){ name =>
            mgmt.makeEdgeLabel(VIEW)
                .signature(sourceIdKey, targetIdKey)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }
        idh.ifRelationIdxNotExists(VIEW, "COLLECTION_VERTEX_VIEWTimeOrder"){ name =>
            mgmt.buildEdgeIndex(lblView, name, OUT, DESC, timeOrderKey)
        }

        val lblInvite = idh.getEnsureEdgeLabel(INVITE)
        idh.ifRelationIdxNotExists(INVITE, "INVITEbyCreationTime"){ name =>
            mgmt.buildEdgeIndex(lblInvite, name, OUT, DESC, creationTimeKey)
        }

        val popularLbl = idh.ifEdgeLabelNotExists(POPULAR){ name =>
            mgmt.makeEdgeLabel(name)
                .sortKey(scoreKey)
                .sortOrder(DESC)
                .make()
        }
        idh.ifRelationIdxNotExists(POPULAR, "POPULARbyScore"){ name =>
            mgmt.buildEdgeIndex(popularLbl, name, OUT, DESC, scoreKey)
        }
        idh.ifRelationIdxNotExists(POPULAR, "POPULARbyKeyIdentifierLong"){ name =>
            mgmt.buildEdgeIndex(popularLbl, name, OUT, DESC, keyIdentifierLongKey)
        }

        idh.ifGraphIdxNotExists("POSTMARKbyTitle"){ name =>
            mgmt.buildIndex(name, classOf[Vertex]).addKey(postMarkTitleKey).unique()
                .indexOnly(POSTMARK).buildCompositeIndex()
        }

        idh.ifGraphIdxNotExists("STATSbyTrackUrl"){ name =>
            val k1 = idh.getOrCreatePropKey("stats.track-url", classOf[java.lang.String])
            mgmt.buildIndex(name, classOf[Vertex]).addKey(k1).unique().buildCompositeIndex()
        }

        val knowslabel = idh.ifEdgeLabelNotExists(KNOWS){ lbl =>
            mgmt.makeEdgeLabel(KNOWS)
                .sortKey(scoreKey, creationTimeKey)
                .sortOrder(DESC)
                .make()
        }
        idh.ifRelationIdxNotExists(KNOWS, "KNOWSbyScore"){ name =>
            mgmt.buildEdgeIndex(knowslabel, name, BOTH, DESC, scoreKey)
        }

        val giveReputationLbl = idh.ifEdgeLabelNotExists(GIVE_REPUTATION) { name =>
            mgmt.makeEdgeLabel(GIVE_REPUTATION)
                .signature(sourceIdKey, targetIdKey)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }

        idh.ifRelationIdxNotExists(GIVE_REPUTATION, "GIVE_REPUTATIONbyTimeOrder") { name =>
            mgmt.buildEdgeIndex(giveReputationLbl, name, OUT, DESC, timeOrderKey)
        }

        val monitorLbl = idh.ifEdgeLabelNotExists(MONITOR) { name =>
            mgmt.makeEdgeLabel(MONITOR)
                .signature(sourceIdKey, targetIdKey)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }

        idh.ifRelationIdxNotExists(MONITOR, "MONITORByTimeOrder") { name =>
            mgmt.buildEdgeIndex(monitorLbl, name, BOTH, DESC, timeOrderKey)
        }

        // Sort index untuk label has_faq
        val hasTopicLbl =  idh.ifEdgeLabelNotExists(HAS_FAQ){ name =>
            mgmt.makeEdgeLabel(name)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }

        idh.ifRelationIdxNotExists(HAS_FAQ, "HAS_FAQbyTimeOrder"){ name =>
            mgmt.buildEdgeIndex(hasTopicLbl, name, OUT, DESC, timeOrderKey)
        }

        val hasCategoryLbl = idh.ifEdgeLabelNotExists(HAS_CATEGORY){ name =>
            mgmt.makeEdgeLabel(name)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }

        idh.ifRelationIdxNotExists(HAS_CATEGORY, "HAS_CATEGORYTimeOrder") { name =>
            mgmt.buildEdgeIndex(hasCategoryLbl, name, OUT, DESC, timeOrderKey)
        }

////        val leaderboardLbl = idh.getEnsureEdgeLabel(LEADERBOARD)
//        val leaderboardLbl = idh.ifEdgeLabelNotExists(LEADERBOARD){ name =>
//            mgmt.makeEdgeLabel(name)
//                .sortKey(lbPointKey, timeOrderKey)
//                .sortOrder(DESC)
//                .make()
//        }
//
//        idh.ifRelationIdxNotExists(LEADERBOARD, "LEADERBOARDPointOrder") { name =>
//            mgmt.setTTL(leaderboardLbl, 30 * 3, TimeUnit.DAYS) // expires within 3 months
//            mgmt.buildEdgeIndex(leaderboardLbl, name, OUT, DESC, lbPointKey, timeOrderKey)
//        }

        val hasLeaderboardLbl = idh.ifEdgeLabelNotExists(HAS_LEADERBOARD) { name =>
            mgmt.makeEdgeLabel(name)
                .sortKey(timeOrderKey)
                .sortOrder(DESC)
                .make()
        }

        idh.ifRelationIdxNotExists(HAS_LEADERBOARD, "HAS_LEADERBOARDOrder") { name =>
            mgmt.buildEdgeIndex(hasLeaderboardLbl, name, OUT, DESC, timeOrderKey)
        }

        val userLeaderboardLbl = idh.ifEdgeLabelNotExists(USER_LEADERBOARD) { name =>
            mgmt.makeEdgeLabel(name).make()
        }

        // oreded by timeOrder key
        idh.ifRelationIdxNotExists(USER_LEADERBOARD, "USER_LEADERBOARDTimeOrder"){ name =>
            mgmt.buildEdgeIndex(userLeaderboardLbl, name, BOTH, DESC, timeOrderKey)
        }

        idh.ifRelationIdxNotExists(USER_LEADERBOARD, "USER_LEADERBOARDOrder") { name =>
            mgmt.buildEdgeIndex(userLeaderboardLbl, name, OUT, DESC, yearKey, monthKey)
        }

        /************************************************
          * INDEX SEMUA LABELS YANG MUNGKIN BELUM DIINDEX
          ***********************************************/

        indexable.foreach { lbl =>
            if (!mgmt.containsRelationType(lbl)){
                info("indexing edge label: " + lbl)
                mgmt.makeEdgeLabel(lbl).make()
            }
        }

        mgmt.commit()

        _db.commit()

    }

    /**
     * Nama database
     * @return
     */
    def getName = "TitanDb"

    /**
     * Versi database atau driver ini.
     * @return
     */
    def getVersion: String = Titan.version()

    /**
     * Close database connection.
     */
    override def close() {
        info("closing %s...".format(this))
        db.shutdown()
    }

    override def getRaw[T]: T = {
        db.asInstanceOf[T]
    }

    def getGraph: GraphType = db

}

trait TitanDatabaseImpl extends DatabaseComponent with Slf4jLogger {
    def isSharedThread: Boolean = database.asInstanceOf[TitanDatabase].isSharedThread
}

class IdxHelper(mgmt: TitanManagement){
    def getOrCreatePropKey(key:String, dataType:Class[_]):PropertyKey = {
        try {
            if (mgmt.getRelationType(key) == null)
                mgmt.makePropertyKey(key).dataType(dataType).make()
            else
                mgmt.getPropertyKey(key)
        }catch{
            case e:java.lang.IllegalArgumentException if e.getMessage.contains("violates a uniqueness constraint") =>
                mgmt.getPropertyKey(key)
        }
    }

    def ifGraphIdxNotExists(idxName:String)(func: String => Unit){
        if (!mgmt.containsGraphIndex(idxName)){
            func(idxName)
        }
    }

    def ifRelationIdxNotExists(label: String, idxName: String)(func: String => Unit) {
        val _edgeLabel = mgmt.getEdgeLabel(label)
        require(_edgeLabel != null, s"cannot get edge label $label")
        if (!mgmt.containsRelationIndex(_edgeLabel, idxName)) {
            func(idxName)
        }
    }

    def getEnsureEdgeLabel(lbl:String): EdgeLabel = {
        if (!mgmt.containsRelationType(lbl))
            mgmt.makeEdgeLabel(lbl).make()
        else
            mgmt.getEdgeLabel(lbl)
    }
    def ifEdgeLabelNotExists(lbl:String)(func: String => EdgeLabel):EdgeLabel = {
        if (!mgmt.containsRelationType(lbl))
            func(lbl)
        else
            mgmt.getEdgeLabel(lbl)
    }
}

