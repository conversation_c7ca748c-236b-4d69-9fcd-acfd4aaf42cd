/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.database

import com.tinkerpop.blueprints.util.wrappers.id.IdGraph.IdFactory
import java.util.concurrent.atomic.AtomicLong

/**
  * Author: robin
  * Date: 3/29/13
  * Time: 6:08 PM
  *
  */
class LocalIdFactory extends IdFactory {
     private val count = new AtomicLong(0L)
     def createId() = {
         count.addAndGet(1L).asInstanceOf[AnyRef]
     }
 }

