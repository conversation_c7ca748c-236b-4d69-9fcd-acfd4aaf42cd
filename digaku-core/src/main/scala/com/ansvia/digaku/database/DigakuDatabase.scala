/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.database

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.exc.NotSupportedException


abstract class DigakuDatabase extends Slf4jLogger {

    /**
     * Nama database
     * @return
     */
    def getName: String

    /**
     * Versi database atau driver ini.
     * @return
     */
    def getVersion: String

    def isConnected: Boolean = {
        throw NotSupportedException("this method not supported for current db: " + getName)
    }

    /**
     * Close database connection.
     */
    def close() {
        // do nothing.
    }

    def getRaw[T]: T


    def index(){}
}


trait DatabaseComponent {
    def database:DigakuDatabase


}

