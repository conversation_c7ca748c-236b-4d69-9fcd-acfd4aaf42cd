/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.database
//
//import com.tinkerpop.blueprints._
//
//
//trait GraphDbOperation[T <: Graph] extends DigakuDatabase {
//
//
//    protected implicit val db:T
////
////    implicit def elementToDbRecord(elm: Element, bucket: String): DigakuDbRecord = {
////        var rv = new Neo4jDbRecord(bucket)
////
////        val it = elm.getPropertyKeys.iterator()
////
////        while (it.hasNext) {
////            val k = it.next()
////            val v = elm.getProperty(k)
////            rv += (k -> v)
////        }
////
////        //        rv += ("_bucket_" -> bucket)
////
////        rv.setRawData(elm)
////
////        rv
////    }
////
////    implicit def elementToDbRecord(elm: Element): DigakuDbRecord = {
////        var rv = new DigakuDbRecord
////        val it = elm.getPropertyKeys.iterator()
////
////        while (it.hasNext) {
////            val k = it.next()
////            val v = elm.getProperty(k)
////            rv += (k -> v)
////        }
////
////        rv
////    }
////    /**
////     * Simpan objek ke database.
////     * @param obj object to save.
////     * @param bucket bucket name.
////     */
////    def save(obj: DigakuDbRecord, bucket: String) {
////        transact {
////            val vertex = db.addVertex(null)
////
////            for ((k, v) <- obj.iterator) {
////                vertex.set(k, v)
////            }
////        }
////    }
////
////    /**
////     * Dapatkan objek berdasarkan ID.
////     * @param id
////     * @param bucket
////     * @return
////     */
////    def getById(id: AnyRef, bucket: String) = {
////        val vertex = db.getVertex(id)
////        if (vertex != null) {
////            Some(elementToDbRecord(vertex, bucket))
////        } else {
////            None
////        }
////    }
//
////    /**
////     * Find object by filter
////     * filter is map represent your key value based filter.
////     * @param filter filter map.
////     * @param bucket bucket name.
////     * @param offset offset start.
////     * @param limit limit.
////     * @return
////     */
////    def findByMap(filter: Map[String, Any], bucket: String, offset: Int, limit: Int) = {
////        if (bucket == GraphBucketType.VERTEX) {
////            val it = filter.iterator
////            val (k, v) = it.next()
////            var vertices = db.getVertices(k, v).iterator()
////
////            while (it.hasNext) {
////                val fl = it.next()
////                vertices = vertices.filter(v => v.getProperty(fl._1) != fl._2)
////            }
////
////            getOffsetLimit(vertices.toIterable, offset, limit)
////                  .map(v => elementToDbRecord(v, bucket)).slice(offset, limit).toArray
////
////            // 1,2,3,4,5,6,7,8,9,10
////            // 2,10
////
////        } else if (bucket == GraphBucketType.EDGE) {
////            val it = filter.iterator
////            val (k, v) = it.next()
////            var edges = db.getEdges(k, v).iterator()
////            while (it.hasNext) {
////                val fl = it.next()
////                edges = edges.filter(v => v.getProperty(fl._1) != fl._2)
////            }
////
////            getOffsetLimit(edges.toIterable, offset, limit)
////                  .map(v => elementToDbRecord(v, bucket)).toArray
////
////        } else {
////            throw new InvalidParameterException("Don't know how to handle bucket named `%s`".format(bucket))
////        }
////    }
////
////    def list(bucket: String, offset: Int, limit: Int) = {
////        if (bucket == GraphBucketType.VERTEX) {
////
////            var rv: Iterable[Vertex] = db.getVertices
////            rv = getOffsetLimit(rv, offset, limit)
////            rv.map(v => elementToDbRecord(v)).toArray
////
////        } else if (bucket == GraphBucketType.EDGE) {
////
////            var rv: Iterable[Edge] = db.getEdges
////            rv = getOffsetLimit(rv, offset, limit)
////            rv.map(e => elementToDbRecord(e)).slice(offset, limit).toArray
////
////        } else {
////            throw new InvalidParameterException("Don't know how to handle bucket named `%s`".format(bucket))
////        }
////    }
////
////    def deleteById(id: Any, bucket: String) {
////        bucket match {
////            case GraphBucketType.VERTEX =>
////                transact {
////                    val v = db.getVertex(id)
////                    if (v != null) {
////                        db.removeVertex(v)
////                    }
////                }
////            case GraphBucketType.EDGE =>
////                transact {
////                    val e = db.getEdge(id)
////                    if (e != null) {
////                        db.removeEdge(e)
////                    }
////                }
////            case _ =>
////                throw new InvalidParameterException("Don't know how to handle bucket named `%s`".format(bucket))
////        }
////    }
////
////    def deleteByMap(filter: Map[String, Any], bucket: String) {
////        throw NotSupportedException("deleteByMap not supported for %s database".format(getName))
////    }
//
//    def getRaw[T] = db.asInstanceOf[T]
//
//    /**
//     * Close database connection.
//     */
//    override def close() {
//        db.shutdown()
//    }
//
//
////    /**
////     * Safe transaction wrapper
////     * useful when using thread independent database.
////     * with additional parameter `db`.
////     *
////     * Example:
////     *
////     *          lockup { db =>
////     *
////     *              // transaction here.
////     *
////     *          }
////     *
////     * @param func function to call.
////     * @tparam T return parameter type.
////     * @return
////     */
////    def using[T](func: (TransactionalGraph) => T):T
//
////
////    private def getOffsetLimit[T: Manifest](arr: Array[T], offset: Int, limit: Int): Array[T] = {
////        if (offset > arr.length) {
////            Array.empty[T]
////        } else if (limit > arr.length) {
////            Array.empty[T]
////        } else if (arr.length == 0) {
////            Array.empty[T]
////        } else {
////            val newLimit = arr.length - offset
////            arr.slice(offset, newLimit)
////        }
////    }
////
////    private def getOffsetLimit[T: Manifest](arr: Iterable[T], offset: Int, limit: Int): Iterable[T] = {
////        if (offset > arr.size) {
////            Array.empty[T]
////        } else if (arr.size == 0) {
////            Array.empty[T]
////        } else {
////            val newLimit = arr.size - offset
////            arr.slice(offset, newLimit)
////        }
////    }
//}
