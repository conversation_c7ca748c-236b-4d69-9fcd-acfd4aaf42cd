/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
// * Copyright (C) 2013. Ansvia Inc.
// * Author: robin
// * Date: 2013/1/7
// */
//
//package com.ansvia.digaku.database
//
//import com.tinkerpop.blueprints.impls.tg.{TinkerGraph, TinkerGraphFactory}
//import com.tinkerpop.blueprints.{Graph, TransactionalGraph}
//
//
//final case class TinkerGraphDatabase() extends DigakuDatabase with GraphDbOperation[Graph] {
//
//    implicit lazy val db:TinkerGraph = TinkerGraphFactory.createTinkerGraph()
//
//    /**
//     * Nama database
//     * @return
//     */
//    def getName = "TinkerGraphDb"
//
//    /**
//     * Versi database atau driver ini.
//     * @return
//     */
//    def getVersion = "0.1"
//
//}
//
//
//
