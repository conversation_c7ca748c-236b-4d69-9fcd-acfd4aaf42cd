/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
// * Copyright (c) 2013. Ansvia Inc.
// * Author: robin
// * Created: 1/3/13 6:46 PM
// */
//
//package com.ansvia.digaku.database
//
//import com.mongodb.{MongoException, Mongo}
//import org.bson.types.ObjectId
//import com.mongodb.casbah._
//import commons.MongoDBObject
//import java.util
//import java.net.ConnectException
//
///**
// * Digaku database implementation for MongoDB.
// * @param host mongodb host.
// * @param port mongodb port.
// * @param dbName database name to use.
// */
//final case class MongoDb(host: String = "localhost", port: Int = 27017, dbName: String = "digaku") extends DigakuDatabase {
//
//    lazy val conn = MongoConnection(host, port)
//    lazy val db = conn(dbName)
//
//    def getName = "MongoDB"
//
//    def getVersion = "0.1"
//
//    /**
//     * Get raw mongodb connection.
//     * @return
//     */
//    def getRawConnection = conn
//
//
//    /**
//     * Save object to database.
//     * @param obj
//     */
//    def save(obj: DigakuDbRecord, bucket: String) {
//        //        val dbObj = obj.asInstanceOf[MongoDbDatabaseRecord]
//        val col = db(bucket)
//        col += toDbObject(obj.toMap)
//    }
//
//    /**
//     * convert map to DigakuDbRecord.
//     * @param map
//     * @return
//     */
//    def mapToDbRecord(map: util.Map[String, Any]): DigakuDbRecord = {
//        val rv = new DigakuDbRecord
//        val it = map.entrySet().iterator()
//        while (it.hasNext) {
//            val x = it.next()
//            rv.put(x.getKey, x.getValue)
//        }
//        rv
//    }
//
//    /**
//     * Get record by id.
//     * @param id object id.
//     * @param bucket collection name.
//     * @return
//     */
//    def getById(id: AnyRef, bucket: String) = {
//        val rv = db(bucket).findOneByID(new ObjectId(id.asInstanceOf[String]))
//        if (rv.isDefined) {
//            Some(mapToDbRecord(rv.get.toMap.asInstanceOf[util.Map[String, Any]]))
//        } else {
//            None
//        }
//    }
//
//    /**
//     * Find object by filtering map.
//     * @param filter filter map.
//     * @param bucket collection name.
//     * @param offset offset start.
//     * @param limit record limit.
//     * @return
//     */
//    def findByMap(filter: Map[String, Any], bucket: String, offset: Int = 0, limit: Int = 10) = {
//        val q = toDbObject(filter)
//        db(bucket).find(q).skip(offset).limit(limit).map {
//            d =>
//                mapToDbRecord(d.toMap.asInstanceOf[util.Map[String, Any]])
//        }.toArray
//    }
//
//    /**
//     * List database records.
//     * @param bucket collection name.
//     * @param offset offset start.
//     * @param limit record limit.
//     * @return
//     */
//    def list(bucket: String, offset: Int = 0, limit: Int = 10) = {
//        db(bucket).find().skip(offset).limit(limit).map {
//            d =>
//                mapToDbRecord(d.toMap.asInstanceOf[util.Map[String, Any]])
//        }.toArray
//    }
//
//    /**
//     * delete object by id.
//     * @param id object id to delete.
//     * @param bucket collection name.
//     */
//    def deleteById(id: Any, bucket: String) {
//        //        db(bucket).remove(MongoDBObject("_id" -> new ObjectId(id.asInstanceOf[String])))
//        deleteById(new ObjectId(id.asInstanceOf[String]), bucket)
//    }
//
//    /**
//     * delete object by id.
//     * @param id object id to delete.
//     * @param bucket collection name.
//     */
//    def deleteById(id: ObjectId, bucket: String) {
//        db(bucket).remove(MongoDBObject("_id" -> id))
//    }
//
//    /**
//     * Delete record by map object from database.
//     * @param filter map query filter.
//     * @param bucket mongodb collection.
//     */
//    def deleteByMap(filter: Map[String, Any], bucket: String) {
//        db(bucket).remove(toDbObject(filter))
//    }
//
//    /**
//     * Convert map to [[com.mongodb.DBObject]]
//     * @param d map to convert.
//     * @return
//     */
//    private def toDbObject(d: Map[String, Any]) = {
//        val q = {
//            val qd = MongoDBObject.newBuilder
//            d.iterator.foreach {
//                m =>
//                    qd += m._1 -> m._2
//            }
//            qd.result()
//        }
//        q
//    }
//
//    /**
//     * periksa apakah database konek.
//     * @return
//     */
//    override def isConnected = {
//        var rv = false
//        try {
//            val m = new Mongo(host, port)
//            val db = m.getDB("test")
//            db.getCollectionNames
//            rv = true
//            m.close()
//        } catch {
//            case e: MongoException =>
//            case e: ConnectException =>
//        }
//        rv
//    }
//
//    /**
//     * Get raw mongodb database instance.
//     * @return
//     */
//    def getRaw[T] = db.asInstanceOf[T]
//
//    /**
//     * Close database connection.
//     */
//    override def close() {
//        conn.close()
//    }
//
//    /**
//     * Safe transaction wrapper
//     * useful when using thread independent database.
//     *
//     * Example:
//     *
//     * lockup {
//     *
//     * // transaction here.
//     *
//     * }
//     *
//     * @param func function to call.
//     * @tparam T return parameter type.
//     * @return
//     */
//    def using[T](func: => T) = func
//
//
//}
//
//
//case class MongoDbDatabaseRecord(colName: String) extends DigakuDbRecord {
//
//    def getCollection: String = colName
//
//}
