/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
//* Copyright (c) 2013. Ansvia Inc.
//* Author: robin
//* Created: 1/3/13 6:46 PM
//*/
//
//package com.ansvia.digaku.database
//
//import com.tinkerpop.blueprints.impls.neo4j.Neo4jGraph
//import com.tinkerpop.blueprints.{TransactionalGraph, Edge, Element, Vertex}
//import scala.collection.JavaConversions._
//import com.ansvia.digaku.exc.{NotSupportedException, InvalidParameterException}
//
///**
// * Digaku Database implementation for Neo4j graph db.
// * @param dbDir path to db dir where Neo4j
// *              will using in.
// */
//final case class Neo4jDatabase(dbDir: String) extends DigakuDatabase with GraphDbOperation[TransactionalGraph] {
//
//    implicit lazy val db:Neo4jGraph = new Neo4jGraph(dbDir)
//
//    /**
//     * Nama database
//     * @return
//     */
//    def getName = "Neo4j"
//
//    /**
//     * Versi database atau driver ini.
//     * @return
//     */
//    def getVersion = "0.1"
//}
//
//
//class Neo4jDbRecord(elmKind: String) extends DigakuDbRecord with GraphDbRecord
