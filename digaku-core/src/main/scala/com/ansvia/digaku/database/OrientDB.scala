/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
// * Copyright (c) 2013. Ansvia Inc.
// * Author: robin
// * Created: 1/3/13 6:46 PM
// */
//
//package com.ansvia.digaku.database
//
//class OrientDB extends DigakuDatabase{
//    def getName = null
//
//    def getVersion = null
//
//    def save(obj: Serializable) {}
//
//    def getById(id: String) = null
//
//    def findByMap(filter: Map[String, Any], offset: Int, limit: Int) = null
//
//    def deleteById(id: String) {}
//
//    def deleteByMap(filter: Map[String, Any]) {}
//}
