///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Types.{GremPipeVertex, GraphType, IDType}
//import scala.collection.JavaConversions._
//import com.ansvia.graph.BlueprintsWrapper._
//import com.ansvia.digaku.model.Label._
//import com.ansvia.digaku.exc.{LimitationReachedException, InvalidParameterException, DigakuException, AlreadyExistsException}
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.graph.annotation.Persistent
//import java.util.{Calendar, Date}
//import com.tinkerpop.gremlin.Tokens.T
//import com.ansvia.digaku.utils.Comparator
//
//
///**
// * Author: robin
// *
// */
//
///**
// * Ini adalah model untuk gallery di dalam group.
// * @param name gallery name.
// */
//case class ChannelGallery(name:String) extends BaseModel[IDType] with DbUser {
//
//
//
//    /**
//     * Group yang meng-handle gallery ini.
//     */
//    lazy val group:Group = {
//        getVertex.pipe.in(GALLERY).headOption.flatMap(_.toCC[Group]).get
//    }
//
//
//    def getCollections(offset:Int, limit:Int) = {
//        getVertex.pipe.out(ALBUM)
//            .range(offset, offset + limit - 1)
//            .iterator().flatMap(_.toCC[GalleryCollection])
//    }
//
//    def addCollection(album:GalleryCollection){
////        transact {
//            val ed = this --> ALBUM --> album <()
//            ed.setProperty("timeOrder", System.currentTimeMillis())
////        }
//        db.commit()
//    }
//
//    def removeCollection(album:GalleryCollection){
////        transact {
//            getVertex.pipe.outE(ALBUM).as("ed").inV()
//                .has("id", album.getId).back("ed")
//                .remove()
////        }
//        db.commit()
//    }
//
//
//    def setChannel(ch:Group){
//        if (getVertex.pipe.in(GALLERY).headOption.isDefined)
//            throw AlreadyExistsException("This gallery already set to other group")
//
////        transact {
//            ch --> GALLERY --> this
////        }
//        db.commit()
//    }
//
//    def detachChannel(ch:Group){
////        transact {
//            getVertex.pipe.inE(GALLERY).as("ed").outV()
//                .has("id", ch.getId).back("ed").remove()
////        }
//        db.commit()
//    }
//
//}
//
//
///**
// * Gallery collection ini digunakan misalnya
// * sebagai album dalam gallery.
// * @param name nama collection.
// * @param desc deskripsi dari collection ini.
// */
//abstract class GalleryCollectionBase(name:String, desc:String) extends BaseModel[IDType] with CollectionVertex with DbUser {
//
//    lazy val gallery:Option[ChannelGallery] = {
//        getVertex.pipe.in(ALBUM).headOption.flatMap(_.toCC[ChannelGallery])
//    }
//
//    lazy val group:Option[Group] = {
//        getVertex.pipe.in(ALBUM).in(GALLERY).headOption.flatMap(_.toCC[Group])
//    }
//
//    def creationDateTime = new Date(creationTime)
//
//    /**
//     * Untuk meletakkan media pada collection ini.
//     * @param media media yang akan dipindah. lihat [[com.ansvia.digaku.model.Media]]
//     */
//    def put(media:Media, noTx:Boolean = false) {
//
//        require(media != null, "media cannot be null")
//
//        val thisV = this.reload().getVertex
//
//        require(thisV != null)
//
//        if (thisV.getOrElse("count", 0L) > 150000L)
//            throw LimitationReachedException("Group gallery album max 150000 photos")
//
//        media match {
//            case pic:Picture =>
//                // simpan informasi album di dalam picture post
//                val picV = pic.getVertex
//                picV.setProperty("albumPhotoId", this.getId)
//            case _ =>
//        }
//
//        val mediaV = media.reload().getVertex
//
//        // gunakan media time sebagai partition key dan time order-nya.
//        val cal = Calendar.getInstance()
//        cal.setTimeInMillis(media.getCreationTime)
//        val cvYear = cal.get(Calendar.YEAR)
//        val cvMonth = cal.get(Calendar.MONTH)
//        // val cvTimeOrder = media.getCreationTime
//
//        val ed = this.getMPart(CollectionVertex.Kind.STREAM, cvYear, cvMonth) --> STREAM --> mediaV <()
//
//        // simpan informasi jumlah
//        thisV.setProperty("count", thisV.getOrElse("count", 0L) + 1L)
//
//        val now = System.currentTimeMillis()
//
//        ed.setProperty("timeOrder", now)
//
//        ed.setProperty("kind", StreamKind.GALLERY)
//
//        // gak perlu pake targetId karena gak terlalu berguna?
//        ed.setProperty("targetId", mediaV.getId)
//
//
//        if (!noTx)
//            db.commit()
//
//    }
//
//    /**
//     * Untuk memindahkan media dari collection ini
//     * ke collection lain.
//     * @param media media yang akan dipindan. lihat [[com.ansvia.digaku.model.Media]]
//     * @param collection collection target dimana media akan dipindahkan ke.
//     */
//    def moveTo(media:Media, collection:GalleryCollectionBase){
//        remove(media, noTx=true)
//        collection.put(media)
//    }
//
//
//    /**
//     * Hapus media dari collection ini.
//     * @param media media yang akan dihapus.
//     * @param noTx
//     */
//    def remove(media:Media, noTx:Boolean=false){
//        val thisV = getVertex
//
//        thisV.pipe
//            .out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM)
////            .out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM)
//            .outE(STREAM)
//            .as("ed").inV()
//            .has("id", media.getId).back("ed").remove()
//
//        // simpan informasi jumlah
//        thisV.setProperty("count", thisV.getOrElse("count", 0L) - 1L)
//
//        if (!noTx)
//            db.commit()
//    }
//
//    /**
//     * Untuk memeriksa apakah collection ini memiliki
//     * media
//     * @param media media yang akan diperiksa.
//     * @return
//     */
//    def hasMedia(media:Media) = {
//        this.getVertex.pipe
//            .out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM)
////            .out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM)
//            .out(STREAM).has("id", media.getId).count() > 0
//    }
//
//    /**
//     * Untuk mendapatkan jumlah media yang ada di collection ini.
//     * @return
//     */
//    def getMediaCount:Long = {
////        this.getVertex.pipe.outE(STREAM).count().toInt
////        reload().getVertex.getOrElse("count", 0L)
//        reload().getVertex.pipe
//            .out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM)
//            .out(STREAM).count()
//    }
//
//
//    /**
//     * Untuk mendapatkan stream media pada collection ini.
//     * @param maxId
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def getStream(maxId:IDType, offset:Int, limit:Int) = {
//
////        val rv =
//            if (maxId < 1L) {
//                getVertex.pipe
//                    .out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM)
//                    .out(STREAM).order(Comparator.timeOrdererDesc)
//                    .range(offset, offset + limit - 1)
//                    .iterator()
//                    .flatMap(_.toCC[Picture])
//            } else {
//                getVertex.pipe
//                    .out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM)
//                    .out(STREAM).order(Comparator.timeOrdererDesc)
//                    .has("id", T.lt, maxId)
//                    .range(offset, offset + limit - 1)
//                    .iterator()
//                    .flatMap(_.toCC[Picture])
//            }
//
////        // @TODO(robin): remove this backward compatibility
////        // safe to remove after 30 mei 2014
////        // begin backward compatibility ---------------------------
////        if (rv.hasNext) {
////            rv
////        } else {
////            if (maxId < 1L) {
////                getVertex.pipe
////                    .outE(STREAM)
////                    .range(offset, offset + limit - 1)
////                    .inV()
////                    .iterator()
////                    .flatMap(_.toCC[Picture])
////            } else {
////                getVertex.pipe
////                    .outE(STREAM)
////                    .has("targetId", T.lt, maxId)
////                    .range(offset, offset + limit - 1)
////                    .inV()
////                    .iterator()
////                    .flatMap(_.toCC[Picture])
////            }
////        }
////        // end of backward compatibility ---------------------------
//
//    }
//
//}
//
//case class GalleryCollection(name:String, desc:String, tagsStr:String) extends GalleryCollectionBase(name, desc)
//
///**
// * Model representasi album untuk koleksi photo.
// * @see [[com.ansvia.digaku.model.GalleryCollection]]
// * @param name nama collection.
// * @param desc deskripsi dari collection ini.
// * @param tagsStr tags.
// */
//case class Album(name:String, desc:String, tagsStr:String) extends GalleryCollectionBase(name, desc){
//
//    /**
//     * Url ke picture untuk cover dari album ini.
//     */
//    @Persistent var coverPicUrl:String = ""
//
//    lazy val tags = tagsStr.split(",")
//
//    /**
//     * Taruh photo di album ini.
//     * @param photo photo yang akan ditaruh.
//     */
//    def put(photo:Picture, noTx:Boolean){
//        put(photo.asInstanceOf[Media], noTx)
//    }
//
//
//
//    /**
//     * Untuk meletakkan media pada collection ini.
//     * @param media media yang akan dipindan. lihat [[com.ansvia.digaku.model.Media]]
//     */
//    override def put(media: com.ansvia.digaku.model.Media, noTx:Boolean) {
//        if (!media.isInstanceOf[Picture])
//            throw InvalidParameterException("Only accept Picture")
//
//        super.put(media, noTx)
//    }
//
//    /**
//     * Pindah photo dari album ini ke album lainnya.
//     * @param photo photo yang akan diperiksa.
//     * @param album album target.
//     */
//    def moveTo(photo:Picture, album:Album){
//        moveTo(photo.asInstanceOf[Media], album)
//    }
//
//    /**
//     * Hapus photo dari album ini.
//     * @param photo photo yang akan dihapus.
//     */
//    def remove(photo:Picture){
//        remove(photo.asInstanceOf[Media])
//    }
//
//    /**
//     * Periksa apakah photo exists di album ini.
//     * @param photo photo yang akan diperiksa.
//     * @return
//     */
//    def hasPhoto(photo:Picture) = {
//        hasMedia(photo.asInstanceOf[Media])
//    }
//
//}
//
///**
// * DAO untuk Album.
// */
//object Album extends DaoBase[GraphType, Album]{
//
//    import Group.ALBUM_LOWER_NAME_KEY
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.AlbumRootVertex"
//
//    /**
//     * Buat album baru.
//     * @param name nama album.
//     * @param desc deskripsi album.
//     * @param tags kata kunci album.
//     * @param gallery target gallery.
//     * @return
//     */
//    def create(name:String, desc:String, tags:String, gallery:ChannelGallery) = {
////        transact {
//
//        val normTags = tags.split(",").filter(_.trim.length > 1)
//            .map(_.toLowerCase).slice(0, 5).reduceLeftOption(_ + "," + _).getOrElse("")
//
//        val v = Album(name, desc, normTags).save()
//
//        v.setProperty(ALBUM_LOWER_NAME_KEY, name.toLowerCase)
//
//        addToRoot(v)
//
//        val ed = gallery.getVertex --> ALBUM --> v <()
//        ed.setProperty("timeOrder", System.currentTimeMillis())
//
//        val rv = v.toCC[Album].getOrElse {
//            throw new DigakuException("Cannot create GalleryAlbum, persistent problem?")
//        }
//
////        }
//        db.commit()
//        rv
//    }
//
//    /**
//     * remove album
//     * @param id album
//     * @param gallery target gallery
//     */
//    def remove(id:IDType, gallery:ChannelGallery) = {
//        val albumV = gallery.getVertex.pipe.out(ALBUM)
//            .has("id", id).asInstanceOf[GremPipeVertex]
//
//        val album = albumV.headOption.flatMap(_.toCC[Album])
//
//        if (album.isDefined){
//            super.delete(album.get)
//        }
//
////        transact {
//            albumV.remove()
////        }
//        db.commit()
//    }
//
//}
//
///**
// * DAO untuk galeri group.
// */
//object ChannelGallery extends DaoBase[GraphType, ChannelGallery] {
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.ChannelGalleryRootVertex"
//
//    /**
//     * Buat galeri group.
//     * @param name nama galeri.
//     * @param ch group target.
//     * @return
//     */
//    def create(name:String, ch:Group) = {
//
//        if (ch.hasGallery(name))
//            throw AlreadyExistsException("This group %s already have gallery".format(ch.name))
//
////        transact {
//            val galleryV = ChannelGallery(name).save()
//
//            galleryV.setProperty("gallery.lower-name", ch.getId + "-" + name.toLowerCase)
//
//            addToRoot(galleryV)
//
//            ch.getVertex --> GALLERY --> galleryV
//
//            val rv = galleryV.toCC[ChannelGallery].getOrElse {
//                throw new DigakuException("Cannot create Group gallery, persistent problem?")
//            }
////        }
//        db.commit()
//        rv
//    }
//
////    def getByName(name:String) = {
////        rootVertex.pipe.out(rootVertexLabel).has("gallery.lower-name", name.toLowerCase)
////            .asInstanceOf[GremPipeVertex]
////            .headOption.flatMap(_.toCC[ChannelGallery])
////    }
//
//
//}
//
///**
// * DAO untuk gallery collection.
// */
//object GalleryCollection extends DaoBase[GraphType, GalleryCollection] {
//
//    import Group.ALBUM_LOWER_NAME_KEY
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.GalleryCollection"
//
//    /**
//     * Buat koleksi galeri baru.
//     * @param name
//     * @param desc
//     * @param tags
//     * @param gallery
//     * @return
//     */
//    def create(name:String, desc:String, tags:String, gallery:ChannelGallery) = {
//
////        transact {
//            val v = GalleryCollection(name, desc, tags).save()
//
//            v.setProperty(ALBUM_LOWER_NAME_KEY, name.toLowerCase)
//
//            addToRoot(v)
//
//            val ed = gallery.getVertex --> ALBUM --> v <()
//            ed.setProperty("timeOrder", System.currentTimeMillis())
//
//            val rv = v.toCC[GalleryCollection].getOrElse {
//                throw new DigakuException("Cannot create GalleryAlbum, persitent problem?")
//            }
////        }
//        db.commit()
//        rv
//    }
//
//    /**
//     * Buat galeri baru tanpa parent gallery.
//     * @param name
//     * @param desc
//     * @param tags
//     * @return
//     */
//    def create(name:String, desc:String, tags:String) = {
//
////        transact {
//            val v = GalleryCollection(name, desc, tags).save()
//            val rv = v.toCC[GalleryCollection].getOrElse {
//                throw new DigakuException("Cannot create GalleryAlbum, persitent problem?")
//            }
////        }
//        db.commit()
//
//        rv
//    }
//
//
//}
//
//
//
