package com.ansvia.digaku.model

import com.ansvia.digaku.helpers.AbstractDbAccess
import com.ansvia.graph.AbstractDbObject
import Label._
import com.ansvia.graph.BlueprintsWrapper._
import scala.collection.JavaConversions._

/**
  * Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
  */

trait PostAsUserGroupable extends AbstractDbObject with AbstractDbAccess {

    /**
      * Mendapatkan user group.
      * @return mengembalikan user group ketika thread/response di post sebagai user group
      */
    def getPostAs:Option[UserGroup] = {
        this.getVertex.pipe.out(POST_AS).headOption.flatMap(_.toCC[UserGroup])
    }

    /**
      * Set post as user group
      * @param userGroup
      */
    def setPostAs(userGroup:UserGroup): Unit = {
        if (!getPostAs.exists(_.getId == userGroup.getId)) {
            removePostAs()
            this.getVertex.reload().addEdge(POST_AS, userGroup.getVertex.reload())
        }
    }

    /**
      * Remove post as user group.
      */
    def removePostAs(): Unit = {
        this.getVertex.pipe.outE(POST_AS).remove()
    }

}
