///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.helpers.DbAccess
//
///**
// * Author: robin
// *
// * @deprecated safe to remove after september 2013
// */
//trait DbUser extends DbAccess {
////    def usingDb[T](func: => T):T = {
////        Global.database.using(func)
////    }
////
////    def usingDb[T](func: (TransactionalGraph) => T):T = {
////        Global.database.using(func(db))
////    }
//}
