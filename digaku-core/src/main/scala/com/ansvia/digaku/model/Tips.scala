///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.exc.{InvalidParameterException, DigakuException}
//import com.ansvia.graph.annotation.Persistent
//
///**
// * Author: robin
// * Date: 5/24/13
// * Time: 3:11 PM
// *
// */
//
///**
// * Model untuk tips, jobs, dan guidelines yang muncul untuk user.
// * @param title title untuk tips/jobs/guidelines
//// * @param snip tips snippet (short desc) // ditiadakan menyesuaikan dengan task
//// *             https://mindtalk.atlassian.net/browse/MM-657
// * @param kind tips kind see [[com.ansvia.digaku.model.Tips.kinds]]
// * @param category harus di set (tips, job, guideline)
// *                 see [[com.ansvia.digaku.model.Tips.category]]
// */
//case class Tips(title:String,  kind:Int, category:Int) extends BaseModel[IDType] {
//
//    @Persistent var content:String = ""
//    @Persistent var url = ""
//
//    lazy val kindStr = Tips.kinds.intToStr(kind)
//    lazy val categoryStr = Tips.category.intToStr(category)
//
//}
//
//
//object Tips extends DaoBase[GraphType, Tips] with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.TipsRootVertex"
//
//    object kinds {
//        val TEXT = 1
//        val LINK = 2
//
//        def intToStr(kindInt:Int) = kindInt match {
//            case TEXT => "text"
//            case LINK => "link"
//        }
//
//        def strToInt(kindStr:String) = kindStr match {
//            case "text" => TEXT
//            case "link" => LINK
//        }
//    }
//
//    /**
//     * untuk mengkategorikan content
//     * yang terdiri dari jobs, tips, guidelines
//     */
//    object category {
//        val TIPS = 1
//        val JOB = 2
//        val GUIDELINE = 3
//
//        def intToStr(categoryInt:Int) = categoryInt match {
//            case TIPS => "tips"
//            case JOB => "job"
//            case GUIDELINE => "guideline"
//        }
//
//        def strToInt(categoryStr:String) = categoryStr match {
//            case "tips" => TIPS
//            case "job" => JOB
//            case "guideline" => GUIDELINE
//        }
//    }
//
//    /**
//     * create tips/jobs/guidelines dengan content text
//     * @param title title untuk tips/jobs/guidelines
////     * @param snip snip tips
//     * @param content content untuk tips, jobs, dan guidelines
//     * @param category harus di set (tips, job, guideline)
//     *                 see [[com.ansvia.digaku.model.Tips.category]]
//     * @return
//     */
//    def createText(title:String, content:String, category:Int):Tips = {
//
//        if (title.length == 0)
//            throw InvalidParameterException("Please fill title")
//
////        if (snip.length == 0)
////            throw InvalidParameterException("Please fill snippet")
//
//        if (content.length == 0)
//            throw InvalidParameterException("Please fill content")
//
//
//        val tips = Tips(title, kinds.TEXT, category)
//        tips.content = content
//        tips.save().toCC[Tips].getOrElse {
//            throw new DigakuException("Cannot create Tips")
//        }
//        addToRoot(tips.getVertex)
//
//        db.commit()
//
//        tips
//
//    }
//
//    /**
//     * create tips dengan content link
//     * untuk jobs dan guidelines tidak menggunakan link hanya dgn text saja
//     * @param title title tips
////     * @param snip snip tips
//     * @param url url tips
//     * @param category default-nya pakai tips
//     * @return
//     */
//    def createLink(title:String, url:String, category:Int):Tips = {
//
//        if (title.length == 0)
//            throw InvalidParameterException("Please fill title")
//
////        if (snip.length == 0)
////            throw InvalidParameterException("Please fill snippet")
//
//        if (url.length == 0)
//            throw InvalidParameterException("Please fill url")
//
//
//        val tips = Tips(title, kinds.LINK, category)
//        tips.url = url
//        tips.save().toCC[Tips].getOrElse {
//            throw new DigakuException("Cannot create Tips")
//        }
//        addToRoot(tips.getVertex)
//
//        db.commit()
//
//        tips
//    }
//
//    /**
//     * get list content berdasarkan categorynya
//     * @param category harus di set (tips, job, guideline)
//     *                 see [[com.ansvia.digaku.model.Tips.category]]
//     * @param sinceId id terkecil dari content yang akan kita dapatkan
//     * @param maxId id terbesar dari content yang didapatkan
//     * @param limit batas dari kembalian
//     * @return
//     */
//    def getByCategory(category:Int, sinceId:Option[IDType], maxId:Option[IDType], limit:Int=10):Iterator[Tips] = {
//        this.getListRight(sinceId, maxId, limit).filter(_.category == category)
//    }
//}
//
