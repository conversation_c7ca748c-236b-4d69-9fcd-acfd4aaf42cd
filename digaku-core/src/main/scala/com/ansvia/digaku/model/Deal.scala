///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import java.text.{DecimalFormat, DecimalFormatSymbols}
//import java.util.Date
//import java.util.concurrent.TimeUnit
//
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.event.impl.CreateDealEvent
//import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException, PermissionDeniedException}
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.model.Label._
//import com.ansvia.digaku.utils.{Cache, Digaku.engine.dateUtils}
//import com.ansvia.graph.BlueprintsWrapper._
//import com.ansvia.graph.annotation.Persistent
//import com.thinkaurelius.titan.core.attribute.Decimal
//import com.tinkerpop.blueprints.{Direction, Vertex}
//import org.ocpsoft.prettytime.PrettyTime
//
///**
// * model for Deal
// * @param name product name to sell.
// * @param price product price.
// * @param desc product description.
// * @param currency see [[com.ansvia.digaku.model.Currency]]
// * @param locale localization.
// * @param condition product condition.
// * @param location product location.
// */
//case class Deal(var name:String, var price:Double, var desc:String,
//                var currency:String, var locale:String, var condition:String,
//                var location:String) extends Post(desc) with ContentVersioning with Streamable[IDType] {
//
//    import com.ansvia.digaku.utils.RichDate._
//
//
//    /**
//     * @see [[com.ansvia.digaku.model.PostKind]]
//     */
//    val kind:Int = PostKind.DEAL
//
//    @Persistent var thumbnail = ""
//
//    @Persistent var thumbUrl = ""
//    @Persistent var editReason = ""
//    @Persistent var lastEditTime = 0L
//    @Persistent var lastEditorUserId = 0L
//    @Persistent var editable = true
//
//    /**
//     * array separated by commas.
//     */
//    var photos = Array.empty[String]
//
//    lazy val lastEditor:Option[User] = {
//        User.getById(lastEditorUserId)
//    }
//
//    def getLastEditTimeAge:String = {
//        new PrettyTime(new Date()).format(new Date(lastEditTime))
//    }
//
//    def getLastEditTimeStd:String = {
//        new Date(lastEditTime).toStdFormat
//    }
//
//    /**
//     * Custom deserializer.
//     * @param vertex vertex object.
//     */
//    override def __load__(vertex: Vertex) {
//        super.__load__(vertex)
//        photos = vertex.getOrElse("photos", "").split(",").map(_.trim)
//    }
//
//
//    override def __save__(v:Vertex){
//        v.setProperty("photos", photos.reduceLeftOption(_ + "," + _).getOrElse(""))
//    }
//
//    lazy val decimalFormatSymbol = {
//        val dfs = new DecimalFormatSymbols()
//        dfs.setDecimalSeparator('.')
//        dfs.setGroupingSeparator(',')
//        new DecimalFormat("#,##0", dfs)
//    }
//
//    def priceIn(cur:String):String = {
//
//        cur match {
//            case Currency.IDR =>
//                "Rp. %s,-".format(decimalFormatSymbol.format(price))
//            case Currency.USD =>
//                "$ %s,-".format(decimalFormatSymbol.format(price))
//        }
//    }
//
//    override def toString = "Deal(%s, %s)".format(getId, name)
//}
//
//
//object Deal extends DaoBase[GraphType, Deal] with DbAccess
//    with Popularable
//    with Slf4jLogger {
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.DealDaoRootVertex"
//
//    import scala.collection.JavaConversions._
//
//    private lazy val cache = Cache.steady[String, Seq[(Deal, Double)]]("popular-deal",2,1,TimeUnit.MINUTES){ key =>
//        val s = key.split(":")
//        val offset = s(1).toInt
//        val limit = s(2).toInt
//        getPopularInternal(offset, limit)
//    }
//
//
//    /**
//     * Create deal
//     * @param creator deal creator.
//     * @param name product name.
//     * @param price product price.
//     * @param desc product description.
//     * @param currency price currency.
//     * @param locale localization.
//     * @param condition product condition.
//     * @param location seller/product location.
//     * @param origin product origin.
//     * @return
//     */
//    def create(creator: User, name:String, price:Double, desc:String,
//                   currency:String, locale:String, condition:String,
//                   location:String, origin: Origin[GraphType],
//                   triggerEvent:Boolean=true): Deal =
//    {
//        assert(creator != null, "no creator")
//        assert(origin != null, "no origin")
//
//        origin match {
//            case ch:Group =>
//                if (!ch.userCanCreateDeal_?(creator)){
//                    if (ch.getWhoCanCreateDeal.isEmpty){
//                        throw PermissionDeniedException("Only the owner can create deal")
//                    } else{
//                        throw PermissionDeniedException("you should be a %s to create deal"
//                            .format(ch.getWhoCanCreateDeal.reduceLeftOption(_ + " or " + _).getOrElse("")))
//                    }
//                }
//
//                if (ch.isBlockedUser(creator)){
//                    throw PermissionDeniedException("you has been blocked in this group")
//                }
//
//            case _ =>
//        }
//
//        if (desc.length < 160)
//            throw InvalidParameterException("Description too short, min 160 characters, currently " + desc.length)
//
//        if (price < 0.0)
//            throw InvalidParameterException("Invalid price, min 1")
//
//        if (!Currency.isSupported(currency))
//            throw InvalidParameterException("Currency %s not supported".format(currency))
//
//        if (location.length < 2)
//            throw InvalidParameterException("Min location length 2 characters")
//
//        assert(currency.length > 0, "No currency?")
//        assert(locale.length > 0, "No locale?")
//
//        var dealO:Option[Deal] = None
//
////        transact {
//            val deal = Deal(name, price, desc, currency, locale, condition, location).save().toCC[Deal].getOrElse{
//                throw new DigakuException("Cannot create deal")
//            }
//
//            val v = deal.getVertex
//
//            val now = Digaku.engine.dateUtils.nowMilis
//
//            v.setProperty("creationTime", now)
//
//            val rootEdge = addToRoot(v)
//            rootEdge.setProperty("kind", PostKind.DEAL)
//
//            val originV = origin.getVertex
//            val dealV = deal.getVertex
//
////            val wroteE = creator --> WROTE --> dealV <()
//            deal --> ORIGIN --> originV
//
//            originV.setProperty("dealCount", v.getOrElse("dealCount", 0) + 1)
//
//            // hanya digunakan untuk ordering
//            // untuk dapetin waktu pembuatan gunakan creationTime instead.
////            wroteE.setProperty("time", now)
////            originE.setProperty("time", -now)
//
////            wroteE.setProperty("creationTime", now)
//
//
//            val creatorV = creator.getVertex
//
//            val publishE = creator.getYMPart(CollectionVertex.Kind.PUBLISH_CONTENT) --> PUBLISH_CONTENT --> deal <()
//
//            creatorV.setProperty("lastPostTime", now)
//            publishE.setProperty("timeOrder", now)
//
//            if (origin.kind == OriginKind.GROUP){
//                val streamE = addToStream(originV, dealV)
//                streamE.setProperty("sticky", false)
//                streamE.setProperty("locked", false)
//                streamE.setProperty("post.kind", PostKind.DEAL)
//                streamE.setProperty("originId", originV.getId)
//
//            }
//
//            dealO = Some(deal)
////        }
//
//        db.commit()
//
//        if (triggerEvent)
//            Digaku.engine.eventStream emit CreateDealEvent(creator, dealO.get)
//
//        dealO.get
//    }
//
//    protected def addToStream(channelV:Vertex, targetItemV:Vertex, timeFromCreation:Boolean=false) = {
//        val ed = channelV --> STREAM --> targetItemV <()
//
//        // gak perlu pake targetId karena gak terlalu berguna?
//        ed.setProperty("targetId", targetItemV.getId)
//
//        if (timeFromCreation){
//            ed.setProperty("timeOrder", targetItemV.getOrElse("creationTime", 0L))
//        }else{
//            ed.setProperty("timeOrder", System.currentTimeMillis())
//        }
//
//        ed
//    }
//
//
//    /**
//     * Get popular deal post dari cache
//     * @param offset post offset
//     * @param limit post limit
//     * @return
//     */
//    def getPopular(offset:Int=0, limit:Int=10):Seq[(Deal, Double)] = {
//        cache.apply("popular-deal:%d:%d".format(offset, limit))
//    }
//
//    /**
//     * Get popular deal post langsung dari db
//     * @param offset
//     * @param limit
//     * @return
//     */
//    private def getPopularInternal(offset:Int=0, limit:Int=10):Seq[(Deal, Double)] = {
//
//        val _rootVertex = db.getVertex(rootVertex.getId)
//
//        assert(_rootVertex != null, "_rootVertex is null")
//
//        val vertices =
//            _rootVertex.pipe.outE(rootVertexLabel)
//                .has("kind", PostKind.DEAL)
//                .range(0, 2000)
//                .inV().as("deal")
//                .out(ORIGIN)
//                .has("privated", false)
//                .back("deal").asInstanceOf[GremPipeVertex]
//                .iterator()
//
//
//            clearPopular()
//            for (v <- vertices){
//
//                val likesCount = v.getOrElse("likesCount", 0)
//                val viewsCount = v.getOrElse("viewsCount", 0)
//
//                val responseCount = v.getOrElse("responseCount", 0)
//                val creationTime = v.getOrElse("creationTime", 0L)
//
//                val s = (responseCount * 15) + (likesCount * 5) + (viewsCount / 2)
//
//                val order = math.log10(math.max(math.abs(s), 1))
//                val sign = if (responseCount == 0 || likesCount == 0) 0 else if (s > -1) 1 else -1
//
//                val seconds = ((creationTime / 1000) - 1134028003)
//                val score:Double = order.toDouble + sign.toDouble * seconds.toDouble / 45000D
//
//                addPopular(v, score)
//            }
//
//
//        db.commit()
//
//        popularRootVertex.pipe.outE(POPULAR)
////            .has("kind", Popular.kinds.DEAL)
//            .inV()
//            .hasNot("blocked", true)
//            .range(offset, (offset + limit) - 1)
//            .iterator.flatMap(_.toCC[Deal])
//            .map( deal => (deal, {
//                    val x = deal.getVertex.getEdges(Direction.IN, POPULAR).head.getProperty[Decimal]("score")
//                    if (x != null)
//                        x.floatValue()
//                    else
//                        0.0
//                }
//            ) ).toSeq
//
//    }
//
//    def clearCache() = {
//        cache.invalidateAll()
//    }
//
//
//}
//
//
