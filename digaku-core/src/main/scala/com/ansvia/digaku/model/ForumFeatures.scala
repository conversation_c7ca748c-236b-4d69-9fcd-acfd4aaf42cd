/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

/**
 * Author: bramoepoetra
 *
 */
object ForumFeatures {

    val ARTICLE = "article"
    val EVENT = "event"

    val supportedFeatures = Array(
        EVENT, ARTICLE
    )

    def isSupported(feature:String) =
        supportedFeatures.contains(feature)
}
