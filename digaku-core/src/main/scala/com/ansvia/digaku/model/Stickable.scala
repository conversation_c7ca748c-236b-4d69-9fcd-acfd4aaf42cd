/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.util.concurrent.TimeUnit

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.event.impl.{StickPictureEvent, StickPostEvent}
import com.ansvia.digaku.exc.PermissionDeniedException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.persistence.MapDb
import com.ansvia.graph.AbstractDbObject
import com.ansvia.digaku.database.GraphCompat.tx
import com.ansvia.digaku.helpers.GremlinHelpers.gremlin


/** $regression:$ Fungsional sticky.
 */
/**
 * Author: alamybs
 *
 */
trait Stickable extends AbstractDbObject with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._
    import scala.collection.JavaConversions._

    /**
     * digunakan untuk set sticky
     * @param state boolean
     */
    def setSticky(state:Boolean, noTx:Boolean=false) {

        if (isSticked != state) {
            this match {
                case post: HasOrigin[GraphType] =>
                    post.origin match {
                        case forum: Forum =>
                            if (state && forum.stickyPostCount >= 5) {
                                throw PermissionDeniedException("Cannot pin post more than 5 threads")
                            }

                            if (state) {
                                forum.incrementStickyPostCount()
                            } else {
                                forum.decrementStickyPostCount()
                            }

                        case _ =>
                    }
                case _ =>
            }

            gremlin(getVertex).inE(STREAM).iterator().foreach { ed =>
                ed.setProperty("post.sticky", state)
            }

            if (!noTx)
                db.commit()

            this match {
                case post: Post =>
                    Digaku.engine.eventStream emit StickPostEvent(post)
                case pic: Picture =>
                    Digaku.engine.eventStream emit StickPictureEvent(pic)
                case _ =>
            }
        }
    }

    /**
     * cek apakah post di sudah di sticky
     * @return true ketika di-sticky
     */
    def isSticked:Boolean = {
//        this.getVertex.pipe.inE(STREAM)
//            .has("post.sticky", true)
//            .headOption.exists(_.getOrElse("post.sticky", false))
        Stickable.isSticked(this)
    }
}
/*$regression:end$*/



object Stickable extends DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._
    import scala.collection.JavaConversions._

//    lazy val isStickedCache2 = MapDb.compressed.createHashMap("stickable_ns_cache")
//        .expireAfterAccess(60, TimeUnit.MINUTES)
//        .expireAfterWrite(120, TimeUnit.MINUTES)
//        .expireMaxSize(5000)
//        .make[String, Option[Boolean]]()


    def isSticked(s:Stickable) = {
        //di comment dulu karena penggunaan disini tidak terdistribusi ke semua server
        //hanya di server ini saja
//        val sticked = isStickedCache2.get(s.getVertex.getId.toString)

//        if (sticked == null || sticked.isEmpty) {

            val rv =
                tx { t =>
                    t.getVertex(s.getVertex.getId).pipe.inE(STREAM)
                        .has("post.sticky", true)
                        .headOption.exists(_.getOrElse("post.sticky", false))
                }

//            isStickedCache2.put(s.getVertex.getId.toString, Some(rv))

            rv
//        } else {
//            sticked.get
//        }
    }


}
