///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.graph.BlueprintsWrapper._
//import scala.collection.JavaConversions._
//import com.ansvia.graph.AbstractDbObject
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.event.impl.{UserConnectTwEvent, UserConnectFbEvent}
//import com.ansvia.digaku.exc.{AlreadyExistsException, UnsupportedException}
//import com.tinkerpop.blueprints.Vertex
//
///**
// * Author: nadir
// *
// */
//trait Connectable3rdSite extends AbstractDbObject with DbAccess {
//
//    import Label._
//    import com.ansvia.graph.gremlin._
//
//    /**
//     * digunakan untuk menambahkan create FacebookInfo dan menambahkan edge connect_fb
//     * yang menggambil property user "fb.id"
//     * jika fb.id empty maka akan di throw ke UnsupportedException
//     * @return
//     */
//    def connectFacebookInfo(emit:Boolean=true):FacebookInfo = {
//        val user = this.asInstanceOf[User]
//
//        val fbId = user.reload().getVertex.get[String]("fb.id").getOrElse("")
//
//        if (fbId.trim.isEmpty)
//            throw UnsupportedException("users can't connected to facebook")
//
//        val facebookInfo = FacebookInfo.create(fbId)
//
//        if (facebookInfo.isUsed && this.getFacebookInfo.isDefined && facebookInfo != this.getFacebookInfo.get)
//                throw AlreadyExistsException("Facebook id is already in use")
//
//        if (!hasFacebookInfo) {
//            this --> CONNECT_FB --> facebookInfo
//            db.commit()
//        }
//
//        if (emit) {
//            Digaku.engine.eventStream.emit(UserConnectFbEvent(user))
//        }
//        facebookInfo
//    }
//
//    /**
//     * digunakan untuk mengambil FacebookInfo
//     * @return
//     */
//    def getFacebookInfo:Option[FacebookInfo] = {
//        this.getVertex.pipe.out(CONNECT_FB).headOption.flatMap(_.toCC[FacebookInfo])
//    }
//
//    /**
//     * check apakah user sudah memiliki FacebookInfo
//     * @return
//     */
//    def hasFacebookInfo:Boolean = {
//        this.getVertex.pipe.out(CONNECT_FB).headOption.isDefined
//    }
//
//    /**
//     * digunakan untuk mendapatkan user
//     * yang berteman di facebook dan join mindtalk
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def getFriendsFacebook(offset:Int, limit:Int):Iterator[User] = {
//        this.getVertex.pipe.out(CONNECT_FB)
//            .out(FRIEND)
//            .in(CONNECT_FB)
//            .range(offset, limit)
//            .iterator()
//            .flatMap(_.toCC[User])
//    }
//
//    /**
//     * Get friends from facebook by name
//     * @param name nama user
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def getFriendsFacebookByName(name:String, offset: Int, limit: Int):Iterator[User] = {
//        this.getVertex.pipe.out(CONNECT_FB)
//            .out(FRIEND)
//            .in(CONNECT_FB)
//            .filter { (v:Vertex) =>
//                v.getOrElse("user.lower-name", "").toLowerCase.contains(name.toLowerCase)
//            }
//            .range(offset, limit)
//            .iterator()
//            .flatMap(_.toCC[User])
//    }
//
//
//    /**
//     * digunakan untuk remove facebook info dari user
//     */
//    def disconnectFacebookInfo() {
//        this.getVertex.pipe.outE(CONNECT_FB).iterator().foreach(_.remove())
//        db.commit()
//    }
//
//
//    /**
//     * connect-kan user ke twitter info
//     * @return see [[com.ansvia.digaku.model.TwitterInfo]]
//     */
//    def connectTwitterInfo(emit:Boolean=true) = {
//        val user = this.asInstanceOf[User]
//
//        val twId = user.reload().getVertex.get[String]("tw.id").getOrElse("")
//
//        if (twId.trim.isEmpty)
//            throw UnsupportedException("users can't connected to twitter")
//
//        val twitterInfo = TwitterInfo.create(twId)
//
//        if (twitterInfo.isUsed)
//            throw UnsupportedException("Twitter id is already in use")
//
//
//        if (!hasTwitterInfo) {
//            this --> CONNECT_TW --> twitterInfo
//            db.commit()
//        }
//
//        if (emit) {
//            Digaku.engine.eventStream.emit(UserConnectTwEvent(user))
//        }
//        twitterInfo
//
//    }
//
//    /**
//     * cek apakah user sudah memiliki twitter info
//     * @return
//     */
//    def hasTwitterInfo:Boolean = {
//        this.getVertex.pipe.out(CONNECT_TW).headOption.isDefined
//    }
//
//    /**
//     * digunakan untuk user mendapatkan twitter info
//     * @return
//     */
//    def getTwitterInfo:Option[TwitterInfo] = {
//        this.getVertex.pipe.out(CONNECT_TW).headOption.flatMap(_.toCC[TwitterInfo])
//    }
//
//    /**
//     * digunakan untuk mendapatkan teman twitter yang sudah connect ke mindtalk
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def getFriendsTwitter(offset:Int, limit:Int):Iterator[User] = {
//        this.getVertex.pipe.out(CONNECT_TW)
//            .out(FOLLOW)
//            .in(CONNECT_TW)
//            .range(offset, limit)
//            .iterator()
//            .flatMap(_.toCC[User])
//    }
//
//    /**
//     * Get friends from twitter by name
//     * @param name nama user
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def getFriendsTwitterByName(name:String, offset:Int, limit:Int):Iterator[User] = {
//        this.getVertex.pipe.out(CONNECT_TW)
//            .out(FOLLOW)
//            .in(CONNECT_TW)
//            .filter { (v:Vertex) =>
//                v.getOrElse("user.lower-name", "").toLowerCase.contains(name.toLowerCase)
//            }
//            .range(offset, limit)
//            .iterator()
//            .flatMap(_.toCC[User])
//    }
//
//
//    /**
//     * disconnect twitter info dari user
//     */
//    def disconnectTw() {
//        this.getVertex.pipe.outE(CONNECT_TW).iterator().foreach(_.remove())
//        db.commit()
//    }
//
//}
