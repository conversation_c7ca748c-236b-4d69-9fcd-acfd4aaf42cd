/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.graph.{AbstractIDGetter, AbstractDbObject}
import com.ansvia.digaku.Types.IDType

/**
 * Author: robin
 *
 */

/**
 * Semua model yang diturunkan dari Media ini
 * akan mendukung jadi koleksi media dari User / Group.
 */
trait Media extends AbstractDbObject with AbstractIDGetter[IDType] {
    this: BaseModel[IDType] =>

    def getCreationTime = this.creationTime
}
