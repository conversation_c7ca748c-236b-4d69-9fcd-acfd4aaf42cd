/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

object SexType {
    val FEMALE = 0
    val MALE = 1
    val UNISEX = 5

    /**
     * convert sex type code to string.
     * @param code sex type code.
     * @return
     */
    def toStr(code:Int) = {
        code match {
            case MALE => "male"
            case FEMALE => "female"
            case UNISEX => "?"
        }
    }


}
