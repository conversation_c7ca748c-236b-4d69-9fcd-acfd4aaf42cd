package com.ansvia.digaku.model

import com.ansvia.digaku.Types.{GraphType, IDType}
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException, PermissionDeniedException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.graph.BlueprintsWrapper.DbObject
import com.ansvia.graph.BlueprintsWrapper._
import Label._
import com.ansvia.digaku.Digaku
import scala.collection.JavaConversions._

/**
  * Author: Buaetin Nadir (<EMAIL>)
  */

case class TopicCategory(var name:String, var topicId:Long) extends BaseModel[IDType] with DbObject with DbAccess {

    /**
      * Menambahkan sub category
      * @param category
      */
    def addSubCategory(category:TopicCategory): Unit = {

        if (getParentCategory().nonEmpty) {
            throw PermissionDeniedException("Tidak bisa menambah sub category")
        }

        val ed = this.getVertex --> HAS_CATEGORY --> category.getVertex <()
        ed.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)

        db.commit()
    }


    /**
      * Mendapatkan list sub category
      * @return
      */
    def getSubCategories():Iterator[TopicCategory] = {
        this.getVertex.pipe.out(HAS_CATEGORY).iterator().flatMap(_.toCC[TopicCategory])
    }

    /**
      * Mendapatkan topic category
      * @return
      */
    def getTopic:Option[Topic] = {
        db.getVertex(topicId).toCC[Topic]
    }

    /**
      * Mendapatkan parent category dari sub category
      * @return
      */
    def getParentCategory():Option[TopicCategory] = {
        this.getVertex.pipe.in(HAS_CATEGORY).iterator().toSeq.flatMap(_.toCC[TopicCategory])
            .headOption
    }
}

object TopicCategory extends DaoBase[GraphType, TopicCategory]{
    override val ROOT_VERTEX_CLASS:String = "com.ansvia.digaku.model.TopicCategoryRootVertex"

    /**
      * Create category
      * @param name nama category
      * @param topic topic category
      * @return
      */
    def create(name:String, topic:Topic): TopicCategory = {
        val _name = name.trim

        if(_name.length < 3 || _name.length > 160) {
            throw InvalidParameterException("Name Category minimum 3 and maximum 160 characters")
        }

        val category = TopicCategory(_name, topic.getId)
        val vertex = category.save()
        val newCategory = vertex.toCC[TopicCategory].getOrElse{
            throw new DigakuException("Cannot create topic category with name: %s".format(_name))
        }

        addToRoot(vertex)

        db.commit()

        newCategory
    }

    /**
      * Delete category
      * @param obj
      * @param m
      */
    override def delete(obj:TopicCategory)(implicit m:Manifest[TopicCategory]):Unit = {
        obj.getSubCategories().foreach { cat =>
            TopicCategory.delete(cat)
        }

        super.delete(obj)
    }
}
