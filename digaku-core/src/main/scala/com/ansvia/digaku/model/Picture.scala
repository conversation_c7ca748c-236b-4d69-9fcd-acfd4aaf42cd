/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.util.Date
import java.util.concurrent.{Callable, TimeUnit}

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.{DaoBase, PictureDao}
import com.ansvia.digaku.event.impl.{BlockedEvent, ClosedEvent}
import com.ansvia.digaku.exc.{DigakuException, NotExistsException, PermissionDeniedException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.persistence.CounterProviderOperator
import com.ansvia.digaku.stream.StreamBuilder
import com.ansvia.digaku.utils.DateUtils
import com.ansvia.digaku.utils.RichDate._
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.graph.annotation.Persistent
import com.google.common.cache.CacheBuilder
import com.thinkaurelius.titan.core.attribute.Decimal
import com.tinkerpop.blueprints.{Direction, Vertex}
import org.ocpsoft.prettytime.PrettyTime

import scala.collection.JavaConversions._
import scala.util.hashing.MurmurHash3

/**
 * Author: nadir
 *
 */
abstract class PictureBase(title:String) extends PublishableContent with Stickable
    with ViaAppInfo
    with Tagable
    with WatchableContent {

    private lazy val counter = Digaku.engine.counterProvider("picture-counter-" + getId)

    @Persistent var smallUrl = ""
    @Persistent var smallScaledUrl = ""
    @Persistent var mediumUrl = ""
    @Persistent var mediumScaledUrl = ""
    @Persistent var largeUrl = ""

    @Persistent var lastUpdate = 0L
    @Persistent var sticky = false

    /**
     * @see [[com.ansvia.digaku.model.PostKind]]
     */
    @Persistent val kind:Int = PostKind.PICTURE

    @Persistent var editReason = ""
    @Persistent var lastEditTime = 0L
    @Persistent var lastEditorUserId = 0L
    @Persistent var streamKind = StreamKind.PICTURE

    lazy val lastEditor:Option[User] = {
        User.getById(lastEditorUserId)
    }

    lazy val kindStr = PostKind.kindIntToStr(kind)

    def getTitle = title

    def getLastEditTimeAge:String = {
        new PrettyTime(new Date()).format(new Date(lastEditTime))
    }

    def getLastEditTimeStd:String = {
        new Date(lastEditTime).toStdFormat
    }

    lazy val origin: Origin[GraphType] = Picture.getOrigin(this).get


    /**
     * digunakan untuk set blocked picture dan akan diindex oleh digaku search engine
     * @param block block state true or false (true means block otherwise unblock).
     * @param blocker user who block/unblock this object.
     * @return
     */
    override def setBlocked(block:Boolean, blocker:User) = {

        transact {
            super.setBlocked(block, blocker)

            this.save()
        }

        Digaku.engine.eventStream.emit(BlockedEvent(this))

        this

    }

    /**
     * Digunakan untuk set closed picture dan akan diindex oleh digaku search engine
     * @param close close state true or false (true means close otherwise open).
     * @param closer user who close/open this object.
     * @param reason reason why you closed this object.
     * @return
     */
    override def setClosed(close:Boolean, closer:User, reason:String) = {

        transact {
            super.setClosed(close, closer, reason)
            this.save()
        }

        Digaku.engine.eventStream.emit(ClosedEvent(this))

        this

    }

    /**
     * Get pretty printed creation time age
     * relative to current time.
     * ex: 3 hours ago.
     * @return
     */
    def getCreationAge:String = {
        new PrettyTime(new Date()).format(new Date(creationTime))
    }


    /**
     * Get counter provider used by this model.
     * @return
     */
    def getCounter: CounterProviderOperator = counter

    /**
     * Custom deserializer.
     * @param vertex vertex object.
     */
    override def __load__(vertex: Vertex) {
        super.__load__(vertex)
    }

    override def __save__(v:Vertex) = {
        v.setProperty("lastUpdatedTime", Digaku.engine.dateUtils.nowMilis)
        super.__save__(v)
    }

    override def hash:String = {
        val key = title.trim + mediumUrl.trim
        if (key.length == 0)
            getId.toString
        else
            MurmurHash3.stringHash(key).toString
    }

}

/**
 * Model picture yang merepresentasikan semua photo dan gambar pada Digaku.
 * ini bisa masuk di album dan di-referensikan oleh [[com.ansvia.digaku.model.FreePicture]].
 * @param title judul dari picture.
 */
case class Picture(var title:String) extends PictureBase(title)
//    with ContentVersioning
    with Likable
    with Responable
    with Embeddable[IDType]
    with Media {

    override def toString = {
        if (title.length > 0) {
            val cont = title.replaceAll("\\W+", " ")
            val nc = if(cont.length > 50)
                cont.substring(0, 49) + "..."
            else
                cont
            "Picture(" + id + ", " + nc + ")"
        } else {
            "Picture(" + id + ", empty-title)"
        }
    }

    /**
     * override set deleted ketika picture di embed oleh picture group
     * akan ngecek picture group tersebut apakah masih memiliki embed picture atau tidak
     * dan ketika tidak memiliki embeded picture maka picture group akan ikut di hapus juga.
     * @param deletedRole see [[com.ansvia.digaku.model.DeletedRole]]
     * @param userDeleting user yang delete
     * @param deletedReason deleted reason
     * @param collected set false ketika di override dan ingin menambahkan property ke dalam collect
     */
    override def setDeleted(deletedRole:String, userDeleting:User, deletedReason:String = "", collected:Boolean=false) {

        super.setDeleted(deletedRole, userDeleting, deletedReason, collected)

        val edge = Deletable.collect(this, noTx = true)
        edge.setProperty("deletedObject", "Picture")
        edge.setProperty("deletedKind", "Streamable")
        edge.setProperty("originId", this.origin.getId)
        db.commit()

        //check whether this picture is marked as non-publish or not
        //this value is set on PostRest, see [[com.ansvia.digaku.restapi.PostRest]]
        if (this.getVertex.pipe.in(EMBED).hasNot("publish", false).count() > 0) {
            //check whether this picture is embedded in a picture group to check if the picture group has
            //other pictures that hasn't been deleted yet
            this.getVertex.pipe.in(EMBED).out(EMBED).hasNot("deleted", true).count() match {
                case 1 =>
                    //add last single picture to stream
                    val pic = this.getVertex.pipe.in(EMBED).out(EMBED).hasNot("deleted", true)
                        .iterator().toSeq.headOption.flatMap(_.toCC[Picture]).getOrElse {
                        throw NotExistsException("Picture not exists")
                    }

                    //mark as publish in picture Group
                    //digunakan agar dapat menampilkan sisa 1 picture yang tidak didelete dari picture group
                    //tanpa mendelete hubungan embeded dengan parent nya (picture group)
                    pic.getVertex.pipe.inE(PUBLISH_CONTENT).iterator().foreach(ed => ed.setProperty("publish", true))

                    this.origin match {
                        case ch:Forum =>
                            //add to creator stream
                            StreamBuilder.addToStream(pic.creator, pic, timeFromCreation = true)

                            //add to stream group
                            val streamE = StreamBuilder.addToStream(ch, pic, timeFromCreation = true)

                            streamE.setProperty("post.sticky", false)
                            streamE.setProperty("locked", false)
                            streamE.setProperty("post.kind", PostKind.kindStrClassToInt(pic.getClass.getName))
                            streamE.setProperty("originId", origin.getId)
                            streamE.setProperty("targetId", pic.getId)

                        case _ =>
                            //add to creator stream
                            StreamBuilder.addToStream(pic.creator, pic, timeFromCreation = true)
                    }

//                    //delete parent picture group
//                    val picGroup = this.getVertex.pipe.in(EMBED).headOption.flatMap(_.toCC[PictureGroup]).getOrElse {
//                        throw NotExistsException("Picture group not exists")
//                    }
//                    if (!picGroup.isDeleted) {
//                        picGroup.setDeletedAdvance(deletedType, deletedRole, userDeleting, deletedReason, collected, deleteAllPictures = false)
//                    }

                    db.commit()

//                    Digaku.engine.eventStream.emit(ReplacePictureEvent(picGroup.creator, pic))
//                    Digaku.engine.eventStream.emit(CreatePictureEvent(picGroup.creator, pic))
                case 0 =>
//                    this.getVertex.pipe.in(EMBED).headOption.flatMap(_.toCC[PictureGroup])
//                        .foreach { picGroup =>
//                        if (!picGroup.isDeleted)
//                            picGroup.setDeleted(deletedType, deletedRole, userDeleting, deletedReason, collected)
//                    }
                case _ =>
            }
        }
    }

    /**
     * cek apakah picture diembed oleh model lain
     * seperti di embed PictureGroup
     * true ketika di embed
     * @return
     */
    def isEmbeddedPicture = {
        this.getVertex.pipe.in(EMBED).range(0,1).iterator().hasNext
    }

    override def __save__(vertex: Vertex){
        super.__save__(vertex)
        if (id > 0){
            vertex.setProperty("likesCount", getLikesCount)
        }
    }
}

/**
 * Ini sama kayak picture, bedanya sifatnya bebas, artinya semua post yang mengandung pic
 * akan terekstrak pic-nya dan menjadi assets untuk user yang membuatnya.
 * Sebagai contoh user itu sharing pic di via simple post (status) user tersebut
 * akan mendapatkan koleksi picture via label HAS_IMAGE.
 * @param title image title ini bisa diekstrak dari simple post atau article title.
 */
case class FreePicture(var title:String, url:String)
    extends PictureBase(title) with HasCreator with Media {

    /**
     * override this for custom label.
     * @return
     */
    override protected def creatorLabel = HAS_PICTURE


}

object Picture extends PictureDao[IDType, GraphType] with Popularable {


    /**
     * Untuk mendapatkan picture apapun yang diturunkan dari
     * PictureBase berdasarkan id.
     * @param id picture ID.
     * @return
     */
    def getPictureFromBaseById(id:IDType):Option[PictureBase] = {
        val v = db.getVertex(id)
        if (v != null){
            v.getOrElse("_class_", "") match {
                case "com.ansvia.digaku.model.Picture" |
                    "com.ansvia.digaku.model.FreePicture" |
                    "com.ansvia.digaku.model.PictureBase" |
                    "com.ansvia.digaku.model.PictureGroup" =>
                    v.toCC[PictureBase]
                case _ =>
                    None
            }
        }else{
            None
        }
    }


    /**
     * kalkulasi dan mendapatkan popular picture.
     * Popular picture di sini dikalkulasikan oleh service external
     * menggunakan digaku-shell > build-popular-pictures
     * yang biasanya dijalankan secara berkala menggunakan crontab.
     * @param offset starting offset.
     * @param limit ends limit.
     * @return
     */
    def loadPopularPictureInternal(offset:Int, limit:Int): Seq[(Picture, Double)] = {
        popularRootVertex.pipe.outE(POPULAR)
            .inV()
            .hasNot("blocked", true)
            .dedup()
            .range(offset, (offset + limit) - 1)
            .iterator.flatMap(_.toCC[Picture])
            .map { pic =>
                val x = pic.getVertex.getEdges(Direction.IN, POPULAR).head.getProperty[Decimal]("score")
                val score = if (x != null) x.floatValue() else 0.0
                (pic, score)
            }.toSeq
    }


    /**
     * digunakan untuk mendapatkan popular picture
     * @param offset
     * @param limit
     * @param cached
     * @return
     */
    def getPopularPictures(offset:Int, limit:Int, cached:Boolean): Seq[(Picture, Double)] =
        loadPopularPictureInternal(offset, limit)


    /**
     * refresh popular picture cache
     */
    def refreshPopularPictureCache() {
        //        vertexCache.refresh("popular-picture:0:10")
    }



    lazy val originCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build[String,Option[Origin[GraphType]]]()

    def getOrigin(pb:PictureBase) = {
        originCache.get(pb.getId.toString, new Callable[Option[Origin[GraphType]]]{
            def call(): Option[Origin[GraphType]] = {
                pb.reload()
                val v = pb.getVertex.pipe.out(ORIGIN).range(0,0).headOption
                if (v.isDefined){
                    try {
                        val rv = v.get.toCC[Forum]
                        if (rv.isDefined)
                            rv.asInstanceOf[Option[Origin[GraphType]]]
                        else {
                            val rv2 = v.get.toCC[User]
                            if (rv2.isDefined)
                                rv2
                            else
                                None
                        }
                    }catch{
                        case e:NullPointerException =>
                            error("no creator for picture %s".format(pb.getId))
                            None
                    }
                }else{
                    error("no origin for picture %s".format(this))
                    None
                }
            }
        })
    }
}

object FreePicture extends DaoBase[GraphType, FreePicture] with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._


    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.FreePictureRootVertex"

    def create(title:String, url:String, owner:User):FreePicture = {

        val fPicture = createNoTx(title, url, owner)
        db.commit()
        fPicture
    }

    def createNoTx(title:String, url:String, owner:User):FreePicture = {
        val newPic = FreePicture(title, url).save().toCC[FreePicture].getOrElse {
            throw new DigakuException("Cannot create FreePicture, persistent error.")
        }

        newPic.mediumUrl = url
        addToRoot(newPic.getVertex)

//        /**
//         * kasih tanda kalo url belum dilokalisasi,
//         * dengan ini url akan dilokalisasi oleh image lokalizer engine
//         * yang jalan di belakang layar.
//         */
//        edRoot.setProperty("urlLocalized", 0)

        val edHasPic = owner --> HAS_PICTURE --> newPic <()
        edHasPic.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)

        newPic
    }
}

/**
 * Ini untuk menangani profile picture
 * Merupakan Base class dari :
 * [[com.ansvia.digaku.model.ProfilePicDefault]]
 * [[com.ansvia.digaku.model.ProfilePic]]
 *
 * @param url
 */
abstract class ProfilePicBase(url:String)
    extends BaseModel[IDType] with Media with DbAccess  {

    @Persistent var smallUrl = ""
    @Persistent var mediumUrl = ""
    @Persistent var originalUrl = ""

    def updatePic(small:String, medium:String, large:String) {
        this.smallUrl = small
        this.mediumUrl = medium
        this.originalUrl = large
    }


}

/**
 * Untuk menangani foto profile default yang diberikan ke user
 * @param url
 */
case class ProfilePicDefault (url:String)
    extends ProfilePicBase(url) {
    @Persistent var avatarName = ""

    lazy val pairedUser:Option[User] = {
        getVertex.pipe.in(HAS_PROFILE_PICTURE_DEFAULT).headOption.flatMap(_.toCC[User])
    }
}

object ProfilePicDefault extends DaoBase[GraphType, ProfilePicDefault] with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.ProfilePicDefaultRootVertex"

    /**
     * Untuk create avatar default
     * @param url url image
     * @param name nama avatarnya
     * @param owner user creator see [[com.ansvia.digaku.model.User]]
     * @return
     */
    def create(url:String, name:String, owner:User):ProfilePicDefault = {
        if (owner.role == UserRole.USER)
            throw PermissionDeniedException("Permission Denied")

        val rv = createNoTx(url, name, owner)
        db.commit()

        rv
    }

    /**
     * Untuk create avatar default
     * @param url url image
     * @param name nama avatarnya
     * @param owner user creator see [[com.ansvia.digaku.model.User]]
     * @return
     */
    def createNoTx(url:String, name:String, owner:User):ProfilePicDefault = {
        val newPic = ProfilePicDefault(url).save().toCC[ProfilePicDefault].getOrElse {
            throw new DigakuException("Cannot create default Profile Picture, persistent error.")
        }

        newPic.originalUrl = url
        newPic.avatarName = name

        addToRoot(newPic.getVertex)

        val edHasPic = owner.reload() --> CREATE --> newPic.reload() <()
        edHasPic.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)

        newPic
    }

    /**
     * get avatar default dari database berdasarkan namanya.
     * @param name nama user
     * @return
     */
    def getByName(name: String): Option[ProfilePicDefault] = {

        rootVertex.pipe.out(rootVertexLabel)
            .has("avatarName", name).headOption.flatMap(_.toCC[ProfilePicDefault])
    }

    /**
     * Mendapatkan semua photo profile default
     * @return
     */
    def getAllDefaultProfilePic:Iterator[ProfilePicDefault] = {
        rootVertex.pipe.out(rootVertexLabel)
            .iterator().flatMap(_.toCC[ProfilePicDefault])
    }

    /**
     * Mendapatkan semua list photo profile default
     *
     * @param offset -- photo profile offset
     * @param limit -- photo profile limit
     * @return
     */
    def getDefaultProfilePicList(offset:Int, limit:Int):Iterator[ProfilePicDefault] = {
        rootVertex.pipe.out(rootVertexLabel).range(offset, (offset + limit) - 1)
            .iterator().flatMap(_.toCC[ProfilePicDefault])
    }

    /**
     * cek apakah avatar dengan nama tersebut ada
     * @param name nama avatar
     * @return
     */
    def existsByName(name:String):Boolean  = {
        getByName(name).isDefined
    }


}


/**
 * Untuk foto profile yang di upload user
 * @param url
 */
case class ProfilePic (url:String)
    extends ProfilePicBase(url) with HasCreator {
    @Persistent var active:Boolean = false

    /**
     * override this for custom label.
     * @return
     */
    override protected def creatorLabel = HAS_PROFILE_PICTURE

    /**
     * Set bahwa profile pic ini yang dipakai saat ini
     * @param state
     */
    def setActive(state:Boolean)  {
        transact {
            this.active = state
            this.save()
        }
    }
}

object ProfilePic extends DaoBase[GraphType, ProfilePic] with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._


    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.ProfilePicRootVertex"

    def create(url:String, owner:User):ProfilePic = {

        val profilePic = createNoTx(url, owner)

        db.commit()

        profilePic
    }

    def createNoTx(url:String, owner:User):ProfilePic = {
        val newPic = ProfilePic(url).save().toCC[ProfilePic].getOrElse {
            throw new DigakuException("Cannot create ProfilePic, persistent error.")
        }

        newPic.originalUrl = url

        addToRoot(newPic.getVertex)

        val now = Digaku.engine.dateUtils.nowMilis

        owner.reload()
        val edHasPic = owner --> HAS_PROFILE_PICTURE --> newPic <()
        edHasPic.setProperty("timeOrder", now)

        ProfilePicHistory.create(newPic, now)

        newPic
    }

}

case class ProfilePicHistory(var usageTime: Long) extends BaseModel[IDType] with DbAccess {

    lazy val picture = this.getVertex.pipe.in(PROFILE_PIC_HISTORY).headOption.flatMap(_.toCC[ProfilePic]).getOrElse {
        throw NotExistsException("Profile Picture not exist")
    }
}

/**
 * Model untuk historical profile pictures
 * Menyimpan tanggal dari pemakaian sebuah profile picture
 * @return
 */
object ProfilePicHistory extends DaoBase[GraphType, ProfilePicHistory] with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.ProfilePicHistoryRootVertex"

    def create(pic:ProfilePic, usageTime:Long):ProfilePicHistory = {

//        var profPicHistory:ProfilePicHistory = null

//        transact {

        val vertex = ProfilePicHistory(usageTime).save()

        val profPicHistory = vertex.toCC[ProfilePicHistory].getOrElse {
            throw new DigakuException("Cannot create profile picture history")
        }

        addToRoot(profPicHistory.getVertex)

        pic.reload()
        pic --> PROFILE_PIC_HISTORY --> vertex

        db.commit()

//        }
        profPicHistory
    }

}


