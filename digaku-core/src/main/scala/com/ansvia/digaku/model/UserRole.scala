/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

object UserRole {
    val SUPER_ADMIN = 0
    val ADMIN = 1
    val USER = 2

    val roles = Seq(SUPER_ADMIN, ADMIN, USER)

    def toName(role:Int):String = role match {
        case SUPER_ADMIN => "Super Administrator"
        case ADMIN => "Admin"
        case USER => "User"
    }
}

object ChannelAccess {
    val NONE = 0
    val DELETE_POST = 1
    val KICK_USER = 2
    val PICK_ARTICLE = 4
    val CHANGE_BANNER = 8
    val ALL = DELETE_POST | KICK_USER | PICK_ARTICLE | CHANGE_BANNER
}

object EmailRoleNotif {
    val MENTION_POST = "mention-post"
    val MENTION_RESPONSE = "mention-response"
    val ADDED_COLLABORATION = "added-collaboration"
    val INVITED_STAFF_CHANNEL = "invited-staff-group"
    val NEW_SUPPORTER = "new-supporter"
    val NEW_MESSAGE = "new-message"
    val SHOUT = "shouted-post"
    val ANNOUNCEMENT = "announcement"
    val ALL = "all"
}

object PrivacyRole {
    val SUPPORT_APPROVAL = "support-approval"
}

