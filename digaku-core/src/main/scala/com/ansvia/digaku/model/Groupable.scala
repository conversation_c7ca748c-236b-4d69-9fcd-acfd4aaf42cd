/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types._

/**
 * Author: fajrhf
 *
 * Group dapat dimasukkan ke dalam Group seperti layaknya memasukkan group ke dalam Promoted
 * Saat ini hanya type Object Group yang dapat dimasukkan ke dalam Group, sudah disiapkan apabila akan memasukkan Post ke dalam Group
 * Saat ini tidak diberikan custom image untuk group-group yang ada dalam Group, namun sudah disiapkan apabila kedepannya akan menggunakan custom image pada group Group
 *
 * Group group ini pada Soccertalk digunakan untuk menambahkan News Group di sistem Soccertalk.
 * Kenapa tidak dibikin news saja instead of group?
 * Agar mendukung penambahan tipe-tipe serupa, seperti Regional group di BCA MC2
 *
 */
trait Groupable {
    var grouped = false

    def getId:IDType
}
