/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types._
import com.ansvia.digaku.database.GraphCompat._
import com.ansvia.digaku.exc.PermissionDeniedException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.graph.BlueprintsWrapper.IdDbObject
import com.tinkerpop.blueprints.Vertex
import com.tinkerpop.gremlin.java.GremlinPipeline

import scala.collection.JavaConversions._
import com.ansvia.digaku.helpers.GremlinHelpers._

/**
 * Author: nadir, robin
 *
 */

trait Reportable[idT] extends IdDbObject[idT] with DbAccess with HasCreator {

    import com.ansvia.graph.BlueprintsWrapper._

    /**
     * untuk menambahkan report di content
     * @param reporter see [[com.ansvia.digaku.model.User]]
     * @param message String message report
     * @return
     */
    def addReport(reporter:User, message:String): AbuseReport = {

        if (userHasReporting(reporter))
            throw PermissionDeniedException("Cannot report twice.")

        this match {
            case hc:HasCreator =>
                if (hc.creator == reporter)
                    throw PermissionDeniedException("Creator can't report.")

            case _ =>
        }

        val report = AbuseReport.create(reporter, message)
        this.addReport(report)
    }

    def userHasReporting(reporter:User):Boolean = {
//        this.reload()
//        tx { t =>
            gremlin(getVertex).in(REPORT_OF)
                .in(REPORT_WITH)
                .has("id", reporter.getId).count() > 0
//        }
    }

    /**
     * untuk menambahkan report di content dengan parameter abuseReport
     * @param abuseReport see [[com.ansvia.digaku.model.AbuseReport]]
     * @return
     */
    def addReport(abuseReport:AbuseReport):AbuseReport = {
        val contentV = db.getVertex(getId)
//        transact {
            abuseReport --> REPORT_OF --> contentV
            val originId =
                this match {
                    case content:HasOrigin[IDType] =>
                        content.origin.getId
                }

            abuseReport.originId = originId.asInstanceOf[Long]
            abuseReport.save()

//        }
        db.commit()

        abuseReport

    }

    /**
     * get reports untuk content
     * @param offset
     * @param limit
     * @return
     */
    def getReports(offset:Int, limit:Int):Seq[AbuseReport] = {
        this.getVertex.pipe.in(REPORT_OF).range(offset, (offset + limit) - 1).iterator().flatMap(_.toCC[AbuseReport]).toSeq
    }

    /**
     * get report counts dari content
     * @return
     */
    def getReportCounts:Int = {
        this.getVertex.pipe.in(REPORT_OF).count().toInt
    }

    /**
     * delete report abuse dari content
     * @param abuseReport see [[com.ansvia.digaku.model.AbuseReport]]
     */
    def deleteReport(abuseReport:AbuseReport) {
        transact {
            this.getVertex.pipe.in(REPORT_OF).has("id", abuseReport.getId)
                .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
                .iterator().foreach { v =>
                    v.pipe.bothE().iterator().foreach(db.removeEdge)
                    db.removeVertex(v)
                }
        }
    }

    /**
     * delete semua report abuse yang ada di content
     */
    def deleteReports() {
        transact {
            this.reload().getVertex.pipe.in(REPORT_OF)
                .iterator().foreach { v =>
                    v.pipe.bothE().iterator().foreach(e => db.removeEdge(e))

                    db.removeVertex(v)

                }
        }
    }


}

object Reportable extends DbAccess {
    import com.ansvia.graph.BlueprintsWrapper._

    def getById(id:IDType):Option[Reportable[IDType]] = {
        val v = db.getVertex(id)
        if (v != null){
            v.toCC[Reportable[IDType]]
        }else{
            None
        }
    }
}
