/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.graph.AbstractDbObject
import com.ansvia.graph.annotation.Persistent

trait Closable extends AbstractDbObject {

    @Persistent var closed = false
    @Persistent var closeOpenByUserName = ""
    @Persistent var closeOpenByUserId = 0L
    @Persistent var reasonClosed = ""
    @Persistent var closeOpenTime = 0L

    /**
     * Set this object as closed.
     * Non transactional jalankan di dalam transact/sebelum `db.commit()`,
     * perlu panggil `.save()` setelahnya, contoh:
     *
     *    post.setClosed(true).save()
     *
     * @param close close state true or false (true means close otherwise open).
     * @param closer user who close/open this object.
     * @param reason reason why you closed this object.
     */
    def setClosed(close:<PERSON><PERSON><PERSON>, closer:User, reason:String) = {
        closed = close
        closeOpenByUserName = closer.getName
        closeOpenByUserId = closer.getId
        reasonClosed = reason
        closeOpenTime = Digaku.engine.dateUtils.nowMilis
        this
    }

    /**
     * Check is this object closed.
     * @return
     */
    def isClosed:Boolean = closed

}
