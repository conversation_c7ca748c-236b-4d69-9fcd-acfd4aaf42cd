/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model


object Label {


    /************************************************
     * WARNING: untuk buat label baru jangan sampai
     * konflik dengan nama attribut/property key yang
     * sudah ada!!
     ***********************************************/

    val OWN = "own"
    val JOIN = "join"
    val CREATE = "create"
    val WROTE = "wrote"
    val GIVE_REPUTATION = "give_reputation"

    // group of CREATE, WROTE
    val PUBLISH_CONTENT = "publish_content"

    val ORIGIN = "origin"
    val STAFF_AT = "staff_at"
//    val INVITED_STAFF_AT = "invited_staff_at"
    val RESPONSE_OF = "response_of"
    val RESPONSE_WITH = "response_with"
    val LIKES = "likes"
    val VIEW = "view"
    val SUPPORT = "support"
    val ATTEND = "attend"
    val UPDATE = "update"
    val NOTIFICATION = "notif"
    val CHOICE = "choice"
    val CHOOSE = "choose"
    val BLOCK = "block"
    val CREATE_STATUS = "create_status"

    val INVITE = "invite"
    val POST_MARK = "post-mark"
    val POST_MARK_CATEGORY = "post_mark_category"
    val EMBED = "embed"
    val START_CONVERSATION = "start_conversation"

    /**
     * Digunakan untuk menandai user yang merupakan participant dalam
     * private message atau response dari private message.
     * Referensi : https://mindtalk.atlassian.net/browse/MT-289
     */
    val PARTICIPANT = "participant"

    val REPLY_OF = "reply_of"
    val REPLY_WITH = "reply_with"
    val CAN_EDIT = "can_edit"
    val VERIFICATION = "verification"
    val EMAIL_VERIFICATION = "email_verification"
    val CHANGE_EMAIL_VERIFICATION = "change_email_verification"
    val PHONE_VERIFICATION = "phone_verification"
    val CHANGE_PHONE_VERIFICATION = "change_phone_verification"
    val RESET_PASS_VERIFY = "reset_pass_verify"
    val USER_STATS = "user_stats"
    val EVENT_REMINDER = "event_reminder"
    val EMAIL_DRAFT = "email_draft"
    val CONTENT_LOCALE = "content_locale"
    val POPULAR_VIDEO = "popular_video"
    val HAS_PICTURE = "has_pic"
    val SUMMONED = "summoned"
    val HAS_PROFILE_PICTURE = "has_profile_pic"
    val HAS_PROFILE_PICTURE_DEFAULT = "has_profile_pic_def"
    val POST_VIA = "post_via"
    val POST_AS = "post_as"
    val SHOUT = "shout"
    val RETALK = "retalk"
    val RETALK_REF = "retalk_ref"
    val REPORT_WITH = "report_with"
    val REPORT_OF = "report_of"
    val GALLERY = "gallery"
    val ALBUM = "album"
    val CONNECT = "connect"
    val CONNECT_FB = "connect_fb"
    val CONNECT_TW = "connect_tw"
    val CONNECT_APPS = "connect_apps"
    val FRIEND = "friend"
    val FOLLOW = "follow"
    val HAS_TROPHY = "has_trophy"
    val FEEDBACK = "feedback"
    val PROFILE_PIC_HISTORY = "profile_pic_history"
    val CATEGORY_INTEREST = "category_interest"
    val CREATE_PROMOTED = "create_promoted"
    val HAS_PROMOTED = "has_promoted"
    val USE_APP = "use_app"
    val CREATE_GROUP = "create_group"
    val HAS_GROUP = "has_group"
    val HAS_CATEGORY = "has_category"
    val MONITOR = "monitor"
    val LEADERBOARD = "leaderboard"

    val HAS_LEADERBOARD = "has_leaderboard"
    val USER_LEADERBOARD = "user_leaderboard"

    /**
     * label default yang digunakan dao
     * untuk menghubungkan vertex dengan root vertex-nya.
     */
    val DAO_LIST = "dao_list"
    val POPULAR = "popular"  // commons popular

    val STREAM = "stream"

    // User Stream
    val USER_STREAM = "user_stream"

    // stream untuk private message
    val PM_STREAM = "pm_stream"
    val REVISION = "_revision_"
    val CLICK_AD = "click_ad"
    val AD_CLICKER = "ad_clicker"

    val CREATE_AD = "create_ad"
    val VISIT_URL = "visit_url"

    val ENDORSE = "endorse"
    val ENDORSEMENT = "endorsement"

    val IGNORE_RECOMMENDATION = "ignore_recommendation"
    val PYMK = "pymk" // people you may know

    /**
     * Digunakan untuk me-link-kan user ke user lainnya
     * sebagai relasi saling mengenal satu sama lain.
     */
    val KNOWS = "knows"

    val BOOKMARK_CHANNEL = "bookmark_channel"

    val MONTHLY_ARCHIVE = "monthly_archive"
    val YEARLY_ARCHIVE = "yearly_archive"

    val COLLECTION_VERTEX = "cv"
    val UNWATCH = "unwatch"

    /**
     * edge label untuk menandai
     * bahwa suatu forum adalah memiliki sub forum lagi di bawahnya.
     */
    val SUB_FORUM = "sub_forum"

    /**
     * Edge label untuk me-link-an user sebagai member di user group
     */
    val USER_GROUP_MEMBER = "user_group_member"

    /**
     * Edge label untuk manambahkan faq ke dalam topic
     */
    val HAS_FAQ = "has_faq"

    /************************************************
     * NOTE: kalo nambahin label baru tambahkan juga
     * di list bawah ini (untuk keperluan indexing).
     ***********************************************/

    val indexable = List(
        OWN, JOIN, CREATE, WROTE, ORIGIN, STAFF_AT, RESPONSE_OF,
        RESPONSE_WITH, LIKES, VIEW, SUPPORT, ATTEND, UPDATE, NOTIFICATION,
        CHOICE, INVITE, POST_MARK, POST_MARK_CATEGORY, EMBED, START_CONVERSATION,
        PARTICIPANT, REPLY_OF, REPLY_WITH, CAN_EDIT, POPULAR,
        VERIFICATION, EVENT_REMINDER, EMAIL_DRAFT, BLOCK, CONTENT_LOCALE,
        DAO_LIST, POPULAR_VIDEO, HAS_PICTURE, POST_VIA, STREAM, RETALK, RETALK_REF, SHOUT,
        REVISION, REPORT_OF, REPORT_WITH, CLICK_AD, CREATE_AD, AD_CLICKER,
        VISIT_URL, ENDORSE, ENDORSEMENT, GALLERY, ALBUM, EMAIL_VERIFICATION,
        PHONE_VERIFICATION, RESET_PASS_VERIFY, CONNECT_FB, CONNECT_TW, FRIEND, FOLLOW,
        CONNECT_APPS, BOOKMARK_CHANNEL, HAS_TROPHY, SUMMONED, CREATE_STATUS,
        PROFILE_PIC_HISTORY, CATEGORY_INTEREST, CREATE_PROMOTED, HAS_PROMOTED,
        MONTHLY_ARCHIVE, USE_APP, COLLECTION_VERTEX, UNWATCH, CREATE_GROUP, HAS_GROUP,
        GIVE_REPUTATION, KNOWS, SUB_FORUM, CHOOSE, USER_GROUP_MEMBER, HAS_FAQ,
        POST_AS, HAS_CATEGORY, LEADERBOARD, HAS_LEADERBOARD, USER_LEADERBOARD
    )

}

/************************************************
  * VertexLabels ini digunakan untuk melabeli
  * suatu vertex agar terklasifikasikan dengan
  * baik sehingga lebih mudah dalam indexing
  * dan lebih optimal dalam querying.
  *
  * Fitur vertex label hanya ada di Titan 0.5
  * keatas, tentang vertex label baca ini:
  * http://s3.thinkaurelius.com/docs/titan/0.5.0/schema.html
 ***********************************************/
object VertexLabels {
    val USER = "user"
    val FORUM = "forum"
    val TROPHY = "trophy"
    val APP = "app"
    val THIRDPARTY_CONNECT = "thirdparty_connect"
    val CHANNEL_GALLERY = "channel_gallery"
    // untuk writable content
    val CONTENT = "content"
    val CATEGORY_INTEREST = "category_interest"
    val THIRDPARTY_ACCOUNT = "thirdparty_account"
    val POSTMARK = "postmark"
    val EMBEDDED_OBJECT = "embedded_object"
    val PM = "pm"
    val NOTIFICATION = "notification"
    val STATS = "stats"
    val GROUP = "group"
    val USER_GROUP = "user_group"
    val USER_LEADERBOARD = "user_leaderboard"
}

