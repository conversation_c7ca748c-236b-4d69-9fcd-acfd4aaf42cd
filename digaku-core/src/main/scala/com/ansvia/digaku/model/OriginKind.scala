/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

/**
 * Author: robin
 * 
 */
object OriginKind {
    val NONE = 0
    val SUB_FORUM = 1
    val USER = 2


    def strToInt(kindStr:String) = {
        kindStr match {
            case "group" => SUB_FORUM
            case "user" => USER
        }
    }

    def intToStr(kindInt:Int) = {
        kindInt match {
            case SUB_FORUM => "group"
            case USER => "user"
        }
    }


}
