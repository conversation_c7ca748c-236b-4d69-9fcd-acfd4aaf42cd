/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.event.impl.{CreateAppEvent, DeleteAppEvent}
import com.ansvia.digaku.exc.{AlreadyExistsException, DigakuException, InvalidParameterException}
import com.ansvia.digaku.validator.AppValidator
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.graph.IdGraphTitanDbWrapper._
import com.ansvia.graph.annotation.Persistent
import com.ansvia.util.idgen.RandomStringGenerator
import org.apache.commons.codec.binary.Base64
import org.apache.commons.codec.digest.DigestUtils

import scala.collection.JavaConversions._

/**
 * Author: robin
 *
 * Class untuk aplikasi (app) di Digaku 2.
 */

case class App(name:String,
               desc:String,
               version:String,
               redirectUri:String)
    extends BaseModel[IDType] with HasOwner with Blockable {

    import com.ansvia.digaku.model.App._


    /**
     * mendapatkan client id dari app ini.
     * @return
     */
    def clientId:IDType = getVertex.getProperty[IDType]("app.client-id")

    /**
     * mendapatkan client secret dari app ini.
     * @return
     */
    def clientSecret:String = getVertex.getOrElse("app.client-secret", "-")

    def devAccessToken = {
        getVertex.getOrElse("app.dev-access-token", "-")
    }

    /**
     * menandakan apakah aplikasi ini digunakan oleh internal sistem?
     */
    @Persistent var internal:Boolean = false

    // menandakan apakah aplikasi ini verified atau tidak.
    @Persistent var verified:Boolean = false

    // menandakan apakah aplikasi ini di-pick oleh sistem
    @Persistent var picked:Boolean = false

    /**
     * menandakan apakah aplikasi ini mendukung XAuth?
     * secara default tidak.
     */
    @Persistent var supportXAuth:Boolean = false

    // limitation / day
    @Persistent var writePostLimit:Int = 2000
    @Persistent var searchLimit:Int = 2000
    @Persistent var allDispatchLimit:Int = 8000

    @Persistent var supportCanvas:Boolean = false
    @Persistent var canvasUrl:String = ""

    /**
     * gambar dengan ukuran 16x16 pixel (icon)
     */
    @Persistent var pictureSmall = ""

    /**
     * gambar dengan ukuran 50x50 pixel.
     */
    @Persistent var pictureMedium = ""

    /**
     * gambar dengan ukuran 64x64 pixel.
     */
    @Persistent var pictureLarge = ""

    @Persistent var category = ""


    /**
     * Reset secret key apps
     * @return
     */
    def resetSecretKey() = {
        val cs = generateRandomKey()
        getVertex.setProperty("app.client-secret", cs)

        db.commit()

        cs
    }

    /**
     * Reset secret key apps
     * @return
     */
    def resetDevAccessToken() = {
        val devAt = generateRandomKey("dat")
//        transact {
            getVertex.setProperty("app.dev-access-token", devAt)
//        }
        db.commit()
        devAt
    }

    /**
     * untuk me-revoke / cabut akses token.
     * @param user
     */
    def revokeAccessToken(user:User){

    }


    /**
     * List all available access tokens
     */
    def getAccessTokens(offset:Int, limit:Int) = {
        0
    }

}


object App extends RandomStringGenerator with DaoBase[GraphType, App] {

    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.AppRootVertex"

    /**
     * generate random secret key.
     * @return
     */
    def generateRandomKey(prefix:String="") = {
        val hash = DigestUtils.sha512Hex(nextId() + System.currentTimeMillis().toString + nextId())
        prefix + (Base64.encodeBase64String(hash.getBytes))
            .replaceAll("\\W+","").substring(10, 10 + 32)
    }




    /**
     * Mengembalikan error jika apps name tidak valid
     * error: [[com.ansvia.digaku.exc.InvalidParameterException]]
     * @param name
     */
    def validateName(name:String){

        if (name.trim.length < 2)
            throw InvalidParameterException("Min name length 1 characters")

        if (name.trim.length > 100)
            throw InvalidParameterException("Max name length 100 characters")

        if (!AppValidator.validName(name))
            throw InvalidParameterException("Invalid App name `" + name + "`, should be started with alphabet and " +
                "accept only or combination of alphanumeric, dot, underscore, and dash")
    }


    /**
     * create new Digaku 2 App.
     * @param name name apps
     * @param desc desc apps
     * @param version version apps
     * @param redirectUri redirect URI apps
     * @param owner user see [[com.ansvia.digaku.model.User]]
     * @param internal boolean internal
     * @return
     */
    def create(name:String, desc:String, version:String,
               redirectUri:String, owner:User, internal:Boolean=false) = {

        // check parameter
        validateName(name)

        if (desc.trim.length > 1000)
            throw InvalidParameterException("Max description length 1000 characters")

        if (version.trim.length == 0)
            throw InvalidParameterException("No version, please set version, eg: 0.0.1")

        // check apakah dah ada?
        if (isExists(name)){
            throw AlreadyExistsException("Application with name " + name + " already exists, pick another name.")
        }

//        var rv:App = null

//        transact {
            val v = App(name.trim, desc.trim, version, redirectUri).saveWithLabel(VertexLabels.APP)

            v.setProperty("app.lower-name", name.toLowerCase)
            v.setProperty("app.client-id", v.getId)
            v.setProperty("app.client-secret", generateRandomKey())
            v.setProperty("app.dev-access-token", generateRandomKey("dat"))
            v.setProperty("internal", internal)

            addToRoot(v)

            val newApp = v.toCC[App].getOrElse {
                throw new DigakuException("Cannot create App, persistent error?")
            }

            owner.reload()
            newApp.reload()
            newApp.setOwner(owner)

            /**
             * if owner not apps developer
             * make him developer.
             */
            if (!owner.appDeveloper){
                owner.appDeveloper = true
                owner.save()
            }

            newApp.internal = internal

        db.commit()
        
//            newApp
//        }

        if (newApp != null){
            Digaku.engine.eventStream emit CreateAppEvent(newApp)
        }

        newApp
    }

    /**
     * get apps by name
     * @param name name apps
     * @return
     */
    def getByName(name:String) = {
//        db.getVertices("app.lower-name", name.toLowerCase)
//            .headOption.flatMap(_.toCC[App])
        
        db.query().has("label", VertexLabels.APP)
            .has("app.lower-name", name.toLowerCase)
            .vertices()
            .headOption.flatMap(_.toCC[App])
    }

    /**
     * Get app by dev access token.
     * @param devAccessToken
     * @return
     */
    def getByDevAccessToken(devAccessToken:String) = {
//        db.getVertices("app.dev-access-token", devAccessToken)
//            .headOption.flatMap(_.toCC[App])

        db.query().has("label", VertexLabels.APP).has("app.dev-access-token", devAccessToken).vertices()
            .headOption.flatMap(_.toCC[App])
    }



    /**
     * Untuk memeriksa apakah Apps exists
     * mengembalikan true jika apps sudah ada, dan false untuk sebaliknya.
     * @param name nama yang akan diperiksa.
     * @return
     */
    def isExists(name:String) = rootVertex.pipe.out(rootVertexLabel)
        .has("app.lower-name", name.toLowerCase).count() > 0


    override def delete(app: App)(implicit m:Manifest[App]){
        Digaku.engine.eventStream emit DeleteAppEvent(app)
        super.delete(app)
    }
}

