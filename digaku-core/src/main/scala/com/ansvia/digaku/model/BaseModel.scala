/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.util.Date

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.exc.NotSupportedException
import com.ansvia.graph.BlueprintsWrapper.IdDbObject
import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.util.io.graphson.{GraphSONMode, GraphSONUtility}
import com.tinkerpop.blueprints.util.wrappers.id.IdVertex
import com.tinkerpop.blueprints.{Graph, Vertex}

trait BaseModel[idT] extends Slf4jLogger with IdDbObject[idT] with Serializable {

    @Persistent var creationTime = 0L

    def creationDate = new Date(creationTime)

    override def hashCode = getId.toString.hashCode

    override def delete()(implicit db: Graph){
        throw NotSupportedException("Cannot delete model from here, use Dao object delete instead")
    }

    /**
     * Sama seperti `getVertex` bedanya ini
     * akan mendapatkan raw vertex-nya/base vertex-nya apabila
     * element vertex-nya di-wrap menggunakan IdVertex
     * @return
     */
    def getRawVertex = {
        getVertex match {
            case v:IdVertex => v.getBaseVertex
            case v => v
        }
    }

    /**
     * Sama seperti `getId` bedanya ini
     * akan mendapatkan raw id-nya bukan id dari IdGraph-nya.
     * @return
     */
    def getRawId = getRawVertex.getId

    /**
     * Untuk merepresentasikan model vertex ini dalam bentuk JSON string.
     * Biasanya digunakan untuk debugging.
     * @return
     */
    def toJsonString = {
        val json = GraphSONUtility.jsonFromElement(getVertex, null, GraphSONMode.COMPACT)
        json.toString(2)
    }

    /**
     * Menampilkan representasi vertex ini dalam bentuk json ke stdout.
     */
    def print(){
        // scalastyle:off regex.line
        println(toJsonString)
        // scalastyle:on regex.line
    }


    override def __save__(vertex: Vertex){
        if (creationTime == 0L){
            creationTime = Digaku.engine.dateUtils.nowMilis
            vertex.setProperty("creationTime", creationTime)
        }
        super.__save__(vertex)
    }
}




