/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types.IDType
import com.ansvia.graph.AbstractDbObject

/**
 * Author: robin
 *
 */
trait GroupableObject extends AbstractDbObject {
    def getId:IDType
}



