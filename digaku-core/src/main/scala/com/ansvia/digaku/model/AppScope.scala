/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.exc.NotExistsException

/**
 * Author: fajrhf
 *
 */
object AppScope {
    val DEFAULT = APIScope("default", "Access Basic information")
    val ALL = APIScope("all", "Access all user resource")
    val WRITE = APIScope("write", "Can write a content includes text, pic, and event on your stream and group")
    val READ = APIScope("read", "Can read posts from your stream")
    val PM = APIScope("whisper", "Can read and write whispers")
    val EMAIL = APIScope("email", "Get your email address")
    val BIRTH_DATE = APIScope("birth_date", "Get your birth date information")

    val AVAILABLE_SCOPES = List(WRITE, READ, PM, EMAIL, BIRTH_DATE)

    val SCOPES = List(DEFAULT, ALL) ++ AVAILABLE_SCOPES

    def isValid(scope:String) =
        SCOPES.contains(scope)

    def getByName(name:String) = {
        SCOPES.find(sc => sc.ScopeName == name).getOrElse {
            throw NotExistsException("Scope not exists with name " + name)
        }
    }

}

case class APIScope(ScopeName:String, ScopeInfo:String) {
    private var _mandatory = false

    def this(name:String, info:String, mandatory:Boolean){
        this(name, info)
        this._mandatory = mandatory
    }

    def mandatory(state:Boolean) = {
        val dupScope = APIScope(ScopeName, ScopeInfo)
        dupScope._mandatory = true

        dupScope
    }

    def isMandatory = this._mandatory
}