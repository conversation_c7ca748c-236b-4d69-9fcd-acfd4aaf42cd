/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.util.concurrent.{Callable, TimeUnit}

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.exc.AlreadyExistsException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.graph.AbstractDbObject
import com.google.common.cache.CacheBuilder

/**
 * Author: robin
 * 
 */

trait AbstractHasCreator {
    val creator:User
    val creatorOption:Option[User]
}

trait HasCreator extends AbstractDbObject
    with <PERSON>bstract<PERSON><PERSON><PERSON><PERSON> with Slf4j<PERSON>ogger with DbAccess {

    import com.ansvia.digaku.model.Label._

    lazy val creator:User = {
        creatorOption.get
    }

    lazy val creatorOption:Option[User] = {
//        val id = this.getVertex.getId
//        val v = db.getVertex(id)
//        if (v == null)
//            throw new Exception("Cannot get creator for object " + this +
//                " with id " + id + ". Got null when getting current vertex.")
//
//        v.pipe.in(creatorLabel).headOption
//            .map(_.toCC[User]).getOrElse(None)

        HasCreator.getCreator(this, creatorLabel)
    }

    /**
     * override this for custom label.
     * @return
     */
    protected def creatorLabel = CREATE
}

trait HasOwner extends AbstractDbObject with Slf4jLogger with DbAccess {

    import com.ansvia.digaku.model.Label._
    import com.ansvia.graph.BlueprintsWrapper._

    import scala.collection.JavaConversions._

    lazy val owner:User = {
        this.getVertex.pipe.in(ownerLabel).headOption.flatMap(_.toCC[User]).get
    }

    lazy val ownerOption:Option[User] = {
        this.getVertex.pipe.in(ownerLabel).headOption
            .flatMap(_.toCC[User])
    }

    def hasOwner = this.getVertex.pipe.in(ownerLabel).headOption.isDefined

    /**
     * override this for custom label.
     * @return
     */
    protected def ownerLabel = OWN

    /**
     * non transactional set owner,
     * please place this inside of transact.
     * @param owner of this object.
     * @return
     */
    def setOwner(owner:User) = {
        if (hasOwner)
            throw AlreadyExistsException("Already has owner")

        val ownerV = db.getVertex(owner.getId)
        val meV = db.getVertex(this.getVertex.getId)

        ownerV --> ownerLabel --> meV
    }
}

object HasCreator extends DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._
    import scala.collection.JavaConversions._

    lazy val creatorCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build[String,Option[User]]()

    def getCreator(hc:HasCreator, creatorLabel:String) = {
        creatorCache.get(hc.getVertex.getId.toString, new Callable[Option[User]]{
            def call(): Option[User] = {
                val id = hc.getVertex.getId
                val v = db.getVertex(id)

                if (v == null)
                    throw new Exception("Cannot get creator for object " + this +
                        " with id " + id + ". Got null when getting current vertex.")

                v.pipe.in(creatorLabel).headOption
                    .map(_.toCC[User]).getOrElse(None)
            }
        })
    }

}

