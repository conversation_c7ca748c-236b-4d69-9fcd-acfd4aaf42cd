/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

///*
// * Copyright (c) 2013. Ansvia Inc.
// * Author: robin
// * Created: 1/3/13 6:46 PM
// */
//
//package com.ansvia.digaku.model
//
//import com.tinkerpop.blueprints.{Vertex, Graph}
//import com.ansvia.digaku.Global
//import com.ansvia.digaku.model.Label._
//import com.ansvia.graph.BlueprintsWrapper.DbObject
//import com.ansvia.digaku.exc.AlreadyExistsException
//import com.ansvia.digaku.dao.{AppDao}
//import com.ansvia.digaku.helpers.DbAccess
//
//
//abstract class BaseApp[idT](name: String,
//                            desc: String,
//                            devToken: String,
//                            secretCode:String,
//                            canvasUrl:String,
//                            sandboxMode:Boolean,
//                            integrates:Boolean)
//    extends Lockable with BaseModel[idT] with Deletable  with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//    import com.ansvia.digaku.database.GraphCompat.transact
//
////    implicit def db:Graph = Global.database.getRaw
////
////    protected var id:idT
////
////    def setId(id:idT) {
////        this.id = id
////    }
//
//
//    lazy val owner: User = {
//
//        val oV = getVertex.pipe.inFirst(OWN)
//        if (oV.isDefined) {
//            oV.get.toCC[User].getOrElse(null)
//        } else {
//            null
//        }
//
//    }
//
//    def setOwner(user: User) {
//        if(getVertex.pipe.inFirst("own").isDefined)
//            throw AlreadyExistsException("App %s already has owner".format(this))
//
//        transact {
//            user --> OWN --> this
//        }
//    }
//
//    def getOwner:User = this.owner
//
//    override def __load__(vertex: Vertex) {
//        super.__load__(vertex)
//
////        id = vertex.getId.asInstanceOf[idT]
//
//    }
//
//}
//
//case class App(name: String,
//               desc: String,
//               devToken: String,
//               secretCode:String,
//               canvasUrl:String,
//               sandboxMode:Boolean,
//               integrates:Boolean) extends BaseApp[Long](name, desc, devToken, secretCode, canvasUrl, sandboxMode, integrates){
//
////    protected var id:Long = 0L
//}
//
//object App extends AppDao[Long]
//
