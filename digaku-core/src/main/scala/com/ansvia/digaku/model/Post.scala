/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.util.Date
import java.util.concurrent.{Callable, TimeUnit}

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.PostDao
import com.ansvia.digaku.persistence.{MapDb, CounterProviderOperator}

//import com.ansvia.digaku.dao.PostDao
import com.ansvia.digaku.event.impl.UserCanEditPostEvent
import com.ansvia.digaku.exc.PermissionDeniedException
import com.ansvia.digaku.utils.{SortDirection, Comparator, DateUtils}
import com.ansvia.graph.annotation.Persistent
import com.google.common.cache.CacheBuilder
import com.tinkerpop.blueprints.Vertex
import org.ocpsoft.prettytime.PrettyTime
import com.ansvia.digaku.database.GraphCompat._
import com.ansvia.digaku.helpers.TypeHelpers._
import scala.util.hashing.MurmurHash3

/**
 * Abstrak class untuk diimplementasi oleh Post.
 *
 * @param content main post content.
 */
abstract class PostBase(content:String) extends PublishableContent
        with Responable with Likable with Stickable with ViaAppInfo
        with Tagable with Counter with ContentVersioning with PostAsUserGroupable {

    import com.ansvia.digaku.model.Label._
    import com.ansvia.graph.BlueprintsWrapper._
    import scala.collection.JavaConversions._


    private lazy val bestResponseSeqStore = Digaku.engine.seqStoreProvider.build("best-respone-" + this.getId,
        ()=> Digaku.engine.idFactory.createId().asInstanceOf[Long])

    val monitoristStr = "monitorist"
    val childResponseStr = "child-response"

    @Persistent var containsPic: Boolean = false
    @Persistent var containsLink: Boolean = false

    /**
     * Flag untuk menandai apakah post ini mengandung video
     * maupun link ke video di dalamnya? Misalnya ada link ke
     * Youtube atau Vimeo.
     *
     * true apabila ada false apabila tidak ada.
     *
     * @see [[com.ansvia.digaku.model.HasEmbeddedObject]]
     */
    @Persistent var containsVideoLink: Boolean = false

    /**
     * oembed link seperti slideshare, scribd, kickstarter
     * see [[com.ansvia.digaku.utils.WebLink]]
     */
    @Persistent var containsOembedLink: Boolean = false
    @Persistent var containsGreeting: Boolean = false
    @Persistent var channelPick: Boolean = false

    /**
     * Digunakan sebagai status apakah artikel ini merupakan
     * pinned artikel atau bukan
     */
    @Persistent var sticky: Boolean = false
    @Persistent var originKind: Int = OriginKind.SUB_FORUM
    @Persistent var streamKind: Int = StreamKind.TEXT

    private lazy val counter = Digaku.engine.counterProvider("post-counter-" + getId)



    lazy val mark:Option[PostMark] = {
        val v = this.getVertex
        v.reload()
        v.pipe.out(POST_MARK).range(0,0).headOption.flatMap(_.toCC[PostMark])
    }

    /**
     * Get counter provider for this model.
     * @return
     */
    def getCounter:CounterProviderOperator = counter

    /**
     * Custom deserializer.
     * @param vertex vertex object.
     */
    override def __load__(vertex: Vertex) {
        super.__load__(vertex)
    }


    lazy val origin:Origin[GraphType] =
        Post.getOrigin(this).orNull

//    def cc = Post.getOrigin(this).orNull

    /**
     * Get pretty printed creation time age
     * relative to current time.
     * ex: 3 hours ago.
     * @return
     */
    def getCreationAge:String = {
        new PrettyTime(new Date()).format(new Date(creationTime))
    }

    /**
     * Set post mark
     * @param mark post mark see [[com.ansvia.digaku.model.PostMark]]
     * @param noTx
     */
    def setMark(mark:PostMark, noTx:Boolean=false){
        def setMarkInternal(){
            reload()

            val v = getVertex

            v.pipe.outE(POST_MARK).remove()

            v --> POST_MARK --> db.getVertex(mark.getVertex.getId)

//            println("v.pipe.inE(STREAM).count(): " + v.pipe.inE(STREAM).count())

            v.pipe.inE(STREAM)
//                .asInstanceOf[GremlinPipeline[Vertex, Edge]]
                .iterator().foreach { ed =>
                ed.setProperty("post.mark", mark.title.toLowerCase)
            }
        }
        if (noTx){
            setMarkInternal()
        }else{
            setMarkInternal()
            db.commit()
        }
    }

    /**
     * Clear post mark
     */
    def clearMark(){
        reload()

        val v = getVertex

        v.pipe.outE(POST_MARK).remove()

        v.pipe.inE(STREAM)
            .asInstanceOf[GremPipeEdge]
            .iterator().map { ed => ed.removeProperty[String]("post.mark") }

        db.commit()
    }

    def lastUpdatedTime = getVertex.getOrElse("lastUpdatedTime", 0L)


    override def __save__(v:Vertex) = {
        v.setProperty("lastUpdatedTime", Digaku.engine.dateUtils.nowMilis)
        if (creationTime == 0L){
            v.setProperty("creationTime", Digaku.engine.dateUtils.nowMilis)
        }else{
            super.__save__(v)

            // masih tetap perlu ini untuk operasi OLAP seperti mendapatkan popular pictures
            vertex.setProperty("likesCount", getLikesCount)
        }
    }

    /**
     * Make user can edit post
     * @param user user see [[com.ansvia.digaku.model.User]]
     */
    def mekeUserCanEdit(user:User){

        if (userCanEdit(user))
            throw PermissionDeniedException("%s has been added to edit articles".format(user.name))

        user.reload().getVertex --> CAN_EDIT --> this.getVertex
        db.commit()

        Digaku.engine.eventStream.emit(UserCanEditPostEvent(creator, user, this.asInstanceOf[Post]))
    }

    /**
     * Get list user can edit post
     * @return
     */
    def userCanEditList:Seq[User] = {
        this.getVertex.pipe.in(Label.CAN_EDIT).toList.flatMap(_.toCC[User])
    }

    /**
     * Remove user yang dapat edit
     * @param user user see [[com.ansvia.digaku.model.User]]
     */
    def removeUserCanEdit(user:User) {

        user.getVertex.pipe.outE(Label.CAN_EDIT).as("ed")
            .inV()
            .has("id", this.getId)
            .back("ed")
            .remove()

        db.commit()
//                .asInstanceOf[GremlinPipeline[Vertex, Edge]]
//                .iterator()
//                .foreach(db.removeEdge)

    }

    /**
     * Mengembalikan true jika user dapat melakukan
     * edit, false jika tidak dapat
     * @param user user see [[com.ansvia.digaku.model.User]]
     * @return
     */
    def userCanEdit(user:User):Boolean = {
        user.reload().getVertex.pipe.out(Label.CAN_EDIT)
            .has("id", this.getId).count() > 0
    }


    override def hash:String = {
        if (content.trim.length == 0)
            getId.toString
        else
            MurmurHash3.stringHash(content.trim).toString
    }

    /**
     * Mendapatkan jumlah monitorist post
     * @return
     */
    def getMonitoristCount():Int = {
        counter.get(monitoristStr).toInt
    }

    /**
     * increment jumlah monitorist
     * @param by
     */
    def incrementMonitoristCount(by: Int = 1): Unit = {
        counter.incrementBy(monitoristStr, by.toLong)
    }

    /**
     * decrement jumlah monitorist
     * @param by
     */
    def decrementMonitoristCount(by: Int = 1): Unit = {
        counter.decrementBy(monitoristStr, by.toLong)
    }

    /**
     * check apakah user sudah monitor
     * @param user
     * @return
     */
    def isMonitorist(user:User): Boolean = {
        user.reload().getVertex.pipe.outE(MONITOR)
            .has("targetId", this.getId).headOption match {
            case Some(x) =>
                true
            case _ =>
                false
        }
    }

    /**
     * Menambahkan monitorist ke post
     * @param user subsriber baru
     * @return
     */
    def userMonitor(user:User) = {
        if ((creator != null && creator.getId != user.getId) && !isMonitorist(user)){

            if (isDeleted)
                throw PermissionDeniedException("Thread has been removed")

            origin match {
                case forum:Forum =>
                    if (forum.privated){
                        val isAdminOrModerator = user.role == UserRole.SUPER_ADMIN || user.role == UserRole.ADMIN || forum.isMember(user)
                        if (!isAdminOrModerator) {
                            throw PermissionDeniedException("Only member can monitor")
                        }
                    }
                case _ =>
            }


            val edge = user.getVertex.addEdge(MONITOR, getVertex)

            edge.setProperty("sourceId", user.getId)
            edge.setProperty("targetId", this.getId)
            edge.setProperty("timeOrder", System.currentTimeMillis())

            incrementMonitoristCount()
            user.incrementMonitorCount()
            db.commit()
        }
        else {
            throw PermissionDeniedException(("%s is creator or already " +
                monitoristStr)
                .format(user.name))
        }
    }

    /**
     * Mengeluarkan user dari daftar monitorist
     * @param user user yang dikeluarkan
     */
    def userUnMonitor(user: User) = {
        if (isMonitorist(user)){

            if (isDeleted)
                throw PermissionDeniedException("Thread has been removed")

            getVertex.pipe.inE(MONITOR).has("sourceId", user.getId).remove()

            decrementMonitoristCount()
            user.decrementMonitorCount()

            db.commit()
        }
        else {
            throw PermissionDeniedException("%s not a monitorist"
                .format(user.name))
        }
    }

    /**
     * get lists of minotorists
     * @param offset from
     * @param limit to
     * @return
     */
    def getMonitorists(offset: Int, limit: Int): Seq[User] = {
        db.getVertex(getId).pipe.in(MONITOR)
            .range(offset, offset + limit - 1)
            .iterator()
            .flatMap(_.toCC[User])
            .toSeq
    }

    /**
      * Mendapatkan jumlah keseluruhan child reply/response post
      * @return
      */
    def getChildResponseCount:Int = {
        counter.get(childResponseStr).toInt
    }

    /**
      * increment jumlah child reply/response
      * @param by
      */
    def incrementChildResponseCount(by: Int = 1): Unit = {
        counter.incrementBy(childResponseStr, by.toLong)
    }

    /**
      * decrement jumlah child reply/response
      * @param by
      */
    def decrementChildResponseCount(by: Int = 1): Unit = {
        counter.decrementBy(childResponseStr, by.toLong)
    }

    /**
      * Mendapatkan semua jumlah baik untuk
      * response level 1 ataupun level 2
      * @return
      */
    def getAllResponseCount: Int = {
        getResponseCount + getChildResponseCount
    }

    /**
      * Menambahkan best response untuk post ini.
      * @param response
      */
    def addBestResponse(response: Response): Unit = {
        if (getBestResponseCount < 3) {
            bestResponseSeqStore.insert(response.getId, Digaku.engine.dateUtils.nowMilis.toString)
        } else {
            throw PermissionDeniedException("Best reply tidak boleh lebih dari 3.")
        }
    }

    /**
      * Mendapatkan jumlah best response
      * @return
      */
    def getBestResponseCount = {
        bestResponseSeqStore.getCount
    }

    /**
      * Menghapus best response pada post ini
      * @param response
      */
    def removeBestResponse(response: Response): Unit ={
        bestResponseSeqStore.delete(response.getId)
    }

    /**
      * Mendapatkan list id best response
      * @return
      */
    def getBestResponseIds:Seq[Long] = {
        bestResponseSeqStore.getStream(None, None, 5).toSeq.sortBy { rv =>
            -rv._2.toLongOr(0L)
        }.map( rv => rv._1.toLong)
    }

    /**
      * Mendapatkan list best response
      * @return
      */
    def getBestResponses: Seq[Response] = {
        getBestResponseIds.flatMap(rv => Response.getById(rv))
    }

    /**
      * Check apakan response sudah dijadikan best response
      * @param response
      * @return
      */
    def isBestResponse(response: Response): Boolean = {
        bestResponseSeqStore.get(response.getId).isDefined
    }

}

abstract class Post(content:String) extends PostBase(content) with WatchableContent {

    /**
     * @see [[com.ansvia.digaku.model.PostKind]]
     */
    @Persistent val kind:Int
    @Persistent var shoutCount:Int = 0


    lazy val kindStr: String = PostKind.kindIntToStr(kind)

    def getContent = if (content != null) {
        content
    } else {
        warn("content is null for post id " + getId)
        ""
    }

    def setLocked(locked:Boolean, reason:String) {
        super.setLocked(locked)
        this.lockedReason = reason
        this.save()
    }



}

/**
 * semua implementasi static Post ini
 * ada di [[com.ansvia.digaku.dao.PostDao]]
 * ini untuk mempermudah akses DAO menggunakan nama modelnya.
 */
object Post extends PostDao[GraphType] with Slf4jLogger {

    import com.ansvia.digaku.model.Label.ORIGIN
    import com.ansvia.graph.BlueprintsWrapper._
    import scala.collection.JavaConversions._

    /**
     * bitwise untuk flag test post contains apa.
     */
    object contains {
        val NONE = 0
        val LINK = 1
        val VIDEO_LINK = 2
        val PIC = 4
        val GREETING = 8

        val ALL = LINK | VIDEO_LINK | PIC | GREETING
    }

    val originCache2 = MapDb.compressed.hashMapCreate("origin_cache")
        .expireAfterAccess(60, TimeUnit.MINUTES)
        .expireAfterWrite(120, TimeUnit.MINUTES)
        .expireMaxSize(5000)
        .make[String, Option[Origin[GraphType]]]()

    def getOrigin(pb:PostBase) = {

//        val origin = originCache2.get(pb.getId.toString)

//        if (origin == null || origin.isEmpty) {
            tx { t =>
                val meV = t.getVertex(pb.getId)
                if (meV != null) {
                    val v = meV.pipe.out(ORIGIN).range(0,1).headOption
                    if (v.isDefined) {
                        try {
                            val rv = v.get.toCC[Forum]
                            if (rv.isDefined) {
                                val orig = rv.asInstanceOf[Option[Origin[GraphType]]]
                                val _orig = Some(orig.get.reload()(t))
    //                            debug(s"got origin: ${_orig}")

                                  //sementara tidak menggunakan cache dulu
    //                            originCache2.put(pb.getId.toString, orig)

                                _orig
                            } else {
                                None
                            }
                        } catch {
                            case e:NullPointerException =>
                                error("no origin for post %s".format(pb))
                                None
                        }
                    } else {
                        error("no origin for post %s".format(pb))
                        None
                    }
                } else {
                    None
                }
            }
//        } else {
//            origin
//        }
    }

}
