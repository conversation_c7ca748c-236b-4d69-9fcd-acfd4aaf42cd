/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types._
import com.ansvia.digaku.exc.UnsupportedException

/**
 * Author: robin
 *
 */
abstract class ContentLocaleBase(locale:String, content:String) extends BaseModel[IDType] {

//    /**
//     * total clean delete operation.
//     * @param db
//     * @return
//     */
//    override def delete()(implicit db: Graph) {
//        transact {
//            getVertex.pipe.bothE().iterator().foreach(db.removeEdge)
//            db.removeVertex(getVertex)
//        }
//    }
}

case class ContentLocale(locale:String, content:String) extends ContentLocaleBase(locale, content)

object Locale {
    val en_US = "en_US"
    val id_ID = "id_ID"
    val ja_JP = "ja_<PERSON>"

    val DEFAULT_LOCALE = id_ID
    val DEFAULT_TIMEZONE = 7

    val supported = Seq(
        en_US, id_ID, ja_JP
        )

    /**
     * list timezone
     */

    val listTimezone = Seq(
        ("(GMT -12:00) Eniwetok, Kwajalein", "-12"),
        ("(GMT -11:00) Midway Island, Samoa", "-11"),
        ("(GMT -10:00) Hawaii", "-10"),
        ("(GMT -9:00) Alaska", "-9"),
        ("(GMT -8:00) Pacific Time (US & Canada)", "-8"),
        ("(GMT -7:00) Mountain Time (US & Canada)", "-7"),
        ("(GMT -6:00) Central Time (US & Canada), Mexico City", "-6"),
        ("(GMT -5:00) Eastern Time (US Canada), Bogota, Lima", "-5"),
        ("(GMT -4:00) Atlantic Time (Canada), La Paz, Santiago", "-4"),
        ("(GMT -3:00) Brazil, Buenos Aires, Georgetown", "-3"),
        ("(GMT -2:00) Mid-Atlantic", "-2"),
        ("(GMT -1:00 hour) Azores, Cape Verde Islands", "-1"),
        ("(GMT) Western Europe Time, London, Lisbon, Casablanca", "0"),
        ("(GMT +1:00 hour) Brussels, Copenhagen, Madrid, Paris", "1"),
        ("(GMT +2:00) Kaliningrad, South Africa", "2"),
        ("(GMT +3:00) Baghdad, Riyadh, Moscow, St. Petersburg", "3"),
        ("(GMT +4:00) Abu Dhabi, Muscat, Baku, Tbilisi", "4"),
        ("(GMT +5:00) Ekaterinburg, Islamabad, Karachi, Tashkent", "5"),
        ("(GMT +6:00) Almaty, Dhaka, Colombo", "6"),
        ("(GMT +7:00) Bangkok, Hanoi, Jakarta", "7"),
        ("(GMT +8:00) Beijing, Perth, Singapore, Hong Kong", "8"),
        ("(GMT +9:00) Tokyo, Seoul, Osaka, Sapporo, Yakutsk", "9"),
        ("(GMT +10:00) Eastern Australia, Guam, Vladivostok", "10"),
        ("(GMT +11:00) Magadan, Solomon Islands, New Caledonia", "11"),
        ("(GMT +12:00) Auckland, Wellington, Fiji, Kamchatka", "12")
    )

    /**
     * Mengembalikan true jika locale disupport,
     * false jika tidak
     * @param locale locale name
     * @return
     */
    def isSupported(locale:String) =
        supported.contains(locale)


    private val titles = Map(
        en_US -> "English (US)",
        id_ID -> "Bahasa",
        ja_JP -> "Japan (日本)"
    )

    /**
     * Get locale title
     * dan mengembalikan error jika locale
     * tidak disupport
     * @param locale locale title
     * @return
     */
    def getTitle(locale:String):String = {
        titles.getOrElse(locale, {
            throw UnsupportedException("Unsupported locale " + locale)
        })
    }

}


