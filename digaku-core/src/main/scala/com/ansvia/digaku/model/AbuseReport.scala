/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.util.Date

import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.utils.RichDate._
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.Vertex
import com.tinkerpop.gremlin.java.GremlinPipeline
import org.ocpsoft.prettytime.PrettyTime

import scala.collection.JavaConversions._

/**
 * Author: nadir
 *
 */
class AbuseReportBase[idT, dbT <: GraphType] (message:String)
    extends BaseModel[idT] with DbAccess {

    // origin id dari suatu content
    @Persistent var originId = 0L

    /**
     * untuk memanggil orang yang me-report
     * @return see [[com.ansvia.digaku.model.User]]
     */
    lazy val reporter:User = {
        val uv = getVertex.pipe.in(REPORT_WITH).headOption
        if(!uv.isDefined)
            throw new DigakuException("Cannot get response creator")
        uv.get.toCC[User].getOrElse(
            throw new DigakuException("Cannot get response creator, invalid database object.")
        )
    }

    /**
     * digunakan untuk memanggil content yang di-report
     * @return
     */
    def getReportedObject:Option[Reportable[idT]] = {
        getVertex.pipe.out(REPORT_OF).headOption.map { v =>
            v.getOrElse("_class_", "") match {
//                case "com.ansvia.digaku.model.SimplePost" =>
//                    v.toCC[SimplePost].get.asInstanceOf[Reportable[idT]]
                case "com.ansvia.digaku.model.Article" =>
                    v.toCC[Article].get.asInstanceOf[Reportable[idT]]
                case "com.ansvia.digaku.model.Event" =>
                    v.toCC[Event].get.asInstanceOf[Reportable[idT]]
                case "com.ansvia.digaku.model.Picture" =>
                    v.toCC[Picture].get.asInstanceOf[Reportable[idT]]
                case "com.ansvia.digaku.model.Response" =>
                    v.toCC[Response].get.asInstanceOf[Reportable[idT]]
            }
        }
    }

    /**
     * Get pretty printed creation time age
     * relative to current time.
     * ex: 3 hours ago.
     * @return
     */
    def getCreationAge:String = {
        new PrettyTime(new Date()).format(new Date(creationTime))
    }

    def getCreationTimeStd:String = {
        new Date(creationTime).toStdFormat
    }


    /**
     * Custom deserializer.
     * @param vertex vertex object.
     */
    override def __load__(vertex: Vertex) {
        super.__load__(vertex)
    }

}

case class AbuseReport(message:String) extends AbuseReportBase[IDType, GraphType] (message)

object AbuseReport extends DaoBase[GraphType, AbuseReport]{

    val ROOT_VERTEX_CLASS:String = "com.ansvia.digaku.model.AbuseReportRootVertex"

    /**
     * create report
     * @param reporter see [[com.ansvia.digaku.model.User]]
     * @param message message report String
     * @return
     */
    def create(reporter:User, message:String): AbuseReport = {
        if (message.trim.length < 3)
            throw InvalidParameterException("Message min 3 characters.")

        val rp = AbuseReport(message)

        reporter.reload()

        val vertex = rp.save()

        addToRoot(vertex)

        val report = vertex.toCC[AbuseReport].getOrElse{
            throw new DigakuException("Cannot create report with message: %s".format(message))
        }
        reporter --> REPORT_WITH --> report

        db.commit()

        report

    }

    /**
     * digunakan untuk mengambil report object (content)
     * @param offset
     * @param limit
     * @return
     */
    def getListReportedObject(offset:Int, limit:Int):Seq[Reportable[IDType]] = {
        rootVertex.pipe.out(DAO_LIST)
            .out(REPORT_OF).dedup()
            .range(offset, offset + limit)
            .iterator().flatMap(_.toCC[Reportable[IDType]]).toSeq
    }

    /**
     * digunakan untuk mengambil report object (content)
     * dan difilter berdasarkan deletednya
     * @param deleted Boolean true or false
     * @param offset
     * @param limit
     * @return
     */
    def getListReportedObject(deleted:Boolean, offset:Int, limit:Int):Seq[Reportable[IDType]] = {
        rootVertex.pipe.out(DAO_LIST)
            .out(REPORT_OF).dedup()
            .has("deleted", deleted)
            .range(offset, offset + limit)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .iterator().flatMap(_.toCC[Reportable[IDType]]).toSeq
    }

    /**
     * digunakan untuk mengambil report object yang ada di suatu origin
     * @param offset Int
     * @param limit int
     * @param origin forum/user
     * @return
     */
    def getListReportedObjectOrigin(offset:Int, limit:Int, origin:Origin[GraphType]) = {
        rootVertex.pipe.out(DAO_LIST)
            .has("originId", origin.getId)
            .out(REPORT_OF).dedup()
            .has("deleted", false)
            .range(offset, offset + limit)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .iterator().flatMap(_.toCC[Reportable[IDType]]).toSeq
    }

}
