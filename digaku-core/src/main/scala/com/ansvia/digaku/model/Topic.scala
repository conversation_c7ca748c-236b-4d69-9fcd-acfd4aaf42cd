package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.event.impl.AddTopicAgentEvent
import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException, PermissionDeniedException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label.HAS_CATEGORY
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.digaku.utils.UserSettings._
import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.Edge
import scala.collection.JavaConversions._
import com.ansvia.graph.gremlin._

/**
 * Author: nadir (<EMAIL>)
 */

case class Topic(var name:String, var description:String) extends BaseModel[IDType] with DbAccess {

    // Status arsip topic, true jika diarsipkan
    @Persistent var archived = false
    // <PERSON>asan diarsipkan
    @Persistent var archivedReason = ""
    // Pesan pengarsipan
    @Persistent var archivedMessage = ""
    // Time terakhir kali diarsipkan
    @Persistent var archivedTime = 0L

    lazy val userAgentSeqStore = Digaku.engine.seqStoreProvider.build("agents-" + getId, ()=> Digaku.engine.idFactory.createId().asInstanceOf[Long])

    lazy val counter = Digaku.engine.counterProvider("topic-counter-" + getId)

    private val faqCountKey = "faqCount"

    /**
     * Get list Faq yang ada pada topic
     * @param offset
     * @param limit
     * @return
     */
    def getFaqs(offset:Int, limit:Int):Iterator[FAQ] = {
        this.getVertex.pipe.outE(Label.HAS_FAQ)
            .order{ (a:Edge, b:Edge) =>
                - a.getOrElse("timeOrder", 0L).compareTo(b.getOrElse("timeOrder", 0L))
            }.range(offset, offset + limit - 1)
            .inV().iterator().flatMap(_.toCC[FAQ])
    }

    /**
      * Digunakan untuk update archived topic
      * @param archived set true jika topic ingin di arsipkan
      * @param reason
      * @param message
      */
    def setArchived(archived:Boolean, reason:String, message:String, noTx: Boolean = false): Unit = {
        if (archived) {
            if (reason.length < 3 || reason.length > 500) {
                throw InvalidParameterException("Reason length is min. 3 and max. 500 characters.")
            }

            if (message.length > 500) {
                throw InvalidParameterException("Message length is max. 500 characters.")
            }

            this.archivedTime = Digaku.engine.dateUtils.nowMilis
        } else {
            this.archivedTime = 0L
        }

        this.archived = archived
        this.archivedReason = reason
        this.archivedMessage = message

        if (!noTx) {
            this.save()
            db.commit()
        }
    }

    /**
     * Add user agent untuk topic
     * @param user
     */
    def addUserAgents(user:User): Unit = {
        userAgentSeqStore.insert(user.getId, user.name)
        user.addTopic(this)

        Digaku.engine.eventStream.emit(AddTopicAgentEvent(this, user))
        Digaku.engine.searchEngine.index(user)
    }

    /**
     * Get user agent pada topic
     * @param fromId
     * @param toId
     * @param limit
     * @return
     */
    def getAgents(fromId:Option[Long], toId:Option[Long], limit:Int):Iterator[User] = {
        userAgentSeqStore.getStream(fromId.map(Long.box), toId.map(Long.box), limit)
            .flatMap(rv => User.getById(rv._1))
    }

    /**
     * Remove agent pada topic
     * @param user
     */
    def removeAgents(user: User): Unit = {
        userAgentSeqStore.delete(user.getId)
        user.deleteTopic(this)

        Digaku.engine.searchEngine.index(user)
    }

    /**
     * Check apakah user sebagai agent pada topic ?
     * @param user
     * @return
     */
    def isAgent(user: User): Boolean = {
        userAgentSeqStore.get(user.getId).isDefined
    }

    /**
     * Get jumlah faq pada topic
     * @return
     */
    def getFaqCount = counter.get(faqCountKey)

    /**
     * Increment faq count pada topic
     * @param count
     * @return
     */
    def incrementFaqCount(count: Int = 1) = {
        counter.incrementBy(faqCountKey, count)
        counter.get(faqCountKey)
    }

    /**
     * decrement faq count pada topic
     * @param count
     * @return
     */
    def decrementFaqCount(count: Int = 1) = {
        counter.decrementBy(faqCountKey, count)
        counter.get(faqCountKey)
    }

    /**
      * Menambah category pada topic ini
      * @param category
      */
    def addCategory(category: TopicCategory): Unit = {
        val ed = this.getVertex --> HAS_CATEGORY --> category.getVertex <()
        ed.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)
        db.commit()
    }

    /**
      * Mendapatkan semua category
      * @return
      */
    def getCategories():Iterator[TopicCategory] = {
        this.getVertex.pipe.out(HAS_CATEGORY).iterator().flatMap(_.toCC[TopicCategory])
    }

    /**
     * Get jumlah agent di sebuah topic
     * @return
     */
    def getAgentCount = userAgentSeqStore.getCount
}

object Topic extends DaoBase[GraphType, Topic]{
    override val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.TopicRootVertex"

    /**
     * Create topic
     * @param name
     * @param desc
     * @return
     */
    def create(name:String, desc:String): Topic = {

        if (name.length < 3 || name.length > 160) {
            throw InvalidParameterException("Topic name length is min. 3 and max. 160 characters.")
        }

        if (desc.length < 3 || desc.length > 500) {
            throw InvalidParameterException("Topic description length is min. 3 and max. 500 characters.")
        }

        val newTopic = Topic(name, desc)

        val v = newTopic.save()

        addToRoot(v)

        db.commit()

        val topic = v.toCC[Topic].getOrElse{
            throw new DigakuException("Cannot create topic : %s".format(newTopic))
        }

        Digaku.engine.searchEngine.indexTopic(topic)

        topic
    }

    /**
     * Validasi untuk delete dan penghapusan agent dari sebuah topic
     * @param topic
     */
    private def deleteTopicInternal(topic: Topic): Unit = {
        if (topic.getFaqCount > 0) {
            throw PermissionDeniedException("Tidak dapat menghapus Question Topic. karena terdapat faq didalamnya")
        }

        topic.userAgentSeqStore.paging(None, None) { case (uid, uname) =>
            User.getById(uid).foreach { user =>
                user.deleteTopic(topic)
            }
            true
        }

        topic.userAgentSeqStore.clear()

        Digaku.engine.searchEngine.deleteTopic(topic)
    }

    /**
     * Delete Topic berdasarkan model
     * @param topic
     * @param m
     * @return
     */
    override def delete(topic: Topic)(implicit m:Manifest[Topic]){
        deleteTopicInternal(topic)

        topic.getCategories().foreach { cat =>
            TopicCategory.delete(cat)
        }

        super.delete(topic)
    }

    /**
     * Delete Topic berdasarkan ID FAQ
     * @param id
     * @param m
     * @return
     */
    override def deleteById(id: IDType)(implicit m:Manifest[Topic]){
        getById(id).foreach { topic =>

            topic.getCategories().foreach { cat =>
                TopicCategory.delete(cat)
            }

            deleteTopicInternal(topic)
        }

        super.deleteById(id)
    }

    /**
      * Mendapatkan list Topic yang diarsipkan.
      * @param offset
      * @param limit
      * @param _db
      * @return
      */
    def getArchivedTopics(offset:Int, limit:Int, _db:TransactionalGraphType = db): Iterator[Topic] ={
        _db.getVertex(rootVertex.getId).pipe.out(rootVertexLabel).has("archived", true)
            .range(offset, (offset + limit) - 1)
            .iterator().flatMap(_.toCC[Topic])
    }
}
