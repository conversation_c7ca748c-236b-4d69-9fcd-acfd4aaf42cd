/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.database.GraphCompat.tx
import com.ansvia.digaku.event.impl.{LikeEvent, UnlikeEvent}
import com.ansvia.digaku.exc._
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.graph.AbstractDbObject
import com.tinkerpop.blueprints.Edge
import com.tinkerpop.gremlin.Tokens.T

/** $regression:$ Fungsional "like" pada object-object yang bisa di-like.
 */
/**
 * Semua model yang diturunkan dari sini bisa di-like.
 * Contoh: post dan response
 */
trait Likable extends AbstractDbObject with DbAccess {

    this: BaseModel[IDType] with Counter =>

    import com.ansvia.digaku.model.Label._
    import com.ansvia.graph.BlueprintsWrapper._

    import scala.collection.JavaConversions._

    private lazy val counter = getCounter


    def getId:IDType

    /**
     * add user like pada post
     * @param user user
     */
    def addLikeWithRate(user: User, rate:Int){

        // throw digakuException ketika method ini digunakan untuk response
        // method ini hany digunakan untuk thread
        if (this.isInstanceOf[Response])
            throw new DigakuException("Like with rate feature not supported for response")

        if (rate > 5)
            throw InvalidParameterException("Like maximum 5 rate.")

        if (user.isLocked) {
            throw PermissionDeniedException("Tidak dapat memberikan Rating. Anda masih berada dalam status Suspended.")
        }

        this match {
            case publishable:PublishableContent =>
                if (publishable.creator.isBlocked(user)){
                    throw PermissionDeniedException("You can't rate this post, you has been blocked by creator")
                }

                if (publishable.isLocked) {
                    throw PermissionDeniedException("Unable to rate, This content has been locked.")
                }

                if (publishable.isDeleted) {
                    throw PermissionDeniedException("Unable to rate, This content has been deleted.")
                }
            case _ =>
        }

        val unlike = rate < 1

        val now = Digaku.engine.dateUtils.nowMilis

        //apakah user sudah pernah me-like?
        if (isLiker(user)) {
            if (unlike) {
                // unlike ketika rate = 0
                removeLike(user)
            } else {
                getLikeEdge(user).foreach { ed =>
                    val prevRate = getLikeRate(user)

                    val calcRate = rate - prevRate
                    if (calcRate != 0) {

                        // set total rate untuk object ini.
                        counter.incrementBy("rating", calcRate)
                        incrementLikeRate(user, calcRate)
                        ed.setProperty("creationTime", now)
                        db.commit()

                        recalculateCreatorExperience(user, calcRate)
                    }

                    if (calcRate > 0)
                        Digaku.engine.eventStream.emit(LikeEvent(user, this.reload().asInstanceOf[Likable]))

                }
            }

        } else {
            if (!unlike) {
                val thisObject = this.reload()
                val ed = user.reload().getYMPart(CollectionVertex.Kind.LIKES) --> LIKES --> thisObject <()
                counter.increment("likes")

                changeRateCountBy(1)

                // set total rate untuk object ini.
                counter.incrementBy("rating", rate)
                incrementLikeRate(user, rate)

                val _likesCount = counter.get("likes")
                thisObject.getVertex.setProperty("likesCount", _likesCount.toInt)

                ed.setProperty("count", _likesCount.toInt)
                ed.setProperty("sourceName", user.lowerName)
                ed.setProperty("creationTime", now)

                thisObject.save()
                db.commit()

                recalculateCreatorExperience(user, rate)

                thisObject match {
                    case hasCreator: HasCreator =>
                        if (hasCreator.creator.getId != user.getId) {
                            UserLeaderboard.incrementPoint(hasCreator.creator, 5)
                        }

                    case _ =>
                }

                Digaku.engine.eventStream.emit(LikeEvent(user, thisObject.asInstanceOf[Likable]))
            }
        }
    }

    /**
     * calkulasikan experience untuk crator thread ketika remove atau add like
     * @param liker user yang like
     * @param rate nilai dari rate(nilai bisa minus (-))
     */
    private def recalculateCreatorExperience(liker:User, rate:Int): Unit = {

        val experience = rate * 5

        this match {
            case hasCreator:HasCreator =>
                val creator = hasCreator.creator
                if (liker != creator) {
                    if (rate > 0)
                        creator.increaseExperience(experience)
                    else
                        creator.decreaseExperience(-experience)
                }
        }
    }

    /**
     * simpan jumlah rate yang di terima
     * oleh creator dari likable ini, tidak peduli berapa pun nilai rate nya
     * tiap rate dihitung sebagai 1, sehingga change rate tidak dihitung
     * salah satu kegunannya adalah untuk badges/trophy
     * @param int jumlah perubahan
     */
    private def changeRateCountBy(int:Int) = {
        this match {
            case hasCreator:HasCreator =>
                if (int > 0) {
                    hasCreator.creator.increaseRaterCount(int)
                } else {
                    hasCreator.creator.decreaseRaterCount(-int)
                }
        }
    }

    /**
     * mendapatkan like edge dari user tertentu
     * @param user
     * @return
     */
    def getLikeEdge(user:User): Option[Edge] = {
        this.reload().getVertex.pipe
            .inE(LIKES).as("ed").outV().in(COLLECTION_VERTEX).has("id", user.getId).back("ed")
            .asInstanceOf[GremPipeEdge]
            .headOption
    }

    /**
     * add user like pada response
     * @param user user
     */
    def addLike(user: User) = {

        // throw digakuException ketika method ini digunakan untuk response
        // method ini hany digunakan untuk thread
        if (!this.isInstanceOf[Response])
            throw new DigakuException("Like ini hanya digunakan untuk response.")

        if (user.isLocked) {
            throw PermissionDeniedException("Tidak dapat memberikan Like. Anda masih berada dalam status Suspended.")
        }

        if (this.isLiker(user))
            throw AlreadyExistsException("You already liked this")

        this match {
            case publishable:PublishableContent =>
                if (publishable.creator.isBlocked(user)){
                    throw PermissionDeniedException("You can't like this post, you has been blocked by creator")
                }

                if (publishable.isLocked) {
                    throw PermissionDeniedException("Unable to like, This content has been locked.")
                }

                if (publishable.isDeleted) {
                    throw PermissionDeniedException("Unable to like, This content has been deleted.")
                }
            case _ =>
        }


        val thisObject = this.reload()

        val ed = user.reload().getYMPart(CollectionVertex.Kind.LIKES) --> LIKES --> thisObject <()

        counter.increment("likes")
        val _likesCount = counter.get("likes")
        thisObject.getVertex.setProperty("likesCount", _likesCount.toInt)


        val now = Digaku.engine.dateUtils.nowMilis

        ed.setProperty("count", _likesCount.toInt)
        ed.setProperty("sourceName", user.lowerName)
        ed.setProperty("creationTime", now)

        thisObject.save()

        db.commit()

        this match {
            case response:Response =>
                if (response.creator != user) {
                    debug(s"adding experience to user ${response.creator} from an added like")
                    response.creator.increaseExperience(1)
                }
            case _ =>
        }

        thisObject match {
            case hasCreator: HasCreator =>
                if (hasCreator.creator.getId != user.getId) {
                    UserLeaderboard.incrementPoint(hasCreator.creator, 3)
                }

            case _ =>
        }

        Digaku.engine.eventStream.emit(LikeEvent(user, thisObject.asInstanceOf[Likable]))

        ed
    }

    /**
     * remove like by id user
     * @param id id user
     */
    def removeLike(id: Long) {
        if (!this.getVertex.pipe.in(LIKES).in(COLLECTION_VERTEX).has("id", id).headOption.isDefined)
            throw NotExistsException("You cannot unlike this post/response, because you didn't like it")

        val user = User.getById(id).getOrElse(throw NotExistsException("user not found with id : " + id))

        if (user.isLocked) {
            throw PermissionDeniedException("Tidak dapat membatalkan Like. Anda masih berada dalam status Suspended.")
        }

        this.reload().getVertex.pipe
            .inE(LIKES).as("ed").outV().in(COLLECTION_VERTEX).has("id", id).back("ed")
            .asInstanceOf[GremPipeEdge]
            .headOption.map { ed =>
            val rateUser = getLikeRate(user)
            if (rateUser > 0) {
                recalculateCreatorExperience(user, -rateUser)
                incrementLikeRate(user, -rateUser)
            }

            db.removeEdge(ed)
            counter.decrement("likes")
        }

        db.commit()

        this match {
            case response:Response =>
                if (response.creator != user) {
                    debug(s"remove experience to user ${response.creator} from an remove like")
                    response.creator.decreaseExperience(1)

                    UserLeaderboard.incrementPoint(response.creator, -3)
                }
            case _ =>
        }

        Digaku.engine.eventStream.emit(UnlikeEvent(user, this))

    }

    /**
     * remove likes by user (liker)
     * @param user user who likes the post.
     */
    def removeLike(user:User){
        removeLike(user.getId)
    }


    /**
     * get all user like dari  post atau response
     * @return
     */
    def getLikers(offset:Int, limit:Int,
                  sinceTime:Option[Long]=None, maxTime:Option[Long]=None):Iterable[User]= {

        var pipe = this.getVertex.pipe.inE(LIKES)

        sinceTime.map { time =>
            pipe = pipe.has("creationTime", T.gt, time).asInstanceOf[GremPipeEdge]
        }

        maxTime.map { time =>
            pipe = pipe.has("creationTime", T.lt, time).asInstanceOf[GremPipeEdge]
        }

        pipe.outV().in(COLLECTION_VERTEX).range(offset, (offset + limit) - 1)
            .iterator().flatMap(_.toCC[User]).toIterable
    }

    /**
     * get all user like edges dari  post atau response
     * @return edges
     */
    def getLikersEdge(offset:Int, limit:Int,
                  sinceTime:Option[Long]=None, maxTime:Option[Long]=None):List[Edge]= tx { t =>

        var pipe = t.getVertex(getId).pipe.inE(LIKES)

        sinceTime.map { time =>
            pipe = pipe.has("creationTime", T.gt, time).asInstanceOf[GremPipeEdge]
        }

        maxTime.map { time =>
            pipe = pipe.has("creationTime", T.lt, time).asInstanceOf[GremPipeEdge]
        }

        pipe.range(offset, (offset + limit) - 1).iterator().toList
    }

    /**
     * cek apakah user sudah like atau belum
     * @param user user
     * @return true or false
     */

    def isLiker(user:User):Boolean={
        this.reload()
        this.getRawVertex.pipe.in(LIKES)
            .in(COLLECTION_VERTEX).has("id", user.getRawId).headOption.isDefined
    }

    /**
     * Get likes count by counting incoming LIKES edge.
     * @return
     */
    def getLikesCount = {
//        getVertex.pipe.in(LIKES).count()
        counter.get("likes").toInt
    }

    def incrementLikesCount(by:Int=1){
        counter.incrementBy("likes", by.toLong)
    }

    def decrementLikesCount(by:Int=1){
        counter.decrementBy("likes", by.toLong)
    }

    /**
     * increment rate untuk setiap user yang like thread
     * @param user user yang ngasih rate
     * @param by increment by this value
     */
    def incrementLikeRate(user:User, by:Int): Unit ={
        counter.incrementBy("rate-%s".format(user.getId.toString), by)
    }

    /**
     * mendapatkan rate, yang ditambahkan ketika user like
     * @param user liker.
     * @return
     */
    def getLikeRate(user:User):Int = {
        counter.get("rate-%s".format(user.getId)).toInt
    }

}
/*$regression:end$*/



object Likable extends DbAccess {
    import com.ansvia.graph.BlueprintsWrapper._

    def getById(id:IDType) = {
        val v = db.getVertex(id)
        if (v != null)
            v.toCC[Likable]
        else
            None
    }
}
