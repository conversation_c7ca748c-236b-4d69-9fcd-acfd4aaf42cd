///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Types.GraphType
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.model.Label._
//import scala.collection.JavaConversions._
//import com.ansvia.graph.BlueprintsWrapper._
//
//case class Question(title:String, content:String) extends Post(content) {
//
//    /**
//     * @see [[com.ansvia.digaku.model.PostKind]]
//     */
//    val kind:Int = PostKind.QUESTION
//
//
//}
//
//object Question extends DaoBase[GraphType, Question] {
//    val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.QuestionRootVertex"
//}