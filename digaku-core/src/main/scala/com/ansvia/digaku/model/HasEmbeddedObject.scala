/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.exc.LimitationReachedException
import com.ansvia.graph.BlueprintsWrapper.IdDbObject
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.Types._
import com.tinkerpop.blueprints.Direction
import com.ansvia.graph.annotation.Persistent
import com.ansvia.digaku.Types

/**
 * Author: robin
 *
 * Trait untuk digunakan oleh object agar bisa
 * di-embed oleh embeddable object ( see [[Embeddable]] )
 *
 */
trait HasEmbeddedObject[idT] extends IdDbObject[idT] {
    this: Counter =>

    import scala.collection.JavaConversions._
    import com.ansvia.graph.BlueprintsWrapper._

    implicit protected def db:GraphType

    @Persistent var embeddedObjectCount:Int = 0

    /**
     * get embedded pictures kecuali yang di delete
     * @return
     */
    def getEmbeddedObjects:Seq[Embeddable[Types.IDType]] = {
        if (embeddedObjectCount > 0){
            reload()
            getVertex.pipe.out(EMBED).hasNot("deleted", true)
                .iterator().toSeq.flatMap(_.toCC[Embeddable[IDType]])
        }else
            Nil
    }

    /**
     * Menambahkan Embedded object untuk object tersebut
     * @param obj object yang akan di-remove.
     * @param noTx apabila tidak ingin di-commit langsung ke db.
     */
    def addEmbeddedObject(obj: Embeddable[IDType], noTx:Boolean=false) {
        if (embeddedObjectCount >= 100)
            throw LimitationReachedException("max embedded object is 100")

        getVertex --> EMBED --> obj.getVertex

        getCounter.increment(HasEmbeddedObject.embeddedObjectText)
        embeddedObjectCount = getCounter.get(HasEmbeddedObject.embeddedObjectText).toInt

        save()

        if (!noTx)
            db.commit()
    }

    /**
     * Add embedded objects secara massal.
     * non transaction.
     * Untuk penggunaan transaction gunakan yang satu-persatu dengan
     * parameter noTx = false, @see [[com.ansvia.digaku.model.HasEmbeddedObject.addEmbeddedObject]]
     * @param objs objects to add.
     */
    def addEmbeddedObjects(objs:Embeddable[IDType]*){
        objs.foreach(o => addEmbeddedObject(o, noTx = true))
    }


    /**
     * Menghapus embedded object secara permanen
     * @param obj object yang akan di-remove.
     */
    def removeEmbeddedObject(obj:Embeddable[IDType]){
        getVertex.pipe.out(EMBED).has("id", obj.getId)
            .asInstanceOf[GremPipeVertex]
            .iterator().toSeq.map { v =>

            v.getEdges(Direction.BOTH).foreach(db.removeEdge)
            db.removeVertex(v)

//            embeddedObjectCount -= 1

            getCounter.decrement(HasEmbeddedObject.embeddedObjectText)

            save()
        }


        embeddedObjectCount = getCounter.get(HasEmbeddedObject.embeddedObjectText).toInt

        db.commit()
    }
}

private[this] object HasEmbeddedObject {
    val embeddedObjectText = "embedded-objects"
}