///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Types._
//import com.tinkerpop.blueprints.Vertex
//import com.ansvia.digaku.Digaku
//import scala.collection.JavaConversions._
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.event.impl.ConnectedFriendsFbEvent
//import com.ansvia.digaku.exc.DigakuException
//import com.ansvia.graph.IdGraphTitanDbWrapper._
//
//
//case class FacebookInfo(fbId:String) extends ThirdPartyAccount {
//    import com.ansvia.graph.BlueprintsWrapper._
//    import Label._
//
//    /**
//     * untuk mendapatkan User dari object ini
//     * @return
//     */
//    def getUserConnectFb = {
//        this.getVertex.pipe.in(CONNECT_FB).headOption.flatMap(_.toCC[User])
//    }
//
//    /**
//     * menambahkan friends FacebookInfo
//     * @param facebookInfos
//     */
//    def addFriends(facebookInfos:FacebookInfo*) {
//        for (facebookInfo <- facebookInfos; if (!isFriend(facebookInfo))){
//            this --> FRIEND --> facebookInfo
//        }
//        db.commit()
//
//        getUserConnectFb.map { user =>
//            Digaku.engine.eventStream.emit(ConnectedFriendsFbEvent(user))
//        }
//    }
//
//    /**
//     * cek apakah this sudah berteman dengan facebookInfo
//     * @param facebookInfo
//     * @return
//     */
//    def isFriend(facebookInfo:FacebookInfo):Boolean = {
//        this.getVertex.pipe.out(FRIEND).has("fbId", facebookInfo.fbId).hasNext
//    }
//
//    /**
//     * cek apakah object facebookInfo sudah digunakan oleh user atau belum
//     * @return
//     */
//    def isUsed:Boolean = {
//        getUserConnectFb.isDefined
//    }
//
//    /**
//     * custom save routine, called when blueprint-scala
//     * saving this record.
//     * @return
//     */
//    override def __save__(v:Vertex) = {
//        v.setProperty("fb.id", fbId)
//    }
//}
//
//object FacebookInfo extends DaoBase[GraphType, FacebookInfo] {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.FacebookInfoRootVertex"
//
//    /**
//     * digunakan untuk create FacebookInfo
//     * @param fbId facebook id
//     * @return see [[com.ansvia.digaku.model.FacebookInfo]]
//     *         save to database ketika belum exist dan mengembalikan FacebookInfo
//     *         ketika sudah exist hanya akan mengembalikan FacebookInfo yang sudah ada
//     */
//    def create(fbId:String): FacebookInfo ={
//        if (!existsByFbId(fbId)) {
//            val facebookInfo = FacebookInfo(fbId)
//
//            //            var newFacebookInfo:FacebookInfo = null
//
//            try {
//                //                transact {
//
//                val vertex = facebookInfo.saveWithLabel(VertexLabels.THIRDPARTY_ACCOUNT)
//
//                addToRoot(vertex)
//
//                val newFacebookInfo = vertex.toCC[FacebookInfo].getOrElse {
//                    throw new DigakuException("Cannot create facebookInfo : "+FacebookInfo)
//                }
//
//                db.commit()
//
//
//                newFacebookInfo
//
//                //                }
//            } catch{
//                case e:Exception =>
//                    throw new DigakuException("Cannot create facebookInfo " + fbId)
//            }
//        } else {
//            getByFbid(fbId).getOrElse(throw new DigakuException("Cannot getting FacebookInfo with id: " + fbId))
//        }
//    }
//
//    /**
//     * get FacebookInfo by facebook id
//     * @param fbId facebook id
//     * @return
//     */
//    def getByFbid(fbId: String): Option[FacebookInfo] = {
//
//        db.query().has("label", VertexLabels.THIRDPARTY_ACCOUNT)
//            .has("fb.id", fbId)
//            .vertices()
//            .headOption.flatMap(_.toCC[FacebookInfo])
//
//        //        db.getVertices("fb.id", fbId).headOption.flatMap(_.toCC[FacebookInfo])
//    }
//
//    /**
//     * cek apakah FacebookInfo sudah ada atau belum.
//     * @param fbId
//     * @return
//     */
//    def existsByFbId(fbId: String): Boolean = {
//        getByFbid(fbId).isDefined
//    }
//}
//
