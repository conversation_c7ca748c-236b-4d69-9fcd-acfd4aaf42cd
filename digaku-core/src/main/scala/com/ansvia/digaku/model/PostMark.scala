/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.{PostMarkCategoryDao, PostMarkDao}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.graph.BlueprintsWrapper.DbObject
import com.tinkerpop.blueprints.{Direction, Edge, Graph, Vertex}
import com.tinkerpop.gremlin.java.GremlinPipeline
import com.ansvia.graph.BlueprintsWrapper._
import scala.collection.JavaConversions._

/**
 * Author: nadir
 * 
 */
abstract class PostMarkCategoryBase [idT, dbT <: Graph](category:String)
    extends BaseModel[idT] with DbObject with DbAccess {

    /**
     * Menambahkan post mark
     * @param postMarks
     */
    def addPostMark(postMarks:Seq[PostMark]){
        for (pM <- postMarks){
            db.getVertex(pM.getId) --> POST_MARK_CATEGORY --> db.getVertex(this.getId)
        }

        db.commit()

    }

    /**
     * Mendapatkan daftar post marks.
     * @return Seq[PostMark]
     */
    def getPostMarks:Seq[PostMark]={
        reload().getVertex.pipe.in(POST_MARK_CATEGORY).iterator().flatMap(_.toCC[PostMark]).toSeq
    }

    /**
     * Remove post mark by title
     * @param title title dari post mark yang diinginkan.
     */
    def removePostMarkByTitle(title:String){
        val pM = PostMark.getByPostMarkTitle(title).get
        getVertex.pipe.inE(POST_MARK_CATEGORY).as("ed")
            .outV().has("id", pM.getId)
            .back("ed").asInstanceOf[GremlinPipeline[Vertex, Edge]]
            .headOption.map { x =>
                db.removeEdge(x)
            }

        db.commit()
    }

    /**
     * Remove post mark
     * @param postMark post mark see [[com.ansvia.digaku.model.PostMark]]
     */
    def removePostMark(postMark:PostMark){
        getVertex.pipe.inE(POST_MARK_CATEGORY).as("ed")
            .outV().has("id", postMark.getId)
            .back("ed").asInstanceOf[GremlinPipeline[Vertex, Edge]]
            .headOption.map { x =>
                db.removeEdge(x)
            }

        db.commit()
    }

    def clearPostmark(){
        db.getVertex(this.getId).getEdges(Direction.BOTH).iterator().toList.foreach(db.removeEdge)
        db.commit()
    }

}

case class PostMark(title:String, var color:String) extends BaseModel[IDType] with DbObject with DbAccess {
    override def __save__(vertex: Vertex): Unit = {
        if (vertex.getOrElse("post-mark.title", "").trim.isEmpty) {
            vertex.setProperty("post-mark.title", title.toLowerCase)
        }

        super.__save__(vertex)
    }
}

object PostMark extends PostMarkDao[IDType, GraphType]

case class PostMarkCategory(var category:String) extends PostMarkCategoryBase[Long, GraphType](category)

object PostMarkCategory extends PostMarkCategoryDao[IDType, GraphType]