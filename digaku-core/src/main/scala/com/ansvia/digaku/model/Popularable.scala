/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types._
import com.ansvia.digaku.helpers.RootVertex
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.model.Popular.{ContainsKeyIdentifier, ExceptKeyIdentifier, KeyIdentifier, NopKeyIdentifier}
import com.ansvia.graph.BlueprintsWrapper._
import com.tinkerpop.blueprints.Vertex

import scala.collection.JavaConversions._

/** $regression:$ Fungsional popular content.
 */
/**
 * Author: ubai, robin
 *
 */

/**
 * digunakan untuk post yang bisa di popular kan
 */
trait Popularable {
    this : RootVertex =>

    import com.ansvia.digaku.database.GraphCompat.tx


    lazy val POPULAR_VERTEX_CLASS:String = ROOT_VERTEX_CLASS + "-Popular"

    /**
     * get popular root vertex
     * @return
     */
    def popularRootVertex = {
        val _id =
        db.getVertices("_class_", POPULAR_VERTEX_CLASS)
            .map(_.getId)
            .headOption.getOrElse {
            tx { t =>
                val v = t.addVertex(null)
                v.setProperty("_class_", POPULAR_VERTEX_CLASS)
                v.getId
            }
        }
        db.getVertex(_id)
    }

    def getPopularRootVertexTx(_db:TransactionalGraphType) = {
        _db.getVertices("_class_", POPULAR_VERTEX_CLASS)
            .headOption.getOrElse {

            val v = _db.addVertex(null)
            v.setProperty("_class_", POPULAR_VERTEX_CLASS)
            v
        }
    }

    /**
     * Digunakan untuk crear popular dengan kind tertentu
     * non transaction, harus dijalankan di dalam transaction.
     */
    def clearPopular(keyIdentifier:KeyIdentifier=NopKeyIdentifier) {
        if (keyIdentifier.key > 0L){
            keyIdentifier match {
                case ContainsKeyIdentifier(keyId) =>
                    popularRootVertex.pipe.outE(POPULAR).has("keyIdentifierLong", keyId)
                        // sementara dibuat hasNot('promoted') saja karena ada bug di Titan
                        // issue ini: https://github.com/thinkaurelius/titan/issues/868
//                        .hasNot("promoted", true)
                        .hasNot("promoted")
                        .remove()
                case ExceptKeyIdentifier(keyId) =>
                    popularRootVertex.pipe.outE(POPULAR).hasNot("keyIdentifierLong", keyId)
                        // sementara dibuat hasNot('promoted') saja karena ada bug di Titan
                        // issue ini: https://github.com/thinkaurelius/titan/issues/868
//                        .hasNot("promoted", true)
                        .hasNot("promoted")
                        .remove()
                case NopKeyIdentifier =>
            }
        }else{
            popularRootVertex.pipe.outE(POPULAR).remove()
        }
    }

    /**
     * add popular to root vertex
     * @param vertex vertex
     * @param score score popular
     */
    def addPopular(vertex:Vertex, score:Double) = {
        require(vertex != null, "vertex is null")
        require(vertex.getOrElse("_class_", "") == getClass.getCanonicalName.replace("$",""),
            "cannot add popular item %s into this dao %s".format(vertex.getOrElse("_class_", ""),
                this.getClass.getCanonicalName.replace("$","")))

        val edge = popularRootVertex.addEdge(POPULAR, vertex)
        edge.setProperty("score", score)

        edge
    }

    /**
     * digunakan untuk remove content dari popular
     * @param vertex object/record yang ingin dihapus.
     */
    def removePopular(vertex:Vertex) {
        vertex.pipe.inE(POPULAR)
            .remove()
    }

    /**
     * Digunakan untuk menambahkan content ke promoted popular.
     * @param vertex object/record yang ingin ditambahkan ke daftar popular.
     * @param score skor popularitas object/record.
     * @param user promotor / user yang mem-promote.
     * @return
     */
    def promoteToPopular(vertex:Vertex, score:Double, user:User, headLine:String = "") = {

        val existing = popularRootVertex.pipe.outE(POPULAR)
            .as("edge")
            .inV()
            .has("id", vertex.getId)
            .back("edge")
            .asInstanceOf[GremPipeEdge]

        if (!existing.hasNext) {

            val edge = addPopular(vertex, score)

            edge.setProperty("promotorId", user.getId)
            // true ketika di promote oleh admin
            edge.setProperty("promoted", true)
            edge.setProperty("headLine", headLine)

            edge

        } else {
            existing.headOption.map { edge =>
                edge.setProperty("score", score)
                edge.setProperty("promotorId", user.getId)
                // true ketika di promote oleh admin
                edge.setProperty("promoted", true)
                edge.setProperty("headLine", headLine)
            }
        }

    }

    /**
     * add popular to root vertex
     * @param vertex vertex
     * @param score score popular
     */
    def addPopularTx(vertex:Vertex, score:Double, _db:TransactionalGraphType) = {
        require(vertex != null, "vertex is null")
        require(vertex.getOrElse("_class_", "") == getClass.getCanonicalName.replace("$",""),
            "cannot add popular item %s into this dao %s".format(vertex.getOrElse("_class_", ""),
                this.getClass.getCanonicalName.replace("$","")))

        val edge = getPopularRootVertexTx(_db).addEdge(POPULAR, vertex)
        edge.setProperty("score", score)

        edge
    }
}
/*$regression:end$*/

object Popular {
    sealed abstract class KeyIdentifier(val key:Long)
    case class ContainsKeyIdentifier(override val key:Long) extends KeyIdentifier(key)
    case class ExceptKeyIdentifier(override val key:Long) extends KeyIdentifier(key)
    object NopKeyIdentifier extends KeyIdentifier(0L)
}
