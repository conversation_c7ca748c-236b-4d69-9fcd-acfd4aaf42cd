/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.model.Label._
import com.ansvia.graph.AbstractDbObject
import scala.collection.JavaConversions._
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.digaku.helpers.AbstractDbAccess

/**
 * Author: robin
 *
 * unittest ada di com.ansvia.digaku.functional.ViaAppInfoSpec
 */
trait ViaAppInfo extends AbstractDbObject with AbstractDbAccess {


    /**
     * Mengembalikan apps yang digunakan user untuk melakukan response
     * @return app
     */
    def getPostVia:Option[App] = {
        this.reload().getVertex.pipe.out(POST_VIA).headOption.flatMap(_.toCC[App])
    }

    /**
     * Set post via app edge
     * non transaction, needs to execute
     * inside of transaction.
     * @param app app to add via.
     * @return edge
     */
    def setPostVia(app:App) = {
        this.getVertex.addEdge(POST_VIA, app.getVertex)
    }

}
