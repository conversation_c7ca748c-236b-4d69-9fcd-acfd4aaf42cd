/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types._
import java.util.Date
import com.ansvia.graph.AbstractIDGetter
import com.ansvia.digaku.Types

/**
 * Author: robin
 *
 */


/**
 * abstract class yang merupakan representasi dari object stream
 * @param streamable
 */
sealed abstract class StreamObject(streamable:Streamable[IDType]) extends AbstractIDGetter[IDType] {


    def getId: Types.IDType = streamable.getId

    def content = streamable

}

/**
 * Representasi stream object untuk normal post.
 * @param post
 */
case class PostStreamObject(post:Streamable[IDType]) extends StreamObject(post)

/**
 * Representasi stream object untuk post yang di-shout.
 * @param shouter
 * @param message
 * @param post
 * @param time
 */
case class ShoutStreamObject(shouter:User, message:String,
                             post:Streamable[IDType], time:Date) extends StreamObject(post)

/**
 * Representasi stream object untuk post yang di-retalk.
 * @param retalker
 * @param post
 * @param channelName
 * @param time
 */
case class RetalkStreamObject(retalker:User,
                             post:Streamable[IDType], channelName:String, time:Date) extends StreamObject(post)


//case class SponsorPostRef(sp:SponsorPost) extends StreamObject(sp) {
//}
