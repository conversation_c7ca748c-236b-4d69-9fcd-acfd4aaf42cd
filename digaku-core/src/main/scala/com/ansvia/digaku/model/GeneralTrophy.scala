/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.graph.annotation.Persistent
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.Types._

/**
 * Author: nadir, robin
 * 
 */

/**
 * Implementasi dari Trophy, yang paling sering digunakan.
 * @param name nama trophy.
 * @param description penjelasan tentang trophy.
 */
case class GeneralTrophy(var name:String, var description:String) extends Trophy(name, description) {

//    import com.ansvia.digaku.database.GraphCompat.transact

    /**
     * key property dari user
     * misal : level atau supportersCount atau supportingCount
     * yang nantinya digunakan untuk parameter penambahan trophy
     *
     * sebagai contoh :
     * di general property ini jika ingin menambahkan trophy ke user yang memiliki level 0 sampai 9
     * akan memiliki trophy Meteora
     * maka :
     * keyProperty akan di-isi level (key dari User)
     * greaterThanEqual = 0
     * lessThen = 10
     * atau bisa menggunakan method setAutoAssignWithInterval see[[com.ansvia.digaku.model.GeneralTrophy#setAutoAssignWithInterval(java.lang.String, long, long)]]
     *
     */
    @Persistent var keyProperty = ""

    /**
     * greaterThanEqual ini untuk menentukan interval minimum
     * dari parameter keyProperty jika keyProperty merupakan type number
     */
    @Persistent var greaterThanEqual = 0L

    /**
     * lessThan ini untuk menentukan interval maximum-nya
     * dari nilai parameter keyProperty, jika keyProperty-nya merupakan type number
     */
    @Persistent var lessThan = 0L

    /**
     * value merupakan nilai yang pasti dari keyProperty
     * contoh :
     * ketika user post dengan 10 post perhari dalam tujuh hari
     * akan merubah property vertex user (teenPostADayOnWeek)
     * menjadi true dan menambahkan trophy Spry
     * maka :
     * keyProperty di-isi teenPostADayOnWeek
     * value = "true" (String)
     *
     * atau bisa menggunakan see [[com.ansvia.digaku.model.GeneralTrophy#setAutoAssignWithValue(java.lang.String, java.lang.String)]]
     * untuk contoh penggunaan lengkapnya bisa lihat di GeneralTrophySpec
     *
     */
    @Persistent var value = ""

    /**
     * digunakan untuk check apakah user dapat menerima trophy ini atau tidak
     * true ketika bisa menerima trophy
     * @param user user yang akan dicheck.
     * @return
     */
    def canReceive(user: User): Boolean = {
        if (autoAssign) {

            var state = false

            user.getVertex.get[Any](keyProperty).foreach { x =>
                if (!value.trim.isEmpty) {
                    x match {
                        case rv:Boolean =>
                            state = rv == value.toBoolean
                        case rv:Int =>
                            state = rv == value.toInt
                        case rv:Long =>
                            state = rv == value.toLong
                        case _ =>
                            state = x == value
                    }

                } else {
                    if (greaterThanEqual > 0) {
                        state = x.toString.toLong >= greaterThanEqual
                    }

                    if (lessThan > 0) {
                        state = x.toString.toLong < lessThan
                    }

                    if (greaterThanEqual > 0 && lessThan > 0) {

                        state = x.toString.toLong >= greaterThanEqual && x.toString.toLong < lessThan
                    }
                }
            }

            state

        } else {
            true
        }

    }

    /**
     * set automatic assignment trophy dengan nilai property yang pasti
     * @param key property user
     * @param value nilai property
     */
    def setAutoAssignWithValue(key:String, value:String) {

//        transact {
            this.keyProperty = key
            this.autoAssign = true
            this.value = value
            this.greaterThanEqual = 0
            this.lessThan = 0
            this.save()
//        }
        db.commit()

    }

    /**
     * set automatic assignment trophy dengan interval tertentu
     * @param key property user
     * @param gte greater than equal
     * @param lt less than
     */
    def setAutoAssignWithInterval(key:String, gte:Long, lt:Long) {

//        transact {
            this.keyProperty = key
            this.autoAssign = true
            this.value = ""
            this.greaterThanEqual = gte
            this.lessThan = lt
            this.save()
//        }
        db.commit()

    }

    /**
     * remove auto assign
     */
    def removeAutoAssign() {
//        transact {
            this.keyProperty = ""
            this.autoAssign = false
            this.value = ""
            this.greaterThanEqual = 0
            this.lessThan = 0
            this.save()
//        }
        db.commit()
    }

    /**
     * digunakan untuk set property name
     * @param trophyName
     */
    def setTrophyName(trophyName: String) {
        this.name = trophyName
    }

    def setDescription(description: String){
        this.description = description
    }
}

object GeneralTrophy extends DaoBase[GraphType, GeneralTrophy] {
    val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.GeneralTrophyRootVertex"
}