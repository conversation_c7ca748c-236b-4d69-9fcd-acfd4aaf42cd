/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.graph.annotation.Persistent

trait Lockable {

    @Persistent var locked = false
    @Persistent var lockedReason = ""
    @Persistent var lockedTime = 0L

    /**
     * Set lock for this object.
     * @param locked lock state.
     */
    def setLocked(locked:Boolean) {
        this.locked = locked

        if (locked) {
            this.lockedTime = Digaku.engine.dateUtils.nowMilis
        }
    }

    /**
     * Check is this object locked.
     * @return
     */
    def isLocked:Boolean = locked

}

/**
 * Trait untuk lockable object yang memiliki sifat
 * auto release, artinya object tersebut bisa otomatis terbuka/unlock
 * apabila waktunya sudah melebihi `releaseAfter`.
 *
 * Operasi auto-unlock tidak real-time, menggunakan bulk operation
 * via scheduler.
 */
trait LockableAutoRelease extends Lockable with DbAccess {

    /**
     * Kapan waktu object ini akan otomatis di-unlock.
     */
    @Persistent var releaseAfter = 0L

    def isExpired:Boolean = {
        this.releaseAfter <= Digaku.engine.dateUtils.nowMilis
    }

}

