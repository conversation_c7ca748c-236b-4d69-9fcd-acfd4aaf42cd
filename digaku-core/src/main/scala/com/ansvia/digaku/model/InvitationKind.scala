/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

/**
 * Author: robin
 * 
 */
object InvitationKind {
    val NONE = 0
    val BECOME_STAFF = 1
    val BECOME_MEMBER = 2
    val BECOME_OWNER = 3
    val BECOME_SUPPORTED = 4
}
