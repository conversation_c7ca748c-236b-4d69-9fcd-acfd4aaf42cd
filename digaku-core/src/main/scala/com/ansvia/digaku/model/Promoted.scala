/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.persistence.SeqStore
import com.ansvia.digaku.utils.ArrangeSettings
import com.ansvia.digaku.event.impl.PromotedThreadEvent
import scala.collection.JavaConversions._
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.digaku.exc._
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.exc.InvalidParameterException
import com.ansvia.graph.annotation.Persistent
import scala.reflect.ClassTag
import com.tinkerpop.blueprints.{Vertex, Direction}
import com.thinkaurelius.titan.core.attribute.Decimal
import com.ansvia.digaku.utils.ArrangeSettings._

/**
 * Author: temon
 * Date: 2/5/14
 * Time: 12:03 PM
 *
 * Model untuk promoted
 * ex: promoted group or promoted post
 */
case class Promoted(var promotedId:Long, var logo:String, kind:Int) extends BaseModel[IDType] with HasCreator {

    import Promoted.Kind

    @Persistent var lastEditorUserId = 0L
    @Persistent var headlineTitle = ""

    override def creatorLabel = CREATE_PROMOTED

    lazy val lastEditor:Option[User] = {
        User.getById(lastEditorUserId)
    }

    lazy val promotedObj:Option[Promotable] = {
        kind match {
            case Kind.GROUP => Forum.getById(promotedId)
            case Kind.POST => Post.getPostById(promotedId)
            case Kind.PICTURE => Picture.getPictureFromBaseById(promotedId)
            case x =>
                throw NotSupportedException("unknown kind: " + x)
        }
    }

    /**
     * Get creator headline, who admin add featured post to headline
     * @return
     */
    def creatorHeadline:Option[User] = {
        val id = getVertex.getEdges(Direction.IN, HAS_PROMOTED)
            .head.getProperty[Long]("headlineCreator")

        User.getById(id)
    }

    /**
     * Get creator draft, who admin remove featured post from headline
     * @return
     */
    def creatorDraft:Option[User] = {
        val id = getVertex.getEdges(Direction.IN, HAS_PROMOTED)
            .head.getProperty[Long]("draftCreator")

        User.getById(id)
    }
}

/**
 * Promoted DAO.
 */
object Promoted extends DaoBase[GraphType, Promoted] {
    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.PromotedRootVertex"

    val promotedListStr = "promoted-list"

    lazy val promotedListStore = ArrangeSettings.seqStore.build("promoted-list", () => ArrangeSettings.generateId())
    lazy val promotedSubForumListStore = ArrangeSettings.seqStore.build("promoted-subforum-list", () => ArrangeSettings.generateId())

    /**
     * Jenis promoted-nya.
     *
     */
    object Kind {
        val ALL = 0
        val POST = 1
        val GROUP = 2
        val PICTURE = 3
    }

    /**
     * Create Promoted
     * @param creator user yang promote.
     * @param promotedObject promoted object.
     * @param kind jenis promoted. see [[com.ansvia.digaku.model.Promoted.Kind]]
     * @param logo promoted logo.
     * @return
     */
    def create(creator:User, promotedObject:Promotable, kind:Int, logo:String, headline:String = ""):Promoted = {

        if (isExists(promotedObject.getId, kind))
            throw AlreadyExistsException("already promoted.")

        if (creator.role == UserRole.USER)
            throw PermissionDeniedException("Permission Denied.")

        kind match {
            case Kind.GROUP =>
                createPromotedSubForum(creator, promotedObject, kind)

            case Kind.POST | Kind.PICTURE =>
                createPromotedPost(creator, promotedObject, kind, logo, headline)

            case x =>
                throw NotSupportedException("unknown kind: " + x)
        }


    }

    private def createPromotedPost (creator:User, promotedObject:Promotable, kind:Int, logo:String, headline:String) = {
        val promotedObj = kind match {
            case Kind.POST =>
                Post.getPostById(promotedObject.getId).getOrElse(throw InvalidParameterException("Post not found."))

            case Kind.PICTURE =>
                Picture.getPictureFromBaseById(promotedObject.getId).getOrElse(throw InvalidParameterException("Post not found."))
        }

        if (logo.isEmpty)
            throw InvalidParameterException("Logo can\'t be empty.")

        val promoted = Promoted(promotedObject.getId, logo, kind)

        if (kind != Kind.GROUP)
            promoted.headlineTitle = headline

        creator.reload()

        val vertex = promoted.save()

        val ed = addToRoot(vertex)

        val newPromoted = vertex.toCC[Promoted].getOrElse{
            throw new DigakuException("Cannot promoted : %s".format(promotedObj.getId))
        }

        ed.setProperty("kind", kind)

        creator --> CREATE_PROMOTED --> newPromoted
        val promotedeEd = promotedObj --> HAS_PROMOTED --> newPromoted <()

        promotedeEd.set("creationTime", Digaku.engine.dateUtils.nowMilis)
        promotedeEd.set("creatorId", creator.getId)

        db.commit()

        // move to top
        moveToTop(newPromoted, promotedListStore)

        // promote thread yang disimpan hanya 10
        getArrange(promotedListStore).drop(10).foreach { prom =>
            deleteById(prom.getId)
        }

        // emit untuk notifikasi ke creator post nya.
        promotedObject match {
            case p: Post =>
                Digaku.engine.eventStream.emit(PromotedThreadEvent(p))
            case _ =>
        }

        newPromoted
    }

    private def createPromotedSubForum (creator:User, promotedObject:Promotable, kind:Int) = {
        val promotedObj = Forum.getById(promotedObject.getId).getOrElse(throw InvalidParameterException("Sub Forum not found."))

        val promoted = Promoted(promotedObject.getId, "", kind)

        creator.reload()

        val vertex = promoted.save()

        val ed = addToRoot(vertex)

        val newPromoted = vertex.toCC[Promoted].getOrElse{
            throw new DigakuException("Cannot promoted : %s".format(promotedObj.getId))
        }

        ed.setProperty("kind", kind)

        creator --> CREATE_PROMOTED --> newPromoted
        val promotedeEd = promotedObj --> HAS_PROMOTED --> newPromoted <()

        promotedeEd.set("creationTime", Digaku.engine.dateUtils.nowMilis)
        promotedeEd.set("creatorId", creator.getId)

        db.commit()

        // move to top
        moveToTop(newPromoted, promotedSubForumListStore)

        // promote thread yang disimpan hanya 3
        getArrange(promotedSubForumListStore).drop(3).foreach { prom =>
            deleteById(prom.getId)
        }

        // emit untuk notifikasi ke creator post nya.
        promotedObject match {
            case p: Post =>
                Digaku.engine.eventStream.emit(PromotedThreadEvent(p))
            case _ =>
        }

        newPromoted
    }


    /**
     * Untuk update/edit promoted
     *
     * @param promotedId id promoted obj yang lama.
     * @param newPromotedObject id promoted obj yg baru.
     * @param kind jenis promoted. see [[com.ansvia.digaku.model.Promoted.Kind]]
     * @param logo promoted logo.
     * @return
     */
    def update(promotedId:Long, newPromotedObject:Promotable, kind:Int, logo:String, headline:String = "") = {

        val newPromotedObj = kind match {
            case Kind.GROUP => Forum.getById(newPromotedObject.getId).getOrElse(throw InvalidParameterException("Group not found."))
            case Kind.POST => Post.getPostById(newPromotedObject.getId).getOrElse(throw InvalidParameterException("Post not found."))
            case Kind.PICTURE => Picture.getPictureFromBaseById(newPromotedObject.getId).getOrElse(throw InvalidParameterException("Post not found."))
            case x =>
                throw NotSupportedException("unknown kind: " + x)
        }

        if (!isExists(promotedId, kind))
            throw InvalidParameterException("Promoted not found.")

        val promoted = Promoted.getByPromoteId(promotedId, kind).get

        promoted.reload()

        val promotedV = promoted.getVertex

        promotedV.pipe.inE(HAS_PROMOTED).iterator().foreach( x => db.removeEdge(x) )

        newPromotedObj --> HAS_PROMOTED --> promotedV

        promoted.logo = logo

        if (kind != Kind.GROUP)
            promoted.headlineTitle = headline

        promoted.save()

        db.commit()
    }

    /**
     * Get Promoted list without kind group
     * @param offset starting offset.
     * @param limit ends limit.
     * @param toDisplayed true if want get featured content is published to users (headline)
     * @return
     */
    def getListPost(offset:Int, limit:Int, toDisplayed:Boolean = false) = {
        var pipe = rootVertex.pipe.outE(rootVertexLabel)
            .hasNot("kind", Kind.GROUP).inV()

        if (toDisplayed) {
            pipe = pipe.as("v")
                .inE(HAS_PROMOTED)
                .has("displayed", toDisplayed)
                .back("v").asInstanceOf[GremPipeVertex]
        }

        pipe.range(offset, (offset + limit) - 1)
            .iterator()
            .flatMap(_.toCC[Promoted])
            .map { promoted =>
                val x = promoted.getVertex.getEdges(Direction.IN, HAS_PROMOTED).head.getProperty[Decimal]("score")
                val score = if (x != null) x.floatValue() else 0.0
                (promoted, score)
            }.toSeq.sortBy(-_._2)
    }

    /**
     * Mendapatkan promoted obj list
     *
     * @param offset
     * @param limit
     * @param kind see [[com.ansvia.digaku.model.Promoted.Kind]]
     * @return
     */
    def getPromotedObjects[T<:Promotable:ClassTag](offset:Int, limit:Int, kind:Int) = {
        rootVertex.pipe.outE(rootVertexLabel)
            .has("kind", kind).inV()
            .in(HAS_PROMOTED)
            .range(offset, (offset + limit) - 1)
            .iterator()
            .flatMap(_.toCC[T])
    }

    /**
     * Check is Promoted label already exists.
     * @param kind jenis promoted. see [[com.ansvia.digaku.model.Promoted.Kind]]
     * @param promoteId Promoted obj id.
     * @return
     */
    def isExists(promoteId:Long, kind:Int) = {
        getByPromoteId(promoteId, kind).isDefined
    }

    /**
     * Get Promoted by promoted obj id.
     * @param promoteId Promoted id.
     * @param kind jenis promoted. see [[com.ansvia.digaku.model.Promoted.Kind]]
     * @return
     */
    def getByPromoteId(promoteId:Long, kind:Int) = {
        var pipe = rootVertex.pipe.outE(rootVertexLabel)

        if (kind != Kind.ALL)
            pipe = pipe.has("kind", kind).asInstanceOf[GremPipeEdge]

        pipe.inV().as("v")
            .in(HAS_PROMOTED)
            .has("id", promoteId)
            .back("v").asInstanceOf[GremPipeVertex]
            .headOption.flatMap(_.toCC[Promoted])
    }

    /**
     * Set sebuah featured post yang akan ditampilkan kepada user
     * @param vertex vertex dari object id yang akan di featured
     * @param kind see [[com.ansvia.digaku.model.Promoted.Kind]]
     * @param user see [[com.ansvia.digaku.model.User]]
     * @param toDisplayed set true jika ingin sebuah featured post ditampilkan kepada user
     * @param score score dari sebuah featured post
     */
    def setVisible(vertex:Vertex, kind:Int, user:User, toDisplayed:Boolean = false, score:Double = 0.0) {
        rootVertex.pipe.outE(rootVertexLabel)
            .inV().as("v")
            .in(HAS_PROMOTED)
            .has("id", vertex.getId)
            .back("v")
            .inE(HAS_PROMOTED)
            .headOption.foreach { edge =>
            edge.setProperty("score", score)

            if (toDisplayed) {
                // simpan admin yang menaikkan featured post menjadi headline
                edge.setProperty("headlineCreator", user.getId)
            } else {
                // simpan admin yang menurunkan featured post menjadi draft
                edge.setProperty("draftCreator", user.getId)
            }

            edge.setProperty("displayed", toDisplayed)
        }
    }

    /**
     * get arranged Promoted list
     * @param seqStore kind of SeqStore
     * @return
     */
    def getArrange[T](seqStore:SeqStore[T]):List[Promoted] = {
        val ids = ArrangeSettings.getArrangedList(seqStore)
        ids.flatMap { id =>
            val promoted = Promoted.getById(id.toLong)

            if (promoted.isEmpty) {
                ArrangeSettings.removeFromList(seqStore, id.toLong)
            }

            // handle jangan menampilkan thread yang private dan deleted
            promoted.flatMap(_.promotedObj).flatMap {
                case article:Article =>
                    if (article.isDeleted || article.originIsPrivate) {
                        deleteById(promoted.get.getId)
                        None
                    } else {
                        promoted
                    }
                case forum:Forum =>
                    if (forum.getPrivated || forum.isDeleted) {
                        deleteById(promoted.get.getId)
                        None
                    } else {
                        promoted
                    }
                case _ =>
                    promoted
            }
        }
    }

    override def deleteById(id: IDType)(implicit m: Manifest[Promoted]): Unit ={
        val promotedObj = Promoted.getById(id)
        val seqStore = promotedObj.flatMap(_.promotedObj).flatMap {
            case article:Article =>
                Some(promotedListStore)

            case forum:Forum =>
                Some(promotedSubForumListStore)

            case x =>
                None
        }
        seqStore.map( ss => ArrangeSettings.removeFromList(ss, id))
        super.deleteById(id)
    }

    def arrangeUp[T](promoted:Promoted, seqStore:SeqStore[T]):List[Promoted] = {
        ArrangeSettings.arrangeUp(seqStore, promoted.getId)
        getArrange(seqStore)
    }

    def arrangeDown[T](promoted:Promoted, seqStore:SeqStore[T]):List[Promoted] = {
        ArrangeSettings.arrangeDown(seqStore, promoted.getId)
        getArrange(seqStore)
    }

    def moveToTop[T](promoted:Promoted, seqStore:SeqStore[T]):List[Promoted] = {
        ArrangeSettings.moveToTop(seqStore, promoted.getId)
        getArrange(seqStore)
    }

    def setArrangedList[T](objList:List[String], seqStore:SeqStore[T]) = {
        ArrangeSettings.setArrangedList(seqStore, objList)
    }

}
