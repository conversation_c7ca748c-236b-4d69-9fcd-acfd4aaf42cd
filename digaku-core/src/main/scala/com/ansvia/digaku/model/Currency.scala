///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
///**
// * Author: robin
// *
// */
//object Currency {
//    val USD = "usd"
//    val IDR = "idr"
//    val SGD = "sgd"
//
//    val supported = Seq(
//        USD, IDR, SGD
//    )
//
//    /**
//     * Mengembalikan true jika currency disupport,
//     * false jika tidak
//     * @param cur
//     * @return
//     */
//    def isSupported(cur:String) =
//        supported.contains(cur)
//}
