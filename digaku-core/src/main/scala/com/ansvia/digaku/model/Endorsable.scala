/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.graph.{AbstractIDGetter, AbstractDbObject}
import com.ansvia.digaku.Types._
import com.ansvia.digaku.model.Label._
import scala.collection.JavaConversions._
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.digaku.helpers.DbAccess

/**
 * Author: robin
 *
 */

/**
 * Semua model yang diturunkan dari ini bisa
 * di-endorse.
 * Contoh [[com.ansvia.digaku.model.User]] dan [[com.ansvia.digaku.model.Forum]]
 */
trait Endorsable extends AbstractDbObject with AbstractIDGetter[IDType] with DbAccess {


    /**
     * Get endorsement list.
     * @param offset
     * @param limit
     * @return
     */
    def getEndorsements(offset:Int, limit:Int):Seq[Endorsement] = {
        getVertex.pipe.outE(ENDORSEMENT)
            .has("accepted", true)
            .inV()
            .dedup()
            .range(offset, offset + limit - 1)
            .iterator().flatMap(_.toCC[Endorsement]).toSeq
    }

    /**
     * cek apakah endorsement sudah ada atau belum
     * @param endorsement
     * @return
     */
    def hasEndorsement(endorsement:Endorsement):Boolean = {
        getVertex.pipe.outE(ENDORSEMENT)
            .has("endorsementId", endorsement.getId)
            .hasNext
    }

    /**
     * Cek apakah endorser sudah ada atau belum
     * @param user
     * @param endorsement
     * @return
     */
    def hasEndorser(user:User, endorsement:Endorsement):Boolean = {
        getVertex.pipe
            .outE(ENDORSEMENT)
            .has("endorserId", user.getId)
            .has("endorsementId", endorsement.getId).hasNext
    }

    /**
     * Dapetin jumlah count endorser dari endorsement
     * @param endorsement
     * @return
     */
    def getEndorserCount(endorsement:Endorsement) = {
        getVertex.pipe.outE(ENDORSEMENT)
            .has("endorsementId", endorsement.getId).count()
    }
    

    /**
     * Hapus semua endorsement yang didapat oleh user ini.
     * Ini hanya menghapus edge ENDORSEMENT-nya bukan
     * vertex Endorsement itu sendiri.
     * @param endorsement endorsement yang akan dihapus.
     */
    def removeEndorsement(endorsement:Endorsement) = {
        getVertex.pipe.outE(ENDORSEMENT)
            .has("endorsementId", endorsement.getId)
            .asInstanceOf[GremPipeEdge]
            .iterator().foreach(db.removeEdge)

        db.commit()
    }

    /**
     * Hapus endorser dari sebuah endorsement
     * @param user
     * @param endorsement
     */
    def removeEndorser(user:User, endorsement:Endorsement) = {
        getVertex.pipe.outE(ENDORSEMENT)
            .has("endorserId", user.getId)
            .has("endorsementId", endorsement.getId)
            .remove()

        db.commit()
    }

}
