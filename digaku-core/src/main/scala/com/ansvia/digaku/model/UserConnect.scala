/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.tinkerpop.blueprints.Vertex
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.dao.UserConnectDao
import com.ansvia.digaku.Types._
import com.ansvia.perf.PerfTiming

/**
 * Author: temon, robin
 * 
 */

/**
 * Untuk menyimpan id dari apps luar (ex: facebook, twitter dan lain2)
 * jika user melakukan connect
 *
 * @param uid id unik dari id user di apps luar
 * @param via dari mana user melakukan connect
 */
abstract class UserConnectBase[idT, dbT <: GraphType](uid:String, via:String) extends BaseModel[idT] with PerfTiming

case class UserConnect(var uid: String, var via:String) extends UserConnectBase[IDType, GraphType](uid, via) with DbAccess {
    /**
     * custom save routine, called when blueprint-scala
     * saving this record.
     * @return
     */
    override def __save__(v:Vertex) = {
        v.setProperty("user-connect.uid", uid)
        v.setProperty("user-connect.via", via)
    }

    override def toString: String = via
}

object UserConnect extends UserConnectDao[Long, GraphType]
