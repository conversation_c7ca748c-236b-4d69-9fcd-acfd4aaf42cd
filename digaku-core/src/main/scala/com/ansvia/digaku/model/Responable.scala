/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.util.concurrent.{Callable, TimeUnit}

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.database.GraphCompat._
import com.ansvia.digaku.event.impl.{DeleteResponseEvent, ResponseEvent}
import com.ansvia.digaku.event.{EventStreamListener, StreamEvent}
import com.ansvia.digaku.exc.{DigakuException, PermissionDeniedException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.utils.{Comparator, SortDirection}
import com.ansvia.graph.BlueprintsWrapper.IdDbObject
import com.ansvia.perf.PerfTiming
import com.google.common.cache.CacheBuilder
import com.tinkerpop.blueprints.Vertex
import com.tinkerpop.gremlin.Tokens.T
//import com.tinkerpop.pipes.util.structures.{Pair => GPair}

import scala.collection.JavaConversions._
import scala.util.Random
import com.ansvia.digaku.utils.ForumSettings._


/** $regression:$ Fungsional respon/comment.
 */

object ResponsePaginationMode {
    val STREAM = 0
    val PAGE_NUMBER = 1
}

/**
 * Semua model yang mixin dari trait ini akan bisa di-respon.
 */
trait Responable extends IdDbObject[IDType] with DbAccess with AbstractHasCreator with PerfTiming {

    this: Counter =>

    import com.ansvia.digaku.model.Label._
    import com.ansvia.digaku.model.Responable.responseCountKey
    import com.ansvia.graph.BlueprintsWrapper._

    private lazy val counter = getCounter

    /**
     * Add response to this object.
     * @see [[com.ansvia.digaku.model.Responable.addResponse(Response)]]
     * @param creator user who's create/write the response.
     * @param content response content.
     */
    def addResponse(creator : User, content : String):Response = {
        if (creator.isLocked)
            throw PermissionDeniedException("Anda tidak bisa memberikan reply, Anda masih berada dalam status Suspended.")

        this match {
            case article:Article =>
                if (article.isDeleted)
                    throw PermissionDeniedException("Anda tidak bisa reply post, Post ini telah dihapus.")

            case response:Response =>
                if (response.isDeleted) {
                    throw PermissionDeniedException("Anda tidak bisa reply post, Post ini telah dihapus.")
                }

                if (response.getRespondedObject.exists(_.isInstanceOf[Response])) {
                    throw PermissionDeniedException("Anda tidak bisa reply post, post reply hanya support sampai 2 level")
                }

            case _ =>
        }

        val forum = db.getVertex(getId).pipe.out(ORIGIN)
            .has("_class_", "com.ansvia.digaku.model.Forum")
            .asInstanceOf[GremPipeVertex]
            .headOption
            .flatMap(_.toCC[Forum])

        if (forum.isDefined) {
            val ch = forum.get.reload()
            if (ch.isDeleted) {
                throw PermissionDeniedException("You are not allowed to response any post on this group. " +
                    "Permission denied by group management")
            }
            if (!ch.userCanResponse_?(creator.reload())) {
                this match {
                    case movable:Movable =>
                        if (!(movable.isMoved && creator == this.creator)) {
                            if (ch.getWhoCanResponses.isEmpty) {
                                throw PermissionDeniedException("You are not allowed to response any post on this group. " +
                                    "Permission denied by group management")
                            } else {
                                throw PermissionDeniedException("you should be a %s to response"
                                    .format(ch.getWhoCanResponses.reduceLeftOption(_ + " or " + _).getOrElse("")))
                            }
                        }
                    case _ =>
                }
            }
        }

        // check for duplicates if any
        db.getVertex(getId).pipe.in(RESPONSE_OF)
            .as("resp")
            .in(RESPONSE_WITH)
            .in(COLLECTION_VERTEX)
            .has("name", creator.name)
            .back("resp").asInstanceOf[GremPipeVertex]
            .order(Comparator.timeOrdererDesc)
            .range(0, 2)
            .headOption foreach { lastRespV =>
                lastRespV.get[String]("content") foreach { responseContent =>
                    if (responseContent.trim.equalsIgnoreCase(content.trim))
                        throw new DigakuException("Possibly double response.")
                }
            }


        val response = Response.create(creator, content)

        creator.incrementReplyCount()

        if (this.creator.getId != creator.getId) {
            UserLeaderboard.incrementPoint(creator, 10)
        }

        db.commit()

        this.addResponse(response)
    }

    /**
     * Add response by response object.
     * @param response resp object.
     */
    def addResponse(response:Response): Response = {

        val postV = db.getVertex(getId)

        response.creator.reload()
        response.reload()

        val respV = db.getVertex(response.getId)


        val ed = respV --> RESPONSE_OF --> postV <()


        counter.increment(responseCountKey)

        val currentResponseCount = counter.get(responseCountKey)

        ed.setProperty("count", currentResponseCount.toInt)
        postV.setProperty("responseCount", currentResponseCount.toInt)

        // set last response id
        // ini untuk optimasi mendapatkan response terakhir
        postV.setProperty("lastResponseId", respV.getId)

        // update last update time
        postV.setProperty("lastUpdatedTime", Digaku.engine.dateUtils.nowMilis)

        db.commit()

        if (this.creator.getId != response.creator.getId) {
            UserLeaderboard.incrementPoint(this.creator, 4)
        }

        Digaku.engine.eventStream.emit(ResponseEvent(response.creator, response, this))

        // invalidate caches
        Responable.getRespondersCache.invalidate(this.getId.toString)
        Responable.isResponderCache.invalidate(this.getId.toString + "-" + response.creator.getId)

        response
    }

    /**
     * get for all response
     * @param skip skip
     * @param limit limit
     * @return
     */
    def getResponses(skip : Int, limit : Int):Seq[Response] = {
        getVertex.pipe.in(RESPONSE_OF)
            .order(Comparator.timeOrdererAsc)
            .range(skip, skip + (limit - 1))
            .iterator()
            .flatMap(_.toCC[Response])
            .toSeq
    }

    /**
     * get for all response mode stream
     * contoh penggunaan di com.ansvia.mindtalk.snippet.ResponseSnippet#page
     *
     * @param sinceId
     * @param maxId
     * @param limit
     * @param orderBy bisa asc atau desc (default) bisa lihat di mari [[com.ansvia.digaku.utils.SortDirection]]
     * @param inclusive boolean true (default), jika inclusive response yg mempunyai maxId/sinceId dimasukan
     * @param deleted boolean jika true tampilkan soft deleted
     * @return
     */
    def getResponses(sinceId:Option[IDType]=None, maxId:Option[IDType]=None, limit : Int,
                     orderBy:SortDirection = SortDirection.DESC, inclusive:Boolean = false, deleted : Boolean = true):Seq[Response] = {

        var pipe = getVertex.pipe.inE(RESPONSE_OF)

        sinceId map { _sinceId =>
            if (inclusive)
                pipe = pipe.as("ed").outV().has("id", T.gte, _sinceId).back("ed").asInstanceOf[GremPipeEdge]
            else
                pipe = pipe.as("ed").outV().has("id", T.gt, _sinceId).back("ed").asInstanceOf[GremPipeEdge]
        }

        maxId map { _maxId =>
            if (inclusive)
                pipe = pipe.as("ed").outV().has("id", T.lte, _maxId).back("ed").asInstanceOf[GremPipeEdge]
            else
                pipe = pipe.as("ed").outV().has("id", T.lt, _maxId).back("ed").asInstanceOf[GremPipeEdge]
        }

        if (orderBy == SortDirection.ASC) {
            pipe.outV().order(Comparator.timeOrdererAsc)
                .range(0, (limit - 1))
                .iterator()
                .flatMap(_.toCC[Response])
                .toSeq
        } else {
            pipe.outV().order(Comparator.timeOrdererDesc)
                .range(0, (limit - 1))
                .iterator()
                .flatMap(_.toCC[Response])
                .toSeq
        }
    }

    /**
     * cek apakah user merupakan responder atau tidak
     * @param user
     * @return
     */
    def isResponder(user:User) = {
        Responable.isResponder(user, this)
    }


    /**
     * Mendapatkan daftar responders (user-user yang merespon object ini).
     * @param offset starting offset.
     * @param limit ends limit.
     * @return user siapa aja yang nge-response.
     */
    def getResponders(offset:Int, limit:Int): Seq[User] = {
        timing(s"Responable($id).getResponders"){
            Responable.getResponders(this, offset, limit)
        }
    }


    /**
     * digunakan untuk mengetahui urutan atau index response yang ke berapa pada suatu post
     * @param response resp object
     * @return
     */
    def getIndexOfResponse(response: Response):Int = tx { t =>
        t.getVertex(getId).pipe
            .in(RESPONSE_OF)
            .order(Comparator.timeOrdererAsc)
            .indexOf(response.getVertex)
    }

    /**
     * Hapus response berdasarkan object response-nya.
     * @see [[com.ansvia.digaku.model.Responable.removeResponse(Long)]]
     * @param resp response to remove.
     */
    def removeResponse(resp:Response){
        Responable.getRespondersCache.invalidate(this.getId.toString)
        Responable.isResponderCache.invalidate(this.getId.toString + "-" + resp.creator.getId)
        removeResponse(resp.getId)
    }


    /**
     * remove rospose berdasarkan id
     * ini akan menghapus vertex dan semua edges yang terhubung
     * ke vertex ini baik in maupun out.
     * @param id id respose
     */
    def removeResponse(id: Long){
        //        transact
        //        {
        //            Response.deleteById(id)

        val respV = db.getVertex(id)

        // remove out and in edges
        respV.pipe.bothE().remove()

        // remove vertex it self
        db.removeVertex(respV)

        val postV = db.getVertex(getVertex.getId)

        // @TODO(robin): remove this backward compatibility
        // safe to remove after 02 Nov 2015
        // begin backward compatibility ---------------------------
        if (counter.get(responseCountKey) == 0L && postV.getOrElse("responseCount", 0) > 0){
            counter.incrementBy(responseCountKey, postV.getOrElse("responseCount", 0).toLong)
        }
        // end of backward compatibility ---------------------------

        counter.decrement(responseCountKey)

        //            responseCount = this.getVertex.getOrElse("responseCount", responseCount) - 1
        postV.setProperty("responseCount", counter.get(responseCountKey).toInt)

        // fix last response id
        Responable.fixLastResponseId(postV)

        // update last update time
        postV.setProperty("lastUpdatedTime", Digaku.engine.dateUtils.nowMilis)

        //            // update lastResponseId
        //            var lastResponseId = 0L
        //            var offset = 0
        //
        //            while (getVertex.pipe.in(RESPONSE_OF).hasNot("deleted",true).range(offset,offset+1).iterator().hasNext){
        //                getVertex.pipe.in(RESPONSE_OF).hasNot("deleted",true).range(offset,offset+100).iterator().foreach { v =>
        //
        ////                    println("resp: (" + v.getId + ") " + v.getOrElse("content", ""))
        //                    lastResponseId = v.getId.asInstanceOf[Long]
        //
        //                }
        //                offset = offset + 100
        //            }
        //
        //            if (lastResponseId > 0)
        //                getVertex.setProperty("lastResponseId", lastResponseId)
        //        }
        val replyCount = this.creator.decrementReplyCount()
        val v = creator.getVertex
        v.setProperty("userReplyCount", replyCount)
        db.commit()



        Responable.getRespondersCache.invalidate(this.getId.toString)
        Responable.isResponderCache.invalidateAll()
    }

    /**
     * digunakan untuk mendapatkan jumlah response
     * @return
     */
    def getResponseCount:Int = {
        counter.get(responseCountKey).toInt
    }

    /**
     * Mendapatkan response terakhir.
     * ini akan mencoba mendapatkan response terakhir secara optimal
     * dengan kompleksitas konstan O(1) berdasarkan property `lastResponseId`
     * apabila tidak ditemukan dan untuk alasan backward compatibility
     * akan coba mendapatkan dari post pertama
     * menggunakan `getResponses` hal ini dilakukan agar tidak terjadi error.
     * @return
     */
    def getLastResponse: Option[Response] = {
        import com.ansvia.graph.gremlin._

        // pertama coba dapetin dari property `lastResponseId`-nya dulu
        val attempt1 = this.getVertex.get[IDType]("lastResponseId").flatMap { respId =>
            Response.getById(respId).filterNot(_.deleted)
        }

        if (attempt1.isDefined){
            attempt1
        }else{
            // backward compatibility
            // dengan dapetin response pertama biar gak error
            //            getResponses(0,1).headOption

            // fixup lastResponseId property
            // dengan cara iterasi ke seluruh response-nya dan meng-update
            // property lastResponseId
            var _lastResponseId = 0L

            getVertex.pipe.in(RESPONSE_OF)
                .hasNot("deleted", true)
                .asInstanceOf[GremPipeVertex]
                .sideEffect { (a:Vertex) =>
                _lastResponseId = a.getId.asInstanceOf[IDType]
            }.iterate()

            if (_lastResponseId > 0){
                getVertex.setProperty("lastResponseId", _lastResponseId)
                db.commit()
                reload()
                Response.getById(_lastResponseId)
            }else{
                None
            }
        }
    }


    def incrementResponseCount(by:Int=1){
        counter.incrementBy(responseCountKey, by)
    }

    override def __save__(vertex: Vertex): Unit = {
        super.__save__(vertex)

        // masih tetap perlu ini untuk operasi OLAP seperti mendapatkan popular pictures
        vertex.setProperty("responseCount", getResponseCount)
    }
}


object Responable extends DbAccess {
    import com.ansvia.graph.BlueprintsWrapper._

    val responseCountKey = "response_count"

    registerEventStream()

    def getById(id:IDType):Option[Responable] = {
        val v = db.getVertex(id)
        if (v != null){
            v.toCC[Responable]
        }else{
            None
        }
    }

    private object DeleteListener extends EventStreamListener {
        /**
         * Check is event handled by this listener.
         * WARNING: don't use `transact` inside this method
         * to avoid dead-lock.
         * @param eventName name of event.
         * @return
         */
        def isEventHandled(eventName: String): Boolean = eventName == "delete-response"

        def dispatch: PartialFunction[StreamEvent, Unit] = {
            case DeleteResponseEvent(deleter, response) => response.getRespondedObject.foreach(fixLastResponseId)
        }

        //            val actorName: String = "response-delete-handler"
    }

    private def registerEventStream(){

        /**
         * Berguna untuk menghandle apabila ada event delete.
         */
        Digaku.engine.eventStream.addListeners(DeleteListener)
    }

    protected def fixLastResponseId(responable:Responable) {
        fixLastResponseId(responable.getVertex)
    }



    protected def fixLastResponseId(responableV:Vertex) {
        import com.ansvia.digaku.model.Label.RESPONSE_OF
        import com.ansvia.graph.gremlin._

        // update referensi ke lastResponseId
        // yang menunjukkan response terakhir
        // apabila response ini dihapus maka posisi lastResponseId harus berubah.

        //                    this match {
        //                        case response:Response => {

        // update lastResponseId
        //        responal.getRespondedObject.map { obj =>
        //                                var lastResponseId = 0L
        //                                var offset = 0

        var lastResponseId = 0L

        responableV.pipe.in(RESPONSE_OF)
            .hasNot("deleted",true).asInstanceOf[GremPipeVertex]
            .transform { (v:Vertex) =>
                lastResponseId = v.getId.asInstanceOf[Long]
            }
            .iterate()

        if (lastResponseId > 0){
            responableV.setProperty("lastResponseId", lastResponseId)
        }

        //
        //                                while (obj.getVertex.pipe.in(RESPONSE_OF).hasNot("deleted",true).range(offset,offset+1).iterator().hasNext){
        //                                    obj.getVertex.pipe.in(RESPONSE_OF).hasNot("deleted",true).range(offset,offset+100).iterator().foreach { v =>
        //
        //                                    //                                println("resp: (" + v.getId + ") " + v.getOrElse("content", ""))
        //                                        lastResponseId = v.getId.asInstanceOf[Long]
        //
        //                                    }
        //                                    offset = offset + 100
        //                                }
        //
        //                                if (lastResponseId > 0)
        //                                    obj.getVertex.setProperty("lastResponseId", lastResponseId)
        //        }
        //                        }
        //                        case _ =>
        //                    }
    }


    import com.ansvia.digaku.model.Label._
    import com.ansvia.graph.BlueprintsWrapper._

    import scala.collection.JavaConversions._

    lazy val getRespondersCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build[String,Seq[User]]()

    lazy val isResponderCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build[String,Option[Boolean]]()



    def getResponders(r:Responable, offset:Int, limit:Int): Seq[User] = {
        getRespondersCache.get(r.getId.toString, new Callable[Seq[User]]{
            def call(): Seq[User] = {
                r.getVertex.pipe.in(RESPONSE_OF)
                    .hasNot("deleted", true)
                    .in(RESPONSE_WITH)
                    .in(COLLECTION_VERTEX)
                    .dedup().range(offset, offset + limit - 1)
                    .iterator().flatMap(_.toCC[User]).toSeq
            }
        })
    }


    def isResponder(u:User, r:Responable): Boolean = {
        isResponderCache.get(r.getId.toString + "-" + u.getId.toString, new Callable[Option[Boolean]]{
            def call(): Option[Boolean] = {
                Some(r.getVertex.pipe.in(RESPONSE_OF)
                    .in(RESPONSE_WITH)
                    .in(COLLECTION_VERTEX)
                    .has("id", u.getId)
                    .hasNext)
            }
        }).get
    }



}
/*$regression:end$*/
