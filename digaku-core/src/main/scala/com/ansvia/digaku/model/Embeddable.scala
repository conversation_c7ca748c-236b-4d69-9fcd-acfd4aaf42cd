/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.tinkerpop.blueprints.Vertex
import com.ansvia.digaku.Types._
import scala.collection.JavaConversions._
import scala.reflect.ClassTag


/**
 * Author: robin
 *
 * Embeddable trait, semua model yang diturunkan dari sini
 * akan otomatis bisa di-embed-kan ke HasEmbeddable object.
 * @see [[com.ansvia.digaku.model.HasEmbeddedObject]]
 */
trait Embeddable[idT] {
    this : BaseModel[IDType] =>

    import com.ansvia.graph.BlueprintsWrapper._
    import com.ansvia.digaku.model.Label.EMBED

    implicit protected def db:GraphType

    def getId:idT
    def getVertex:Vertex
    def getEmbedParentObject[modelT <: HasEmbeddedObject[idT] : ClassTag]:Option[modelT] = {
        getVertex.pipe.in(EMBED).headOption.flatMap(_.toCC[modelT])
    }
}
