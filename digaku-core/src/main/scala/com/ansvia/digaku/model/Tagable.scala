/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.graph.annotation.Persistent

/**
 * Author: robin
 *
 */
trait Tagable {

    @Persistent var tags:String=""


    // get tags in array
    def tagsArray:Array[String] = {
        if(tags == null)
            Array[String]()
        else
            tags.split(",").map(_.trim)
    }

    def setTags(tags:String): Unit = {
        val normTags = tags.split(",").map(_.toLowerCase)
            .filter(t => t.length > 1 && t.length < 50).distinct
            .slice(0, 4).reduceLeftOption(_ + "," + _).getOrElse("")
        this.tags = normTags
    }

}
