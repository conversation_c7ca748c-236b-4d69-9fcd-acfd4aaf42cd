/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types._
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.graph.BlueprintsWrapper.IdDbObject

/**
 * Author: robin
 *
 */
trait Promotable extends IdDbObject[IDType] with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    var promoted = false

    def getId:IDType

    /**
     * Check apakah sebuah content di featured atau tidak
     * @return
     */
    def isPromoted:Boolean = {
        this.getVertex.pipe.outE(HAS_PROMOTED).hasNext
    }

    /**
     * Check apakah sebuah featured post ditampilkan kepada user atau tidak
     * @return
     */
    def isDisplayed:Boolean = {
        this.getVertex.pipe.outE(HAS_PROMOTED).has("displayed", true).hasNext
    }
}
