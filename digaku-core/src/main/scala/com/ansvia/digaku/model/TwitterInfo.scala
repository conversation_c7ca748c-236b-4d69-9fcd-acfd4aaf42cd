///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Types._
//import scala.collection.JavaConversions._
//import com.ansvia.digaku.Digaku
//import com.tinkerpop.blueprints.Vertex
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.event.impl.ConnectedFriendsTwEvent
//import com.ansvia.digaku.exc.DigakuException
//import com.ansvia.graph.IdGraphTitanDbWrapper._
//
///**
// * Author: nadir
// * Date: 11/27/13
// * Time: 10:53 AM
// *
// */
//
//case class TwitterInfo(twId:String) extends ThirdPartyAccount {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//    import Label._
//
//    /**
//     * digunakan untuk mendapatkan user yang connect ke twitter info ini
//     * @return
//     */
//    def getUserConnectTw = {
//        this.getVertex.pipe.in(CONNECT_TW).headOption.flatMap(_.toCC[User])
//    }
//
//    /**
//     * menambahkan teman atau orang yang di-follow dari twitter info ini
//     * @param twitterInfos
//     */
//    def addFriends(twitterInfos:TwitterInfo*) {
////        transact {
//            for (twitterInfo <- twitterInfos; if (!isFriend(twitterInfo))){
//                this --> FOLLOW --> twitterInfo
//            }
////        }
//        db.commit()
//
//        getUserConnectTw.map { user =>
//            Digaku.engine.eventStream.emit(ConnectedFriendsTwEvent(user))
//        }
//    }
//
//    /**
//     * cek apakah sudah menjadi teman atau di follow dari twitter info ini
//     * @param twitterInfo
//     * @return
//     */
//    def isFriend(twitterInfo:TwitterInfo):Boolean = {
//        this.getVertex.pipe.out(FOLLOW).has("twId", twitterInfo.twId).hasNext
//    }
//
//    /**
//     * cek apakah twitter info ini sudah di pakai oleh user atau belum
//     * @return
//     */
//    def isUsed:Boolean = {
//        getUserConnectTw.isDefined
//    }
//
//    override def __save__(v:Vertex) = {
//        v.setProperty("tw.id", twId)
//    }
//}
//
//object TwitterInfo extends DaoBase[GraphType, TwitterInfo] {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.TwitterInfoDaoRootVertex"
//
//    /**
//     * digunakan untuk create TwitterInfo
//     * akan selalu mengembalikan TwitterInfo meskipun create twitter info yang sudah ada (tidak throw kan dan
//     * tidak di create hanya mengembalikan TwitterInfo yang sudah ada
//     * @param twId
//     * @return
//     */
//    def create(twId:String): TwitterInfo = {
//        if (!existsByTwId(twId)) {
//            val twitterInfo = TwitterInfo(twId)
//
//            try {
//                //                transact {
//
//
//                val vertex = twitterInfo.saveWithLabel(VertexLabels.THIRDPARTY_ACCOUNT)
//
//                addToRoot(vertex)
//
//                val newTwitterInfo = vertex.toCC[TwitterInfo].getOrElse {
//                    throw new DigakuException("Cannot create twitterInfo : " + twitterInfo)
//                }
//
//
//                //                }
//                db.commit()
//
//                newTwitterInfo
//            } catch{
//                case e:Exception =>
//                    throw new DigakuException("Cannot create twitterInfo " + twId)
//            }
//        } else {
//            getByTwid(twId).getOrElse(throw new DigakuException("Cannot getting TwitterInfo with id: " + twId))
//        }
//    }
//
//    /**
//     * get TwitterInfo berdasarkan twitter id nya
//     * @param twId Twitter ID.
//     * @return
//     */
//    def getByTwid(twId: String): Option[TwitterInfo] = {
//
//        db.query().has("label", VertexLabels.THIRDPARTY_ACCOUNT)
//            .has("tw.id", twId)
//            .vertices()
//            .headOption.flatMap(_.toCC[TwitterInfo])
//
//        //        db.getVertices("tw.id", twId).headOption.flatMap(_.toCC[TwitterInfo])
//    }
//
//    /**
//     * cek apakah TwitterInfo sudah ada dengan twId di parameter
//     * @param twId
//     * @return
//     */
//    def existsByTwId(twId: String): Boolean = {
//        getByTwid(twId).isDefined
//    }
//
//}
//
