/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.persistence.CounterProviderOperator

/**
 * Author: robin
 *
 *
 */

/**
 * Semua model yang mixin trait ini
 * harus provide counter provider.
 */
trait Counter {

    /**
     * Get counter provider used by this model.
     * @return
     */
    def getCounter:CounterProviderOperator

}
