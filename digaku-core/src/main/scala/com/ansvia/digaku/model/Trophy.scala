/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.event.impl.UserHasTrophyEvent
import com.ansvia.digaku.exc.{AlreadyExistsException, DigakuException, InvalidParameterException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.utils.DateUtils
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.graph.IdGraphTitanDbWrapper._
import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.Vertex
import com.ansvia.digaku.helpers.TypeHelpers._
import scala.collection.JavaConversions._

/**
 * Author: nadir, robin
 *
 */

/**
 * Base trophy abstract class, semua jenis trophy harus diturunkan dari sini.
 * @param trophyName nama trophy.
 * @param description penjelasan tentang trophy.
 */
abstract class Trophy(trophyName:String, description:String)
    extends BaseModel[IDType] with DbAccess {

    var abilities = Array.empty[String]
    @Persistent var lastSaved = 0L

    @Persistent var icons = ""

    /**
     * Penanda sebuah trophy bisa otomatis di-assign oleh sistem.
     * true ketika trophy bisa otomatis di-assign ke user oleh system
     * dengan kalkulasi tertentu.
     * ex : see [[com.ansvia.digaku.model.GeneralTrophy#canReceive(com.ansvia.digaku.model.User)]]
     */
    @Persistent var autoAssign = false

    /**
     * group dari sebuah trophy see[[com.ansvia.digaku.model.TrophyGroup]]
     */
    @Persistent var trophyGroup = ""

    /**
     * When true then this trophy is not suitable for use.
     */
    @Persistent var inactive = false


    /**
     * untuk replace trophies tertentu ketika mendapatakan trophy tertentu
     * Array di-isi id trophy yang akan di replace
     */
    var replaced = Array.empty[String]

    /**
     * get trophy name
     * @return
     */
    def getName = trophyName


    /**
     * Get throphy description.
     * @return
     */
    def getDescription = description

    /**
     * implement ini supaya bisa set trophy name bisa dari model Trophy
     * @param trophyName
     */
    def setTrophyName(trophyName:String)

    def setDescription(description:String)

    /**
     * digunakan untuk check apakah user dapat menerima trophy ini atau tidak
     * true ketika bisa menerima trophy
     * @param user user yang akan ditest apakah dapat menerima.
     * @return
     */
    def canReceive(user:User):Boolean


    /**
     * Daftarkan nama-nama trophy yang akan di-replace/remove
     * ketika trophy ini di-assign ke user.
     * @param trophyIds nama trophy yang akan direplace.
     * @return
     */
    def withReplaceableTrophies(trophyIds:Array[String]) = {
        replaced = trophyIds
        this
    }


    /**
     * Tambahkan trophy ini ke user.
     * Berbeda dengan `giveTo` ini tidak menggunakan validator `canReceive`
     * gunakan `giveTo` apabila ingin di-wrap menggunakan validator `canReceive`.
     * @param user user yang akan ditambahkan trophy ini.
     * @param giver user yang memberi trophy ini, optional by default None.
     * @param noTx set true apabila tidak ingin automatic commit/transaction.
     * @param noEmitEvent set true apabila tidak ingin event stream di-trigger.
     */
    def addTrophy(user: User, giver:Option[User]=None, noTx:Boolean=false, noEmitEvent:Boolean=false) = {

//        val ed = user.reload() --> HAS_TROPHY --> this.reload() <()
        val ed = user.reload().getVertex.addEdge(HAS_TROPHY, this.reload().getVertex)

        ed.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)

        // set giver apabila ada giver-nya
        giver.map(_u => ed.setProperty("giverUserId", _u.getId))

        if (!noTx)
            db.commit()

        // apabila ada yang bisa di-replace maka hapuskan (seakan di-replace).
        replaced.flatMap(x => Trophy.getById(x.toLongOr(0L))).foreach { tr =>
            tr.removeTrophyFromUser(user)
        }

        if (!noEmitEvent)
            Digaku.engine.eventStream.emit(UserHasTrophyEvent(user, this.asInstanceOf[Trophy]))

        db.getEdge(ed.getId)
    }

    /**
     * digunakan untuk menambahkan trophy ke user
     * @param user
     */
    def giveTo(user:User, giver:Option[User]=None) {

        if (this.reload().canReceive(user) && !userHasTrophy(user)) {
            addTrophy(user, giver)
        }

    }

    /**
     * digunakan untuk menambahkan trophy ke user
     * @param user
     */
    def forceGiveTo(user:User, giver:Option[User]=None) {

        if (!userHasTrophy(user)) {
            addTrophy(user, giver)
        }

    }

    /**
     * digunakan untuk update sebuah trophy pada user
     * @param user
     */
    def updateTrophyState(user:User, noEmitEvent:Boolean=false) {
        if (this.reload().canReceive(user)) {
            if (!userHasTrophy(user)) {
                addTrophy(user, None, noEmitEvent=noEmitEvent)
            }
        } else {
            removeTrophyFromUser(user)
        }
    }

    /**
     * set replaced trophy
     * @param replaced
     */
    def setReplaced(replaced:Array[String]) {
        this.replaced = replaced
        this.save()

        db.commit()
    }

    /**
     * untuk cek apakah user sudah mendapatkan trophy object ini
     * @param user
     * @return
     */
    def userHasTrophy(user:User): Boolean = {
        user.getVertex.pipe.out(HAS_TROPHY)
            .has("id", this.getId).count() > 0
    }

    /**
     * remove trophy dari user
     * @param user
     */
    def removeTrophyFromUser(user:User) {
        user.getVertex.pipe.outE(HAS_TROPHY).as("edge").inV()
            .has("id", this.getId).back("edge")
            .remove()
        db.commit()
    }

    override def __save__(v:Vertex) = {
        if (creationTime == 0L){
            v.setProperty("creationTime", System.currentTimeMillis())
        }
        v.setProperty("lastSaved", System.currentTimeMillis())
        v.setProperty("trophy.abilities", abilities.map(_.trim.toLowerCase).distinct.mkString(","))
        v.setProperty("trophy.replaced", replaced.map(_.trim.toLowerCase).distinct.mkString(","))

        if (v.getProperty[String]("trophy.name") != trophyName.trim.toLowerCase)
            v.setProperty("trophy.name", trophyName.trim.toLowerCase)
    }

    override def __load__(vertex: Vertex) {
        super.__load__(vertex)

        abilities = vertex.getOrElse("trophy.abilities", "").split(",").array
        replaced = vertex.getOrElse("trophy.replaced", "").split(",").array

    }
}




object Trophy extends DaoBase[GraphType, Trophy] {


    val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.TrophyDaoRootVertex"

    /**
     * Create general trophy.
     * @param trophyName trophy name
     * @param description description trophy
     * @return
     */
    def createGeneral(trophyName:String, description:String,
                      iconsUrl:String, trophyGroup:String):GeneralTrophy = {

        if (trophyName.length < 2)
            throw InvalidParameterException("Min thropy name length 3 characters")

        if (trophyName.length > 20)
            throw InvalidParameterException("Max thropy name length 20 characters")

        if (isExistByName(trophyName))
            throw AlreadyExistsException("trophy name already exists: " + trophyName)

        if (iconsUrl.trim.isEmpty)
            throw InvalidParameterException("please insert icons")

        var trophy:GeneralTrophy= null

        val newTrophy = GeneralTrophy(trophyName, description)
        newTrophy.icons = iconsUrl

        trophy = newTrophy.saveWithLabel(VertexLabels.TROPHY).toCC[GeneralTrophy]
            .getOrElse { throw new DigakuException("cannot create Trophy, persistent error?") }

        addToRoot(trophy.getVertex)


        db.commit()

        trophy
    }

    /**
     * get trophy by name
     * @param name trophy name to get.
     * @return
     */
    def getByName(name:String) :Option[Trophy] = {

        db.query().has("label", VertexLabels.TROPHY)
            .has("trophy.name", name.toLowerCase)
            .vertices()
            .headOption.flatMap(_.toCC[Trophy])
    }

    /**
     * Dapatkan semua daftar trophy.
     * @return
     */
    def getList:Iterator[Trophy] = {
//        rootVertex.pipe.out(rootVertexLabel).iterator().flatMap(_.toCC[Trophy])
        getListRight(None, None, 100)
    }

    /**
     * get semua trophy yang automatic assignment
     * @see [[com.ansvia.digaku.model.Trophy.autoAssign]]
     * @return
     */
    def getTrophyWithAutoAssign:Iterator[Trophy] = {
        rootVertex.pipe.out(rootVertexLabel).has("autoAssign", true)
            .asInstanceOf[GremPipeVertex]
            .iterator().flatMap(_.toCC[Trophy])
    }

    /**
     * cek apakah trophy sudah ada atau belum
     * @param trophyName
     * @return
     */
    def isExistByName(trophyName:String):Boolean = {
//        getByName(trophyName).isDefined
        db.query().has("label", VertexLabels.TROPHY).has("trophy.name", trophyName.toLowerCase).limit(1)
            .vertices().iterator().hasNext

    }

}

object TrophyGroup {
    val PERSONAL = "personal"
    val GROUP = "group"
    val ARTICLE = "article"
    val LEVEL = "level"
    val COMMUNITY = "community"
    val OTHER = "other"

    def getTrophyGroups = {
        Seq(PERSONAL, GROUP, ARTICLE, LEVEL,COMMUNITY, OTHER)
    }

}
