/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

/**
 * Author: robin
 *
 */
object PostKind {
    val OTHER = 0
    val ANY = 0
    val SIMPLE_POST = 1
    val ARTICLE = 2
    val QUESTION = 3
    val DEAL = 4
    val PICTURE = 5
    val EVENT = 6
    val SPONSOR = 7
    val RETALKED = 8

    /**
     * convert class model post kind to int post kind.
     * ex: kindStrToInt("com.ansvia.digaku.model.Article") will produce [[com.ansvia.digaku.model.PostKind.ARTICLE]]
     * @param kindStrClassRef
     * @return
     */
    def kindStrClassToInt(kindStrClassRef:String) = {
        kindStrClassRef match {
            case "com.ansvia.digaku.model.Post" => PostKind.OTHER
            case "com.ansvia.digaku.model.Article" => PostKind.ARTICLE
            case "com.ansvia.digaku.model.SimplePost" => PostKind.SIMPLE_POST
            case "com.ansvia.digaku.model.Question" => PostKind.QUESTION
            case "com.ansvia.digaku.model.Deal" => PostKind.DEAL
            case "com.ansvia.digaku.model.Picture" | "com.ansvia.digaku.model.PictureGroup" => PostKind.PICTURE
            case "com.ansvia.digaku.model.Event" => PostKind.EVENT
            case "com.ansvia.digaku.model.SponsorPost" => PostKind.SPONSOR
            case "com.ansvia.digaku.model.RetalkWrapper" => PostKind.RETALKED
        }
    }

    /**
     * Sama seperti kindStrClassToInt hanya saja
     * bukan class tapi sama dengan yang di model
     * misalnya SimplePost.
     * @param kindStr
     * @return
     */
    def kindStrToInt(kindStr:String) = {
        kindStr match {
            case "Article" => PostKind.ARTICLE
            case "SimplePost" => PostKind.SIMPLE_POST
            case "Question" => PostKind.QUESTION
            case "Deal" => PostKind.DEAL
            case "Picture" => PostKind.PICTURE
            case "Event" => PostKind.EVENT
            case "SponsorPost" => PostKind.SPONSOR
            case "RetalkWrapper" => PostKind.RETALKED
            case _ => PostKind.OTHER
        }
    }

    /**
     * Convert dari kind int str.
     * misalnya SimplePost.
     * @param kindInt
     * @return
     */
    def kindIntToStr(kindInt:Int) = {
        kindInt match {
            case PostKind.ARTICLE => "Article"
            case PostKind.SIMPLE_POST => "SimplePost"
            case PostKind.QUESTION => "Question"
            case PostKind.DEAL => "Deal"
            case PostKind.PICTURE => "Picture"
            case PostKind.EVENT => "Event"
            case PostKind.SPONSOR => "SponsorPost"
            case PostKind.RETALKED => "RetalkWrapper"
        }
    }

    def kindIntToClassStr(code:Int) = {
        code match {
            case SIMPLE_POST => "com.ansvia.digaku.model.SimplePost"
            case ARTICLE => "com.ansvia.digaku.model.Article"
            case QUESTION => "com.ansvia.digaku.model.Question"
            case DEAL => "com.ansvia.digaku.model.Deal"
            case PICTURE => "com.ansvia.digaku.model.Picture"
            case EVENT => "com.ansvia.digaku.model.Event"
            case SPONSOR => "com.ansvia.digaku.model.SponsorPost"
            case OTHER => "com.ansvia.digaku.model.Post"
            case RETALKED => "com.ansvia.digaku.model.RetalkWrapper"
        }
    }



}
