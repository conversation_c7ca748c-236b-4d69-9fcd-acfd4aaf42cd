/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.tinkerpop.blueprints.Graph
import com.ansvia.graph.BlueprintsWrapper.DbObject
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.database.GraphCompat._


trait HasOriginAbstract[dbT <: Graph] {
    val origin: Origin[dbT]
    def originIsPrivate:Boolean
    def originIsDeleted:Boolean
    def setOrigin(origin: Origin[dbT], noTx:Boolean)
}

trait HasOrigin[dbT <: Graph] extends DbObject with DbAccess with HasOriginAbstract[dbT] {

    import com.ansvia.digaku.model.Label.ORIGIN
    import com.ansvia.graph.BlueprintsWrapper._

    val origin: Origin[dbT]

    /**
     * Check apakah originnya private.
     * @return
     */
    def originIsPrivate:Boolean = {
        origin.privated
    }

    /**
     * Check apakah originnya di delete
     * @return
     */
    def originIsDeleted:Boolean = {
        origin match {
            case deletable:Deletable =>
                deletable.isDeleted
            case _ =>
                false
        }
    }

    /**
     * Set origin for current object.
     * @param origin target origin.
     */
    def setOrigin(origin: Origin[dbT], noTx:Boolean=false) {

        tx { t =>
            val v = reload().getVertex

            // hapus origin sebelumnya jika sudah ada
            v.pipe.outE(ORIGIN).remove()

            v --> ORIGIN --> origin.reload().getVertex

//            v.addEdge(ORIGIN, origin.reload().getVertex)

        }

        if (!noTx)
            db.commit()

    }
}

