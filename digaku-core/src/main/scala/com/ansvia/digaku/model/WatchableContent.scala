/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.graph.AbstractDbObject
import com.ansvia.digaku.helpers.DbAccess
import Label._
import com.ansvia.graph.BlueprintsWrapper._

/**
 * Author: ubai
 *
 */
trait WatchableContent extends AbstractDbObject with DbAccess {

    /**
     * digunakan untuk unwatch/leave conversation dari sebuah content
     * @param user
     * @param noTx
     */
    def unwatchContent(user:User, noTx:Boolean=false) = {
        if (!isUnwatchContent(user)) {

            user --> UNWATCH --> this

            if (!noTx)
                db.commit()
        }
    }

    /**
     * digunakan untuk watch/kembali ke conversation sebuah content
     * @param user
     * @param noTx
     */
    def watchContent(user:User, noTx:Boolean=false) = {
        getVertex.pipe.inE(UNWATCH).as("x").outV().has("id", user.getId).back("x").remove()

        if (!noTx)
            db.commit()
    }

    /**
     * cek apakah user unwatch/leave conversation ?
     * true jika sudah leave
     * @param user
     * @return
     */
    def isUnwatchContent(user:User) = {
        reload()
        getVertex.pipe.in(UNWATCH).has("id", user.getId).hasNext
    }

    /**
     * cek apakah user bisa unwatch sebuah content ?
     * true jika bisa
     * @param user
     * @return
     */
    def userCanUnwatch(user:User) = {
        !isUnwatchContent(user) && {
                this match {
                    case post:Post =>
                        post.isResponder(user) || post.creator == user
                    case picture:Picture =>
                        picture.isResponder(user) || picture.creator == user
//                    case pictureGroup:PictureGroup =>
//                        pictureGroup.isResponder(user) || pictureGroup.creator == user
//                    case event:Event =>
//                        event.isResponder(user)
                    case _ =>
                        false

                }
        }
    }


}
