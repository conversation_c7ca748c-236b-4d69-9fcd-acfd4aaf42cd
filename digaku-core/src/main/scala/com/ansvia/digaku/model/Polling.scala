/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types._
import com.ansvia.graph.gremlin._
import com.ansvia.digaku.dao.PollingDao
import com.ansvia.digaku.exc.{InvalidParameterException, AlreadyExistsException, DigakuException, NotExistsException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.model.Polling.chooserText
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.{Vertex, Edge}
import com.tinkerpop.gremlin.java.GremlinPipeline
import org.joda.time.DateTime
import com.ansvia.digaku.helpers.GremlinHelpers._
import scala.collection.JavaConversions._

/**
 * Author: robin
 *
 */
case class Polling(title:String, desc:String)
    extends BaseModel[IDType] with Embeddable[IDType]
    with HasOrigin[GraphType] with Deletable with Closable with DbAccess {


    /**
     * Expiration dalam bentuk timestamp untuk menandai
     * kapan sebuah polling akan expired.
     * Apabila diisi 0 maka polling ini tidak akan pernah expired.
     */
    @Persistent var expired:Long = 0L

    /**
     * get origin polling
     */
    lazy val origin:Origin[GraphType] = {
        reload()
//        val v = this.getVertex.pipe.outFirst(ORIGIN)
        val vO = this.getVertex.pipe.out(ORIGIN).headOption
        if (vO.isDefined){
            try {
                val rv = vO.get.toCC[Forum].getOrElse(vO.get.toCC[User].orNull)
                if (rv!=null) {
                    rv.asInstanceOf[Origin[GraphType]]
                }else {
                    null
                }
            }catch{
                case e:NullPointerException =>
                    error("no origin for poll %s".format(getId))
                    null
            }
        }else{
            error("no origin for poll %s".format(this))
            null
        }
    }

    /**
     * Set kapan polling ini akan expired.
     *
     * @param expired time to expire in timestamp, apabila diisi 0 maka polling ini tidak akan pernah expire
     */
    def setExpiration(expired:Long){
        this.expired = expired
        this.save()
    }

    /**
     * Check apakah polling ini telah expired atau belum.
     * @return bool
     */
    def isExpired = {
        if (this.expired == 0){
            false
        }else{
            val dt = new DateTime()
            dt.getMillis > this.expired
        }
    }

    /**
     * Mendapatkan total jumlah yang sudah melakukan choice
     * @return
     */
    def getChooserCount:Int = {
        this.getVertex.pipe.in(CHOICE).in(CHOICE).dedup().length
    }

    /**
     * add choice for polling
     * @param title title choice
     * @param pictureUrl url ke gambar apabila diperlukan (optional)
     * @return
     */
    def addChoice(title: String, pictureUrl:String=""):PollingChoice = {

        if (title.trim.length < 3 || title.trim.length > 160)
            throw InvalidParameterException("Option polling minimal 3 dan maksimal 160 karakter.")

        this.reload()

        val pollChoice = PollingChoice(title, pictureUrl).save().toCC[PollingChoice].getOrElse{
            throw new DigakuException("Cannot create polling choice with title %s".format(title))
        }
        pollChoice --> CHOICE --> this

        db.commit()

        pollChoice

    }

    /**
     * add multiple choices for polling
     * @param choices multiple choices to add.
     * @return
     */
    def addChoices(choices:Seq[PollingChoice]){
        this.reload()

        for (pollChoice <- choices){
            pollChoice.save()
            pollChoice --> CHOICE --> this
        }

        db.commit()
    }

    /**
     * remove choice dari polling
     * @param choice polling choice
     */
    def removeChoice(choice:PollingChoice){
        val v = db.getVertex(choice.getId)
        if (v == null){
            throw NotExistsException("polling choice didn't exists.")
        }

        v.pipe.bothE().iterator().foreach(x => db.removeEdge(x))
        db.removeVertex(v)
        db.commit()
    }

    /**
     * remove choice by choice id
     * @param id choice id
     */
    def removeChoiceById(id:IDType){
        val v = db.getVertex(id)
        if (v == null){
            throw NotExistsException("pollinf choice didn't exists.")
        }

        v.pipe.bothE().iterator().foreach(x => db.removeEdge(x))
        db.removeVertex(v)
        db.commit()
    }

    /**
     * get all choice untuk polling
     * @return Seq[PollingChoice]
     */
    def getChoices:Seq[PollingChoice] = {
        this.getVertex.pipe.in(CHOICE).iterator().flatMap(_.toCC[PollingChoice]).toSeq
    }

    /**
     * Get choice by id
     * @param idChoice id polling choice object
     * @return
     */
    def getChoice(idChoice:IDType):Option[PollingChoice] = {
        this.getVertex.pipe.in(CHOICE)
            .has("id", idChoice)
            .headOption
            .flatMap(_.toCC[PollingChoice])
    }

    def clearChoices(){
        val listV = this.getVertex.pipe.in(CHOICE).iterator()
        if (listV.hasNext){
            listV.foreach{ v =>
                v.pipe.bothE().iterator().foreach(x => db.removeEdge(x))
                db.removeVertex(v)
            }
            db.commit()
        }
    }

    /**
     * Check apakah user bisa memilih atau tidak.
     * @param user untuk dicheck.
     * @return
     */
    def canChoice_?(user:User):Boolean = {
        !getChoices.exists(_.getChooser.contains(user))
    }

    /**
     * Check apakah choice termasuk dalam object polling
     * @param idChoice id polling choice object
     * @return
     */
    def isChoices(idChoice: IDType):Boolean = {
        this.getVertex.pipe.in(CHOICE).has("id", idChoice).headOption.isDefined
    }

    /**
     * Check apakah user sudah melakukan vote atau belum
     * @param user object user @see [[com.ansvia.digaku.model.User]]
     * @return
     */
    def isChooser(user:User):Boolean = {
        this.getVertex.pipe.in(CHOICE)
            .in(CHOICE)
            .has("id", user.getId)
            .headOption.isDefined
    }

}

/**
 * Polling choice ini merupakan item yang bisa di-pilih dalam sebuah polling,
 * bisa berisi text saja maupun text dan gambar.
 * @param title judul dari pilihan ini (mandatory).
 * @param pictureUrl url ke gambar apabila ada/dibutuhkan (optional)
 */
case class PollingChoice(var title:String, var pictureUrl:String="") extends BaseModel[IDType] with DbObject with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    import scala.collection.JavaConversions._

    @Persistent var chooserCount = 0



    /**
     * Tambahkan user pemilihnya.
     * @param user user yang memilih, see [[com.ansvia.digaku.model.User]]
     */
    def addChooser(user:User){
        val isChooser = getVertex.pipe.out(CHOICE).in(CHOICE).in(CHOICE).has("id", user.getId)
            .iterator().hasNext

        if (isChooser)
            throw AlreadyExistsException("user has to choose")

        user --> CHOICE --> this
        chooserCount += 1
        getVertex.setProperty(chooserText, chooserCount)

        db.commit()
    }

    /**
     * get semua choicer
     * @return Iterable user
     */
    def getChooser:Iterable[User] = {
        getVertex.pipe.in(CHOICE).iterator().flatMap(_.toCC[User]).toList
    }

    /**
     * Search polling chooser
     * @param query chooser full name query
     * @param offset --
     * @param limit --
     * @return user see [[com.ansvia.digaku.model.User]]
     */
    def searchChooser(query:String, offset:Int, limit:Int):Seq[User] = {
        var pipe = gremlin(getVertex).in(CHOICE)

        if (query.trim.length > 0) {
            pipe = pipe.filter { (v: Vertex) =>
                v.getOrElse("fullName", "").toLowerCase.contains(query.trim.toLowerCase) ||
                    v.getOrElse("name", "").contains(query.trim.toLowerCase)
            }
        }

        pipe.range(offset, (offset + limit) - 1).iterator().flatMap(_.toCC[User]).toSeq

    }

    /**
     * remove choicer berdasarkan user model
     * @param user see [[com.ansvia.digaku.model.User]]
     */
    def removeChooser(user:User){
        getVertex.pipe.inE(CHOICE).as("ed").outV().has("id", user.getId)
            .back("ed").asInstanceOf[GremlinPipeline[Edge,Edge]]
            .headOption.foreach { e =>

            db.removeEdge(e)

            chooserCount -= 1
            getVertex.setProperty(chooserText, chooserCount)
        }
    }

    def isChooser(user:User):Boolean = {
        getVertex.pipe.in(CHOICE).has("id", user.getId).headOption.isDefined
    }

    /**
     * remove choicer berdasarkan user id
     * @param id user id
     */
    def removeChooserById(id:Long){
        getVertex.pipe.inE(CHOICE).as("ed").outV().has("id",id)
            .back("ed").asInstanceOf[GremlinPipeline[Edge,Edge]]
            .headOption.foreach { e =>

            db.removeEdge(e)

            chooserCount -= 1
            getVertex.setProperty(chooserText, chooserCount)
        }
    }

}


object Polling extends PollingDao[GraphType, Polling] {
    val chooserText = "chooserCount"
}
