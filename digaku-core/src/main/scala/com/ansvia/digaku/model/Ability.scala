/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

object Ability {
    val ALL = "all"
    val CREATE_CHANNEL = "create-group"
    val BAN_USER = "ban-user"
    val CREATE_ANNOUNCEMENT = "create-announcement"

    /**
     * Bisa delete semua jenis postingan dalam sebuah group
     */
    val DELETE_POST = "delete-post"

    /**
     * Bisa move semua jenis postingan dalam sebuah group
     */
    val MOVE_CONTENT = "move-content"

    /**
     * Bisa edit semua jenis postingan dalam sebuah group
     */
    val EDIT_POST = "edit-post"

    /**
     * Bisa close semua jenis postingan dalam sebuah group
     */
    val CLOSE_POST = "close-post"

    /**
     * Bisa retalk semua jenis postingan ke dalam sebuah group target
     */
    val RETALK_POST = "retalk-post"

    /**
     * Bisa membuka halaman group setting
     * bagian tab general
     */
    val GENERAL_SETTING_CHANNEL = "general-setting-group"

    /**
     * Bisa membuka halaman group setting
     * bagian tab post
     */
    val POST_SETTING_CHANNEL = "post-setting-group"

    /**
     * Bisa membuka halaman group setting
     * bagian tab ads
     */
    val ADS_SETTING_CHANNEL = "ads-setting-group"

    /**
     * Bisa membuka halaman group setting
     * bagian tab members
     */
    val MEMBERS_SETTING_CHANNEL = "members-setting-group"

    val supported = Seq(
        DELETE_POST, CREATE_CHANNEL, BAN_USER, CREATE_ANNOUNCEMENT, MOVE_CONTENT
    )

    def isSupported(cur:String) =
        supported.contains(cur)


    private val defaultChannelStaffAbility = Seq (
        GENERAL_SETTING_CHANNEL, EDIT_POST, DELETE_POST
    )

    def isSupportChannelStaffAbility(cur:String) =
        defaultChannelStaffAbility.contains(cur)
}
