/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.graph.AbstractDbObject

/**
 * Author: robin
 *
 */


/**
 * Setiap model yang mixin dari sini
 * akan memiliki akses ke collection vertex dengan mudah.
 */
trait CollectionVertex extends AbstractDbObject {

    /**
     * get vertex collection to avoid super node by partitioning using date year.
     * if not exists; then create it.
     *
     * @see [[com.ansvia.digaku.model.CollectionVertex.getYMPart]]
     *
     * @param kind see [[com.ansvia.digaku.model.CollectionVertex.Kind]]
     * @param year the year to get
     * @return
     */
    def getYPart(kind:Int, year:Int=Digaku.engine.dateUtils.getCurrentYear) = {
        CollectionVertex.getYearBased(this, kind, year)
    }

    /**
     * Mendapatkan collection vertex yang dipartisi berbasiskan pada bulan.
     *
     * @see [[com.ansvia.digaku.model.CollectionVertex.getYPart]]
     *
     * @param kind jenis collection vertex-nya.
     * @param year partisi tahun.
     * @param month partisi bulan.
     * @return
     */
    def getMPart(kind:Int, year:Int = Digaku.engine.dateUtils.getCurrentYear, month:Int = Digaku.engine.dateUtils.getCurrentMonth) = {
        CollectionVertex.getMonthBased(this, kind, year, month)
    }


    /**
     * Mendapatkan collection vertex yang dipartisi berbasiskan pada bulan
     * dengan metode inline.
     *
     * @see [[com.ansvia.digaku.model.CollectionVertex.getYPart]]
     *
     * @param kind jenis collection vertex-nya.
     * @param year partisi tahun.
     * @param month partisi bulan.
     * @return
     */
    def getYMPart(kind:Int, year:Int=Digaku.engine.dateUtils.getCurrentYear, month:Int=Digaku.engine.dateUtils.getCurrentMonth) = {
        CollectionVertex.getMonthBasedInline(this, kind, year, month)
    }

    def getYMPartEx(kind:Int, year:Int,
                  month:Int, t:TransactionalGraphType) = {
        CollectionVertex.getMonthBasedInline(this, kind, year, month, t)
    }

}

object CollectionVertex extends DbAccess {

    object Kind {
        val STANDARD = 0
        val PUBLISH_CONTENT = 1
        val STREAM = 2
        val CREATE = 3
        val RESPONSE = 4
        val LIKES = 5
        val PM_STREAM = 6
        val VIEW = 7
        val REPUTATION = 8
        val USER_STREAM = 9
        val LEADERBOARD = 10
        val OTHERS = 50
    }


    import com.ansvia.digaku.model.Label._
    import com.ansvia.graph.BlueprintsWrapper._

    import scala.collection.JavaConversions._


    /**
     * Mendapatkan collection vertex yang dipartisi berdasarkan tahun.
     * Graph model: X --> tahun --> Y
     * @param cv
     * @param kind see [[com.ansvia.digaku.model.CollectionVertex.Kind]]
     * @param year year
     * @param timeOrder time order for sorted store.
     * @return
     */
    def getYearBased(cv:CollectionVertex, kind:Int, year:Int, timeOrder:Long=Digaku.engine.dateUtils.nowMilis) = synchronized {

    	val _cv =
        cv.getVertex.pipe.out(COLLECTION_VERTEX)
            .has("kind", kind).has("year", year).asInstanceOf[GremPipeVertex]
            .headOption.getOrElse {

            val v = db.addVertex(null)
            v.setProperty("kind", kind)
            v.setProperty("year", year)

//            val ed = cv.getVertex --> COLLECTION_VERTEX --> v <()

            val ed = cv.getVertex.addEdge(COLLECTION_VERTEX, v)

            ed.setProperty("timeOrder", year.toLong)

            v
        }
	    db.getVertex(_cv.getId)
    }

    def getYearBasedTrx(cv:CollectionVertex, kind:Int, year:Int, timeOrder:Long=Digaku.engine.dateUtils.nowMilis)(implicit db:TransactionalGraphType) = synchronized {

    	val _cv =
        cv.getVertex.pipe.out(COLLECTION_VERTEX)
            .has("kind", kind).has("year", year).asInstanceOf[GremPipeVertex]
            .headOption.getOrElse {

            val v = db.addVertex(null)
            v.setProperty("kind", kind)
            v.setProperty("year", year)

//            val ed = cv.getVertex --> COLLECTION_VERTEX --> v <()

            val ed = cv.getVertex.addEdge(COLLECTION_VERTEX, v)

            ed.setProperty("timeOrder", year.toLong)

            v
        }

	    db.getVertex(_cv.getId)
    }


    // untuk backward compatibility maka sementara ini, code ini sama dengan getMonthBasedInline
    /**
     * Mendapatkan collection vertex yang dipartisi berdasarkan tahun dan bulan.
     * Graph model: X --> tahun --> bulan --> Y
     * @param cv
     * @param kind see [[com.ansvia.digaku.model.CollectionVertex.Kind]]
     * @param year year
     * @param month month
     * @param timeOrder custom time order, default current time in millisecond.
     * @return
     */
    def getMonthBased(cv:CollectionVertex, kind:Int, year:Int, month:Int,
                      timeOrder:Long = Digaku.engine.dateUtils.nowMilis) = synchronized {

        val v1 = getYearBased(cv, kind, year, timeOrder)

	    val _cv =
        v1.pipe.out(COLLECTION_VERTEX)
            .has("kind", kind).has("year", year)
            .has("month", month).asInstanceOf[GremPipeVertex]
            .headOption.getOrElse {

                val v = db.addVertex(null)
                v.setProperty("kind", kind)
                v.setProperty("year", year)
                v.setProperty("month", month)

                // val ed = v1 --> COLLECTION_VERTEX --> v <()

                val ed = cv.getVertex.addEdge(COLLECTION_VERTEX, v)

                val timeOrder = f"$year%04d$month%02d".toLong

                ed.setProperty("timeOrder", timeOrder)

                v

            }

	    db.getVertex(_cv.getId)
    }

    /**
     * Mendapatkan collection vertex yang dipartisi berdasarkan tahun dan bulan secara inline.
     * Graph model: X --> [tahun,bulan] --> Y
     * @param cv
     * @param kind see [[com.ansvia.digaku.model.CollectionVertex.Kind]]
     * @param year year
     * @param month month
     * @return
     */
    def getMonthBasedInline(cv:CollectionVertex, kind:Int, year:Int, month:Int, _db:TransactionalGraphType=db) = synchronized {

        val _cv =
        cv.getVertex.pipe.out(COLLECTION_VERTEX)
            .has("kind", kind).has("year", year).has("month", month).asInstanceOf[GremPipeVertex]
            .headOption.getOrElse {

            val v = _db.addVertex(null)
            v.setProperty("kind", kind)
            v.setProperty("year", year)
            v.setProperty("month", month)

            val ed = cv.getVertex.addEdge(COLLECTION_VERTEX, v)

            val timeOrder = f"$year%04d$month%02d".toLong

            ed.setProperty("timeOrder", timeOrder)
            // kind juga dituliskan di edge-nya untuk optimal retrieving
            ed.setProperty("kind", kind)

            v
        }

	    _db.getVertex(_cv.getId)
    }

}
