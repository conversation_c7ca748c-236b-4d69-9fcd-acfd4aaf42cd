/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.{DaoBase, UserDao}
import com.ansvia.digaku.database.GraphCompat.tx
import com.ansvia.digaku.event.impl._
import com.ansvia.digaku.exc.{AlreadyExistsException, DigakuException, InvalidParameterException, LimitationReachedException, NotExistsException, NotSupportedException, PermissionDeniedException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.notifications.impl.NotifPartition
import com.ansvia.digaku.notifications._
import com.ansvia.digaku.persistence.CounterProviderOperator
import com.ansvia.digaku.se.ArticleQueryBuilder
import com.ansvia.digaku.security.Password
import com.ansvia.digaku.utils.UserSettings._
import com.ansvia.digaku.utils.{EmptyPhoneNumberGenerator, StringCodeGenerator}
import com.ansvia.digaku.validator.EmailValidator
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.graph.annotation.Persistent
import com.ansvia.graph.gremlin._
import com.ansvia.perf.PerfTiming
import com.ansvia.util.idgen.{RandomStringGenerator, TokenIdGenerator}
import com.tinkerpop.blueprints.{Vertex, _}
import com.tinkerpop.gremlin.Tokens.T
import com.tinkerpop.gremlin.java.GremlinPipeline
import com.tinkerpop.pipes.util.FastNoSuchElementException
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, Years}
import com.ansvia.digaku.utils.ForumSettings._
import scala.collection.JavaConversions._
import scala.math._


/**
 * Model untuk User
 * @param name nama user.
 * @param fullName nama lengkap user.
 * @param emailLogin email untuk login.
 * @param sex gender user.
 * @param location lokasi user.
 * @param selfDescs self description.
 * @param birthDate tempat tanggal lahir user.
 */
case class User(name: String,
                var fullName: String,
                var emailLogin: String,
                var sex: Int, // see [[SexType]]
                var location: String,
                var selfDescs: String, // separated by comma.
                var birthDate: String)
    extends BaseModel[IDType]
    with Origin[GraphType]
    with LockableAutoRelease
    with Deletable
    with PerfTiming
    with DbAccess
    with Counter
    with CollectionVertex
    with GroupableObject
    with Endorsable {

    import com.ansvia.digaku.model.SubForumStateFlags._
    import scala.collection.JavaConversions._

    require(name != null, "Name cannot be null")
    require(!(emailLogin == null || emailLogin == ""), "Email login cannot be null nor empty string")

    protected lazy val counter = Digaku.engine.counterProvider("user-counter-" + getId)


    // see [[UserRole]]
    @Persistent var role: Int = UserRole.USER

    @Persistent var kind = OriginKind.USER
    // @deprecated dah gak disimpen di db, digantikan oleh user.lower-name
    val lowerName: String = name.toLowerCase
    @Persistent var timezone: Int = 7

    /**
     * untuk user yang sudah diverifikasi
     * oleh staff, misalnya untuk selebriti, presiden,
     * dan public figur lainnya.
     */
    @Persistent var verified = false

    @Persistent var title = ""

    // personal description, ditulis oleh user
    @Persistent var description = ""

    @Persistent var meStatus = ""
    @Persistent var hasToured = false
    @Persistent var hasGetStarted = false

    /**
     * inactive digunakan untuk user
     * me-nonaktifkan account dan menambahkan reason
     */
    @Persistent var inactive = false
    @Persistent var inactiveReason = ""

    @Persistent var level = 0
    @Persistent var point = 0
    @Persistent var photoSmall = ""
    @Persistent var photoMedium = ""
    @Persistent var photoLarge = ""
    @Persistent var bannerPicUrl = ""
    @Persistent var theme = ""
    @Persistent var registerProvider = ""
    @Persistent var registerId = ""
    @Persistent var authMechanism = ""
    @Persistent var passHash = ""
    @Persistent var passV = 3

    // Department user yang di ambil dari LDAP
    @Persistent var department = ""

    /**
     * @see [[com.ansvia.digaku.model.Locale]]
     */
    @Persistent var locale = Locale.DEFAULT_LOCALE

    /**
     * Digunakan untuk user yang sudah verifikasi email atau phone /  aktifasi.
     */
    @Persistent var activated = false

    /**
     * digunakan untuk user yang sudah verifikasi phone number
     */
    lazy val mobilePhoneActivated = getVertex.getOrElse("user.mobilePhoneActivated", false)

    /**
     * digunakan untuk user yang sudah aktifasi email
     */
    lazy val emailActivated = getVertex.getOrElse("user.emailActivated", false)

    @Persistent var supportersCount: Int = 0
    @Persistent var supportingCount: Int = 0
    @Persistent var joinTime = 0L

    // true ketika user di create dari admin panel
    @Persistent var createdFromAdminPanel = false

    @Persistent var mobilePhone = ""

    var privacys: Array[String] = Array.empty[String]
    var noEmailNotifs: Array[String] = Array.empty[String]

    @Persistent var bgPic = ""
    @Persistent var widgetPos = "default" // support `default` and `custom`

    /**
     * Dashboard settings for the user
     * @see [[com.ansvia.digaku.model.User#supportedLayouts]]
     *      can be:
     *      + classic
     *      + dashboard
     */
    @Persistent var layout = "classic"
    // default layout for user
    @Persistent var toured = "" // separated by commas tour, eg: basic,settings

    // ini nandain kalo user ini developer App atau bukan
    // App di sini adalah Digaku App [[model.App]]
    @Persistent var appDeveloper = false

    // ini digunakan untuk menandai kalo user ini adalah
    // provider iklan atau bukan.
    @Persistent var adsProvider = false

    def getName = {
        if (fullName.isEmpty)
            name
        else
            fullName
    }

    /**
     * Set user verified
     * @param verified state (true/false)
     * @param title eg: Celebrity, Community, Contributor, etc
     */
    def setVerified(verified: Boolean, title: String) {
        tx { trx =>
            val v = trx.getVertex(getId)

            v.setProperty("verified", verified)
            v.setProperty("title", title)

            this.verified = verified
            this.title = title
        }
    }

    /**
     * Set user email login
     * @param email email user
     */
    def setEmailLogin(email: String) {
        EmailValidator.validate(email)

        tx { trx =>
            val v = trx.getVertex(getId)
            v.setProperty("emailLogin", email)
            v.setProperty("user.email", EmailValidator.normalizedEmail(email))
        }
    }

    /**
     * Set this if not want email notifications
     * non transaction, call save() after to save.
     * @param noEmailNotif list of no email notification see [[com.ansvia.digaku.model.EmailRoleNotif]]
     */
    def setNoEmailNotif(noEmailNotif: Array[String]) {
        this.settings.set("noEmailNotif", noEmailNotif.mkString(","))
    }

    /**
     * non transaction, call save() after to save.
     * @param privacys
     */
    def setPrivacys(privacys: Array[String]) {
        this.privacys = privacys
    }

    def getNoEmailNotif = {
        this.settings.get("noEmailNotif", "").split(",")
    }

    def getPrivacys = privacys

    /**
     * non transaction, call save() after to save.
     * @param noEmailNotif
     */
    def addNoEmailNotif(noEmailNotif: String) {
        this.noEmailNotifs +:= noEmailNotif
    }

    /**
     * Menambahkan reputasi kepada creator thread atau reply ex: Cendol / Bata
     * @param postObject Object Post see [[com.ansvia.digaku.model.Post]] atau
     *                   Response see [[com.ansvia.digaku.model.Response]]
     * @param reputation good or bad, see [[Reputation]]
     * @param reason alasan memberikan reputasi ini.
     */
    def giveReputation(postObject: HasCreator, reputation: Reputation, reason: String) {
        val userTarget = postObject.creator
        postObject match {
            case resp:Response if resp.isDeleted =>
                throw PermissionDeniedException("Anda tidak dapat memberikan kompor pada response yang sudah di-delete")
            case post:Post if post.isDeleted =>
                throw PermissionDeniedException("Anda tidak dapat memberikan kompor pada post yang sudah di-delete")
            case post:Post if post.isLocked =>
                throw PermissionDeniedException("Anda tidak dapat memberikan kompor pada post yang sedang di-lock")
            case post:Post =>
            case response:Response =>
            case _ => throw NotSupportedException("Not valid object")
        }

        if (userTarget == this) {
            throw PermissionDeniedException("Anda tidak dapat memberikan kompor ke diri sendiri")
        }

        if (this.locked) {
            throw PermissionDeniedException("Tidak dapat memberikan Kompor. Anda masih berada dalam status Suspended.")
        }

        // user harus memiliki >= 100 post, untuk memberikan reputasi kepada user lain.
        if (getPostCount < 100) {
            throw PermissionDeniedException("Tidak dapat memberikan kompor, Anda harus mempunyai lebih dari 100 post")
        }

        val userSettings = this.settings
        val now = Digaku.engine.dateUtils.getCurrentTime()
        val nowMillis = now.getMillis
        val timestamp = userSettings.getOption("reputation.timestamp").getOrElse(0L)

        if (timestamp > 0 && nowMillis < new DateTime(timestamp).plusHours(24).getMillis) {
            throw LimitationReachedException("Anda harus menunggu selama 24 jam sebelum dapat memberikan kompor lagi")
        }

        if (inLastGivenReputationList(userTarget)) {
            throw LimitationReachedException("Anda harus memberikan kompor ke 20 user lain sebelum dapat memberikan kompor ke user ini")
        }

        // tidak bisa add reputatation pada object post atau response yang sama
        val alreadyForObjId = {
            this.getVertex.pipe.out(COLLECTION_VERTEX)
                .has("kind", CollectionVertex.Kind.REPUTATION)
                .outE(GIVE_REPUTATION)
                .has("objectId", postObject.getVertex.getId)
                .range(0, 1)
                .hasNext
        }

        if (alreadyForObjId) {
            throw AlreadyExistsException("Anda sudah memberikan kompor untuk post ini")
        }

        this.reload()
        val ed = this.getYMPart(CollectionVertex.Kind.REPUTATION) --> GIVE_REPUTATION --> userTarget.reload() <()

        ed.setProperty("sourceId", this.getId)
        ed.setProperty("targetId", userTarget.getId)
        ed.setProperty("value", reputation.value)
        ed.setProperty("reason", reason)
        ed.setProperty("timeOrder", nowMillis)
        ed.setProperty("objectId", postObject.getVertex.getId)

        userSettings.set("reputation.timestamp", nowMillis)

        reputation match {
            case Reputation.Good => userTarget.getCounter.increment(Reputation.counterKey)
            case Reputation.Bad => userTarget.getCounter.decrement(Reputation.counterKey)
        }

        db.commit()

        UserLeaderboard.incrementPoint(userTarget, 3)

        Digaku.engine.eventStream.emit(GiveReputationEvent(this.asInstanceOf[User], userTarget, postObject))
    }

    def getReputationCount = {
        this.getCounter.get(Reputation.counterKey)
    }

    def getReputationList() = {
        this.getVertex.pipe
            .inE(GIVE_REPUTATION)
            .range(0, 20 - 1) // max to 20 latest
            .asInstanceOf[GremPipeEdge]
            .iterator()
            .map { ed =>
                (ed.getOrElse("value", 0),
                    ed.getOrElse("reason", ""))
            }
    }

    /**
     * digunakan untuk melakukan pengecekan apakah user target ada di dalam
     * list 20 user terakhir yang diberikan reputation oleh current user
     * @param userTarget
     * @return
     */
    def inLastGivenReputationList(userTarget: User) = {
        this.getVertex.pipe.out(COLLECTION_VERTEX)
            .has("kind", CollectionVertex.Kind.REPUTATION)
            .outE(GIVE_REPUTATION)
            .range(0, 2 - 1) // max to 20 latest
            .iterator()
            .exists(_.getOrElse("targetId", 0L) == userTarget.getId)
    }

    /**
     * digunakan untuk mengkalkulasikan jumlah bar reputation
     * berdasarkan reputation  yang di dapat
     * untuk kalkulasinya bisa dilihat di
     * https://mindtalk.atlassian.net/secure/attachment/22356/22356_Give-Reputation-Rev.1+23-okt-2015.jpg
     * @return
     */
    def getCalcReputationBar: Int = {
        val repCount = this.getReputationCount
        val absRepCount = Math.abs(repCount)
        val rv =
            if (absRepCount < 1000) {
                ceil(absRepCount.toDouble / 100).toInt
            } else {
                11
            }

        if (repCount < 0) {
            rv * -1
        } else {
            rv
        }
    }

    /**
     * digunakan untuk check user bisa ngasih reputation atau ngga pada suatu post.
     * @param postObject Object Post see [[com.ansvia.digaku.model.Post]] atau
     *                   Response see [[com.ansvia.digaku.model.Response]]
     * @return
     */
    def userCanGiveReputation(postObject: HasCreator): Boolean = {
        val userSettings = this.settings
        val now = Digaku.engine.dateUtils.getCurrentTime()
        val nowMillis = now.getMillis
        val timestamp = userSettings.getOption("reputation.timestamp").getOrElse(0L)

        val alreadyGivenInLast = this.getVertex.pipe.out(COLLECTION_VERTEX)
            .has("kind", CollectionVertex.Kind.REPUTATION)
            .outE(GIVE_REPUTATION)
            .range(0, 20 - 1) // max to 20 latest
            .iterator()
            .exists(_.getOrElse("targetId", 0L) == postObject.getVertex.getId)

        // tidak bisa add reputatation pada object post atau response yang sama
        val alreadyForObjId = {
            this.getVertex.pipe.out(COLLECTION_VERTEX)
                .has("kind", CollectionVertex.Kind.REPUTATION)
                .outE(GIVE_REPUTATION)
                .has("objectId", postObject.getVertex.getId)
                .range(0, 1)
                .hasNext
        }

        !((postObject.creator == this) ||
            (timestamp > 0 && nowMillis < new DateTime(timestamp).plusHours(24).getMillis) ||
                alreadyGivenInLast || getPostCount < 100 || alreadyForObjId)
    }

    /**
     * non transaction, call save() after to save.
     * @param noEmailNotif
     */
    def removeNoEmailNotif(noEmailNotif: String) {
        this.noEmailNotifs = noEmailNotifs.filterNot(_.equalsIgnoreCase(noEmailNotif))
    }

    /**
     * get list group count yang berhubungan dengan user
     * menggunakan parameter flag
     * @param flags bitwise style of flags, @see [[com.ansvia.digaku.model.SubForumStateFlags]]
     * @param offset
     * @param limit
     * @param withPrivate if true then return private channels too.
     * @param withSoftDeleted if true then return also soft deleted channels.
     * @return
     */
    def getChannelsCount(flags: Int, offset: Int = 0, limit: Int = 10, withPrivate: Boolean = false,
                         withSoftDeleted: Boolean = false): Int = {

        var rvCount: Integer = 0

        tx { t =>
            val v = t.getVertex(getId)

            if ((flags | OWNER) == flags) {
                rvCount += (withPrivate match {
                    case true =>
                        if (withSoftDeleted) {
                            v.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                                .range(offset, (offset + limit) - 1).count().toInt
                        } else {
                            v.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                                .hasNot("deleted", true)
                                .range(offset, (offset + limit) - 1).count().toInt
                        }

                    case _ =>
                        if (withSoftDeleted) {
                            v.pipe.out(OWN).hasNot("privated", true)
                                .has("_class_", "com.ansvia.digaku.model.Forum")
                                .range(offset, (offset + limit) - 1).count().toInt
                        } else {
                            v.pipe.out(OWN).hasNot("privated", true).hasNot("deleted", true)
                                .has("_class_", "com.ansvia.digaku.model.Forum")
                                .range(offset, (offset + limit) - 1).count().toInt
                        }
                })
            }
            if ((flags | STAFF) == flags) {
                rvCount += (withPrivate match {
                    case true =>
                        if (withSoftDeleted) {
                            v.pipe.out(STAFF_AT).range(offset, offset + limit).count().toInt
                        } else {
                            v.pipe.out(STAFF_AT).hasNot("deleted", true).range(offset, offset + limit).count().toInt
                        }

                    case _ =>
                        if (withSoftDeleted) {
                            v.pipe.out(STAFF_AT).hasNot("privated", true).range(offset, offset + limit).count().toInt
                        } else {
                            v.pipe.out(STAFF_AT).hasNot("privated", true)
                                .hasNot("deleted", true)
                                .range(offset, offset + limit).count().toInt
                        }
                })
            }
            if ((flags | STAFF_ONLY) == flags) {
                rvCount += (withPrivate match {
                    case true =>
                        if (withSoftDeleted) {
                            v.pipe.out(STAFF_AT).filter { (v: Vertex) =>
                                !v.pipe.out(OWN).has("id", v.getId).hasNext
                            }.range(offset, offset + limit).count().toInt
                        } else {
                            v.pipe.out(STAFF_AT).hasNot("deleted", true)
                                .asInstanceOf[GremPipeVertex]
                                .filter { (v: Vertex) =>
                                    !v.pipe.out(OWN).has("id", v.getId).hasNext
                                }.range(offset, offset + limit).count().toInt
                        }
                    case _ =>
                        if (withSoftDeleted) {
                            v.pipe.out(STAFF_AT).hasNot("privated", true)
                            asInstanceOf[GremPipeVertex].filter { (v: Vertex) =>
                                !v.pipe.out(OWN).has("id", v.getId).hasNext
                            }.range(offset, offset + limit).count().toInt
                        } else {
                            v.pipe.out(STAFF_AT).hasNot("privated", true)
                                .hasNot("deleted", true)
                                .asInstanceOf[GremPipeVertex].filter { (v: Vertex) =>
                                !v.pipe.out(OWN).has("id", v.getId).hasNext
                            }.range(offset, offset + limit).count().toInt
                        }
                })
            }
            if ((flags | JOINED) == flags) {
                try {
                    rvCount += (withPrivate match {
                        case true =>
                            if (withSoftDeleted) {
                                v.pipe.out(JOIN).filter { (v: Vertex) =>
                                    !v.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                                        .has("id", v.getId).hasNext
                                }.range(offset, (offset + limit) - 1).count().toInt
                            } else {
                                v.pipe.out(JOIN).hasNot("deleted", true).asInstanceOf[GremPipeVertex].filter { (v: Vertex) =>
                                    !v.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                                        .has("id", v.getId).hasNext
                                }.range(offset, (offset + limit) - 1).count().toInt
                            }
                        case _ =>
                            if (withSoftDeleted) {
                                v.pipe.out(JOIN).hasNot("privated", true).asInstanceOf[GremPipeVertex].filter { (v: Vertex) =>
                                    !v.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                                        .has("id", v.getId).hasNext
                                }.range(offset, (offset + limit) - 1).count().toInt
                            } else {
                                v.pipe.out(JOIN).hasNot("privated", true)
                                    .hasNot("deleted", true)
                                    .asInstanceOf[GremPipeVertex].filter { (v: Vertex) =>
                                    !v.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                                        .has("id", v.getId).hasNext
                                }.range(offset, (offset + limit) - 1).count().toInt
                            }
                    })
                } catch {
                    case e: FastNoSuchElementException =>
                }
            }
            if ((flags | JOINED_AND_OWNED) == flags) {
                try {
                    rvCount += (withPrivate match {
                        case true =>
                            if (withSoftDeleted) {
                                v.pipe.out(JOIN).range(offset, (offset + limit) - 1).dedup().count().toInt
                            } else {
                                v.pipe.out(JOIN).hasNot("deleted", true)
                                    .range(offset, (offset + limit) - 1).dedup().count().toInt
                            }
                        case _ =>
                            if (withSoftDeleted) {
                                v.pipe.out(JOIN).hasNot("privated", true)
                                    .range(offset, (offset + limit) - 1).dedup().count().toInt
                            } else {
                                v.pipe.out(JOIN).hasNot("privated", true)
                                    .hasNot("deleted", true)
                                    .range(offset, (offset + limit) - 1).dedup().count().toInt
                            }
                    })
                } catch {
                    case e: FastNoSuchElementException =>
                }
            }

            rvCount
        }
    }

    // @TODO(robin): optimasi ini menggunakan search engine index saja
    /**
     * get list group yang berhubungan dengan user
     * menggunakan parameter flag
     * @param flags bitwise style of flags, @see [[com.ansvia.digaku.model.SubForumStateFlags]]
     * @param offset
     * @param limit
     * @param withPrivate if true then return private channels too.
     * @param withSoftDeleted if true then return also soft deleted channels.
     * @return
     */
    def getChannels(flags: Int, offset: Int = 0, limit: Int = 10, withPrivate: Boolean = false,
                    withSoftDeleted: Boolean = false): Array[Forum] = {

        timing(s"User($name).getChannels") {
            var rvCount: Integer = 0
            var offsetIter = offset
            var doIteration = true
            var nthIteration = 0
            var rv = Array.empty[Forum]

            tx { t =>
                while (doIteration) {
                    rvCount = getChannelsCount(flags, offsetIter, limit, withPrivate, withSoftDeleted)
                    val v = t.getVertex(getId)
                    if ((flags | OWNER) == flags) {
                        rv ++= (withPrivate match {
                            case true =>
                                if (withSoftDeleted) {
                                    v.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                                        .range(offsetIter, (offsetIter + limit) - 1)
                                        .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                } else {
                                    v.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                                        .hasNot("deleted", true)
                                        .range(offsetIter, (offsetIter + limit) - 1)
                                        .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                }

                            case _ =>
                                if (withSoftDeleted) {
                                    v.pipe.out(OWN).hasNot("privated", true)
                                        .has("_class_", "com.ansvia.digaku.model.Forum")
                                        .range(offsetIter, (offsetIter + limit) - 1)
                                        .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                } else {
                                    v.pipe.out(OWN).hasNot("privated", true).hasNot("deleted", true)
                                        .has("_class_", "com.ansvia.digaku.model.Forum")
                                        .range(offsetIter, (offsetIter + limit) - 1)
                                        .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                }

                        })
                    }
                    if ((flags | STAFF) == flags) {
                        rv ++= (withPrivate match {
                            case true =>
                                if (withSoftDeleted) {
                                    v.pipe.out(STAFF_AT).range(offsetIter, offsetIter + limit)
                                        .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                } else {
                                    v.pipe.out(STAFF_AT).hasNot("deleted", true).range(offsetIter, offsetIter + limit)
                                        .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                }

                            case _ =>
                                if (withSoftDeleted) {
                                    v.pipe.out(STAFF_AT).hasNot("privated", true).range(offsetIter, offsetIter + limit)
                                        .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                } else {
                                    v.pipe.out(STAFF_AT).hasNot("privated", true)
                                        .hasNot("deleted", true)
                                        .range(offsetIter, offsetIter + limit)
                                        .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                }

                        })

                    }
                    if ((flags | STAFF_ONLY) == flags) {
                        rv ++= (withPrivate match {
                            case true =>
                                if (withSoftDeleted) {
                                    v.pipe.out(STAFF_AT).filter { (v: Vertex) =>
                                        !v.pipe.out(OWN).has("id", v.getId).hasNext
                                    }.range(offsetIter, offsetIter + limit)
                                        .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                } else {
                                    v.pipe.out(STAFF_AT).hasNot("deleted", true)
                                        .asInstanceOf[GremPipeVertex]
                                        .filter { (v: Vertex) =>
                                            !v.pipe.out(OWN).has("id", v.getId).hasNext
                                        }.range(offsetIter, offsetIter + limit)
                                        .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                }
                            case _ =>
                                if (withSoftDeleted) {
                                    v.pipe.out(STAFF_AT).hasNot("privated", true)
                                    asInstanceOf[GremPipeVertex].filter { (v: Vertex) =>
                                        !v.pipe.out(OWN).has("id", v.getId).hasNext
                                    }.range(offsetIter, offsetIter + limit)
                                        .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                } else {
                                    v.pipe.out(STAFF_AT).hasNot("privated", true)
                                        .hasNot("deleted", true)
                                        .asInstanceOf[GremPipeVertex].filter { (v: Vertex) =>
                                        !v.pipe.out(OWN).has("id", v.getId).hasNext
                                    }.range(offsetIter, offsetIter + limit)
                                        .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                }
                        })

                    }
                    if ((flags | JOINED) == flags) {
                        try {
                            rv ++= (withPrivate match {
                                case true =>
                                    if (withSoftDeleted) {
                                        v.pipe.out(JOIN).filter { (v: Vertex) =>
                                            !v.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                                                .has("id", v.getId).hasNext
                                        }.range(offsetIter, (offsetIter + limit) - 1)
                                            .asInstanceOf[GremPipeVertex]
                                            .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                    } else {
                                        v.pipe.out(JOIN).hasNot("deleted", true).asInstanceOf[GremPipeVertex].filter { (v: Vertex) =>
                                            !v.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                                                .has("id", v.getId).hasNext
                                        }.range(offsetIter, (offsetIter + limit) - 1)
                                            .asInstanceOf[GremPipeVertex]
                                            .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                    }
                                case _ =>
                                    if (withSoftDeleted) {
                                        v.pipe.out(JOIN).hasNot("privated", true).asInstanceOf[GremPipeVertex].filter { (v: Vertex) =>
                                            !v.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                                                .has("id", v.getId).hasNext
                                        }.range(offsetIter, (offsetIter + limit) - 1)
                                            .asInstanceOf[GremPipeVertex]
                                            .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                    } else {
                                        v.pipe.out(JOIN).hasNot("privated", true)
                                            .hasNot("deleted", true)
                                            .asInstanceOf[GremPipeVertex].filter { (v: Vertex) =>
                                            !v.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                                                .has("id", v.getId).hasNext
                                        }.range(offsetIter, (offsetIter + limit) - 1)
                                            .asInstanceOf[GremPipeVertex]
                                            .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                    }
                            })
                        } catch {
                            case e: FastNoSuchElementException =>
                        }
                    }
                    if ((flags | JOINED_AND_OWNED) == flags) {
                        try {
                            rv ++= (withPrivate match {
                                case true =>
                                    if (withSoftDeleted) {
                                        v.pipe.out(JOIN).range(offsetIter, (offsetIter + limit) - 1).dedup()
                                            .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                    } else {
                                        v.pipe.out(JOIN).hasNot("deleted", true)
                                            .range(offsetIter, (offsetIter + limit) - 1).dedup()
                                            .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                    }
                                case _ =>
                                    if (withSoftDeleted) {
                                        v.pipe.out(JOIN).hasNot("privated", true)
                                            .range(offsetIter, (offsetIter + limit) - 1).dedup()
                                            .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                    } else {
                                        v.pipe.out(JOIN).hasNot("privated", true)
                                            .hasNot("deleted", true)
                                            .range(offsetIter, (offsetIter + limit) - 1).dedup()
                                            .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
                                    }
                            })
                        } catch {
                            case e: FastNoSuchElementException =>
                        }
                    }

                    rv = rv.distinct

                    if (rv.length >= rvCount || rv.length >= limit || rvCount < 1) {
                        doIteration = false
                    } else {
                        offsetIter += rv.length
                        nthIteration += 1
                    }
                }
            }

            if (rv.length > limit) {
                rv.slice(0, limit).distinct
            } else {
                rv.distinct
            }
        }
    }

    /**
     * Mendapatkan list group berdasarkan ability yang dimiliki user
     *
     * Untuk mendapatkan list group yg berhubungan dengan user secara global pakai [[com.ansvia.digaku.model.User#getChannels]]
     *
     * @param ability ability yang di miliki oleh user @see [[com.ansvia.digaku.model.Ability]]
     * @param offset
     * @param limit
     * @param query untuk search berdasarkan nama group
     * @return
     */
    def getChannelsByAbility(ability: String, offset: Int = 0, limit: Int = 10, query: String = ""): Array[Forum] = {
        tx { t =>

            val meV = t.getVertex(getId)

            if (!ability.isEmpty) {
                var channels = meV.pipe.outE(STAFF_AT).filter { (edge: Edge) =>
                    edge.getVertex(Direction.IN).pipe.in(OWN).has("id", this.getId).hasNext ||
                        edge.getOrElse("abilities", "").split(",").contains(ability)
                }.inV()

                if (query.trim.length > 0) {
                    channels = channels.filter { (v: Vertex) =>
                        v.getOrElse("forum.lower-name", "").contains(query.toLowerCase)
                    }
                }
                channels.range(offset, (offset + limit) - 1)
                    .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
            } else
                Array.empty[Forum]
        }
    }


    /**
     * Untuk mendapatkan jumlah group berdasarkan ability tertentu yang dia punya
     *
     * @param ability ability yang di miliki oleh user @see [[com.ansvia.digaku.model.Ability]]
     * @param query untuk search berdasarkan nama group
     * @return
     */
    def getChannelsByAbilityCount(ability: String, query: String = "") = {
        if (ability.isEmpty)
            0L
        else {
            var channels = getVertex.pipe.outE(STAFF_AT).filter { (edge: Edge) =>
                edge.getVertex(Direction.IN).pipe.in(OWN).has("id", this.getId).hasNext ||
                    edge.getOrElse("abilities", "").split(",").contains(ability)
            }.inV()

            if (query.length > 0) {
                channels = channels.filter { (v: Vertex) =>
                    v.getOrElse("forum.lower-name", "").contains(query.toLowerCase)
                }
            }
            channels.count()
        }
    }

    /**
     * digunakan untuk search group dari user
     * @param flags @see [[com.ansvia.digaku.model.SubForumStateFlags]]
     * @param offset
     * @param limit
     * @param query query yang digunakan untuk search group
     * @return
     */
    def searchChannel(flags: Int, offset: Int = 0, limit: Int = 10, query: String): Seq[Forum] = {
        var rv = Array.empty[Forum]
        if ((flags | OWNER) == flags) {
            rv ++= getVertex.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                .asInstanceOf[GremPipeVertex]
                .filter { (v: Vertex) =>
                    v.getOrElse("name", "").toLowerCase.contains(query.toLowerCase)
                }.range(offset, (offset + limit) - 1)
                .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
        }
        if ((flags | STAFF) == flags) {
            rv ++= getVertex.pipe.out(STAFF_AT)
                .asInstanceOf[GremPipeVertex]
                .filter { (v: Vertex) =>
                    v.getOrElse("name", "").toLowerCase.contains(query.toLowerCase)
                }.range(offset, offset + limit)
                .iterator().flatMap(_.toCC[Forum]).toArray[Forum]

        }
        if ((flags | STAFF_ONLY) == flags) {
            rv ++= getVertex.pipe.out(STAFF_AT).filter { (v: Vertex) =>
                !this.getVertex.pipe.out(OWN).has("id", v.getId).hasNext &&
                    v.getOrElse("name", "").toLowerCase.contains(query.toLowerCase)
            }.range(offset, offset + limit)
                .iterator().flatMap(_.toCC[Forum]).toArray[Forum]

        }
        if ((flags | JOINED) == flags) {
            try {
                rv ++= getVertex.pipe.out(JOIN).filter { (v: Vertex) =>
                    !this.getVertex.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.Forum")
                        .has("id", v.getId).hasNext &&
                        v.getOrElse("name", "").toLowerCase.contains(query.toLowerCase)
                }.range(offset, (offset + limit) - 1)
                    .asInstanceOf[GremPipeVertex]
                    .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
            } catch {
                case e: FastNoSuchElementException =>
            }
        }
        if ((flags | JOINED_AND_OWNED) == flags) {
            try {
                rv ++= getVertex.pipe.out(JOIN).filter { (v: Vertex) =>
                    v.getOrElse("name", "").toLowerCase.contains(query.toLowerCase)
                }.range(offset, (offset + limit) - 1).dedup()
                    .asInstanceOf[GremPipeVertex]
                    .iterator().flatMap(_.toCC[Forum]).toArray[Forum]
            } catch {
                case e: FastNoSuchElementException =>
            }
        }

        rv
    }

    /**
     * Check is current user already activated
     * or not.
     * this flag by default is false when user
     * firstly created / registered,
     * need further activation, eg: via email or phone.
     * @return
     */
    def isActivated = activated

    /**
     * Check is current user already activated email
     * or not.
     * this flag by default is false when user
     * firstly created / registered,
     * @return
     */
    def isActivatedEmail = emailActivated

    /**
     * Check is current user already activated mobile phone
     * or not.
     * this flag by default is false when user
     * firstly created / registered,
     * @return
     */
    def isActivatedPhone = mobilePhoneActivated

    /**
     * Set user as activated.
     * @param state active state.
     */
    def setActivated(state: Boolean) {
        val v = db.getVertex(getId)
        v.setProperty("activated", state)
        db.commit()
        this.activated = state
    }

    /**
     * set user as activated email
     * @param state
     */
    def setEmailActivated(state: Boolean) {
        tx { t =>
            val v = t.getVertex(getId)
            v.setProperty("user.emailActivated", state)
        }
    }

    /**
     * Set user as activated phone number
     * @param state
     */
    def setPhoneActivated(state: Boolean) {
        tx { t =>
            val v = t.getVertex(getId)
            v.setProperty("user.mobilePhoneActivated", state)
        }
    }

    /**
     * check apakah account user non-aktif atau tidak
     * ketika true = account nonaktif
     * ketika false = account aktif
     * @return Boolen
     */
    def isInactive = this.inactive

    /**
     * set inactive user untuk me-nonaktifkan atau mengaktifkan account.
     * @param state inactive state.
     */
    def setInactive(state: Boolean, inactiveReason: String, noTx: Boolean = false) {
        this.inactive = state
        this.inactiveReason = inactiveReason
        this.save()

        if (!noTx)
            db.commit()

        refreshUserCache()

        Digaku.engine.eventStream.emit(InactiveUserEvent(this.asInstanceOf[User]))
    }

    /**
     * refreshing user cache
     */
    def refreshUserCache() = {
        debug("refreshing user cache")

        HasCreator.creatorCache.asMap().foreach { case (k, v) =>
            v.map { creator =>
                if (creator.getId == this.getId) {
                    HasCreator.creatorCache.invalidate(k)
                }
            }
        }

        PublishableContent.creatorCache.asMap().foreach { case (k, v) =>
            v.map { creator =>
                if (creator.getId == this.getId) {
                    PublishableContent.creatorCache.invalidate(k)
                }
            }
        }
    }

    /**
     * Suspend user dan mengindex nya di search engine
     *
     * @param locked lock state.
     */
    override def setLocked(locked: Boolean) {
        if (locked) {
            if (this.role == UserRole.SUPER_ADMIN || role == UserRole.ADMIN) {
                throw PermissionDeniedException("Ubah role menjadi user terlebih dahulu.")
            }

            if (this.moderatedForums.getCount > 0) {
                val fId = this.moderatedForums.getStream(None, None, 1).toList.head

                Forum.getById(fId._1).foreach { f =>
                    throw PermissionDeniedException(s"Tidak bisa suspend, user masih menjadi moderator pada forum ${f.getName}")
                }
            }

            if (this.isAgentTopic) {
                val topicId = this.topicAgentSeqStore.getStream(None, None, 1).toList.head

                Topic.getById(topicId._1).foreach { topic =>
                    throw PermissionDeniedException(s"Tidak bisa suspend, User masih menjadi agent pada topic ${topic.name}")
                }
            }
        }

        super.setLocked(locked)
    }

    /**
     * Get user post stream, termasuk stream dirinya.
     * biasanya digunakan di home.
     * @param offset stream offset.
     * @param limit stream limit.
     * @param sinceId hanya mengembalikan konten dengan creationTime lebih besar dari id ini (konten yang lebih baru
     *                post id ini)
     * @param maxId hanya mengembalikan konten dengan creationTime lebih kecil dari id ini (konten yang lebih tua dari
     *              post id ini)
     * @return
     */
    def getStream(offset: Int, limit: Int, sinceId: Option[Long] = None,
                  maxId: Option[Long] = None): Iterator[StreamObject] = tx { t =>

        var pipe = t.getVertex(getId).pipe.out(COLLECTION_VERTEX)
            .has("kind", CollectionVertex.Kind.STREAM)
            .range(0, 1).outE(STREAM)

        sinceId map { _sinceId =>
            pipe = pipe.as("x").inV().has("id", T.gt, _sinceId).back("x").asInstanceOf[GremPipeEdge]
        }

        maxId map { _maxId =>
            pipe = pipe.as("x").inV().has("id", T.lte, _maxId).back("x").asInstanceOf[GremPipeEdge]
        }

        pipe.as("x").inV().out(ORIGIN)
            .filter((v: Vertex) => !v.pipe.out(BLOCK).has("id", getId).iterator().hasNext)
            .back("x").asInstanceOf[GremPipeEdge]
            .range(offset, (offset + limit) - 1)
            .iterator().toList.flatMap { ed =>
                val inV = ed.getVertex(Direction.IN)
                inV.getProperty[String]("_class_") match {
                    case "com.ansvia.digaku.model.Article" | "com.ansvia.digaku.model.SimplePost" |
                         "com.ansvia.digaku.model.Deal" | "com.ansvia.digaku.model.Question" =>
                        inV.toCC[Post].map(PostStreamObject)
                    case "com.ansvia.digaku.model.Picture" =>
                        inV.toCC[Picture].map(PostStreamObject)
                    case x =>
                        throw NotSupportedException("class name " + x + " not supported yet for stream object, " +
                            "please add it first manually in User.getStream pattern matcher")
                }
            }.toIterator
    }

    /**
     * Mendapatkan semua stream/article pada forum yang di follow user
     * @param offset
     * @param limit
     * @return
     */
    def getFollowingForumStream(offset: Int, limit: Int): Iterator[Article] = {
        getVertex.pipe.out(COLLECTION_VERTEX)
            .has("kind", CollectionVertex.Kind.USER_STREAM)
            .range(0, 1).out(USER_STREAM)
            .range(offset, (offset + limit) - 1)
            .iterator().flatMap(_.toCC[Article])
    }

    /**
     * Mendapatkan semua stream/article(without deleted) pada forum yang di follow user
     * @param offset
     * @param limit
     * @param maxId
     * @return
     */
    def getFollowingForumStream(offset: Int, limit: Int, maxId: Option[Long]): Iterator[Article] = {
        var pipe = getVertex.pipe.out(COLLECTION_VERTEX)
            .has("kind", CollectionVertex.Kind.USER_STREAM)
            .range(0, 1).outE(USER_STREAM)

        maxId foreach { _maxId =>
            pipe = pipe.as("x").inV().has("id", T.lt, _maxId).back("x").asInstanceOf[GremPipeEdge]
        }

        pipe.inV()
            .has("deleted", false)
            .range(offset, (offset + limit) - 1)
            .iterator().flatMap(_.toCC[Article])
    }

    /**
     * Increment stream count ketika stream/article ditambahkan
     * pada forum yang di-follow user
     * @param count
     * @return
     */
    def incrementForumStreamCount(count: Int = 1): Long = {
        counter.incrementBy(followingForumStreamKey, count)
        counter.get(followingForumStreamKey)
    }

    /**
     * Decrement stream count ketika stream/article dihapus
     * pada forum yang di-follow user
     * @param count
     * @return
     */
    def decrementForumStreamCount(count: Int = 1): Long = {
        counter.decrementBy(followingForumStreamKey, count)
        counter.get(followingForumStreamKey)
    }

    /**
     * Mendapatkan stream count untuk post/article yang di tambahkan
     * pada forum yang di follow user
     * @return
     */
    def getForumStreamCount = {
        counter.get(followingForumStreamKey)
    }

    /**
     * Increment julmlah forum yang di-follow
     * @param count
     * @return
     */
    def incrementFollowingForumCount(count: Int = 1) = {
        counter.incrementBy(followingForumKey, count)
        counter.get(followingForumKey)
    }

    /**
     * Decrement jumlah forum yang di-follow
     * @param count
     * @return
     */
    def decrementFollowingForumCount(count: Int = 1) = {
        counter.decrementBy(followingForumKey, count)
        counter.get(followingForumKey)
    }

    /**
     * Mendapatkan jumlah forum yang di-follow
     * @return
     */
    def getFollowingForumCount = {
        counter.get(followingForumKey)
    }

    /**
     * Mendapatkan list forum yang di follow oleh user.
     * @param offset
     * @param limit
     * @return
     */
    def getFollowingForum(offset: Int, limit: Int): Iterator[Forum] = {
        getVertex.pipe.out(FOLLOW)
            .range(offset, (offset + limit) - 1)
            .iterator()
            .flatMap(_.toCC[Forum])
    }

    /**
     * mendapatkan profile stream di filter berdasarkan stream kind see [[com.ansvia.digaku.model.StreamKind]]
     * @param offset
     * @param limit
     * @param streamKind see [[com.ansvia.digaku.model.StreamKind]]
     * @param sinceId hanya mengembalikan konten dengan creationTime lebih besar dari id ini (konten yang lebih baru
     *                post id ini)
     * @param maxId id dari post yang mana merupakan batas dari stream yang ingin kita dapatkan.
     *              set -1 untuk mendapatkan dari latest.
     * @param exclusive apakah tidak termasuk return post dengan id yang termasuk dalam sinceId/maxId ?
     * @param year creation year where post wanted
     * @param month creation month where post wanted
     * @return
     */
    def getProfileStreamByType(offset: Int, limit: Int, streamKind: Option[Int] = None,
                               sinceId: Option[Long] = None, maxId: Option[Long] = None,
                               exclusive: Boolean = false, year: Option[Int] = None, month: Option[Int] = None): Iterator[PostStreamObject] = {

        timing(s"User($name).getProfileStreamByType") {

            tx { t =>
                var pipe = if (year.isDefined && month.isDefined) {

                    /**
                     * If year and month parameter specified
                     */

                    val cv = CollectionVertex.getMonthBasedInline(this, CollectionVertex.Kind.PUBLISH_CONTENT, year.get, month.get)

                    val _cv = db.getVertex(cv.getId)

                    _cv.pipe.outE(PUBLISH_CONTENT)

                } else {

                    /**
                     * If year and month parameter not specified
                     * do a 2 level graph traversal
                     */

                    getVertex.pipe.out(COLLECTION_VERTEX)
                        .has("kind", CollectionVertex.Kind.PUBLISH_CONTENT).outE(PUBLISH_CONTENT)
                }

                sinceId.map { since =>
                    pipe = if (exclusive)
                        pipe.has("targetId", T.gt, since).asInstanceOf[GremPipeEdge]
                    else
                        pipe.has("targetId", T.gte, since).asInstanceOf[GremPipeEdge]
                }

                maxId.map { to =>
                    pipe = if (exclusive)
                        pipe.has("targetId", T.lt, to).asInstanceOf[GremPipeEdge]
                    else
                        pipe.has("targetId", T.lte, to).asInstanceOf[GremPipeEdge]
                }

                pipe = pipe.hasNot("publish", false).asInstanceOf[GremPipeEdge]

                if (sinceId.isEmpty && maxId.isEmpty) {
                    // apabila sinceId dan maxId empty, maka perlu di-range,
                    // karena tanpa sinceId/maxId Titan akan me-map semua edges yang mengakibatkan super node problem
                    // jadi untuk jaga-jaga range ke batas max wajar 1jt edges max.
                    pipe = pipe.range(0, 1000)
                }

                // traversal streamKind ditaruh di bawah traversal sinceId dan maxId karena
                // cardinality-nya lebih kecil

                val st = streamKind.getOrElse(0)

                val _pipe = if (st != StreamKind.ALL) {
                    pipe.inV().has("streamKind", st).asInstanceOf[GremPipeVertex]
                } else {
                    pipe.inV()
                }

                _pipe.hasNot("deleted", true)
                    .as("post").out(ORIGIN).hasNot("privated", true)
                    .back("post")
                    .range(offset, (offset + limit) - 1)
                    .asInstanceOf[GremPipeVertex]
                    .iterator()
                    .flatMap { inV =>


                        inV.getProperty[String]("_class_") match {
                            case "com.ansvia.digaku.model.Article" | "com.ansvia.digaku.model.SimplePost" |
                                 "com.ansvia.digaku.model.Deal" | "com.ansvia.digaku.model.Question" =>
                                inV.toCC[Post].map(PostStreamObject)
                            case "com.ansvia.digaku.model.Picture" =>
                                inV.toCC[Picture].map(PostStreamObject)
                            case x =>
                                throw NotSupportedException("class name " + x + " not supported yet for stream object, " +
                                    "please add it first manually in User.getStream pattern matcher")
                        }

                    }
            }
        }
    }

    /**
     * Untuk mendapatkan content-content yang pernah dipublish oleh user
     * sama seperti stream di profil hanya dengan nama yang lebih relevance.
     * @param offset starts.
     * @param limit ends.
     * @param contains @see [[com.ansvia.digaku.model.Post.contains]]
     * @tparam T
     * @return
     */
    def getPublishedContents[T <: Post : Manifest](offset: Int, limit: Int, contains: Int = Post.contains.NONE) = {

        import com.ansvia.digaku.model.Post.contains._

        timing(s"User($name).getPublishedContents") {

            var pipe = getVertex.pipe.out(COLLECTION_VERTEX)
                .has("kind", CollectionVertex.Kind.PUBLISH_CONTENT).out(PUBLISH_CONTENT)
                .hasNot("deleted", true)


            val postKind = manifest[T].runtimeClass.getCanonicalName

            if (postKind != "com.ansvia.digaku.model.Post") {
                pipe = pipe.has("_class_", postKind).asInstanceOf[GremPipeVertex]
            }
            if ((contains | LINK) == contains) {
                pipe = pipe.has("containsLink", true).asInstanceOf[GremPipeVertex]
            }
            if ((contains | VIDEO_LINK) == contains) {
                pipe = pipe.has("containsVideoLink", true).asInstanceOf[GremPipeVertex]
            }
            if ((contains | PIC) == contains) {
                pipe = pipe.has("containsPicLink", true).asInstanceOf[GremPipeVertex]
            }

            pipe.range(0, 150).as("post") // di-range untuk menghindari dari ledakan memory
                .out(ORIGIN).hasNot("privated", true)
                .back("post")
                .range(offset, offset + limit - 1)
                .asInstanceOf[GremPipeVertex]
                .iterator().flatMap(_.toCC[T]).toSeq
        }

    }

    /**
     * Get event stream.
     * @param offset
     * @param limit
     * @return
     */
    def getEventStream(offset: Int, limit: Int): Seq[Event] = {

        this.getVertex.pipe.out(CREATE)
            .has("_class_", "com.ansvia.digaku.model.User")
            .range(offset, limit).asInstanceOf[GremPipeVertex]
            .order((a:Vertex,b:Vertex) => -a.getOrElse("creationTime", 0L).compareTo(b.getOrElse("creationTime", 0L)))
            .iterator()
            .flatMap(_.toCC[Event])
            .toSeq
    }

    /**
     * Digunakan untuk support user.
     * @param userTarget user target yang mau disupport.
     * @param force selalu support apapun yang terjadi kecuali apabila sudah support,
     *              ini tidak akan melakukan pengecekan apakah user target diblock
     *              oleh penyupport ataupun user target tidak mengijinkan untuk disupport
     *              sembarangan.
     */
    def support(userTarget: User, force: Boolean = false) {
        reload()
        userTarget.reload()

        if (isSupport(userTarget))
            return

        if (userTarget == this) {
            throw PermissionDeniedException("You can't support yourself")
        }

        if (!force) {
            if (userTarget.isBlocked(this.asInstanceOf[User]))
                throw PermissionDeniedException("You can't support %s".format(userTarget.name))

            // user yang di suspend tidak bisa support
            if (this.isLocked) {
                throw PermissionDeniedException("Unable to support user, you are currently suspended")
            }
        }

        supportInternal(userTarget)
    }

    private def supportInternal(userTarget: User) {
        val ed = this.reload() --> SUPPORT --> userTarget.reload() <()

        // digunakan untuk optimasi relevansi search
        // contoh digunakan di group members relevance.
        ed.setProperty("sourceId", this.getId)
        ed.setProperty("targetId", userTarget.getId)
        ed.setProperty("targetName", userTarget.lowerName)

        val v = this.getVertex

        supportingCount = v.getOrElse("supportingCount", 0) + 1

        v.setProperty("supportingCount", supportingCount)

        userTarget.reload()
        val targetV = userTarget.getVertex
        userTarget.supportersCount = targetV.getOrElse("supportersCount", 0) + 1
        targetV.setProperty("supportersCount", userTarget.supportersCount)

        db.commit()

        Digaku.engine.eventStream.emit(FollowUserEvent(this.asInstanceOf[User], userTarget))
    }

    /**
     * Digunakan untuk unsupport user.
     * @param userTarget user target yang mau diunsupport.
     */
    def unsupport(userTarget: User) {
        if (!isSupport(userTarget))
            return

        val v = this.reload().getVertex
        v.pipe.outE(SUPPORT).as("ed").inV().has("id", userTarget.getId).back("ed")
            .asInstanceOf[GremPipeEdge].headOption map { edge: Edge =>
            db.removeEdge(edge)
        }

        supportingCount = v.getOrElse("supportingCount", 0) - 1
        v.setProperty("supportingCount", supportingCount)

        userTarget.reload()
        val userTargetV = userTarget.getVertex

        userTarget.supportersCount = userTargetV.getOrElse("supportersCount", 0) - 1
        userTargetV.setProperty("supportersCount", userTarget.supportersCount)

        db.commit()
    }

    /**
     * Periksa apakah user ini telah men-support user ?
     * @param user user support target yang mau ditest.
     * @return
     */
    def isSupport(user: User): Boolean = tx { t =>
        t.getVertex(this.getId).pipe.outE(SUPPORT).has("targetId", user.getId).iterator().hasNext
    }

    /**
     * Check whether user is joined group.
     * @param group to check.
     * @return
     */
    def isJoined(group: Forum) = {
        group.isMember(this)
    }

    /**
     * Untuk mendapatkan jumlah group yang di-joini oleh user.
     * @return
     */
    def getJoinedChannelCount(includeOwned: Boolean = false): Int = {
        if (includeOwned)
            counter.get("join_channel").toInt
        else
            counter.get("join_channel").toInt - getOwnedChannelCount
    }

    def incrementJoinChannelCount(by: Int = 1) {
        counter.incrementBy("join_channel", by)
    }

    def decrementJoinChannelCount(by: Int = 1) {
        counter.decrementBy("join_channel", by)
    }

    def incrementOwnedChannelCount(by: Int = 1) {
        counter.incrementBy("owned_channel", by)
    }

    def decrementOwnedChannelCount(by: Int = 1) {
        counter.decrementBy("owned_channel", by)
    }

    /**
     * Untuk mendapatkan jumlah group yang mengangkat staff seorang user
     * @return
     */
    def getStaffAtChannelCount(includeOwned: Boolean = false) = {
        if (includeOwned)
            getVertex.pipe.outE(STAFF_AT).count()
        else {
            getVertex.pipe.out(STAFF_AT)
                .filter((v: Vertex) => v.pipe.in(OWN).has("id", this.getId).headOption.isEmpty).count()
        }
    }

    /**
     * Untuk mendapatkan jumlah group yang dimiliki oleh user.
     * @return
     */
    def getOwnedChannelCount: Int = {
        counter.get("owned_channel").toInt
    }

    /**
     * Untuk mendapatkan daftar supporter-nya.
     *
     * @param offset starting offset.
     * @param limit max limit. Apabila limit diset < 1 maka
     *              akan return semua supporter-nya (unlimited).
     * @return
     */
    def getSupporters(offset: Int = 0, limit: Int = 10): Seq[User] = timing(s"User($name).getSupporters") {
        val v = reload().getVertex
        if (limit > 0) {
            v.pipe.in(SUPPORT) // range-nya dihilangkan karena ada permintaan list supporters ditampilkan semua
                .order { (v1:Vertex,v2:Vertex) =>
                    v1.getOrElse("user.lower-name","").compareTo(v2.getOrElse("user.lower-name",""))
                }
                .range(offset, (offset + limit) - 1).iterator()
                .flatMap(_.toCC[User]).toSeq
        } else {
            v.pipe.in(SUPPORT) // range-nya dihilangkan karena ada permintaan list supporters ditampilkan semua
                .order { (v1:Vertex,v2:Vertex) =>
                    v1.getOrElse("user.lower-name","").compareTo(v2.getOrElse("user.lower-name",""))
                }
                .iterator()
                .flatMap(_.toCC[User]).toSeq
        }
    }

    /**
     * Untuk mendapatkan daftar supporting-nya.
     * @param offset starting offset.
     * @param limit max limit.
     * @return
     */
    def getSupporting(offset: Int = 0, limit: Int = 10): Seq[User] = timing("User.getSupporting") {
        // @TODO(robin): optimize this, masih lambat
        getVertex.pipe.out(SUPPORT) // range-nya dihilangkan karena ada permintaan list supportings ditampilkan semua
            .order { (a:Vertex, b:Vertex) =>
                a.getOrElse("user.lower-name", "").compareTo(b.getOrElse("user.lower-name", ""))
            }
            .range(offset, (offset + limit) - 1).iterator()
            .flatMap(_.toCC[User]).toSeq
    }

    /**
     * digunakan untuk search supporting langsung dari db
     * @param query query
     * @param offset
     * @param limit
     * @return
     */
    def searchSupporting(query: String, offset: Int, limit: Int): Seq[User] = timing("User.searchSupporting") {
        // @TODO(robin): optimize this, masih lambat
        getVertex.pipe.out(SUPPORT) // range-nya dihilangkan karena ada permintaan list supportings ditampilkan semua
            .filter { (v:Vertex) =>
                v.getOrElse("user.lower-name", "").contains(query.toLowerCase)
            }
            .range(offset, (offset + limit) - 1).iterator()
            .flatMap(_.toCC[User]).toSeq
    }

    /**
     * digunakan untuk search supporters langsung dari db
     * @param query query string
     * @param offset
     * @param limit
     * @return
     */
    def searchSupporters(query: String, offset: Int, limit: Int): Seq[User] = {
        // @TODO(robin): optimize this, masih lambat
        timing("User.searchSupporters") {
            getVertex.pipe.in(SUPPORT) // range-nya dihilangkan karena ada permintaan list supporters ditampilkan semua
            .filter { (v:Vertex) =>
                v.getOrElse("user.lower-name", "").contains(query.toLowerCase)
            }
            .order((a:Vertex,b:Vertex) => a.getOrElse("user.lower-name","").compareTo(b.getOrElse("user.lower-name","")))
            .range(offset, (offset + limit) - 1).iterator()
                .flatMap(_.toCC[User]).toSeq
        }
    }

    /**
     * Get user recommendation.
     * @param offset
     * @param limit
     * @return
     */
    def getRecommendation(offset: Int, limit: Int) = {
        User.getRecommendationList(this.asInstanceOf[User], limit)
    }

    /**
     * Digunakan untuk mendapatkan user recommendation berdasarkan nama
     * @param query query
     * @param offset
     * @param limit
     * @return
     */
    def getRecommendation(query: String, offset: Int, limit: Int) = {
        User.getRecommendationByName(query, this.asInstanceOf[User], offset, limit)
    }


    /**
     * Get mutual supporter relative to `toUser`.
     * @param toUser user to getting mutual supporter.
     * @return
     */
    def getMutualSupportersTo(toUser: User, offset: Int, limit: Int) = {
        User.getMutualSupporter(this.asInstanceOf[User], toUser, offset, limit)
    }

    /**
     * Get mutual supporting relative to `toUser`.
     * @param toUser user to getting mutual supporting.
     * @return
     */
    def getMutualSupportingsTo(toUser: User, offset: Int, limit: Int) = {
        User.getMutualSupporting(this.asInstanceOf[User], toUser, offset, limit)
    }

    /**
     * Get list of user who supported by current user and also support current user
     * @param offset
     * @param limit
     * @return
     */
    def getMutualSupport(offset: Int, limit: Int) = {
        User.getMutualSupport(this.asInstanceOf[User], offset, limit)
    }

    /**
     * Get count of user who supported by current user and also support current user
     * @return
     */
    def getMutualSupportCount: Long = {
        User.getMutualSupportCount(this.asInstanceOf[User])
    }

    /**
     * cek apakah this user dan toUser saling support
     * @param toUser
     * @return
     */
    def isMutualSupport(toUser: User): Boolean = {
        this.isSupport(toUser) && toUser.isSupport(this.asInstanceOf[User])
    }

    /**
     * untuk medapatkan jumlah mutual supporter
     * @param toUser
     * @return
     */
    def getMutualSupportersCount(toUser: User) = {
        User.getMutualSupporterCount(this.asInstanceOf[User], toUser)
    }

    /**
     * untuk medapatkan jumlah mutual supporting
     * @param toUser
     * @return
     */
    def getMutualSupportingsCount(toUser: User) = {
        User.getMutualSupportingCount(this.asInstanceOf[User], toUser)
    }

    /**
     * get group recommendation
     * @param offset
     * @param limit
     */
    def getChannelRecommendation(offset: Int, limit: Int) = {
        User.getChannelRecommendationList(this.asInstanceOf[User], offset, limit)
    }

    /**
     * Set user password.
     * @param plainPassword unencrypted/plain password.
     */
    def setPassword(plainPassword: String, autoCommit: Boolean = true) {
        val ph = Password.encrypt(plainPassword)
        passHash = ph
        passV = 3
        val v = db.getVertex(getId)
        v.setProperty("passHash", ph)
        v.setProperty("passV", passV)

        if (autoCommit) {
            db.commit()
        }
    }

    /**
     * Check is password match for current user password.
     * @param password plain password to check.
     * @return
     */
    def passwordMatch(password: String): Boolean = {
        Password.isMatch(password, passHash, passV)
    }

    /**
     * Block user
     * @param user User
     */
    def blockUser(user: User) {
        if (this == user)
            throw PermissionDeniedException("You can't block yourself")

        if (this.isBlocked(user))
            throw AlreadyExistsException("%s already blocked".format(user.name))

        if (isSupport(user))
            unsupport(user)

        tx { trx =>
            trx.getVertex(getId).addEdge(BLOCK, trx.getVertex(user.getId))
        }

        Digaku.engine.eventStream emit BlockUserEvent(this.asInstanceOf[User], user)
    }

    /**
     * Unblock user
     * @param user user @see [[com.ansvia.digaku.model.User]]
     */
    def unblockUser(user: User) {
        if (!this.isBlocked(user))
            throw PermissionDeniedException("user not being blocked")

        db.getVertex(this.getId).pipe.outE(BLOCK).as("ed").inV()
            .has("id", user.getId).back("ed").remove()

        db.commit()
    }

    /**
     * Memeriksa apakah user ini mem-blok user X (:user).
     * Mengembalikan true jika user diblock,
     * false jika tidak diblock
     * @param user user yang ingin diperiksa apakah diblok atau tidak.
     * @return true/false.
     */
    def isBlocked(user: User): Boolean = {
        tx { t =>
            t.getVertex(this.getId).pipe.out(BLOCK)
                .has("id", user.getId).iterator().hasNext
        }
    }

    /**
     * Mendapatkan dafatar user-user yang telah diblok.
     * @param offset offset user
     * @param limit limit user
     * @return
     */
    def getBlockedUsers(offset: Int, limit: Int): Seq[User] = {
        db.getVertex(this.getId).pipe.out(BLOCK)
            .range(offset, (offset + limit) - 1)
            .iterator()
            .flatMap(_.toCC[User])
            .toSeq
    }

    /**
     * Get blocked user by query
     * @param offset offset user
     * @param limit limit user
     * @param query query for search user
     * @return
     */
    def getBlockedUsers(query: String, offset: Int, limit: Int): Seq[User] = {
        db.getVertex(this.getId).pipe.out(BLOCK)
            .filter { (v: Vertex) =>
                v.getOrElse("user.lower-name", "").contains(query)
            }
            .range(offset, (offset + limit) - 1)
            .iterator()
            .flatMap(_.toCC[User])
            .toSeq
    }

    /**
     * Mendapatkan jumlah user yang di-block oleh user ini.
     * @return
     */
    def getBlockedUserCount(): Int = {
        db.getVertex(this.getId).pipe.outE(BLOCK).count().toInt
    }

    private def partIt(pipe: GremlinPipeline[Vertex, _ <: Element], partition: Int) = {
        if (partition != NotifPartition.ANY) {
            if (partition == NotifPartition.ALL_NOTICE_LEVEL) {
                pipe.interval("notification.partition", NotifPartition.LEVEL_1_NOTICE,
                    NotifPartition.LEVEL_4_NOTICE)
            } else
                pipe.has("notification.partition", partition)
        } else
            pipe
    }

    /**
     * Get current user notification list.
     * setiap record yang return dari sini perlu di-reload apabila ingin
     * menggunakan-nya karena record-nya berasal dari isolated transaction.
     * @param offset starting offset.
     * @param limit max limit.
     * @param state notification state @see [[com.ansvia.digaku.model.NotificationState]]
     *              default is NotificationState.UNREAD.
     * @param partition notification partition. @see [[com.ansvia.digaku.notifications.impl.NotifPartition]]
     *                  ex: for getting dialogue use NotifPartition.CONVERSATION
     * @return
     *
     */
    def getNotifications(offset: Int, limit: Int, state: Int = NotificationState.UNREAD,
                         partition: Int = NotifPartition.ANY): List[PersistentNotification] = {

        tx { t =>
            if (state == NotificationState.ALL) {
                var pipe = t.getVertex(getId).pipe.outE(NOTIFICATION)
                    .hasNot("notifKind", NotifGroup.ATTENTION)
                    .hasNot("notification.groupMerged", true) // jangan masukkan yang dah di merge oleh [[com.ansvia.digaku.notifications.impl.ResponseNotification.regroup]]

                pipe = partIt(pipe, partition)

                pipe.range(offset, offset + limit - 1)
                    .inV()
                    .iterator()
                    .toList.flatMap(NotificationMapper.generalMapper)

            } else {
                var pipe =
                    t.getVertex(getId).pipe.outE(NOTIFICATION)
                        .hasNot("notification.groupMerged", true) // jangan masukkan yang dah di merge oleh [[com.ansvia.digaku.notifications.impl.ResponseNotification.regroup]]
                        .has("read", {
                        state match {
                            case NotificationState.UNREAD => false
                            case NotificationState.READ => true
                        }
                    })

                pipe = partIt(pipe, partition)
                pipe.range(offset, offset + limit - 1)
                    .inV()
                    .iterator()
                    .toList
                    .flatMap(NotificationMapper.generalMapper)
            }
        }
    }

    /**
     * Mendapatkan daftar notification untuk dialogue
     * @param offset starting offset.
     * @param limit ends limit.
     * @param state @see [[com.ansvia.digaku.model.NotificationState]]
     * @return
     */
    def getDialogue(offset: Int, limit: Int, state: Int = NotificationState.UNREAD) = {
        getNotifications(offset, limit, state, NotifPartition.CONVERSATION)
    }

    /**
     * get attention type of notification.
     * @param offset starting offset.
     * @param limit max limit.
     * @param state notification state @see [[com.ansvia.digaku.model.NotificationState]]
     *              default is NotificationState.UNREAD.
     * @return
     */
    def getAttentionNotifications(offset: Int, limit: Int,
                                  state: Int = NotificationState.UNREAD): List[PersistentNotification with Attention] = {

        tx { t =>
            if (state == NotificationState.ALL) {
                t.getVertex(getId).pipe.outE(NOTIFICATION)
                    .has("notifKind", NotifGroup.ATTENTION)
                    .range(offset, offset + limit - 1)
                    .inV()
                    .iterator()
                    .toList
                    .flatMap(NotificationMapper.attentionMapper)

            } else {
                t.getVertex(getId).pipe.outE(NOTIFICATION)
                    .has("notifKind", NotifGroup.ATTENTION)
                    .range(offset, offset + limit -1)
                    .has("read", {
                    state match {
                        case NotificationState.UNREAD => false
                        case NotificationState.READ => true
                    }
                }).inV()//.asInstanceOf[GremlinPipeline[Vertex, Vertex]]
                    .iterator()
                    .toList
                    .flatMap(NotificationMapper.attentionMapper)
            }
        }
    }

    /**
     * Get unread notification count for this user.
     * ini termasuk unread pending notification + update notification
     * tapi tidak termasuk private message.
     * @return
     */
    def getUnreadNotificationCount: Int = {
        getUnreadGenericNotificationCount + getUnreadPendingNotificationCount()
    }

    // @TODO(ubai): tambahkan ke unittest
    /**
     * get pending notification count for this user
     * @param byIter - iterasi database untuk dapatkan count yang sebenarnya.
     * @return
     */
    def getUnreadPendingNotificationCount(byIter: Boolean = false): Int = {
        if (byIter) {
            tx { t =>
                partIt(t.getVertex(getId).pipe.outE(NOTIFICATION)
                    .has("notifKind", NotifGroup.ATTENTION)
                    .hasNot("read", true), NotifPartition.ALL_NOTICE_LEVEL)
                    .count().toInt
            }
        } else {
            counter.get("notif.pending").toInt
        }

    }

    // @TODO(ubai): tambahkan ke unittest
    /**
     * get unread update notification tidak termasuk pending notification
     * @return
     */
    def getUnreadGenericNotificationCount: Int = {
        counter.get("notif.generic").toInt
    }

    /**
     * Mendapatkan jumlah dialogue yang belum dibaca.
     * @return
     */
    def getUnreadDialogueCount: Int = {
        counter.get("notif.dialogue").toInt
    }

    /**
     * Mark notification read/unread.
     * @param state true = read, false = unread.
     */
    def markReadNotif(ntf: NotificationBase, state: Boolean) {
        tx { t =>
            t.getVertex(getId).pipe
                .outE(NOTIFICATION)
                .hasNot("read", state)
                .has("notifId", ntf.getId)
                .asInstanceOf[GremPipeEdge]
                .headOption.foreach { edge =>

                //            assert(edge != null, "No associated vertex for this notification: %s".format(getVertex))

                NotifUtil.recalcNotifCounter(this, edge)

                edge.setProperty("read", state)
            }
        }
    }

    def markReadNotifByIds(state: Boolean, ntfIds: Long*) {
        tx { t =>
            ntfIds.foreach { ntfId =>
                t.getVertex(getId).pipe
                    .outE(NOTIFICATION)
                    .has("notifId", ntfId)
                    .has("read", !state)
                    .asInstanceOf[GremPipeEdge]
                    .sideEffect { (edge: Edge) =>

                        NotifUtil.recalcNotifCounter(this, edge)

                        edge.setProperty("read", state)

                    }.iterate()
            }
        }
    }

    // @TODO(robin): optimize this
    /**
     * Mark all notification as read
     */
    def markAllNotifAsRead() {
        tx { t =>
            t.getVertex(getId).pipe.outE(NOTIFICATION)
                .hasNot("notifKind", NotifGroup.ATTENTION)
                .hasNot("notification.groupMerged", true) // jangan masukkan yang dah di merge oleh [[com.ansvia.digaku.notifications.impl.ResponseNotification.regroup]]
                .hasNot("read", true)
                .asInstanceOf[GremPipeEdge]
                .sideEffect((edge: Edge) => edge.setProperty("read", true))
                .iterate()
        }

        counter.decrementBy("notif.generic", counter.get("notif.generic"))
    }

    /**
     * digunakan untuk mark all pending notif
     */
    def markAllPendingNotifAsRead() {
        tx { t =>
            t.getVertex(getId).pipe.outE(NOTIFICATION)
                .has("notifKind", NotifGroup.ATTENTION)
                .has("read", false)
                .asInstanceOf[GremPipeEdge]
                .sideEffect((edge: Edge) => edge.setProperty("read", true))
                .iterate()
        }

        counter.decrementBy("notif.pending", counter.get("notif.pending"))
    }

    /**
     * Untuk menandai kalo dialogue sudah dibaca semua.
     */
    def markAllDialogueAsRead() {
        tx { t =>
            t.getVertex(getId).pipe.outE(NOTIFICATION)
                .has("notification.partition", NotifPartition.CONVERSATION)
                .has("read", false)
                .hasNot("notification.groupMerged", true) // jangan masukkan yang dah di merge oleh [[com.ansvia.digaku.notifications.impl.ResponseNotification.regroup]]
                .asInstanceOf[GremPipeEdge]
                .sideEffect((edge: Edge) => edge.setProperty("read", true))
                .iterate()
        }

        counter.decrementBy("notif.dialogue", counter.get("notif.dialogue"))
    }

    private val supportedRoles = Seq(UserRole.USER,
        UserRole.ADMIN, UserRole.SUPER_ADMIN)

    /**
     * Set user role.
     * @param _role role types @see [[com.ansvia.digaku.model.UserRole]]
     */
    def setRole(_role: Int, noTx: Boolean = false) {
        assert(supportedRoles.contains(_role), "Role not supported: " + _role)
        if (this.role != _role) {
            this.role = _role
            val v = db.getVertex(getId)
            v.setProperty("role", _role)
            if (!noTx)
                db.commit()
        }
    }

    /**
     * custom save routine, called when blueprint-scala
     * saving this record.
     * @return
     */
    override def __save__(v: Vertex) = {
        v.setProperty("noEmailNotifs", noEmailNotifs.filter(_.trim.length > 0)
            .map(_.toLowerCase).distinct.mkString(","))
        v.setProperty("privacys", privacys.filter(_.trim.length > 0).map(_.toLowerCase).distinct.mkString(","))
        if (v.getProperty[String]("user.lower-name") != name.toLowerCase.trim)
            v.setProperty("user.lower-name", name.toLowerCase.trim)
        val email = EmailValidator.normalizedEmail(emailLogin)
        if (v.getProperty[String]("user.email") != email)
            v.setProperty("user.email", email)
        if (mobilePhone == "") {
            mobilePhone = EmptyPhoneNumberGenerator.nextNumber
        }
        if (v.getProperty[String]("user.mobilePhone") != mobilePhone)
            v.setProperty("user.mobilePhone", mobilePhone)
    }

    /**
     * digunakan untuk get email activation id
     * @return see [[com.ansvia.digaku.model.UserEmailActivationId]]
     */
    def getEmailActivationId: Option[UserEmailActivationId] = {
        this.getVertex.pipe.out(EMAIL_VERIFICATION).headOption.flatMap(_.toCC[UserEmailActivationId])
    }

    /**
     * digunakan untuk get email change activation id
     * @return see [[com.ansvia.digaku.model.UserEmailChangeVerificationId]]
     */
    def getEmailChangeActivationId: Option[UserEmailChangeVerificationId] = {
        this.getVertex.pipe.out(CHANGE_EMAIL_VERIFICATION).headOption.flatMap(_.toCC[UserEmailChangeVerificationId])
    }

    /**
     * digunakan untuk check apakah user sudah memiliki email activation id
     * @return see [[com.ansvia.digaku.model.UserEmailActivationId]]
     */
    def hasEmailActivationId: Boolean = {
        this.getVertex.pipe.out(EMAIL_VERIFICATION).count() > 0
    }

    /**
     * digunakan untuk check apakah user sudah memiliki change email activation id
     * @return see [[com.ansvia.digaku.model.UserEmailChangeVerificationId]]
     */
    def hasChangeEmailActivationId: Boolean = {
        this.getVertex.pipe.out(CHANGE_EMAIL_VERIFICATION).has("oldVerification", false).count() > 0
    }

    /**
     * digunakan untuk check apakah user sudah memiliki change email activation id
     * dan sudah verifikasi email lamanya
     * @return see [[com.ansvia.digaku.model.UserEmailChangeVerificationId]]
     */
    def hasChangeOldEmailActivationId: Boolean = {
        this.getVertex.pipe.out(CHANGE_EMAIL_VERIFICATION).has("oldVerification", true).count() > 0
    }

    /**
     * digunakan untuk get reset password code atau linkCode
     * @return @see [[com.ansvia.digaku.model.UserResetPasswordCode]]
     */
    def getResetPasswordCode: Option[UserResetPasswordCode] = {
        this.getVertex.pipe.out(RESET_PASS_VERIFY).headOption.flatMap(_.toCC[UserResetPasswordCode])
    }

    /**
     * digunakan untuk check apakah user sudah memiliki reset password code
     * @return @see [[com.ansvia.digaku.model.UserResetPasswordCode]]
     */
    def hasResetPasswordCode: Boolean = {
        this.getVertex.pipe.out(RESET_PASS_VERIFY).count() > 0
    }

    // @TODO(robin): refactor: bersih-bersih ini
    /**
     * digunakan untuk mendapatkan channels target ketika user ingin move sebuah content
     * @param offset
     * @param limit
     * @param fromCh group asal dari content yang akan di move
     * @param query query untuk search group target
     * @return
     */
    def getChannelsToMovePost(offset: Int, limit: Int, fromCh: Option[Forum], query: String): Seq[Forum] = {
        var chsV = this.getVertex.pipe.outE(STAFF_AT)
            .filter { (edge: Edge) =>
                val chV = edge.getVertex(Direction.IN)
                edge.getOrElse("abilities", "").split(",").contains(Ability.MOVE_CONTENT) ||
                    chV.pipe.in(OWN).has("id", this.getId).hasNext
            }.inV()

        if (!query.trim.isEmpty) {
            chsV = chsV.filter { (v: Vertex) =>
                v.getOrElse("forum.lower-name", "").contains(query.toLowerCase)
            }
        }

        chsV.filter { (v:Vertex) =>
            v.getProperty[String]("_class_") match {
                case "com.ansvia.digaku.model.Forum" =>
                    //article hanya dapat dipindahkan ke forum yang mempunyai fitur artikel
                    v.toCC[Forum].exists(_.hasFeature(ForumFeatures.ARTICLE))
                case _ => false
            }
        }


        /*
        parameter fromCh diubah dari group menjadi Option[Group] karena fungsi ini dimanfaatkan oleh
        API untuk mendapatkan list semua group di awal apps di buka dan dilakukan caching.
         */
        if (fromCh.isDefined) {
            chsV.hasNot("id", fromCh.get.getId)
                .range(offset, (offset + limit) - 1)
                .iterator()
                .flatMap(_.toCC[Forum])
                .toSeq
        } else {
            chsV.range(offset, (offset + limit) - 1)
                .iterator()
                .flatMap(_.toCC[Forum])
                .toSeq
        }
    }

    /**
     * digunakan untuk mendapatkan jumlah channels target ketika user ingin move sebuah content
     * @param fromCh  group asal dari content yang akan di move
     * @param query query untuk search group target
     * @return
     */
    def getChannelsCountToMovePost(fromCh: Forum, query: String): Long = {
        this.reload()

        var chsV: GremPipeVertex = this.getVertex.pipe.outE(STAFF_AT)
            .filter { (edge: Edge) =>
                val chV = edge.getVertex(Direction.IN)
                edge.getOrElse("abilities", "").split(",").contains(Ability.MOVE_CONTENT) ||
                    chV.pipe.in(OWN).has("id", this.getId).hasNext
            }.inV()

        if (!query.trim.isEmpty) {
            chsV = chsV.filter { (v: Vertex) =>
                v.getOrElse("forum.lower-name", "").contains(query.toLowerCase)
            }
        }

        chsV.hasNot("id", fromCh.getId).count()
    }

    /**
     * digunakan untuk mendapatkan channels target ketika user ingin retalk sebuah content
     * @param offset
     * @param limit
     * @param fromCh group asal dari content yang akan di move
     * @param query query untuk search group target
     * @return
     */
    def getChannelsToRetalkPost(offset: Int, limit: Int, fromCh: Option[Forum], query: String): Seq[Forum] = tx { t =>
        var chsV = t.getVertex(getId).pipe.outE(STAFF_AT)
            .filter { (edge: Edge) =>
                val chV = edge.getVertex(Direction.IN)
                edge.getOrElse("abilities", "").split(",").contains(Ability.RETALK_POST) ||
                    chV.pipe.in(OWN).has("id", this.getId).hasNext
            }.inV().hasNot("supportRetalk", false)
            .asInstanceOf[GremPipeVertex]

        if (!query.trim.isEmpty) {
            chsV = chsV.filter { (v: Vertex) =>
                v.getOrElse("forum.lower-name", "").contains(query.toLowerCase)
            }
        }

        /*
        parameter fromCh diubah dari group menjadi Option[Group] karena fungsi ini dimanfaatkan oleh
        API untuk mendapatkan list semua group di awal apps di buka dan dilakukan caching.
         */
        if (fromCh.isDefined) {
            chsV.hasNot("id", fromCh.get.getId)
                .range(offset, (offset + limit) - 1)
                .iterator()
                .toList
                .flatMap(_.toCC[Forum])
                .filter(x => !x.deleted)
                .toSeq
        } else {
            chsV.range(offset, (offset + limit) - 1)
                .iterator()
                .toList
                .flatMap(_.toCC[Forum])
                .filter(x => !x.deleted)
                .toSeq
        }
    }

    /**
     * digunakan untuk mendapatkan count channels target ketika user ingin retalk sebuah content
     * @param fromCh  group asal dari content yang akan di move
     * @param query query untuk search group target
     * @return
     */
    def getChannelsCountToRetalkPost(fromCh: Forum, query: String) = tx { t =>
        var chsV = t.getVertex(getId).pipe.outE(STAFF_AT)
            .filter { (edge: Edge) =>
                val chV = edge.getVertex(Direction.IN)
                edge.getOrElse("abilities", "").split(",").contains(Ability.RETALK_POST) ||
                    chV.pipe.in(OWN).has("id", this.getId).hasNext
            }.inV().hasNot("supportRetalk", false)
            .asInstanceOf[GremPipeVertex]

        if (!query.trim.isEmpty) {
            chsV = chsV.filter { (v: Vertex) =>
                v.getOrElse("forum.lower-name", "").contains(query.toLowerCase)
            }
        }

        chsV.hasNot("id", fromCh.getId).count()
    }

    /**
     * Get counter provider used by this model.
     * @return
     */
    def getCounter: CounterProviderOperator = counter

    /**
     * Set user layout
     * should be called inside of transaction.
     * @param layout layout name.
     */
    def setLayout(layout: String) {
        if (!User.supportedLayouts.contains(layout))
            throw new NotExistsException("layout not supported " + layout + ", currently only support: " +
                User.supportedLayouts.reduceLeft(_ + ", " + _))
        this.layout = layout
        save()
    }

    override def toString = "User(%s,%s)".format(id, name)

    def generateEmailActivationId(): UserEmailActivationId = {
        UserEmailActivationId.create(this.reload())
    }

    def generateChangeEmailActivationId(newEmail: String): UserEmailChangeVerificationId = {
        UserEmailChangeVerificationId.create(this.reload(), newEmail)
    }

    /**
     * digunakan untuk generate reset password code yang nantinya
     * akan digunakan untuk dikirim ke email
     * @return @see [[com.ansvia.digaku.model.UserResetPasswordCode]]
     */
    def generateResetPasswordCode(): UserResetPasswordCode = {
        UserResetPasswordCode.create(this.reload())
    }

    /**
     * Mendapatkan birthdate dalam instance DateTime
     * @return
     */
    def getBirthDate = {
        val date = User.BIRTH_DATE_FORMAT.parseDateTime(birthDate)
        date
    }

    /**
     * Get user's age, calculated from
     * birthDate attribute.
     * @return
     */
    def age = {
        Years.yearsBetween(getBirthDate, new DateTime)
    }

    /**
     * Get owned Apps.
     * @param offset
     * @param limit
     * @return
     */
    def appList(offset: Int, limit: Int) = {
        import com.ansvia.digaku.model.Label.OWN
        import com.ansvia.graph.BlueprintsWrapper._
        import scala.collection.JavaConversions._

        getVertex.pipe.out(OWN).has("_class_", "com.ansvia.digaku.model.App")
            .range(offset, offset + limit - 1)
            .iterator().flatMap(_.toCC[App])
    }

    /**
     * Shout post.
     * Fungsi ini akan men-trigger event shout [[com.ansvia.digaku.event.impl.ShoutEvent]]
     * @param post post yang akan di-shout.
     * @param message shout message.
     */
    def shout(post: Post, message: String) {
        var edO: Option[Edge] = None

        edO = Some(this --> SHOUT --> post <())
        val ed = edO.get
        ed.setProperty("timeOrder", System.currentTimeMillis())
        ed.setProperty("message", message)

        db.commit()

        edO map { ed =>
            Digaku.engine.eventStream emit ShoutEvent(this, post, message, ed)
        }
    }

    /**
     * get sex in string representation.
     * @return
     */
    def sexStr = SexType.toStr(sex)

    /**
     * Digunakan untuk meng-ignore user
     * agar tidak lagi muncul sebagai recommendation
     * untuk current user.
     * @param userTarget target user yang akan
     *                   di-ignore.
     */
    def ignoreUserRecommendation(userTarget: User) {
        this --> IGNORE_RECOMMENDATION --> userTarget
        db.commit()
    }

    /**
     * Digunakan untuk meng-ignore user
     * agar tidak lagi muncul sebagai active user recommendation
     * untuk current user
     * @param userTarget
     * target user yang akan di ignore
     */
    def ignoreActiveUserRecommendation(userTarget: User) {
        this --> IGNORE_RECOMMENDATION --> userTarget
        db.commit()
    }

    /**
     * Digunakan untuk meng-connect-kan user
     * ke apps
     * salah satu kegunaanya adalah untuk push notification
     * @param to UserConnect kemana user akan di-connect-kan
     */
    def connectApp(to: UserConnect) {
        if (this.getVertex.pipe.out(CONNECT_APPS)
            .has("user-connect.via", to.via).count() > 0) {
            disconnectAppByProvider(to.via)
        }

        to.save()

        this --> CONNECT_APPS --> to

        db.commit()
    }

    /**
     * Digunakan untuk melakukan revoke app connection
     * @param service string dari provider
     */
    def disconnectAppByProvider(service: String) {
        val v = this.getVertex.pipe.out(CONNECT_APPS)
            .has("user-connect.via", service)

        if (v.count <= 0) {
            throw NotExistsException(s"User not connected to $service")
        }

        try {
            tx { t =>
                // ambil dulu semua vertexnya
                val connectAppsV = t.getVertex(getId).pipe.out(CONNECT_APPS)
                    .has("user-connect.via", service).dedup()
                    .asInstanceOf[GremPipeVertex].iterator().toList

                // hapus dulu semua edges nya
                t.getVertex(getId).pipe.outE(CONNECT_APPS).as("ed").inV()
                    .has("user-connect.via", service).back("ed").remove()

                // kemudia hapus semua vertex nya
                connectAppsV.foreach { v =>
                    v.remove()
                }
            }
        } catch {
            case e: Exception =>
                error(e.getMessage)
                error(e.getStackTraceString)
        }
    }

    /**
     * Digunakan untuk mendapatkan user terkoneksi ke aplikasi
     * @param service string nama aplikasi yang akan dicek
     * @return
     */
    def getConnectedApp(service: String): Option[UserConnect] = {
        if (this.getVertex.pipe.out(CONNECT_APPS).has("user-connect.via", service).count() > 0) {
            this.getVertex.pipe.out(CONNECT_APPS)
                .has("user-connect.via", service).headOption.flatMap(_.toCC[UserConnect])
        } else {
            None
        }
    }

    /**
     * Digunakan untuk mendapatkan daftar koneksi user ke aplikasi
     * @return Iterator dari applikasi
     */
    def getConnectedApp: Iterator[UserConnect] = {
        tx { t =>
            t.getVertex(getId).reload().pipe.out(CONNECT_APPS).iterator().flatMap(_.toCC[UserConnect])
        }
    }

    /**
     * Untuk memeriksa apakah user mempunyai profile picture yang active
     * atau tidak.
     *
     * @return
     */
    def hasActiveProfilePicture = {
        reload().getVertex.pipe.out(HAS_PROFILE_PICTURE)
            .has("active", true).iterator().hasNext()
    }

    /**
     * Untuk memeriksa apakah user mempunyai profile picture Default
     *
     * @return
     */
    def hasActiveProfilePictureDefault = {
        reload().getVertex.pipe.out(HAS_PROFILE_PICTURE_DEFAULT).iterator().hasNext()
    }

    /**
     * Create profile picture
     *
     * @param photoLarge large pic url
     * @param withActivatePic activated profile pic if true
     */
    def createProfilePicture(photoLarge: String, withActivatePic: Boolean = true): ProfilePic = {
        val profPic = ProfilePic.create(photoLarge, this)
        if (withActivatePic)
            setActiveProfilePic(profPic)

        profPic
    }

    /**
     * Mendapatkan profile picture yang lagi di pakai
     * @return
     */
    def getActiveProfilePic: Option[ProfilePic] = {
        reload().getVertex.pipe.out(HAS_PROFILE_PICTURE).has("active", true)
            .asInstanceOf[GremPipeVertex]
            .headOption.flatMap(_.toCC[ProfilePic])
    }

    /**
     * Mendapatkan history dari profile picture yang sedang digunakan
     * dan diorder berdasarkan yang terakhir digunakan
     * @return
     */
    def getActiveHistoryProfilePic: Option[ProfilePicHistory] = {
        reload().getVertex.pipe.out(HAS_PROFILE_PICTURE).has("active", true)
            .out(PROFILE_PIC_HISTORY)
            .order { (a: Vertex, b: Vertex) =>
                -a.getOrElse("usageTime", 0L).compareTo(b.getOrElse("usageTime", 0L))
            }
            .asInstanceOf[GremPipeVertex]
            .headOption.flatMap(_.toCC[ProfilePicHistory])
    }

    /**
     * Untuk mengeset profile picture dalam parameter menjadi
     * active profile picture
     * @param profilePic
     * @return
     */
    def setActiveProfilePic(profilePic: ProfilePic): Boolean = {
        // set all other profile picture to false
        getAllProfilePic foreach { profilePicture =>
            profilePicture.setActive(state = false)
        }

        // set this particular photo to active
        profilePic.setActive(state = true)

        true
    }

    /**
     * Mendapatkan profile picture default yang terhubung dengan user
     * @return
     */
    def getActiveProfilePicDefault: Option[ProfilePicDefault] = {
        reload().getVertex.pipe.out(HAS_PROFILE_PICTURE_DEFAULT)
            .asInstanceOf[GremPipeVertex]
            .headOption.flatMap(_.toCC[ProfilePicDefault])
    }

    /**
     * Mendapatkan semua photo profile current user
     * @return
     */
    def getAllProfilePic: Iterator[ProfilePic] = {
        reload().getVertex.pipe.out(HAS_PROFILE_PICTURE)
            .iterator().flatMap(_.toCC[ProfilePic])
    }

    /**
     * Mendapatkan historical photo profile
     * @param withCurrentActivePic include currently active profile picture or not
     * @return
     */
    def getHistoricalProfilePic(withCurrentActivePic: Boolean = false): Iterator[ProfilePic] = {
        getHistoricalPPTime(withCurrentActivePic).map(_.picture)
    }

    /**
     * Mendapatkan historical time dari photo profile
     * @param withCurrentActivePic include currently active profile picture or not
     * @return
     */
    def getHistoricalPPTime(withCurrentActivePic: Boolean = false): Iterator[ProfilePicHistory] = {
        val ppHistoryVertex = if (withCurrentActivePic) {
            reload().getVertex.pipe.out(HAS_PROFILE_PICTURE).out(PROFILE_PIC_HISTORY)
        } else {
            reload().getVertex.pipe.out(HAS_PROFILE_PICTURE).has("active", false).out(PROFILE_PIC_HISTORY)
        }

        ppHistoryVertex
            .order { (a: Vertex, b: Vertex) =>
                -a.getOrElse("usageTime", 0L).compareTo(b.getOrElse("usageTime", 0L))
            }
            .iterator().flatMap(_.toCC[ProfilePicHistory])
    }

    /**
     * Merubah photo profile
     * dengan photo profile yang pernah di upload
     * @param pic
     */
    def changeProfilePic(pic: ProfilePic) {
        /**
         * Jika punya profile picture yang active
         * di non active kan
         */
        if (hasActiveProfilePicture) {
            getActiveProfilePic foreach { profile_pic =>
                profile_pic.setActive(false)
            }
        }

        ProfilePicHistory.create(pic, Digaku.engine.dateUtils.nowMilis)

        pic.setActive(true)
        this.photoSmall = pic.smallUrl
        this.photoMedium = pic.mediumUrl
        this.photoLarge = pic.originalUrl
        this.save()

        db.commit()
    }

    /**
     * Merubah photo profile
     * dengan photo profile default dari system
     * @param pic
     */
    def changeProfilePicDefault(pic: ProfilePicDefault) {
        /**
         * Jika punya profile picture yang active
         * di non active kan
         */
        if (hasActiveProfilePicture) {
            getActiveProfilePic foreach { profile_pic =>
                profile_pic.setActive(false)
            }
        }

        if (hasActiveProfilePictureDefault) {
            //            transact {
            this.getVertex.pipe.outE(HAS_PROFILE_PICTURE_DEFAULT).remove()
            //            }
            db.commit()
        }

        setDefaultAvatar(pic)
    }

    /**
     * Mendapatkan jumlah photo profile yang sudah pernah di upload
     * @return
     */
    def getProfilePictureCount(withCurrentActivePic: Boolean = false) = {
        if (withCurrentActivePic) {
            reload().getVertex.pipe.out(HAS_PROFILE_PICTURE).count()
        } else {
            reload().getVertex.pipe.out(HAS_PROFILE_PICTURE).has("active", false).count()
        }
    }

    /**
     * set default photo profile user
     * @param pic profile picture default (jika kosong akan di random dari profile picture default yang ada)
     */
    def setDefaultAvatar(pic: ProfilePicDefault = null) {
        if (pic == null) {
            val profilePicCount = ProfilePicDefault.getCount
            if (profilePicCount > 0) {
                val num =
                    if (profilePicCount > 1)
                        (this.name.map(_.toInt).sum % profilePicCount).toInt
                    else
                        0

                val avatar = ProfilePicDefault.getListRight(None, None, 100).toList(num)
                val avatarV = avatar.getVertex
                val edHasDefPic = this --> HAS_PROFILE_PICTURE_DEFAULT --> avatarV <()
                edHasDefPic.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)

                photoSmall = avatar.smallUrl
                photoMedium = avatar.mediumUrl
                photoLarge = avatar.originalUrl
                save()
            }
        } else {
            val avatarV = pic.getVertex
            val edHasDefPic = this --> HAS_PROFILE_PICTURE_DEFAULT --> avatarV <()
            edHasDefPic.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)

            photoSmall = pic.smallUrl
            photoMedium = pic.mediumUrl
            photoLarge = pic.originalUrl
            save()

            db.commit()
        }
    }

    /**
     * digunakan untuk mendapatkan seluruh trophy yang ada di User
     * @return
     */
    def getTrophies: Iterator[Trophy] = {
        getVertex.pipe.outE(HAS_TROPHY)
            .order{ (a:Edge, b:Edge) =>
                - a.getOrElse("timeOrder", 0L).compareTo(b.getOrElse("timeOrder", 0L))
            }.inV().iterator().flatMap(_.toCC[Trophy])
    }

    /**
     * mendapatkan trophy berdasarkan group tertentu
     * @param trophyGroup group dari trophy see [[com.ansvia.digaku.model.TrophyGroup]]
     * @return
     */
    def getTrophies(trophyGroup: String): Iterator[Trophy] = {
        if (!TrophyGroup.getTrophyGroups.contains(trophyGroup))
            throw InvalidParameterException("Invalid trophy group")

        getVertex.pipe.out(HAS_TROPHY).has("trophyGroup", trophyGroup)
            .asInstanceOf[GremPipeVertex].iterator().flatMap(_.toCC[Trophy])
    }

    /**
     * Get Trophy list
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def getTrophies(offset: Int, limit: Int): Iterator[Trophy] = {
        getVertex.pipe.outE(HAS_TROPHY)
            .order{ (a:Edge, b:Edge) =>
                - a.getOrElse("timeOrder", 0L).compareTo(b.getOrElse("timeOrder", 0L))
            }.inV().range(offset, offset + limit - 1).iterator().flatMap(_.toCC[Trophy])
    }

    private val creditKey = "credit"
    private val articleCountKey = "articleCount"
    private val publishedContentKey = "published_content"
    private val publishedSimpleTextKey = "simple_text"
    private val userReplyCountKey = "userReplyCount"
    private val followingForumStreamKey = "followingForumStream"
    private val followingForumKey = "followingForum"
    private val monitorCountKey = "monitorCount"

    /** **********************************************
      * USER CREDIT
      * **********************************************/

    /**
     * Get current credit for this user.
     * @return
     */
    def getCredit = {
        counter.get(creditKey)
    }

    /**
     * Increment credit by :count for this user.
     * @param count
     * @return
     */
    def incCredit(count: Int) = {
        counter.incrementBy(creditKey, count)
        counter.get(creditKey)
    }

    /**
     * Decrement credit by :count for this user.
     * @param count
     * @return
     */
    def decCredit(count: Int) = {
        counter.decrementBy(creditKey, count)
        counter.get(creditKey)
    }

    def incrementPublishedContentCount(by: Int = 1) = {
        counter.incrementBy(publishedContentKey, by.toLong)
        counter.get(publishedContentKey)
    }

    def decrementPublishedContentCount(by: Int = 1) = {
        counter.decrementBy(publishedContentKey, by.toLong)
        counter.get(publishedContentKey)
    }

    def getPublishedContentCount = counter.get(publishedContentKey)

    def incrementReplyCount(by: Int = 1) = {
        counter.incrementBy(userReplyCountKey, by.toLong)
        counter.get(userReplyCountKey)
    }

    def decrementReplyCount(by: Int = 1) = {
        counter.decrementBy(userReplyCountKey, by.toLong)
        counter.get(userReplyCountKey)
    }

    def getReplyCount = counter.get(userReplyCountKey)

    /**
     * digunakan untuk mendapatkan jumlah keseluruhan post dan thread
     * yang dibuat oleh user terkait
     * @return
     */
    def getPostCount = getArticleCount + getReplyCount

    def incrementArticleCount(count: Int = 1) = {
        counter.incrementBy(articleCountKey, count)
        counter.get(articleCountKey)
    }

    def decrementArticleCount(count: Int = 1) = {
        counter.decrementBy(articleCountKey, count)
        counter.get(articleCountKey)
    }

    def getArticleCount = {
        counter.get(articleCountKey)
    }


    def incrementSimpleTextCount(count: Int = 1) = {
        counter.incrementBy(publishedSimpleTextKey, count)
        counter.get(publishedSimpleTextKey)
    }

    def decrementSimpleTextCount(count: Int = 1) = {
        counter.decrementBy(publishedSimpleTextKey, count)
        counter.get(publishedSimpleTextKey)
    }

    def getSimpleTextCount = {
        counter.get(publishedSimpleTextKey)
    }

    def getExperience = {
        counter.get("experience").toInt
    }

    def increaseExperience(incBy:Int){
        val prevRank = getRank
        counter.incrementBy("experience", incBy)

        if (prevRank != getRank) {
            getRank.foreach { rank =>
                Digaku.engine.eventStream.emit(NewRankEvent(this, rank))
            }
        }
    }

    def decreaseExperience(decBy:Int) = {
        counter.decrementBy("experience", decBy)
    }

    def getRank = {
        ExperienceRank.calcRank(getExperience)
    }

    def contentQuery() = {
        new ArticleQueryBuilder()
            .terms("creatorId", this.getId.toString)
    }

    /**
     * Digunakan untuk menyimpan jumlah rate yang di terima
     * oleh user ini, tidak peduli berapa pun nilai rate nya
     * tiap rate dihitung sebagai 1
     * salah satu kegunannya adalah untuk badges/trophy
     * @param incBy
     */
    def increaseRaterCount(incBy:Int) = {
        counter.incrementBy("rate-count", incBy)
    }

    def decreaseRaterCount(decBy:Int) = {
        counter.decrementBy("rate-count", decBy)
    }

    /**
     * Increment jumlah post yang di subscribe
     * @param count
     * @return
     */
    def incrementMonitorCount(count: Int = 1) = {
        counter.incrementBy(monitorCountKey, count)
        counter.get(monitorCountKey)
    }

    /**
     * Decrement jumlah post yang di subscribe
     * @param count
     * @return
     */
    def decrementMonitorCount(count: Int = 1) = {
        counter.decrementBy(monitorCountKey, count)
        counter.get(monitorCountKey)

    }

    /**
     * list post yang dimonitor user
     * @param offset from
     * @param limit to
     * @return
     */
    def getMonitoredPosts(offset: Int, limit: Int): Seq[Post]  = {
        getVertex.pipe.out(Label.MONITOR)
            .range(offset, (offset + limit) - 1)
            .iterator()
            .toSeq
            .flatMap(_.toCC[Post])
    }

    /**
     * get number of monitored post
     * @return
     */
    def getMonitorCount(): Long = {
        counter.get(monitorCountKey)
    }

    /**
     * Mendapatkan user group list dimana user ini
     * sudah dijadikan sebagai member
     * @param offset
     * @param limit
     * @return
     */
    def getUserGroups(offset:Int, limit:Int):Seq[UserGroup] = {
        this.getVertex.pipe.out(USER_GROUP_MEMBER)
            .range(offset, (offset + limit) - 1)
            .iterator()
            .flatMap(_.toCC[UserGroup])
            .toSeq
    }

    /**
      * Mendapatkan list user group yang dimana user bisa
      * post sebagai user group tersebut.
      * @param offset
      * @param limit
      * @return
      */
    def getPostAllowedUserGroup(offset:Int, limit:Int): Seq[UserGroup] = {
        this.getVertex.pipe.outE(USER_GROUP_MEMBER)
            .has("postAsGroup", true)
            .inV()
            .range(offset, (offset + limit) - 1)
            .iterator()
            .flatMap(_.toCC[UserGroup])
            .toSeq
    }

    /**
     * Mendapatkan jumlah grup pada user yang sudah dijadikan member
     * @return
     */
    def getUserGroupCount():Long = {
        this.getVertex.pipe.out(USER_GROUP_MEMBER).count()
    }
}


/**
 * semua implementasi static User ini
 * ada di [[com.ansvia.digaku.dao.UserDao]]
 * ini untuk mempermudah akses Dao menggunakan nama modelnya.
 */
object User extends UserDao[IDType, GraphType] {
    val supportedLayouts = Seq("dashboard", "classic")
    val BIRTH_DATE_FORMAT = DateTimeFormat.forPattern("dd/MM/yyyy")
    val JOIN_DATE_FORMAT = DateTimeFormat.forPattern("yyyy/MM/ddd")
    val MAX_BOOKMARK = 8

    lazy val stringCodeGen = new RandomStringGenerator()
}


case class UserEmailActivationId(code: String,
                                 linkCode: String) extends BaseModel[IDType] with DbObject with DbAccess {


    /**
     * Get linked user by this code.
     */
    lazy val pairedUser: Option[User] = {
        getVertex.pipe.in(EMAIL_VERIFICATION).range(0, 0).headOption.flatMap(_.toCC[User])
    }
}


private[model] trait CodeGenerator {

    object codeGenerator extends TokenIdGenerator {
        def generate() = {
            nextId().toUpperCase.replaceAll("[LO0IJ]+", "").substring(0, 5)
        }
    }

}


object UserEmailActivationId extends DaoBase[GraphType, UserEmailActivationId] with DbAccess with CodeGenerator {

    import com.ansvia.graph.BlueprintsWrapper._
    import scala.collection.JavaConversions._

    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.UserEmailActivationIdRootVertex"

    val linkCodeText = "linkCode"


    /**
     * Create user email activation ID
     * @param user user @see [[com.ansvia.digaku.model.User]]
     * @return
     */
    def create(user: User): UserEmailActivationId = {

        if (user.hasEmailActivationId)
            throw new DigakuException("user already has the email activation code")

        var verificationId: UserEmailActivationId = null

        // clean up code to only for alpha numeric
        val code = codeGenerator.generate()

        val _verificationId = UserEmailActivationId(code, createVerificationIdCode())
        verificationId = _verificationId.save().toCC[UserEmailActivationId]
            .getOrElse(throw new DigakuException("cannot create UserEmailActivationId"))

        user.getVertex --> EMAIL_VERIFICATION --> verificationId

        addToRoot(verificationId.getVertex)
        db.commit()

        verificationId
    }

    private lazy val codeGen = new RandomStringGenerator()

    private def createVerificationIdCode() = {
        val rv = codeGen.nextId()
        rv
    }

    def isLinkCodeValid(linkCode: String) = {
        rootVertex.pipe.out(rootVertexLabel)
            .has(linkCodeText, linkCode)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .iterator().hasNext()
    }

    def getByLinkCode(linkCode: String) = {
        rootVertex.pipe.out(rootVertexLabel)
            .has(linkCodeText, linkCode)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .headOption.flatMap(_.toCC[UserEmailActivationId])
    }

    def getByCode(code: String): Option[UserEmailActivationId] = {
        rootVertex.pipe.out(rootVertexLabel)
            .has("code", code)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .headOption.flatMap(_.toCC[UserEmailActivationId])
    }

    /**
     * Delete email activation ID by code.
     * @param code
     */
    def deleteByCode(code: String) {
        getByCode(code) foreach { vid =>
            vid.getVertex.pipe.bothE().remove()
            db.removeVertex(vid.getVertex)
        }

        db.commit()
    }
}


case class UserEmailChangeVerificationId(code: String, linkCode: String, newEmail: String,
                                         oldCode: String, oldLinkCode: String, oldEmail: String, oldVerification: Boolean)
    extends BaseModel[IDType] with DbObject with DbAccess {


    /**
     * Get linked user by this code.
     */
    lazy val pairedUser: Option[User] = {
        getVertex.pipe.in(CHANGE_EMAIL_VERIFICATION).range(0, 0).headOption.flatMap(_.toCC[User])
    }
}


object UserEmailChangeVerificationId extends DaoBase[GraphType, UserEmailChangeVerificationId]
    with DbAccess with CodeGenerator {

    import com.ansvia.graph.BlueprintsWrapper._
    import scala.collection.JavaConversions._

    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.UserEmailChangeActivationIdRootVertex"

    /**
     * Create user change email activation ID
     * @param user user see [[com.ansvia.digaku.model.User]]
     * @return
     */
    def create(user: User, newEmail: String): UserEmailChangeVerificationId = {

        if (user.hasChangeEmailActivationId)
            throw new DigakuException("user already has the change email activation code")

        var verificationId: UserEmailChangeVerificationId = null

        val code = codeGenerator.generate()

        val _verificationId = UserEmailChangeVerificationId(code, code, newEmail,
            code, code, user.emailLogin, oldVerification = false)

        verificationId = _verificationId.save().toCC[UserEmailChangeVerificationId]
            .getOrElse(throw new DigakuException("cannot create UserEmailChangeVerificationId"))

        user.getVertex --> CHANGE_EMAIL_VERIFICATION --> verificationId

        addToRoot(verificationId.getVertex)
        db.commit()

        verificationId
    }

    // old email verification
    def isOldLinkCodeValid(linkCode: String) = {
        rootVertex.pipe.out(rootVertexLabel)
            .has("oldLinkCode", linkCode)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .iterator().hasNext
    }

    def getByOldLinkCode(linkCode: String) = {
        rootVertex.pipe.out(rootVertexLabel)
            .has("oldLinkCode", linkCode)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .headOption.flatMap(_.toCC[UserEmailChangeVerificationId])
    }

    def getByOldCode(code: String): Option[UserEmailChangeVerificationId] = {
        rootVertex.pipe.out(rootVertexLabel)
            .has("oldCode", code)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .headOption.flatMap(_.toCC[UserEmailChangeVerificationId])
    }

    def getByOldEmail(newEmail: String): Option[UserEmailChangeVerificationId] = {
        rootVertex.pipe.out(rootVertexLabel)
            .has("oldEmail", newEmail)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .headOption.flatMap(_.toCC[UserEmailChangeVerificationId])
    }

    def setOldVerification(oldEmail: String, status: Boolean) {
        getByOldEmail(oldEmail).foreach { vid =>
            vid.getVertex.setProperty("oldVerification", status)
            db.commit()
        }
    }

    /**
     * Delete email activation new Email
     * @param newEmail
     */
    def deleteByOldEmail(newEmail: String) {
        getByOldEmail(newEmail).map { vid =>
            vid.getVertex.pipe.bothE().remove()
            db.removeVertex(vid.getVertex)
        }

        db.commit()
    }

    /**
     * Delete email activation ID by code.
     * @param code
     */
    def deleteByOldCode(code: String) {
        getByOldCode(code).foreach { vid =>
            vid.getVertex.pipe.bothE().remove()
            db.removeVertex(vid.getVertex)
        }

        db.commit()
    }

    // new email verification
    def isLinkCodeValid(linkCode: String) = {
        rootVertex.pipe.out(rootVertexLabel)
            .has("linkCode", linkCode)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .iterator().hasNext()
    }

    def getByLinkCode(linkCode: String) = {
        rootVertex.pipe.out(rootVertexLabel)
            .has("linkCode", linkCode)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .headOption.flatMap(_.toCC[UserEmailChangeVerificationId])
    }

    def getByCode(code: String): Option[UserEmailChangeVerificationId] = {
        rootVertex.pipe.out(rootVertexLabel)
            .has("code", code)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .headOption.flatMap(_.toCC[UserEmailChangeVerificationId])
    }

    def getByNewEmail(newEmail: String): Option[UserEmailChangeVerificationId] = {
        rootVertex.pipe.out(rootVertexLabel)
            .has("newEmail", newEmail)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .headOption.flatMap(_.toCC[UserEmailChangeVerificationId])
    }

    /**
     * Delete email activation new Email
     * @param newEmail
     */
    def deleteByNewEmail(newEmail: String) {
        getByNewEmail(newEmail).foreach { vid =>
            vid.getVertex.pipe.bothE().remove()
            db.removeVertex(vid.getVertex)
        }

        db.commit()
    }

    /**
     * Delete email activation ID by code.
     * @param code
     */
    def deleteByCode(code: String) {
        getByCode(code).foreach { vid =>
            vid.getVertex.pipe.bothE().remove()
            db.removeVertex(vid.getVertex)
        }

        db.commit()
    }
}


case class UserPhoneActivationId(code: String) extends BaseModel[IDType]
    with DbObject with DbAccess {

    /**
     * Get linked user by this code.
     */
    lazy val pairedUser: Option[User] = {
        getVertex.pipe.in(PHONE_VERIFICATION).range(0, 0).headOption.flatMap(_.toCC[User])
    }
}


case class UserResetPasswordCode(code: String, linkCode: String) extends BaseModel[IDType]
    with DbObject with DbAccess {

    /**
     * Get linked user by this code.
     */
    lazy val pairedUser: Option[User] = {
        getVertex.pipe.in(RESET_PASS_VERIFY).range(0, 0).headOption.flatMap(_.toCC[User])
    }
}


object UserResetPasswordCode extends DaoBase[GraphType, UserResetPasswordCode] with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._
    import scala.collection.JavaConversions._

    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.UserResetPasswordCodeRootVertex"

    /**
     * Create user reset password code
     * @param user user @see [[com.ansvia.digaku.model.User]]
     * @return
     */
    def create(user: User): UserResetPasswordCode = {
        if (user.hasResetPasswordCode)
            throw new DigakuException("user already has a reset password code")

        var phoneVerificationId: UserResetPasswordCode = null

        val _verificationId = UserResetPasswordCode(StringCodeGenerator.nextId(), createVerificationIdCode())
        phoneVerificationId = _verificationId.save().toCC[UserResetPasswordCode]
            .getOrElse(throw new DigakuException("cannot create UserResetPasswordCode"))

        user.getVertex --> RESET_PASS_VERIFY --> phoneVerificationId

        addToRoot(phoneVerificationId.getVertex)

        db.commit()

        phoneVerificationId
    }

    private lazy val codeGen = new RandomStringGenerator()

    private def createVerificationIdCode() = {
        val rv = codeGen.nextId()
        rv
    }

    def isLinkCodeValid(linkCode: String) = {
        rootVertex.pipe.out(rootVertexLabel)
            .has("linkCode", linkCode)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .iterator().hasNext()
    }

    def getByLinkCode(linkCode: String) = {
        rootVertex.pipe.out(rootVertexLabel)
            .has("linkCode", linkCode)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .headOption.flatMap(_.toCC[UserResetPasswordCode])
    }

    def getByCode(code: String): Option[UserResetPasswordCode] = {
        rootVertex.pipe.out(rootVertexLabel)
            .has("code", code)
            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .headOption.flatMap(_.toCC[UserResetPasswordCode])
    }

    /**
     * Delete reset password code id by code.
     * @param code
     */
    def deleteByCode(code: String) {
        getByCode(code).map { vid =>
            vid.getVertex.pipe.bothE()
                .asInstanceOf[GremlinPipeline[Vertex, Edge]]
                .iterator()
                .foreach(db.removeEdge)

            db.removeVertex(vid.getVertex)
        }
        db.commit()
    }
}


/**
 * representasi class untuk kode invitation user.
 * ini bukan model hanya wrapper untuk edge [[com.ansvia.digaku.model.Label.INVITE]]
 * @param code kode invitation (unik dan autogenerated).
 * @param kind jenis invitation.
 * @param edge [[com.tinkerpop.blueprints.Edge]]
 */
case class UserInvitationCode(code: String, kind: Int, edge: Edge)


abstract class Reputation(val value:Int)


object Reputation {
    val counterKey = "reputation"
    object Good extends Reputation(1)
    object Bad extends Reputation(-1)
}
