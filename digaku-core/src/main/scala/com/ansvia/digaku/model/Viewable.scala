/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.graph.AbstractDbObject
import com.ansvia.graph.BlueprintsWrapper.IdDbObject
import com.tinkerpop.blueprints.Vertex
import com.ansvia.digaku.database.GraphCompat._
import Label._
import com.ansvia.graph.BlueprintsWrapper._


/** $regression:$ Fungsional jumlah view di viewable content seperti post.
 */
/**
 * Author: robin
 *
 */
trait Viewable[idT] extends IdDbObject[idT] with CollectionVertex {

    this: BaseModel[idT] with Counter =>

    /**
     * untuk nge-track jumlah view di post.
     */
    // deprecated, digantikan menggunakan counter provider
//    @Persistent var viewsCount:Int = 0

//    private lazy val counter = Digaku.engine.counterProvider("viewable-attrs-" + getId)

    private lazy val counter = getCounter

    private lazy val viewers = Digaku.engine.seqStoreProvider.build("viewer-ids-" + this.getId,
        ()=> Digaku.engine.idFactory.createId().asInstanceOf[Long])

    /**
     * Untuk mendapatkan jumlah views
     * @return
     */
    def viewsCount = {
        counter.get("views_count").toInt
    }

    /**
     * Untuk menambahkan views count dengan 1.
     */
    def incrementViews(){
        counter.increment("views_count")
    }

    /**
     * Untuk menambahkan jumlah views count dengan :by.
     * @param by jumlah views count yang ingin ditambahkan.
     */
    def incrementViewsBy(by:Int){
        counter.incrementBy("views_count", by)
    }

    /**
     * add viewer for this object
     * @param viewer see[[com.ansvia.digaku.model.User]]
     */
    def addViewer(viewer:User): Unit = {
        if (!isViewer(viewer)) {
            viewers.insert(viewer.getId, viewer.getId.toString)
            incrementViews()

            this.asInstanceOf[Viewable[idT]] match {
                case article:Post =>
                    tx { implicit t =>
                        val _viewer = getFromTx(viewer, t)
                        val dateUtil = Digaku.engine.dateUtils
                        val ed = _viewer.getYMPartEx(CollectionVertex.Kind.VIEW, dateUtil.getCurrentYear,
                            dateUtil.getCurrentMonth, t).addEdge(VIEW, t.getVertex(this.getId))

                        ed.set("day", dateUtil.getCurrentDayOfMonth)
                        ed.setProperty("creationTime", dateUtil.nowMilis)
                    }
                case _ =>
            }
        }
    }

    /**
     * check if user is viewer for this object
     * @param viewer see[[com.ansvia.digaku.model.User]]
     * @return
     */
    def isViewer(viewer:User):Boolean = {
        viewers.get(viewer.getId).isDefined
    }

    def getViewers(fromId:Option[java.lang.Long], toId:Option[java.lang.Long], limit:Int) = {
        viewers.getStream(fromId, toId, limit)
    }

    override def __save__(vertex: Vertex): Unit = {
        super.__save__(vertex)

        // masih tetap perlu ini untuk operasi OLAP seperti mendapatkan popular pictures
        vertex.setProperty("viewsCount", viewsCount)
    }
}
/*$regression:end$*/



//
//object ViewerIds extends Slf4jLogger {
//
//    private val idGen = new SnowFlakeIdFactory(Digaku.engine.config.machineId)
//
//    def generateId(): Long = idGen.createId().asInstanceOf[Long]
//
//    private lazy val CF = new ColumnFamily[String, java.lang.Long](
//        Global.STANDARD_CF_NAME,
//        StringSerializer.get(),
//        LongSerializer.get(),
//        ByteSerializer.get())
//
//    val seqStore = new CassandraBackedSeqStore(CF,
//        Digaku.config.mainDatabase.keyspaceName,
//        Digaku.config.mainDatabase.clusterName,
//        Digaku.config.mainDatabase.hostName,
//        Digaku.config.mainDatabase.replStrategy,
//        Digaku.config.mainDatabase.replStrategyOpts)
//        .setComparatorType("LongType(reversed=true)")
//}
