///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Types._
//import com.ansvia.graph.BlueprintsWrapper.DbObject
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.dao.MessageResponseDao
//import scala.collection.JavaConversions._
//import org.ocpsoft.prettytime.PrettyTime
//import java.util.Date
//import com.ansvia.graph.annotation.Persistent
//import com.tinkerpop.blueprints.Vertex
//import com.ansvia.digaku.utils.DateUtils
//
///**
// * Author: nadir
// * Date: 4/26/13
// * Time: 11:24 AM
// *
// */
//
//
///**
// * A private message response.
// * @param content
// */
//case class MessageResponse(content:String) extends BaseModel[IDType] with Deletable
//    with DbObject with DbAccess with HasCreator {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//    import Label._
//
//    @Persistent var responseKind:String = MessageResponseKind.GENERAL
//    @Persistent var leaveReason:String = ""
//
//    /**
//     * memanggil private message dari this message response
//     * @return Option[PrivateMessage]
//     */
//    def getPrivateMessageObject:Option[PrivateMessage]={
////        this.getVertex.pipe.outFirst(REPLY_OF).flatMap(_.toCC[PrivateMessage])
//        this.getVertex.pipe.out(REPLY_OF).headOption.flatMap(_.toCC[PrivateMessage])
//    }
//
//    /**
//     * Get pretty printed creation time age
//     * relative to current time.
//     * ex: 3 hours ago.
//     * @return
//     */
//    def getCreationAge:String = {
//        new PrettyTime(new Date()).format(new Date(creationTime))
//    }
//
//    override def __load__(vertex: Vertex) {
//        super.__load__(vertex)
//    }
//
//    override protected def creatorLabel = REPLY_WITH
//}
//
//object MessageResponse extends MessageResponseDao[Long, GraphType]
//
///**
// * Response kind yang digunakan untuk leave atau add
// * pada sebuah conversation di dalam whisper
// */
//object MessageResponseKind {
//    val ADD = "add"
//    val LEAVE = "leave"
//    val GENERAL = "general"
//}
