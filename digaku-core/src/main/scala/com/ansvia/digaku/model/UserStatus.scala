///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.utils.DateUtils
//import Label._
//import com.ansvia.digaku.exc.{InvalidParameterException, DigakuException}
//import org.ocpsoft.prettytime.PrettyTime
//import java.util.Date
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.persistence.CounterProviderOperator
//
///**
// * Author: nadir
// *
// */
//case class UserStatus(var content:String) extends BaseModel[IDType]
//    with <PERSON><PERSON><PERSON> with Deletable with <PERSON><PERSON><PERSON> with Counter {
//
//    override protected def creatorLabel = CREATE_STATUS
//
//    private lazy val counter = Digaku.engine.counterProvider("userstatus-counter-" + getId)
//
//
//    /**
//     * Get counter provider used by this model.
//     * @return
//     */
//    def getCounter: CounterProviderOperator = counter
//
//    /**
//     * Get pretty printed creation time age
//     * relative to current time.
//     * ex: 3 hours ago.
//     * @return
//     */
//    def getCreationAge:String = {
//        new PrettyTime(new Date()).format(new Date(creationTime))
//    }
//
//}
//
//
//object UserStatus extends DaoBase[GraphType, UserStatus] with DbAccess {
//    val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.UserStatusDaoRootVertex"
//
//    import com.ansvia.graph.BlueprintsWrapper._
//    import com.ansvia.graph.IdGraphTitanDbWrapper._
//
//    /**
//     * create UserStatus
//     * @param creator see [[com.ansvia.digaku.model.User]]
//     * @param content content of status
//     * @return
//     */
//    def create(creator:User, content:String) = {
//
//        if (content.length > 160 || content.length < 3)
//            throw InvalidParameterException("min content 3 and maxs content 160 characters")
//
////        var newUserSt:UserStatus = null
////        transact {
//            val newUserSt = UserStatus(content).saveWithLabel(VertexLabels.CONTENT)
//                .toCC[UserStatus].getOrElse {
//                throw new DigakuException("Cannot create user status")
//            }
//            val v = newUserSt.getVertex
//            addToRoot(v)
//            val ed = creator --> CREATE_STATUS --> v <()
//            ed.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)
////        }
//        db.commit()
//        newUserSt
//    }
//
//}