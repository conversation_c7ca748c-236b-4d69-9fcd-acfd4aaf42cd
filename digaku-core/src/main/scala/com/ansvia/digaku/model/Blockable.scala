/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.graph.AbstractDbObject
import com.ansvia.graph.annotation.Persistent

/**
 * Author: robin
 *
 */
trait Blockable extends AbstractDbObject {

    @Persistent var blocked = false
    @Persistent var blockUnblockByUserName = ""
    @Persistent var blockUnblockByUserId = 0L
    @Persistent var blockUnblockTime = 0L


    /**
     * Set this object as blocked.
     * Non transactional jalankan di dalam transact/sebelum `db.commit()`,
     * perlu panggil `.save()` setela<PERSON>ya, contoh:
     *
     *    post.setBlocked(true).save()
     *
     * @param block block state true or false (true means block otherwise unblock).
     * @param blocker user who block/unblock this object.
     */
    def setBlocked(block:<PERSON><PERSON><PERSON>, blocker:User) = {
        blocked = block
        blockUnblockByUserName = blocker.getName
        blockUnblockByUserId = blocker.getId
        blockUnblockTime = Digaku.engine.dateUtils.nowMilis
        this
    }

}
