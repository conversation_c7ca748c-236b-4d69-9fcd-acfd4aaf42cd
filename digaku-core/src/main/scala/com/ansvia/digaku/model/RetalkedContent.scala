///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Types.{GraphType, IDType}
//import com.ansvia.digaku.dao.DaoBase
//import java.util.concurrent.{TimeUnit, Callable}
//import com.google.common.cache.CacheBuilder
//import com.ansvia.digaku.helpers.{DbAccess, AbstractDbAccess}
//import com.tinkerpop.blueprints.Vertex
//import com.ansvia.digaku.utils.DateUtils
//import com.ansvia.graph.annotation.Persistent
//import com.ansvia.digaku.exc.{NotExistsException, DigakuException}
//import Label._
//import com.ansvia.digaku.event.EventStream
//import com.ansvia.digaku.event.impl.RetalkEvent
//import com.ansvia.digaku.stream.StreamBuilder
//
///**
// * Author: fajr
// *
// */
//object RetalkedContent extends DaoBase[GraphType, RetalkWrapper] with DbAccess {
//    val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.RetalkedContentRootVertex"
//}
//
//case class RetalkWrapper(retalkedObjectId:Long) extends BaseModel[IDType] with Streamable[IDType]
//        with HasOrigin[GraphType] with HasCreator with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//    import scala.collection.JavaConversions._
//
//    @Persistent var originId:Long = 0L
//
//    override protected def creatorLabel = Label.RETALK
//
//    lazy val origin:Origin[GraphType] = {
//        db.getVertex(originId).toCC[Group].getOrElse {
//            throw NotExistsException("Invalid origin")
//        }
//    }
//
//    lazy val retalkedObject = {
//        this.getVertex.reload().pipe.out(RETALK_REF).headOption.flatMap(_.toCC[Streamable[IDType]]).getOrElse {
//            throw NotExistsException("Invalid retalked object")
//        }
//    }
//
//    lazy val retalker = {
//        this.getVertex.pipe.in(RETALK).headOption.flatMap(_.toCC[User]).getOrElse {
//            throw NotExistsException("Invalid retalker")
//        }
//    }
//
//    /**
//     * Custom deserializer.
//     * @param vertex vertex object.
//     */
//    override def __load__(vertex: Vertex) {
//        super.__load__(vertex)
//    }
//
//    override def __save__(v:Vertex) = {
//        v.setProperty("lastUpdatedTime", Digaku.engine.dateUtils.nowMilis)
//        if (creationTime == 0L){
//            v.setProperty("creationTime", Digaku.engine.dateUtils.nowMilis)
//        }else
//            super.__save__(v)
//    }
//}
