///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Types._
//import com.ansvia.graph.annotation.Persistent
//
///**
// * Author: andrie
// * Date: 2/12/14
// * Time: 2:41 PM
// *
// */
//case class ChannelLogoDefault(var logoOriginalUrl:String, var logoDefaultName:String) extends BaseModel[IDType] {
//    @Persistent var logoDefaultSmallUrl = ""
//    @Persistent var logoDefaultMediumUrl = ""
//
//    /**
//     * Digunakan untuk edit dan update group logo default
//     * @param small
//     * @param medium
//     * @param large
//     */
//    def updateLogo(small:String, medium:String, large:String) {
//        this.logoDefaultSmallUrl = small
//        this.logoDefaultMediumUrl = medium
//        this.logoOriginalUrl = large
//    }
//
//}
//
//object ChannelLogoDefault extends DaoBase[GraphType, ChannelLogoDefault] with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//    import collection.JavaConversions._
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.ChannelLogoDefaultRootVertex"
//
//    /**
//     * Create group logo default
//     * @param logoOriginalUrl
//     * @param logoDefaultName
//     * @return
//     */
//    def create(logoOriginalUrl:String, logoDefaultName:String):ChannelLogoDefault = {
//
//        var newLogo:ChannelLogoDefault = null
//
//        newLogo = ChannelLogoDefault(logoOriginalUrl, logoDefaultName.toLowerCase).save().toCC[ChannelLogoDefault].getOrElse {
//            throw new DigakuException("Cannot create group logo default")
//        }
//
//        addToRoot(newLogo.getVertex)
//
//        db.commit()
//
//        newLogo
//    }
//
//    /**
//     * Get group logo by name
//     * @param name
//     * @return
//     */
//    def getByName(name:String):Option[ChannelLogoDefault] = {
//        rootVertex.pipe.out(rootVertexLabel).has("logoDefaultName", name.toLowerCase).headOption.flatMap(_.toCC[ChannelLogoDefault])
//    }
//
//    /**
//     * Check group logo sudah ada atau belum
//     * @param name
//     * @return
//     */
//    def existByName(name:String):Boolean = {
//        getByName(name).isDefined
//    }
//
//    /**
//     * Get list group logo default
//     * @return
//     */
//    def getListLogoDefault:Iterator[ChannelLogoDefault] = {
//        rootVertex.pipe.out(rootVertexLabel)
//            .iterator().flatMap(_.toCC[ChannelLogoDefault])
//    }
//
//}
