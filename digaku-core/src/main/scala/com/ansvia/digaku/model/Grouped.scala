///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.exc.{InvalidParameterException, _}
//import com.ansvia.digaku.model.Label._
//import com.ansvia.graph.BlueprintsWrapper._
//import com.ansvia.graph.IdGraphTitanDbWrapper.idGraphTitanDbObjectWrapper
//import com.ansvia.graph.annotation.Persistent
//import com.tinkerpop.blueprints.Vertex
//
//import scala.collection.JavaConversions._
//
///**
//* Author: fajrhf
//*
//*/
//
///**
// * Saat ini digunakan untuk membuat grouped forum seperti regional group (mindtalk) dan news group (soccertalk)
// * Grouped ini nantinya juga dapat digunakan untuk melakukan grouping post untuk keperluan-keperluan tertentu seperti menampilkan
// * post di halaman depan apps atau web.
// *
// * @param subForumId
// * @param logo
// * @param kind
// */
//case class Grouped(var subForumId:Long, kind:Int, var name:String, var logo:String) extends BaseModel[IDType] with HasCreator {
//
//    import Grouped.GroupKind
//
//    @Persistent var lastEditorUserId = 0L
//
//    override def creatorLabel = CREATE_GROUP
//
//    /**
//     * custom save routine, called when blueprint-scala
//     * saving this record.
//     * @return
//     */
//    override def __save__(v:Vertex) = {
//        super.__save__(v)
//        v.setProperty("group.lower-name", name.toLowerCase)
//        v.setProperty("kind", kind)
//    }
//
//
//    lazy val lastEditor:Option[User] = {
//        User.getById(lastEditorUserId)
//    }
//
//    lazy val groupObj:Option[Groupable] = {
//        kind match {
//            case GroupKind.SUB_FORUM => Forum.getById(subForumId)
//            case GroupKind.POST => throw NotSupportedException("Currently not supported kind: Post")
//            case x =>
//                throw NotSupportedException("unknown kind: " + x)
//        }
//    }
//}
//
//
///**
//* Grouped DAO.
//*/
//object Grouped extends DaoBase[GraphType, Grouped] {
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.GroupRootVertex"
//
//
//    /**
//     * Jenis group-nya.
//     *
//     */
//    object GroupKind {
//        val POST = 1
//        val SUB_FORUM = 2
//    }
//
//    /**
//     * Create Group
//     * @param creator user creator.
//     * @param groupObject group object.
//     * @param kind jenis grouped. see [[com.ansvia.digaku.model.Grouped.GroupKind]]
//     * @param logo group logo.
//     * @return
//     */
//    def create(creator:User, groupObject:Groupable, kind:Int, groupNameParam:String, logo:String):Grouped = {
//        val groupObj = kind match {
//            case GroupKind.SUB_FORUM => Forum.getById(groupObject.getId).getOrElse(throw InvalidParameterException("Group not found."))
//            case GroupKind.POST => throw NotSupportedException("Currently not supported kind: Post")
//            case x =>
//                throw NotSupportedException("unknown kind: " + x)
//        }
//
//        val groupName = groupNameParam.trim.toLowerCase
//
//        if (isExists(groupObject.getId, kind))
//            throw AlreadyExistsException("already grouped in " + groupName)
//
//        if (!List(UserRole.ADMIN, UserRole.SUPER_ADMIN).contains(creator.role))
//            throw PermissionDeniedException("Permission denied, only Admin can do this action")
//
//        val group = Grouped(groupObject.getId, kind, groupName, logo)
//
//        creator.reload()
//
//        val vertex = group.saveWithLabel(VertexLabels.GROUP)
////
////        vertex.setProperty("group.lower-name", groupName.toLowerCase)
////        vertex.setProperty("kind", kind)
//
////        val ed = addToRoot(vertex)
//        addToRoot(vertex)
//
//        val newGroup = vertex.toCC[Grouped].getOrElse{
//            throw new DigakuException("Cannot group : %s".format(groupObj.getId))
//        }
////        ed.setProperty("group.kind", kind)
////        ed.setProperty("group.lower-name", name.toLowerCase)
//
//        creator --> CREATE_GROUP --> newGroup
//        groupObj --> HAS_GROUP--> newGroup
//
//        db.commit()
//
//        newGroup
//    }
//
//
//    /**
//     * Untuk update/edit group
//     *
//     * @param subForumId id group obj yang lama.
//     * @param newGroupObject id group obj yg baru.
//     * @param kind jenis group. see [[com.ansvia.digaku.model.Grouped.GroupKind]]
//     * @param logo group logo.
//     * @return
//     */
//    def update(subForumId:Long, newGroupObject:Groupable, kind:Int, groupName:String, logo:String) = {
//        val newGroupObj = kind match {
//            case GroupKind.SUB_FORUM => Forum.getById(newGroupObject.getId).getOrElse(throw InvalidParameterException("Group not found."))
//            case GroupKind.POST => throw NotSupportedException("Currently not supported kind: Post")
//            case x =>
//                throw NotSupportedException("unknown kind: " + x)
//        }
//
//        if (isExists(subForumId, kind))
//            throw InvalidParameterException("Group not found.")
//
//        val group = Grouped.getByGroupId(subForumId, kind).getOrElse {
//            throw new DigakuException("Cannot get group")
//        }
//
//        group.reload()
//
//        val groupV = group.getVertex
//
////        groupV.pipe.inE(HAS_GROUP).iterator().foreach(x => db.removeEdge(x))
//        groupV.pipe.inE(HAS_GROUP).remove()
//
//        newGroupObj --> HAS_GROUP --> groupV
//
//        group.logo = logo
//
//        group.save()
//        db.commit()
//    }
//
//    /**
//     * Get group list
//     * @param offset starting offset.
//     * @param limit ends limit.
//     * @return
//     */
//    def getList(offset: Int, limit: Int, kind:Int): Iterator[Grouped] = {
//        rootVertex.pipe.out(rootVertexLabel)
//            .has("kind", kind)
//            .range(offset, offset + limit)
//            .iterator()
//            .flatMap(_.toCC[Grouped])
//    }
//
//    /**
//     * Mendapatkan group obj list
//     *
//     * @param offset
//     * @param limit
//     * @param kind see [[com.ansvia.digaku.model.Grouped.GroupKind]]
//     * @return
//     */
//    def getGroupObjects[T <: Groupable : Manifest](groupName:String, offset: Int, limit: Int, kind:Int)= {
//        rootVertex.pipe.out(rootVertexLabel)
//            .has("kind", kind)
//            .has("group.lower-name", groupName.toLowerCase)
//            .in(HAS_GROUP)
//            .range(offset, offset + (limit -1))
//            .iterator()
//            .flatMap(_.toCC[T])
//    }
//
//    /**
//     * Check is group label already exists.
//     * @param kind jenis group. see [[com.ansvia.digaku.model.Grouped.GroupKind]]
//     * @param subForumId group obj id.
//     * @return
//     */
//    def isExists(subForumId:Long, kind:Int) = {
//        getByGroupId(subForumId, kind).isDefined
//    }
//
//    /**
//     * Get group by group obj id.
//     * @param subForumId group id.
//     * @param kind jenis group. see [[com.ansvia.digaku.model.Grouped.GroupKind]]
//     * @return
//     */
//    def getByGroupId(subForumId:Long, kind:Int) = {
//        rootVertex.pipe.out(rootVertexLabel).has("kind", kind).as("v")
//            .in(HAS_GROUP)
//            .has("id",subForumId)
//            .back("v").asInstanceOf[GremPipeVertex]
//            .headOption.flatMap(_.toCC[Grouped])
//    }
//
//    /**
//     * Get griup by group obj name
//     * @param name
//     * @param kind
//     * @return
//     */
//    def getByGroupName(name:String, kind:Int) = {
//        rootVertex.pipe.out(rootVertexLabel)
//            .has("kind", kind)
//            .has("group.lower-name", name.toLowerCase)
//            .as("v")
//            .inE(HAS_GROUP)
//            .back("v").asInstanceOf[GremPipeVertex]
//            .headOption.flatMap(_.toCC[Grouped])
//    }
//}
//
