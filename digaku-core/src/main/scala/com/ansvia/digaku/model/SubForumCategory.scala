///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.exc.{PermissionDeniedException, DigakuException, InvalidParameterException}
//import collection.JavaConversions.{asScalaIterator, iterableAsScalaIterable}
//import com.ansvia.digaku.model.Label._
//import com.ansvia.graph.BlueprintsWrapper._
//import com.ansvia.graph.IdGraphTitanDbWrapper._
//import com.ansvia.digaku.database.GraphCompat.tx
//import com.tinkerpop.blueprints.{Vertex, Direction}
//
///**
// * Author: andrie, robin
// *
// */
//
//case class SubForumCategory(var categoryName:String, var logo:String) extends BaseModel[IDType] with DbAccess {
//
//    import com.ansvia.graph.gremlin._
//
//    /**
//     * custom save routine, called when blueprint-scala
//     * saving this record.
//     * @return
//     */
//    override def __save__(v:Vertex) = {
//        super.__save__(v)
//        v.setProperty("group-category.name", categoryName.toLowerCase)
//    }
//
//    /**
//     * Get user/group berdasarkan category interest
//     * @param offset
//     * @param limit
//     * @param hasPromoted true jika ingin mendapatkan user/group
//     *                    yang di promote oleh admin BCA MC2
//     * @tparam T
//     * @return
//     */
//    def getGroupCategory[T<:Origin[GraphType]:Manifest](offset:Int, limit:Int, hasPromoted:Boolean = false):Seq[T] = {
//        val originKind = manifest[T].runtimeClass.getCanonicalName
//
//        var pipe = this.getVertex.pipe.in(CATEGORY_INTEREST)
//            .has("_class_", originKind)
//
//        if (hasPromoted) {
//            pipe = pipe.as("x").outE(CATEGORY_INTEREST)
//                .has("promotedGS", true)
//                .back("x")
//                .asInstanceOf[GremPipeVertex]
//        }
//
//        pipe.range(offset, (offset + limit) - 1)
//            .asInstanceOf[GremPipeVertex]
//
//        // originKind group di order berdasarkan member terbanyak
//        if (originKind == "com.ansvia.digaku.model.Forum") {
//            pipe = pipe.hasNot("privated", true).asInstanceOf[GremPipeVertex].order { (a:Vertex, b:Vertex) =>
//                    -a.getOrElse("memberCount", 0).compareTo(b.getOrElse("memberCount", 0))
//                }
//                .asInstanceOf[GremPipeVertex]
//        }
//
//        pipe.iterator().flatMap(_.toCC[T]).toSeq
//    }
//
//    /**
//     * Untuk mem-promote user/group dari category interest oleh admin BCA MC2
//     * yang ditampilkan di halaman get started
//     * @param vertex vertex dari object (ex: group) digunakan untuk query data dari Category Interest
//     * @param user see [[com.ansvia.digaku.model.User]]
//     * @return
//     */
//    def promotedToInterest(vertex:Vertex, user:User) = {
//        tx { t =>
//            val existing = t.getVertex(getId)
//                .pipe.in(CATEGORY_INTEREST)
//                .has("id", vertex.getId)
//                .asInstanceOf[GremPipeVertex]
//
//            if (!existing.hasNext) {
//                val edge = vertex.addEdge(CATEGORY_INTEREST, getVertex)
//
//                edge.setProperty("promotorGSId", user.getId)
//                edge.setProperty("promotedGS", true)
//                edge
//            } else {
//                vertex.getEdges(Direction.OUT, CATEGORY_INTEREST).map { edge =>
//                    edge.setProperty("promotorGSId", user.getId)
//                    edge.setProperty("promotedGS", true)
//                }
//            }
//        }
//    }
//
//}
//
//object SubForumCategory extends DaoBase[GraphType, SubForumCategory] with DbAccess {
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.GroupCategoryRootVertex"
//
//    /**
//     * Create Category Interest
//     * @param name category name/title
//     * @param logo category logo
//     * @return
//     */
//    def create(name:String, logo:String):SubForumCategory = {
//        if (name.length < 3)
//            throw InvalidParameterException("Min category name length 3 characters")
//
//        if (name.length > 25)
//            throw InvalidParameterException("Max category name length 25 characters")
//
//        if (SubForumCategory.isExistByName(name))
//            throw PermissionDeniedException("Category name already exist!")
//
//        val cat = SubForumCategory(name, logo).saveWithLabel(VertexLabels.CATEGORY_INTEREST)
//            .toCC[SubForumCategory].getOrElse {
//            throw new DigakuException("Cannot create Category")
//        }
//
//        addToRoot(cat.getVertex)
//
//        db.commit()
//
//        cat
//    }
//
//    /**
//     * Get user/group yang sama dengan category interest
//     * @param origin
//     * @tparam T
//     * @return
//     */
//    def getMutualGroupCategory[T<:Origin[GraphType]:Manifest](offset:Int, limit:Int, origin:Origin[GraphType]):Seq[T] = {
//        val originKind = manifest[T].runtimeClass.getCanonicalName
//
//        var pipe = origin.getVertex.pipe.out(CATEGORY_INTEREST)
//            .in(CATEGORY_INTEREST)
//            .hasNot("id", origin.getId)
//            .dedup()
//            .asInstanceOf[GremPipeVertex]
//
//        if (originKind != "com.ansvia.digaku.model.Origin") {
//            pipe = pipe.has("_class_", originKind).asInstanceOf[GremPipeVertex]
//        }
//
//        pipe.range(offset, (offset + limit) -1)
//            .iterator().flatMap(_.toCC[T]).toSeq
//    }
//
//    /**
//     * Get Category by Name
//     * @param name
//     * @return
//     */
//    def getByName(name:String):Option[SubForumCategory] = {
//        db.query().has("label", VertexLabels.CATEGORY_INTEREST)
//            .has("group-category.name", name.toLowerCase)
//            .vertices()
//            .headOption.flatMap(_.toCC[SubForumCategory])
//    }
//
//    /**
//     * Check category sudah ada atau belum
//     * @param name
//     * @return
//     */
//    def isExistByName(name:String):Boolean =
//        getByName(name).isDefined
//
//}