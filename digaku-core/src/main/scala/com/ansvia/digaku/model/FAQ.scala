package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.persistence.SeqStore
import com.ansvia.digaku.utils.ArrangeSettings
import com.ansvia.digaku.validator.PostValidator
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.Direction
import scala.collection.JavaConversions._
import Label._
import com.ansvia.digaku.helpers.TypeHelpers._

/**
 * Author: nadir (<EMAIL>)
 */

case class FAQ(var title:String, var content:String) extends BaseModel[IDType] with DbAccess {
    @Persistent var creatorId: Long = 0L

    // FAQ Creator
    lazy val creator:Option[User] = User.getById(creatorId)

    // FAQ Topic
    lazy val topic:Option[Topic] = this.getVertex.pipe.in(HAS_FAQ).iterator()
        .flatMap(_.toCC[Topic]).toSeq.headOption

    override def toString = "FAQ(" + getId + ", " + title.replaceAll("\\W+", " ") + ")"
}

object FAQ extends DaoBase[GraphType, FAQ]{
    override val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.FAQRootVertex"

    lazy val promotedListStore = ArrangeSettings.seqStore.build("promoted-faq-list", () => ArrangeSettings.generateId())

    /**
     * Create FAQ
     * @param title
     * @param content
     * @param creator
     * @param topic see [[com.ansvia.digaku.model.Topic]]
     * @return
     */
    def create(title:String, content:String, creator: User, topic: Topic): FAQ ={
        val _title = title.trim
        val _content = content.trim

        if(_title.length < 3 || _title.length > 300)
            throw InvalidParameterException("Question minimum 3 and maximum 300 characters")

        if(_content.length > 20000 || _content.length < 3)
            throw InvalidParameterException("Answer minimum 3 and maximum 20000 characters")

        val newFaq = FAQ(_title, _content)
        newFaq.creatorId = creator.getId

        val vertex = newFaq.save()

        val ed = topic.getVertex --> HAS_FAQ --> vertex <()
        ed.setProperty("timeOrder", System.currentTimeMillis())

        addToRoot(vertex)

        db.commit()

        topic.incrementFaqCount()

        val faq = vertex.toCC[FAQ].getOrElse{
            throw new DigakuException("Cannot create FAQ : %s".format(newFaq))
        }

        Digaku.engine.searchEngine.indexFaq(faq)

        faq
    }

    /**
     * Mengubah topic pada FAQ
     * @param faq
     * @param topic
     * @param noTx
     */
    def changeTopic(faq:FAQ, topic: Topic, noTx:Boolean = false): Unit = {
        val faqV = faq.getVertex
        faqV.pipe.inE(HAS_FAQ).iterator().foreach { ed =>
            ed.getVertex(Direction.OUT).toCC[Topic].foreach { oldTopic =>
                oldTopic.decrementFaqCount()
            }

            ed.remove()
        }

        val ed = topic.getVertex.addEdge(HAS_FAQ, faqV)
        ed.setProperty("timeOrder", System.currentTimeMillis())

        topic.incrementFaqCount()

        if (!noTx) {
            db.commit()
        }
    }

    /**
     * Delete FAQ berdasarkan model
     * @param faq
     * @param m
     * @return
     */
    override def delete(faq: FAQ)(implicit m:Manifest[FAQ]){
        faq.topic.foreach(_.decrementFaqCount())
        removePromotedFaq(faq)

        Digaku.engine.searchEngine.deleteFaq(faq)

        super.delete(faq)
    }

    /**
     * Delete FAQ berdasarkan ID FAQ
     * @param id
     * @param m
     * @return
     */
    override def deleteById(id: IDType)(implicit m:Manifest[FAQ]){
        getById(id).foreach { faq =>
            faq.topic.foreach(_.decrementFaqCount())
            removePromotedFaq(faq)

            Digaku.engine.searchEngine.deleteFaq(faq)
        }

        super.deleteById(id)
    }

    /**
     * Mendapatkan promoted list FAQ
     * @return
     */
    def getPromotedList: List[FAQ] = {
        ArrangeSettings.getArrangedList(promotedListStore).flatMap { faqIdStr =>
            getById(faqIdStr.toLongOr(0L))
        }
    }

    /**
     * Check apakah faq dipromote
     * @param fAQ
     * @return
     */
    def isPromoted(fAQ: FAQ):Boolean = {
        ArrangeSettings.getArrangedList(promotedListStore).exists(_ == fAQ.getId.toString)
    }

    /**
     * Remove faq from promoted list
     * @param fAQ
     */
    def removePromotedFaq(fAQ: FAQ): Unit ={
        ArrangeSettings.removeFromList(promotedListStore, fAQ.getId)
    }

    /**
     * Move up FAQ from promoted list
     * @param faq
     */
    def arrangePromotedUp(faq:FAQ): Unit = {
        ArrangeSettings.arrangeUp(promotedListStore, faq.getId)
    }

    /**
     * Move down FAQ from promoted list
     * @param faq
     */
    def arrangePromotedDown(faq:FAQ): Unit = {
        ArrangeSettings.arrangeDown(promotedListStore, faq.getId)
    }

    /**
     * Move to top FAQ from promoted list
     * @param faq
     */
    def movePromotedToTop(faq:FAQ): Unit = {
        ArrangeSettings.moveToTop(promotedListStore, faq.getId)
    }

    /**
     * Set arrange promoted with new list
     * @param objList
     * @return
     */
    def setPromotedArrangeList(objList:List[String]) = {
        ArrangeSettings.setArrangedList(promotedListStore, objList)
    }
}
