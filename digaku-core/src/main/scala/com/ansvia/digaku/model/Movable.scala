/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.event.impl.{MoveEventEvent, MovePostEvent}
import com.ansvia.digaku.exc.{InvalidParameterException, PermissionDeniedException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.graph.AbstractDbObject
import com.ansvia.graph.annotation.Persistent

import com.ansvia.digaku.utils.ForumSettings._

/** $regression:$ Fungsional move content.
 */
/**
 * Author: temon
 *
 */

/**
 * Semua post yang bisa dipindahkan channelnya (movable)
 * harus diturunkan dari sini.
 */
trait Movable extends AbstractDbObject with DbAccess {

    @Persistent
    var moved:Boolean = false

    /**
     * see [[com.ansvia.digaku.model.MovedType]]
     */
    @Persistent
    var movedType:Int = 0

    // ganti dengan movedByUserId
//    @Persistent
//    var movedByUserName = ""

    @Persistent
    var movedByUserId:Long = 0L

    // timestamp
    @Persistent
    var movedTime:Long = 0L

    /**
     * digunakan untuk move content sebagai siapa
     * see [[com.ansvia.digaku.model.MovedRole]]
     */
    @Persistent
    var movedRole:String = ""

    /**
     * forum original saat post pertama kali di post
     */
    @Persistent
    var movedForumOriginId:Long = 0L

    @Persistent
    var movedForumOriginName:String = ""


    @Persistent
    var movedReason:String = ""


    def isMoved:Boolean = moved

    /**
     * check apakah content di move dengan soft move
     * @return Boolean
     */
    def isSoftMoved:Boolean = {
        movedType == MovedType.SOFT
    }

    /**
     * check apakah content di move dengan hard move
     * @return Boolean
     */
    def isHardMoved:Boolean = {
        movedType == MovedType.HARD
    }

    /**
     * digunakan untuk move content
     * @param movedType see [[com.ansvia.digaku.model.MovedType]]
     * @param movedRole see [[com.ansvia.digaku.model.MovedRole]]
     * @param mover user yang move
     * @param reason alasan pemindahan
     */
    def setMoved(movedType:Int, movedRole:String, mover:User, channelTargetId:IDType, reason:String) {
        Forum.getById(channelTargetId).map { ch =>

            this match {
                case post:HasOrigin[GraphType] =>

                    post.origin match {
                        case user:User =>
                            throw PermissionDeniedException("Invalid move content type")
                        case group:Forum =>

                            if(ch == post.origin)
                                throw InvalidParameterException("Invalid forum target, cannot move to current subforum")

                            /**
                             * Validasi mover & group
                             * salah satu staff group yang punya ability move content kedua group
                             * Staff atau Admin System
                             * owner dari group kedua group
                             */
                            if (!ch.isStaff(mover) &&
                                !ch.isOwner(mover) &&

                                !group.isStaff(mover) &&
                                !group.isOwner(mover) &&

                                mover.role != UserRole.SUPER_ADMIN &&
                                mover.role != UserRole.ADMIN
                            )
                                throw PermissionDeniedException("Permission Denied")

                            /**
                             * Validasi feature forum target
                             */
                            post match {
//                                case post:Picture =>
//                                    if(!ch.hasFeature(ForumFeatures.PHOTO))
//                                        throw PermissionDeniedException("Invalid Forum Target not supported Photo feature")
                                case post:Event =>
                                    if(!ch.hasFeature(ForumFeatures.EVENT))
                                        throw PermissionDeniedException("Invalid Forum Target not supported Event feature")
                                case _ =>
                            }
                    }

                    if(ch == post.origin)
                        throw InvalidParameterException("Invalid Forum Target")
                case _ =>
                    throw PermissionDeniedException("Invalid move content type")
            }

            this match {
                case post:Deletable =>
                    if (post.isDeleted)
                        throw PermissionDeniedException("Cannot move content, this thread has been deleted.")
                case _ =>
            }

//            transact {
            this match {
                case post:HasOrigin[GraphType] =>
                    if (!this.moved)
                        this.movedForumOriginId = post.origin.getId
            }

            this.movedRole = movedRole
            this.movedReason = reason
            this.moved = true
            this.movedType = movedType
            this.movedByUserId = mover.getId
            this.movedTime = Digaku.engine.dateUtils.nowMilis
            this.save()
//            }
            db.commit()

            this match {
                case post:Post =>
                    post.setOrigin(ch.reload())
                    Post.originCache2.remove(post.getId.toString)
                    Digaku.engine.eventStream.emit(MovePostEvent(post, ch, movedType))
                case event:Event =>
                    event.setOrigin(ch.reload())
                    Digaku.engine.eventStream.emit(MoveEventEvent(event, ch, movedType))
//                case picture:Picture =>
//                    picture.setOrigin(ch.reload())
//                    Picture.originCache.invalidate(picture.getId.toString)
//                    Digaku.engine.eventStream.emit(MovePictureEvent(picture, ch, movedType))
                case _ =>
            }

        } getOrElse {
            throw InvalidParameterException("Invalid Thread Target")
        }
    }

    /**
     * digunakan untuk membatalkan move content
     */
//    def restoreMoved() {
//
//        if (this.movedType == MovedType.HARD){
//
//            this match {
//                case post:Post =>
//                    Digaku.engine.eventStream.emit(RestoreContentEvent(post.creator , post))
//                case event:Event =>
//                    Digaku.engine.eventStream.emit(RestoreContentEvent(event.creator , event))
//                case picture:Picture =>
//                    Digaku.engine.eventStream.emit(RestoreContentEvent(picture.creator , picture))
//                case _ =>
//
//            }
//        }
//
//        transact {
//
//            this.movedRole = ""
//            this.moved = false
//            this.movedType = 0
//            //        this.movedByUserId = userDeleting.getId
//            this.movedByUserName = ""
//            this.movedTime = 0L
////            this.movedReason = ""
//            this.save()
//
//        }
//    }

}
/*$regression:end$*/

object Movable extends DbAccess {
    import com.ansvia.graph.BlueprintsWrapper._

    def getById(id:IDType):Option[Movable] = {
        val v = db.getVertex(id)
        if (v != null){
            v.toCC[Movable]
        }else{
            None
        }
    }
}

object MovedType {
    val SOFT = 1
    val HARD = 2
}

object MovedRole {
    val ADMIN_CHANNEL = "admin-group"
//    val POST_CREATOR = "post-creator"
    val ADMIN_SYSTEM = "admin"
}