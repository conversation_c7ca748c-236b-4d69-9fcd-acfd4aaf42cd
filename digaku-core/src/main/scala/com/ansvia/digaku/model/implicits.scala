/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.tinkerpop.blueprints.{Direction, Edge}
import com.ansvia.graph.BlueprintsWrapper._

/**
 * Author: robin
 * 
 */
object implicits {

    sealed class StaffConverter(edge:Edge){
        def toStaff = {
            val user = edge.getVertex(Direction.OUT).toCC[User]
            val attr = StaffAttribute(edge.getOrElse("title", ""),
                edge.getOrElse("abilities", "").split(",").map(_.toLowerCase.trim))
            Staff(user.get, attr)
        }
    }
    implicit def edgeStaffConverter(edge:Edge) = new StaffConverter(edge)
}



