/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */
package com.ansvia.digaku.model

// scalastyle:off file.size.limit

import java.util

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.ForumDao
import com.ansvia.digaku.event.impl._
import com.ansvia.digaku.exc.{AlreadyExistsException, InvalidParameterException, LimitationReachedException, NotExistsException, _}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.persistence.CounterProviderOperator
import com.ansvia.digaku.se.ArticleQueryBuilder
import com.ansvia.digaku.utils.{Comparator, SortDirection}
import com.ansvia.graph.annotation.Persistent
import com.ansvia.graph.gremlin._
import com.ansvia.perf.PerfTiming
import com.tinkerpop.blueprints._
import com.tinkerpop.gremlin.java.GremlinPipeline
import com.ansvia.digaku.database.GraphCompat._
import com.ansvia.digaku.utils.UserSettings._
import com.ansvia.digaku.helpers.GremlinHelpers._

case class Forum(var name: String, var desc: String) extends BaseModel[IDType]
        with Origin[GraphType]
        with Deletable
        with Lockable
        with DbAccess
        with Blockable
        with PerfTiming
        with Counter
        with CollectionVertex
        with Endorsable
        with Promotable
        with Groupable {

    import Label._
    import com.ansvia.graph.BlueprintsWrapper._
    import implicits._

    import scala.collection.JavaConversions._

    private lazy val counter = Digaku.engine.counterProvider("forum-counter-" + getId)

    /**
     * @see [[com.ansvia.digaku.model.OriginKind]]
     */
    @Persistent var kind = OriginKind.SUB_FORUM


    // untuk perankingan forum
    @Persistent var rank: Int = 0

    @Persistent var largeLogoUrl: String = ""
    @Persistent var mediumLogoUrl: String = ""
    @Persistent var smallLogoUrl: String = ""

    val lowerName: String = name.toLowerCase

    /**
     * Bitwise type.
     * @see [[com.ansvia.digaku.model.ForumVisibility]]
     */
    @Persistent var visibility: Int = ForumVisibility.ALL

    @Persistent var adsPublisher: String = ""
    @Persistent var bannerPicUrl: String = ""

    @Persistent var announcementText: String = ""
    @Persistent var announcementActive: Boolean = false

    /**
     * ID dari forum atas-nya (parent)
     * Ini berguna untuk arsitektur forum yang memiliki forum lagi di dalamnya.
     * Ini hanya attribute, selain itu forum harus di-link-kan menggunakan edge.
     */
    @Persistent var parentForumId: Long = 0L


    // supported features berisi fitur-fitur yang didukung
    // pada suatu forum, separated by comma
//    var supportedFeatures: Array[String] = Array(ForumFeatures.PHOTO, ForumFeatures.SIMPLE_POST, ForumFeatures.VIDEO)
//    var whoCanPosts: Array[String] = Array(ForumPermission.STAFF, ForumPermission.MEMBER)
//    var whoCanCreateEvents: Array[String] = Array.empty[String]

//    var whoCanResponses: Array[String] = Array(ForumPermission.STAFF, ForumPermission.MEMBER, ForumPermission.REGISTERED)

    // whoCanInviteUser digunakan untuk siapa saja yang bisa invite user daari forum
//    var whoCanInviteUsers: Array[String] = Array(ForumPermission.STAFF, ForumPermission.MEMBER)

    lazy val owner: User = {

        // map owner
        reload().getVertex.pipe.in(OWN).range(0, 0).headOption
            .flatMap(_.toCC[User]).orNull

    }

    @Persistent var tags: String = ""

    def getName = name


    /**
     * digunakan untuk set blocked forum
     * @param block block state true or false (true means block otherwise unblock).
     * @param blocker user who block/unblock this object.
     * @return
     */
    override def setBlocked(block: Boolean, blocker: User) = {

        super.setBlocked(block, blocker)
        this.save()

        db.commit()

        Digaku.engine.eventStream.emit(BlockedEvent(this))

        this

    }

    override def setDeleted(deletedRole: String, userDeleting: User, deletedReason: String = "", collected: Boolean = true) {
        if (!isOwner(userDeleting) && userDeleting.role != UserRole.SUPER_ADMIN && userDeleting.role != UserRole.ADMIN) {
            throw PermissionDeniedException("Unable to delete forum, you are not admin or owner on this forum")
        }

        super.setDeleted(deletedRole, userDeleting, deletedReason, collected)
        owner.decrementOwnedChannelCount()

    }


    import Forum.COMMA

    /**
     * Custom save routine.
     * @return
     */
    override def __save__(v: Vertex) = {
        // serialize supported features.
//        v.setProperty("supportedFeatures", supportedFeatures.mkString(COMMA))
        if (v.getProperty[String]("forum.lower-name") != name.toLowerCase)
            v.setProperty("forum.lower-name", name.toLowerCase)
//        v.setProperty("whoCanPosts", whoCanPosts.mkString(COMMA))
//        v.setProperty("whoCanCreateEvents", whoCanCreateEvents.mkString(COMMA))
//        v.setProperty("whoCanResponses", whoCanResponses.mkString(COMMA))
//        v.setProperty("whoCanInviteUsers", whoCanInviteUsers.mkString(COMMA))

        if (id > 0) {
            v.setProperty("simplePostCount", counter.get("simple_post").toInt)
            v.setProperty("articleCount", counter.get("simple_post").toInt)
        }
    }


    /**
     * Custom deserializer
     * @param vertex current vertex.
     */
    override def __load__(vertex: Vertex) {
        super.__load__(vertex)


        /**
         * sekarang gak perlu loading data yang udah ada
         * annotation @Persistent lagi, karena sudah otomatis
         * diload oleh blueprints-scala >= 0.0.3
         * kecuali untuk beberapa yang kita set dengan
         * private atau protected.
         */

        rank = vertex.getOrElse("rank", 0)

//        if (supportedFeatures != null)
//            supportedFeatures = vertex.getOrElse("supportedFeatures", "")
//                .split(COMMA).map(_.trim.toLowerCase)

//        whoCanPosts = vertex.getOrElse("whoCanPosts", "").split(COMMA).array
//        whoCanCreateEvents = vertex.getOrElse("whoCanCreateEvents", "").split(COMMA).array
//        whoCanResponses = vertex.getOrElse("whoCanResponses", "").split(COMMA).array
//        whoCanInviteUsers = vertex.getOrElse("whoCanInviteUsers", "").split(COMMA).array

    }

    def isSubForum:Boolean = this.getVertex.getProperty[Long]("parentForumId") != 0L
//
//    /**
//     * untuk merubah semua permission who can create post, response, event, picture
//     * berdasarkan isi parameter channelPermission
//     * @param channelPermission see [[com.ansvia.digaku.model.ForumPermission]]
//     */
//    def setAllPermission(channelPermission: String) {
//        setWhoCanCreateArticle(channelPermission)
//        setWhoCanCreateEvents(channelPermission)
//        setWhoCanResponses(channelPermission)
//        this.save()
//
//        db.commit()
//    }

    /**
     * Add forum members without emitting event.
     * @param users array of user object.
     */
    def addMembersNoEmit(users: User*) = {

        var count: Int = 0
//        var maleCount: Int = 0
//        var femaleCount: Int = 0

        // ketika users terdapat user yang di locked/suspend add members tidak akan di proses
        if (users.exists(_.isLocked)) {
            throw PermissionDeniedException("Cannot add members, " +
                users.filter(_.isLocked).map(_.name).mkString(", ") +
                " is currently suspended")
        }

        for (u <- users) {
            // don't join already joined users.
            val uV = db.getVertex(u.getId)
            val chV = db.getVertex(getId)
            if (!this.isMember(u)) {

                //                val ed = uV --> JOIN --> chV <()
                val ed = uV.addEdge(JOIN, chV)

                ed.setProperty("sourceId", u.getId)
                ed.setProperty("targetId", this.getId)
                ed.setProperty("userGender", u.sex)
                ed.setProperty("targetName", lowerName)


                //                uV.setProperty("joinCount", uV.getOrElse("joinCount", 0) + 1)
                u.incrementJoinChannelCount()

                count = count + 1

                //data dari ldap tidak ada informasi gender
//                u.sex match {
//                    case SexType.MALE =>
//                        maleCount = maleCount + 1
//                    case SexType.FEMALE =>
//                        femaleCount = femaleCount + 1
//                    case _ =>
//                }

                u.joinedForums.insert(getId, getName)

                //                Foru.isMemberCache.invalidate(u.getId + "-" + this.getId)
            }
        }

        incrementMemberCount(count)

//        counter.incrementBy("member_all", maleCount + femaleCount)
//        counter.incrementBy("member_male", maleCount)
//        counter.incrementBy("member_female", femaleCount)

        this.getVertex.setProperty("memberCount", getMemberCount())

        db.commit()

        Forum.markMembers(this, users, state = true)


        this
    }

    /**
     * Untuk menambahkan member ke forum tanpa melakukan emit ke event stream.
     * @param users see [[com.ansvia.digaku.model.User]]
     * @return
     */
    def addMembers(users: User*) = {
        val usersx = users.distinct
        addMembersNoEmit(usersx: _*)
        for (u <- usersx) {
            if (u != owner || getPrivated) {
                Digaku.engine.eventStream.emit(JoinChannelEvent(u, this))
            }
        }
        this
    }


    /**
     * add forum staff
     * @see [[com.ansvia.digaku.model.Forum.unsetStaff]]
     * @param user user object
     * @param title staff title.
     * @param abilities staff abilities.
     */
    def addStaff(user: User, title: String, abilities: Array[String]) {
        require(user != null)
        require(title != null)

        validateStaffAdd(user, title, mustMember = true)

        //        transact {

        val userR = user.reload()
        val chR = this.reload()

        val edge = userR --> STAFF_AT --> chR <()

        edge.setProperty("abilities", abilities.map(_.toString.trim.toLowerCase)
            .mkString(COMMA))

        edge.setProperty("title", title)

        //            getVertex.setProperty("staffCount", getVertex.getOrElse("staffCount", 0) + 1)
        counter.increment("staff")
        //        }
        db.commit()

        user.moderatedForums.insert(getId, getName)

        Digaku.engine.eventStream.emit(AddStaffEvent(user, this))

        //        Forum.isStaffCache.invalidate(user.getId + "-" + this.getId)

    }

    //    /**
    //     * Invite user to become staff on this forum.
    //     * @param invitor who's invite.
    //     * @param invited target user to be invited.
    //     * @param title staff title.
    //     * @param abilities staff abilities.
    //     * @return
    //     */
    //    def inviteToBecomeStaff(invitor:User, invited:User,
    //                            title:String, abilities: Array[String]): ChannelInvitationCode = {
    //
    //        validateStaffAdd(invited, title, mustMember = false)
    //
    //        if (!(isOwner(invitor) || isStaff(invitor)))
    //            throw PermissionDeniedException("Only owner / staff can invite someone to become staff.")
    //
    //        val invO = tx { t =>
    //            val chR = t.getVertex(this.getId).toCC[Forum].getOrElse {
    //                throw NotExistsException("Can't get forum target")
    //            }
    //
    //            val invitedR = t.getVertex(invited.getId).toCC[User].getOrElse {
    //                throw NotExistsException("Can't get invited user")
    //            }
    //
    //            if (this.isDeleted) {
    //                throw PermissionDeniedException("This forum has removed")
    //            }
    //
    //            if (chR.isInvited(invitedR, InvitationKind.BECOME_STAFF))
    //                throw AlreadyExistsException(invitedR.name + " already invited")
    //
    //            val cV = chR.getVertex
    //            val uV = invitedR.getVertex
    //
    //            val edge = cV.reload() --> INVITE --> uV.reload() <()
    //
    //            val invCode = createInvitationCode()
    //            val kind = InvitationKind.BECOME_STAFF
    //
    //            edge.setProperty("kind", kind)
    //            edge.setProperty("invitedByUserId", invitor.getId)
    //            edge.setProperty("invitedByUserName", invitor.name)
    //            edge.setProperty("code", invCode)
    //            edge.setProperty("title", title)
    //            edge.setProperty("abilities", abilities.map(_.toString.trim.toLowerCase).mkString(COMMA))
    //            edge.setProperty("creationTime", Digaku.engine.dateUtils.nowMilis)
    //
    //            val inv = ChannelInvitationCode(invCode, kind, edge)
    //
    //            db.commit()
    //
    //            Some(inv)
    //        }
    //
    //        Digaku.engine.eventStream.emit(ChannelInviteStaffEvent(this.asInstanceOf[Forum].reload(), invitor.reload(), invited.reload(), invO.get))
    //
    //        invO.get
    //
    //    }
    //
    //
    //    /**
    //     * Invite user to become owner on this forum.
    //     * @param invitor who's invite.
    //     * @param invited target user to be invited.
    //     * @return
    //     */
    //    def inviteToBecomeOwner(invitor:User, invited:User) = {
    //
    //        if (!(isOwner(invitor) || isStaff(invitor)))
    //            throw PermissionDeniedException("Only owner / staff can invite someone to become staff.")
    //
    //        validOwnerTransfer(invited)
    //
    //        var invO:Option[ChannelInvitationCode] = None
    //        val chR = this.reload()
    //        val userR = invited.reload()
    //
    //        if (chR.isInvited(userR, InvitationKind.BECOME_OWNER))
    //            throw AlreadyExistsException(invited.name + " already invited")
    //
    //        val edge = chR --> INVITE --> userR <()
    //
    //        val invCode = createInvitationCode()
    //        val kind = InvitationKind.BECOME_OWNER
    //
    //        edge.setProperty("kind", kind)
    //        edge.setProperty("invitedByUserId", invitor.getId)
    //        edge.setProperty("invitedByUserName", invitor.name)
    //        edge.setProperty("code", invCode)
    //        edge.setProperty("creationTime", Digaku.engine.dateUtils.nowMilis)
    //
    //        val inv = ChannelInvitationCode(invCode, kind, edge)
    //
    //        invO = Some(inv)
    //
    //        db.commit()
    //
    //        Digaku.engine.eventStream.emit(ChannelInviteOwnerEvent(this.asInstanceOf[Forum], invitor, invited, invO.get))
    //
    //        invO.get
    //
    //    }


    /**
     * Melakukan pengecekan di add staff forum
     * @param user user @see [[com.ansvia.digaku.model.User]]
     * @param title title staff forum
     * @param mustMember
     */
    private def validateStaffAdd(user: User, title: String, mustMember: Boolean = false) {
        if (title.length > 20) {
            throw new InvalidParameterException("Max title 20 characters")
        }

        if (!User.existsByName(user.reload().name))
            throw new NotExistsException("user %s is not exsist".format(user.name))

        if (getStaffByUserName(user.reload().name).isDefined)
            throw new AlreadyExistsException("user %s is already a staff".format(user.name))

        if (getStaffs(0, 100).length >= 100)
            throw new LimitationReachedException("%s forum already has a staff of more than 100..".format(this))

        user.reload()
        if (user.getChannels(SubForumStateFlags.STAFF, 0, 100).length >= 100)
            throw new LimitationReachedException("user %s has a staff of more than 100 channels".format(user.name))

        // harus member
        if (mustMember && !isMember(user))
            throw new InvalidParameterException("cannot add staff, user not member of forum.")


    }

    /**
     * Get forum members.
     * @param offset starting offset.
     * @param limit end limit.
     * @param relevanceTo [optional] list relevance to user.
     *                    if this set then list will return
     *                    ordered by supported user first if any.
     *                    default = None.
     * @return
     */
    def getMembers(offset: Int, limit: Int, relevanceTo: Option[User] = None): Seq[User] = {

        timing(s"Forum($name).getMembers") {
            if (relevanceTo.isEmpty) {
                // @TODO(fajr): perlu diubah menggunkan cara yang benar,
                // masih merupakan sementara agar tidak melakukan komparasi dalam jumlah banyak
                // hal ini menyebabkan lambat di server
                // order dilakukan setelah range

                getVertex.pipe.in(JOIN)
                    .dedup()
                    .range(offset, (offset + limit) - 1)
                    .order((a: Vertex, b: Vertex) => Comparator.ByName(SortDirection.ASC)(a, b))
                    .iterator()
                    .flatMap(_.toCC[User]).toSeq
            } else {

                val cols = new util.LinkedList[Vertex]()

                val relevance = getVertex.pipe.in(JOIN)
                    .as("members")
                    .aggregate(cols)
                    .inE(SUPPORT).has("sourceId", relevanceTo.get.getId)
                    .back("members")
                    .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
                    .retain(cols)
                    // .hasNot("id", relevanceTo.get.getId)
                    .range(offset, (offset + limit) - 1).iterator()
                    .flatMap(_.toCC[User]).toSeq

                var rv = relevance

                if (rv.length < limit) {
                    val nonRelevance = getVertex.pipe.in(JOIN)
                        // .hasNot("id", relevanceTo.get.getId)
                        .range(offset, (offset + limit) - 1).iterator()
                        .flatMap(_.toCC[User]).toSeq

                    rv = (relevance ++ nonRelevance).distinct
                }

                if (rv.length > limit)
                    rv.slice(0, limit).toSeq
                else
                    rv
            }
        }
    }

    /**
     * Mendapatkan daftar member dari forum yang kebetulan
     * di-support oleh :user
     *
     * @param user user yang akan dicheck supporter-nya.
     * @param limit max return list.
     * @return
     */
    def getMemberMayUserKnow(user: User, limit: Int) = {
        timing(s"Foru($name).getMemberMayUserKnow") {
            user.getVertex.pipe.outE(Forum.CMMYK)
                .has("channelId", getId).range(0, limit - 1)
                .inV()
                .iterator().flatMap(_.toCC[User])
        }
    }


    /**
     * Get forum member count.
     * @param sex [[com.ansvia.digaku.model.SexType]]
     * @return
     */
    def getMemberCount(sex: Int = -1): Int = {

        counter.get("member_all").toInt

//        sex match {
//            case SexType.MALE => counter.get("member_male").toInt
//            case SexType.FEMALE => counter.get("member_female").toInt
//            case _ => counter.get("member_all").toInt
//        }
    }

    /**
     * Increase member count by 1
     */
    def incrementMemberCount(by: Int = 1) {
        counter.incrementBy("member_all", by.toLong)
//        ref.sex match {
//            case SexType.MALE => counter.increment("member_male")
//            case SexType.FEMALE => counter.increment("member_female")
//            case _ =>
//        }
    }

    /**
     * Decrease member count by 1
     */
    def decrementMemberCount(by: Int = 1) {
        counter.decrementBy("member_all", by.toLong)
    }

    /**
     * Mendapatkan jumlah follower forum
     * @return
     */
    def getFollowerCount():Int = {
        counter.get("follower_all").toInt
    }

    /**
     * Increment jumlah follower forum
     * @param by
     */
    def incrementFollowerCount(by: Int = 1) {
        counter.incrementBy("follower_all", by.toLong)
    }

    /**
     * Decrement jumlah follower forum
     * @param by
     */
    def decrementFollowerCount(by:Int = 1) {
        counter.decrementBy("follower_all", by.toLong)
    }


    /**
     * get list staff for forum
     * @param offset starting offset.
     * @param limit ends limit.
     * @param noOwner if true then only return staff not including owner.
     * @return array of staff @see [[com.ansvia.digaku.model.Staff]]
     */
    def getStaffs(offset: Int, limit: Int, noOwner: Boolean = false): Array[Staff] = {
        assert(getVertex != null, "no vertex, unsaved model %s?".format(this))

        if (noOwner) {
            reload().getVertex.pipe.inE(STAFF_AT)
                .filter((edge: Edge) => edge.toStaff.user != owner)
                .range(offset, (offset + limit) - 1).iterator().map { edge =>
                edge.toStaff
            }.toArray
        } else {
            reload().getVertex.pipe.inE(STAFF_AT)
                .range(offset, (offset + limit) - 1).iterator().map { edge =>
                edge.toStaff
            }.toArray
        }

    }

    /**
     * Mendapatkan jumlah staff di forum ini.
     * @return
     */
    def getStaffCount: Int = {
        counter.get("staff").toInt
    }

    def incrementStaffCount(by: Int = 1) {
        counter.incrementBy("staff", by)
    }

    def decrementStaffCount(by: Int = 1) {
        counter.decrementBy("staff", by)
    }


    /**
     * Get staff of this forum by user name.
     * @param name user name.
     * @return
     */
    def getStaffByUserName(name: String): Option[User] = {
        assert(getVertex != null, "no vertex, unsaved model %s ?".format(this))
        val staffO = getVertex.pipe.in(STAFF_AT).has("name", name).headOption
        if (staffO.isDefined)
            staffO.get.toCC[User]
        else
            None
    }

    /**
     * Get staff attribute.
     * @param user user to get staff's attribute.
     * @return
     */
    def getStaffAttribute(user: User): StaffAttribute = {
        reload()
        user.reload()

        if (!isStaff(user))
            throw new DigakuException("%s is not staff at forum %s".format(user.getName, this.name))

        val edge: Edge = getVertex.pipe.inE(STAFF_AT).as("ed").outV().has("id", user.getId)
            .back("ed").asInstanceOf[GremPipeEdge].next()

        StaffAttribute(edge.getOrElse[String]("title", "Staff"), edge.getOrElse[String]("abilities", "").split(COMMA))
    }

    /**
     * Set staff attribute for user.
     *
     * @param user user to set.
     * @param title staff title.
     * @param abilities staff abilities.
     */
    def setStaffAttribute(user: User, title: String, abilities: Array[String]) {
        reload()
        user.reload()

        if (!isStaff(user))
            throw new DigakuException("%s is not staff at forum %s".format(user.getName, this.name))

        val edge: Edge = getVertex.pipe.inE(STAFF_AT).as("ed").outV().has("id", user.getId)
            .back("ed").asInstanceOf[GremPipeEdge].next()

        edge.setProperty("title", title)
        edge.setProperty("abilities", abilities.map(_.toString.trim.toLowerCase).mkString(COMMA))
        edge.setProperty("creationTime", Digaku.engine.dateUtils.nowMilis)

        db.commit()

    }

    /**
     * Check whether user is staff of this forum.
     * @param user to check.
     * @return
     */
    def isStaff(user: User): Boolean = {
        assert(user != null, "user is null")

        Forum.isStaff(user, this)
    }

    /**
     * Get staff forum by id
     * @param id
     * @return
     */
    def getStaffById(id: IDType): Option[User] = {
        assert(getVertex != null, "no vertex, unsaved model %s ?".format(this))
        val staff = getVertex.pipe.in(STAFF_AT).has("id", id).headOption
        if (staff.isDefined)
            staff.get.toCC[User]
        else
            None
    }

    /**
     * Remove forum members.
     * @param users
     */
    def removeMembers(users: User*) {
        this.reload()
        //        transact {
        val v = this.getVertex


        var count: Int = 0

        var maleCount: Int = 0
        var femaleCount: Int = 0

        for (u <- users) {
            if (!isOwner(u)) {
                unsetStaffInternal(u)

                v.pipe.inE(JOIN).has("sourceId", u.getId).remove()

                // hapus juga link mutual join-nya
                // lihat com.ansvia.digaku.dao.UserDao.getMutualJoinChannel
                // @TODO(robin): benerin ini, sementara di-disable dulu karena performance-nya slow
                //                u.getVertex.pipe.inE(Forum.CMMYK).has("channelId", getId).remove()

                // jangan kurangin counter jika counternya 0
                // (tidak mungkin jumlah joined channelnya negatif)
                if (u.getJoinedChannelCount(true) > 0)
                    u.decrementJoinChannelCount()


                count = count + 1

                u.sex match {
                    case SexType.MALE =>
                        maleCount = maleCount + 1
                    case SexType.FEMALE =>
                        femaleCount = femaleCount + 1
                    case _ =>
                }

                u.joinedForums.delete(getId)
                u.getMonitoredPosts(0, u.getMonitorCount().toInt).filter(_.origin == this).foreach{
                    p =>
                        p.userUnMonitor(u)
                }

                //                Foru.isMemberCache.invalidate(u.getId + "-" + this.getId)

                Digaku.engine.eventStream.emit(LeaveChannelEvent(u, this))
            }
        }

        decrementMemberCount(count)
        counter.decrementBy("member_male", maleCount)
        counter.decrementBy("member_female", femaleCount)

        db.commit()

        Forum.markMembers(this, users, state = false)
    }


    /**
     * Get forum member by id or name.
     * @param name user name.
     * @return
     */
    def getMemberByUserName(name: Option[String]): Option[User] = {
        assert(getVertex != null, "no vertex, unsaved model %s ?".format(this))
        val member0 = getVertex.pipe.in(JOIN).has("name", name.get).headOption
        if (member0.isDefined)
            member0.get.toCC[User]
        else
            None
    }

    /**
     * get forum member by id user
     * @param id id user
     * @return
     */
    def getMemberByUserId(id: IDType): Option[User] = {
        assert(getVertex != null, "no vertex, unsaved model %s ?".format(this))
        val member0 = getVertex.pipe.inE(JOIN).has("sourceId", id).outV().headOption
        if (member0.isDefined)
            member0.get.toCC[User]
        else
            None
    }

    /**
     * Check whether user is member of this forum.
     * @param user user to check.
     * @return
     */
    def isMember(user: User, cached: Boolean = true): Boolean = {
        assert(getVertex != null, "no vertex, unsaved forum model? " + this)
        assert(user != null, "user null?")
        assert(user.getVertex != null, "unsaved user?")

        Forum.isMember(user, this, cached)

    }


    /**
     * set staff ability.
     * @param user user to set.
     * @param abilities @see [[com.ansvia.digaku.model.Ability]]
     */
    def setStaffAbility(user: User, abilities: Array[String]) {
        require(user != null)
        require(getVertex != null)

        val v = getVertex.pipe.inE(STAFF_AT).as("edge")
            .outV().has("id", user.getId).back("edge").next().asInstanceOf[Edge]
        v.set("abilities", abilities.map(_.toString.trim.toLowerCase)
            .mkString(COMMA))

        db.commit()
    }

    /**
     * get property abilities dari edge staff_at
     * @param user user
     * @return list of [[com.ansvia.digaku.model.Ability]]
     */
    def getStaffAbility(user: User): Array[String] = {
        require(user != null)

//        reload()
        gremlin(getVertex).inE(STAFF_AT).as("edge").outV()
            .has("id", user.getId).back("edge").headOption.map { x =>

            val edge = x.asInstanceOf[Edge]

            if (edge != null) {
                val str: String = edge.getProperty("abilities").asInstanceOf[String]
                if (str != null) {
                    str.toString.split(COMMA).map(_.trim)
                } else
                    Array.empty[String]
            } else
                Array.empty[String]

        } getOrElse Array.empty[String]
    }

    /**
     * Check whether staff has ability for forum.
     * @param user user to check (must be staff).
     * @param ability @see [[com.ansvia.digaku.model.Ability]]
     * @return
     */
    def hasAbility(user: User, ability: String): Boolean = {
        isOwner(user) || user.role == UserRole.SUPER_ADMIN ||
            getStaffAbility(user).contains(ability)
    }

    /**
     * remove staff by user model
     * @param users user
     */
    def unsetStaff(users: User*) {
        require(users != null)

        for (user <- users) {
            unsetStaffInternal(user)
        }
        db.commit()
    }

    /**
     * same as [[com.ansvia.digaku.model.Forum#unsetStaff]]
     * but not wrapped in transact
     * @see [[com.ansvia.digaku.model.Forum#unsetStaff]]
     * @param user user to unset
     */
    private def unsetStaffInternal(user: User) {
        val edges = getVertex.pipe.inE(STAFF_AT).as("edge").outV()
            .has("id", user.getId).back("edge").asInstanceOf[GremPipeEdge]

        if (edges.hasNext) {
            db.removeEdge(edges.next())
            //            getVertex.setProperty("staffCount", getVertex.getOrElse("staffCount", 0) - 1)
            counter.decrement("staff")

            user.moderatedForums.delete(getId)

            Digaku.engine.eventStream.emit(RemoveStaffEvent(user, this))

        }

        //        Forum.isStaffCache.invalidate(user.getId + "-" + this.getId)
    }

    /**
     * Menambahkan follower ke sebuah forum
     * @param users Users yang akan ditambahkan
     */
    def addFollowers(users:User*): Unit = {
        for (u <- users) {
            if(!isFollower(u)) {
                val edge = u.getVertex.addEdge(FOLLOW, getVertex)
                edge.setProperty("sourceId", u.getId)
                edge.setProperty("targetId", this.getId)

                incrementFollowerCount()
                u.incrementFollowingForumCount()
            }
        }

        db.commit()

        Forum.markFollower(this, users, state = true)
    }

    /**
     * Remove follower dari sebuah forum
     * @param users Users yang akan diremove
     */
    def removeFollowers(users:User*):Unit = {
        for (u <- users) {
            if (isFollower(u)) {
                getVertex.pipe.inE(FOLLOW).has("sourceId", u.getId).remove()

                decrementFollowerCount()
                u.decrementFollowingForumCount()
            }
        }

        db.commit()

        Forum.markFollower(this, users, state = false)
    }

    /**
     * Cek apakah user sebagai follower
     * @param user
     * @return
     */
    def isFollower(user: User): Boolean = {
        Forum.isFollower(user, this)
    }

    /**
     * Mendapatkan followers dari sebuah forum
     * @param offset
     * @param limit
     * @return
     */
    def getFollowers(offset:Int, limit:Int):Seq[User] = {
        db.getVertex(getId).pipe.in(FOLLOW)
            .dedup()
            .range(offset, (offset + limit) - 1)
            .order((a: Vertex, b: Vertex) => Comparator.ByName(SortDirection.ASC)(a, b))
            .iterator()
            .flatMap(_.toCC[User])
            .toSeq
    }

    /**
     * Remove all followers from forum.
     */
    def removeAllFollowers():Unit = {
        /*db.getVertex(getId).pipe.in(FOLLOW).iterator().foreach { v =>
            v.toCC[User].foreach { u =>
                removeFollowers(u)
            }
        }*/

        val followers = gremlin(getVertex).in(FOLLOW).iterator().flatMap(_.toCC[User]).toSeq
        removeFollowers(followers: _*)
    }

    /**
     * Get Sticky Count
     * @return
     */
    def stickyPostCount: Int = {
        counter.get("stickyPost").toInt
    }

    def incrementStickyPostCount(): Unit = {
        counter.increment("stickyPost")
    }

    def decrementStickyPostCount(): Unit = {
        counter.decrement("stickyPost")
    }

    //    /**
    //     * Mengembalikan jumlah sticky simple post
    //     * @return
    //     */
    //    def stickySimplePostCount:Int = {
    //        getVertex.pipe.out(COLLECTION_VERTEX)
    //            .has("kind", CollectionVertex.Kind.STREAM).range(0,1)
    //            .outE(STREAM)
    //            .has("post.sticky", true)
    //            .has("post.kind", PostKind.SIMPLE_POST)
    //            .count().toInt
    //    }

    /**
     * Mengembalikan jumlah sticky article
     * @return
     */
    def stickyArticleCount: Int = {
        getVertex.pipe.out(COLLECTION_VERTEX)
            .has("kind", CollectionVertex.Kind.STREAM).range(0, 1)
            .outE(STREAM)
            .has("post.sticky", true)
            .has("post.kind", PostKind.ARTICLE)
            .count().toInt
    }

    /**
     * Mengembalikan jumlah sticky event
     * @return
     */
    def stickyEventCount: Int = {
        //        getVertex.pipe.in(ORIGIN)
        //            .has("_class_COMMAcom.ansvia.digaku.model.Event")
        //            .has("sticky", true)
        //            .count()
        //            .toInt
        getVertex.pipe.out(COLLECTION_VERTEX)
            .has("kind", CollectionVertex.Kind.STREAM).range(0, 1)
            .outE(STREAM)
            .has("post.sticky", true)
            .has("post.kind", PostKind.EVENT)
            .count().toInt
    }

    /**
     * Mengembalikan jumlah sticky picture
     * @return
     */
    def stickyPictureCount: Int = {
        //        getVertex.pipe.in(ORIGIN)
        //            .has("_class_", "com.ansvia.digaku.model.Picture")
        //            .has("sticky", true)
        //            .has("sticky", true)
        //            .count()
        //            .toInt
        getVertex.pipe.out(COLLECTION_VERTEX)
            .has("kind", CollectionVertex.Kind.STREAM).range(0, 1)
            .outE(STREAM)
            .has("post.sticky", true)
            .has("post.kind", PostKind.PICTURE)
            .count().toInt
    }

    /**
     * Mengembalikan jumlah sticky deal
     * @return
     */
    def stickyDealCount: Int = {
        //        getVertex.pipe.in(ORIGIN).has("_class_COMMAcom.ansvia.digaku.model.Deal")
        //            .has("sticky", true)
        //            .count()
        //            .toInt
        getVertex.pipe.out(COLLECTION_VERTEX)
            .has("kind", CollectionVertex.Kind.STREAM).range(0, 1)
            .outE(STREAM)
            .has("post.sticky", true)
            .has("post.kind", PostKind.DEAL)
            .count().toInt
    }

//    /**
//     * Get user post stream, termasuk stream dirinya.
//     * @param offset stream offset.
//     * @param limit stream limit.
//     * @tparam T
//     * @return
//     */
//    def getStream[T <: Post : Manifest](offset: Int, limit: Int, sticky: Int = StickyState.ALL, maxId: Option[IDType] = None, sinceId: Option[IDType] = None): Seq[T] = {
//        //        timing("Foru(%s).getStream(sticky=%s)".format(name,sticky)){
//
//        val postKind = PostKind.kindStrClassToInt(manifest[T].runtimeClass.toString.substring(6))
//
//        var pipe: GremPipeEdge = getVertex.pipe.out(COLLECTION_VERTEX)
//            .has("kind", CollectionVertex.Kind.STREAM).range(0, 1)
//            .outE(STREAM)
//
//        if (postKind != PostKind.OTHER) {
//            pipe = pipe.has("post.kind", postKind).asInstanceOf[GremPipeEdge]
//        } else {
//            pipe = pipe.filter { (ed: Edge) =>
//                Seq(PostKind.ARTICLE, PostKind.DEAL, PostKind.SIMPLE_POST, PostKind.QUESTION)
//                    .contains(ed.getOrElse("post.kind", 0))
//            }
//        }
//
//        if (sticky != StickyState.ALL) {
//            pipe = pipe.has("post.sticky", {
//                sticky match {
//                    case StickyState.STICKY => true
//                    case StickyState.UNSTICKY => false
//                }
//            }).asInstanceOf[GremPipeEdge]
//        }
//
//        sinceId foreach { _sinceId =>
//            pipe = pipe.as("ed").inV().has("id", T.gt, _sinceId).back("ed").asInstanceOf[GremPipeEdge]
//        }
//
//        maxId foreach { _maxId =>
//            // Menggunakan T.lt karena result-nya harus exclusive tidak termasuk content dari maxId-nya
//            pipe = pipe.as("ed").inV().has("id", T.lt, _maxId).back("ed").asInstanceOf[GremPipeEdge]
//        }
//
//        pipe
//            .inV().hasNot("deleted", true).asInstanceOf[GremPipeVertex]
//            .range(offset, offset + (limit - 1))
//            .order(Comparator.timeOrdererDesc)
//            .iterator()
//            .flatMap(_.toCC[T])
//            .toSeq
//        //        }
//    }
//
//    /**
//     * Get forum post stream.
//     * Sama seperti [[com.ansvia.digaku.model.Forum.getStream]] bedanya ini return StreamObject
//     * bukan T itu sendiri.
//     * @param offset stream offset.
//     * @param limit stream limit.
//     * @tparam T
//     * @return
//     */
//    def getStreamObj[T <: Post : Manifest](offset: Int, limit: Int, sticky: Int = StickyState.ALL): Seq[StreamObject] = {
//
//        val postKind = PostKind.kindStrClassToInt(manifest[T].runtimeClass.toString.substring(6))
//
//        var pipe: GremPipeEdge = getVertex.pipe.out(COLLECTION_VERTEX)
//            .has("kind", CollectionVertex.Kind.STREAM).range(0, 1)
//            .outE(STREAM)
//
//        if (postKind != PostKind.OTHER) {
//            pipe = pipe.has("post.kind", postKind).asInstanceOf[GremPipeEdge]
//        } else {
//            pipe = pipe.filter { (ed: Edge) =>
//                Seq(PostKind.ARTICLE, PostKind.DEAL, PostKind.SIMPLE_POST, PostKind.QUESTION)
//                    .contains(ed.getOrElse("post.kind", 0))
//            }
//        }
//
//        if (sticky != StickyState.ALL) {
//            pipe = pipe.has("post.sticky", {
//                sticky match {
//                    case StickyState.STICKY => true
//                    case StickyState.UNSTICKY => false
//                }
//            }).asInstanceOf[GremlinPipeline[Vertex, Edge]]
//        }
//
//        pipe.as("ed").inV()
//            // filter out deleted posts
//            .hasNot("deleted", true).asInstanceOf[GremPipeVertex]
//            .range(offset, offset + (limit - 1))
//            .order(Comparator.timeOrdererDesc)
//            .back("ed").asInstanceOf[GremPipeEdge]
//            .iterator().toSeq
//            .flatMap { ed =>
//                if (ed.getOrElse("isRetalk", false)) {
//                    //                    val postV = ed.getVertex(Direction.IN)
//                    //                    if (postV != null){
//                    ed.getProperty[IDType]("retalk.userId") match {
//                        case retalkUserId: Long =>
//                            val channelName = ed.getOrElse("retalk.forum", "")
//                            User.getById(retalkUserId).flatMap { user =>
//                                ed.getVertex(Direction.IN).toCC[T].map { post =>
//                                    val time = ed.getOrElse("retalk.timestamp", 0L)
//                                    RetalkStreamObject(user, post, channelName, new Date(time))
//                                }
//                            }
//                        //                            case _ => None // unreachable
//                    }
//                    //                    }else{
//                    //                        None
//                    //                    }
//                } else {
//                    val inV = ed.getVertex(Direction.IN)
//                    inV.toCC[T].map(PostStreamObject)
//                }
//            }.toSeq
//    }
//
//    // @TODO(robin): menurut @fajr di https://repository.mindtalk.com/digaku/digaku2/merge_requests/618
//    //               ini lambat, maka perlu lakukan investigasi lebih lanjut.
//    /**
//     * Dapatkan semua tipe post stream.
//     * @param maxId id dari post yang mana merupakan batas dari stream yang ingin kita dapatkan. (post yang dikembalikan
//     *              mempunyai id lebih kecil atau sama dengan id ini)
//     *              set None untuk mendapatkan dari latest.
//     * @param sinceId id terkecil dari post yang akan kita dapatkan. (post yang dikembalikan lebih besar
//     *                dari id ini)
//     * @param limit batas dari kembalian.
//     * @param sticky state sticky-nya. lihat [[com.ansvia.digaku.model.StickyState]]
//     * @return
//     */
//    def getStreamAll(maxId: Option[IDType] = None, sinceId: Option[IDType] = None,
//                     offset: Int, limit: Int, sticky: Int = StickyState.ALL): Seq[StreamObject] = {
//
//        var pipe = getVertex.pipe.out(COLLECTION_VERTEX)
//            .has("kind", CollectionVertex.Kind.STREAM).range(0, 1)
//            .outE(STREAM)
//
//        if (sticky != StickyState.ALL) {
//            pipe = pipe.has("post.sticky", {
//                sticky match {
//                    case StickyState.STICKY => true
//                    case StickyState.UNSTICKY => false
//                }
//            }).asInstanceOf[GremPipeEdge]
//        }
//
//        sinceId foreach { _sinceId =>
//            pipe = pipe.as("ed").inV().has("id", T.gt, _sinceId).back("ed").asInstanceOf[GremPipeEdge]
//        }
//
//        maxId foreach { _maxId =>
//            // behaviour maxId harusnya sama dengan sinceId tidax inclusive (tidak termasuk post maxId)
//            pipe = pipe.as("ed").inV().has("id", T.lt, _maxId).back("ed").asInstanceOf[GremPipeEdge]
//        }
//
//        pipe.as("ed").inV()
//            // filter out deleted posts
//            .hasNot("deleted", true).asInstanceOf[GremPipeVertex]
//            .range(offset, offset + (limit - 1))
//            .order(Comparator.timeOrdererDesc)
//            .back("ed").asInstanceOf[GremPipeEdge]
//            .iterator().toSeq
//            .flatMap { ed =>
//
//
//                val inV = ed.getVertex(Direction.IN)
//                inV.getOrElse("_class_", "") match {
//                    case "com.ansvia.digaku.model.Post" |
//                         "com.ansvia.digaku.model.SimplePost" |
//                         "com.ansvia.digaku.model.Article" =>
//                        inV.toCC[Post].map(PostStreamObject)
//                    case "com.ansvia.digaku.model.Picture" =>
//                        inV.toCC[Picture].map(PostStreamObject)
//                    //                    case "com.ansvia.digaku.model.PictureForum" =>
//                    //                        inV.toCC[PictureForum].map(PostStreamObject)
//                    case "com.ansvia.digaku.model.Event" =>
//                        inV.toCC[Event].map(PostStreamObject)
//                    //                    case "com.ansvia.digaku.model.RetalkWrapper" =>
//                    //                        inV.toCC[RetalkWrapper].map(PostStreamObject)
//
//                }
//                //            }
//
//            }
//    }
//
//
//    /**
//     * Get popular discussion stream
//     * @param offset stream offset
//     * @param limit stream limit
//     * @tparam T
//     * @return
//     */
//    def getPopularDiscussionStream[T <: Post : Manifest](offset: Int, limit: Int): Seq[T] = {
//
//        val postKind = PostKind.kindStrClassToInt(manifest[T].runtimeClass.toString.substring(6))
//
//        var pipe = getVertex.pipe.out(COLLECTION_VERTEX)
//            .has("kind", CollectionVertex.Kind.STREAM).range(0, 1).outE(STREAM)
//
//        if (postKind != PostKind.OTHER) {
//            pipe = pipe.has("post.kind", postKind).asInstanceOf[GremPipeEdge]
//        }
//
//        // @TODO(robin): ini mungkin tidak working,
//        //               perlu disesuaikan karena `responseCount` sudah digantikan menggunakan counter provider
//        pipe.range(0, 100).inV()
//            .has("responseCount", T.gt, 1).asInstanceOf[GremPipeVertex]
//            .order { (a:Vertex, b:Vertex) =>
//                -a.getOrElse[Int]("responseCount", 0).compareTo(b.getOrElse[Int]("responseCount", 0))
//            }
////            .sort((a, b) =>  -a.getOrElse[Int]("responseCount", 0).compareTo(b.getOrElse[Int]("responseCount", 0)))
////            .range(offset, offset + (limit-1))
//            .iterator()
//            .flatMap(_.toCC[T])
//            .toSeq.slice(offset, offset + limit - 1)
//    }
//
//    /**
//     * Get active post stream
//     * @param offset stream offset
//     * @param limit stream limit
//     * @tparam T
//     * @return
//     */
//    def getActivePostStream[T <: Post : Manifest](offset: Int, limit: Int): Seq[T] = {
//        timing(s"Foru($name).getActivePostStream") {
//            val postKind = PostKind.kindStrClassToInt(manifest[T].runtimeClass.toString.substring(6))
//
//            var pipe = getVertex.pipe.out(COLLECTION_VERTEX)
//                .has("kind", CollectionVertex.Kind.STREAM).range(0, 1)
//                .outE(STREAM)
//
//            if (postKind != PostKind.OTHER) {
//                pipe = pipe.has("post.kind", postKind).asInstanceOf[GremPipeEdge]
//            }
//
//            pipe.inV().as("post")
//                .in(RESPONSE_OF).back("post").asInstanceOf[GremPipeVertex]
////                .sort((a, b) => -a.getOrElse[Long]("creationTime", 0L).compareTo(b.getOrElse[Long]("creationTime", 0L)) )
//                .order { (a:Vertex, b:Vertex) =>
//                    -a.getOrElse[Long]("creationTime", 0L).compareTo(b.getOrElse[Long]("creationTime", 0L))
//                }
//                .range(offset, offset + (limit - 1))
//                .iterator()
//                .flatMap(_.toCC[T])
//                .toSeq
//        }
//
//
//    }
//
//    /**
//     * Get active photo stream
//     * @param offset
//     * @param limit
//     * @tparam T
//     */
//    def getActivePictureStream[T <: Picture : Manifest](offset: Int, limit: Int): Seq[T] = {
//        timing(s"Foru($name).getActivePictureStream") {
//
//            val postKind = PostKind.kindStrClassToInt(manifest[T].runtimeClass.toString.substring(6))
//
//            var pipe: GremPipeVertex = getVertex.pipe.out(COLLECTION_VERTEX)
//                .has("kind", CollectionVertex.Kind.STREAM).range(0, 1)
//                .out(STREAM).range(0, 100 - 1)
//
//            if (postKind != PostKind.OTHER) {
//                pipe = pipe.has("_class_", PostKind.kindIntToClassStr(postKind)).asInstanceOf[GremPipeVertex]
//            }
//
//            pipe.as("post")
//                .in(RESPONSE_OF).range(0,100-1).back("post").asInstanceOf[GremPipeVertex]
////                .sort((a, b) => -a.getOrElse[Long]("creationTime", 0L).compareTo(b.getOrElse[Long]("creationTime", 0L)) )
//                .order { (a:Vertex, b:Vertex) =>
//                    -a.getOrElse[Long]("creationTime", 0L).compareTo(b.getOrElse[Long]("creationTime", 0L))
//                }
//                .range(offset, offset + (limit - 1))
//                .iterator()
//                .flatMap(_.toCC[T])
//                .toSeq
//        }
//    }


//    /**
//     * get total post count in this forum.
//     * @return
//     */
//    def getArticleCount(stickyState: Int): Int = {
//        //        reload().getVertex.pipe.in(ORIGIN).has("_class_", "com.ansvia.digaku.model.Article").count()
//        //        stickyState match {
//        //            case StickyState.ALL =>
//        //                reload().getVertex.pipe.outE(STREAM).has("post.kind", PostKind.ARTICLE).count()
//        //            case StickyState.STICKY =>
//        //                reload().getVertex.pipe.outE(STREAM)
//        //                    .has("post.kind", PostKind.ARTICLE)
//        //                    .has("post.sticky", true).count()
//        //            case StickyState.UNSTICKY =>
//        //                reload().getVertex.pipe.outE(STREAM)
//        //                    .has("post.kind", PostKind.ARTICLE)
//        //                    .has("post.sticky", false).count()
//        //        }
//        stickyState match {
//            case StickyState.ALL =>
//                //                reload().getVertex.getOrElse("articleCount", 0)
//                getArticleCount
//            case StickyState.STICKY =>
//                // @TODO(robin): fix this
//                reload().getVertex.pipe.out(COLLECTION_VERTEX)
//                    .has("kind", CollectionVertex.Kind.STREAM).outE(STREAM)
//                    .has("post.kind", PostKind.ARTICLE)
//                    .has("post.sticky", true).count().toInt
//            case StickyState.UNSTICKY =>
//                // @TODO(robin): fix this
//                reload().getVertex.pipe.out(COLLECTION_VERTEX)
//                    .has("kind", CollectionVertex.Kind.STREAM).outE(STREAM)
//                    .has("post.kind", PostKind.ARTICLE)
//                    .has("post.sticky", false).count().toInt
//        }
//
//    }


    def incrementSimplePostCount(by: Int = 1) {
        counter.incrementBy("simple_post", by)
    }

    def decrementSimplePostCount(by: Int = 1) {
        counter.decrementBy("simple_post", by)
    }

    //    /**
    //     * Untuk mendapatkan jumlah simple post pada forum ini.
    //     *
    //     * @return
    //     */
    //    def getSimplePostCount = {
    ////        reload().getVertex.getOrElse("simplePostCount", 0)
    //        counter.get("simple_post").toInt
    //    }


    /**
     * get total picture count in this forum.
     * @return
     */
    def getPictureCount: Int = {
        //        reload().getVertex.pipe.in(ORIGIN).has("_class_", "com.ansvia.digaku.model.Picture").count()
        //        reload().getVertex.pipe.outE(STREAM).has("post.kind", PostKind.PICTURE).count()
        //        reload().getVertex.getOrElse("pictureCount", 0)
        counter.get("picture_content").toInt
    }

    /**
     * increase picture content count.
     */
    def incrementPictureContentCount() {
        counter.increment("picture_content")
    }

    /**
     * Decrease picture content count.
     */
    def decrementPictureContentCount() {
        counter.decrement("picture_content")
    }


    def incrementContentCount() {
        counter.increment("all_content")
    }

    def incrementContentCount(by: Int) {
        counter.incrementBy("all_content", by)
    }

    def decrementContentCount() {
        counter.decrement("all_content")
    }

    def decrementContentCount(by: Int) {
        counter.decrementBy("all_content", by)
    }

    def getContentCount = counter.get("all_content").toInt

    def incrementArticleCount() {
        counter.increment("article")
    }

    def incrementArticleCount(by:Int) {
        counter.incrementBy("article", by)
    }

    def decrementArticleCount() {
        counter.decrement("article")
    }

    def decrementArticleCount(by:Int) {
        counter.decrementBy("article", by)
    }

    def getArticleCount = counter.get("article").toInt

    /**
     * get event count in this forum.
     * @return
     */
    def getEventCount =
    //        reload().getVertex.pipe.in(ORIGIN).has("_class_", "com.ansvia.digaku.model.Event").count()
        getVertex.getOrElse("eventCount", 0)

    /**
     * get deal count in this forum.
     * @return
     */
    def getDealCount =
    //        reload().getVertex.pipe.in(ORIGIN).has("_class_", "com.ansvia.digaku.model.Deal").count()
        getVertex.getOrElse("dealCount", 0)

//    /**
//     * Get event stream forum
//     * @param offset stream offset
//     * @param limit stream limit
//     * @param sticky sticky stream event
//     * @return
//     */
//    def getEventStream(offset: Int, limit: Int, sticky: Int = StickyState.ALL): Seq[Event] = {
//
//        var pipe: GremPipeEdge = getVertex.pipe.out(COLLECTION_VERTEX)
//            .has("kind", CollectionVertex.Kind.STREAM).outE(STREAM)
//            .has("post.kind", PostKind.EVENT).asInstanceOf[GremPipeEdge]
//
//
//        if (sticky != StickyState.ALL) {
//            pipe = pipe.has("post.sticky", {
//                sticky match {
//                    case StickyState.STICKY => true
//                    case StickyState.UNSTICKY => false
//                }
//            }).asInstanceOf[GremPipeEdge]
//        }
//
//        pipe//.sort(Comparator.ByCreationTime(SortDirection.DESC))
//            .range(offset, offset + (limit-1))
//            .inV()
//            .iterator()
//            .flatMap(_.toCC[Event])
//            .toSeq
//    }

//    /**
//     * Get event stream forum
//     * @param offset stream offset
//     * @param limit stream limit
//     * @param sticky sticky stream event
//     * @return
//     */
//    // @TODO(temon): add to unittest
//    def getEventStreamObj(offset: Int, limit: Int, sticky: Int = StickyState.ALL): Seq[StreamObject] = {
//        var pipe = getVertex.pipe.out(COLLECTION_VERTEX)
//            .has("kind", CollectionVertex.Kind.STREAM).range(0, 1).outE(STREAM)
//            .has("post.kind", PostKind.EVENT).asInstanceOf[GremPipeEdge]
//
//
//        if (sticky != StickyState.ALL) {
//            pipe = pipe.has("post.sticky", {
//                sticky match {
//                    case StickyState.STICKY => true
//                    case StickyState.UNSTICKY => false
//                }
//            }).asInstanceOf[GremPipeEdge]
//        }
//
//        pipe//.sort(Comparator.ByCreationTime(SortDirection.DESC))
//            .range(offset, offset + (limit-1))
//            .iterator().toSeq
//            .flatMap { ed =>
//                if (ed.getOrElse("isRetalk", false)) {
//                    ed.getProperty[Long]("retalk.userId") match {
//                        case retalkUserId: Long =>
//                            val channelName = ed.getOrElse("retalk.forum", "")
//                            User.getById(retalkUserId).flatMap { user =>
//                                ed.getVertex(Direction.IN).toCC[Event].map { post =>
//                                    val time = ed.getOrElse("retalk.timestamp", 0L)
//                                    RetalkStreamObject(user, post, channelName, new Date(time))
//                                }
//                            }
//                        //                        case _ => None // unreachable
//                    }
//                } else {
//                    val inV = ed.getVertex(Direction.IN)
//                    inV.toCC[Event].map(PostStreamObject)
//                }
//            }.toSeq
//    }
//
//    /**
//     * Get picture stream
//     * @param offset stream picture
//     * @param limit stream limit
//     * @param sticky sticky stream picture
//     * @return
//     */
//    def getPictureStream(offset: Int, limit: Int, sticky: Int = StickyState.ALL, maxId: Option[IDType] = None, sinceId: Option[IDType] = None): Seq[Picture] = {
//
//        var pipe = getVertex.pipe.out(COLLECTION_VERTEX)
//            .has("kind", CollectionVertex.Kind.STREAM).range(0, 1).outE(STREAM)
//            .has("post.kind", PostKind.PICTURE).asInstanceOf[GremPipeEdge]
//
//        if (sticky != StickyState.ALL) {
//            pipe = pipe.has("post.sticky", {
//                sticky match {
//                    case StickyState.STICKY => true
//                    case StickyState.UNSTICKY => false
//                }
//            }).asInstanceOf[GremlinPipeline[Vertex, Edge]]
//        }
//
//        sinceId foreach { _sinceId =>
//            pipe = pipe.as("ed").inV().has("id", T.gt, _sinceId).back("ed").asInstanceOf[GremPipeEdge]
//        }
//
//        maxId foreach { _maxId =>
//            // Menggunakan T.lt karena result-nya harus exclusive tidak termasuk Picture dari maxId-nya
//            pipe = pipe.as("ed").inV().has("id", T.lt, _maxId).back("ed").asInstanceOf[GremPipeEdge]
//        }
//
//        pipe//.sort(Comparator.ByCreationTime(SortDirection.DESC))
//            .range(offset, offset + (limit-1))
//            .inV()
//            .iterator()
//            .flatMap(_.toCC[Picture])
//            .toSeq
//    }
//
//    /**
//     * Get picture stream
//     * @param offset stream picture
//     * @param limit stream limit
//     * @param sticky sticky stream picture
//     * @return
//     */
//    // @TODO(robin): add to unittest
//    def getPictureStreamObj(offset: Int, limit: Int, sticky: Int = StickyState.ALL): Seq[StreamObject] = {
//        var pipe = getVertex.pipe.out(COLLECTION_VERTEX)
//            .has("kind", CollectionVertex.Kind.STREAM).range(0, 1).outE(STREAM)
//            .has("post.kind", PostKind.PICTURE).asInstanceOf[GremlinPipeline[Vertex, Edge]]
//
//        if (sticky != StickyState.ALL) {
//            pipe = pipe.has("post.sticky", {
//                sticky match {
//                    case StickyState.STICKY => true
//                    case StickyState.UNSTICKY => false
//                }
//            }).asInstanceOf[GremlinPipeline[Vertex, Edge]]
//        }
//
//        pipe//.sort(Comparator.ByCreationTime(SortDirection.DESC))
//            .range(offset, offset + (limit-1))
//            .iterator().toSeq
//            .flatMap { ed =>
//                if (ed.getOrElse("isRetalk", false)) {
//                    val retalkUserId = ed.getProperty[Long]("retalk.userId")
//
//                    val channelName = ed.getOrElse("retalk.forum", "")
//
//                    User.getById(retalkUserId).flatMap { user =>
//                        ed.getVertex(Direction.IN).toCC[Picture].map { post =>
//                            val time = ed.getOrElse("retalk.timestamp", 0L)
//                            RetalkStreamObject(user, post, channelName, new Date(time))
//                        }
//                    }
//
//                } else {
//                    val inV = ed.getVertex(Direction.IN)
//                    inV.toCC[PictureBase].map(PostStreamObject)
//                }
//            }.toSeq
//    }
//
//    /**
//     * mendapatkan stream dengan kind stream tertentu.
//     * @param streamKind type dari stream see [[com.ansvia.digaku.model.StreamKind]]
//     * @param offset deprecated jangan gunakan offset.
//     * @param limit
//     * @param maxId id dari post yang mana merupakan batas dari stream yang ingin kita dapatkan.
//     * @param sinceId id dari post yang merupakan batas bawah dari stream yang ingin kita dapatkan.
//     * @param sticky
//     * @param year creation year where stream wanted
//     * @param month creation month where stream wanted
//     * @return
//     */
//    def getStreamKindObj(streamKind: Int, offset: Int, limit: Int, maxId: Option[IDType] = None,
//                         sinceId: Option[IDType] = None, sticky: Int = StickyState.ALL,
//                         year: Option[Int] = None, month: Option[Int] = None): Seq[StreamObject] = {
//
//        timing(s"Foru($name).getStreamKindObj") {
//            //            /**
//            //             * rawan kena super vertex bila limit besar (1000) atau
//            //             * mentravers antar collection vertex dengan jangkauan cukup besar antara maxId dan sinceID
//            //             * dan mengandung post cukup banyak
//            //             * bahaya untuk channel2 besar seperti GELIGILA atau Movies
//            //             * namun tetap diperlukan untuk forum-forum kecil seperti managementpractice atau ilearn
//            //             * solusi sementara: harus dibatesin post count nya.
//            //             */
//            //            val LIMIT_POST_COUNT = 500
//
//            tx { t =>
//
//                //                var pipe = if(year.isDefined && month.isDefined) {
//                //
//                //                    /**
//                //                     * If year and month parameter specified
//                //                     */
//                //
//                //                    val cv = CollectionVertex.getMonthBasedInline(this, CollectionVertex.Kind.STREAM, year.get, month.get)
//                //
//                //                    val _cv = db.getVertex(cv.getId)
//                //
//                //                    _cv.pipe.outE(STREAM)
//                //
//                //                } else {
//                //
//                //                    /**
//                //                     * If year and month parameter not specified
//                //                     * run an iteration
//                //                     */
//                //
//                //                    val collectionVertexCount = getVertex.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM).count().toInt
//                //
//                //                    var postCount = 0
//                //                    var collVIter = 0
//                //
//                //                    while (postCount < limit && collVIter < collectionVertexCount && postCount < LIMIT_POST_COUNT) {
//                //                        collVIter += 1
//                //                        var pipeC = getVertex.pipe.out(COLLECTION_VERTEX)
//                //                            .has("kind", CollectionVertex.Kind.STREAM).range(0,collVIter).outE(STREAM)
//                //
//                //                        if (streamKind != StreamKind.ALL) {
//                //                            pipeC = pipeC.has("streamKind", streamKind)
//                //                                .asInstanceOf[GremPipeEdge]
//                //                        }
//                //
//                //                        if (sticky != StickyState.ALL){
//                //                            pipeC = pipeC.has("post.sticky", {
//                //                                sticky match {
//                //                                    case StickyState.STICKY => true
//                //                                    case StickyState.UNSTICKY => false
//                //                                }
//                //                            }).asInstanceOf[GremPipeEdge]
//                //                        }
//                //
//                //                        sinceId map { _sinceId =>
//                //                            pipeC = pipeC.as("ed").inV().has("id", T.gt, _sinceId).back("ed").asInstanceOf[GremPipeEdge]
//                //                        }
//                //
//                //                        maxId map { _maxId =>
//                //                            // Menggunakan T.lt karena result-nya harus exclusive tidak termasuk post dari maxId-nya
//                //                            pipeC = pipeC.as("ed").inV().has("id", T.lt, _maxId).back("ed").asInstanceOf[GremPipeEdge]
//                //                        }
//                //
//                //                        postCount = pipeC.range(offset, offset + limit-1).count().toInt
//                //                    }
//                //
//                //                    getVertex.pipe.out(COLLECTION_VERTEX)
//                //                        .has("kind", CollectionVertex.Kind.STREAM).range(0,collVIter).outE(STREAM)
//                //
//                //                }
//
//
//                var pipe = if (year.isDefined && month.isDefined) {
//
//                    /**
//                     * If year and month parameter specified
//                     */
//
//                    val cv = CollectionVertex.getMonthBasedInline(this, CollectionVertex.Kind.STREAM, year.get, month.get)
//
//                    val _cv = db.getVertex(cv.getId)
//
//                    _cv.pipe.outE(STREAM)
//
//                } else {
//
//                    /**
//                     * If year and month parameter not specified
//                     * do a 2 level graph traversal.
//                     */
//
//                    getVertex.pipe.out(COLLECTION_VERTEX)
//                        .has("kind", CollectionVertex.Kind.STREAM).outE(STREAM)
//
//                }
//
//
//                sinceId foreach { _sinceId =>
//                    //                    pipe = pipe.as("ed").inV().has("id", T.gt, _sinceId).back("ed").asInstanceOf[GremPipeEdge]
//                    pipe = pipe.has("targetId", T.gt, _sinceId).asInstanceOf[GremPipeEdge]
//                }
//
//                maxId foreach { _maxId =>
//                    // Menggunakan T.lt karena result-nya harus exclusive tidak termasuk post dari maxId-nya
//                    //                    pipe = pipe.as("ed").inV().has("id", T.lt, _maxId).back("ed").asInstanceOf[GremPipeEdge]
//                    pipe = pipe.has("targetId", T.lt, _maxId).asInstanceOf[GremPipeEdge]
//                }
//
//                if (sinceId.isEmpty && maxId.isEmpty) {
//                    // apabila sinceId dan maxId empty, maka perlu di-range,
//                    // karena tanpa sinceId/maxId Titan akan me-map semua edges yang mengakibatkan super node problem
//                    // jadi untuk jaga-jaga range ke batas max wajar 1jt edges max.
//                    pipe = pipe.range(0, 1000000)
//                }
//
//                if (streamKind != StreamKind.ALL) {
//                    pipe = pipe.has("streamKind", streamKind)
//                        .asInstanceOf[GremPipeEdge]
//                }
//
//                if (sticky != StickyState.ALL) {
//                    pipe = pipe.has("post.sticky", {
//                        sticky match {
//                            case StickyState.STICKY => true
//                            case StickyState.UNSTICKY => false
//                        }
//                    }).asInstanceOf[GremPipeEdge]
//                }
//
//
//                pipe.range(offset, offset + limit - 1)
//                    .iterator().toSeq.flatMap { ed =>
//                    val inV = ed.getVertex(Direction.IN)
//
//                    inV.getProperty[String]("_class_") match {
//                        case "com.ansvia.digaku.model.Article" | "com.ansvia.digaku.model.SimplePost" |
//                             "com.ansvia.digaku.model.Deal" | "com.ansvia.digaku.model.Question" =>
//                            inV.toCC[Post] map (PostStreamObject)
//                        case "com.ansvia.digaku.model.Picture" =>
//                            inV.toCC[Picture].map(PostStreamObject)
//                        case "com.ansvia.digaku.model.Event" =>
//                            inV.toCC[Event].map(PostStreamObject)
//                        //                        case "com.ansvia.digaku.model.PictureForum" =>
//                        //                            inV.toCC[PictureForum].map(PostStreamObject)
//                        //                        case "com.ansvia.digaku.model.SponsorPost" =>
//                        //                            inV.toCC[SponsorPost].map(PostStreamObject)
//                        //                        case "com.ansvia.digaku.model.RetalkWrapper" =>
//                        //                            inV.toCC[RetalkWrapper].map(PostStreamObject)
//                        case x =>
//                            throw NotSupportedException("class name " + x + " not supported yet for stream object, " +
//                                "please add it first manually in Foru.getStreamKindObj pattern matcher")
//                    }
//                }
//            }
//        }
//    }


    private object ownerLock extends Object

    /**
     * set for forum owner
     * @param user user
     */
    def setOwner(user: User, noTx: Boolean = false) {
        ownerLock.synchronized {

            // periksa apakah owner sudah ada?
            if (this.reload().getVertex.pipe.in("own").iterator().hasNext)
                throw AlreadyExistsException("Foru %s already has owner".format(this))

            val uv = user.reload().getVertex
            val chv = this.reload().getVertex

            uv --> OWN --> chv
            uv --> STAFF_AT --> chv

            user.moderatedForums.insert(getId, getName)

            //            chv.setProperty("staffCount", chv.getOrElse("staffCount", 0) + 1)
            this.incrementStaffCount()
            this.incrementMemberCount()
            //            uv.setProperty("joinCount", uv.getOrElse("joinCount", 0) + 1)
            user.incrementJoinChannelCount()
            user.incrementOwnedChannelCount()

            //            uv.setProperty("ownChannelCount", uv.getOrElse("ownChannelCount", 0) + 1)

            // join-kan kalo belum join
            if (!isMember(user, cached = false)) {
                val joinEd = uv --> JOIN --> chv <()
                joinEd.setProperty("sourceId", uv.getId)
                joinEd.setProperty("targetId", this.getId)
                joinEd.setProperty("userGender", user.sex)
                joinEd.setProperty("targetName", lowerName)

                user.joinedForums.insert(getId, getName)

                Forum.markMembers(this, Seq(user), state = true)
            }

            // tetep di-save di vertex-nya buat keperluan OLAP
            chv.setProperty("memberCount", getMemberCount())


            if (!noTx)
                db.commit()

//            val cacheKey = user.getId + "-" + this.getId
        }
    }

    /**
     * digunakan untuk merubah owner pada suatu forum
     * @param user
     */
    def changeOwnerTo(user: User) {

        validOwnerTransfer(user)

        if (this.isDeleted)
            throw PermissionDeniedException("#%s is deleted".format(this.getName))

        val prevOwner = owner
        val uv = user.reload().getVertex
        val chv = this.reload().getVertex

        // hapus previous owner
        chv.pipe.inE(OWN).remove()

        uv --> OWN --> chv

        if (!isStaff(user)) {
            uv --> STAFF_AT --> chv
            getVertex.setProperty("staffCount", getVertex.getOrElse("staffCount", 0) + 1)
            this.incrementStaffCount()
        }

        if (!isMember(user, cached = false)) {
            val joinEd = uv --> JOIN --> chv <()
            joinEd.setProperty("sourceId", uv.getId)
            joinEd.setProperty("targetId", this.getId)
            joinEd.setProperty("userGender", user.sex)
            joinEd.setProperty("targetName", lowerName)

            val v = getVertex

            this.incrementMemberCount()
            v.setProperty("memberCount", this.getMemberCount())

            user.incrementJoinChannelCount()
        }

        val prevOwnerV = prevOwner.reload().getVertex
        prevOwnerV.setProperty("ownChannelCount", prevOwnerV.getOrElse("ownChannelCount", 0) - 1)
        uv.setProperty("ownChannelCount", uv.getOrElse("ownChannelCount", 0) + 1)

        prevOwner.decrementOwnedChannelCount()
        user.incrementOwnedChannelCount()
        //        Foru.isMemberCache.invalidate(user.getId + "-" + this.getId)
        //        Forum.isStaffCache.invalidate(user.getId + "-" + this.getId)
        //        Foru.isOwnerCache.invalidate(prevOwner.getId + "-" + this.getId)
        //        Foru.isOwnerCache.invalidate(user.getId + "-" + this.getId)

        db.commit()
    }

    /**
     * Hapus owner dari forum ini,
     * hanya menghapus edge aja bukan menghapus user-nya.
     * @param noTx set true apabila tidak ingin dijalankan dalam mode transaction.
     */
    def removeOwner(noTx: Boolean = false) {
        val chv = this.reload().getVertex

        if (chv.pipe.in("own").range(0, 0).iterator().isEmpty)
            throw NotExistsException("Forum %s has no owner".format(this))

        chv.pipe.inE(OWN).remove()

        if (!noTx)
            db.commit()
    }


    private def validOwnerTransfer(user: User) {
        if (this.isOwner(user))
            throw AlreadyExistsException("%s is already owner at %s forum".format(user.name, this.getName))

        if (!user.isActivated)
            throw PermissionDeniedException("%s email has not activated".format(user.name))

        if (user.isInactive)
            throw PermissionDeniedException("%s is inactive".format(user.name))

        if (this.isBlockedUser(user))
            throw PermissionDeniedException("%s is blocked from current forum".format(user.name))

        //        if (user.channelQuota == user.getOwnedChannelCount) {
        //            throw PermissionDeniedException("Sorry, unable to transfer forum because %s forum quota is already full"
        //                .format(if (user.sex == SexType.FEMALE) "her" else "his"))
        //        }
    }

    /**
     * Get forum owner.
     * @return
     */
    def getOwner: Option[User] = {
        ownerLock.synchronized {
            assert(getVertex != null, "novertex, unsaved model %s?".format(this))
            getVertex.pipe.in(OWN).headOption.flatMap(_.toCC[User])
        }
    }


    /**
     * Check apakah user merupakan owner dari forum ini.
     * @param user yang akan dicheck.
     * @return
     */
    def isOwner(user: User): Boolean = {
        ownerLock.synchronized {
            //            owner == user
            Forum.isOwner(user, this)
        }
    }



    def hasInvitedOwner: Boolean = {
        getVertex.pipe.outE(INVITE)
            .has("kind", InvitationKind.BECOME_OWNER)
            .iterator().hasNext
    }

    /**
     * Tambah post mark.
     * @param title post mark title.
     */
    def addPostMark(title: String): PostMark = {


        // post mark max 10 / forum
        val count = reload().getVertex.pipe.out(POST_MARK).count()
        if (count > 10)
            throw new DigakuException("Mark max 10 items, currently " + count)

        if (!hasPostMark(title)) {
            //            transact {
            val postMark = PostMark.getByPostMarkTitle(title).getOrElse {
                throw NotExistsException("Mark not exist with name: " + title)
            }
            val postMarkV = db.getVertex(postMark.getId)

            db.getVertex(this.getVertex.getId) --> POST_MARK --> db.getVertex(postMarkV.getId)

            val rv = postMarkV.toCC[PostMark].getOrElse {
                throw new DigakuException("cannot save post mark")
            }
            //            }
            db.commit()
            rv
        } else
            null
    }

    /**
     * Check apakah ada post mark dengan title.
     * @param title title post mark.
     * @return
     */
    def hasPostMark(title: String): Boolean =
        reload().getVertex.pipe.out(POST_MARK).has("title", title).iterator().hasNext

    /**
     * get post mark by title.
     * @param title
     * @return
     */
    def getPostMark(title: String): Option[PostMark] = {
        reload().getVertex.pipe.out(POST_MARK).has("title", title).headOption
            .map { v => v.toCC[PostMark] }.getOrElse(None)
    }

    /**
     * Get post mark list.
     * @param offset starting offset.
     * @param limit end limit.
     * @return
     */
    def getPostMarks(offset: Int, limit: Int): Seq[PostMark] = {
        reload().getVertex.pipe.out(POST_MARK).iterator().flatMap(_.toCC[PostMark]).toSeq
    }


    def hasPostMark = getVertex.pipe.out(POST_MARK).range(0, 1).iterator().hasNext

    /**
     * Hapus post mark by title.
     * @param title
     */
    def removePostMark(title: String) {
        assert(title != null)
        assert(title.length > 0)

        getVertex.pipe.outE(POST_MARK).as("ed").inV().has("title", title)
            .back("ed").remove()

        db.commit()

    }

    /**
     * Hapus post mark by object.
     * @param postMark to remove.
     */
    def removePostMark(postMark: PostMark) {
        assert(postMark != null, "postMark cannot be null")
        getVertex.pipe.outE(POST_MARK).as("ed").inV()
            .has("id", postMark.getId).back("ed").remove()
        db.commit()
    }

    /**
     * digunakan untuk block user pada suatu forum
     * @param user @see[[com.ansvia.digaku.model.User]]
     * @param blockedType @see[[com.ansvia.digaku.model.BlockedType]]
     */
    def blockUser(user: User, blockedType: String) {

        if (isOwner(user))
            throw PermissionDeniedException("can't block owner forum : %s".format(user.name))

        if (!isMember(user))
            throw PermissionDeniedException("user is not member at this forum : %s".format(user.name))

        if (isBlockedUser(user))
            throw PermissionDeniedException("user already blocked : %s".format(user.name))


        val edge = this.getVertex.addEdge(BLOCK, user.getVertex)
        edge.setProperty("blockedType", blockedType)

        db.commit()
    }

    /**
     * check apakah user di Full block oleh owner ?
     * @param user
     * @return
     */
    def isFullBlockedUser(user: User): Boolean = getBlockedType(user).exists(_ == BlockedType.FULL)

    /**
     * mengambil type blocked user pada sebuah forum
     * @param user
     * @return Option[String] (blocked type)
     */
    def getBlockedType(user: User): Option[String] = {
        this.getVertex.pipe.outE(BLOCK).as("edge")
            .inV().has("id", user.getId).back("edge")
            .asInstanceOf[GremPipeEdge]
            .headOption.flatMap { edge =>
            edge.get[String]("blockedType")
        }
    }

    /**
     * digunakan untuk unblock user
     * @param user
     */
    def unblockUser(user: User) {
        if (!isBlockedUser(user))
            throw InvalidParameterException("%s is not blocked".format(user.name))

        this.getVertex.pipe.outE(BLOCK).as("ed")
            .inV().has("id", user.getId)
            .back("ed").remove()
        db.commit()

    }

    /**
     * check apakah user diblock atau tidak
     * @param user
     * @return
     */
    def isBlockedUser(user: User): Boolean = {
        this.getRawVertex.pipe.out(BLOCK)
            .has("id", user.getRawId)
            .iterator().hasNext
    }

    /**
     * ambil semua user yang diblock dalam sebuah forum
     * @param offset
     * @param limit
     * @return
     */
    def getBlockedUser(offset: Int, limit: Int): Seq[User] = {
        this.getVertex.pipe.out(BLOCK)
            .range(offset, (offset + limit))
            .iterator()
            .flatMap(_.toCC[User])
            .toSeq
    }

    def getMutualSupportingsCount(user: User) = {
        this.getVertex.pipe.in(JOIN).as("u").outE(SUPPORT).has("targetId", user.getId)
            .back("u").count().toInt
    }


    /**
     * Digunakan untuk meng-ignore forum
     * agar tidak lagi muncul sebagai forum recommendation
     *
     * @param channelTarget
     * forum target yang akan di ignore
     */
    def ignoreChannelRecommendation(channelTarget: Forum) {
        this --> IGNORE_RECOMMENDATION --> channelTarget
        db.commit()
    }

    /**
     * Digunakan untuk meng-ignore similar forum
     * agar tidak lagi muncul sebagai similar forum
     *
     * @param channelTarget
     * forum target yang akan di ignore
     */
    def ignoreSimilarChannelRecommendation(channelTarget: Forum) {
        this --> IGNORE_RECOMMENDATION --> channelTarget
        db.commit()
    }

    /**
     * Get counter provider used by this model.
     * @return
     */
    def getCounter: CounterProviderOperator = counter


    override def toString = "Forum(%s, %s)".format(id, name)


    /**
     * Get article filtered by post mark.
     * @param postMarkTitle post mark title to filter.
     * @param offset starting offset.
     * @param limit ends limit.
     * @return
     */
    def filterByPostMark(postMarkTitle: String, offset: Int, limit: Int): Iterator[Article] = {

        getVertex.pipe.out(COLLECTION_VERTEX)
            .has("kind", CollectionVertex.Kind.STREAM)
            .outE(STREAM).has("post.mark", postMarkTitle.toLowerCase)
            .range(offset, offset + limit - 1)
            .inV()
            .iterator().flatMap(_.toCC[Article])

    }

    //    /**
    //     * Untuk memeriksa apakah forum ini memiliki gallery
    //     * atau tidak.
    //     * @return
    //     */
    //    def hasGallery(name:String) = {
    //        getVertex.pipe.out(GALLERY)
    //            .has("gallery.lower-name", this.getId + "-" + name.toLowerCase).headOption.isDefined
    //    }
    //
    //    /**
    //     * Create gallery if doesn't have
    //     * @see [[com.ansvia.digaku.model.ChannelGallery]]
    //     *
    //     * @param name gallery name.
    //     * @return
    //     */
    //    def createGallery(name:String) = {
    //        if (hasGallery(name))
    //            throw AlreadyExistsException("Cannot generate, this forum has gallery named " + name)
    //
    //        ChannelGallery.create(name, this)
    //    }
    //
    //    def getGallery(name:String) = {
    //        getVertex.pipe.out(GALLERY).has("gallery.lower-name", this.getId + "-" + name.toLowerCase)
    //            .asInstanceOf[GremPipeVertex]
    //            .headOption.flatMap(_.toCC[ChannelGallery])
    //    }
    //
    //    private def getPhotoGallery = {
    //        if (!hasGallery("photo")){
    //            createGallery("photo")
    //        }else{
    //            getGallery("photo").getOrElse {
    //                throw NotExistsException("No gallery with name photo")
    //            }
    //        }
    //    }
    //
    //    /**
    //     * Create new album for this forum.
    //     * @param name album name.
    //     * @param description album description.
    //     * @param tags album tags.
    //     * @return
    //     */
    //    def createAlbum(name:String, description:String, tags:String) = {
    //
    //        if (name.trim.length < 3 || name.trim.length > 25) {
    //            throw InvalidParameterException("Name min 3 and max 25 character")
    //        }
    //
    //        if (description.trim.length > 160) {
    //            throw InvalidParameterException("Description max 160 character")
    //        }
    //
    //        if (getVertex.pipe.out(GALLERY).out(ALBUM)
    //            .has(ALBUM_LOWER_NAME_KEY, name.toLowerCase)
    //            .count() > 0)
    //            throw AlreadyExistsException("Album with that name already exists")
    //
    //        Album.create(name, description, tags, getPhotoGallery)
    //    }
    //
    //
    //    /**
    //     * Get list of album in this forum.
    //     * @param offset starting offset.
    //     * @param limit ends limit.
    //     * @return
    //     */
    //    def getAlbums(offset:Int, limit:Int) = {
    //        getVertex.pipe.out(GALLERY).outE(ALBUM)
    //            .range(offset, offset + limit - 1)
    //            .inV().iterator().flatMap(_.toCC[Album])
    //    }
    //
    //    /**
    //     * Untuk mendapatkan album general
    //     * Akan otomatis meng-create kalo belum ada.
    //     *
    //     * @return album general.
    //     */
    //    def getGeneralAlbum:Album = {
    //        val generalAlbum = getVertex.pipe.out(GALLERY).out(ALBUM)
    //            .has(ALBUM_LOWER_NAME_KEY, "general").asInstanceOf[GremPipeVertex]
    //            .headOption.flatMap(_.toCC[Album])
    //
    //        if (!generalAlbum.isDefined){
    //            Album.create("GeneralCOMMAAlbum for general categoryCOMMAgeneral", getPhotoGallery)
    //        }else{
    //            generalAlbum.get
    //        }
    //    }
    //
    //    /**
    //     * Get album by ID.
    //     * @param id
    //     * @return
    //     */
    //    def getAlbum(id:IDType) = {
    //        getVertex.pipe.out(GALLERY).out(ALBUM)
    //            .has("id", id).asInstanceOf[GremPipeVertex]
    //            .headOption.flatMap(_.toCC[Album])
    //    }
    //
    //
    //    /**
    //     * Mendapatkan jumlah album pada forum ini.
    //     * @return
    //     */
    //    def getAlbumCount = {
    //        getVertex.pipe.out(GALLERY).outE(ALBUM).count()
    //    }
    //
    //    /**
    //     * Untuk me-remove sebuah album.
    //     * @param album album yang akan di delete
    //     * @param deletedType see [[com.ansvia.digaku.model.DeletedType]]
    //     * @param deletedRole see [[com.ansvia.digaku.model.DeletedRole]]
    //     * @param userDeleting user yang men-delete
    //     * @param deletedReason alasan mengapa di-delete
    //     * @param collected set false ketika di override dan ingin menambahkan property ke dalam collect
    //     */
    //    def removeAlbum(album:Album, deletedType:Int, deletedRole:String, userDeleting:User, deletedReason:String = "", collected:Boolean=false) = {
    //
    //        //ambil seluruh pic yang akan didelete
    //        val objToRemoveCount = album.getMediaCount
    //        val stream = album.getStream(0L, 0, objToRemoveCount.toInt)
    //
    //        //delete semua pic
    //        stream.foreach { pic =>
    //            pic.reload()
    //            pic.setDeleted(deletedType, deletedRole, userDeleting, deletedReason, collected)
    //            album.remove(pic)
    //        }
    //
    //        Album.remove(album.getId, getPhotoGallery)
    //
    //    }


//    /**
//     * Mendapatkan active users dari 100 publish activity terakhir
//     * di dalam forum.
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def getActiveUsers(offset: Int, limit: Int, cached: Boolean = true) = {
//
//        timing(s"Forum($name).getActiveUsers") {
//            Forum.getActiveUsers(this, offset, limit, cached)
//        }
//
//    }

    def getActiveUsersCount: Int = {
        timing(s"Foru($name).getActiveUsers") {
            getVertex.pipe
                .out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.STREAM) //.has("year", Digaku.engine.dateUtils.getCurrentYear) // user
                .range(0,1)
                .out(STREAM).range(0, 100)
                .in(PUBLISH_CONTENT).in(COLLECTION_VERTEX)
                .dedup.count().toInt
        }
    }

    /**
     * Digunakan untuk mendapatkan promoted jika dipromote
     * @return
     */
    def getPromoted: Option[Promoted] =
        this.getVertex.pipe.out(HAS_PROMOTED).headOption.flatMap(_.toCC[Promoted])


    /**
     * Tambahkan sub-forum ke forum ini.
     * @param forum forum yang akan dijadikan sub-forum.
     */
    def addSubForum(forum: Forum) {

        if (forum.getId == this.getId)
            throw InvalidParameterException("cannot set parent to itself")


        if (this.getSubForumCount >= 100)
            throw LimitationReachedException("max 100 sub forum allowed")

        tx { t =>
            forum.reload()(t)
            this.reload()(t)

            forum.parentForumId = this.getId
            forum.getVertex.setProperty("parentForumId", this.getId)

            val edge = this.getVertex.addEdge(Label.SUB_FORUM, forum.getVertex)

            edge.setProperty("lowerName", forum.lowerName)
        }


        db.commit()

        forum.reload()
        this.reload()

    }


    /**
     * Mendapatkan parent forum (forum atasnya) yang menjadi
     * orang tua dari sub-forum ini.
     * @return Option
     */
    def getParentForum = {
        val v = db.getVertex(this.parentForumId)
        if (v != null) {
            v.toCC[Forum]
        } else {
            None
        }
    }

    /**
     * Mendapatkan jumlah keseluruhan sub-forum di dalam forum ini.
     * @return
     */
    def getSubForumCount = {
        this.getVertex.pipe.outE(SUB_FORUM).inV().hasNot("deleted", true).count().toInt
    }


    /**
     * Mendapatkan daftar sub-forum pada forum ini.
     * @return
     */
    def getSubForums = {
        this.getVertex.pipe.out(SUB_FORUM).iterator().flatMap(_.toCC[Forum])
    }

    /**
     * Untuk mendapatkan daftar subforum pada sebuah forum, berdasarkan role dari user terkait
     * Rule nya adalah:
     * 1. Semua user bisa melihat forum (tidak ada closed subforum, semuanya open)
     * 2. Super admin bisa melihat semua subforum
     * 3. Admin bisa melihat semua subforum
     * 4. Moderator forum bisa melihat semua subforum di bawah forum yg di moderasinya
     * 5. Moderator subforum bisa melihat semua subforum yg di moderasi
     * 6. Member bisa melihat semua forum yg dijoin
     * 7. User hanya bisa melihat open subforum
     *
     * @param user
     * @return
     */
    def getSubForumForUser(user:User, asAdmin:Boolean = true) = {

        getSubForums.filter(!_.deleted).filter { forum =>
            val rule2 = asAdmin && (user.role == UserRole.SUPER_ADMIN)
            val rule3 = asAdmin &&(user.role == UserRole.ADMIN)
            val rule4 = asAdmin && isStaff(user)
            val rule5 = asAdmin && forum.isStaff(user)
            val rule6 = forum.isMember(user)
            val rule7 = forum.getPrivated

            (rule7 && (rule2 || rule3 || rule4 || rule5 || rule6)) || !rule7
        }
    }

    def getForumArrangementForUser(user:User, asAdmin:Boolean = true):List[Forum] = {
        Forum.getForumArrangement(Forum.subforumListStore(this))
            .filter(!_.deleted).filter { forum =>
            val rule2 = asAdmin && (user.role == UserRole.SUPER_ADMIN)
            val rule3 = asAdmin &&(user.role == UserRole.ADMIN)
            val rule4 = asAdmin && isStaff(user)
            val rule5 = asAdmin && forum.isStaff(user)
            val rule6 = forum.isMember(user)
            val rule7 = forum.getPrivated

            (rule7 && (rule2 || rule3 || rule4 || rule5 || rule6)) || !rule7
        }
    }

    def contentQuery() = {
        new ArticleQueryBuilder()
            .setOrigin(this.getId)
    }
}




/**
 * semua implementasi static Foru ini
 * ada di [[com.ansvia.digaku.dao.ForumDao]]
 * ini untuk mempermudah akses Dao menggunakan nama modelnya.
 */
object Forum extends ForumDao

/**
 * Forum visibility attributes.
 */
object ForumVisibility {
    val NONE = 0
    val ON_SEARCH = 2
    val ON_CHANNEL_LIST = 4
    val ON_POPULAR = 8


    val ALL = ON_SEARCH | ON_CHANNEL_LIST | ON_POPULAR
}
