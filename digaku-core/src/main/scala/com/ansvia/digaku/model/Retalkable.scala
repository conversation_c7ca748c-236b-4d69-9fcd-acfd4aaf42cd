///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.event.impl.RetalkEvent
//import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException, PermissionDeniedException}
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.model.Ability._
//import com.ansvia.digaku.model.Label._
//import com.ansvia.digaku.stream.StreamBuilder
//import com.ansvia.graph.BlueprintsWrapper.IdDbObject
//
//import scala.collection.JavaConversions._
//
///**
// * Author: ubai
// * Date: 6/19/14
// * Time: 11:36 AM
// *
// */
//
///**
// * Semua model yang mixin dari trait ini akan bisa di-retalk.
// */
//trait Retalkable  extends IdDbObject[IDType] with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    private lazy val counter = Digaku.engine.counterProvider("retalkable-attrs-" + getId)
//
//    /**
//     * digunakan untuk create retalk
//     * @param chTarget group target untuk retalk
//     * @param retalker user yang me-retalk
//     * @return
//     */
//    def retalk(chTarget:Group, retalker:User) = {
//        if (!chTarget.isOwner(retalker) &&
//            !chTarget.isStaff(retalker) &&
//            !chTarget.hasAbility(retalker, RETALK_POST) &&
//            retalker.role != UserRole.ADMIN &&
//            retalker.role != UserRole.SUPER_ADMIN
//        )
//            throw PermissionDeniedException("Permission Denied")
//
//        this match {
//            case post:Picture =>
//                if(!chTarget.hasFeature(GroupFeatures.PHOTO))
//                    throw PermissionDeniedException("Invalid Group Target not supported Photo feature")
//            case post:Event =>
//                if(!chTarget.hasFeature(GroupFeatures.EVENT))
//                    throw PermissionDeniedException("Invalid Group Target not supported Event feature")
//            case _ =>
//        }
//
//        this match {
//            case hasOrigin:HasOrigin[GraphType] =>
//                if (chTarget == hasOrigin.origin)
//                    throw InvalidParameterException("Invalid Group Target")
//
//                hasOrigin.origin match {
//                    case ch:Group =>
//                        if (!ch.isMember(retalker)) {
//                            throw PermissionDeniedException(s"User ${retalker.name} is not member on group ${ch.name}")
//                        }
//
//                    case _ =>
//                }
//            case _ =>
//
//        }
//
//        this match {
//            case p:Deletable =>
//                if (p.isDeleted)
//                    throw PermissionDeniedException("Cannot retalk, this Post has been deleted.")
//            case _ =>
//        }
//
//        var retalked:RetalkWrapper = RetalkWrapper(getId)
//
//        retalked.originId = chTarget.getId
//
//        retalked = retalked.save().toCC[RetalkWrapper].getOrElse {
//            throw new DigakuException("Cannot create RetalkWrapper")
//        }
//
//        val retalkRefEd = retalked --> RETALK_REF --> this <()
//        retalkRefEd.setProperty("timeOrder", System.currentTimeMillis())
//
//        val ed = retalker --> RETALK --> retalked <()
//        ed.setProperty("timeOrder", System.currentTimeMillis())
//
//        val streamE = StreamBuilder.addToStream(chTarget.asInstanceOf[Origin[GraphType] with CollectionVertex], retalked)
//        streamE.setProperty("post.sticky", false)
//        streamE.setProperty("locked", false)
//        streamE.setProperty("post.kind", PostKind.RETALKED)
//        streamE.setProperty("originId", chTarget.getId)
//        streamE.setProperty("targetId", getId)
//
//        db.commit()
//
//        counter.increment("retalk_count")
//
//        Digaku.engine.eventStream.emit(RetalkEvent(retalker, retalked, chTarget))
//
//        retalked
//    }
//
//    /**
//     * mendapatkan jumlah sudah berapakali post ini diretalk
//     * @return int
//     */
//    def getRetalkCount = {
//        counter.get("retalk_count")
//    }
//
//    /**
//     * digunakan untuk mendapatkan object RetalkWrapper dari sebuah post
//     * @param skip
//     * @param limit
//     * @return
//     */
//    def getRetalkWrapper(skip:Int, limit:Int):Iterator[RetalkWrapper] = {
//        this.getVertex.pipe.inE(RETALK_REF)
//            .range(skip, skip + (limit - 1))
//            .inV()
//            .iterator()
//            .flatMap(_.toCC[RetalkWrapper])
//    }
//
//    /**
//     * cek apakah sebuah content di retalk atau tidak
//     * @return
//     */
//    def isRetalked:Boolean = {
//        this.getVertex.pipe.inE(RETALK_REF).hasNext
//    }
//
//    /**
//     * delete semua retalkan dari this object (retalkable).
//     */
//    def deleteRetalkWrapers() {
//        this.getVertex.pipe.in(RETALK_REF).remove()
//        db.commit()
//    }
//}
//
//object Retalkable extends DbAccess {
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    def getById(id:IDType):Option[Retalkable] = {
//        val v = db.getVertex(id)
//        if (v != null){
//            v.toCC[Retalkable]
//        }else{
//            None
//        }
//    }
//}
