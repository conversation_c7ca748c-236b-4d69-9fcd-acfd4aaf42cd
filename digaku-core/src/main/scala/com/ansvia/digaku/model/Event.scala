/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.util.Date

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.EventDao
import com.ansvia.digaku.event.impl.AttenderEvent
import com.ansvia.digaku.exc.PermissionDeniedException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.persistence.CounterProviderOperator
import com.ansvia.digaku.utils.RichDate._
import com.ansvia.graph.annotation.Persistent
import com.ansvia.graph.gremlin._
import com.tinkerpop.blueprints.{Edge, Vertex}
import com.tinkerpop.gremlin.java.GremlinPipeline
import org.ocpsoft.prettytime.PrettyTime
import com.ansvia.digaku.database.GraphCompat._
import scala.collection.JavaConversions._
import com.ansvia.digaku.helpers.GremlinHelpers._

/**
 * Author: nadir
 *
 */

/**
 * Base class untuk model Event
 * @param title event title.
 * @param content event description.
 * @param location event location.
 * @param startDate start date.
 * @param finishDate finish date.
 */
abstract class EventBase(title:String, content:String, location:String, startDate:Long, finishDate:Long)
//    extends PublishableContent with Likable with Responable with Stickable with ContentVersioning with Counter {
    extends BaseModel[IDType] with Embeddable[IDType]
    with HasOrigin[GraphType] with Deletable with Closable with DbAccess with Counter {

    import Label._
    import com.ansvia.graph.BlueprintsWrapper._

    private lazy val counter = Digaku.engine.counterProvider("event-counter-" + getId)

//    @Persistent var creationTime = 0L
    @Persistent var lastUpdate = 0L

    /**
     * @see [[com.ansvia.digaku.model.PostKind]]
     */
    @Persistent val kind:Int = PostKind.EVENT

    @Persistent var sticky = false

    @Persistent var editReason = ""
    @Persistent var lastEditTime = 0L
    @Persistent var lastEditorUserId = 0L

    /**
     * Get last edited user.
     */
    lazy val lastEditor:Option[User] = {
        User.getById(lastEditorUserId)
    }

    lazy val kindStr = PostKind.kindIntToStr(kind)

    /**
     * Get last edit time in relative age format.
     * eg: 2 hours ago.
     * @return
     */
    def getLastEditTimeAge:String = {
        new PrettyTime(new Date()).format(new Date(lastEditTime))
    }

    /**
     * Get last edit time in standard format yyyy/MM/dd HH:mm:ss
     * @return
     */
    def getLastEditTimeStd:String = {
        new Date(lastEditTime).toStdFormat
    }


    lazy val origin:Origin[GraphType] = {
        reload()
//        val v = this.getVertex.pipe.outFirst(ORIGIN)
        val vO = this.getVertex.pipe.out(ORIGIN).headOption
        if (vO.isDefined){
            try {
                val rv = vO.get.toCC[Forum].getOrElse {
                    vO.get.toCC[User].getOrElse(null)
                }
                if (rv!=null)
                    rv.asInstanceOf[Origin[GraphType]]
                else
                    null
            }catch{
                case e:NullPointerException =>
                    error("no creator for event %s".format(getId))
                    null
            }
        }else{
            error("no origin for event %s".format(this))
            null
        }
    }

    /**
     * Get last event updater.
     */
    lazy val lastUpdater:Option[User] = {
//        val v = this.getVertex.pipe.inFirst(UPDATE)
        val vO = this.getVertex.pipe.in(UPDATE).headOption
        if (vO.isEmpty){
            error("no creator for event %s".format(this))
            None
        }else{
            vO.get.toCC[User]
        }
    }

    override def __load__(vertex: Vertex) {
        super.__load__(vertex)
    }

    /**
     * Add attenders to this event.
     * @param kind see [[com.ansvia.digaku.model.AttenderKind]]
     * @param users users to attend
     */
    def addAttenders(kind:Int, users:User*){

        for (u <- users){
            var edge = u.reload().getVertex.pipe.outE(ATTEND).as("edge")
                .inV().has("id", this.getId).back("edge")
                .asInstanceOf[GremlinPipeline[Vertex, Edge]].headOption.getOrElse(null)

            if (edge == null){
                edge = u.reload() --> ATTEND --> this.reload() <()
            }

            edge.set("kind", kind)

            Digaku.engine.eventStream.emit(AttenderEvent(this.asInstanceOf[Event], u, kind))

            EventReminder.create(this.asInstanceOf[Event], u, noTx=true)

        }

        db.commit()

    }

    /**
     * remove attenders.
     * @param kind see [[com.ansvia.digaku.model.AttenderKind]]
     * @param users users to remove.
     */
    def removeAttenders(kind:Int, users:User*){
        for (u <- users){
            tx { t =>
                gremlin(t.getVertex(this.getId)).inE(ATTEND).as("ed").outV()
                    .has("id", u.getId).back("ed").remove()
            }
        }
        db.commit()
    }

    /**
     * get event attenders.
     * @param kind see [[com.ansvia.digaku.model.AttenderKind]]
     * @param offset
     * @param limit
     * @return
     */
    def getAttenders(kind:Int, offset:Int = 0, limit:Int = 10):Seq[User] = {
        if (kind == AttenderKind.ALL) {
            getVertex.pipe.inE(ATTEND)
                .outV().range(offset, (offset + limit) - 1)
                .iterator().flatMap(_.toCC[User]).toSeq
        } else {
            getVertex.pipe.inE(ATTEND)
                .has("kind", kind).outV()
                .range(offset, (offset + limit) - 1)
                .iterator().flatMap(_.toCC[User]).toSeq
        }
    }

    /**
     * Periksa apakah user adalah attender dari event ini
     * dengan tipe tertentu (kind).
     * Contoh penggunaan:
     *
     *      isAttenders(MAYBE, u1, u2, ...)
     *
     * @param kind tipe attendernya. lihat [[com.ansvia.digaku.model.AttenderKind]]
     * @param users user yang mau di check.
     * @return
     */
    def isAttenders(kind:Int, users:User*) = {
        if (kind==AttenderKind.ALL)
            getVertex.pipe.inE(ATTEND).outV()
                .filter((v:Vertex) => users.map(_.getId).contains(v.getId)).length > 0
        else
            getVertex.pipe.inE(ATTEND).has("kind", kind).outV()
                .filter((v:Vertex) => users.map(_.getId).contains(v.getId)).length > 0
    }

    /**
     * Mengembalikan jumlah attenders
     * @param kind see [[com.ansvia.digaku.model.AttenderKind]]
     * @return
     */
    def getAttendersCount(kind:Int=AttenderKind.POSITIVE) = {
        tx { t =>

            val v = t.getVertex(this.getId)

            if (kind == AttenderKind.ALL) {
                v.pipe.inE(ATTEND).count()
            } else {
                v.pipe.inE(ATTEND).has("kind", kind).count()
            }
        }
    }

    /**
     * Mengembalikan umur event
     * @return
     */
    def getCreationAge:String = {
        new PrettyTime(new Date()).format(new Date(creationTime))
    }

    def getLastUpdateAge:String = {
        new PrettyTime(new Date()).format(new Date(lastUpdate))
    }

    def getLastUpdateStr:String = {
        new Date(lastUpdate).toStdFormatWithTime
    }

    /**
     * Mengembalikan event reminder
     * @param offset
     * @param limit
     * @return
     */
    def getReminders(offset:Int, limit:Int):Iterator[EventReminder] = {
        getVertex.pipe.out(EVENT_REMINDER).range(offset, offset + limit).iterator().flatMap(_.toCC[EventReminder])
    }


    /**
     * Get counter provider used by this model.
     * @return
     */
    def getCounter: CounterProviderOperator = counter

    /**
     * Search event attenders
     * @param kind attenders kind @see [[com.ansvia.digaku.model.AttenderKind]]
     * @param query attenders query
     * @param offset
     * @param limit
     * @return
     */
    def searchAttenders(kind:Int, query:String, offset:Int = 0, limit:Int = 10):Seq[User] = {
        if (kind == AttenderKind.ALL) {
            getVertex.pipe.inE(ATTEND).as("ed").outV().filter { (v:Vertex) =>
                v.getOrElse("fullName", "").toLowerCase.contains(query.toLowerCase)
            }.range(offset, (offset + limit) - 1).iterator().flatMap(_.toCC[User]).toSeq
        } else {
            getVertex.pipe.inE(ATTEND).has("kind", kind).as("ed").outV().filter { (v:Vertex) =>
                v.getOrElse("fullName", "").toLowerCase.contains(query.toLowerCase)
            }.range(offset, (offset + limit) - 1).iterator().flatMap(_.toCC[User]).toSeq
        }
    }

    override def __save__(v:Vertex) = {
        if (creationTime == 0L){
            v.setProperty("creationTime", System.currentTimeMillis())
        }else
            super.__save__(v)
    }

}

case class Event(var title:String, var content:String, var location:String, var startDate:Long, var finishDate:Long)
        extends EventBase(title, content, location, startDate:Long, finishDate:Long) {

    def getDueDate = new Date(finishDate)

    /**
     * Cek apakah event sedang berlangsung.
     * @return
     */
    def isOnGoing = {
        val nowMillis = Digaku.engine.dateUtils.nowMilis
        nowMillis >= startDate && nowMillis <= finishDate
    }

    def expired = getDueDate.before(Digaku.engine.dateUtils.getCurrentTime().toDate)

    /**
     * Add attenders to this event.
     * @param kind see [[com.ansvia.digaku.model.AttenderKind]]
     * @param users users to attend
     */
    override def addAttenders(kind:Int, users:User*){
        if (isOnGoing) {
            throw PermissionDeniedException("Event sedang berlangsung.")
        }

        if (expired) {
            throw PermissionDeniedException("Event telah berakhir.")
        }

        super.addAttenders(kind, users:_*)
    }
}

object Event extends EventDao[GraphType]
