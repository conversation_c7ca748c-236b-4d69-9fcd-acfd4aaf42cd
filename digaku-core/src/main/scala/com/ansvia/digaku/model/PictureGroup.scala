///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.event.EventStream
//import com.ansvia.digaku.event.impl.{CreatePictureGroupEvent, DeletePictureGroupEvent, RestoreContentEvent}
//import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException, LimitationReachedException}
//import com.ansvia.digaku.model.Label._
//import com.ansvia.digaku.stream.StreamBuilder
//import com.ansvia.digaku.utils.DateUtils
//import com.ansvia.graph.BlueprintsWrapper._
//import com.tinkerpop.blueprints.Vertex
//
//import scala.collection.JavaConversions._
//
///**
// * Author: robin
// *
// */
//
///**
// * Model streamable untuk picture group post.
// * @param title judul dari picture group ini.
// */
//case class PictureGroup(title:String) extends PictureBase(title)
//    with HasEmbeddedObject[IDType] with Responable with Likable with Counter {
//
////    private lazy val counter = Digaku.engine.counterProvider("picturegroup-counter-" + getId)
//
////    @Persistent var creationTime = 0L
////
////    lazy val origin:Origin[ GraphType] = {
////        val meV = db.getVertex(this.getId)
////        val v = meV.pipe.outFirst(ORIGIN)
////        if (v.isDefined){
////            try {
////                val rv = v.get.toCC[Group].getOrElse {
////                    v.get.toCC[User].getOrElse(null)
////                }
////                if (rv!=null)
////                    rv.asInstanceOf[Origin[ GraphType]]
////                else
////                    null
////            }catch{
////                case e:NullPointerException =>
////                    error("no origin for post %s".format(getId))
////                    null
////            }
////        }else{
////            error("no origin for post %s".format(this))
////            null
////        }
////    }
//
//    override def toString = {
//        if (title.length > 0) {
//            val cont = title.replaceAll("\\W+", " ")
//            val nc = if(cont.length > 50)
//                cont.substring(0, 49) + "..."
//            else
//                cont
//            "PictureGroup(" + id + ", " + nc + ")"
//        } else {
//            "PictureGroup(" + id + ", empty-title)"
//        }
//    }
//
//    lazy val pictures = getPictures.map {
//        case pic:Picture =>
//            pic
//    }
//
//    /**
//     * Untuk dapetin pic pertama dalam daftar koleksi pic
//     */
//    lazy val headPic = {
//        reload()
//        val it = getVertex.pipe.out(EMBED).iterator()
//        if (it.hasNext)
//            it.next().toCC[Picture]
//        else
//            None
//    }
//
//    /**
//     * override set deleted ini supaya ketika picture group di hapus
//     * picture yang belum didelete yang di embed oleh picture group ini juga akan ikut di delete.
//     * @param deletedType see [[com.ansvia.digaku.model.DeletedType]]
//     * @param deletedRole see [[com.ansvia.digaku.model.DeletedRole]]
//     * @param userDeleting user yang delete
//     * @param deletedReason deleted reason
//     * @param collected set false ketika di override dan ingin menambahkan property ke dalam collect
//     */
//    override def setDeleted(deletedType:Int ,deletedRole:String, userDeleting:User, deletedReason:String = "", collected:Boolean=false) {
//        setDeletedAdvance(deletedType, deletedRole, userDeleting, deletedReason, collected, deleteAllPictures = true)
//    }
//
//    /**
//     * menghapus picture group dengan lebih customize, apabila ingin menghapus picture group nya saja,
//     * atau termasuk picture-picture yang di embed.
//     * @param deletedType see [[com.ansvia.digaku.model.DeletedType]]
//     * @param deletedRole see [[com.ansvia.digaku.model.DeletedRole]]
//     * @param userDeleting user yang delete
//     * @param deletedReason deleted reason
//     * @param collected set false ketika di override dan ingin menambahkan property ke dalam collect
//     * @param deleteAllPictures set false jika tidak ingin menghapus picture-picture yang di embed ke dalamnya
//     */
//    def setDeletedAdvance(deletedType:Int ,deletedRole:String, userDeleting:User, deletedReason:String = "", collected:Boolean=false, deleteAllPictures:Boolean=true) {
//        super.setDeleted(deletedType, deletedRole, userDeleting, deletedReason, collected)
//
//        Digaku.engine.eventStream.emit(DeletePictureGroupEvent(this.creator , this))
//
//        if (deleteAllPictures) {
//            this.getPictures.foreach { pic =>
//                pic.setDeleted(deletedType, deletedRole, userDeleting, deletedReason, collected)
//            }
//        }
//
//
//            val edge = Deletable.collect(this, noTx = true)
//            edge.setProperty("deletedObject", "PictureGroup")
//            edge.setProperty("deletedKind", "Streamable")
//            edge.setProperty("originId", this.origin.getId)
//
//            getVertex.pipe.out(EMBED)
//                .has("deleted", true)
//                .inE(Deletable.rootVertexLabel)
//                .remove()
//
//        db.commit()
//
//    }
//
//    /**
//     * override ini supaya ketika restore deleted picture group
//     * picture yang di embed juga ikut ke restore.
//     */
//    override def restoreDeleted() {
//        super.restoreDeleted()
//
//        getVertex.pipe.out(EMBED).iterator().flatMap(_.toCC[Picture])
//            .foreach { pic =>
//                pic.restoreDeleted()
//            }
//
//        Digaku.engine.eventStream.emit(RestoreContentEvent(this.creator , this))
//
//    }
//
//    /**
//     * Tambahkan pictures ke group.
//     * @param pics pictures.
//     */
//    def addPictures(pics:Picture*) = {
//        // batasi hanya max 5 pics
//        if (pictures.length + pics.length > 5){
//            throw LimitationReachedException("Max 5 pictures per group")
//        }
//        addEmbeddedObjects(pics:_*)
//    }
//
//
//    /**
//     * Menambahkan Embedded object untuk object tersebut
//     * @param obj
//     * @param noTx
//     */
//    override def addEmbeddedObject(obj: Embeddable[IDType], noTx: Boolean) = {
//        obj match {
//            case pic:Picture =>
//                super.addEmbeddedObject(obj, noTx)
//            case x =>
//                throw InvalidParameterException("Only accept picture for embedded object.")
//        }
//    }
//
//
//
//    /**
//     * Add embedded objects secara massal.
//     * non transaction.
//     * Untuk penggunaan transaction gunakan yang satu-persatu dengan
//     * parameter noTx = false, @see [[com.ansvia.digaku.model.HasEmbeddedObject.addEmbeddedObject]]
//     * @param objs objects to add.
//     */
//    override def addEmbeddedObjects(objs:Embeddable[IDType]*){
//        objs.filter(x => !x.isInstanceOf[Picture]).foreach { obj =>
//            throw InvalidParameterException("%s is not instance of Picture".format(obj))
//        }
//        super.addEmbeddedObjects(objs: _*)
//    }
//
//    /**
//     * Mendapatkan picture yang diembed pada picture group
//     * Deleted picture di filter
//     *
//     * @return
//     */
//    def getPictures = {
//        getEmbeddedObjects.map(_.asInstanceOf[Picture]).filter(!_.deleted)
//    }
//
//    override def __save__(vertex: Vertex){
//        super.__save__(vertex)
//        if (creationTime > 0){
//            vertex.setProperty("likesCount", getLikesCount)
//        }
//    }
//}
//
///**
// * DAO untuk PictureGroup.
// */
//object PictureGroup extends DaoBase[GraphType, PictureGroup]{
//
//    import com.ansvia.digaku.model.Label._
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.PictureGroupRootVertex"
//
//
//    /**
//     * Buat PictureGroup baru.
//     * @param title judul dari picture group.
//     * @param creator user creator-nya.
//     * @param origin origin-nya.
//     * @param noEmitEvent apakah event [[com.ansvia.digaku.event.impl.CreatePictureGroupEvent]] perlu di emit?
//     *                    default true.
//     * @return
//     */
//    def create(title:String, creator:User, origin:Origin[GraphType], noEmitEvent:Boolean=false,
//               autoStreamToChannel:Boolean=true, autoStreamToCreator:Boolean=true) = {
//
//        var rv:Option[PictureGroup] = None
//
////            val now = Digaku.engine.dateUtils.nowMilis
//
//        val v = PictureGroup(title).save()
//
//        val ed = creator.getYMPart(CollectionVertex.Kind.PUBLISH_CONTENT) --> PUBLISH_CONTENT --> v <()
//
//        ed.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)
//
//        v --> ORIGIN --> origin.reload().asInstanceOf[DbObject]
//
////            originE.setProperty("time", -now)
//
//        addToRoot(v)
//
//        val cc = v.toCC[PictureGroup].getOrElse {
//            throw new DigakuException("Cannot create PictureGroup")
//        }
//
//
//        origin match {
//            case ch:Group =>
//
//                if (autoStreamToCreator && !ch.privated){ // hanya kalau group-nya tidak private
//                    // langsung aja masukkan ke creator-nya
//                    // biar langsung muncul dan lebih reliable + realtime dibanding
//                    // mengandalkan event stream
//                    StreamBuilder.addToStream(creator, cc, timeFromCreation = true)
//                }
//
//                if (autoStreamToChannel){
//
//                    // langsung di-meld ke group-nya biar terlihat realtime tanpa perlu
//                    // mengandalkan StreamBuilder yang bekerja di background.
//                    val streamE = StreamBuilder.addToStream(ch, cc, timeFromCreation = true)
//
//                    streamE.setProperty("post.sticky", false)
//                    streamE.setProperty("locked", false)
//                    streamE.setProperty("post.kind", PostKind.kindStrClassToInt(cc.getClass.getName))
//                    streamE.setProperty("originId", ch.getId)
//                    streamE.setProperty("targetId", cc.getId)
//                }
//
//            case _ =>
//                if (autoStreamToCreator){
//                    // langsung aja masukkan ke creator-nya
//                    // biar langsung muncul dan lebih reliable + realtime dibanding
//                    // mengandalkan event stream
//                    StreamBuilder.addToStream(creator, cc, timeFromCreation = true)
//                }
//        }
//
//        db.commit()
//
//        rv = Some(cc)
//
//        // emit event stream
//        if (!noEmitEvent)
//            rv.map(pg => Digaku.engine.eventStream.emit(CreatePictureGroupEvent(creator, pg)))
//
//        rv.get
//    }
//
////    /**
////     * Get other post may like this posts
////     * based on user who like this post also like X posts.
////     * @param offset start offset.
////     * @param limit ends limit.
////     * @param containsFlag additional parameter if should only
////     *                     return post that contains x.
////     *                     @see [[com.ansvia.digaku.model.Post.contains]]
////     * @return
////     */
////    def getOtherMayLike(offset:Int, limit:Int, containsFlag:Int=NONE) = {
////
////        //        println("this.getClass.getCanonicalName: " + this.getClass.getCanonicalName)
////
////        var pipe = getVertex.pipe.aggregate().as("me_post")
////            .in(LIKES).out(LIKES)
////            .has("_class_", this.getClass.getCanonicalName)
////            .except("me_post")
////
////        if ( (containsFlag | LINK) == containsFlag ){
////            pipe = pipe.has("containsLink", true).asInstanceOf[GremPipeVertex]
////        }
////        if ( (containsFlag | VIDEO_LINK) == containsFlag ){
////            pipe = pipe.has("containsVideoLink", true).asInstanceOf[GremPipeVertex]
////        }
////        if ( (containsFlag | PIC) == containsFlag ){
////            pipe = pipe.has("containsPic", true).asInstanceOf[GremPipeVertex]
////        }
////
////
////        pipe.range(offset, offset + limit - 1).iterator().flatMap(_.toCC[SimplePost])
////    }
//}
//
//
//
