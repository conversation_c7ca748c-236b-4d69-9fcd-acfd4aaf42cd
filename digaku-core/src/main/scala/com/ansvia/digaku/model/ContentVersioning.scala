/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.graph.annotation.Persistent

/**
 * Author: robin
 *
 * model yang implement ini akan memiliki
 * attribut versioning.
 *
 * contoh implementasi lihat [[com.ansvia.digaku.model.Article]]
 */
trait ContentVersioning {
    this: Counter =>

    @Persistent var revision: Int = 0


    def incRevision() = {
        synchronized {

            getCounter.increment("revision")
            revision = getCounter.get("revision").toInt

            revision
        }
    }

    def latestRevision:Long = revision
}
