/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types._
import com.ansvia.graph.AbstractDbObject
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.database.GraphCompat.tx


/**
 * Author: robin
 *
 */
trait Streamable[idT] extends AbstractDbObject with HasOriginAbstract[GraphType] {
    this: BaseModel[idT] =>

    def getId:idT

    var creationTime:Long

    def hash:String = getId.toString
}

object Streamable extends DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    /**
     * digunakan untuk remove post tertentu dari stream user
     * @param streamable post stream yang akan di hide/remove
     */
    def removeStreamItem(user:User, streamable:Streamable[IDType]) {

        tx { t =>
//            db.getVertex(user.getId)
            t.getVertex(user.getId).pipe.out(COLLECTION_VERTEX)
                .has("kind",CollectionVertex.Kind.STREAM)
                .range(0,1).outE(STREAM)
                .has("targetId", streamable.getId)
                .remove()
        }


//        db.commit()
    }
}

object StreamKind {
    val ALL = 0
    val TEXT = 1
    val VIDEO = 2
    val GALLERY = 3
    val PICTURE = 4
    val DEAL = 5
    val EVENT = 6
    val SPONSOR = 7
    val OTHER = 8

    /**
     * digunakan untuk mendapatkan streamKind dari sebuah object Streamable
     * @param streamable
     * @return
     */
    def streamableToStreamKindInt(streamable:Streamable[IDType]) = {
        streamable match {
//            case simple:SimplePost =>
//                simple.containsVideoLink match {
//                    case true =>
//                        VIDEO
//                    case _ =>
//                        TEXT
//                }

            case article:Article =>
                TEXT
            case picture:PictureBase => PICTURE
//            case deal:Deal => DEAL
            case event:Event => EVENT
//            case sponsor:SponsorPost => SPONSOR
            case _ => OTHER
        }
    }

    /**
     * convert KindStr to Int
     * @param kindStr
     * @return
     */
    def streamKindStrToInt(kindStr:String) = {
        kindStr match {
            case "all" => ALL
            case "text" => TEXT
            case "video" => VIDEO
            case "picture" => PICTURE
            case "deal" => DEAL
            case "event" => EVENT
            case "sponsor" => SPONSOR
            case "gallery" => GALLERY
            case _ => OTHER
        }
    }

    /**
     * Convert dari kind int ke str.
     * @param kindInt
     * @return
     */
    def streamKindIntToStr(kindInt:Int) = {
        kindInt match {
            case TEXT => "text"
            case VIDEO => "video"
            case PICTURE => "picture"
            case DEAL => "deal"
            case EVENT => "event"
            case SPONSOR => "sponsor"
            case GALLERY => "gallery"
            case _ => "all"
        }
    }
}


