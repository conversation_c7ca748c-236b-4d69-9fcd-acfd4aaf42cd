///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
///**
// * Author: andrie
// *
// * Model yang berkaitan dengan iklan (Ad)
// */
//
//import com.ansvia.digaku.Types._
//import com.ansvia.graph.annotation.Persistent
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.exc.{InvalidParameterException, DigakuException}
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.event.impl.{DeleteAdEvent, CreateAdEvent}
//import com.tinkerpop.blueprints.{Direction, Vertex}
//import com.ansvia.digaku.utils.DateUtils
//import com.ansvia.graph.BlueprintsWrapper._
//import scala.collection.JavaConversions._
//import com.ansvia.digaku.model.Label.CREATE_AD
//import com.ansvia.commons.logging.Slf4jLogger
//import java.util.Calendar
//
//
//sealed abstract class Advertisement(val title:String) extends BaseModel[IDType] {
//    /**
//     * active hours ini artinya iklan ini akan muncul di jam-jam tertentu
//     * yang telah diset di attribut ini, sebagai contoh apabila
//     * diset activeHours = "1,4,9"
//     * maka iklan ini hanya akan muncul pada jam 1 malam, 4 pagi, dan 9 pagi.
//     * format jam menggunakan 24.
//     * Apabila di-set dengan wildcard `*` maka iklan akan muncul di setiap waktu.
//     */
//    @Persistent var activeHours = "*"
//
//    /**
//     * sama seperti `activeHours` bedanya ini cycle-nya adalah hari.
//     */
//    @Persistent var activeDays = "*"
//
//    /**
//     * minimal umur yang boleh melihat iklan ini,
//     * standard-nya 19.
//     * Apabila di-set 0 maka akan berlaku untuk semua umur.
//     */
//    @Persistent var minAge = 19
//
//    /**
//     * max umur yang boleh melihat iklan ini.
//     */
//    @Persistent var maxAge = 0
//
//    /**
//     * target gender yang akan melihat iklan ini.
//     * male / female
//     */
//    @Persistent var targetGender = ""
//
//    @Persistent var maxViewPerDay = 5
//    @Persistent var maxViewPerWeek = 5
//    @Persistent var maxViewPerMonth = 5
//
//    @Persistent var maxViewOverall = 100
//    @Persistent var clickCount = 0
//
//    @Persistent var tagStr = ""
//
//    /**
//     * Apabila diset maka iklan ini akan expired
//     * dan tidak muncul lagi setelah waktu yang ditentukan.
//     * Set 0 untuk disable expiration.
//     */
//    @Persistent var expired:Long = 0L
//
//    /**
//     * Apabila kita ingin menggunakan custom rules
//     * maka override method ini dan kembalikan
//     * true.
//     * Maka Ads manager akan memanggil fungsi
//     * `shouldShow`.
//     * @return
//     */
//    def useCustomRules:Boolean = false
//
//    /**
//     * Akan dipanggil apabila menggunakan custom rules
//     * `useCustomRules` == true.
//     * @param func custom function.
//     * @return
//     */
//    def shouldShow(func: ()=> Boolean):Boolean = {
//        func()
//    }
//
//    /**
//     * get tags in Seq
//     * @return
//     */
//    def tags = tagStr.split(",").toSeq
//
//
//    def isAllowed: Boolean = {
//        if (activeHours == "*" && activeDays == "*" && expired == 0L)
//            true
//        else {
//            if (expired < Digaku.engine.dateUtils.nowMilis)
//                true
//            else
//                false
//        }
//    }
//
//}
//
//// @TODO(robin): teruskan ini
///**
// * Implementasi untuk micro ad.
// * @param title judul iklannya.
// * @param description deskripsi iklannya.
// * @param url link yang akan dibuka apabila user meng-klik-nya.
// * @param _tagStr tag yang dipisahkan dengan koma.
// */
//case class MicroAd(override val title:String,
//                   description:String, url:String, _tagStr:String) extends Advertisement(title) {
//
//    @Persistent var thumbnailUrl:String = ""
//
//    tagStr = _tagStr
//
//    def click(user:User){
//        // @TODO(you): code here
//    }
//
//
//    def getClickers(offset:Int, limit:Int) = {
//        // @TODO(robin): code here
//        null
//    }
//
//}
//
//case class GeneralAd(override val title:String, description:String, url:String, _tagStr:String) extends Advertisement(title) {
//    // @TODO(robin): code here
//
//    tagStr = _tagStr
//}
//
//object Ads extends DaoBase[GraphType, Advertisement] with DbAccess {
//
//    import scala.collection.JavaConversions._
//    import com.ansvia.graph.BlueprintsWrapper._
//    import com.ansvia.digaku.model.Label.DAO_LIST
//
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.AdsRootVertex"
////    val rootVertexLabel = DAO_LIST
//
//    def getById(id:IDType) = {
//        val v = db.getVertex(id)
//        if (v != null)
//            v.toCC[Advertisement]
//        else
//            None
//    }
//
//    def addToGeneralRoot(v:Vertex) = {
//        addToRoot(v)
//    }
//
//    /**
//     * Get ad list.
//     * @return
//     */
//    def getList:Iterator[Advertisement] = {
//        rootVertex.pipe.out(rootVertexLabel).iterator()
//            .flatMap(_.toCC[Advertisement])
//    }
//
//
//    /**
//     * Delete ad, all inherited ad from Advertisement
//     * are supported.
//     * @param ad ad to delete.
//     */
//    def delete(ad:Advertisement){
//        ad.reload()
//        ad.getVertex.pipe.bothE().remove()
//        ad.getVertex.remove()
//
//        db.commit()
//
//        Digaku.engine.eventStream emit DeleteAdEvent(ad)
//    }
//}
//
//object MicroAd extends DaoBase[GraphType, MicroAd] {
//
//    val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.MicroAdRootVertex"
//
//    def create(title:String, description:String, url:String, tagStr:String, creator:User) = {
//
//        var adO: Option[MicroAd] = None
//
//        val v = MicroAd(title, description, url, tagStr.toLowerCase).save()
//        v.setProperty("ad.lower.title", title.toLowerCase)
//
//        val ed = addToRoot(v)
//        ed.setProperty("ad.lower.title", title.toLowerCase)
//
//        // add to Ads root
//        Ads.addToGeneralRoot(v)
//
//        creator.reload()
//        val createE = creator.getVertex --> CREATE_AD --> v <()
//        createE.setProperty("timeOrder", System.currentTimeMillis())
//
//        adO = Some(v.toCC[MicroAd].getOrElse {
//            throw new DigakuException("Cannot create MicroAd")
//        })
//
//        db.commit()
//
//        adO.map( ad => Digaku.engine.eventStream.emit(CreateAdEvent(ad)) )
//
//        adO.get
//    }
//
//
//    def getByTitle(title:String): Option[MicroAd] = {
//        rootVertex.pipe.outE(rootVertexLabel)
//            .has("ad.lower.title", title.toLowerCase)
//            .inV()
//            .headOption.flatMap(_.toCC[MicroAd])
//    }
//
//
//    /**
//     * Search Micro Ad by it Tags.
//     * @param tags tags to search.
//     * @return
//     */
//    def getByTags(tags:Seq[String]):Seq[Advertisement] = {
//        val tagsStr = tags.foldLeft("")(_ + " " + _).trim
//        Digaku.engine.searchEngine.searchAd(tagsStr, 0, 1000).entries
//    }
//
//}
//
//
//case class BannerSlider(override val title:String, var url:String) extends Advertisement(title) with Closable with DbAccess {
//
//    @Persistent var bannerPicture = ""
//
//    /**
//     * Get creator banner slider
//     * @return
//     */
//    def creatorBanner:User = {
//        val id = getVertex.getProperty[Long]("creatorId")
//
//        User.getById(id).getOrElse {
//            throw InvalidParameterException("Creator Banner not found")
//        }
//    }
//
//}
//
//
//object BannerSlider extends DaoBase[GraphType, BannerSlider] {
//
//    val ROOT_VERTEX_CLASS:String = "com.ansvia.digaku.model.BannerSliderRootVertex"
//
//    /**
//     * Create Banner Slider
//     * @param title title banner
//     * @param url url redirect to
//     * @param bannerPicture picture banner
//     * @param creator who is create?
//     * @param noTx set true if doesn't want to run in transaction mode
//     * @return
//     */
//    def create(title:String, url:String, bannerPicture:String, creator:User, noTx:Boolean = false) = {
//
//        val v = BannerSlider(title, url).save()
//        v.setProperty("creatorId", creator.getId)
//        v.setProperty("closed", true)
//
//        val ed = addToRoot(v)
//        ed.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)
//
//        val bs = v.toCC[BannerSlider].getOrElse {
//            throw new DigakuException("Cannot create BannerSlider, persistent error?")
//        }
//
//        bs.bannerPicture = bannerPicture
//        bs.save()
//
//        if (!noTx)
//            db.commit()
//
//        bs
//
//    }
//
//    /**
//     * Get list banner slider without is closed true
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def getListActive(offset:Int, limit:Int):Seq[BannerSlider] = {
//        rootVertex.pipe.out(rootVertexLabel)
//            .hasNot("closed", true)
//            .asInstanceOf[GremPipeVertex]
//            .range(offset, (offset + limit) -1)
//            .iterator()
//            .flatMap(_.toCC[BannerSlider]).toSeq
//    }
//
//}
//
//
//case class BannerAd(override val title:String, var url:String) extends Advertisement(title) with Closable with DbAccess {
//
//    import java.util.Date
//
//
//    @Persistent var bannerAdPicture = ""
//    @Persistent var startTime:Long = 0L
//    @Persistent var endTime:Long = 0L
//
//
//    /**
//     * Get creator banner ad
//     * @return
//     */
//    def creatorBannerAd:User = {
//        val id = getVertex.getProperty[Long]("creatorId")
//
//        User.getById(id).getOrElse {
//            throw InvalidParameterException("Creator Banner Ad not found")
//        }
//    }
//
//    /**
//     * Who user add channels to banner ad
//     * @return
//     */
//    def getAdvertiser:User = {
//        getVertex.pipe.inE(CREATE_AD).headOption.flatMap { ed =>
//            User.getById(ed.getOrElse("advertiserId", 0L))
//        }.getOrElse {
//            creatorBannerAd
//        }
//    }
//
//    /**
//     * Add channels to banner ad
//     * @param user see [[com.ansvia.digaku.model.User]]
//     * @param startTime start time
//     * @param endTime end time
//     * @param group list of group see [[com.ansvia.digaku.model.Forum]]
//     */
//    def addToChannel(user:User, startTime:Date, endTime:Date, group:Group) {
//
//        val ed = group.reload() --> CREATE_AD --> this.reload() <()
//        ed.setProperty("advertiserId", user.getId)
//        ed.setProperty("adBannerId", this.getId)
//        ed.setProperty("adChannelId", group.getId)
//
//        if (this.startTime == 0L || this.endTime == 0L) {
//            val cal = Calendar.getInstance()
//            cal.setTime(endTime)
//            cal.add(Calendar.DATE, 1)
//
//            this.startTime = startTime.getTime
//            this.endTime = cal.getTime.getTime
//        }
//
//        this.save()
//
//    }
//
//    /**
//     * Get group list from a banner ad
//     * @param offset
//     * @param limit
//     * @return
//     */
//    def getListChannel(offset:Int = 0, limit:Int = 20):Seq[Group] = {
//        this.getVertex.pipe.inE(CREATE_AD)
//            .has("adBannerId", this.getId)
//            .range(offset, (offset + limit) - 1)
//            .asInstanceOf[GremPipeEdge]
//            .iterator().flatMap { ed =>
//                Group.getById(ed.getOrElse("adChannelId", 0L))
//            }.toSeq
//    }
//
//    override def isAllowed:Boolean = {
//        val now = Digaku.engine.dateUtils.now
//
//        if (startTime < now.getTime && endTime > now.getTime) {
//            if (this.isClosed) {
//                this.setClosed(false, getAdvertiser, "").save()
//                db.commit()
//            }
//
//            true
//        } else {
//            if (!this.isClosed) {
//                this.setClosed(true, getAdvertiser, "").save()
//                db.commit()
//            }
//
//            false
//        }
//    }
//
//}
//
//
//object BannerAd extends DaoBase[GraphType, BannerAd] with Slf4jLogger {
//
//    import org.joda.time.format.DateTimeFormat
//
//
//    val ROOT_VERTEX_CLASS:String = "com.ansvia.digaku.model.AdsBannerChannelRootVertex"
//
//    private val DATE_TIME_RE = """\d\d/\d\d/\d{4}""".r
//
//    val DATE_TIME_FORMAT = DateTimeFormat.forPattern("dd/MM/yyyy")
//
//    object Kind {
//        val USER = 1
//        val GROUP = 2
//    }
//
//    /**
//     * Create banner ad
//     * @param title title banner
//     * @param url url redirect to
//     * @param bannerPicture banner ad picture
//     * @param creator who is create?
//     * @param kind kind from banner ad see [[com.ansvia.digaku.model.BannerAd.Kind]]
//     * @param noTx set true if doesn't want to run in transaction mode
//     * @return
//     */
//    def create(title:String, url:String, bannerPicture:String, creator:User, kind:Int, noTx:Boolean = false) = {
//        val v = BannerAd(title, url).save()
//        v.setProperty("creatorId", creator.getId)
//        v.setProperty("kind", kind)
//        v.setProperty("closed", true)
//
//        val ed = addToRoot(v)
//        ed.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)
//
//        val ba = v.toCC[BannerAd].getOrElse {
//            throw new DigakuException("Cannot create AdsBannerChannel, persistent error?")
//        }
//
//        ba.bannerAdPicture = bannerPicture
//        ba.save()
//
//        if (!noTx)
//            db.commit()
//
//        ba
//
//    }
//
//    private def validDateTime(dateStr:String):Boolean = {
//        if (DATE_TIME_RE.pattern.matcher(dateStr).matches()) {
//            try {
//                DATE_TIME_FORMAT.parseDateTime(dateStr)
//                true
//            } catch {
//                case e:org.joda.time.IllegalFieldValueException =>
//                    warn(e.getMessage)
//                    false
//            }
//        } else
//            false
//    }
//
//    /**
//     * Check datetime format
//     * @param dateStr datetime string format dd/MM/yyyy
//     */
//    def validateDateTime(dateStr:String) {
//        if (!validDateTime(dateStr)) {
//            throw InvalidParameterException(s"Invalid date time: $dateStr, use this format: day/month/year example: 29/04/1989")
//        }
//    }
//
//
//    implicit class ChannelAdBanner(group:Group) {
//
//        def hasAdBanner:Boolean = {
//            group.getVertex.pipe.outE(CREATE_AD)
//                .has("adChannelId", group.getId)
//                .asInstanceOf[GremPipeEdge]
//                .iterator().hasNext
//        }
//
//        def getBannerAd:Option[BannerAd] = {
//            group.getVertex.pipe.outE(CREATE_AD)
//                .has("adChannelId", group.getId)
//                .inV().headOption.flatMap(_.toCC[BannerAd])
//        }
//
//        def setBannerDefault() {
//            group.getVertex.pipe.outE(CREATE_AD)
//                .has("adChannelId", group.getId)
//                .asInstanceOf[GremPipeEdge]
//                .headOption.foreach { ed =>
//                    ed.setProperty("defaultBanner", group.bannerPicUrl)
//                }
//        }
//
//        def getBannerDefault:String = {
//            group.getVertex.getEdges(Direction.OUT, CREATE_AD).headOption.map { ed =>
//                ed.getOrElse("defaultBanner", "")
//            }.getOrElse("")
//        }
//
//        def removeBannerAd(bannerAdPic:String) {
//            if (group.bannerPicUrl == bannerAdPic) {
//                group.bannerPicUrl = group.getBannerDefault
//                group.save()
//            }
//
//            group.reload().getVertex.pipe.outE(CREATE_AD).remove()
//        }
//
//    }
//
//}
