package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types.{GremPipeVertex, IDType, TransactionalGraphType}
import com.ansvia.digaku.database.GraphCompat._
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.utils.SynchronizedKey
import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.Vertex
import com.ansvia.graph.BlueprintsWrapper._
import com.tinkerpop.gremlin.Tokens
import com.ansvia.graph.gremlin._
import scala.collection.JavaConversions._
import com.ansvia.graph.IdGraphTitanDbWrapper._
import org.joda.time.{DateTime, DateTimeZone}


/**
  * Author: Buaetin Nadir (<EMAIL>)
  */

case class UserLeaderboard(leaderboardName:String, uid:Long, var lbPoint:Int) extends BaseModel[IDType] with DbAccess
    with CollectionVertex {
    // Point per-day
    @Persistent var pointDaily:String = ""
    @Persistent var lastUpdated:Long = 0L
    @Persistent var exclude: Boolean =false

    // SeqStore untuk point history (format penyimpanan (time:point))
    lazy val pointHistorySeqStore = Digaku.engine.seqStoreProvider.build("point-history-" + leaderboardName, () =>
        Digaku.engine.idFactory.createId().asInstanceOf[Long])

    /**
      * Mendapatkan perolehan point per-day
      * @return
      */
    def getPointDaily = {
        pointDaily.split(",").filter(_.nonEmpty).flatMap { rv =>
            val kv = rv.split(":")

            if (kv.length == 2) {
                Some(kv(0).toInt, kv(1).toInt)
            } else {
                None
            }
        }.toMap
    }

    /**
      * Menambah history point
      * @param point
      */
    def addHistory(point:Int): Unit ={
        pointHistorySeqStore.insert(s"${Digaku.engine.dateUtils.nowMilis}:$point")
    }

    /**
      * Mendapatkan User dari point history
      * @return
      */
    def getUser: Option[User] = {
        User.getById(uid)
    }
}


object UserLeaderboard extends DbAccess with SynchronizedKey[Long] {

    val ROOT_VERTEX_CLASS:String = "com.ansvia.digaku.dao.UserLeaderboardVertex"

    lazy val excludedUserSeqStore = Digaku.engine.seqStoreProvider.build("leaderboard-excluded-users", () => 0L)
    lazy val excludedUserLogSeqStore = Digaku.engine.seqStoreProvider
        .build("leaderboad-excluded-users-log", () => Digaku.engine.idFactory.createId().asInstanceOf[Long])

    /**
      * Root vertex dari leaderboard
      * @return
      */
    def rootVertex(_db:TransactionalGraphType = db ): Vertex = synchronized {
        _db.getVertices("_class_", ROOT_VERTEX_CLASS)
            .headOption.getOrElse {

            val v = db.addVertex(null)
            v.setProperty("_class_", ROOT_VERTEX_CLASS)

            v
        }
    }

    val cvKind = CollectionVertex.Kind.LEADERBOARD

    /**
      * Get current time on WIB UTC+7
      * @return
      */
    private def getCurrentTime():DateTime = {
        Digaku.engine.dateUtils.getCurrentTime().toDateTime(DateTimeZone.forID("Asia/Jakarta"))
    }

    /**
      * Get year and month
      * @return
      */
    private def getYearMonth: (Int, Int) = {
        val dt = getCurrentTime()

        (dt.getYear, dt.getMonthOfYear)
    }

    /**
      * Mendapatkan collection vertex berdasarkan tahun dan bulan function ini di panggil
      * @param cv vertex untuk di collectionkan
      * @param year
      * @param month
      * @param _db
      * @return
      */
    def getCollectionVertex(cv:Vertex, year:Int, month:Int, _db:TransactionalGraphType = db) = synchronized {
        val _cv =
            cv.pipe.out(COLLECTION_VERTEX)
                .has("kind", cvKind).has("year", year).has("month", month).asInstanceOf[GremPipeVertex]
                .headOption.getOrElse {
                    val v = _db.addVertex(null)
                    v.setProperty("kind", cvKind)
                    v.setProperty("year", year)
                    v.setProperty("month", month)

                    val ed = cv.addEdge(COLLECTION_VERTEX, v)
                    val timeOrder = f"$year%04d$month%02d".toLong
                    ed.setProperty("timeOrder", timeOrder)
                    // kind juga dituliskan di edge-nya untuk optimal retrieving
                    ed.setProperty("kind", cvKind)
                    v
                }

        _db.getVertex(_cv.getId)
    }

    /**
      * Create leaderboard untuk user tertentu
      * @param user
      * @return
      */
    private def createInternal(user: User, point:Int): Unit = {
        val (year, month) = getYearMonth
        val cv = getCollectionVertex(rootVertex(), year, month)
        val name = s"$year%02d${user.getId}".format(month)
        val day = getCurrentTime().getDayOfMonth
        val newUserLb = UserLeaderboard(name, user.getId, point)
        newUserLb.pointDaily = s"$day:$point"
        newUserLb.lastUpdated = Digaku.engine.dateUtils.nowMilis
        newUserLb.exclude = isExcluded(user)

        val v = newUserLb.saveWithLabel(VertexLabels.USER_LEADERBOARD)
        val nowMillis = Digaku.engine.dateUtils.nowMilis
        val ed = cv.addEdge(HAS_LEADERBOARD, v)
        ed.setProperty("timeOrder", nowMillis)

        val userEd = v.addEdge(USER_LEADERBOARD, db.getVertex(user.getId))
        userEd.setProperty("timeOrder", nowMillis)

        db.commit()

        v.toCC[UserLeaderboard].getOrElse {
            throw new DigakuException("Cannot create user leaderboard: " + user.getId)
        }

        newUserLb.addHistory(point)
    }

    /**
      * Menambahkan point user leaderboard
      * @param user
      * @return
      */
    def incrementPoint(user: User, incBy: Int): Unit = synchronizedKey(user.getId) {
        val day = getCurrentTime().getDayOfMonth
        getLeaderboard(user).map { lb =>
            lb.addHistory(incBy)
            tx { t =>
                val _lb = getFromTx(lb, t)
                var pointDaily = _lb.getPointDaily

                pointDaily.+=(day -> (pointDaily.getOrElse(day, 0) + incBy))

                _lb.lbPoint = _lb.lbPoint + incBy
                _lb.lastUpdated = Digaku.engine.dateUtils.nowMilis
                _lb.pointDaily = pointDaily.map(rv => s"${rv._1}:${rv._2}").mkString(",")

                _lb.save()(t)
            }
        }.getOrElse {
            createInternal(user, incBy)
        }

        db.commit()
    }

    /**
      * Mendapatkan leaderboard user tertentu
      * @param user
      * @return
      */
    def getLeaderboard(user: User):Option[UserLeaderboard] = {
        val (year, month) = getYearMonth
        val name = s"$year%02d${user.getId}".format(month)
        db.query().has("label", VertexLabels.USER_LEADERBOARD)
            .has("leaderboardName", name)
            .vertices()
            .headOption.flatMap(_.toCC[UserLeaderboard])
    }

    /**
      * Mendapatkan point leaderboard user
      * @param user
      * @return
      */
    def getUserPoint(user: User): Int = {
        getLeaderboard(user).map(_.lbPoint).getOrElse(0)
    }

    /**
      * Mendapatkan ranking user pada leaderboard
      * @param user
      * @return
      */
    def getRank(user:User, _db:TransactionalGraphType = db):Option[Int] = {
        val (year, month) = getYearMonth
        val rank = rootVertex(_db).pipe.out(COLLECTION_VERTEX)
            .has("kind", cvKind)
            .has("year", year)
            .has("month", month)
            .out(HAS_LEADERBOARD)
            .has("lbPoint", Tokens.T.gt, 0)
            .has("exclude", false)
            .asInstanceOf[GremPipeVertex]
            .order { (v1:Vertex, v2:Vertex) =>
                val point = -v1.getOrElse("lbPoint", 0).compareTo(v2.getOrElse("lbPoint", 0))
                if (point != 0) {
                    point
                } else {
                    v1.getOrElse("lastUpdated", 0L).compareTo(v2.getOrElse("lastUpdated", 0L))
                }
            }
            .indexWhere(v => v.getOrElse("uid", 0L) == user.getId)

        if (rank < 0) {
            None
        } else {
            Some(rank + 1)
        }
    }

    /**
      * Mendapatkan list user pada leaderboard
      * @param offset
      * @param limit
      * @param _db
      * @return
      */
    def getList(offset:Int, limit:Int, withExcludeUser:Boolean = false, _db:TransactionalGraphType = db): List[(User, Int)] = {
        val (year, month) = getYearMonth
        var query = rootVertex(_db).pipe.out(COLLECTION_VERTEX)
            .has("kind", cvKind)
            .has("year", year)
            .has("month", month)
            .out(HAS_LEADERBOARD)
            .has("lbPoint", Tokens.T.gt, 0)

        if (!withExcludeUser) {
            query = query.has("exclude", false)
        }

        query.asInstanceOf[GremPipeVertex]
            .order { (v1:Vertex, v2:Vertex) =>
                val point = -v1.getOrElse("lbPoint", 0).compareTo(v2.getOrElse("lbPoint", 0))
                if (point != 0) {
                    point
                } else {
                    v1.getOrElse("lastUpdated", 0L).compareTo(v2.getOrElse("lastUpdated", 0L))
                }
            }.range(offset, (offset + limit) - 1)
            .iterator().toList
            .flatMap(_.toCC[UserLeaderboard])
            .flatMap { uLb =>
                User.getById(uLb.uid).map(u => (u, uLb.lbPoint))
            }
    }

    /**
      * Mendapatkan jumlah user yang ada dalam leaderboard
      * @param maxCount
      * @param excludeUser
      * @param _db
      * @return
      */
    def getCount(maxCount:Option[Int], q:String="", withExcludeUser:Boolean = false, _db:TransactionalGraphType = db):Long = {
        val (year, month) = getYearMonth
        var query = rootVertex().pipe.out(COLLECTION_VERTEX)
            .has("kind", cvKind)
            .has("year", year)
            .has("month", month)
            .out(HAS_LEADERBOARD)
            .has("lbPoint", Tokens.T.gt, 0)

        if (!withExcludeUser) {
            query = query.has("exclude", false)
        }

        val _q = q.trim.toLowerCase
        if (_q.nonEmpty) {
            query = query
                .out(USER_LEADERBOARD)
                .filter { v:Vertex =>
                    v.getOrElse("user.lower-name", "").toLowerCase.contains(_q) ||
                        v.getOrElse("fullName", "").toLowerCase.contains(_q)
                }
        }

        maxCount.foreach { mc =>
            query = query.range(0, mc - 1)
        }

        query.count()
    }

    /**
      * Pencarian user dalam leaderboard
      * @param offset
      * @param limit
      * @param q
      * @param withExcludeUser
      * @param _db
      * @return
      */
    def searchUserLeaderboard(offset:Int, limit:Int, q:String = "",
                              withExcludeUser:Boolean = false, _db:TransactionalGraphType = db): List[(User, Int, Int)] = {

        val (year, month) = getYearMonth
        var map:Map[Long, Int] = Map.empty[Long, Int]
        var query = rootVertex(_db).pipe.out(COLLECTION_VERTEX)
            .has("kind", cvKind)
            .has("year", year)
            .has("month", month)
            .out(HAS_LEADERBOARD)
            .has("lbPoint", Tokens.T.gt, 0)
            .asInstanceOf[GremPipeVertex]

        if (!withExcludeUser) {
            query = query.has("exclude", false).asInstanceOf[GremPipeVertex]
        }

        query = query.order { (v1:Vertex, v2:Vertex) =>
            val point = -v1.getOrElse("lbPoint", 0).compareTo(v2.getOrElse("lbPoint", 0))
            if (point != 0) {
                point
            } else {
                v1.getOrElse("lastUpdated", 0L).compareTo(v2.getOrElse("lastUpdated", 0L))
            }
        }

        val _q = q.trim.toLowerCase
        query = query.as("lbV").out(USER_LEADERBOARD)

        if (_q.nonEmpty) {
            var count = 1
            query = query.filter { v:Vertex =>
                    val rv = v.getOrElse("user.lower-name", "").toLowerCase.contains(_q) ||
                        v.getOrElse("fullName", "").toLowerCase.contains(_q)

                    if (rv) {
                        map += (v.getId.asInstanceOf[Long] -> count)
                    }

                    count = count + 1
                    rv
                }
        }

        query.back("lbV").asInstanceOf[GremPipeVertex].range(offset, (offset + limit) - 1)
            .iterator()
            .toList
            .flatMap { lbV =>
                val uid = lbV.getOrElse("uid", 0L)
                val lbPoint = lbV.getOrElse("lbPoint", 0)
                User.getById(uid).map {user =>
                    (user, map.getOrElse(uid, 0), lbPoint)
                }
            }
    }

    /**
      * Exculde user tertentu dari leaderboard.
      * @param user
      */
    def excludeUser(user: User, exclude:Boolean, _db:TransactionalGraphType = db): Unit = synchronizedKey(user.getId) {
        getLeaderboard(user).foreach { lb =>
            lb.exclude = exclude
            lb.save()(_db)
        }

        val now = Digaku.engine.dateUtils.nowMilis
        if (exclude) {
            excludedUserSeqStore.insert(user.getId, now.toString)
        } else {
            excludedUserSeqStore.delete(user.getId)
        }

        excludedUserLogSeqStore.insert(s"${user.getId},$now,${exclude.toString}")
    }

    /**
      * Check apakah user sudah di exclude dari leaderboard
      * @param user
      * @return
      */
    def isExcluded(user: User): Boolean = {
        excludedUserSeqStore.get(user.getId).isDefined
    }

    /**
      * Mendapatkan list user yang di exclude
      * @param fromId
      * @param toId
      * @param limit
      * @return
      */
    def getExcludedUsers(fromId:Option[Long], toId:Option[Long], limit:Int):Iterator[User] = {
        excludedUserSeqStore.getStream(fromId.map(Long.box), toId.map(Long.box), limit)
            .flatMap(rv => User.getById(rv._1))
    }

    /**
      * Mendapatkan jumlah user yang di exclude
      * @return
      */
    def getExcludedUsersCount:Int = {
        excludedUserSeqStore.getCount
    }
}
