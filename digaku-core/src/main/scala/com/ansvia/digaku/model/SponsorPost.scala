///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.graph.annotation.Persistent
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.exc.{InvalidParameterException, DigakuException}
//import com.ansvia.digaku.event.EventStream
//import com.ansvia.digaku.event.impl.SponsorPostCreatedEvent
//import com.ansvia.digaku.utils.DateUtils
//import com.ansvia.digaku.model.Label._
//import com.ansvia.graph.BlueprintsWrapper._
//import java.util.Date
//import scala.collection.JavaConversions._
//import com.tinkerpop.blueprints.Vertex
//import scala.util.Random
//import com.tinkerpop.gremlin.Tokens.T
//import com.ansvia.digaku.event.impl.SponsorPostCreatedEvent
//import scala.Some
//import com.ansvia.digaku.exc.InvalidParameterException
//import com.ansvia.digaku.{Digaku, Types}
//
//
///**
// * Author: robin
// * Date: 12/18/13
// * Time: 5:22 PM
// *
// */
//case class SponsorPost(name:String, content:String) extends BaseModel[IDType]
//    with Likable with Promotable with Deletable with HasCreator
//    with Closable with Viewable[IDType] with DbAccess with Streamable[IDType]
//    with HasEmbeddedObject[IDType] with HasOrigin[GraphType] with Counter {
//
//    private lazy val counter = Digaku.engine.counterProvider("sponsorpost-counter-" + getId)
//
//    /**
//     * @see [[com.ansvia.digaku.model.PostKind]]
//     */
//    @Persistent val kind:Int = PostKind.SPONSOR
//
//    @Persistent var clickedCount:Int = 0
//    @Persistent var appearCount:Int = 0
//
//    /**
//     * Ini command satu line dalam bentuk string
//     * yang merepresentasikan apabila diclick sponsor ini
//     * operasi apa yang akan dilakukan.
//     *
//     * Format:
//     *          [action]||[parameter-1][parameter-n]
//     *
//     * Contoh:
//     *
//     *          goto-url||http://my.cool.advertising.com/referer=123
//     *
//     * action yang didukung:
//     *
//     *      1. goto-url
//     *
//     *          format: [goto-url]||[URL-TARGET]
//     *
//     *      2. goto-group
//     *
//     *          format: [goto-group]||[FORUM-NAME]
//     *
//     *      3. join-group
//     *
//     *          format: [join-group]||[FORUM-NAME]
//     *
//     * @see [[com.ansvia.digaku.model.SponsorPost.SUPPORTED_ACTIONS]]
//     */
//    @Persistent var actionRule:String = ""
//
//    @Persistent var tags:String = ""
//    @Persistent var minAge:Int = 0
//    @Persistent var maxAge:Int = 0
//    @Persistent var startTime:Long = 0L
//    @Persistent var endsTime:Long = 0L
//    @Persistent var bannerPicture:String = ""
//
//    /**
//     * jam tayang
//     */
//    @Persistent var showTimeStart:Int = 0
//    @Persistent var showTimeEnds:Int = 0
//
//
//    /**
//     * @see [[com.ansvia.digaku.model.SexType]]
//     * for all just set -1
//     */
//    @Persistent var genderTarget:Int = -1
//
//    @Persistent var targetCountry:String = ""
//    @Persistent var targetProvince:String = ""
//    @Persistent var targetCity:String = ""
//
//    lazy val origin: Origin[GraphType] = {
//        val meV = db.getVertex(this.getId)
//        val v = meV.pipe.out(ORIGIN).headOption
//        if (v.isDefined){
//            try {
//                val rv = v.get.toCC[Group].getOrElse {
//                    v.get.toCC[User].getOrElse(null)
//                }
//                if (rv!=null)
//                    rv.asInstanceOf[Origin[GraphType]]
//                else
//                    null
//            }catch{
//                case e:NullPointerException =>
//                    error("no origin for post %s".format(getId))
//                    null
//            }
//        }else{
//            error("no origin for post %s".format(this))
//            null
//        }
//    }
//
//    /**
//     * Get counter provider used by this model.
//     * @return
//     */
//    def getCounter = counter
//
//
//}
//
//
//
//object SponsorPost extends DaoBase[GraphType, SponsorPost] with DbAccess {
//
//    val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.SponsorPostRootVertex"
//
//    val SUPPORTED_ACTIONS = Seq(
//        "goto-url", "goto-group", "join-group"
//    )
//
//
//    /**
//     * Buat SponsorPost.
//     * @param name nama sponsor post.
//     * @param content isi dari sponsor post.
//     * @param actionRule action rule.
//     *                   @see [[com.ansvia.digaku.model.SponsorPost.actionRule]]
//     * @param startTime starting time.
//     * @param endsTime ends time.
//     * @param active if set to true then this sponsor will pre-activated.
//     * @return
//     */
//    def create(name:String, content:String, actionRule:String,
//               startTime:Date, endsTime:Date, active:Boolean=false) = {
//
//        // validate action rule
//        actionRule.split("\\|\\|").toList match {
//            case _action :: _ => {
//                if (!isActionSupported(_action))
//                    throw InvalidParameterException("Action not supported: " + _action)
//            }
//            case _ =>
//                throw InvalidParameterException("No action rule")
//        }
//
//        val v = SponsorPost(name, content).save()
//
//        v.setProperty("closed", !active)
//        v.setProperty("actionRule", actionRule)
//        v.setProperty("startTime", startTime.getTime)
//        v.setProperty("endsTime", endsTime.getTime)
//
//        val ed = addToRoot(v)
//
//        ed.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)
//
//        db.commit()
//
//        val sp = v.toCC[SponsorPost].getOrElse {
//            throw new DigakuException("Cannot create SponsorPost, persistent error?")
//        }
//
//        Digaku.engine.eventStream.emit(SponsorPostCreatedEvent(sp))
//
//        sp
//    }
//
//
//    /**
//     * Periksa apakah action didukung.
//     * @param action nama action (key).
//     * @return
//     */
//    def isActionSupported(action:String) = {
//        SUPPORTED_ACTIONS.contains(action)
//    }
//
//    private lazy val rnd = new Random(System.currentTimeMillis())
//
//
//    /**
//     * Pick random sponsor from collection.
//     * @return
//     */
//    def pickOneRandomSponsor(vs:Seq[Vertex]): Option[Vertex] = {
//        if (vs.length > 0){
//            val i = rnd.nextInt(vs.length)
//            Some(db.getVertex(vs(i).getId))
//        }else
//            None
//    }
//
//    /**
//     * Pick random sponsor.
//     * @return
//     */
//    def pickOneRandomSponsor(containTags:Option[String]=None):Option[Vertex] = {
//        import com.ansvia.graph.gremlin._
//
//        val now = Digaku.engine.dateUtils.nowMilis
//
//        var pipe = SponsorPost.rootVertex.pipe.out(SponsorPost.rootVertexLabel)
//            .has("startTime", T.lte, now)
//            .has("endsTime", T.gt, now)
//            .hasNot("closed", true).asInstanceOf[GremPipeVertex]
//
//        containTags.map { tags =>
//            pipe = pipe.filter { (v:Vertex) =>
//                tags.exists(v.getOrElse("tags","").toLowerCase.split(",").contains(_))
//            }
//        }
//
//        val vs = pipe
//                .asInstanceOf[GremPipeVertex]
//                .iterator().toSeq
//
//        pickOneRandomSponsor(vs)
//    }
//
//
//    /**
//     * clear / remove all sponsor post.
//     * used in unittest.
//     */
//    def clear(){
//        rootVertex.pipe.out(rootVertexLabel).remove()
//        db.commit()
//    }
//
//}
//
