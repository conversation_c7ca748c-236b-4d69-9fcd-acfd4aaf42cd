/*
 * Copyright (c) 2013-2017 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.event.impl.ResponseQuoteEvent
import com.ansvia.digaku.exc.LimitationReachedException
import com.ansvia.digaku.notifications.impl.QuoteNotification

/**
 * Author: nadir (<EMAIL>)
 * Digunakan untuk menyimpan response id yang di quote pada response tertentu.
 */

trait ResponseQuotes {

    implicit class ResponseSettingsWrapper(response:Response) {

        val key = "quoted-response-" + response.getId

        lazy val quotedResponse = Digaku.engine.seqStoreProvider.build(key, () =>
            Digaku.engine.idFactory.createId().asInstanceOf[Long])

        /**
         * Digunakan untuk menggubah response yang di quote
         * @param responses response yang akan di quote
         */
        def setQuoted(responses:Seq[Response]): Unit = {

            if (responses.length > 10) {
                throw LimitationReachedException("Maximum 10 quote.")
            }

            quotedResponse.clear()

            responses.sortBy(_.creationTime).foreach { resp =>
                quotedResponse.insert(resp.getId.toString)
            }

            Digaku.engine.eventStream.emit(ResponseQuoteEvent(response))

        }

        /**
         * Mengubah response yang di quote menggunakan response id
         * @param respId
         */
        def setQuotedIds(respId:Seq[Long]): Unit ={
            setQuoted(respId.flatMap(Response.getById))
        }

        /**
         * Mendapatkan id response yang di quote
         * @return
         */
        def getQuotedResponseIds():Seq[Long] = {
            quotedResponse.getStream(None, None, 10).map { rv =>
                rv._2.toLong
            }.toSeq
        }

        /**
         * Mendapatkan response yang di quote
         * @return
         */
        def getQuotedResponse():Seq[Response] = {
            getQuotedResponseIds().flatMap { id =>
                Response.getById(id)
            }
        }
    }
}

object ResponseQuotes extends ResponseQuotes
