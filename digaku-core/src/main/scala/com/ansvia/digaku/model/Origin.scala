/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.Graph
import com.ansvia.digaku.Types._
import com.ansvia.digaku.utils.DateUtils
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.graph.AbstractDbObject


trait Origin[dbT <: Graph] extends AbstractDbObject with Privatable[IDType, GraphType] {

    def getId:IDType

    /**
     * see [[com.ansvia.digaku.model.OriginKind]]
     */
    @Persistent var kind:Int

    lazy val kindStr = OriginKind.intToStr(kind)

    def getName:String


}



//object Origin extends DbAccess {
//
//}

