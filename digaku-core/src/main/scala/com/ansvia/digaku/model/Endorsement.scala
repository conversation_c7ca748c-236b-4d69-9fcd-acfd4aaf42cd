/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.helpers.DbAccess
import scala.collection.JavaConversions._
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.digaku.exc.{InvalidParameterException, LimitationReachedException, AlreadyExistsException, DigakuException}
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.event.impl.EndorseEvent
import com.tinkerpop.blueprints._
import com.tinkerpop.gremlin.Tokens.T
import com.ansvia.graph.gremlin._

/**
 * Author: robin
 *
 * model untuk Endorsement
 */
case class Endorsement(var labelName:String) extends BaseModel[IDType] with Blockable with DbAccess {
    override def __save__(v:Vertex) = {
        v.setProperty("endorsement.label-lower", labelName.toLowerCase.trim)
    }

    /**
     * Mendapatkan daftar user siapa saja yang
     * mengendorse.
     * @param offset starting offset.
     * @param limit ends limit.
     * @param origin user yang didapat berdasarkan origin
     * @return
     */
    def getEndorsers(offset:Int, limit:Int)(implicit origin:Origin[GraphType]):Seq[User] = {
        origin.getVertex.pipe.outE(ENDORSEMENT)
            .has("endorsementId", getId)
            .has("accepted", true)
            .range(offset, offset + limit - 1)
            .asInstanceOf[GremPipeEdge]
            .iterator().flatMap { ed =>

            User.getById(ed.getOrElse("endorserId", 0L))
        }.toSeq
    }

}

/**
 * Endorsement DAO.
 */
object Endorsement extends DaoBase[GraphType, Endorsement] {
    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.EndorsementRootVertex"

    private lazy val ENDORSEMENT_RE = """[a-zA-Z0-9\-\._]*?$""".r

    /**
     * Jenis endorsement-nya.
     */
    object Kind {
        val USER = 1
        val GROUP = 2
    }

    /**
     * Create new endorsement.
     * @param label endorsement label name.
     * @param kind endorsement kind, @see [[com.ansvia.digaku.model.Endorsement.Kind]]
     * @return
     */
    def create(label:String, kind:Int):Endorsement = {

        // check is already exists
        if (isExists(label, kind))
            throw AlreadyExistsException("Label already exists with name " + label)

        // check is valid label name
        if (!isValidLabel(label))
            throw InvalidParameterException("Label name not valid: " + label)

        val v = Endorsement(label).save()

        val ed = addToRoot(v)

        ed.setProperty("kind", kind)

        db.commit()

        v.toCC[Endorsement].getOrElse {
            throw new DigakuException("Cannot create Endorsement record")
        }

    }

    /**
     * Get endorsement by it label.
     * @param label endorsement label name.
     * @param kind endorsement kind, @see [[com.ansvia.digaku.model.Endorsement.Kind]]
     * @return
     */
    def getByLabel(label:String, kind:Int) = {
        rootVertex.pipe.outE(rootVertexLabel)
            .has("kind", kind)
            .inV()
            .has("endorsement.label-lower", label.toLowerCase)
            .headOption.flatMap(_.toCC[Endorsement])
    }

    /**
     * Check is endorsement label already exists.
     * @param label endorsement label name.
     * @param kind endorsement kind, @see [[com.ansvia.digaku.model.Endorsement.Kind]]
     * @return
     */
    def isExists(label:String, kind:Int) = {
//        db.getVertices("endorsement.label-lower", label.trim.toLowerCase).headOption.isDefined
        getByLabel(label, kind).isDefined
    }

    /**
     * Check is endorsement label valid
     * @param label endorsement label name
     * @return
     */
    def isValidLabel(label:String) = {
        ENDORSEMENT_RE.pattern.matcher(label).matches()
    }

    /**
     * Endorse user
     * @param endorser user who's endorse.
     * @param endorsement endorsement object.
     * @param target target who will receive endorsement.
     * @param accepted when true then user automatically accept the endorsement
     *                 used for unittesting purpose.
     */
    def endorse(endorser:User, endorsement:Endorsement, target:Endorsable, accepted:Boolean=false) = {

        // check barangkali user dah endorse
        if (target.hasEndorser(endorser, endorsement))
            throw AlreadyExistsException("Already endorsed")

        // max endorsements == 20 per user
        if (target.getVertex.pipe.out(ENDORSEMENT).dedup().iterator().length >= 20)
            throw LimitationReachedException("Max 20 endorsement per user")

//        val ed = target --> ENDORSEMENT --> endorsement <()
        val ed = target.getVertex.addEdge(ENDORSEMENT, endorsement.getVertex)

        ed.setProperty("accepted", accepted)
        ed.setProperty("endorserId", endorser.getId)
        ed.setProperty("endorserName", endorser.name)
        ed.setProperty("endorsementId", endorsement.getId)

        db.commit()

        if (!accepted) {
            Digaku.engine.eventStream emit EndorseEvent(endorser, endorsement, target)
        }
    }

    /**
     * Get endorsement list
     * @param offset starting offset.
     * @param limit ends limit.
     * @param kind @see [[com.ansvia.digaku.model.Endorsement.Kind]]
     * @param m manifest.
     * @return
     */
    def getList(offset: Int, limit: Int, kind:Int)(implicit m: Manifest[Endorsement]): Iterator[Endorsement] = {
        rootVertex.pipe.outE(rootVertexLabel)
            .has("kind", kind)
            .inV()
            .range(offset, (offset + limit) - 1)
            .iterator()
            .flatMap(_.toCC[Endorsement])
    }

    /**
     * Untuk mendapatkan popular endorsement (a.k.a group tags)
     * story: https://mindtalk.atlassian.net/browse/MM-78
     * @param kind endorsement kind [[com.ansvia.digaku.model.Endorsement.Kind]]
     * @param offset starting offset.
     * @param limit ends limit.
     * @return
     */
    def getPopularEndorsements(kind:Int, offset:Int, limit:Int) = {
        rootVertex.pipe.outE(rootVertexLabel)
            .has("kind", kind)
            .inV()
            .in(ENDORSEMENT).range(0, 1000) // user or group
            .out(ENDORSEMENT)
            .groupCount.cap()
            .orderMap(T.decr)
            .range(offset, offset + limit - 1)
            .asInstanceOf[GremPipeVertex]
            .iterator().flatMap(_.toCC[Endorsement])
    }

    /**
     * Mendapatkan siapa saja (user/group) yang diendorse berdasarkan label-nya
     * @param offset
     * @param limit
     * @param kind endorsement kind [[com.ansvia.digaku.model.Endorsement.Kind]]
     * @param label
     * @return
     */
    def getEndorsableObjectByLabel(kind:Int, label:String, offset:Int, limit:Int) = {
        rootVertex.pipe.outE(rootVertexLabel)
            .has("kind", kind)
            .inV()
            .has("endorsement.label-lower", label.toLowerCase)
            .in(ENDORSEMENT)
            .dedup()
            .range(offset, offset + limit)
            .iterator()
            .flatMap(_.toCC[Endorsable])
    }


    /**
     * Untuk pencarian endorsements berdasarkan endorsement label name
     * @param query search query
     * @param offset starting offset
     * @param limit ends limit
     * @param kind endorsement kind [[com.ansvia.digaku.model.Endorsement.Kind]]
     * @param m manifest
     * @return
     */
    def searchEndorsements(query:String, offset: Int, limit: Int, kind:Int)(implicit m: Manifest[Endorsement]): Iterator[Endorsement] =  {
        rootVertex.pipe.outE(rootVertexLabel)
            .has("kind", kind)
            .inV()
            .filter { (v:Vertex) =>
                v.getOrElse("endorsement.label-lower", "").contains(query.toLowerCase)
            }
            .range(offset, offset + limit)
            .iterator()
            .flatMap(_.toCC[Endorsement])
    }

    /**
     * Mengembalikan jumlah endorsement berdasarkan query
     * @param query search query
     * @param kind endorsement kind [[com.ansvia.digaku.model.Endorsement.Kind]]
     * @return
     */
    def getEndorsementCount(query:String, kind:Int, blocked:Boolean): Int = {
        rootVertex.pipe.outE(rootVertexLabel)
            .has("kind", kind)
            .inV()
            .filter { (v:Vertex) =>
                v.getOrElse("endorsement.label-lower", "").contains(query.toLowerCase) &&
                    v.getOrElse("blocked", false).equals(blocked)
            }
            .count().toInt
    }

}
