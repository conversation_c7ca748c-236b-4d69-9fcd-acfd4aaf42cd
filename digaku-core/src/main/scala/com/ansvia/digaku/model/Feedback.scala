///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//
//import com.tinkerpop.blueprints._
//import com.ansvia.digaku.dao.DaoBase
//import com.tinkerpop.gremlin.java.GremlinPipeline
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.exc._
//import com.ansvia.digaku.model.Label.FEEDBACK
//import org.ocpsoft.prettytime.PrettyTime
//import java.util.Date
//import com.ansvia.digaku.utils.RichDate._
//import scala.collection.JavaConversions._
//import com.ansvia.graph.BlueprintsWrapper._
//
///**
// * Author: temon
// *
// * Untuk feedback ke mindtalk
// * dari user register/ user anonim
// * bisa kasi feedback
// *
// */
//
//case class Feedback (email:String, content:String, rate:Int)
//    extends BaseModel[IDType] with Deletable with DbObject
//    with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    /**
//     * Get pretty printed creation time age
//     * relative to current time.
//     * ex: 3 hours ago.
//     * @return
//     */
//    def getCreationAge:String = {
//        new PrettyTime(new Date()).format(new Date(creationTime))
//    }
//
//    def getCreationTimeStd:String = {
//        new Date(creationTime).toStdFormat
//    }
//
//    /**
//     * Get linked user by this code.
//     */
//    lazy val pairedUser:Option[User] = {
//        getVertex.pipe.in(FEEDBACK).headOption.flatMap(_.toCC[User])
//    }
//}
//
//
//object Feedback extends DaoBase[GraphType, Feedback] with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//    import scala.collection.JavaConversions._
//
//
//    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.FeedbackRootVertex"
//
//    /**
//     * Create Feedback
//     *
//     * @param email
//     * @param rate
//     * @param content
//     * @return
//     */
//    def create(email:String, content:String, rate:Int):Feedback = {
//
//        var feedback:Feedback = null
//
//        val _feedback = Feedback(email, content, rate)
//        feedback = _feedback.save().toCC[Feedback]
//            .getOrElse(throw new DigakuException("cannot create Feedback"))
//
//        User.getByEmailLogin(email).map { user =>
//            user --> FEEDBACK --> feedback
//        }
//
//        addToRoot(feedback.getVertex)
//
//        db.commit()
//
//        feedback
//    }
//
//    def getByEmail(email:String) = {
//        new GremlinPipeline[Vertex, Vertex](db.getVertices("_class_", "com.ansvia.digaku.model.Feedback"))
//            .has("email", email)
//            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
//            .headOption.flatMap(_.toCC[Feedback])
//    }
//}