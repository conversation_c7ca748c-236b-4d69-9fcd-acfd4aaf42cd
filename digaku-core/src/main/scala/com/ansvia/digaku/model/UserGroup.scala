/*
 * Copyright (c) 2013-2017 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.exc.{DigakuException, InvalidParameterException, PermissionDeniedException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.validator.UserGroupValidator
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.graph.IdGraphTitanDbWrapper._
import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.{Edge, Vertex}
import Label._
import com.ansvia.graph.gremlin._

import scala.collection.JavaConversions._
import scala.collection.mutable.ListBuffer

/**
 * Author: nadir (<EMAIL>)
 *
 * Model untuk User Group.
 * Digunakan untuk mengelompokan beberapa user ke dalam sebuah group,
 * model ini digunakan untuk mendukung fitur yang memungkinkan user untuk melakukan mention
 * terhadap beberapa user sekaligus, dalam satu group.
 *
 * ref: BCA-789
 *
 * @param name Nama user group
 */
case class UserGroup(var name:String) extends BaseModel[IDType] with DbAccess with GroupableObject {
    @Persistent var logo:String = ""
    @Persistent var description:String = ""

    lazy val userGroupSeqStore = Digaku.engine.seqStoreProvider.build("user-group-" + this.getId, ()=>
        Digaku.engine.idFactory.createId().asInstanceOf[Long])

//    val userPostAsGroupKey =

    lazy val userPostAsGroupSeqStore = Digaku.engine.seqStoreProvider.build("post-as-user-group-" + this.getId, () =>
        Digaku.engine.idFactory.createId().asInstanceOf[Long])

    val abilityPostGroupStr = "post-group"

    /**
     * Get lower name of user group
     * @return
     */
    def getLowerName = name.toLowerCase

    /**
     * Add members to user group.
     * @param user
     */
    def addMember(user: User): Unit = {

        val ed = user.getVertex.addEdge(USER_GROUP_MEMBER, this.getVertex)

        ed.setProperty("sourceId", user.getId)
        ed.setProperty("targetId", this.getId)
        ed.setProperty("timeOrder", Digaku.engine.dateUtils.nowMilis)

        db.commit()

        userGroupSeqStore.insert(user.getId, user.getName)
    }

    /**
      * Menambahkan ability user untuk bisa post
      * sebagai user group atau tidak
      * @param user user target
      * @param hasAbility set true jika user ingin bisa post sebagai user group
      */
    def setAbilityPostAsGroup(user:User, hasAbility:Boolean): Unit = {
        user.getVertex.pipe.outE(USER_GROUP_MEMBER)
            .has("targetId", this.getId).iterator().asInstanceOf[GremPipeEdge].sideEffect { (ed:Edge) =>
                ed.setProperty("postAsGroup", hasAbility)
                db.commit()
            }.iterate()

        if (hasAbility) {
            userPostAsGroupSeqStore.insert(user.getId, "")
        } else {
            userPostAsGroupSeqStore.delete(user.getId)
        }
    }

    /**
      * Check user apakah bisa post sebagai user group pada group ini
      * @param user target user
      * @return
      */
    def isCanPostAsGroup(user:User): Boolean ={
        userPostAsGroupSeqStore.get(user.getId).isDefined
    }

    /**
      * get semua id untuk user yang bisa post sebagai user group pada group ini.
      * @return
      */
    def getUserCanPostAsGroup(): Iterator[Long] = {
        Digaku.engine.seqStoreProvider.csh.usingQuery { q =>
            val columns = q.getKey("post-as-user-group-" + this.getId).execute().getResult

            var rv = new ListBuffer[Long]
            for ( i <- 0 to columns.size()-1 ){
                val col = columns.getColumnByIndex(i)
                rv += col.getName
            }

            rv.toIterator
        }
    }

    /**
     * Check user is member ?
     * @param user
     * @return
     */
    def isMember(user: User):Boolean = {
        userGroupSeqStore.get(user.getId).isDefined
    }

    /**
     * Delete member
     * @param user
     */
    def deleteMember(user:User): Unit = {

        user.getVertex.pipe.outE(USER_GROUP_MEMBER)
            .has("targetId", this.getId)
            .remove()

        db.commit()

        userGroupSeqStore.delete(user.getId)
    }

    /**
     * Get member ids
     * @return Seq[(java.lang.Long, String)]
     */
    def getMemberIds(fromId:Option[java.lang.Long], toId:Option[java.lang.Long], limit:Int):Seq[java.lang.Long] = {
        userGroupSeqStore.getStream(fromId, toId, limit)
            .map(_._1).toSeq
    }

    /**
      * Mendapatkan semua id members
      * @return Seq[Long]
      */
    def getAllMemberIds:Iterator[Long] = {
        Digaku.engine.seqStoreProvider.csh.usingQuery { q =>
            val columns = q.getKey("user-group-" + this.getId).execute().getResult

            var rv = new ListBuffer[Long]
            for ( i <- 0 to columns.size()-1 ){
                val col = columns.getColumnByIndex(i)
                rv += col.getName
            }

            rv.toIterator
        }
    }

    /**
     * get list members
     * @param fromId
     * @param toId
     * @param limit
     * @return
     */
    def getMembers(fromId:Option[Long], toId:Option[Long], limit:Int):Seq[User] = {
        getMemberIds(fromId.map(Long.box), toId.map(Long.box), limit).flatMap(id => User.getById(id))
    }

    /**
     * Get member count.
     * @return
     */
    def getMemberCount():Int = {
        userGroupSeqStore.getCount
    }

    override def __save__(v: Vertex) = {
        if (v.getProperty[String]("user-group.lower-name") != name.toLowerCase) {
            v.removeProperty("user-group.lower-name")
            v.setProperty("user-group.lower-name", name.toLowerCase)
        }

        super.__save__(vertex)
    }

    override def toString = "UserGroup(" + getId + ", " + name.replaceAll("\\W+", " ") + ")"

}

object UserGroup extends DaoBase[GraphType, UserGroup] with Slf4jLogger {
    override val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.UserGroupVertex"

    /**
     * Get user group by lower name
     * @param lowerName
     * @return
     */
    def getByName(lowerName:String): Option[UserGroup] = {
        db.query().has("label", VertexLabels.USER_GROUP).has("user-group.lower-name", lowerName.trim.toLowerCase).vertices().headOption.flatMap(_.toCC[UserGroup])
    }

    /**
     * create user group
     * @param name
     * @param logo
     * @param noTx
     * @return
     */
    def create(name:String, logo:String, description:String, noTx:Boolean = false): UserGroup = {

        if (getByName(name).isDefined) {
            throw InvalidParameterException("Group name already exist.")
        }

        UserGroupValidator.validateName(name)

        if (description.length > 160) {
            throw InvalidParameterException("Description maximum 160 character")
        }

        var newUserGroup:UserGroup = null

        try {
            val ug = UserGroup(name)

            ug.logo = logo
            ug.description = description

            val vertex = ug.saveWithLabel(VertexLabels.USER_GROUP)

            addToRoot(vertex)

            newUserGroup = vertex.toCC[UserGroup].getOrElse {
                throw new DigakuException("Cannot create user: " + name)
            }

            if (!noTx) {
                db.commit()
            }
        } catch {
            case e:Exception =>
                error(s"Cannot create User group with name: $name, logo: $logo")
                e.printStackTrace()
                throw new DigakuException("Cannot create user group " + name + ". Error: " + e.getMessage)
        }

        Digaku.engine.searchEngine.indexUserGroup(newUserGroup)

        newUserGroup

    }

    override def deleteById(id: IDType)(implicit m: Manifest[UserGroup]){

        val v = db.getVertex(id)

        assert(v != null, "vertex is null, cannot delete vertex with id " + id)

        // hapus semua members nya
        v.toCC[UserGroup].foreach { ug =>
            ug.userGroupSeqStore.clear()
            Digaku.engine.searchEngine.deleteUserGroup(ug)
        }

        super.deleteById(id)(m)
    }

}
