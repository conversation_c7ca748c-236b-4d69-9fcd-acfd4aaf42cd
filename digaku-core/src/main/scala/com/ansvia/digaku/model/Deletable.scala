/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.event.impl.{DeleteEventEvent, DeletePictureEvent, DeletePostEvent, _}
import com.ansvia.digaku.exc._
import com.ansvia.digaku.helpers.{DbAccess, RootVertex}
import com.ansvia.digaku.utils.DateUtils
import com.ansvia.graph.AbstractDbObject
import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.Edge
import com.tinkerpop.gremlin.Tokens.T
import scala.collection.JavaConversions._

/**
 * Semua model yang bisa dihapus (deletable)
 * harus diturunkan dari sini.
 */
trait Deletable extends AbstractDbObject with DbAccess {

    @Persistent
    var deleted:Boolean = false

//    /**
//     * see [[com.ansvia.digaku.model.DeletedType]]
//     */
//    @Persistent
//    var deletedType:Int = 0

    // ganti dengan deletedByUserId
//    @Persistent
//    var deletedByUserName = ""

    @Persistent
    var deletedByUserId:Long = 0L

    // timestamp
    @Persistent
    var deletedTime:Long = 0L

    @Persistent
    var deletedReason:String = ""

    /**
     * digunakan untuk delete content sebagai siapa
     * see [[com.ansvia.digaku.model.DeletedRole]]
     */
    @Persistent
    var deletedRole:String = ""

    def isDeleted:Boolean = deleted

//    /**
//     * check apakah content di delete dengan soft delete
//     * @return Boolean
//     */
//    def isSoftDeleted:Boolean = {
//        deletedType == DeletedType.SOFT
//    }

//    /**
//     * check apakah content di delete dengan hard delete
//     * @return Boolean
//     */
//    def isHardDeleted:Boolean = {
//        deletedType == DeletedType.HARD
//    }

    /**
     * digunakan untuk delete content
     * @param deletedRole @see [[com.ansvia.digaku.model.DeletedRole]]
     * @param userDeleting user yang delete
     * @param deletedReason deleted reason
     * @param collected set false ketika di override dan ingin menambahkan property ke dalam collect
     */
    def setDeleted(deletedRole:String, userDeleting:User, deletedReason:String = "", collected:Boolean=true) {

        require(DeletedRole.supported.contains(deletedRole), "deleted role only accept: " +
            DeletedRole.supported.mkString(", "))
        require(userDeleting != null, "user who deleting cannot be null")

        if (deletedReason.trim.isEmpty) {
            throw PermissionDeniedException("Deleted reason can't be empty")
        }

        this.deletedRole = deletedRole
        this.deleted = true
        this.deletedByUserId = userDeleting.getId
        this.deletedTime = Digaku.engine.dateUtils.nowMilis
        this.deletedReason = deletedReason

        this.save()

        db.commit()

        if (collected) {
            Deletable.collect(this.reload())
        }

        this match {
            case article:Article =>
                UserLeaderboard.incrementPoint(article.creator, -20)
                Digaku.engine.eventStream.emit(DeletePostEvent(article.creator , article.getId))
                article.creator.decreaseExperience(10)
            case post:Post =>
                UserLeaderboard.incrementPoint(post.creator, -20)
                Digaku.engine.eventStream.emit(DeletePostEvent(post.creator , post.getId))
//            case event:Event =>
//                Digaku.engine.eventStream.emit(DeleteEventEvent(event.creator , event))
            case picture:Picture =>
                Digaku.engine.eventStream.emit(DeletePictureEvent(picture.creator , picture))
            case group:Forum =>
                Digaku.engine.eventStream.emit(DeleteChannelEvent(group))
            case response:Response =>
                response.getRespondedObject.foreach { rObj =>
                    val rObjCreator = rObj.creator
                    if (rObjCreator.getId != response.creator.getId) {
                        UserLeaderboard.incrementPoint(response.creator, -10)
                        UserLeaderboard.incrementPoint(rObjCreator, -4)
                    }
                }

                Digaku.engine.eventStream.emit(DeleteResponseEvent(userDeleting, response))
            case _ =>
        }

        this match {
            case hc:HasCreator =>
                hc.creator match {
                    case c:Counter =>
                        c.getCounter.decrement("status")
                    case _ =>
                }
            case _ =>
        }
    }

    /**
     * digunakan untuk membatalkan delete content
     */
    def restoreDeleted() {

        this match {
            case post:Post =>
                Digaku.engine.eventStream.emit(RestoreContentEvent(post.creator , post))
//            case event:Event =>
//                Digaku.engine.eventStream.emit(RestoreContentEvent(event.creator , event))
            case picture:Picture =>
                Digaku.engine.eventStream.emit(RestoreContentEvent(picture.creator , picture))
            case group:Forum =>
                Digaku.engine.eventStream.emit(RestoreChannelEvent(group))
            case _ =>

        }

        this.deletedRole = ""
        this.deleted = false
        this.deletedByUserId = 0L
        this.deletedTime = 0L
        this.deletedReason = ""
        this.save()

        try {
            Deletable.restore(this, noTx = true)
        } catch {
            case e:DigakuException =>
        }

        db.commit()

        this match {
            case hc:HasCreator =>
                hc.creator match {
                    case c:Counter =>
                        c.getCounter.increment("status")
                    case _ =>
                }
            case _ =>
        }
    }

}

object Deletable extends DbAccess with RootVertex {

    import com.ansvia.graph.BlueprintsWrapper._


    val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.DeletableRootVertex"
    val rootVertexLabel: String = Label.DAO_LIST

    /**
     * Get deletable object by id
     * @param id id of deletable object.
     * @return
     */
    def getById(id:IDType):Option[Deletable] = {
        val v = db.getVertex(id)
        if (v != null){
            v.toCC[Deletable]
        }else{
            None
        }
    }

    /**
     * Periska apakah suatu object sudah di-koleksi atau belum.
     * @param deletable object yang akan diperiksa.
     * @return
     */
    def isCollected(deletable:Deletable) = {
        rootVertex.pipe.outE(rootVertexLabel)
            .has("targetId", deletable.getVertex.getId).range(0,1).count() > 0
    }


    /**
     * Koleksi deleted object ke root vertex Deletable ini
     * agar mempermudahkan dalam query dan operasi pada deleted object.
     * @param deletable object yang akan di-koleksi.
     * @param noTx apabile true jangan lakukan commit transaction, biasanya
     *             digunakan apabila ini dijalankan di dalam transaction.
     */
    def collect(deletable: Deletable, noTx:Boolean=false) = {

        var edge:Edge = null
        if (!isCollected(deletable)) {
            edge = addToRoot(deletable.getVertex)
            edge.setProperty("collected", true)

            if (!noTx)
                db.commit()
        }

        edge
    }

    /**
     * Keluarkan deletable object dari koleksi deletable root vertex.
     * @param deletable object yang akan dikeluarkan/restore.
     * @param noTx apabile true jangan lakukan commit transaction, biasanya
     *             digunakan apabila ini dijalankan di dalam transaction.
     */
    def restore(deletable: Deletable, noTx:Boolean=false){
        if (!isCollected(deletable))
            throw InvalidParameterException("%s not deleted yet".format(deletable))
        rootVertex.pipe.outE(rootVertexLabel).as("ed")
            .inV().has("id", deletable.getVertex.getId)
            .back("ed").remove()
        if (!noTx)
            db.commit()
    }

    /**
     * Dapatkan daftar object-object yang telah dihapus.
     * @param toTime
     * @param limit
     * @return
     */
    def getDeletedObjects(toTime:Long=Digaku.engine.dateUtils.nowMilis, limit:Int=20) = {
        rootVertex.pipe.outE(rootVertexLabel)
            .has("timeOrder", T.lt, toTime)
            .range(0, limit-1)
            .inV()
            .iterator()
            .flatMap(_.toCC[Deletable])
    }

    /**
     * Mendapatkan daftar object Streamabel yang telah dihapus
     * seperti: SimplePost, Article, Picture, PictureGroup
     * dalam sebuah group
     * @param origin origin dari group
     * @param offset
     * @param limit
     * @return
     */
    def getDeletedStream(origin:Origin[GraphType], offset:Int=0, limit:Int=20) = {
        rootVertex.pipe.outE(rootVertexLabel)
            .has("deletedKind", "Streamable")
            .has("collected", true)
            .has("originId", origin.getId)
            .range(offset, (offset + limit) -1)
            .inV()
            .iterator()
            .flatMap { v =>
                v.getProperty[String]("_class_") match {
                    case "com.ansvia.digaku.model.Article" | "com.ansvia.digaku.model.SimplePost" |
                         "com.ansvia.digaku.model.Deal" |"com.ansvia.digaku.model.Question" =>
                        v.toCC[Post].map(PostStreamObject)
                    case "com.ansvia.digaku.model.Picture" =>
                        v.toCC[Picture].map(PostStreamObject)
//                    case "com.ansvia.digaku.model.Event" =>
//                        v.toCC[Event].map(PostStreamObject)
//                    case "com.ansvia.digaku.model.PictureGroup" =>
//                        v.toCC[PictureGroup].map(PostStreamObject)
                    case x =>
                        throw NotSupportedException("class name " + x + " not supported yet for stream object, " +
                            "please add it first manually in User.getStream pattern matcher")
            }
        }
    }


}
