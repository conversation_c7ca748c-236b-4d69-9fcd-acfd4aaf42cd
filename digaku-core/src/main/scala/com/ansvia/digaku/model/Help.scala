package com.ansvia.digaku.model

import com.ansvia.digaku.Types.{IDType, GraphType}
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.exc.{PermissionDeniedException, DigakuException, InvalidParameterException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.utils.ArrangeSettings
import com.ansvia.graph.BlueprintsWrapper._

/**
 * Author: nadir (<EMAIL>)
 *
 */
case class Help(var title:String, var content:String)  extends BaseModel[IDType]

object Help extends DaoBase[GraphType, Help] {
    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.dao.HelpVertex"

    private lazy val helpListStore = ArrangeSettings.seqStore.build("help-list", () => ArrangeSettings.generateId())

    /**
     * create Help Page
     * @param title title help
     * @param content content help
     * @return
     */
    def create(title:String, content:String): Help = {
        if (getArrange.length >= 15)
            throw PermissionDeniedException("Help Page maksimal 15.")

        if (title.trim.length < 3)
            throw InvalidParameterException("Title minimal 3 dan maksimal 160 karakter")

        val newHelp = Help(title, content).save().toCC[Help]
                .getOrElse(throw new DigakuException("Cannot create Help"))

        addToRoot(newHelp.getVertex)

        db.commit()

        moveToTop(newHelp)

        newHelp
    }

    /**
     * get help pages by arrange
     * @return
     */
    def getArrange:List[Help] = {
        ArrangeSettings.getArrangedList(helpListStore).flatMap(id => getById(id.toLong))
    }

    /**
     * delete help page
     * @param id help page id
     * @param m
     * @return
     */
    override def deleteById(id: IDType)(implicit m:Manifest[Help]){
        ArrangeSettings.removeFromList(helpListStore, id)
        super.deleteById(id)
    }

    def arrangeUp(promoted:Help):List[Help] = {
        ArrangeSettings.arrangeUp(helpListStore, promoted.getId)
        getArrange
    }

    def arrangeDown(promoted:Help):List[Help] = {
        ArrangeSettings.arrangeDown(helpListStore, promoted.getId)
        getArrange
    }

    def moveToTop(promoted:Help):List[Help] = {
        ArrangeSettings.moveToTop(helpListStore, promoted.getId)
        getArrange
    }

    def setArrangedList(objList:List[String]) = {
        ArrangeSettings.setArrangedList(helpListStore, objList)
    }

}

