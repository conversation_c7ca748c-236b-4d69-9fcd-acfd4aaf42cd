/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.util.concurrent.{Callable, TimeUnit}

import com.ansvia.digaku.Types.{GraphType, IDType}
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.helpers.AbstractDbAccess
import com.ansvia.graph.annotation.Persistent
import com.google.common.cache.CacheBuilder

/**
 * Author: robin
 *
 * Base class untuk semua user content yang bisa di-publish,
 * meliputi:
 *
 *      1. Post
 *      2. Picture
 *      3. Event
 *      4. Deal
 */
abstract class PublishableContent extends BaseModel[IDType] with HasOrigin[GraphType]
    with Blockable with Promotable with Deletable with Movable with AbstractHasCreator
    with Lockable with Closable with Viewable[IDType] with AbstractDbAccess with Streamable[IDType]
    with Reportable[IDType] with HasCreator with Counter {

    // @deprecated: for backport compatibility only.
    // berisi old mongodb id.
    @Persistent var moid:String = ""


    /**
     * override this for custom label.
     * @return
     */
    override protected def creatorLabel = Label.PUBLISH_CONTENT

    override lazy val creatorOption: Option[User] = {

//        val id = this.getVertex.getId
//        val v = db.getVertex(id)
//
//        if (v == null)
//            throw new Exception("Cannot get creator for object " + this +
//                " with id " + id + ". Got null when getting current vertex.")
//
//
//        v.pipe.in(creatorLabel).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT)
//            .in(Label.COLLECTION_VERTEX).headOption.flatMap(_.toCC[User])

        PublishableContent.getCreator(this, creatorLabel)
    }


}


object PublishableContent extends DaoBase[GraphType, PublishableContent] {
    val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.PublishableContentRootVertex"

    import com.ansvia.graph.BlueprintsWrapper._

import scala.collection.JavaConversions._

    lazy val creatorCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build[String,Option[User]]()

    def getCreator(hc:HasCreator, creatorLabel:String) = {
        creatorCache.get(hc.getVertex.getId.toString, new Callable[Option[User]]{
            def call(): Option[User] = {
                val id = hc.getVertex.getId
                val v = db.getVertex(id)

                if (v == null)
                    throw new Exception("Cannot get creator for object " + this +
                        " with id " + id + ". Got null when getting current vertex.")


                v.pipe.in(creatorLabel).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT)
                    .in(Label.COLLECTION_VERTEX).headOption.flatMap(_.toCC[User])
            }
        })
    }
}
