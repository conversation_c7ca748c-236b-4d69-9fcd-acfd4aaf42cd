/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Types._
import com.ansvia.graph.annotation.Persistent
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.validator.UrlValidator
import com.ansvia.digaku.exc.DigakuException
import com.tinkerpop.blueprints.Vertex
import com.ansvia.digaku.utils.WebLink


/**
 * Author: robin
 *
 */

/**
 * Model untuk embedded object berjenis link.
 * @param url url link.
 * @param title judul link.
 * @param thumbnail thumbnail dari link, akan berisi kosong ("") apabila tidak ada thumbnail-nya.
 * @param desc deskripsi dari link.
 */
case class EmbeddedLink(url:String, title:String, thumbnail:String, desc:String)
    extends BaseModel[IDType] with Embeddable[IDType] with DbAccess {


    @Persistent var saveToFollow = false

    /**
     * Link kind, bisa salah satu dari:
     *
     *      text
     *      video (apabila link mengarah ke web video seperti Youtube.com)
     *
     * @see [[com.ansvia.digaku.model.LinkKind]]
     * @see [[com.ansvia.digaku.utils.WebLink]]
     */
    @Persistent var kind:Int = LinkKind.NONE

    def normalizedThumbnail = {
        if (thumbnail.length == 0) {
            "https://i.imgur.com/StrOVIm.png"
        } else
            thumbnail
    }

    override def __load__(vertex: Vertex): Unit = {
        super.__load__(vertex)

        // @TODO(robin): hapus backward compatibility ini
        // backward compatibility, aman dihapus setelah maret 2014
        if (kind == LinkKind.NONE){
            kind = WebLink.getLinkKind(url)
            vertex.setProperty("kind", kind)
        }
    }
}

/**
 * Jenis dari link.
 * @see [[com.ansvia.digaku.utils.WebLink]]
 */
object LinkKind {
    val NONE = 0
    val TEXT = 1
    val VIDEO = 2
    val PIC = 3
    val OEMBED = 4
}

/**
 * DAO untuk buat embedded object ber tipe Link.
 */
object EmbeddedLink extends DaoBase[GraphType, EmbeddedLink] with DbAccess {
    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.EmbeddedLinkRootVertex"

    import com.ansvia.graph.BlueprintsWrapper._

    /**
     * Buat embedded link
     * @param url url link
     * @param title judul dari link tersebut
     * @param kind link kind, @see [[com.ansvia.digaku.model.LinkKind]]
     * @param thumbnail thumbnailnya
     * @param desc deskripsi dari link tersebut
     * @param parent parent object-nya @see [[com.ansvia.digaku.model.HasEmbeddedObject]]
     *               (optional)
     * @return
     */
    def create(url:String, title:String, kind:Int, thumbnail:String,
               desc:String, parent:Option[HasEmbeddedObject[IDType]]=None):EmbeddedLink = {

        UrlValidator.validate(url)

        val el = EmbeddedLink(url, title, thumbnail, desc)
        el.kind = kind
        val newEl = el.save().toCC[EmbeddedLink].getOrElse {
            throw new DigakuException("Cannot create EmbeddedLink, persistent error?")
        }
        addToRoot(newEl.getVertex)
        newEl.getVertex.setProperty("kind", kind)

        parent.map { p =>
            p.addEmbeddedObject(newEl, noTx = true)
        }

        db.commit()

        newEl
    }
}
