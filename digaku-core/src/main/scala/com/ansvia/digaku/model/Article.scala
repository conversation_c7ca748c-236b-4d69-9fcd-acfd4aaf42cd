/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.util.Date

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.event.impl.{BlockedEvent, ClosedEvent, CreatePostEvent, UpdateForumEvent}
import com.ansvia.digaku.exc.{UnsupportedException, _}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.stream.StreamBuilder
import com.ansvia.digaku.utils.{ArrangeSettings, ArticleUtil}
import com.ansvia.digaku.utils.RichDate._
import com.ansvia.digaku.validator.PostValidator
import com.ansvia.digaku.{<PERSON><PERSON><PERSON>, Types}
import com.ansvia.graph.AbstractDbObject
import com.ansvia.graph.BlueprintsWrapper._
import com.ansvia.graph.annotation.Persistent
import com.thinkaurelius.titan.core.attribute.Decimal
import com.tinkerpop.blueprints.Direction
import name.fraser.neil.plaintext.DiffMatchPatch
import org.apache.commons.io.FileUtils
import org.ocpsoft.prettytime.PrettyTime
import com.ansvia.digaku.utils.ForumSettings._
import com.ansvia.digaku.database.GraphCompat._

import scala.collection.JavaConversions._

case class Article(var title:String, var content:String, _tags:String="")
    extends Post(content) with ContentVersioning
    with HasEmbeddedObject[IDType] with DbAccess with FileAttachable {


    /**
     * @see [[com.ansvia.digaku.model.PostKind]]
     */
    val kind:Int = PostKind.ARTICLE

    @Persistent var thumbUrl = ""
    @Persistent var editReason = ""
    @Persistent var lastEditTime = 0L
    @Persistent var lastEditorUserId = 0L
    @Persistent var editable = true

    // Menandai bahwa article sudah pernah ditampilkan sebagai popular
    @Persistent var popularized = false

    this.tags = _tags


    // di-wrap menggunakan `contentLabel` karena word `label` telah reserved di db Titan
    lazy val label = getVertex.getOrElse("contentLabel", "")

    lazy val lastEditor:Option[User] = {
        User.getById(lastEditorUserId)
    }

    lazy val textMode = getVertex.getOrElse("textMode", "textile")


    /**
     * Get pretty printed edit time age
     * relative to current time.
     * ex: 3 hours ago.
     * @return
     */
    def getLastEditTimeAge:String = {
        new PrettyTime(new Date()).format(new Date(lastEditTime))
    }

    def getLastEditTimeStd:String = {
        new Date(lastEditTime).toStdFormat
    }

    def isValid:Boolean = origin != null && creator != null

    override def toString = "Article(" + getId + ", " + title.replaceAll("\\W+", " ") + ")"


    /**
     * Untuk memeriksa apakah article ini memeliki polling
     * di dalamnya?
     * @return
     */
    def hasPolling = {
        tx { t =>
            t.getVertex(getId).pipe.out(EMBED).has("_class_", "com.ansvia.digaku.model.Polling").hasNext
        }
    }

    /**
     * Dapatkan polling apabila ada
     * @see [[com.ansvia.digaku.model.Article#hasPolling]]
     * @return
     */
    def getPolls = getVertex.pipe.out(EMBED).iterator().flatMap(_.toCC[Polling]).toSeq


    /**
     * Untuk memeriksa apakah artikel ini memiliki event didalamnya
     * @return
     */
    def hasEvent = {
        tx { t =>
            t.getVertex(getId).pipe.out(EMBED).has("_class_", "com.ansvia.digaku.model.Event").hasNext
        }
    }

    /**
     * Untuk mendapatkan event jika terdapat event dalam artikel
     * @return
     */
    def getEvents = getVertex.pipe.out(EMBED).iterator().flatMap(_.toCC[Event]).toSeq

    override def addEmbeddedObject(obj: Embeddable[Types.IDType], noTx: Boolean) {
        if (this.embeddedObjectCount > 20)
            throw UnsupportedException("Max 20 embedded objects")
        reload()
        super.addEmbeddedObject(obj, noTx)
    }

    /**
     * override untuk set block
     * @param block block state true or false (true means block otherwise unblock).
     * @param blocker user who block/unblock this object.
     * @return
     */
    override def setBlocked(block:Boolean, blocker:User) = {

        super.setBlocked(block, blocker)
        this.save()
        db.commit()

        Digaku.engine.eventStream.emit(BlockedEvent(this))

        this

    }

    /**
     * Digunakan untuk set closed article dan akan diindex oleh digaku search engine
     * @param close close state true or false (true means close otherwise open).
     * @param closer user who close/open this object.
     * @param reason reason why you closed this object.
     * @return
     */
    override def setClosed(close:Boolean, closer:User, reason:String) = {

        super.setClosed(close, closer, reason)
        this.save()
        db.commit()

        Digaku.engine.eventStream.emit(ClosedEvent(this))

        this

    }

    /**
     * Add reply to this thread.
     * Locked thread cannot be replied.
     *
     * @see [[com.ansvia.digaku.model.Responable.addResponse(Response)]]
     * @param creator user who's create/write the response.
     * @param content response content.
     */
    override def addResponse(creator: User, content: String):Response = {
        if (this.isLocked) {
            throw PermissionDeniedException("Cannot reply locked thread")
        }

        if (this.isClosed) {
            throw PermissionDeniedException("Cannot reply closed thread")
        }

        val origin = this.origin match {
            case forum:Forum => forum
            case _ => throw PermissionDeniedException("Cannot create post outside a forum origin")
        }

        if (origin.getPrivated && !origin.isMember(creator) &&
            UserRole.ADMIN != creator.role && UserRole.SUPER_ADMIN != creator.role) {
            throw PermissionDeniedException("Only member can post inside closed forum")
        }

        if (content.trim.length < 3 || content.trim.length > 500000) {
            throw InvalidParameterException("Minimum post length is 3 chars and maximum 500000 chars")
        }

        super.addResponse(creator, content)
    }

    /**
     * Overriding setMoved method to add more rule
     * Cannot move a post when the post is locked.
     *
     * @param movedType see [[com.ansvia.digaku.model.MovedType]]
     * @param movedRole see [[com.ansvia.digaku.model.MovedRole]]
     * @param mover user yang move
     * @param channelTargetId
     * @param reason alasan pemindahan
     */
    override def setMoved(movedType:Int ,movedRole:String, mover:User, channelTargetId:IDType, reason:String) = {
        if (this.isLocked) {
            throw PermissionDeniedException("Cannot move a locked thread")
        }

        val forumTarget = Forum.getById(channelTargetId).getOrElse {
            throw NotExistsException("Forum not exist with id " + channelTargetId)
        }

        if(!forumTarget.hasFeature(ForumFeatures.ARTICLE))
            throw PermissionDeniedException("Target forum not supported Article feature")

        val forumOrigin = Forum.getById(origin.getId).getOrElse {
            throw NotExistsException("Forum not exist with id " + origin.getId)
        }

        this.setSticky(false)

        this.reload()

        origin match {
            case ch:Forum =>
                ch.decrementArticleCount()
                ch.decrementContentCount()
                Digaku.engine.eventStream.emit(UpdateForumEvent(ch))
            case _ =>
        }

        // Tambah counter di forum tujuan
        forumTarget.incrementArticleCount()
        forumTarget.incrementContentCount()
        Digaku.engine.eventStream.emit(UpdateForumEvent(forumTarget))

        super.setMoved(movedType, movedRole, mover, channelTargetId, reason)

        if (isClosed) {
            //saat closed seharusnya tidak dapat menambahkan response
            //penambahan response dari sistem adalah pengecualian
            this.closed = false
            if (reason.isEmpty) {
                this.addResponse(mover, s"moved from #${forumOrigin.name} to #${forumTarget.name}")
            } else {
                this.addResponse(mover, s"moved from #${forumOrigin.name} to #${forumTarget.name} because $reason")
            }
            this.closed = true
        } else {
            if (reason.isEmpty) {
                this.addResponse(mover, s"moved from #${forumOrigin.name} to #${forumTarget.name}")
            } else {
                this.addResponse(mover, s"moved from #${forumOrigin.name} to #${forumTarget.name} because $reason")
            }
        }

    }

    //// conflict dengan punyanya fajr
//    /**
//     * Add reply to this thread.
//     * Locked thread cannot be replied.
//     *
//     * @see [[com.ansvia.digaku.model.Responable.addResponse(Response)]]
//     * @param creator user who's create/write the response.
//     * @param content response content.
//     */
//    override def addResponse(creator: User, content: String):Response = {
//        if (this.isLocked) {
//            throw PermissionDeniedException("Cannot reply locked thread")
//        } else {
//            super.addResponse(creator, content)
//        }
//    }
    //// ---eof---

    /**
     * Update article content.
     * @param newTitle
     * @param rawContent
     * @param newEditReason
     * @param user editor.
     */
    def update(newTitle:String, rawContent:String, htmlContent:String,
               newEditReason:String, user:User, textMode:String) = {

        if (this.isLocked) {
            throw PermissionDeniedException("Cannot edit a locked thread")
        }

        if(rawContent.trim.length > 100000 || rawContent.trim.length<3)
            throw PermissionDeniedException("Min content 3 and max content 100000 characters")

        if(!PostValidator.validArticleTitle(newTitle))
            throw InvalidParameterException("Min title 3 and max title 100 characters")

//        transact {

            reload()

//            val revision = incRevision()

            val now = Digaku.engine.dateUtils.nowMilis

//            val arhV = ArticleRevisionHistory(revision, newTitle,
//                rawContent, now, newEditReason).save()

//            val ed = this --> REVISION --> arhV <()
//
//            ed.setProperty("timeOrder", System.currentTimeMillis())
//            ed.setProperty("lastEditorUserId", user.getId)
//            ed.setProperty("lastEditTime", lastEditTime)
//
//            arhV.setProperty("lastEditorUserId", user.getId)

            title = newTitle
            content = rawContent
            editReason = newEditReason
            lastEditTime = now
            lastEditorUserId = user.getId

            val v = getVertex
            v.setProperty("title", title)
            v.setProperty("content", content)
//            v.setProperty("revision", revision)
            v.setProperty("editReason", editReason)
            v.setProperty("lastEditTime", lastEditTime)
            v.setProperty("lastEditorUserId", lastEditorUserId)
            v.setProperty("textMode", textMode)

            textMode match {
                case "html" =>
                    v.setProperty("contentHtml", htmlContent)
                case _ =>
            }



//        }
        db.commit()

//        arhV.toCC[ArticleRevisionHistory].getOrElse {
//            throw new DigakuException("Cannot create ArticleRevisionHistory, persistence error?")
//        }
    }


//    /**
//     * Balikin konten ke revisi n
//     * @param revNumber nomor revisi yang akan dibalikkan.
//     * @return
//     */
//    def revertToRevision(revNumber:Long) = {
//
//
//        this.getVertex.pipe.out(REVISION)
//            .has("revision", revNumber)
//            .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
//            .headOption.map { rhV =>
//                val v = getVertex
//                v.setProperty("title", rhV.getOrElse("title", title))
//                v.setProperty("content", rhV.getOrElse("content", content))
////                v.setProperty("revision", rhV.getOrElse("revision", revision))
//                v.setProperty("editReason", rhV.getOrElse("editReason", editReason))
//
//                db.commit()
//            }
//
//    }


//
    /**
     * Mendapatkan daftar perubahan pada artikel.
     * Diurutkan dari revisi yang terbaru.
     * @param offset offset mulai.
     * @param limit batas jumlah maksimal.
     * @return
     */
    def getRevisionHistories(offset:Int, limit:Int) = {

        reload()
        this.getVertex.pipe.out(REVISION)
            .range(offset, offset + limit - 1)
            .iterator()
            .flatMap(_.toCC[ArticleRevisionHistory])

    }
//
    def getRevisionHistory(revNum:Long) = {
        getVertex.pipe.out(REVISION)
            .has("revision", revNum)
            .asInstanceOf[GremPipeVertex]
            .headOption
            .flatMap(_.toCC[ArticleRevisionHistory])
    }

//    /**
//     * Mendapatkan latest revision dari edges REVISION-nya.
//     * @return
//     */
//    override def latestRevision = {
//        getVertex.pipe.out(REVISION).range(0,1)
//            .headOption.map(_.getOrElse("revision", revision))
//            .getOrElse(revision)
//    }

    def shortDesc = {
        ArticleUtil.shortDesc(content)
    }

    /**
     * Untuk menghapus article, ini juga akan memasukkan deleted article ke collection
     * by default, untuk membuat tidak masuk ke collection set parameter collected=false.
     * @see [[com.ansvia.digaku.model.Deletable.collect(com.ansvia.digaku.model.Deletable, boolean)]]
     * @param deletedRole @see [[com.ansvia.digaku.model.DeletedRole]]
     * @param userDeleting user yang delete
     * @param deletedReason deleted reason
     * @param collected set false ketika di override dan ingin menambahkan property ke dalam collection.
     */
    override def setDeleted(deletedRole:String, userDeleting:User, deletedReason:String = "",
                            collected:Boolean = true) {

        val isStaff = origin match {
            case ch:Forum =>
                ch.isStaff(userDeleting)
            case _ =>
                false
        }

        if (!(userDeleting == creator || isStaff || userDeleting.role == UserRole.ADMIN || userDeleting.role == UserRole.SUPER_ADMIN))
            throw PermissionDeniedException("Only creator, moderator or admin can delete.")

        this.setSticky(false, false)

        this.reload()

        this.creator.decrementArticleCount()

        this.getMonitorists(0, this.getMonitoristCount()).foreach{
            u => this.userUnMonitor(u)
        }

        origin match {
            case ch:Forum =>
                ch.decrementArticleCount()
                ch.decrementContentCount()
                Digaku.engine.eventStream.emit(UpdateForumEvent(ch))
            case _ =>
        }

        super.setDeleted(deletedRole, userDeleting, deletedReason, collected)

        if (collected) {
            Deletable.rootVertex.pipe.outE(Deletable.rootVertexLabel)
                .has("targetId", this.getVertex.getId)
                .asInstanceOf[GremPipeEdge].headOption.foreach { edge =>

                edge.setProperty("deletedObject", "Article")
                edge.setProperty("deletedKind", "Streamable")
                edge.setProperty("originId", this.origin.getId)

                db.commit()
            }
        }

    }

    def getContentFormat(fmt:String) = {
        fmt match {
            case "anstile" | "textile" => content
            case "html" => getVertex.getOrElse("contentHtml", "&lt;html content&gt;")
        }
    }

    /**
     * Get rating average
     * @return
     */
    def getRatingAverage:Double = {
        if (this.getLikesCount > 0) {
            val average = getRating.toFloat / this.getLikesCount
            "%.1f".format(average).toDouble
        } else {
            0.0
        }
    }

    /**
     * Get article rating.
     * @return
     */
    def getRating:Int = {
        getCounter.get("rating").toInt
    }

    /**
     * Increment article rating
     * @param by increment by, default is 1.
     */
    def incrementRating(by:Int=1){
        getCounter.incrementBy("rating", by)
    }

    def decrementRating(by:Int){
        getCounter.decrementBy("rating", by)
    }

    /**
     * get article popular headline
     * @return
     */
    def getPopularHeadLine = {
        getVertex.pipe.inE(POPULAR).headOption
            .flatMap { edge =>
                edge.get[String]("headLine")
            }.getOrElse("")
    }

}




/**
 * trait untuk membuat suatu model bisa di-attach file.
 * Semua yang implement trait ini bisa di-attach file.
 */
trait FileAttachable  {

    this: AbstractDbObject with HasEmbeddedObject[IDType] with DbAccess =>

    def attachedFiles = getEmbeddedObjects.filter(_.isInstanceOf[EmbeddedFile]).map(_.asInstanceOf[EmbeddedFile])

    def removeAttachedFile(ef:EmbeddedFile){
        removeEmbeddedObject(ef)
    }

}

/**
 * Embedded object untuk file.
 * @see [[com.ansvia.digaku.model.FileAttachable]]
 * @param name file name.
 * @param url file url (download url).
 * @param md5 file hash.
 * @param size file size.
 */
case class EmbeddedFile(name:String, url:String, md5:String, size:Long)
    extends BaseModel[IDType] with Embeddable[IDType] with DbAccess {
    assert(size > 0, "file size is zero")

    def prettySize = FileUtils.byteCountToDisplaySize(size)
}

/**
 * Implementasi dari histori revisi pada konten artikel.
 * @param revision nomor revisi.
 * @param title judul yang baru.
 * @param content konten yang baru.
 * @param timestamp waktu perubahan.
 * @param message pesan perubahan.
 */
case class ArticleRevisionHistory(revision:Long, title:String,
                                  content:String, timestamp:Long,
                                     message:String) extends BaseModel[IDType]{

    import com.ansvia.digaku.model.ArticleRevisionHistory._

    case class DiffOutput(title:String, content:String)

    /**
     * Get editor / revision creator.
     * @return
     */
    def editor = {
        val editorUserId = getVertex.getOrElse("lastEditorUserId", 0L)
        if (editorUserId != 0L){
            User.getById(editorUserId)
        }else
            None
    }

    def compareWith(rh:ArticleRevisionHistory) = {
        val dft = differ.diff_main(rh.title, title)
        val dfc = differ.diff_main(rh.content, content)

        differ.diff_cleanupSemantic(dft)
        differ.diff_cleanupSemantic(dfc)

//        println("differ.diff_levenshtein(dfc): " + differ.diff_levenshtein(dfc))

        (dft, dfc)
    }

    def diffToHtml(rh:ArticleRevisionHistory):DiffOutput = {
        val diff = compareWith(rh)
        DiffOutput(differ.diff_prettyHtml(diff._1),
            differ.diff_prettyHtml(diff._2))
    }

    def diffToHtmlPatches(rh:ArticleRevisionHistory) = {
        val diff = differ.diff_main(rh.content, content)
        differ.diff_cleanupSemantic(diff)
        differ.patch_make(diff)
    }

}

object ArticleRevisionHistory {
    protected lazy val differ = new DiffMatchPatch()
}

object FileAttachable {
    val allowedMimeTypes = Map(
        "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "docm" -> "application/vnd.ms-word.document.macroEnabled.12",
        "dotm" -> "application/vnd.ms-word.template.macroEnabled.12",
        "doc" -> "application/msword",
        "dotx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.template",
        "xls" -> "application/vnd.ms-excel",
        "xltx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.template",
        "xlam" -> "application/vnd.ms-excel.addin.macroEnabled.12",
        "xlsb" -> "application/vnd.ms-excel.sheet.binary.macroEnabled.12",
        "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "xlsm" -> "application/vnd.ms-excel.sheet.macroEnabled.12",
        "xltm" -> "application/vnd.ms-excel.template.macroEnabled.12",
        "pptx" -> "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "pptm" -> "application/vnd.ms-powerpoint.presentation.macroEnabled.12",
        "ppt" -> "application/vnd.ms-powerpoint",
        "potx" -> "application/vnd.openxmlformats-officedocument.presentationml.template",
        "ppsx" -> "application/vnd.openxmlformats-officedocument.presentationml.slideshow",
        "ppam" -> "application/vnd.ms-powerpoint.addin.macroEnabled.12",
        "potm" -> "application/vnd.ms-powerpoint.template.macroEnabled.12",
        "ppsm" -> "application/vnd.ms-powerpoint.slideshow.macroEnabled.12",
        "jpg" -> "image/pjpeg",
        "jpeg" -> "image/jpeg",
        "png" -> "image/png",
        "gif" -> "image/gif",
        "bmp" -> "image/bmp",
        "pdf" -> "application/pdf",
        "txt" -> "text/plain",
        "odt" -> "application/vnd.oasis.opendocument.text",
        "odp" -> "application/vnd.oasis.opendocument.presentation",
        "ods" -> "application/vnd.oasis.opendocument.spreadsheet"
    )

    val allowedFileTypes = allowedMimeTypes.keys.toList
    val allowedFileTypeGroups = Map(
        "image" -> allowedMimeTypes.filter(_._2.startsWith("image")).keys.toList, // Image family
        "microsoft_office" -> allowedFileTypes.filter("do.+|xl.+|pp.+".r.pattern.matcher(_).matches()), // Microsoft office file types
        "libre_office" -> allowedFileTypes.filter(_.startsWith("od")), // Libre office file types
        "adobe_acrobat" -> List("pdf"),
        "text" -> List("txt")
    )

    def isValid(groupFileType:String, mimeType:String) = {
        allowedFileTypeGroups(groupFileType).map(allowedMimeTypes).contains(mimeType)
    }
}

object Article extends DaoBase[GraphType, Article] with DbAccess with Popularable with Slf4jLogger {

    import com.ansvia.graph.BlueprintsWrapper._
    import com.ansvia.graph.IdGraphTitanDbWrapper._

    import scala.collection.JavaConversions._

    val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.ArticleRootVertex"

    val labelRe = """\[([a-z0-9\-\._]+)\]""".r
    private val timeOrderKey = "timeOrder"


    /**
     * create artikel
     * @param creator creator article
     * @param title title article
     * @param content content article
     * @param tags tags article
     * @param origin article origin.
     * @return
     */
    def create(creator: User, title: String, content: String, tags: String,
                      origin: Origin[GraphType], locked:Boolean=false, triggerEvent:Boolean=true): Article = {

        assert(creator != null, "no creator")
        assert(origin != null, "no origin")

        if (creator.isLocked)
            throw PermissionDeniedException("You can not write a thread because it has been suspended")

        origin match {
            case ch:Forum =>
                if (ch.isDeleted) {
                    throw PermissionDeniedException("Can't create thread, subforum has removed")
                }

                if (ch.privated && !ch.isMember(creator)) {
                    throw PermissionDeniedException("you should be a member to create thread in closed subforum")
                }

                if (ch.isBlockedUser(creator)) {
                    throw PermissionDeniedException("cannot create thread, creator is blocked on this forum")
                }
            case _ =>
        }

        if(content.trim.length > 500000 || content.trim.length < 3)
            throw InvalidParameterException("Min content 3 and max content 500000 characters")

        var normTitle = title

        // extract post mark like if any
        // ex: [info] some info here
        //  [info] will removed from title and changed by postmark
        val postMarkO = labelRe.findFirstIn(title) flatMap { lblStr =>
            val rv = PostMark.getByPostMarkTitle(lblStr.replaceAll("[\\[\\]]","").toLowerCase)
            if (rv.isDefined)
                normTitle = title.replace(lblStr, "").trim
            rv
        }

        if(!PostValidator.validArticleTitle(normTitle))
            throw InvalidParameterException("Min title 3 and max title 160 characters")


        //        var rvPost:Option[Article] = None

        //        transact {

        creator.reload()
        origin.reload()

        // normalize tags, harus unik, lower case, min 2 karakter, max 25 karakter,
        // dan maks 5 tag.
        val normTags = tags.split(",").map(_.toLowerCase.trim)
            .filter(t => t.length >= 3 && t.length <= 25)
            .distinct.slice(0, 5)
            .mkString(",")

        val post = Article(normTitle, content, normTags)
            .saveWithLabel(VertexLabels.CONTENT)
            .toCC[Article].getOrElse {

            throw new DigakuException("Cannot create article")
        }

        post.reload()

        val rootEdge = addToRoot(post.getVertex)
        rootEdge.setProperty("kind", PostKind.ARTICLE)

        val v = post.getVertex

        v.setProperty("locked", locked)

        //            val creatorV = db.getVertex(creator.getId)
        val postV = db.getVertex(post.getId)
        val originV = db.getVertex(origin.getId)

        //            creatorV.setProperty("publishedContentCount", creatorV.getOrElse("publishedContentCount", 0) + 1)

        creator.reload()

        //            val wroteE = creatorV --> WROTE --> postV <()
        val publishE = creator.getYMPart(CollectionVertex.Kind.PUBLISH_CONTENT).addEdge(PUBLISH_CONTENT, postV)
        postV.addEdge(ORIGIN, originV)



        // langsung aja masukkan ke creator-nya dan forum-nya
        // biar langsung muncul dan lebih reliable + realtime dibanding
        // mengandalkan event stream
        StreamBuilder.addToStream(creator, post)


        /**
         * Link-kan stream-nya apabila originnya adalah forum
         */
        //             if (origin.kind == OriginKind.FORUM){
        //                 val streamE = StreamBuilder.addToStream(origin.asInstanceOf[Origin[GraphType] with CollectionVertex],
        //                     post, timeFromCreation=true)
        //
        //                 streamE.setProperty("post.sticky", false)
        //                 streamE.setProperty("locked", false)
        //                 streamE.setProperty("post.kind", PostKind.ARTICLE)
        //                 streamE.setProperty("originId", originV.getId)
        //                 streamE.setProperty("targetId", postV.getId)
        //
        //                 //// udah diset di addToStream jadi aku disable.
        //                 // jumlah content total yang ada di stream forum ini
        // //                originV.setProperty("contentCount", originV.getOrElse("contentCount", 0L) + 1L)
        // //                originV.setProperty("articleCount", originV.getOrElse("articleCount", 0) + 1)
        //
        //             }

        val now = Digaku.engine.dateUtils.nowMilis

        // hanya digunakan untuk ordering
        // untuk dapetin waktu pembuatan gunakan creationTime instead.
        //            wroteE.setProperty("time", Digaku.engine.dateUtils.nowMilis)
        publishE.setProperty(timeOrderKey, now)
        publishE.setProperty("post.kind", PostKind.ARTICLE)
        publishE.setProperty("targetId", post.getId)
        //            wroteE.setProperty("creationTime", Digaku.engine.dateUtils.nowMilis)


        creator.getVertex.setProperty("lastPostTime", now)

        origin match {
            case ch:Forum =>
                val streamE = StreamBuilder.addToStream(origin.asInstanceOf[Origin[GraphType] with CollectionVertex],
                    post, timeFromCreation=true)

                streamE.setProperty("post.sticky", false)
                streamE.setProperty("locked", false)
                streamE.setProperty("post.kind", PostKind.ARTICLE)
                streamE.setProperty("originId", originV.getId)
                streamE.setProperty("targetId", postV.getId)

                postMarkO foreach { pMark =>
                    if (ch.hasPostMark(pMark.title)){
                        post.setMark(pMark, noTx = true)
                    }
                }
            case _ =>
        }

        //        /**
        //         * create first revision.
        //         */
        //        val rhV = ArticleRevisionHistory(0, title, content, now,"").save()
        //
        //        val ed = v --> REVISION --> rhV <()
        //        ed.setProperty("timeOrder", now)

        //            rvPost = Some(post)

        //        }
        db.commit()

        creator.increaseExperience(10)
        creator.incrementArticleCount()
        UserLeaderboard.incrementPoint(creator, 20)


//        // update counter and creator's vertex attributes
//        {
//            val contentCount = creator.incrementPublishedContentCount()
//            val articleCount = creator.incrementArticleCount()
//
//            val v = creator.getVertex
//            v.setProperty("publishedContentCount", contentCount)
//            v.setProperty("publishedArticleCount", articleCount)
//        }


        origin match {
            case ch:Forum =>
                // sudah ada di com.ansvia.digaku.stream.StreamBuilder.addToStreamEx
                ch.incrementArticleCount()
                ch.incrementContentCount()
                Digaku.engine.eventStream.emit(UpdateForumEvent(ch))
            case _ =>
        }

        // ini gak perlu lagi, karena sudah di link-kan di transact sebelum-nya.
        //        rvPost.map { post =>
        //
        //            post.reload()
        //            post.getVertex.pipe.outE(STREAM).iterator().foreach { ed =>
        //                println(" => " + ed)
        //                val v = ed.getVertex(Direction.IN)
        //                println("   " + v.toCC[Post])
        //            }
        //
        //            transact {
        //
        //                /**
        //                 * Link-kan stream-nya apabila originnya adalah forum
        //                 */
        //                post.reload()
        //                origin.reload()
        //
        //                val postV = db.getVertex(post.getId)
        //                val originV = db.getVertex(origin.getId)
        //
        //                if (origin.kind == OriginKind.FORUM){
        //
        ////                    println("1a: " + originV.pipe.outE(STREAM).count())
        //
        //                    val streamE = addToStream(originV, postV)
        //
        //                    streamE.setProperty("post.sticky", false)
        //                    streamE.setProperty("locked", false)
        //                    streamE.setProperty("post.kind", PostKind.ARTICLE)
        //                    streamE.setProperty("originId", originV.getId)
        //                    streamE.setProperty("targetId", postV.getId)
        //                }
        //            }
        //
        //            val originV = db.getVertex(origin.getId)
        ////            println("1b: " + originV.pipe.outE(STREAM).count())
        //        }


        if (triggerEvent){
            Digaku.engine.eventStream.emit(CreatePostEvent(creator, post))
        }

        //        rvPost.get
        post
    }


    /**
     * Get popular articles.
     * @param offset starting offset.
     * @param limit ends limit.
     * @param cached whether return from cache if any, default true.
     * @return
     */
    def getPopularArticles(offset:Int, limit:Int, cached:Boolean): Seq[(Article, Double)] = {
//        if (cached)
//            vertexCache("popular-articles:%d:%d".format(offset, limit))
//        else
            loadPopularArticleInternal(offset, limit)
    }

    /**
     * get All popular articles with arranged
     * @return
     */
    def getAllPopularArticles(offset:Int = 0, limit:Int = 10):Seq[Article] = {
        val arrArticles = getPopularArrangement()
        val arrLength = arrArticles.length

        val popularArtiles = if (arrLength < offset) {
            loadPopularArticleGenerated(offset - arrLength, limit).map(_._1)
        } else {
            if (arrLength >= offset + limit) {
                arrArticles.slice(offset, offset + limit)
            } else {
                arrArticles.slice(offset, offset + limit) ++ loadPopularArticleGenerated(0, offset + limit - arrLength).map(_._1)
            }
        }

        // Tandai article sudah pernah ditampilkan sebagai popular
        // supaya PopularArticleBuilder tidak menambahkan ke popular lagi
        popularArtiles.filterNot(_.popularized).map { article =>
            article.popularized = true
            article.save()
        }

        db.commit()

        popularArtiles
    }

    private def getPopularVertex(offset:Int, limit:Int, kind:String = "all") = {
        val popularV = kind match {
            case "all" => popularRootVertex.pipe.outE(POPULAR)

            case "promoted" => popularRootVertex.pipe.outE(POPULAR).has("promoted")

            case _ => popularRootVertex.pipe.outE(POPULAR).hasNot("promoted")
        }

        popularV.inV()
            .hasNot("blocked", true)
            .hasNot("deleted", true)
            .as("post")
            .out(ORIGIN)
            .has("privated", false)
            .back("post")
            .asInstanceOf[GremPipeVertex]
            .dedup()
            .range(offset, (offset + limit) - 1)
            .iterator.flatMap(_.toCC[Article])
//            .map( art => (art,
//                art.getVertex.getEdges(Direction.IN, POPULAR).head.getOrElse("score", 0)
//            ) ).toSeq
            .map { art =>
                val x = art.getVertex.getEdges(Direction.IN, POPULAR).head.getProperty[Decimal]("score")
                val score = if (x != null) x.floatValue() else 0.0
                (art, score)
            }.toSeq
    }

    //get all popular article
    private def loadPopularArticleInternal(offset:Int, limit:Int) = {
        getPopularVertex(offset, limit)
    }

    //get only promoted popular article
    private def loadPopularArticlePromoted(offset:Int, limit:Int) = {
        getPopularVertex(offset, limit, "promoted")
    }

    //get popular article without promoted
    def loadPopularArticleGenerated(offset:Int, limit:Int) = {
        getPopularVertex(offset, limit, "generated")
    }

    /**
     * digunakan untuk refresh popular article cache
     */
    def refreshPopularArticleCache() {
//        vertexCache.refresh("popular-articles:0:10")
    }

   lazy val popularListStore = ArrangeSettings.seqStore.build("popular-list-store", () => ArrangeSettings.generateId())

    /**
     * get article arrangement based on listStr
     * @return
     */
    def getPopularArrangement():List[Article] = {
        ArrangeSettings.getArrangedList(popularListStore).flatMap { id =>
            val article = Article.getById(id.toLong)

            if (article.isEmpty)
                ArrangeSettings.removeFromList(popularListStore, id.toLong)

            article.flatMap { art =>
                if (art.originIsPrivate || art.isDeleted || art.originIsDeleted)
                    None
                else
                    article
            }
        }
    }

    def getPopularArrangementId():List[String] = {
        ArrangeSettings.getArrangedList(popularListStore).map( x => Article.getById(x.toLong)).map(_.get.getId.toString)
    }

    /**
     * move article up one level on article list based on listStr
     * @param articleId
     * @return
     */
    def arrangePopularUp(articleId:Types.IDType):List[Article] = {
        ArrangeSettings.arrangeUp(popularListStore, articleId)
        getPopularArrangement()
    }

    /**
     * move article down one level on article list based on listStr
     * @param articleId
     * @return
     */
    def arrangeArticleDown(articleId:Types.IDType):List[Article] = {
        ArrangeSettings.arrangeDown(popularListStore, articleId)
        getPopularArrangement()
    }

    /**
     * move article to top on article list based on listStr
     * @param articleId
     * @return
     */
    def moveArticleToTop(articleId:Types.IDType):List[Article] = {
        ArrangeSettings.moveToTop(popularListStore, articleId)
        getPopularArrangement()
    }

    /**
     * move article to bottom in article list based on listStr
     * @param articleId
     * @return
     */
    def moveArticleToBottom(articleId:Types.IDType):List[Article] = {
        ArrangeSettings.moveToBottom(popularListStore, articleId)
        getPopularArrangement()
    }

    /**
     * remove article from list arrangement based on listStr
     * @param articleId
     * @return
     */
    def removeFromArticleList(articleId:Types.IDType):List[Article] = {
        ArrangeSettings.removeFromList(popularListStore, articleId)
        getPopularArrangement()
    }

    def setArrangedList(objList:List[String]) = {
        debug("insert to popular seqstore: " + objList.mkString(", "))
        ArrangeSettings.setArrangedList(popularListStore, objList)
    }


}
