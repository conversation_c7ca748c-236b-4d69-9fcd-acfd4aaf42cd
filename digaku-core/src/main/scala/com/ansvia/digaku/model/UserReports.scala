/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.util.{Calendar, Date}

import com.ansvia.digaku.exc.InvalidParameterException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label._
import com.ansvia.graph.gremlin._
import com.tinkerpop.blueprints.Vertex
import com.tinkerpop.pipes.PipeFunction

import scala.collection.JavaConversions._

/**
 * Author: nadir
 * 
 */

/**
 * digunakan untuk mengkalkulasikan report activity user
 * yang nantinya digunakan untuk trophy
 */
object UserReports extends DbAccess{

    import com.ansvia.graph.BlueprintsWrapper._

//    @Persistent var channelOwnedCount = 0
//    @Persistent var teenPostADayOnWeek = false
//    @Persistent var articleCreationCount = 0
//    @Persistent var postUniqueChannel = 0
//    @Persistent var userStatusCount = 0
//    @Persistent var cupidBombCount = 0
//    @Persistent var userSuperpost = 0
//    @Persistent var UserMentionedCount = 0


    /**
     * mengkalkulasikan jumlah group yang dimiliki user
     * @param v vertex user
     * @return
     */
    def calcChannelOwnerCount(v:Vertex):Int = {

        validateVertexUser(v)

        val channelOwnedCount = v.toCC[User].map(_.getOwnedChannelCount.toInt).getOrElse(0)

        transact {
            v.setProperty("channelOwnedCount", channelOwnedCount)
        }

        channelOwnedCount

    }

    /**
     * mengkalkulasikan jumlah post dalam setiap minggunya
     * ketika user post dengan 10 post perhari dalam tujuh hari
     * akan merubah property vertex user (teenPostADayOnWeek) menjadi true
     * @param v vertex user
     * @return
     */
    def calcTeenPostADayOnWeek(v:Vertex):Boolean = {

        validateVertexUser(v)

        val state = v.getOrElse("teenPostADayOnWeek", false)

        if (!state) {

            val postsV = v.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT).out(PUBLISH_CONTENT)

            val lastCreationTime =
                postsV.headOption.flatMap(_.get[Long]("creationTime")).getOrElse(0L)

            val lastCreationDate = new Date(lastCreationTime)
            val cal = Calendar.getInstance()
            cal.setTime(lastCreationDate)
            // substract 7 days
            cal.add(Calendar.DATE, -7)

            // convert to long
            val fromDate = cal.getTime.getTime

            val map:java.util.Map[_, Number] = new java.util.LinkedHashMap[String, Number]

            postsV.interval("creationTime", fromDate, lastCreationTime)
                .groupCount(map, new PipeFunction[Vertex, java.lang.String]() {
                def compute(vertex:Vertex):java.lang.String = {
                    val creationTime = vertex.getProperty[Long]("creationTime")
                    val a = new Date(creationTime)
                    val cal = Calendar.getInstance()
                    cal.setTime(a)
                    cal.get(Calendar.DATE).toString
                }
            }).cap()
            .iterate()

            val teenPostADayOnWeek = map.filter(_._2.intValue() > 9).size >= 7

            transact {
                v.setProperty("teenPostADayOnWeek", teenPostADayOnWeek)
            }

            teenPostADayOnWeek

        } else {
            state
        }

    }

    /**
     * mengkalkulasikan jumlah article yang dibuat oleh user
     * @param v user vertex
     * @return
     */
    def calcArticleCreationCount(v:Vertex):Int = {

        validateVertexUser(v)

        val articleCreationCount = v.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT)
            .out(PUBLISH_CONTENT).has("_class_", "com.ansvia.digaku.model.Article").count().toInt

        transact {
            v.setProperty("articleCreationCount", articleCreationCount)
        }

        articleCreationCount

    }

    /**
     * mengkalkulasikan jumlah group yang pernah di post oleh user
     * @param v vertex user
     * @return
     */
    def calcPostUniqueChannel(v:Vertex):Int = {

        validateVertexUser(v)

//        v.pipe.out(PUBLISH_CONTENT).out(ORIGIN).has("_class_", "com.ansvia.digaku.model.Forum").iterator()
//            .foreach(v => println(v.getOrElse("name","") + ", kind: " + v.getOrElse("kind",0)))

        val postUniqueChannel = v.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT).out(PUBLISH_CONTENT).out(ORIGIN)
            .has("kind", OriginKind.SUB_FORUM)
            .dedup()
            .count().toInt

        transact {
            v.setProperty("postUniqueChannel", postUniqueChannel)
        }

        postUniqueChannel

    }

    /**
     * mangkalkulasikan jumlah post user
     * @param v vertex user
     * @return
     */
    def calcUserPostCount(v:Vertex):Int = {

        validateVertexUser(v)

        val userPostCount = v.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT).out(PUBLISH_CONTENT)
            .count().toInt

        transact {
            v.setProperty("userPostCount", userPostCount)
        }

        userPostCount

    }

    /**
     * mencari satu post yang paling banyak like nya dan di ambil berapa like count nya
     * dan akan menambahkan property cupidBombCount pada user dari jumlah like tersebut
     * @param v
     * @return
     */
    def calcCupidBombCount(v:Vertex):Int = {

        validateVertexUser(v)

        val cupidBombCount = v.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT).out(PUBLISH_CONTENT)
            .order { (a:Vertex,b:Vertex) =>
                -a.getOrElse("likesCount", 0).compareTo(b.getOrElse("likesCount", 0))
            }
//            .sort { (a,b) =>
//            -a.getOrElse("likesCount", 0).compareTo(b.getOrElse("likesCount", 0))
        /*}*/.headOption
            .map(_.getOrElse("likesCount", 0))
            .getOrElse(0)

        transact {
            v.setProperty("cupidBombCount", cupidBombCount)
        }

        cupidBombCount

    }

    /**
     * kalkulasikan post yang paling banyak di response oleh user lain (uniq user)
     * akan menambahkan property userSuperpost yang isinya jumlah user yang meresponse
     * @param v
     * @return
     */
    def calcUserSuperpost(v:Vertex):Int = {

        import com.tinkerpop.pipes.util.structures.{Pair => GPair}

        db.rollback()
        validateVertexUser(v)

        v.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT).out(PUBLISH_CONTENT).iterator().foreach { v =>
            val uniqueCount = v.pipe.in(RESPONSE_OF).in(RESPONSE_WITH).dedup().count()
            v.setProperty("uniqueResponderCount", uniqueCount)
        }

        // @TODO(ubai): optimize this, ini potensi super node nih, karena user.out(PUBLISH_CONTENT)
        //              bisa return ribuan record bisa jadi memory killer nih
        //              di-kasih batasan pake range atau time misal hanya kalkulasiin data 2 bulan terakhir.
        val userSuperpost = v.pipe.out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.PUBLISH_CONTENT).out(PUBLISH_CONTENT)
            .order(new PipeFunction[GPair[Vertex, Vertex], java.lang.Integer] {
            def compute(argument: GPair[Vertex, Vertex]): java.lang.Integer = {
                argument.getB.getProperty[Long]("uniqueResponderCount").compareTo(
                    argument.getA.getProperty[Long]("uniqueResponderCount")
                )
            }
        }).headOption
            .map { v =>
//                println("got post: " + v.getOrElse("title",""))
//                v.pipe.in(RESPONSE_OF).in(RESPONSE_WITH).dedup().iterator().foreach { v =>
//                    println(v.getOrElse("name","-"))
//                }
                v.pipe.in(RESPONSE_OF).in(RESPONSE_WITH).dedup().count().toInt
            }.getOrElse(0)

        transact {
            v.setProperty("userSuperpost", userSuperpost)
        }

        userSuperpost
    }

    /**
     * digunakan untuk menkalkuasikan sudah berapa kali user di summond
     * @param v vertex user
     * @return
     */
    def calcSummonedUser(v:Vertex):Int = {
        validateVertexUser(v)

        val summonedCount = v.pipe.outE(SUMMONED).count().toInt

        transact {
            v.setProperty("summonedCount", summonedCount)
        }

        summonedCount

    }

    /**
     * digunakan untuk ceck apakah vertex merupakan vertex User atau bukan
     * @param v vertex
     */
    def validateVertexUser(v:Vertex) {
        assert(v != null, "vertex can't be null")
        if (!(v.getOrElse("_class_", "") == "com.ansvia.digaku.model.User" ))
            throw InvalidParameterException("not user vertex")

    }

    /**
     * mengkalkulasikan seluruh function yang ada diclass ini
     * @param v vertex user
     */
    def calculateReport(v:Vertex) {
        calcChannelOwnerCount(v)
        calcTeenPostADayOnWeek(v)
        calcArticleCreationCount(v)
        calcPostUniqueChannel(v)
        calcUserPostCount(v)
        calcCupidBombCount(v)
        calcUserSuperpost(v)
        calcSummonedUser(v)
    }

}
