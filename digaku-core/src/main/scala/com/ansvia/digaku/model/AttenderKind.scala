/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

/**
 * Author: nadir
 * 
 */
object AttenderKind {
    val POSITIVE = 1
    val MAYBE = 2
    val ALL = 4

    /**
     * Convert Str attender kind ke Int
     * @param kind attender kind
     * @return
     */
    def KindStrToInt(kind:String) = {
        kind match {
            case "positive" => POSITIVE
            case "maybe" => MAYBE
            case _ => ALL
        }
    }

    /**
     * Convert Int attender kind to Str
     * @param kind
     * @return
     */
    def KindIntToStr(kind:Int) = {
        kind match {
            case POSITIVE => "positive"
            case MAYBE => "maybe"
            case _ => "all"
        }
    }
}
