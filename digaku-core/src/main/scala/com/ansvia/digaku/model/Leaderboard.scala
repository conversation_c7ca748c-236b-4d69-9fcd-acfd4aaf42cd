//package com.ansvia.digaku.model
//import com.tinkerpop.blueprints.{Direction, Edge, Graph, Vertex}
//import com.ansvia.graph.BlueprintsWrapper._
//import Label._
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.Types.{GremPipeEdge, GremPipeVertex, TransactionalGraphType}
//import com.tinkerpop.gremlin.Tokens
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.database.GraphCompat
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.utils.SynchronizedKey
//
//import scala.collection.JavaConversions._
//import com.ansvia.graph.gremlin._
//
///**
//  * Author: Buaetin Nadir (<EMAIL>)
//  */
//
//object Leaderboard extends DbAccess with Slf4jLogger with Synchronized<PERSON>ey[Long] {
//
//    lazy val excludedUserSeqStore = Digaku.engine.seqStoreProvider.build(s"leaderboard-excluded-users", () => 0L)
//
//    lazy val excludedUserLogSeqStore = Digaku.engine.seqStoreProvider
//        .build(s"leaderboad-excluded-users-log", () => Digaku.engine.idFactory.createId().asInstanceOf[Long])
//
//    private val cvKind = CollectionVertex.Kind.LEADERBOARD
//
//    /**
//      * Root vertex dari leaderboard
//      * @return
//      */
//    def rootVertex(): Vertex = {
//        val key = "LeaderBoardRootVertex"
//        db.getVertices("_class_", key)
//            .headOption.getOrElse {
//
//                val v = db.addVertex(null)
//                v.setProperty("_class_", key)
//
//                db.commit()
//                v
//        }
//    }
//
//    /**
//      * Mendapatkan tahun dan bulan saat ini
//      * @return
//      */
//    private def getYearMonth: (Int, Int) = {
//        (Digaku.engine.dateUtils.getCurrentYear, Digaku.engine.dateUtils.getCurrentMonth)
//    }
//
//    /**
//      * Mendapatkan collection vertex berdasarkan tahun dan bulan function ini di panggil
//      * @param cv vertex untuk di collectionkan
//      * @param year
//      * @param month
//      * @param _db
//      * @return
//      */
//    private def getCollectionVertex(cv:Vertex, year:Int, month:Int, _db:TransactionalGraphType = db ) = synchronized {
//        val _cv =
//            cv.pipe.out(COLLECTION_VERTEX)
//                .has("kind", cvKind).has("year", year).has("month", month).asInstanceOf[GremPipeVertex]
//                .headOption.getOrElse {
//                    val v = _db.addVertex(null)
//                    v.setProperty("kind", cvKind)
//                    v.setProperty("year", year)
//                    v.setProperty("month", month)
//
//                    val ed = cv.addEdge(COLLECTION_VERTEX, v)
//
//                    val timeOrder = f"$year%04d$month%02d".toLong
//
//                    ed.setProperty("timeOrder", timeOrder)
//                    // kind juga dituliskan di edge-nya untuk optimal retrieving
//                    ed.setProperty("kind", cvKind)
//
//                    v
//                }
//
//        _db.getVertex(_cv.getId)
//    }
//
//    /**
//      * Increment user point berdasarkan value yang di berikan.
//      * @param user
//      * @param incBy
//      */
//    def incrementUserPoint(user: User, incBy:Int): Unit = synchronizedKey(user.getId){
//        val (year, month) = getYearMonth
//        val day = Digaku.engine.dateUtils.getCurrentDayOfMonth
//
//        GraphCompat.tx { t =>
//            getUserEdge(user, t).map { ed =>
//                val existPoint = ed.getOrElse("lbPoint", 0)
//                var pointHistory = ed.getOrElse("pointHistory", "").split(",").filter(_.nonEmpty).flatMap { rv =>
//                    val kv = rv.split(":")
//
//                    if (kv.length == 2) {
//                        Some(kv(0).toInt, kv(1).toInt)
//                    } else {
//                        None
//                    }
//                }.toMap
//
//                pointHistory.+=(day -> (pointHistory.getOrElse(day, 0) + incBy))
//
//                ed.setProperty("lbPoint", existPoint + incBy)
//                ed.setProperty("timeOrder", -Digaku.engine.dateUtils.nowMilis)
//                ed.setProperty("pointHistory", pointHistory.map(rv => s"${rv._1}:${rv._2}").mkString(","))
//            }.getOrElse {
//                val ed = getCollectionVertex(rootVertex().reload()(t), year, month, t).addEdge(LEADERBOARD, t.getVertex(user.getId))
//
//                ed.setProperty("lbPoint", incBy)
//                ed.setProperty("exclude", isExcluded(user))
//                ed.setProperty("timeOrder", -Digaku.engine.dateUtils.nowMilis)
//                ed.setProperty("targetId", user.getId)
//                ed.setProperty("pointHistory", s"$day:$incBy")
//            }
//        }
//    }
//
//    /**
//      * Mendapatkan edge leaderboard user.
//      * @param user
//      * @return
//      */
//    def getUserEdge(user:User, _db:TransactionalGraphType = db):Option[Edge] = {
//        val (year, month) = getYearMonth
//
//        rootVertex().reload()(_db).pipe.out(COLLECTION_VERTEX)
//            .has("kind", cvKind)
//            .has("year", year)
//            .has("month", month)
//            .outE(LEADERBOARD)
//            .has("targetId", user.getId)
//            .asInstanceOf[GremPipeEdge]
//            .iterator()
//            .toList.headOption
//    }
//
//    /**
//      * Mendapatkan point dari user tertentu
//      * @param user
//      * @return
//      */
//    def getUserPoint(user:User, _db:TransactionalGraphType = db):Int = {
//        getUserEdge(user, _db).toList.headOption.map { ed =>
//            ed.getOrElse("lbPoint", 0)
//        }.getOrElse(0)
//    }
//
//    /**
//      * Mendapatkan ranking user pada leaderboard
//      * @param user
//      * @return
//      */
//    def getRank(user:User, _db:TransactionalGraphType = db):Option[Int] = {
//        val (year, month) = getYearMonth
//
//        val rank = rootVertex().reload()(_db).pipe.out(COLLECTION_VERTEX)
//            .has("kind", cvKind)
//            .has("year", year)
//            .has("month", month)
//            .outE(LEADERBOARD)
//            .has("lbPoint", Tokens.T.gt, 0)
//            .has("exclude", false)
//            .asInstanceOf[GremPipeEdge]
//            .inV()
//            .indexOf(user.getVertex)
//
//
//        if (rank < 0) {
//            None
//        } else {
//            Some(rank + 1)
//        }
//    }
//
//    /**
//      * Pencarian user dalam leaderboard
//      * @param offset
//      * @param limit
//      * @param q
//      * @param withExcludeUser
//      * @param _db
//      * @return
//      */
//    def searchUserLeaderboard(offset:Int, limit:Int, q:String = "",
//                              withExcludeUser:Boolean = false, _db:TransactionalGraphType = db): List[(User, Int, Int)] = {
//
//        val (year, month) = getYearMonth
//
//        var map:Map[Long, Int] = Map.empty[Long, Int]
//
//        var query = rootVertex().reload()(_db).pipe.out(COLLECTION_VERTEX)
//            .has("kind", cvKind)
//            .has("year", year)
//            .has("month", month)
//            .outE(LEADERBOARD)
//            .has("lbPoint", Tokens.T.gt, 0)
//
//
//        if (!withExcludeUser) {
//            query = query.has("exclude", false)
//        }
//
//        val _q = q.trim.toLowerCase
//
//        if (_q.nonEmpty) {
//            var count = 1
//
//            query = query.as("ed")
//                .inV()
//                .filter { (v:Vertex) =>
//                    val rv = v.getOrElse("user.lower-name", "").toLowerCase.contains(_q) ||
//                        v.getOrElse("fullName", "").toLowerCase.contains(_q)
//
//                    if (rv) {
//                        map += (v.getId.asInstanceOf[Long] -> count)
//                    }
//
//                    count = count + 1
//
//                    rv
//                }.back("ed")
//                .asInstanceOf[GremPipeEdge]
//        }
//
//        query.range(offset, (offset + limit) - 1)
//            .asInstanceOf[GremPipeEdge]
//            .iterator()
//            .toList
//            .flatMap { ed =>
//                ed.getVertex(Direction.IN).toCC[User].map { user =>
//                    (user, map.getOrElse(user.getId, 0), ed.getOrElse("lbPoint", 0))
//                }
//            }
//    }
//
//    /**
//      * Mendapatkan list user pada leaderboard
//      * @param offset
//      * @param limit
//      * @param _db
//      * @return
//      */
//    def getList(offset:Int, limit:Int, withExcludeUser:Boolean = false, _db:TransactionalGraphType = db): List[(User, Int)] = {
//        val (year, month) = getYearMonth
//
//        var query = rootVertex().reload()(_db).pipe.out(COLLECTION_VERTEX)
//            .has("kind", cvKind)
//            .has("year", year)
//            .has("month", month)
//            .outE(LEADERBOARD)
//            .has("lbPoint", Tokens.T.gt, 0)
//
//
//        if (!withExcludeUser) {
//            query = query.has("exclude", false)
//        }
//
//        query.range(offset, (offset + limit) - 1)
//            .asInstanceOf[GremPipeEdge]
//            /*.order { (ed1:Edge, ed2:Edge) =>
//                val point = -ed1.getOrElse("lbPoint", 0).compareTo(ed2.getOrElse("lbPoint", 0))
//
//                if (point != 0) {
//                    point
//                } else {
//                    ed1.getOrElse("timeOrder", 0L).compareTo(ed2.getOrElse("timeOrder", 0L))
//                }
//            }*/.iterator()
//            .toList
//            .flatMap { ed =>
//                ed.getVertex(Direction.IN).toCC[User].map { user =>
//                    (user, ed.getOrElse("lbPoint", 0))
//                }
//            }
//    }
//
//    /**
//      * Mendapatkan jumlah user yang ada dalam leaderboard
//      * @param maxCount
//      * @param excludeUser
//      * @param _db
//      * @return
//      */
//    def getCount(maxCount:Option[Int], q:String="", withExcludeUser:Boolean = false, _db:TransactionalGraphType = db):Long = {
//        val (year, month) = getYearMonth
//
//        var query = rootVertex().reload()(_db).pipe.out(COLLECTION_VERTEX)
//            .has("kind", cvKind)
//            .has("year", year)
//            .has("month", month)
//            .outE(LEADERBOARD)
//            .has("lbPoint", Tokens.T.gt, 0)
//
//        if (!withExcludeUser) {
//            query = query.has("exclude", false)
//        }
//
//        val _q = q.trim.toLowerCase
//
//        if (_q.nonEmpty) {
//            query = query.as("ed")
//                .inV()
//                .filter { (v:Vertex) =>
//                    v.getOrElse("user.lower-name", "").toLowerCase.contains(_q) ||
//                        v.getOrElse("fullName", "").toLowerCase.contains(_q)
//                }.back("ed")
//                .asInstanceOf[GremPipeEdge]
//        }
//
//        maxCount.foreach { mc =>
//            query = query.range(0, mc - 1)
//        }
//
//        query.count()
//    }
//
//    /**
//      * Exculde user tertentu dari leaderboard.
//      * @param user
//      */
//    def excludeUser(user: User, exclude:Boolean): Unit = synchronizedKey(user.getId) {
//        getUserEdge(user).foreach { ed =>
//            ed.set("exclude", exclude)
//
//            db.commit()
//        }
//
//        val now = Digaku.engine.dateUtils.nowMilis
//
//        if (exclude) {
//            excludedUserSeqStore.insert(user.getId, now.toString)
//        } else {
//            excludedUserSeqStore.delete(user.getId)
//        }
//
//        excludedUserLogSeqStore.insert(s"${user.getId},$now,${exclude.toString}")
//
//        Digaku.engine.searchEngine.indexUser(user)
//    }
//
//    /**
//      * Check apakah user sudah di exclude dari leaderboard
//      * @param user
//      * @return
//      */
//    def isExcluded(user: User): Boolean = {
//        excludedUserSeqStore.get(user.getId).isDefined
//    }
//
//    /**
//      * Mendapatkan list user yang di exclude
//      * @param fromId
//      * @param toId
//      * @param limit
//      * @return
//      */
//    def getExcludedUsers(fromId:Option[Long], toId:Option[Long], limit:Int):Iterator[User] = {
//        excludedUserSeqStore.getStream(fromId.map(Long.box), toId.map(Long.box), limit)
//            .flatMap(rv => User.getById(rv._1))
//    }
//
//    /**
//      * Mendapatkan jumlah user yang di exclude
//      * @return
//      */
//    def getExcludedUsersCount:Int = {
//        excludedUserSeqStore.getCount
//    }
//}
//
