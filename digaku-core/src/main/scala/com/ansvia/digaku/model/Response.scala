/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.dao.ResponseDao
import com.tinkerpop.blueprints.Vertex
import com.ansvia.graph.annotation.Persistent
import scala.collection.JavaConversions._
import com.ansvia.digaku.exc.{InvalidStateException, DigakuException}
import java.util.Date
import com.ansvia.digaku.model.Label._
import com.ansvia.graph.BlueprintsWrapper._
import org.ocpsoft.prettytime.PrettyTime
import com.ansvia.digaku.Types._
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.Digaku



abstract class ResponseBase[dbT <: GraphType](content:String)
    extends BaseModel[IDType] with Has<PERSON><PERSON>in[dbT] with Deletable with Likable
    with Reportable[IDType] with DbAccess with ViaAppInfo with Counter
    with ContentVersioning with HasEmbeddedObject[IDType] with FileAttachable
    with Responable with PostAsUserGroupable {

    import com.ansvia.digaku.utils.RichDate._

    @Persistent var editReason = ""
    @Persistent var lastEditTime = 0L
    @Persistent var lastEditorUserId = 0L
    @Persistent var point = 0

    /**
     * index ini digunakan untuk pointing
     * posisi index berdasarkan spesifikasi
     * dari BCA, untuk model paging ala Discourse.
     */
    @Persistent var index = 0

    private lazy val counter = Digaku.engine.counterProvider("response-counter-" + getId)

    override lazy val creator:User = {
        val uv = getVertex.pipe.in(RESPONSE_WITH).in(COLLECTION_VERTEX).headOption
        if(uv.isEmpty)
            throw new DigakuException("Cannot get response creator")
        uv.get.toCC[User].getOrElse(
            throw new DigakuException("Cannot get response creator, invalid database object.")
        )
    }

    lazy val lastEditor:Option[User] = {
        User.getById(lastEditorUserId)
    }

    def getCounter = counter

    override def restoreDeleted() {
        super.restoreDeleted()
    }

    /**
     * Get pretty printed edit time age
     * relative to current time.
     * ex: 3 hours ago.
     * @return
     */
    def getLastEditTimeAge:String = {
        new PrettyTime(new Date()).format(new Date(lastEditTime))
    }

    def getLastEditTimeStd:String = {
        new Date(lastEditTime).toStdFormat
    }

    /**
     * ini digunakan untuk set point di response objek ini
     * di set ketika sebuah response mendapatkan extra point
     * @param point number point to add.
     */
    def setPoint(point:Int) {
        this.point = point
        this.save()
        db.commit()
    }


    /**
     * Fungsi yang digunakan untuk me-reset index.
     * Perlu dipanggil untuk pertamakali sebuah
     * response baru terbentuk, dan hanya dipanggil
     * lagi apabila memang diperlukan pada kasus-kasus
     * tertentu misal untuk proses reindexing.
     * @return
     */
    def resetIndex() = {
        val index = getRespondedObject match {
            case Some(p:PostBase) => {
                p.getCounter.increment("response-index")
                p.getCounter.get("response-index").toInt
            }
            case _ =>
                throw InvalidStateException("unexpected state, response should have responded object")
        }
        this.index = index
        this.save()
    }


    /**
     * Remove this response it self and
     * the edge RESPONSE_OF.
     * low level method don't use this for commons usage,
     * use [[com.ansvia.digaku.model.Responable.removeResponse]] instead.
     */
    def remove(){
        getVertex.pipe.outE(RESPONSE_OF).headOption.foreach { edge =>
            db.removeEdge(db.getEdge(edge.getId))
            db.removeVertex(db.getVertex(getVertex.getId))
            db.commit()
        }
    }


    /**
     * Get responded object, ex: Post.
     * @return
     */
    def getRespondedObject:Option[Responable] = {
        getVertex.pipe.out(RESPONSE_OF).headOption.flatMap { v =>
            v.getOrElse("_class_", "") match {
                case "com.ansvia.digaku.model.Article" =>
                    v.toCC[Article].map(_.asInstanceOf[Responable])
                case "com.ansvia.digaku.model.Event" =>
                    v.toCC[Event].map(_.asInstanceOf[Responable])
                case "com.ansvia.digaku.model.Picture" =>
                    v.toCC[Picture].map(_.asInstanceOf[Responable])
                case "com.ansvia.digaku.model.Response" =>
                    v.toCC[Response].map(_.asInstanceOf[Responable])
            }
        }
    }

    /**
     * Get pretty printed creation time age
     * relative to current time.
     * ex: 3 hours ago.
     * @return
     */
    def getCreationAge:String = {
        new PrettyTime(new Date()).format(new Date(creationTime))
    }

    override def addResponse(response:Response):Response = {
        val newResp = super.addResponse(response)

        // Increment child response count
        getRespondedObject.foreach {
            case post:Post =>
                post.incrementChildResponseCount()

            case _ =>
        }

        newResp

    }


    override def __load__(vertex: Vertex){
        super.__load__(vertex)
    }

}

case class Response(var content:String)
    extends ResponseBase[GraphType](content) with HasEmbeddedObject[IDType] {

    lazy val origin:Origin[GraphType] = {
        reload()
        getRespondedObject.map {
            case ho: HasOrigin[GraphType] =>
                ho.origin
//                case _ =>
//                    None
        }.get
    }
}

object Response extends ResponseDao[IDType]
