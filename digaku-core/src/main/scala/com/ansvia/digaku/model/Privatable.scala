/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.event.impl.UpdatePrivatedForumEvent
import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.Graph
import com.ansvia.digaku.Digaku
import com.ansvia.graph.BlueprintsWrapper.DbObject
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.database.GraphCompat._

/**
 * Author: robin
 *
 */
trait Privatable[idT, dbT <: Graph] extends DbObject with DbAccess {

    import com.ansvia.graph.BlueprintsWrapper._

    @Persistent var privated:Boolean = false


    /**
     * Check whether this origin is private.
     * @return
     */
    def getPrivated = privated

    /**
     * set origin private
     * @param state
     */
    def setPrivate(state:<PERSON>olean) {
        privated = state

        tx { t =>
            val v = t.getVertex(getVertex.getId)
            v.set("privated", privated)
        }

        this match {
            case forum: Forum =>

                // Remove semua follower ketika forum dijadikan private
                if (state) {
                    forum.removeAllFollowers()
                }

                Digaku.engine.eventStream.emit(UpdatePrivatedForumEvent(forum, state))

            case _ =>
        }


    }
}
