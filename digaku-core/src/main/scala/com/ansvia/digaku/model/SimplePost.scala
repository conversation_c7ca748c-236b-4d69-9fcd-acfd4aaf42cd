///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.dao.DaoBase
//import com.ansvia.digaku.database.GraphCompat.tx
//import com.ansvia.digaku.event.impl.{BlockedEvent, ClosedEvent}
//import com.ansvia.digaku.model.Label._
//import com.ansvia.digaku.{Digaku, Types}
//import com.ansvia.graph.BlueprintsWrapper._
//import com.tinkerpop.gremlin.Tokens.T
//
//import scala.collection.JavaConversions._
//
///**
// * Implementasi Post untuk simple post
// * yang dibatasi max 160 karakter
// * di digaku.core lama namanya UserStatus.
// */
//case class SimplePost(content:String) extends Post(content) with HasEmbeddedObject[IDType] {
//
//    import com.ansvia.digaku.model.Post.contains._
//
//    /**
//     * @see [[com.ansvia.digaku.model.PostKind]]
//     */
//    val kind:Int = PostKind.SIMPLE_POST
//
//    override def toString = {
//        val cont = getContent.replaceAll("\\W+", " ")
//        val nc = if(cont.length > 50)
//            cont.substring(0, 49) + "..."
//        else
//            cont
//        "SimplePost(" + id + ", " + nc + ")"
//    }
//
//    /**
//     * Menambahkan Embedded object untuk object tersebut
//     * @param obj
//     * @param noTx
//     */
//    override def addEmbeddedObject(obj: Embeddable[Types.IDType], noTx: Boolean){
//        super.addEmbeddedObject(obj, noTx)
//
//        // apabila obj adalah link ke video maka
//        // set flag containsVideo = true
//        obj match {
//            case el:EmbeddedLink => {
//                el.kind match {
//                    case LinkKind.TEXT =>
//
//                        containsLink = true
//                        getVertex.setProperty("containsLink", true)
//
//
//                    case LinkKind.VIDEO =>
//
//                        containsVideoLink = true
//                        getVertex.setProperty("containsVideoLink", true)
//                        getVertex.setProperty("streamKind", StreamKind.VIDEO)
//                        // merubah property streamKind ke "video"
//                        getVertex.pipe.inE(STREAM).iterator().foreach { edge =>
//                            edge.setProperty("streamKind", StreamKind.VIDEO)
//                        }
//
//                    case LinkKind.PIC =>
//
//                        containsPic = true
//                        getVertex.setProperty("containsPic", true)
//
//                    case LinkKind.OEMBED =>
//                        containsOembedLink = true
//                        getVertex.setProperty("containsOembedLink", true)
//                }
//
//                if (!noTx)
//                    db.commit()
//
//            }
//            case _ =>
//        }
//
//    }
//
//    /**
//     * digunakan untuk set blocked simple post dan akan mengindex ulang ke
//     * digaku search engine
//     * @param block block state true or false (true means block otherwise unblock).
//     * @param blocker user who block/unblock this object.
//     * @return
//     */
//    override def setBlocked(block:Boolean, blocker:User) = {
//
//        super.setBlocked(block, blocker)
//        this.save()
//        db.commit()
//
//        Digaku.engine.eventStream.emit(BlockedEvent(this))
//
//        this
//
//    }
//
//    /**
//     * Digunakan untuk set closed simple post dan akan diindex oleh digaku search engine
//     * @param close close state true or false (true means close otherwise open).
//     * @param closer user who close/open this object.
//     * @param reason reason why you closed this object.
//     * @return
//     */
//    override def setClosed(close:Boolean, closer:User, reason:String) = {
//        super.setClosed(close, closer, reason)
//        this.save()
//        db.commit()
//
//        Digaku.engine.eventStream.emit(ClosedEvent(this))
//
//        this
//
//    }
//
//    /**
//     * Override set deleted untuk menghapus simple post
//     * @param deletedType @see [[com.ansvia.digaku.model.DeletedType]]
//     * @param deletedRole @see [[com.ansvia.digaku.model.DeletedRole]]
//     * @param userDeleting user yang delete
//     * @param deletedReason deleted reason
//     * @param collected set false ketika di override dan ingin menambahkan property ke dalam collect
//     */
//    override def setDeleted(deletedType:Int ,deletedRole:String, userDeleting:User, deletedReason:String = "", collected:Boolean=false) {
//        super.setDeleted(deletedType, deletedRole, userDeleting, deletedReason, collected)
//
////        transact {
//            val edge = Deletable.collect(this, noTx = true)
//
//        if (edge != null){
//            edge.setProperty("deletedObject", "SimplePost")
//            edge.setProperty("deletedKind", "Streamable")
//            edge.setProperty("originId", this.origin.getId)
//        }
//
////        }
//        db.commit()
//
//    }
//
//    /**
//     * Get other post may like this posts
//     * based on user who like this post also like X posts.
//     * @param offset start offset.
//     * @param limit ends limit.
//     * @param containsFlag additional parameter if should only
//     *                     return post that contains x.
//     *                     @see [[com.ansvia.digaku.model.Post.contains]]
//     * @return
//     */
//    def getOtherMayLike(offset:Int, limit:Int, containsFlag:Int=NONE) = tx { t =>
//
//        var pipe = t.getVertex(getId).pipe.aggregate().as("this_post")
//            .in(LIKES).in(COLLECTION_VERTEX)
//            .out(COLLECTION_VERTEX).has("kind", CollectionVertex.Kind.LIKES).range(0,10)
//            .out(LIKES).range(0,100) // sementara di-range dulu ntuk menghindari ledakan memory.
//            .has("_class_", this.getClass.getCanonicalName)
//            .except("this_post")
//
//        if ( (containsFlag | LINK) == containsFlag ){
//            pipe = pipe.has("containsLink", true).asInstanceOf[GremPipeVertex]
//        }
//        if ( (containsFlag | VIDEO_LINK) == containsFlag ){
//            pipe = pipe.has("containsVideoLink", true).asInstanceOf[GremPipeVertex]
//        }
//        if ( (containsFlag | PIC) == containsFlag ){
//            pipe = pipe.has("containsPic", true).asInstanceOf[GremPipeVertex]
//        }
//
//        pipe.groupCount().cap().orderMap(T.decr).asInstanceOf[GremPipeVertex]
//            .range(offset, offset + limit - 1).iterator().toList.flatMap(_.toCC[SimplePost])
//
//    }
//
//
//}
//
//object SimplePost extends DaoBase[GraphType, SimplePost] {
//
//
//
//    //    /**
////     * Mendapatkan list simple post
////     * berdasarkan creation time
////     * @param offset post offset
////     * @param limit post limit
////     * @return
////     */
////    def getList(offset:Int, limit:Int):Array[SimplePost] = {
////        db.getVertices("_class_", "com.ansvia.digaku.model.SimplePost")
////              .toSeq
////              .reverse
////              .slice(offset, limit)
////              .flatMap(_.toCC[SimplePost])
////              .sortWith(_.creationTime > _.creationTime)
////              .toArray
////    }
////
//    val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.SimplePostRootVertex"
//}
//
//
///**
// * Ini sebenarnya sama saja dengan SimplePost,
// * hanya untuk memudahkan dao aja.
// */
//object Video extends DaoBase[GraphType, SimplePost] with Popularable with Slf4jLogger {
//
//    import java.util.concurrent.{Callable, TimeUnit}
//
//import com.ansvia.digaku.exc.InvalidParameterException
//    import com.ansvia.digaku.utils.NonBlockingExecutor
//    import com.google.common.cache.{CacheBuilder, CacheLoader}
//    import com.google.common.util.concurrent.ListenableFutureTask
//    import com.thinkaurelius.titan.core.attribute.Decimal
//    import com.tinkerpop.blueprints.{Direction, Vertex}
//
//    val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.VideoRootVertex"
//
//    override def addPopular(vertex:Vertex, score:Double) = {
//        require(vertex != null, "vertex is null")
//        require(vertex.getOrElse("_class_", "") == "com.ansvia.digaku.model.SimplePost",
//            "cannot add popular item %s into this dao %s".format(vertex.getOrElse("_class_", "com.ansvia.digaku.model.SimplePost"),
//                this.getClass.getCanonicalName.replace("$","")))
//
//        val edge = popularRootVertex --> POPULAR --> vertex <()
//        edge.setProperty("score", score)
//        edge
//    }
//
//    private lazy val vertexCacheVideo = CacheBuilder.newBuilder()
//        .maximumSize(10)
//        .refreshAfterWrite(1, TimeUnit.HOURS)
//        .build[String, Seq[(SimplePost, Double)]](new CacheLoader[String, Seq[(SimplePost, Double)]] {
//
//        def load(key: String) = {
//            val s = key.split(":")
//            if (s.length != 3)
//                throw InvalidParameterException("key should contain offset and limit. " +
//                    "eg: popular-videos:0,10")
//            loadPopularVideoInternal(s(1).toInt, s(2).toInt)
//        }
//
//        override def reload(key: String, oldValue: Seq[(SimplePost, Double)]) = {
//
//            debug("on reload popular video cache...")
//
//            val task = ListenableFutureTask.create(new Callable[Seq[(SimplePost, Double)]] {
//                def call() = {
//                    val rv = load(key)
//                    debug("reload cache done for key: " + key)
//                    rv
//                }
//            })
//
//            NonBlockingExecutor.threadPollExecutor.execute(task)
//
//            task
//        }
//    })
//
//    /**
//     * Get popular videos.
//     * @param offset starting offset.
//     * @param limit ends limit.
//     * @param cached whether return from cache if any, default true.
//     * @return
//     */
//    def getPopularVideos(offset:Int, limit:Int, cached:Boolean): Seq[(SimplePost, Double)] = {
//        if (cached)
//            vertexCacheVideo("popular-videos:%d:%d".format(offset, limit))
//        else
//            loadPopularVideoInternal(offset, limit)
//    }
//
//    private def loadPopularVideoInternal(offset:Int, limit:Int) = {
//        popularRootVertex.pipe.outE(POPULAR)
//            .inV()
//            .hasNot("blocked", true)
//            .hasNot("deleted", true)
//            .dedup()
//            .range(offset, (offset + limit) - 1)
//            .iterator.flatMap(_.toCC[SimplePost])
//            .map { vid =>
//                val x = vid.getVertex.getEdges(Direction.IN, POPULAR).head.getProperty[Decimal]("score")
//                val score = if (x != null) x.floatValue() else 0.0
//                (vid, score)
//        }.toSeq
//    }
//
//    /**
//     * digunakan untuk refresh popular video cache
//     */
//    def refreshPopularVideoCache() {
//        vertexCacheVideo.invalidateAll()
//    }
//
//}
//
