/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.exc.{AlreadyExistsException, InvalidParameterException, NotExistsException}

/**
 * Author: nadir (<EMAIL>)
 *
 */

case class Rank(exp:Int, name:String)

object ExperienceRank {

//    private object rankRegistry extends CassandraBackedKVStore(Global.STANDARD_CF_NAME, "user_rank",
//        Digaku.config.mainDatabase.keyspaceName,
//        Digaku.config.mainDatabase.clusterName,
//        Digaku.config.mainDatabase.hostName,
//        Digaku.config.mainDatabase.replStrategy,
//        Digaku.config.mainDatabase.replStrategyOpts
//    )

    private val rankRegistry = Digaku.engine.kvStoreProvider.build("user_rank")

//    private val ranks = MapDb.compressed.createHashMap("ranks_cache")
//        .expireAfterAccess(60, TimeUnit.MINUTES)
//        .expireAfterWrite(120, TimeUnit.MINUTES)
//        .expireMaxSize(5000)
//        .make[String, String]()

    /**
     * digunakan untuk create rank
     * @param exp minimum experience ketika mendapatkan rank ini
     * @param name nama dari rank
     * @return see [[com.ansvia.digaku.model.Rank]]
     */
    def createRank(exp:Int, name: String): Rank ={
        if (rankRegistry.exists(exp.toString))
            throw AlreadyExistsException("Rank already exist with experience " + exp)

        if (name.trim.length <= 3 || name.trim.length >= 20)
            throw InvalidParameterException("Rank name minimal 3 and maximal 20 character")

        rankRegistry.set(exp.toString, name.trim)

        Rank(exp, name.trim)
    }

    /**
     * digunakan untuk mendapatkan rank
     * @param exp dari jumlah experience-nya
     * @return
     */
    def getRank(exp:Int):Option[Rank] = {
        rankRegistry.getOption[String](exp.toString)
            .map(rv => Rank(exp, rv))
    }

    /**
     * mendapatkan seluruh rank
     * @return
     */
    def getRanks = {
        rankRegistry.getAll.map { rank =>
            (rank._1.toInt, rank._2.toString)
        }
    }

    /**
     * update jumlah experience pada rank tertentu.
     * @param oldExp jumlah experience sebelumnya yang akan diubah.
     * @param newExp jumlah experience baru.
     */
    def updateRankExp(oldExp:Int, newExp:Int): Unit = {
        rankRegistry.getOption[String](oldExp.toString)
            .map { rank =>
            removeRank(oldExp)
            createRank(newExp, rank.toString)
        }.getOrElse(throw NotExistsException("Rank is not exist with experience " + oldExp))
    }

    /**
     * digunakan untuk update rank name dari experience value yang ada
     * @param exp experience value
     * @param newName nama rank yang baru
     */
    def updateRankName(exp:Int, newName:String): Unit = {
        if (getRank(exp).isEmpty)
            throw NotExistsException("Rank is not exist with experience " + exp)

        if (newName.trim.length <= 3 || newName.trim.length >= 20)
            throw InvalidParameterException("Rank name minimal 3 and maximal 20 character")

        rankRegistry.set(exp.toString, newName)
    }

    /**
     * kalkulasikan experience untuk mendapatkan rank.
     * @param exp jumlah experience.
     * @return
     */
    def calcRank(exp:Int):Option[Rank] = {
        getRanks.map(x => (x._1.toInt, x._2)).toSeq.filter { rank => rank._1 <= exp }
            .sortBy(_._1)
            .lastOption
            .map(Rank.tupled)
    }

    /**
     * digunakan untuk menghapus rank
     * @param exp
     */
    def removeRank(exp:Int): Unit ={
        rankRegistry.delete(exp.toString)
    }

    /**
     * digunakan  untuk remove seluruh rank
     * saat ini hanya digunakan untuk kebutuhan test spec
     */
    def removeAllRank() = {
        rankRegistry.getAll.foreach(x => rankRegistry.delete(x._1))
    }

}
