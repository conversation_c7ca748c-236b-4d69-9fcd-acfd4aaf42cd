///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.model
//
//import com.tinkerpop.blueprints.{Graph, Direction, Edge}
//import com.ansvia.graph.BlueprintsWrapper._
//import Label._
//import com.ansvia.digaku.Global
//import com.ansvia.digaku.helpers.DbAccess
//
///**
// * Author: robin
// * Date: 2/21/13
// * Time: 4:21 PM
// *
// */
//abstract class ActivityBase[T >: Null](edge:Edge) extends DbAccess  {
//
////    private implicit def db:Graph = Global.database.getRaw
//
//    final def compile:Option[T] = {
//        edge.getLabel match {
//            case JOIN =>
//                edge.getVertex(Direction.OUT).toCC[User].map { user =>
//                    edge.getVertex(Direction.IN).toCC[Group].map {chan =>
//                        Some(compileJoin(user, chan))
//                    }.getOrElse(None)
//                }.getOrElse(None)
//
//            case RESPONSE_WITH =>
//                edge.getVertex(Direction.OUT).toCC[User].map { user =>
//                    edge.getVertex(Direction.IN).pipe.out(RESPONSE_OF).next().toCC[Post].map { post =>
//                        Some(compileResponse(user, post))
//                    }.getOrElse(None)
//                }.getOrElse(None)
//
//            case WROTE =>
//                edge.getVertex(Direction.OUT).toCC[User].map { user =>
//                    edge.getVertex(Direction.IN).toCC[Post].map { post =>
//                        Some(compilePost(user, post))
//                    }.getOrElse(None)
//                }.getOrElse(None)
//
//            case SUPPORT =>
//                edge.getVertex(Direction.OUT).toCC[User].map { userA=>
//                    edge.getVertex(Direction.IN).toCC[User].map { userB =>
//                        Some(compileSupport(userA, userB))
//                    }.getOrElse(None)
//                }.getOrElse(None)
//
//            case CREATE =>
//                edge.getVertex(Direction.OUT).toCC[User].map {user =>
//                    val createV = edge.getVertex(Direction.IN)
//                    if (createV.getOrElse("_class_", "")=="com.ansvia.digaku.model.Picture"){
//                        createV.toCC[Picture].map{ picture =>
//                            Some(compilePicture(user, picture))
//                        }.getOrElse(None)
//                    } else {
//                        createV.toCC[Event].map{ event =>
//                            Some(compileEvent(user, event))
//                        }.getOrElse(None)
//                    }
//                }.getOrElse(None)
//
//            case _=> None
//        }
//    }
//
//    def compileJoin(user:User, group:Group):T
//    def compileSupport(userA:User, userB:User):T
//    def compileResponse(userA:User, post:Post):T
//    def compilePost(user:User, post:Post):T
//    def compileEvent(user:User, event:Event):T
//    def compilePicture(user:User, picture:Picture):T
//}
//
//
//
//case class ActivityString(edge:Edge) extends ActivityBase[String](edge){
//    import com.ansvia.digaku.model.PostKind._
//
//    def compileJoin(user: User, group: Group) =
//        "%s join %s".format(user.name, group.name)
//
//    def compileSupport(userA: User, userB: User) =
//        "%s support %s".format(userA.name, userB.name)
//
//    def compileResponse(userA: User, post: Post) = {
//        val snippet = {
//            post.kind match {
//                case ARTICLE =>
//                    post.asInstanceOf[Article].title
//                case DEAL =>
//                    post.asInstanceOf[Deal].name
//                case QUESTION =>
//                    post.asInstanceOf[Question].title
//                case SIMPLE_POST =>
//                    val content = post.asInstanceOf[SimplePost].content
//                    if (content.length>50){
//                        content.substring(50) + "..."
//                    }else
//                        content
//            }
//        }
//        "%s response to post %s".format(userA.name,snippet)
//    }
//
//    def compilePost(user: User, post: Post): String = {
//        val snippet = {
//            post.kind match {
//                case ARTICLE =>
//                    post.asInstanceOf[Article].title
//                case DEAL =>
//                    post.asInstanceOf[Deal].name
//                case QUESTION =>
//                    post.asInstanceOf[Question].title
//                case SIMPLE_POST =>
//                    val content = post.asInstanceOf[SimplePost].content
//                    if (content.length>50){
//                        content.substring(50) + "..."
//                    }else
//                        content
//            }
//        }
//        "%s wrote post %s".format(user.name, snippet)
//    }
//
//    def compileEvent(user: User, event: Event): String ={
//        "%s create event %s".format(user.name, event.title)
//    }
//
//    def compilePicture(user: User, picture: Picture) = {
//        "%s post picture %s".format(user.name, picture.title)
//    }
//}
//
