/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.exc.{AlreadyExistsException, DigakuException, UnsupportedException}
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.persistence.CassandraBackedKVStoreFactory
import com.ansvia.digaku.utils.{MailSender, TextCompiler}
import com.ansvia.digaku.{Digaku, Types}

/**
 * Author: robin
 *
 * Merupakan draft email untuk blast email.
 */


/**
 * Email draft yang awalnya disimpan terlebih dahulu sebelum dikirimkan.
 * cara kerja:
 *  Setelah draft dibuat kita baru bisa mengirimkannya via [[com.ansvia.digaku.model.EmailTransport]]
 * @param senderName email sender name.
 * @param senderAddress email sender address.
 * @param kind email kind, can be plain, html, or textile, @see [[com.ansvia.digaku.model.EmailKind]]
 */
case class EmailDraft(var senderName:String, var senderAddress:String, var kind:Int)
    extends BaseModel[IDType] with DbAccess {

    import EmailKind._
    import Label._
    import com.ansvia.graph.BlueprintsWrapper._

    import scala.collection.JavaConversions._

    def setReceivers(receivers:User*) =
        EmailTransport(this, receivers:_*)

    def setReceiversEmails(addresses:String*) =
        EmailTransportByAddress(this, addresses:_*)

    def compile(locale:String):String = {
        if (!Locale.isSupported(locale))
            throw UnsupportedException("Locale not supported " + locale)

        val content = getContent(locale).map(_.content).getOrElse("")

        kind match {
            case PLAIN => content
            case HTML => content
            case TEXTILE => TextCompiler.compileMessage(content)
        }
    }

    def contentType = kind match {
        case PLAIN => "text/plain"
        case HTML => "text/html"
        case TEXTILE => "text/html"
    }

    /**
     * check apakah email draft ini sudah punya
     * content dengan locale tertentu?
     * @param locale locale untuk dicheck see [[com.ansvia.digaku.model.Locale]]
     * @return
     */
    def hasLocale(locale:String) =
        availableLocales.exists(_.locale == locale)

    /**
     * Add content untuk locale yang berbeda-beda.
     * @param content
     */
    def addContent(content:EmailContentLocale){
        // check is already exists?
        if (hasLocale(content.locale))
            throw AlreadyExistsException("content for locale %s already exists".format(content.locale))

        this --> CONTENT_LOCALE --> content
        db.commit()
    }

    /**
     * Add content untuk locale yang berbeda-beda.
     * @param locale
     * @param content
     */
    def addContent(locale:String, subject:String, content:String){
        if (!Locale.supported.contains(locale))
            throw UnsupportedException("Locale not supported " + locale)

        // check is already exists?
        if (hasLocale(locale))
            throw AlreadyExistsException("content for locale %s already exists".format(locale))

        addContentNoTx(locale, subject, content)
        db.commit()
    }

    /**
     * just like `addContent` but no transactional
     * should called inside of transact.
     * @param locale
     * @param content
     */
    def addContentNoTx(locale:String, subject:String, content:String){
        if (!Locale.supported.contains(locale))
            throw UnsupportedException("Locale not supported " + locale)

        // check is already exists?
        if (hasLocale(locale))
            throw AlreadyExistsException("content for locale %s already exists".format(locale))

        val ecl = EmailContentLocale(locale, subject, content).save().toCC[EmailContentLocale].getOrElse {
            throw new DigakuException("Cannot create content locale")
        }
        this --> CONTENT_LOCALE --> ecl

    }

    /**
     * Dapatkan content localization berdasarkan locale-nya.
     * @param locale see [[com.ansvia.digaku.model.Locale]]
     * @return
     */
    def getContent(locale:String):Option[EmailContentLocale] = {
        getVertex.pipe.out(CONTENT_LOCALE)
            .has("locale", locale)
            .headOption.flatMap(_.toCC[EmailContentLocale])
    }

    def availableLocales = getVertex.pipe.out(CONTENT_LOCALE)
        .iterator().flatMap(_.toCC[EmailContentLocale])

//    override def delete()(implicit db: Graph) {
//        transact {
//
//            // hapus semua content locales-nya
//            getVertex.pipe.out(CONTENT_LOCALE).iterator().foreach(db.removeVertex)
//
//            // hapus semua edges-nya
//            getVertex.pipe.bothE().iterator().foreach(db.removeEdge)
//
//            // finally hapus vertex-nya.
//            db.removeVertex(getVertex)
//        }
//    }
}

trait MailTemplateCompilerIface {
    def compile(text:String):String

    def compileContent(text:String):String
}

/**
 * Digunakan untuk localization di email
 * berisi subject dan content-nya.
 * @param locale
 * @param subject
 * @param content
 */
case class EmailContentLocale(var locale:String, var subject:String, var content:String) extends ContentLocaleBase(locale, content)

object EmailContentLocale extends DaoBase[GraphType, EmailContentLocale] {

    import com.ansvia.digaku.model.Label.CONTENT_LOCALE
    import com.ansvia.graph.BlueprintsWrapper._

    import scala.collection.JavaConversions._

    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.EmailContentLocaleRootVertex"

    def create(locale:String, subject:String, content:String):EmailContentLocale = {
        val ecl = EmailContentLocale(locale, subject, content).save().toCC[EmailContentLocale].getOrElse {
            throw new DigakuException("Cannot create email content locale, storage error?")
        }

        addToRoot(ecl.getVertex)
        db.commit()

        ecl
    }

    override def deleteById(id: Types.IDType)(implicit m: Manifest[EmailContentLocale]){

        val v = db.getVertex(id)

        assert(v != null, "vertex is null, cannot delete vertex with id " + id)

        // hapus semua content locales-nya
        v.pipe.out(CONTENT_LOCALE).iterator().foreach(db.removeVertex)

        // hapus semua edges-nya
        v.pipe.bothE().remove()

        super.deleteById(id)(m)
    }
}

/**
 * Email transporter, merupakan staging class apabila
 * email draft sudah ready untuk dikirimkan.
 *
 * Email transporter ini dilengkapi anti duplication
 * sehingga menghindari sebuah email sama terkirim lebih dari
 * satu kali ke user yang sama.
 *
 * @param mailDraft email draft yang akan dikirimkan.
 * @param receivers user penerima.
 */
case class EmailTransport(mailDraft:EmailDraft, receivers:User*) extends Slf4jLogger {

    import com.ansvia.digaku.utils.RichString._

    private object tmpKvStoreFactory extends CassandraBackedKVStoreFactory("tmp",
        Digaku.config.analyticDatabase.keyspaceName,
        Digaku.config.analyticDatabase.clusterName,
        Digaku.config.analyticDatabase.hostName,
        Digaku.config.analyticDatabase.replStrategy,
        Digaku.config.analyticDatabase.replStrategyOpts
    )

    private lazy val dupRegistry = tmpKvStoreFactory.build("mail_send_duplicate_detector")

    var mailSender:MailSender = _
    var templateCompiler:MailTemplateCompilerIface = _
    var duplicateDetectorEnabled = true

    /**
     * Kirimkan email.
     */
    def send(){

        assert(mailSender != null, "MailSender not set, please set it first" +
            " using setMailSender")
        assert(templateCompiler != null, "Template compiler not set, please set it first " +
            "using setTemplateCompiler")

        val from = "%s:%s".format(mailDraft.senderAddress, mailDraft.senderName)

        def _send(cl:EmailContentLocale, recv:User){
            val subject = templateCompiler.compile(cl.subject)
            val content = templateCompiler.compileContent(mailDraft.compile(recv.locale))

            mailSender.send(from, subject,
                content,
                mailDraft.contentType, recv)
        }

        for( recv <- receivers ){


            mailDraft.getContent(recv.locale).foreach { cl =>

                val dupKey = "%s-%s-%s".format(recv.emailLogin, cl.subject.trim, cl.content.trim.md5)


                if (duplicateDetectorEnabled){
                    if (!dupRegistry.exists(dupKey)){
                        dupRegistry.set(dupKey, "", Some(3888000)) // expires in 45 days
                        _send(cl, recv)
                    }else{
                        warn(s"Email sender duplicate prevention block email to ${recv.name} duplicate detected, key: $dupKey")
                    }
                }else{
                    _send(cl, recv)
                }



            }
        }


    }

    /**
     * Untuk set apakah perlu mengaktifkan duplicate detector atau tidak,
     * secara default adalah aktif (true).
     * @param state posisi (true = aktif) (false = inactive).
     * @return
     */
    def setDuplicateDetectorEnabled(state:Boolean) = {
        this.duplicateDetectorEnabled = state
        this
    }


    def setMailSender(mailSender:MailSender) = {
        this.mailSender = mailSender
        this
    }

    def setTemplateCompiler(tmplCompiler:MailTemplateCompilerIface) = {
        this.templateCompiler = tmplCompiler
        this
    }
}

/**
 * Email transporter, merupakan staging class apabila
 * email draft sudah ready untuk dikirimkan.
 * @param mailDraft
 * @param addresses
 */
case class EmailTransportByAddress(mailDraft:EmailDraft, addresses:String*){

    var mailSender:MailSender = _
    var templateCompiler:MailTemplateCompilerIface = _

    def send(){

        assert(mailSender != null, "MailSender not set, please set it first" +
            " using setMailSender")
        assert(templateCompiler != null, "Template compiler not set, please set it first " +
            "using setTemplateCompiler")

        val from = "%s:%s".format(mailDraft.senderAddress, mailDraft.senderName)

        val availableLocale = mailDraft.availableLocales.toList(0)

        for( recv <- addresses ){

            mailDraft.getContent(availableLocale.locale).map { cl =>

                val subject = templateCompiler.compile(cl.subject)
                val content = templateCompiler.compileContent(mailDraft.compile(availableLocale.locale))

                mailSender.sendByAddress(from, subject, content, mailDraft.contentType, addresses:_*)
            }
        }


    }

    def setMailSender(mailSender:MailSender) = {
        this.mailSender = mailSender
        this
    }

    def setTemplateCompiler(tmplCompiler:MailTemplateCompilerIface) = {
        this.templateCompiler = tmplCompiler
        this
    }
}


/**
 * Email kind.
 */
object EmailKind {
    val PLAIN = 0
    val HTML = 1
    val TEXTILE = 2
}

/**
 * Email draft dao, digunakan untuk create, get, dan delete (CRUD like operation)
 * dengan mudah.
 */
object EmailDraft extends DaoBase[GraphType, EmailDraft] with DbAccess {

    import Label.EMAIL_DRAFT
    import com.ansvia.graph.BlueprintsWrapper._

    import scala.collection.JavaConversions._


    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.EmailDraftRootVertex"
    override val rootVertexLabel = EMAIL_DRAFT

    /**
     * Buat email draft baru.
     * @param senderName
     * @param senderAddress
     * @param subject
     * @param content
     * @param kind
     * @return
     */
    def create(senderName:String, senderAddress:String, subject:String, content:String, kind:Int) = {
        val emailDraft = EmailDraft(senderName, senderAddress, kind)
            .save().toCC[EmailDraft].getOrElse {
            throw new DigakuException("Cannot create EmailDraft, storage exception.")
        }

        emailDraft.addContentNoTx(Locale.DEFAULT_LOCALE, subject, content)

//            val e = rootVertex --> EMAIL_DRAFT --> emailDraft <()
//            e.set("timeOrder", -Digaku.engine.dateUtils.nowMilis)

        addToRoot(emailDraft.getVertex)

        db.commit()

        emailDraft

    }

    /**
     * Dapatkan daftar email draft yang ada dari database.
     * @param offset
     * @param limit
     * @param m
     * @return
     */
    def getList(offset: Int, limit: Int)(implicit m: Manifest[EmailDraft]) = {
        rootVertex.pipe.out(rootVertexLabel).iterator().flatMap(_.toCC[EmailDraft])
    }

    /**
     * used in unittest only!
     */
    def clear(){
        rootVertex.pipe.outE(rootVertexLabel).remove()
        db.commit()
    }

}