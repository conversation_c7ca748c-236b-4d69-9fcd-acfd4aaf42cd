/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.model

import java.util.Date

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.Label.EVENT_REMINDER
import com.ansvia.graph.BlueprintsWrapper._
import com.tinkerpop.blueprints.Vertex
import com.tinkerpop.gremlin.Tokens.T
import com.tinkerpop.gremlin.java.GremlinPipeline
import org.joda.time.DateTime

import scala.collection.JavaConversions._


/**
 * Author: robin
 * Date: 6/22/13
 * Time: 9:26 PM
 *
 */
case class EventReminder(title:String,dueDate:Long) extends BaseModel[IDType] with DbAccess with Slf4jLogger {

    lazy val event:Option[Event] = {
        getVertex.pipe.in(EVENT_REMINDER)
            .has("_class_", "com.ansvia.digaku.model.Event").asInstanceOf[GremlinPipeline[Vertex, Vertex]]
            .headOption
            .flatMap(_.toCC[Event])
    }

    lazy val user:Option[User] = {
//        getVertex.pipe.outFirst(EVENT_REMINDER).flatMap(_.toCC[User])
        getVertex.pipe.out(EVENT_REMINDER).headOption.flatMap(_.toCC[User])
    }

    def remind() {
        event.map { ev =>
            user.map { u =>
                debug("remind %s to event %s at %s".format(u.name, ev.title, ev.location))

                getVertex.pipe.inE(EVENT_REMINDER).iterator().foreach { e =>
                    val notified = e.getOrElse("notified", 0) + 1
                    e.setProperty("notified", notified)
                }

                db.commit()
            }
        }
    }
}


object EventReminder extends DaoBase[GraphType, EventReminder] with DbAccess {


    val ROOT_VERTEX_CLASS = "com.ansvia.digaku.model.EventReminderRootVertex"
    override val rootVertexLabel = EVENT_REMINDER

    /**
     * Create new event reminder.
     * non transactional should run inside of transact.
     * @return
     */
    def create(event:Event, forUser:User, noTx:Boolean=false):EventReminder = {

        assert(forUser != null, "User cannot be null")
        assert(event != null, "Event cannot be null")

        val dueDate = event.startDate

        assert(dueDate > Digaku.engine.dateUtils.nowMilis, "Invalid due date, must be more than now.")

        val er = EventReminder(event.title, dueDate).save().toCC[EventReminder].getOrElse {
            throw new DigakuException("Cannot create EventReminder, storage error?")
        }

        val rootE = addToRoot(er.getVertex)

        event --> EVENT_REMINDER --> er
        er --> EVENT_REMINDER --> forUser

        rootE.setProperty("dueDate", event.startDate)
        rootE.setProperty("notified", 0)

        if (!noTx)
            db.commit()

        er


    }

    def reset(event:Event){
        rootVertex.pipe.outE(EVENT_REMINDER).as("ed").inV().in(EVENT_REMINDER).has("id", event.getId)
            .back("ed")
            .asInstanceOf[GremPipeEdge]
            .headOption.map { edge =>
                edge.setProperty("dueDate", event.startDate)
                edge.setProperty("notified", 0)
            }
    }

    /**
     * Clear event reminder registry,
     * useful for testing.
     */
    def clear(){
        rootVertex.pipe.outE(EVENT_REMINDER).remove()
    }


    /**
     * Get list event reminder
     * @param offset reminder offset
     * @param limit reminder limit
     * @param m
     * @return
     */
    def getList(offset: Int, limit: Int)(implicit m: Manifest[EventReminder]) = {
//        rootVertex.pipe.out(EVENT_REMINDER).range(offset, offset + limit).iterator().flatMap(_.toCC[EventReminder])
        getListRight(None, None, offset + limit).toList.slice(offset, offset + limit).toIterator
    }

    /**
     * Untuk mendapatkan daftar event setelah :afterDate
     * @param afterDate batas minimal due date.
     * @param notified
     * @param offset
     * @param limit
     * @return
     */
    def getList(afterDate:Date, notified:Int, offset:Int, limit:Int):Iterator[EventReminder] = {
//        println(afterDate.getTime)
        rootVertex.pipe.outE(EVENT_REMINDER)
            .has("dueDate", T.gt, afterDate.getTime)
            .has("notified", notified)
            .inV()
            .range(offset, offset + limit)
            .iterator().flatMap(_.toCC[EventReminder])
    }

    /**
     * Get reminder list that will coming in near n days.
     * @param days n days.
     * @param notified 0 or 1
     * @param offset start offset
     * @param limit end limit
     * @return
     */
    def getListComing(days:Int, notified:Int, offset:Int, limit:Int):Iterator[EventReminder] = {

//        val cal = Calendar.getInstance()
        val now = Digaku.engine.dateUtils.nowMilis

//        cal.setTime(now)
//        cal.add(Calendar.DATE, days)
        val to = new DateTime().plusDays(days).getMillis

        rootVertex.pipe.outE(EVENT_REMINDER)
            .interval("dueDate", now, to)
            .has("notified", notified)
            .inV()
            .range(offset, offset + limit)
            .iterator().flatMap(_.toCC[EventReminder])
    }


}

