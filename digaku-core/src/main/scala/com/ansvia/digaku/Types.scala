
/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku

import com.tinkerpop.blueprints.{Edge, Vertex, KeyIndexableGraph, TransactionalGraph}
import com.tinkerpop.gremlin.java.GremlinPipeline
import com.thinkaurelius.titan.core.TitanGraph
import com.tinkerpop.blueprints.util.wrappers.id.IdGraph

object Types {
    type IDGraphType = IdGraph[TitanGraph] //KeyIndexableGraph with TransactionalGraph
    type TransactionalGraphType = KeyIndexableGraph with TransactionalGraph
    type GraphType = IDGraphType
    type IDType = Long
    type GremPipeEdge = GremlinPipeline[Vertex, Edge]
    type GremPipeVertex = GremlinPipeline[Vertex, Vertex]
}

