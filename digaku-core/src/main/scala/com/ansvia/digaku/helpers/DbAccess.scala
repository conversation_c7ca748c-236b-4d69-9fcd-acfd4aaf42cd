/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.helpers

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types.GraphType

/**
 * Author: robin
 * 
 */

trait AbstractDbAccess {
    protected implicit def db:GraphType
}

trait DbAccess extends AbstractDbAccess {

    protected implicit def db:GraphType = Digaku.engine.database.getRaw


}
