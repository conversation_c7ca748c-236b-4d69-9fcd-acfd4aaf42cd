/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.helpers

import com.ansvia.digaku.Types._
import com.ansvia.digaku.model.BaseModel
import com.tinkerpop.blueprints.Vertex
import com.tinkerpop.blueprints.util.wrappers.id.IdGraph
import com.tinkerpop.gremlin.java.GremlinPipeline
import scala.collection.JavaConversions._

/**
 * Author: robin
 *
 */
trait GremlinHelpers extends AbstractDbAccess {

    class pipeToIdVertex(pipe:GremPipeVertex){
        def toIdVertex = {
            pipe.iterator().map(v => db.getVertex(v.getProperty[IDType](IdGraph.ID)))
        }
    }
    implicit def implicitPipeToIdVertex(pipe:GremPipeVertex) = new pipeToIdVertex(pipe)


    def gremlin(v:Vertex) = {
        new GremlinPipeline[Vertex, AnyRef].start(v)
    }

    implicit class baseModelWrapper(model:BaseModel[IDType]){
        def gremlin(v:Vertex) = {
            new GremlinPipeline[Vertex, AnyRef].start(v)
        }
        def pipe = {
            gremlin(model.getVertex)
        }

    }
}

object GremlinHelpers extends GremlinHelpers with DbAccess
