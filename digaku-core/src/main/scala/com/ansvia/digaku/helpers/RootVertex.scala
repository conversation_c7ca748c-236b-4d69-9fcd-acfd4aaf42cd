/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.helpers

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types._
import com.tinkerpop.blueprints.{Edge, Vertex}
import com.tinkerpop.gremlin.Tokens.T

/**
 * Author: robin
 *
 */

trait RootVertex {

    protected implicit def db:GraphType

    import RootVertex._
    import com.ansvia.graph.BlueprintsWrapper._

    import scala.collection.JavaConversions._

    val ROOT_VERTEX_CLASS:String
    val rootVertexLabel:String
//    private var availablePartition = 0


    /**
     * primary key for using to quick iterate
     * in ordered way.
     * @see [[com.ansvia.digaku.helpers.RootVertex#orderingDirection]]
     * override this as you wish
     */
    val primaryKey:String = "timeOrder"

    private def rootVertexConfig = {
        val key = ROOT_VERTEX_CLASS + "Config"
        db.getVertices("_class_", key)
            .headOption.getOrElse {

            val v = db.addVertex(null)
            v.setProperty("_class_", key)
            v.setProperty(PARTITION_COUNT_KEY, 1)
            v.setProperty(ID_ROUTING_KEY, "0:0")
            v.setProperty(ITEM_COUNT_KEY, 0L)

            db.commit()

            v

        }
    }

    def getAvailablePartition = {
        rootVertexConfig.getOrElse(AVAILABLE_PARTITION_KEY, 0)
    }

    def getRootVertexPartitionCount = {
        rootVertexConfig.getOrElse(PARTITION_COUNT_KEY, 1)
    }



//    /**
//     * ordering direction, -1 for descending
//     * and 1 for ascending.
//     */
//    val orderingDirection = -1

//    @deprecated("gunakan rootVertex(partition)", "3 april 2014")

    /**
     * hanya shortuct untuk rootVertex(0)
     * @return
     */
    def rootVertex:Vertex = {
        rootVertex(getRootVertexPartitionCount-1)
//        db.getVertices("_class_", ROOT_VERTEX_CLASS + "-0")
//            .headOption.getOrElse {
//            transact {
//                val v = db.addVertex(null)
//                v.setProperty("_class_", ROOT_VERTEX_CLASS + "-0")
//                v
//            }
//        }
    }

    def rootVertex(partition:Int, prefix:String=""):Vertex = {
//        if (partition < 1){
//            // for backward compatibility apabila partition < 1
//            // maka gunakan rootVertex lama
//            rootVertex
//        }else {
            val partitionKey = ROOT_VERTEX_CLASS + prefix + "-" + partition
            db.getVertices("_class_", partitionKey)
                .headOption.getOrElse {

                val v = db.addVertex(null)
                v.setProperty("_class_", partitionKey)

                db.commit()
                v
            }
//        }
    }



    /**
     * add vertex to root, all dao create operation should
     * use this method to add their ref to root vertex.
     * non transactional, should be executed inside of
     * transact.
     * @param vertex vertex to add.
     * @return
     */
    def addToRoot(vertex:Vertex):Edge = {
        require(vertex != null, "vertex cannot be null")

        var availablePartition = getAvailablePartition
        val rvc = rootVertexConfig

        // get available partition and update that if any
        val nextPart = (rvc.getOrElse(ITEM_COUNT_KEY, 0L) / DEFAULT_SIZE_PER_PARTITION).toInt
        while (nextPart > availablePartition){

            val prevRootVertex = rootVertex(availablePartition)

            // update available partition

//            debug("switching partition to " + nextPart)

            availablePartition = nextPart


            val partitionCount = rvc.getOrElse(PARTITION_COUNT_KEY, 0) + 1


            rvc.setProperty(AVAILABLE_PARTITION_KEY, availablePartition)
            rvc.setProperty(PARTITION_COUNT_KEY, partitionCount)

            // for fast lookup we need to save starting id as meta data
            rvc.setProperty(STARTING_ID_KEY, vertex.getId)

            // update previous part rootVertex id
            prevRootVertex.setProperty(ENDS_ID_KEY, vertex.getId)

            // id map for easy lookup by routing id to partition
            val idRouting = rvc.getOrElse(ID_ROUTING_KEY,"")
            var idRoutingList = idRouting.split(",")
            idRoutingList = (idRoutingList ++ Seq(vertex.getId + ":" + availablePartition))
                .filter(_.trim.length > 0).distinct
            rvc.setProperty(ID_ROUTING_KEY, idRoutingList.mkString(","))
        }

//        println("getAvailablePartition: " + getAvailablePartition)
        val roov = rootVertex(availablePartition)

        val edge = roov --> rootVertexLabel --> vertex <()

        edge.setProperty(primaryKey, Digaku.engine.dateUtils.nowMilis)
        edge.setProperty("targetId", vertex.getId)

        val count = roov.getOrElse("count", 0L) + 1L
        roov.setProperty("count", count)

        rvc.setProperty(ITEM_COUNT_KEY, rvc.getOrElse(ITEM_COUNT_KEY, 0L) + 1L)

//        edge.setProperty("offset", count)

        edge
    }

    private def getIdRouting = {
        rootVertexConfig.getOrElse(ID_ROUTING_KEY, "").split(",").filter(_.trim.length > 0)
            .map(_.split(":")).map(x => (x(0).toLong, x(1).toInt))
    }


    def whereIs(v:Vertex):Int = {
        val id = v.getId.asInstanceOf[IDType]

        val idRouting = getIdRouting

//        debug("idRouting.sortBy(_._1): " + idRouting.sortBy(_._1).reverse.toList)

        val partition = idRouting.sortBy(_._1).reverse.find(x => x._1 <= id).map(_._2).getOrElse(0)

//        val partition = (id / DEFAULT_SIZE_PER_PARTITION)

        // check if we have that partition?
        val partitionKey = ROOT_VERTEX_CLASS + "-" + partition
        db.getVertices("_class_", partitionKey).iterator.hasNext match {
            case true => partition
            case _ => -1
        }
    }


    def paging(startsId:Option[IDType], endsId:Option[IDType], returnPerPage:Int, maxPage:Option[Int]=None)(func: (Int, Iterator[Vertex]) => Boolean) = {
//        val confV = rootVertexConfig
        require(returnPerPage > 1, "min return per page = 2")

        val highestPartition = getAvailablePartition
        var roov = rootVertex(highestPartition)

        startsId match {
            case Some(id) =>
                var sid = id
                var _done = false
                var page = 0
                var part = highestPartition
                while(!roov.pipe.outE(rootVertexLabel).has("targetId", T.lt, sid)
                    .range(0,0).iterator().hasNext && !_done){

//                    debug("not found in partition " + part + ", check from previous partition")

                    if (part - 1 < 0)
                        _done = true
                    part = part - 1
                    roov = rootVertex(part)
                }
                while(roov.pipe.outE(rootVertexLabel).has("targetId", T.lt, sid)
                    .range(0,0).iterator().hasNext && !_done){
                    val it = roov.pipe.outE(rootVertexLabel).has("targetId", T.lte, sid)
                        .range(0,returnPerPage-1).inV().iterator()

                    page = page + 1

                    _done = !func(page, it)

                    if (!_done){
                        val it2 = roov.pipe.outE(rootVertexLabel).has("targetId", T.lt, sid)
                            .range(returnPerPage,returnPerPage).inV().iterator()


                        it2.hasNext match {
                            case true =>
                                if (!_done){
                                    sid = it2.next().getId.asInstanceOf[IDType]
                                    val pipe = roov.pipe.outE(rootVertexLabel).has("targetId", T.lt, sid)
                                        .range(0,returnPerPage-1).asInstanceOf[GremPipeEdge]
                                    if (!pipe.hasNext){
                                        part = part - 1
                                        roov = rootVertex(part)
                                    }
                                }

                            case false =>
                                part = part - 1
                                roov = rootVertex(part)
                        }
                    }


                }
            case None =>
                var sid = -1L
                var _done = false
                var page = 0
                var part = highestPartition
                var pipe:GremPipeEdge = roov.pipe.outE(rootVertexLabel)
                    .range(0,returnPerPage-1)

                try {
                    while(pipe.iterator().hasNext && !_done){
                        val it = pipe.inV().iterator()
                        page = page + 1

                        _done = !func(page, it)

                        if (_done)
                            throw Break

                        maxPage.foreach {
                            case x if page >= x =>
                                throw Break
                            case _ =>
                        }

                        pipe = roov.pipe.outE(rootVertexLabel)
                        sid match {
                            case id if id > -1L =>
                                pipe = pipe.has("targetId", T.lt, id).range(returnPerPage-1, returnPerPage).asInstanceOf[GremPipeEdge]
                            case _ =>
                                pipe = pipe.range(returnPerPage-1, returnPerPage)
                        }
                        val it2 = pipe.inV().iterator()
                        it2.hasNext match {
                            case true =>
                                if (!_done){
                                    sid = it2.next().getId.asInstanceOf[IDType]
                                    pipe = roov.pipe.outE(rootVertexLabel).has("targetId", T.lt, sid)
                                        .range(0,returnPerPage-1).asInstanceOf[GremPipeEdge]
                                    if (!pipe.hasNext){
                                        part = part - 1
                                        roov = rootVertex(part)
                                        pipe = roov.pipe.outE(rootVertexLabel)
                                            .range(0,returnPerPage-1)
                                        sid = -1L
                                    }
                                }
                            case false =>
                                part = part - 1
                                roov = rootVertex(part)
                                pipe = roov.pipe.outE(rootVertexLabel)
                                    .range(0,returnPerPage-1)
                                sid = -1L
                        }
                    }
                }catch{
                    case Break =>
                }

        }


    }

    def getCount:Long = {
        val pcount = getRootVertexPartitionCount
        (for (i <- 0 to pcount)
            yield rootVertex(i).getOrElse("count", 0L)).sum
    }

}

object RootVertex extends DbAccess {
    val AVAILABLE_PARTITION_KEY = "availablePartition"
    val PARTITION_COUNT_KEY = "partitionCount"
    val STARTING_ID_KEY = "startingId"
    val ID_ROUTING_KEY = "idRouting"
    val ITEM_COUNT_KEY = "itemCount"
    val ENDS_ID_KEY = "endsId"
    var DEFAULT_SIZE_PER_PARTITION = 100000L

    protected object Break extends Exception("break")


    def reset(rootvertex:RootVertex){
        import scala.collection.JavaConversions._

        db.getVertices("_class_", rootvertex.ROOT_VERTEX_CLASS + "Config").headOption.foreach(db.removeVertex)
        db.getVertices("_class_", rootvertex.ROOT_VERTEX_CLASS + "Popular").headOption.foreach(db.removeVertex)
        for (partition <- 0 to rootvertex.getAvailablePartition){
            db.getVertices("_class_", rootvertex.ROOT_VERTEX_CLASS + "-" + partition).headOption.foreach(db.removeVertex)
        }
        db.commit()
    }

    //// when we want to using bloom filter enable this
//
//    class BitArray(bits : Int){
//        val size = nextPow2(bits)
//        require(isPowerOf2(size))
//        private val data = new Array[Long](size >> 6)
//
//        def set(index : Int) = data(idx(index)) |= (1L << index)
//        def get(index : Int) = (data(idx(index)) & (1L << index)) != 0
//
//        private val mask = size - 1
//        private def idx(index : Int) = (index & mask) >> 6
//        private def isPowerOf2(i : Int) = ((i - 1) & i) == 0
//        private def nextPow2(i : Int) = {
//            def highestBit(remainder : Int, c : Int) : Int =
//                if(remainder > 0) highestBit(remainder >> 1, c + 1) else c
//            require(i <= (1 << 30))
//            val n = if(isPowerOf2(i)) i else 1 << highestBit(i, 0)
//            assert(n >= i && i * 2 > n && isPowerOf2(n))
//            n
//        }
//    }
//
//    class BloomFilter(val size : Int, val expectedElements : Int ){
//        import math._
//
//        require(size > 0)
//        require(expectedElements > 0)
//
//        val bitArray = new BitArray(size)
//        val k = ceil((bitArray.size / expectedElements) * log(2.0)).toInt
//        val expectedFalsePositiveProbability = pow(1 - exp(-k * 1.0 * expectedElements / bitArray.size), k)
//
//        def add(hash : Int) {
//            def add(i : Int, seed : Int) {
//                if(i == k) return
//                val next = xorRandom(seed)
//                bitArray.set(next)
//                add(i + 1, next)
//            }
//            add(0, hash)
//        }
//
//        def contains(hash : Int) : Boolean = {
//            def contains(i : Int, seed : Int) : Boolean = {
//                if(i == k) return true
//                val next = xorRandom(seed)
//                if (!bitArray.get(next)) return false
//                return contains(i + 1, next)
//            }
//            contains(0, hash)
//        }
//
//        private def xorRandom(i : Int) = {
//            var y = i
//            y ^= y << 13
//            y ^= y >> 17
//            y ^ y << 5
//        }
//    }
}