/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.helpers

import com.ansvia.digaku.exc.InvalidParameterException

/**
 * Author: robin
 *
 */
object TypeHelpers {

    implicit def intToJavaInteger(i:Int):java.lang.Integer = Int.box(i)

    /**
     * ConvertWrapper ini digunakan untuk menghandle paramater yang tidak dapat
     * di convert menjadi Long
     * @param strNum
     */
    protected class ConvertWrapper(strNum:String){
        def toLongOr(num:Long):Long = {
            try {
                strNum.toLong
            } catch {
                case e: NumberFormatException => num
            }
        }

        /**
         * Coba convert string menjadi <PERSON>, atau kembalikan exception bila gagal
         * Digunakan bila tidak ingin mengembalikan default value
         * @param str string kembalian
         * @return
         */
        def toLongOrExc(str:String):Long = {
            try {
                strNum.toLong
            } catch {
                case e: NumberFormatException => throw InvalidParameterException(str)
            }
        }

        def toIntOr(num:Int):Int = {
            try {
                strNum.toInt
            } catch {
                case e: NumberFormatException => num
            }
        }

        /**
         * Coba convert string menjadi Integer, atau kembalikan exception bila gagal
         * Digunakan bila tidak ingin mengembalikan default value
         * @param str string kembalian
         * @return
         */
        def toIntOrExc(str:String):Int = {
            try {
                strNum.toInt
            } catch {
                case e: NumberFormatException => throw InvalidParameterException(str)
            }
        }

        def toBooleanOr(bool:Boolean):Boolean = {
            try {
                strNum.toBoolean
            } catch {
                case e: NumberFormatException => bool
            }
        }

        /**
         * Convert to Boolean and throw exception if converting failed
         * @param msg Error message to return
         * @return
         */
        def toBooleanExc(msg:String):Boolean = {
            try {
                if (strNum != null) strNum.toLowerCase match {
                    case "true" | "1" => true
                    case "false" | "0" => false
                    case x => throw new NumberFormatException(msg)
                }
                else
                    throw new NumberFormatException(msg)
            } catch {
                case e: NumberFormatException => throw InvalidParameterException(msg)
            }
        }

        def toByteOr(defVal:Byte) = {
            try {
                strNum.toByte
            }catch{
                case e:NumberFormatException =>
                    defVal
            }
        }

        /**
         * Convert to Double and throw exception if converting failed
         * @param msg Error message to return
         * @return
         */
        def toDoubleOrExc(msg:String):Double = {
            try {
                strNum.toDouble
            } catch {
                case e: NumberFormatException => throw InvalidParameterException(msg)
            }
        }

        def toDoubleOr(num:Double):Double = {
            try {
                strNum.toDouble
            } catch {
                case e: NumberFormatException => num
            }
        }

    }

    implicit def implicitConvertWrapper(strNum:String) = new ConvertWrapper(strNum)

}
