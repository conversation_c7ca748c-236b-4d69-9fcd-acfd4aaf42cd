/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.helpers

/**
 * Author: robin
 *
 */
trait ShellHelper {
    // scalastyle:off
    private var prevText = ""

    def printOverwrite(text:String){
        val count = if(text.length > prevText.length)
            0
        else
            prevText.length - text.length

        print(text + (" " * count) + "\r")
        prevText = text
    }

    def println(text:String){
        printOverwrite(text + "\n")
    }
    // scalastyle:on
}


object Tabulator {
    def format(table: Seq[Seq[Any]]) = table match {
        case Seq() => ""
        case _ =>
            val sizes = for (row <- table) yield (for (cell <- row) yield if (cell == null) 0 else cell.toString.length)
            val colSizes = for (col <- sizes.transpose) yield col.max
            val rows = for (row <- table) yield formatRow(row, colSizes)
            formatRows(rowSeparator(colSizes), rows)
    }

    def formatRows(rowSeparator: String, rows: Seq[String]): String = (
        rowSeparator ::
            rows.head ::
            rowSeparator ::
            rows.tail.toList :::
            rowSeparator ::
            List()).mkString("\n")

    def formatRow(row: Seq[Any], colSizes: Seq[Int]) = {
        val cells = (for ((item, size) <- row.zip(colSizes)) yield if (size == 0) "" else ("%" + size + "s").format(item))
        cells.mkString("|", "|", "|")
    }

    def rowSeparator(colSizes: Seq[Int]) = colSizes map { "-" * _ } mkString("+", "+", "+")
}
