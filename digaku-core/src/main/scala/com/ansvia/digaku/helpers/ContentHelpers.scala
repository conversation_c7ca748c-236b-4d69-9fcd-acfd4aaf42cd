/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.helpers

import com.ansvia.digaku.Types._
import com.ansvia.digaku.model

/**
 * Author: robin (<EMAIL>)
 */

object ContentHelpers {

    import com.ansvia.digaku.utils.RichString._

    protected val alphaNumOnlyRe = "\\W+".r

    implicit class PostWrapper(p:model.Post){


        def normalizedTitle(alphaNumOnly:Boolean=false): String = {
            val rv = p match {
                case article:model.Article =>
                    article.title
                case he:model.HasEmbeddedObject[IDType] =>
                    val embs = he.getEmbeddedObjects
                    if (embs.nonEmpty)
                        embs.headOption.map {
                            case pic:model.Picture =>
                                pic.title.truncate(100)
                            case vl:model.EmbeddedLink =>
                                vl.title.truncate(100)
                            case _ =>
                                he match {
//                                    case sp:model.SimplePost =>
//                                        sp.content.truncate(100)
                                    case _ =>
                                        p.getContent.truncate(100)
                                }
                        }.getOrElse(p.getContent.truncate(100))
                    else
                        p.getContent.truncate(100)
                case _ =>
                    p.getContent.truncate(100)
            }
            if (alphaNumOnly)
                alphaNumOnlyRe.replaceAllIn(rv, " ")
            else
                rv
        }

        def normalizedDesc(): String = {
            p match {
                case article:model.Article =>
                    article.shortDesc
//                case sp:model.SimplePost =>
//                    if (sp.content.trim == ""){
//                        val embs = sp.getEmbeddedObjects
//                        if (embs.length > 0)
//                            embs.headOption.map {
//                                case pic:model.Picture =>
//                                    pic.title
//                                case vl:model.EmbeddedLink =>
//                                    vl.desc.truncate(100)
//                                case _ =>
//                                    ""
//                            }.getOrElse("")
//                        else
//                            ""
//                    }else{
//                        sp.content.truncate(100)
//                    }
                case he:model.HasEmbeddedObject[IDType] =>
                    val embs = he.getEmbeddedObjects
                    if (embs.nonEmpty)
                        embs.headOption.map {
                            case pic:model.Picture =>
                                pic.title
                            case vl:model.EmbeddedLink =>
                                vl.desc.truncate(100)
                            case _ =>
                                he match {
//                                    case sp:model.SimplePost =>
//                                        sp.content.truncate(100)
                                    case _ =>
                                        p.getContent.truncate(100)
                                }
                        }.getOrElse(p.getContent.truncate(100))
                    else
                        p.getContent.truncate(100)
                case _ =>
                    p.getContent.truncate(100)
            }
        }
    }
}
