/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.auth

import com.ansvia.digaku.model.User


/**
 * Author: robin (<EMAIL>)
 */

abstract class AuthProvider[-TUID,  TOutput](name: String) {
    def authenticate[T <: TUID](authUserId: T, password: String): TOutput

    override def toString: String = {
        s"AuthProvider($name)"
    }
}


class DefaultAuthProvider() extends AuthProvider[User, Bo<PERSON>an]("default") {
    override def authenticate[T <: User](authUserId: T, password: String): Boolean = {
        authUserId.passwordMatch(password)
    }
}
