///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.live
//
//import scala.concurrent.duration._
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.model.User
//import com.ansvia.digaku.model.Label._
//import java.util.Calendar
//import com.ansvia.digaku.utils.DateUtils
//import com.tinkerpop.gremlin.java.GremlinPipeline
//import com.tinkerpop.blueprints.Vertex
//import scala.collection.JavaConversions._
//import com.ansvia.digaku.event.EventStream
//import com.ansvia.digaku.event.impl.LockedUserEvent
//
//// scalastyle:off magic.number
//
///**
// * Author: nadir
// *
// */
//abstract class AutoUserReleaseLiveAbstract extends LiveObject with DbAccess {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    val period = 12 hour
//
//    private var inProcessing = false
//
//    def run() {
//        synchronized {
//            if (!inProcessing){
//                try {
//                    inProcessing = true
//
////                    val cal = Calendar.getInstance()
//                    val toNow = Digaku.engine.dateUtils.getCurrentTime()
//
////                    cal.setTime(toNow)
////                    cal.add(Calendar.DATE, -5)
////                    val from = cal.getTime
//
//                    val from = toNow.minusDays(5)
//
//                    db.rollback()
//                    User.rootVertex.pipe.out(DAO_LIST).has("locked", true)
//                        .interval("releaseAfter", from.getMillis, toNow.getMillis)
//                        .asInstanceOf[GremlinPipeline[Vertex, Vertex]]
//                        .range(0, 500)
//                        .iterator()
//                        .flatMap(_.toCC[User])
//                        .foreach{ user =>
//                            if(user.isExpired) {
//                                callback(user)
//                                Digaku.engine.eventStream.emit(LockedUserEvent(this.asInstanceOf[User]))
//                                debug("unlock user : "+user.name)
//                            }
//                        }
//                }finally{
//                    inProcessing = false
//                }
//            }
//        }
//    }
//
//    def callback(user:User)
//
//}
