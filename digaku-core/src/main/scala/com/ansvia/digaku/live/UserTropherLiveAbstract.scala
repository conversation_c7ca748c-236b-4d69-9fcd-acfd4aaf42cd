/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.live

import scala.concurrent.duration._
import com.ansvia.digaku.Digaku
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.utils.MailSender
import com.ansvia.digaku.model._
import com.ansvia.digaku.Types._

/**
 * Author: nadir
 * Date: 12/6/13
 * Time: 11:47 AM
 * 
 */
abstract class UserTropherLiveAbstract extends LiveObject with DbAccess   {

    import com.ansvia.graph.BlueprintsWrapper._

    val period = 24 hours

    /**
     * trophy automatic assignment yang akan diimplement pada seluruh user
     */
    val trophyWithAutoAssign:Array[Trophy]

    private var inProcessing = false

    def run() {
        synchronized {
            if (!inProcessing){
                try {
                    inProcessing = true

                    db.rollback()

                    User.paging(None, None, 100, None) { case (page, vertices) =>
                        vertices.foreach { v =>

                            UserReports.calculateReport(v)

                            v.toCC[User].foreach { u =>
                                trophyWithAutoAssign.foreach { trophy =>
                                    trophy.updateTrophyState(u)
                                }
                            }

                        }
                        true
                    }

                }finally{
                    inProcessing = false
                }

            }
        }

    }

}

