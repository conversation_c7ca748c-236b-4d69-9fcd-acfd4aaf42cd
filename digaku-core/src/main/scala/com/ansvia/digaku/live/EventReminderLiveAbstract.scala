/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.live

import scala.concurrent.duration._
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.model.{Event, User, EventReminder}
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.utils.MailSender

/**
 * Author: robin
 * Date: 6/23/13
 * Time: 12:01 AM
 *
 * Merupakan class yang digunakan untuk continuous
 * event reminder sender.
 */
abstract class EventReminderLiveAbstract extends LiveObject with DbAccess {

    val period = 2 hour

    val mailSender:MailSender
    val mailFrom:String
//    private lazy val throttler = RateLimiter.create(1.0, 2, TimeUnit.HOURS)

    private var inProcessing = false

    override def start(){
        assert(mailSender != null, "mail sender not set, please set it first.")
        super.start()
    }

    def run() {
        assert(mailSender != null, "mail sender not set, please set it first.")

        synchronized {
            if (!inProcessing){
                try {
                    inProcessing = true

                    db.rollback()
                    EventReminder.getListComing(5, 0, 0, 500).foreach { er =>

                        er.user map { u =>
                            er.event map { ev =>
                                callback(u, ev)
                            }
                        }

                        er.remind()
                    }
                    EventReminder.getListComing(2, 1, 0, 500).foreach { er =>

                        er.user map { u =>
                            er.event map { ev =>
                                callback(u, ev)
                            }
                        }

                        er.remind()
                    }
                }finally{
                    inProcessing = false
                }

            }
        }

    }

    def callback(user:User, event:Event)


}
