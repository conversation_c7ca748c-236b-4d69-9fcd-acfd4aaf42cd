/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.live

/**
 * Author: robin
 * Date: 12/29/13
 * Time: 9:08 PM
 *
 */
object LiveObjectRegistry {

    private var liveObjects = Seq.empty[LiveObject]

    def register(lo:LiveObject){
        liveObjects :+= lo
    }

    def remove(lo:LiveObject){
        liveObjects = liveObjects.filterNot(_ == lo)
    }

    def clear(){
        liveObjects.foreach(_.stop())
        liveObjects = Seq.empty[LiveObject]
    }

    def getAll() = {
        liveObjects
    }

}
