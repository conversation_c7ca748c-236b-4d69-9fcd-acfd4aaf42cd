/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.live

import akka.actor.ActorSystem
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import scala.concurrent.duration.FiniteDuration

/**
 * Author: robin
 *
 * Seperti thread, tapi lebih keren.
 *
 */
trait LiveObject extends Runnable with Slf4jLogger {
    val period:FiniteDuration
    lazy val initial:FiniteDuration = period

    implicit private def executor = LiveObject.actorSystem.dispatcher

    lazy val name:String = this.getClass.getSimpleName.replace("$","")

    protected lazy val schedule = Digaku.engine.actorSystem.scheduler.schedule(initial, period, this)
    private var started = false

    LiveObjectRegistry.register(this)

    def start(){
        if (!started){
            debug(name + " started. " + !schedule.isCancelled)
            started = true
        }
    }

    def isStarted = started

    def stop(){
        if (started){
            schedule.cancel()
            debug(this + " stopped.")
        }
    }
}

object LiveObject {
    implicit lazy val actorSystem: ActorSystem = ActorSystem("live-object")
}
