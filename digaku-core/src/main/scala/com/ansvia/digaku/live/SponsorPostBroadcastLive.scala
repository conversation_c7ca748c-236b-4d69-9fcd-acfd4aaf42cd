/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

//package com.ansvia.digaku.live
//
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.digaku.model._
//import com.ansvia.digaku.stream.StreamBuilder
//import scala.concurrent.duration.FiniteDuration
//import scala.concurrent.duration._
//import com.ansvia.digaku.Types.{GraphType, GremPipeVertex}
//import com.ansvia.digaku.stats.model.{Stats, CountStats}
//import com.ansvia.digaku.exc.IgnoredException
//import com.tinkerpop.gremlin.Tokens.T
//import com.ansvia.digaku.utils.DateUtils
//import com.ansvia.digaku.Digaku
//import com.ansvia.digaku.event.impl.SponsorPostBroadcastEvent
//import scala.util.Random
//import com.tinkerpop.blueprints.Vertex
//
///**
// * Author: nadir
// * Date: 12/6/13
// * Time: 11:47 AM
// *
// */
//@deprecated("sudah tidak diperlukan lagi sejak telah menggunakan teknik DASA (Dynamic Ads Stream Adder)", "11 mei 2014")
//class SponsorPostBroadcastLive extends LiveObject with DbAccess {
//
//    import scala.collection.JavaConversions._
//    import com.ansvia.graph.BlueprintsWrapper._
//    import com.ansvia.digaku.model.Label._
//
//
//    private lazy val rnd = new Random(System.currentTimeMillis())
//
//    val period: FiniteDuration = 60 minutes
//
//    private var inProcessing = false
//
//    val random = true
//
//    def run() {
//        synchronized {
//            if (!inProcessing){
//                try {
//
//                    inProcessing = true
//
//                    db.rollback()
//
//                    val limit = getUserLimit
//
//                    if (limit > 5){
//
//                        val vs = buildSponsorVertices()
//
//                        var pipe = User.rootVertex.pipe.out(User.rootVertexLabel)
//
//                        if (random)
//                            pipe = pipe.random(0.3)
//
//                        pipe.range(0, limit)
//                            // don't process user with latest post is sponsor
//                            // to prevent user annoyed by too many sponsor in their stream.
////                                .outE(STREAM).range(0,1).hasNot("sponsor",true).back("user")
//                            .asInstanceOf[GremPipeVertex]
//                            .iterator()
//                            .flatMap(_.toCC[User])
//                            .foreach { u =>
//
////                            println("processing u: " + u)
//
//                            try {
//
//                                val uV = u.getVertex
//
//                                pickOneSponsor(vs) map { spV =>
//
//                                    lazy val sp = spV.toCC[SponsorPost].get
//
//
//                                    // don't process user with latest 10 post is sponsor
//                                    // to prevent user annoyed by too many sponsor in their stream.
//                                    val streamEit = uV.pipe.out(COLLECTION_VERTEX)
//                                        .has("kind",CollectionVertex.Kind.STREAM).range(0,1)
//                                        .outE(STREAM).range(0,10).iterator()
//
//                                    while (streamEit.hasNext){
//                                        val streamE = streamEit.next()
//                                        if (streamE.getOrElse("sponsor", false)){
//                                            debug("sponsor broadcast ignored, user " + u + " already has")
//                                            throw IgnoredException()
//                                        }
//                                    }
//
//                                    import com.ansvia.digaku.utils.RichString._
//
//                                    if (sp.minAge > 0){
//                                        if (u.age.getYears < sp.minAge){
//                                            debug("sponsor ignored due age restriction %d < %s".format(u.age.getYears, sp.minAge))
//                                            throw IgnoredException()
//                                        }
//                                    }
//
//                                    if (sp.maxAge > 0){
//                                        if (u.age.getYears > sp.maxAge){
//                                            debug("sponsor ignored due age restriction %d > %s".format(u.age.getYears, sp.maxAge))
//                                            throw IgnoredException()
//                                        }
//                                    }
//
//                                    if (sp.genderTarget != -1){
//                                        if (sp.genderTarget != u.sex){
//                                            debug(("sponsor ignored due gender restriction %s != %s, " +
//                                                "sponsor %s only for user with gender %s").format(SexType.toStr(u.sex),
//                                                SexType.toStr(sp.genderTarget), sp, SexType.toStr(sp.genderTarget)))
//                                            throw IgnoredException()
//                                        }
//                                    }
//
//                                    debug("broadcasting %s to %s".format(spV.getOrElse("content","")
//                                        .truncate(20), uV.getOrElse("name","")))
//
//                                    uV.toCC[Origin[GraphType] with CollectionVertex].map { _origin =>
//                                        val ed = StreamBuilder.addToStream(_origin, sp, timeFromCreation = false)
//                                        ed.setProperty("sponsor", true)
//                                    }
//
//                                    // emit broadcast event
//                                    Digaku.engine.eventStream emit SponsorPostBroadcastEvent(sp)
//
//                                }// getOrElse {
////                                    debug("no picked sponsor")
////                                }
//
//                            }catch{
//                                case e:IgnoredException =>
//                            }
//
//                        }
//
//                        db.commit()
//
//
//                    }
//
//                }catch{
//                    case e:Exception =>
//                        error("broadcast failed/stopped caused by: " + e.getMessage)
//                        error(e.getStackTraceString)
//                        e.printStackTrace()
//
//                }finally{
//                    inProcessing = false
//                }
//
//            }
//        }
//
//    }
//
//    /**
//     * Generate sequence of sponsors.
//     */
//    def buildSponsorVertices() = {
//        val now = Digaku.engine.dateUtils.nowMilis
//
//        val vs =
//            SponsorPost.rootVertex.pipe.out(SponsorPost.rootVertexLabel)
//                .has("startTime", T.lte, now)
//                .has("endTime", T.gt, now)
//                .hasNot("closed", true)
//                .asInstanceOf[GremPipeVertex]
//                .iterator().toSeq
//
//        vs
//    }
//
//
//    /**
//     * override this for your custom sponsor's picker.
//     * @return
//     */
//    def pickOneSponsor(vs:Seq[Vertex]) = {
//        if (vs.length > 0){
//            val i = rnd.nextInt(vs.length)
//            Some(db.getVertex(vs(i).getId))
//        }else
//            None
//    }
//
//    /**
//     * usable for mocking on test.
//     * @return
//     */
//    def getUserCount = {
//        Stats.getCountStats.userCount
//    }
//
//
//    def getUserLimit = {
//        (getUserCount / 3).toInt
//    }
//
//
//}
//
//object SponsorPostBroadcastLive extends SponsorPostBroadcastLive
