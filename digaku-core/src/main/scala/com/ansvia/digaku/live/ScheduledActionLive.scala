/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.live

import java.util.concurrent.LinkedBlockingQueue

import com.ansvia.commons.logging.Slf4jLogger

import scala.concurrent.duration._

/**
 * Author: <PERSON> (<EMAIL>)
 *
 */
abstract class ScheduledActionLive[T] extends LiveObject with Slf4jLogger {

    val period:FiniteDuration

    case class Entry(item:T, deadline:Deadline)

    private val entries = new LinkedBlockingQueue[Entry]()
    private var inProcessing = false
    private lazy val ensureStart = start()

    protected def process(item: T)

    def run(){

        if (!inProcessing){
            inProcessing = true

            val size = entries.size()

            for (i <- 0 until size){
                val entry = entries.peek()

                val item = entry.item

                if (entry.deadline.isOverdue()){

                    process(item)

                    entries.take()
                }
            }

            inProcessing = false
        }

    }

    def register(item:T, exp:FiniteDuration){
        ensureStart
        entries.offer(Entry(item, exp.fromNow))
    }


    def registerBySeconds(item:T, seconds:Int){
        register(item, seconds.seconds)
    }

}
