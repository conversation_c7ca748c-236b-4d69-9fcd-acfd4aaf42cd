///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.popular
//
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.perf.PerfTiming
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.model._
//import com.ansvia.digaku.exc.IgnoredException
//import java.util
//import com.ansvia.digaku.utils.DateUtils
//import com.ansvia.digaku.model.Label._
//import scala.collection.JavaConversions._
//import com.ansvia.digaku.Digaku
//
///**
// * Author: andrie
// *
// */
//class PopularVideoBuilder extends DbAccess with PerfTiming with <PERSON>lf<PERSON>j<PERSON><PERSON><PERSON> {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//    import com.ansvia.digaku.model.Label.ORIGIN
//
//    var processedContents = new util.HashSet[String]()
//
//    val digakuConfig = Digaku.engine.config
//
//    def build() {
//
//        info("building popular videos...")
//
//        timing("Calculating popular videos") {
//
//            val now = Digaku.engine.dateUtils.now
//            val currentKeyIdentifier = now.getTime
//
//            db.commit()
//
//            var idx = 0L
//
//            Video.paging(None, None, 100, Some(10)) { case (page, vs) =>
//
//                vs.foreach { _v =>
//                    idx = idx + 1
//
//                    val v = db.getVertex(_v.getId)
//
//                    val isPromotedPopular =
//                        v.pipe.inE(POPULAR)
//                            .has("promoted", true)
//                            .hasNext
//
//                    try {
//                        if (v.getOrElse("_class_", "") == "com.ansvia.digaku.model.SimplePost") {
//
//                            if (isPromotedPopular)
//                                throw IgnoredException("post with id: " + v.getId +
//                                    " ignored. is already popular, this popular is promoted by admin")
//
//                            if (v.pipe.out(ORIGIN).has("privated", true).range(0, 1).iterator().hasNext)
//                                throw IgnoredException("video id: " + v.getId + " ignored due to origin is private")
//
//                            if (v.getOrElse("deleted", false))
//                                throw IgnoredException(s"video id: ${v.getId} ignored " +
//                                    "due to already deleted")
//
//                            val content = v.getOrElse("content", "")
//
//                            // untuk menghindari duplicated content
//                            if (!processedContents.contains(content.trim)){
//
//                                println("processing: " + v.getOrElse("content", ""))
//
//                                val counter = Digaku.engine.counterProvider("post-counter-" + v.getId)
//
//                                val likesCount = counter.get("likes").toInt
//                                val viewsCount = counter.get("views_count").toInt
//                                val responseCount = counter.get("response_count").toInt
//
//                                val creationTime = v.getOrElse("creationTime", 0L)
//
//                                val s = (responseCount * 15) + (likesCount * 5) + (viewsCount / 2)
//
//                                val order = math.log10(math.max(math.abs(s), 1))
//                                val sign = if (s > -1) 1 else -1
//
//                                val seconds = ((creationTime / 1000) - **********)
//                                val scoreWP:Double = order + sign.toDouble * seconds.toDouble / 45000D
//                                val score:Double = f"$scoreWP%.03f".toDouble
//
//                                val edge = Video.addPopular(v, score)
//
//                                edge.setProperty("keyIdentifierLong", currentKeyIdentifier)
//
//                                if (content.trim.length > 2)
//                                    processedContents.add(content.trim.toLowerCase)
//
//                                println(f"     -> score: $score%.03f")
//
//
//                            }
//
//
//                        }
//                    } catch {
//                        case e:IgnoredException =>
//                            debug(e.getMessage)
//                    }
//                }
//
//                db.commit()
//
//                true
//
//            }
//
//            Video.clearPopular(Popular.ExceptKeyIdentifier(currentKeyIdentifier))
//
//            db.commit()
//
//        }
//
//        info("popular videos built.")
//
//    }
//
//}
