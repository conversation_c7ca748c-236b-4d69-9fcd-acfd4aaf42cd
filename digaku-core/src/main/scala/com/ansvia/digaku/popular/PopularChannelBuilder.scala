/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

//package com.ansvia.digaku.popular
//
//import com.ansvia.digaku.model.{Group, Picture, Article}
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.perf.PerfTiming
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.persistence.CassandraDriver
//import com.ansvia.digaku.exc.IgnoredException
//import com.ansvia.digaku.web.OstrichReporter
//import com.ansvia.digaku.utils.DateUtils
//import org.apache.commons.lang.time.Digaku.engine.dateUtils
//import com.ansvia.digaku.utils.DateUtils
//import com.twitter.ostrich.stats.{Stats, StatsListener, CassandraBackedStatsConstant}
//import com.ansvia.digaku.web.OstrichReporter.DailyCassandraBackedStats
//import com.ansvia.digaku.web.OstrichReporter._
//import com.ansvia.digaku.exc.IgnoredException
//import com.twitter.ostrich.admin.ServiceTracker
//import org.apache.commons.lang.time.{Digaku.engine.dateUtils => ApacheDigaku.engine.dateUtils}
//
///**
// * Author: robin
// * Date: 4/14/14
// * Time: 11:23 AM
// *
// */
//class PopularChannelBuilder extends DbAccess with PerfTiming with Slf4jLogger {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//
//
//    private var ostrichCtx:CassandraDriver.Context = _
//
//    setupOstrich()
//
//
//
//    def calculate(){
//
//
//        //        import scala.collection.JavaConversions._
//        import com.ansvia.graph.BlueprintsWrapper._
//
//        //        var offset = 0
//        //        val limit = 100
//        var idx = 0L
//        val total = Group.getCount
//
//        Group.clearPopular()
//
//        Group.paging(None, None, 100, None){ case (page, vertices) =>
//
//            vertices.foreach { chV =>
//
//                try {
//                    if (chV.getOrElse("privated", true))
//                        throw IgnoredException()
//
//                    idx = idx + 1L
//
//                    val channelName = chV.getProperty[String]("group.lower-name")
//
//                    val progress = "%.2f%% %d/%d".format(idx.toDouble * 100D / total.toDouble, idx, total)
//
//                    printOverwrite("   " + progress + " processing " + channelName)
//
//                    // dapatkan score untuk group sejak 3 hari terakhir
//                    var score: Double = calculate(channelName, 3, total)
//
//                    if (score.isNaN) {
//                        score = 0.0
//                    }
//
//                    // update edges POPULAR
//                    //                    val popEdIter = chV.pipe.inE(POPULAR)
//                    //                    if (popEdIter.hasNext) {
//                    //                        val ed = popEdIter.next()
//                    //                        ed.setProperty("score", score)
//                    //                    } else {
//                    //                        // create
//                    //                        val ed = popularVertex --> POPULAR --> chV <()
//                    //                        ed.setProperty("score", score)
//                    //                    }
//
//                    Group.addPopular(chV, score)
//
//                    if (score > 0.0) {
//                        println("     + %s score %.2f".format(channelName, score))
//                    }
//                }
//                catch {
//                    case e:IgnoredException =>
//                }
//            }
//
//            true
//        }
//
//        //        // @TODO(robin): ganti model offset - limit ke lastTimeOrder - limit
//        //        while (Group.rootVertex.pipe.outE(Group.rootVertexLabel)
//        //            .range(offset, offset + 1).hasNext){
//        //
//        //            Group.rootVertex.pipe.out(Group.rootVertexLabel).range(offset, offset + limit).iterator()
//        //                .foreach { chV =>
//        //
//        //                idx = idx + 1
//        //
//        //                val channelName = chV.getProperty[String]("group.lower-name")
//        //
//        //                val progress = "%.2f%% %d/%d".format(idx.toDouble * 100D / total.toDouble , idx, total)
//        //
//        //                printOverwrite("   " + progress + " processing " + channelName)
//        //
//        //                // dapatkan score untuk group sejak 3 hari terakhir
//        //                var score:Double = calculate(channelName, 3, total)
//        //
//        //                if (score.isNaN){
//        //                    score = 0.0
//        //                }
//        //
//        //                // update edges POPULAR
//        //                val popEdIter = chV.pipe.inE(POPULAR)
//        //                if (popEdIter.hasNext){
//        //                    val ed = popEdIter.next()
//        //                    ed.setProperty("score", score)
//        //                }else{
//        //                    // create
//        //                    val ed = Group.rootVertex --> POPULAR --> chV <()
//        //                    ed.setProperty("score", score)
//        //                }
//        //
//        //                if (score > 0){
//        //                    println("     + %s score %.2f".format(channelName, score))
//        //                }
//        //
//        //            }
//        //
//        //            db.commit()
//        //
//        //            offset = offset + limit
//        //        }
//
//        // fix group count
//        //        popularVertex.setProperty("count", idx)
//        Group.popularRootVertex.setProperty("count", idx)
//        db.commit()
//
//
//    }
//
//
//    /**
//     * Kalkulasikan score dari group sejak :days terakhir.
//     * @param chName nama group yang akan dikalkulasikan score-nya.
//     * @param days sejak berapa hari terakhir.
//     * @return
//     */
//    private def calculate(chName:String, days:Int, factor:Double) = {
//
//        val col = OstrichReporter.cassandraCollections("public")
//
//        val key = "group." + chName + ".daily_visit"
//        val start = Digaku.engine.dateUtils.now
//        val end = ApacheDigaku.engine.dateUtils.addDays(start, -days)
//
//        col.getRange(key, start, end, 1000).collect { case ts :: count :: Nil => count }
//            .sum.toDouble * 70D / factor
//
//    }
//
//    /**
//     * Setup ostrich stats/monitoring service.
//     */
//    private def setupOstrich(){
//
//        import com.twitter.conversions.time._
//
//        val c = conf.detach("database.analytics.cassandra")
//        val cassandraHost = c("host", "127.0.0.1:9160")
//        val cassandraClusterName = c("cluster-name", "digaku")
//        val cassandraKeyspaceName = c("keyspace", "ostrich_digaku")
//        val cassandraReplicationFactor = c("replication-factor", 1)
//
//        info("ostrich cassandra using host: " + cassandraHost +
//            ", cluster name: " + cassandraClusterName +
//            ", keyspace: " + cassandraKeyspaceName +
//            ", repl-factor: " + cassandraReplicationFactor)
//
//        ostrichCtx = CassandraDriver.getContext(cassandraClusterName,
//            cassandraKeyspaceName, cassandraHost, cassandraReplicationFactor)
//
//        ostrichCtx.start()
//        ostrichCtx.ensureKeyspaceExists(cassandraKeyspaceName)
//        ostrichCtx.ensureColumnFamilyExists(CassandraBackedStatsConstant.COLUMN_FAMILY_NAME,
//            cassandraKeyspaceName,"org.apache.cassandra.db.marshal.TimeUUIDType")
//
//        val keyspace = ostrichCtx.keyspace.getClient
//
//        //        val cbs = new HourlyCassandraBackedStats("system", keyspace,1.minute, Stats)
//        val cbs2 = new DailyCassandraBackedStats("public", keyspace,1.hour, DailyStats)
//
//        StatsListener(1.minute, Stats, Nil)
//        StatsListener(1.hour, DailyStats, Nil)
//
//        //        ServiceTracker.register(cbs)
//        ServiceTracker.register(cbs2)
//
//        //        cbs.start()
//        cbs2.start()
//
//        //        OstrichReporter.cassandraCollections += (cbs.name -> cbs)
//        OstrichReporter.cassandraCollections += (cbs2.name -> cbs2)
//
//    }
//
//
//    def shutdown(){
//        if (ostrichCtx != null)
//            ostrichCtx.shutdown()
//
//        OstrichReporter.cassandraCollections.foreach(_._2.shutdown())
//    }
//}
//
//
