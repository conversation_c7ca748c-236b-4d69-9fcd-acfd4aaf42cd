/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.popular

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types.{GremPipeVertex, IDType}
import com.ansvia.digaku.event.impl.IndexAttributeUpdate
import com.ansvia.digaku.exc.IgnoredException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model.{Article, BaseModel, ForumVisibility, Popular}
import com.ansvia.digaku.persistence.CounterProviderOperator
import com.ansvia.perf.PerfTiming
import com.tinkerpop.blueprints.Vertex
import org.elasticsearch.common.joda.time.DateTime

// scalastyle:off regex.line
/**
 * Author: robin
 *
 */
class PopularArticleBuilder extends DbAccess with PerfTiming with Slf4jLogger {

    import com.ansvia.digaku.model.Label.{ORIGIN, _}
    import com.ansvia.digaku.utils.RichString._
    import com.ansvia.graph.BlueprintsWrapper._

    var processedArticles = Set[String]()

    val digakuConfig = Digaku.engine.config


    def build(){

        info("building popular articles...")

        timing("Calculating popular articles"){

            db.commit()

            val now = Digaku.engine.dateUtils.getCurrentTime().toDate
            val currentKeyIdentifier = now.getTime
            val limitTime = new DateTime(now).minusDays(7).getMillis

            var idx = 0L

            val promotedPopular = Article.getPopularArrangement().map(_.getVertex)

            // Popular article diambil dari article terbaru sampai dengan H-7
            Article.paging(None, None, 100){ case (page, vs) =>

                var nextPaging = true

                vs.foreach { _v =>
                    idx = idx + 1

                    val v = db.getVertex(_v.getId)

                    val isPromotedPopular = promotedPopular.exists(art => art.getId == v.getId)

                    try {
                        if (v.getOrElse("_class_", "") == "com.ansvia.digaku.model.Article") {

                            val title = v.getOrElse("title", "-")
                            val creationTime = v.getOrElse("creationTime", 0L)
                            val hasPopularized = v.getOrElse("popularized", false)

                            // ambil counter dari post tersebut
                            val counter = Digaku.engine.counterProvider("post-counter-" + v.getId)

                            val userWhoRateCount = counter.get("likes").toInt
                            val viewsCount = counter.get("views_count").toInt
                            val responseCount = counter.get("response_count").toInt
                            // val ratingAvgCount = counter.get("rating").toDouble

                            val it = v.pipe.out(ORIGIN).range(0, 1)
                                .asInstanceOf[GremPipeVertex].iterator()

                            if (!it.hasNext)
                                throw IgnoredException("content " + _v.getId + " has no origin")

                            val originV = it.next()

                            // Ignore, Apabila article sudah pernah menjadi popular dan pernah ditampilkan.
                            // see [[com.ansvia.digaku.model.Article.getAllPopularArticles]]
                            if (hasPopularized) {
                                throw IgnoredException("post : `" + title + "`, id: " + v.getId +
                                    " ignored, article have been popularized")
                            }

                            // Ignore, apabila lebih lama dari h-7
                            if (creationTime < limitTime) {

                                // Stop iterasi paging ketika creationTime sudah melebihi H-7
                                nextPaging = false

                                throw IgnoredException("post : `" + title + "`, id: " + v.getId +
                                    " ignored, expired article (older than h-7)")
                            }

                            // apabila group origin-nya private, ignore.
                            if (originV.getOrElse("privated", false))
                                throw IgnoredException("post :" + title + ", id: " + v.getId +
                                    " ignored due to origin is private")

                            if (v.getOrElse("deleted", false))
                                throw IgnoredException(s"article: $title, id: ${v.getId} already deleted")

                            if (isPromotedPopular)
                                throw IgnoredException("post : `" + title + "`, id: " + v.getId +
                                    " ignored is already popular, this popular is promoted by admin")

                            // untuk menghindari duplicated title
                            if (!processedArticles.contains(title.trim.toLowerCase)) {

                                println("processing: " + title.truncate(50))

//                                val containsPic = v.getOrElse("containsPic", false)
//                                val thumbUrl = v.getOrElse("thumbUrl", "")
//
//                                if (!containsPic && thumbUrl == "")
//                                    throw IgnoredException("post : `" + title + "`, id: " + v.getId +
//                                        " ignored due to not contains any pic inside")


                                if (viewsCount < 20 || userWhoRateCount < 2)
                                    throw IgnoredException("post : `" + title + "`, id: " + v.getId +
                                        " ignored. Criteria not match (views >= 20, rateCount >= 2")

                                if (userWhoRateCount < 3 && viewsCount < 5 && responseCount < 2)
                                    throw IgnoredException("post : `" + title + "`, id: " + v.getId +
                                        " ignored due to not enough parameter to calculate")

                                // jangan proses artikel yang group visibility-nya tidak menerima popular
                                val visibility = originV.getProperty[Int]("visibility")
                                if ((visibility | ForumVisibility.ON_POPULAR) != visibility){
                                    throw IgnoredException("`" + originV.getOrElse("name","???") + "` ignored due " +
                                        "to insufficient origin visibility")
                                }

                                val s = (responseCount * 15) + (userWhoRateCount * 5) + (viewsCount / 2)

                                val order:Double = math.log10(math.max(math.abs(s), 1))
                                val sign = if (s > -1) 1 else -1

                                val seconds = (creationTime / 1000) - 1134028003
                                val scoreWP:Double = order + (sign.toDouble * seconds.toDouble / 45000D)
                                val score:Double = f"$scoreWP%.03f".toDouble

                                val edge = Article.addPopular(v, score)
                                edge.setProperty("keyIdentifierLong", currentKeyIdentifier)
                                // increment popularity attribute
                                updatePopularityAttribute(v, counter)

                                processedArticles += title.trim.toLowerCase

                                println(f"     -> score: $score%.03f")
                            }

//                            if (idx % 30 == 0)
//                                db.commit()

                        }
                    }
                    catch {
                        case e:IgnoredException =>
                            debug(e.getMessage)
                    }


                }

                db.commit()

                nextPaging
            }

            Article.clearPopular(Popular.ExceptKeyIdentifier(currentKeyIdentifier))
            db.commit()

        }

        info("popular articles built.")

    }

    private def updatePopularityAttribute(v: Vertex, counter:CounterProviderOperator){
        counter.incrementBy("popularity", 3)
        v.toCC[BaseModel[IDType]].foreach { obj =>
            Digaku.engine.eventStream.emit(IndexAttributeUpdate(obj, "popularity" :: Nil))
        }
    }



    def reset(){
        processedArticles = Set[String]()
    }
}


// scalastyle:on regex.line
