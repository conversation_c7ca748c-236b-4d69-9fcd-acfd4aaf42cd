///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.popular
//
//import com.ansvia.digaku.Types.GremPipeVertex
//import com.ansvia.digaku.model.{ForumVisibility, Popular, Picture}
//import com.ansvia.digaku.helpers.DbAccess
//import com.ansvia.perf.PerfTiming
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.exc.IgnoredException
//import com.ansvia.digaku.utils.DateUtils
//import com.ansvia.digaku.database.GraphCompat.tx
//import com.ansvia.digaku.model.Label._
//import scala.collection.JavaConversions._
//import com.ansvia.digaku.Digaku
//
//
//// @TODO(robin): add unittest
///**
// * Author: robin
// *
// */
//class PopularPictureBuilder extends DbAccess with PerfTiming with Slf4jLogger {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//    import com.ansvia.digaku.model.Label.ORIGIN
//    import com.ansvia.digaku.utils.RichString._
//
//    val digakuConfig = Digaku.engine.config
//
//    def build(){
//
//        info("building popular pictures...")
//
//        timing("Calculating popular pictures"){
//
//            db.commit()
//
//            val now = Digaku.engine.dateUtils.now
//            val currentKeyIdentifier = now.getTime
//
//            var idx = 0L
//
//
//            Picture.paging(None, None, 100, Some(10)){ case (page, vs) =>
//
//                vs.foreach { _v =>
//                    idx = idx + 1
//
//                    try {
//
//                        tx { t =>
//                            val v = t.getVertex(_v.getId)
//
//                            val isPromotedPopular =
//                                v.pipe.inE(POPULAR)
//                                    .has("promoted", true)
//                                    .hasNext
//
//                            assert(v != null, s"cannot get vertex ${_v.getId}, vertex is null")
//
//                            if (v.getOrElse("_class_", "") == "com.ansvia.digaku.model.Picture") {
//
//                                val title = v.getOrElse("title", "-")
//
//                                if (isPromotedPopular)
//                                    throw IgnoredException("post with id: " + v.getId +
//                                        " ignored. is already popular, this popular is promoted by admin")
//
////                                // check hanya apabila origin group tidak private
////                                if (v.pipe.out(ORIGIN).has("privated", true).range(0, 1).iterator().hasNext)
////                                    throw IgnoredException("pic :" + title + ", id: " + v.getId +
////                                        " ignored due to origin is private")
//
//                                val it = v.pipe.out(ORIGIN).range(0, 1)
//                                    .asInstanceOf[GremPipeVertex].iterator()
//
//                                if (!it.hasNext)
//                                    throw IgnoredException("content " + _v.getId + " has no origin")
//
//                                val originV = it.next()
//
//                                // apabila group origin-nya private, ignore.
//                                if (originV.getOrElse("privated", false))
//                                    throw IgnoredException("post :" + title + ", id: " + v.getId +
//                                        " ignored due to origin is private")
//
//
//                                if (v.getOrElse("deleted", false))
//                                    throw IgnoredException(s"pic: $title, id: ${v.getId} ignored " +
//                                        "due to picture already deleted")
//
//                                // jangan proses picture yang group visibility-nya tidak menerima popular
//                                val visibility = originV.getProperty[Int]("visibility")
//                                if ((visibility | ForumVisibility.ON_POPULAR) != visibility){
//                                    throw IgnoredException("`" + originV.getOrElse("name","???") + "` ignored due " +
//                                        "to insufficient origin visibility")
//                                }
//
//                                println(idx + ". processing: " + title.truncate(50))
//
//                                val counter = Digaku.engine.counterProvider("picture-counter-" + v.getId)
//
//                                val likesCount = counter.get("likes").toInt
//                                val viewsCount = counter.get("views_count").toInt
//                                val responseCount = counter.get("response_count").toInt
//
//                                val creationTime = v.getOrElse("creationTime", 0L)
//
//                                val s = (responseCount * 15) + (likesCount * 5) + (viewsCount / 2)
//
//                                val order = math.log10(math.max(math.abs(s), 1))
//                                val sign = if (s > -1) 1 else -1
//
//                                val seconds = ((creationTime / 1000) - **********)
//                                //                            val score:Double = order + sign.toDouble * seconds.toDouble / 45000D
//
//                                val scoreWP:Double = order + sign.toDouble * seconds.toDouble / 45000D
//                                val score:Double = f"$scoreWP%.03f".toDouble
//
//                                val edge = Picture.addPopularTx(v, score, t)
//
//                                edge.setProperty("keyIdentifierLong", currentKeyIdentifier)
//
//                                println(f"   -> score: $score%.03f")
//
//                            }
//
//                        }
//
//                    }
//                    catch {
//                        case e:IgnoredException =>
//                            debug(e.getMessage)
//                    }
//
//                }
//
//                db.commit()
//
//                true
//            }
//
//            // hapus yang lama/selain hasil kalkulasi ini
//            Picture.clearPopular(Popular.ExceptKeyIdentifier(currentKeyIdentifier))
//
//            db.commit()
//
//        }
//
//        info("popular pictures built.")
//
//    }
//}
//
//
