///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.ml
//
//import org.apache.commons.io.IOUtils
//
//
//
//object StopWords {
//
//    private lazy val STOP_WORDS = {
//        IOUtils.toString(getClass.getClassLoader.getResourceAsStream("stop-words.txt"))
//            .split(" ").toSeq.filter(_.trim.length > 1)
//    }
//
//    /**
//     * Check is word is listed as stop-words.
//     * @param word word to check.
//     * @return true or false
//     */
//    def contains(word:String): Boolean = STOP_WORDS.contains(word)
//
//    /**
//     * Filter out words (listed as stop-words) from text.
//     * @param text text to filter out.
//     * @return filtered text.
//     */
//    def filter(text:String): String = {
//        text.split("\\s+").map(_.trim)
//            .filter(t => t.length > 0 && !STOP_WORDS.contains(t.toLowerCase)).mkString(" ")
//    }
//
//
//}
