///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.ml
//
///**
// * Author: robin (<EMAIL>)
// *
// */
//object TextNormalizer {
//
//    private val STRIP_RE = """\W+|\d\d\d\d+""".r
//    private val TRIMMER_RE = """^[\-_=]|[\-_=]$""".r
//    private val USER_NAME_REGEX = """(?<![\w#\!@\$%\^&\*\(\)\{\}\-\+\/\\\|<>\=])(@(?:[\w\.^\s\-])*?)(?![\w\.#@\$%\^&\*\(\)\{\}\-\+\/\\\|<>\=])""".r
//    private val QUOTE_RE = """\[quote.*?\].*?\[/quote\]""".r
//    private val SHORTEN_SPACE_RE = "\\s{2}+".r
//
//    /**
//     * Ini akan men-strip user name, long numeric, dan men-trim-nya.
//     * @param text text to normalize.
//     * @return
//     */
//    def apply(text:String) = {
//        SHORTEN_SPACE_RE.replaceAllIn(
//            TRIMMER_RE.replaceAllIn(STRIP_RE.replaceAllIn(StopWords.filter(USER_NAME_REGEX.replaceAllIn(QUOTE_RE.replaceAllIn(text, ""), "")), " "), "").trim,
//            " "
//        ).trim
//    }
//
//}
