/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import com.ansvia.digaku.exc.InvalidParameterException

/**
 * Author: robin
 * Date: 3/6/13
 * Time: 10:05 PM
 * 
 */
object PostMarkValidator {

    private val NAME_REGEX = """[a-zA-Z0-9\-\_\s]+""".r
    private val COLOR_REGEX = """#[a-fA-F0-9]{6}|#[a-fA-F0-9]{3}""".r

    /**
     * Mengembalikan true apabila post mark valid,
     * false apabila tidak valid
     * @param name post mark name
     * @param color post mark color
     * @return
     */
    def isValid(name:String, color:String):Boolean = {
        isValidName(name) && isValidColor(color)
    }

    /**
     * Mengembalikan true apabila post mark name valid,
     * false apabila tidak valid
     * @param name
     * @return
     */
    def isValidName(name:String) =
        (name.trim.length > 1 && name.trim.length < 20 && NAME_REGEX.pattern.matcher(name).matches())

    /**
     * Mengembalikan true apabila color post mark valid,
     * false apabila tidak valid
     * @param color
     * @return
     */
    def isValidColor(color:String) =
        (COLOR_REGEX.pattern.matcher(color).matches())

    /**
     * Mengembalikan error apabila post mark tidak valid,
     * error: [[com.ansvia.digaku.exc.InvalidParameterException]]
     * @param name
     * @param color
     */
    def validate(name:String, color:String){
        if(!isValidName(name))
            throw InvalidParameterException("Invalid name, name min 2 characters and max 20 characters, " +
                "only accept alphabet, numeric, space, dash, and underscore.")

        if(!isValidColor(color))
            throw InvalidParameterException("Invalid color format, use CSS style formatting, i.e: #CA8877")

    }
}
