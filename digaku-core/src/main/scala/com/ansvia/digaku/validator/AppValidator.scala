/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator


object AppValidator {
    val goodNameRe = """^[a-zA-Z0-9][\w\s\._\-&]+$""".r

    /**
     * Untuk memeriksa apakah nama App valid
     * mengembalikan true apabila valid, sebaliknya false.
     * @param name nama yang akan dicheck.
     * @return
     */
    def validName(name:String):Boolean={
        if (name.length > 100)
            false
        else if (name.length < 3)
            false
        else
            goodNameRe.pattern.matcher(name).matches()
    }
}
