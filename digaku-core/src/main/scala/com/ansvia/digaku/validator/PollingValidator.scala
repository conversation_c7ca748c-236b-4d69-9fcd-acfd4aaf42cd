/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import com.ansvia.digaku.exc.InvalidParameterException

/**
 * Author: nadir, robin
 *
 */
object PollingValidator {

    /**
     * Mengembalikan true jika title polling valid,
     * false jika tidak valid
     * @param title Polling title yang akan dicheck
     * @return
     */
    def validTitle(title:String):Boolean = {
        title.trim.length > 1 && title.trim.length <= 160
    }

    /**
     * Memberikan error apabila polling title tidak valid
     * error: [[com.ansvia.digaku.exc.InvalidParameterException]]
     * @param title
     */
    def validateTitle(title:String){
        if(!validTitle(title))
            throw InvalidParameterException("Invalid polling title, title min 1 character and max 300 characters.")
    }
}
