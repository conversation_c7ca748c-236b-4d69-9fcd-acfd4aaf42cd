/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import org.apache.commons.validator.routines
import com.ansvia.digaku.exc.InvalidParameterException

/**
 * Author: robin
 * Date: 2/24/13
 * Time: 6:57 PM
 * 
 */
object UrlValidator {

    private lazy val uv = new routines.UrlValidator(Array("http", "https"), routines.UrlValidator.ALLOW_ALL_SCHEMES)

    /**
     * Mengembalikan true apabila url valid,
     * false jika tidak valid
     * @param url url yang akan di check
     * @return
     */
    def isValid(url:String):Boolean = {
        // Replace double slash with single slash
        val rv = url.replaceAll("""(?<!https?:)//""", "/")
        uv.isValid(rv)
    }

    /**
     * Mengembalikan error jika url tidak valid
     * error: [[com.ansvia.digaku.exc.InvalidParameterException]]
     * @param url
     */
    def validate(url:String){
        if (!isValid(url))
            throw InvalidParameterException("Url not valid: " + url)
    }
}
