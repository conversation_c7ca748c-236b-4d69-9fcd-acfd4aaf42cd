/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

object ChannelValidator {

//    private lazy val CHANNEL_NAME_RE = """^[a-zA-Z][a-zA-Z0-9\-\._]*?[^\.\-\W]$""".r
    val MAX_DESC_LENGTH = 500



    /**
     * Mengembalikan true jika group name valid,
     * false jika group name tidak valid
     * @param name group name
     * @return
     */
    def validName(name: String): Boolean = {
        name.length > 2 && name.length <= 160 //&&
//              CHANNEL_NAME_RE.pattern.matcher(name).matches() &&
//            !restrictedNames.contains(name.toLowerCase)
    }

    /**
     * Mengembalikan true jika group desc valid,
     * false jika group desc tidak valid
     * @param desc
     * @return
     */
    def validDescription(desc: String): Boolean = {
        desc.length <= MAX_DESC_LENGTH
    }
}

