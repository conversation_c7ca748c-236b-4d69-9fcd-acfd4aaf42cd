/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import com.ansvia.digaku.exc.InvalidParameterException
import java.util.Hashtable
import javax.naming.directory.InitialDirContext
import org.apache.commons.io.IOUtils
import com.ansvia.commons.logging.Slf4jLogger

/**
 * Author: robin
 *
 */
object EmailValidator extends Slf4jLogger {

//    private val EMAIL_REGEX = """^[_a-zA-Z0-9-]+(\.[_a-zA-Z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,6})$""".r

    // Pengecualian untuk validator email yang menggunakan @intra.bca harusnya di-valid-kan.
    private val EXCEPTED_EMAIL_RE = """(?i)^[_a-zA-Z0-9-]+(\.[_a-zA-Z0-9-]+)*@(intra\.bca)$""".r

    lazy val PUBLIC_EMAIL_PROVIDER_DOMAIN = {
        IOUtils.toString(getClass.getClassLoader.getResourceAsStream("public-email-domain.txt")).split("\n")
            .toSeq.filter(_.trim.length > 1)
    }
    lazy val PUBLIC_EMAIL_PROVIDER_DOMAIN_RE = {
        IOUtils.toString(getClass.getClassLoader.getResourceAsStream("public-email-domain-re.txt"))
            .split("\n").toSeq.filter(_.trim.length > 1)
    }

    /**
     * set ini ke `false` untuk skip mx lookup checking
     * biasanya digunakan di unittest dan development mode
     * tanpa koneksi internet.
     */
    var mxRecordCheck = true

    // using apache lib instead.
    private lazy val emailValidator = {
        org.apache.commons.validator.routines.EmailValidator.getInstance()
    }

    /**
     * Mengembalikan true apabila email valid
     * atau false apabila tidak valid.
     * @param email alamat email yang akan dicheck.
     */
     def isValid(email:String, noMxLookup:Boolean=false) = {
        // Email dengan @intra.bca akan di-valid-kan
        val emailValid = emailValidator.isValid(email) || EXCEPTED_EMAIL_RE.pattern.matcher(email).matches()

        if(emailValid) {
            if (mxRecordCheck && !noMxLookup) {
                email.split("@").lastOption.exists(dns =>
                    mxLookup(dns))
            } else {
                true
            }
        } else
            false
    }

    /**
     * Memunculkan error apabila email tidak valid.
     * error: [[com.ansvia.digaku.exc.InvalidParameterException]]
     * @param email
     */

    def validate(email:String, noDnsCheck:Boolean=false){
        if (!isValid(email, noDnsCheck))
            throw InvalidParameterException("Please enter a valid email address.")
    }

    /**
     * Akan me-normalisasi email termasuk issue gmail dot
     * semua return akan di-lower-case-kan.
     * @param email email yang akan dinormalisasi.
     * @return
     */
    def normalizedEmail(email:String) = {
        // normalize gmail for dot dot

        email.split("@").toList match {
            case name :: domain :: Nil => {
                domain.toLowerCase match {
                    case "gmail.com" =>
                        (name.replaceAll("\\.", "") + "@" + domain).toLowerCase
                    case _ =>
                        email.toLowerCase
                }
            }
            case _ =>
                email.toLowerCase
        }
    }


    /**
     * digunakan untuk cek mail servers
     * pada sebuah DNS
     * @param dns
     * @return ketika DNS name not found return false
     *         atau ketika tidak domain tidak memiliki mail servers return false
     */
    def mxLookup(dns:String):Boolean ={
        try {
            val env = new Hashtable[String, String]
            env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory")
            val attr = new InitialDirContext(env).getAttributes(dns, Array[String]{"MX"}).get("MX")
            if (attr != null)
                attr.size() > 0
            else
                false
        } catch {
            case e:Exception =>
                debug(e.getMessage)
                false
        }
    }

    /**
     * Dapatkan domain dari email address.
     * @param email email yang akan didapatkan domainnya.
     * @return
     */
    def getDomain(email:String) = {
        validate(email, noDnsCheck = true)
        email.split("@").apply(1)
    }


    def isPublicEmailProvider(email:String) = {
        isPublicEmailDomain(getDomain(email))
    }

    private lazy val PUBLIC_EMAIL_PROVIDER_DOMAIN_RE_C = PUBLIC_EMAIL_PROVIDER_DOMAIN_RE.map { line =>
        if (line.contains("//")){
            line.split("//").apply(0).trim
        }else
            line
    }.mkString("|").r

    /**
     * Untuk memeriksa apakah suatu email ada public domain atau bukan,
     * email public domain bisa seperti: gmail, yahoo, hotmail, dll.
     *
     * Lawan dari public domain adalah private domain, seperti misalnya
     * mindtalk.com, ansvia.com.
     *
     * @param emailDomain email domain yang akan di-check.
     * @return
     */
    def isPublicEmailDomain(emailDomain:String) = {
        if (!PUBLIC_EMAIL_PROVIDER_DOMAIN.contains(emailDomain.toLowerCase)){
            PUBLIC_EMAIL_PROVIDER_DOMAIN_RE_C.pattern.matcher(emailDomain).matches()
        }else
            true
    }

}
