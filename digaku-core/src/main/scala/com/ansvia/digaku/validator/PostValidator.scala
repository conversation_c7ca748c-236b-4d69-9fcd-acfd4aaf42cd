/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

object PostValidator {

    /**
     * Mengembalikan true apabila Question title
     * valid, false jika tidak valid
     * @param title
     * @return
     */
    def validQuestionTitle(title:String):Boolean={
        title.length <= 100 && title.length >= 3
    }

    /**
     * Mengembalikan true apabila Article title
     * valid, false jika tidak valid
     * @param title
     * @return
     */
    def validArticleTitle(title:String):Boolean={
        title.trim.length <= 160 && title.trim.length >= 3
    }

    /**
     * Mengembalikan true apabila content post
     * valid, false jika tidak valid
     * @param content
     * @return
     */
    def isValidSimpleContent(content:String):Boolean={
        content.trim.length <= 160 && content.trim.length >= 3
    }



}

