/*
 * Copyright (c) 2013-2017 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import com.ansvia.digaku.exc.InvalidParameterException

/**
 * Author: nadir (<EMAIL>)
 */

object UserGroupValidator {
    private lazy val USER_GROUP_NAME_RE = """^[a-zA-Z][a-zA-Z0-9\-\._\s]*?[^\.\-\W]$""".r

    /**
     * Check valid user group name
     * @param name user group name
     * @return true if user group name is valid
     */
    def validName(name:String):Boolean = {
        name.length > 1 && name.length <= 25 &&
            USER_GROUP_NAME_RE.pattern.matcher(name).matches()
    }

    /**
     * Validate user group name
     * it's will thrown InvalidParameterException if group name is not valid
     * @param name group name
     */
    def validateName(name:String):Unit = {
        if (!validName(name))
            throw InvalidParameterException("Group name not valid, min 2 and max 25 characters and not support special character.")
    }
}
