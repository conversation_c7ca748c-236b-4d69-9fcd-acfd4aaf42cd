/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.validator

import com.ansvia.digaku.exc.InvalidParameterException
import java.text.SimpleDateFormat
import java.util.{Calendar, Date}
import com.ansvia.digaku.model.User
import com.ansvia.commons.logging.Slf4jLogger

/**
 * Author: nadir
 * Date: 1/3/13
 * Time: 3:13 PM
 *
 */
object UserValidator extends Slf4jLogger {

    private val USER_NAME_RE = """^[a-zA-Z][a-zA-Z0-9\._]+$""".r
    private val LOCATION_RE = """^[a-z0-9]+[a-z0-9\.,/\s]+$""".r
    private val BIRTHDATE_RE = """\d\d/\d\d/\d{4}""".r
    private val MOBILE_PHONE_RE = """^([0-9\(\)\/\+ \-]*)$""".r

    private lazy val restrictedNames = Seq(
        "sys", "system", "batman", "hitler",
        "god", "tuhan", "dementor", "emo", "emoticon",
        "rss", "atom", "api", "rest", "root", "service",
        "web-internal", "web", "internal", "post", "group",
        "ch", "p", "channels"

        /**
         * dummy tidak bisa di-set di-sini karena
         * digunakan di User.create.
         * implementasi di layer atas-nya.
         */
        //"dummy"
    )

    /**
     * Check apakah nama user valid atau tidak.
     * @param name
     * @return
     */
    def validName(name:String):Boolean={
        USER_NAME_RE.pattern.matcher(name).matches() &&
            !restrictedNames.contains(name.toLowerCase) &&
            (name.length > 2 && name.length < 25)
    }

    /**
     * Check apakah format alamat/lokasi sudah benar atau belum.
     * @param location
     * @return
     */
    def validLocation(location:String):Boolean={
        LOCATION_RE.pattern.matcher(location).matches()
    }


    /**
     * Check apakah birthdate valid.
     * contoh valid birthdate: 01/05/1986 <-- TANGGAL / BULAN / TAHUN.
     * selain format itu tidak valid.
     * contoh tidak valid: 01/05/86
     * @param dateStr
     * @return
     */
    def validBirthDate(dateStr:String):Boolean = {
        if (BIRTHDATE_RE.pattern.matcher(dateStr).matches()){
            // check using formatter
            try {
                User.BIRTH_DATE_FORMAT.parseDateTime(dateStr)
                true
            }
            catch {
                case e:org.joda.time.IllegalFieldValueException =>
                    warn(e.getMessage)
                    false
            }
        }else
            false
    }

    def ageValidate(dateStr:String):Boolean = {
        if(validBirthDate(dateStr)) {
            val cal = Calendar.getInstance()

            val sdf = new SimpleDateFormat("dd/MM/yyyy")

            cal.setTime(sdf.parse(dateStr))

            cal.add(Calendar.YEAR, 13)

            val availableReg = cal.getTime

            !availableReg.after(new Date)

        } else
            false
    }

    /**
     * cek apakah mobile phone valid.
     * @param mobilePhone
     * @return
     */
    def validMobilePhone(mobilePhone:String):Boolean = {
        MOBILE_PHONE_RE.pattern.matcher(mobilePhone).matches()
    }

    /**
     * Sama seperti [[com.ansvia.digaku.validator.UserValidator.validateName(name)]]
     * bedanya ini akan mentriger exception apabila tidak valid.
     * @param name
     */
    def validateName(name:String){
        if (!validName(name))
            throw InvalidParameterException("Username not valid: " + name + ", min 3 and max 25 characters and not allowed space and special character")
    }

    /**
     * Sama seperti [[com.ansvia.digaku.validator.UserValidator.validateLocation(location)]]
     * bedanya ini akan mentriger exception apabila tidak valid.
     * @param location
     */
    def validateLocation(location:String){
        if (!validLocation(location)){
            throw InvalidParameterException("Invalid location format. Use like this: Indonesia/Jawa Tengah/Wonosobo")
        }
    }

    /**
     * Sama seperti [[com.ansvia.digaku.validator.UserValidator.validBirthDate(date)]]
     * bedanya ini akan mentriger exception apabila tidak valid.
     * @param dateStr
     */
    def validateBirthDate(dateStr:String){
        if (!validBirthDate(dateStr))
            throw InvalidParameterException("Invalid birth date: `" + dateStr +
                "`, use this format: day/month/year example: 01/05/1986")

        if (!ageValidate(dateStr))
            throw InvalidParameterException("Sorry, we are not able to process your registration. Minimum age 13 years.")

    }

    /**
     * Sama seperti [[com.ansvia.digaku.validator.UserValidator.validMobilePhone(date)]]
     * bedanya ini akan mentriger exception apabila tidak valid.
     * @param mobilePhone
     */
    def validateMobilePhone(mobilePhone:String){
        if (!validMobilePhone(mobilePhone))
            throw InvalidParameterException("Mobile phone not valid: " + mobilePhone + ", Please enter a valid mobile phone")
    }
}
