/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.trophy

import com.ansvia.digaku.model.Trophy

/**
 * Author: ubai, robin
 *
 */
abstract class CustomTrophy(trophyName:String, description:String) extends Trophy(trophyName, description) {

    this.autoAssign = true


    /**
     * implement ini supaya bisa set trophy name bisa dari model Trophy
     * @param trophyName
     */
    def setTrophyName(trophyName:String)

    def setDescription(description:String)

}
