/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.trophy

import com.ansvia.digaku.Types.{GremPipeVertex, GraphType}
import com.ansvia.digaku.dao.DaoBase
import com.ansvia.digaku.exc.PermissionDeniedException
import com.ansvia.digaku.model.Label._
import com.ansvia.digaku.model.{Trophy, Responable, User}
import com.ansvia.digaku.trophy.MC2Trophy.RuleParam
import com.ansvia.graph.annotation.Persistent
import com.tinkerpop.blueprints.Vertex
import com.ansvia.graph.gremlin._
import com.ansvia.graph.BlueprintsWrapper._
import scala.collection.mutable.ListBuffer
import scala.collection.JavaConversions._

/**
 * Author: robin, fajr
 *
 */
case class MC2Trophy(var name:String, var description:String) extends CustomTrophy(name, description) {

    @Persistent
    var calcAttribute:String = ""

    var calcRange:(Int, Int) = (0,0)


    /**
     * implement ini supaya bisa set trophy name bisa dari model Trophy
     * @param trophyName
     */
    def setTrophyName(trophyName: String) {
        this.name = trophyName
    }

    def setDescription(description: String) {
        this.description = description
    }

    def withAttribute(name:String) = {
        calcAttribute = name
        this
    }

    def withRange(range:(Int, Int)) = {
        calcRange = range
        this
    }

    def withAutoAssign(state:Boolean) = {
        autoAssign = state
        this
    }

    def setInactive(state:Boolean) = {
        inactive = state
        this
    }

    /**
     * digunakan untuk mendapatkan trophy yang didapat oleh user dan juga
     * me-replace object trophy ini(trophy yang lebih tingi dari trophy ini).
     * @param user
     * @return
     */
    def getReplacerTrophies(user: User):Iterator[Trophy] ={
        user.getVertex.pipe.out(HAS_TROPHY).filter { (v: Vertex) =>
            v.getOrElse("trophy.replaced", "").split(",").contains(this.getId.toString)
        }.iterator().flatMap(_.toCC[Trophy])
    }

    /**
     * digunakan untuk menambahkan trophy ke user
     * @param user
     */
    override def forceGiveTo(user:User, giver:Option[User]=None): Unit = {
        // jangan tambahkan trophy ketika user sudah mendapatkan trophy yang lebih tinggi dari pada
        // object trophy ini (trophy yang akan di tambahkan).
        if (!getReplacerTrophies(user).hasNext){
            super.forceGiveTo(user, giver)
        }
    }

    /**
     * digunakan untuk check apakah user dapat menerima trophy ini atau tidak
     * true ketika bisa menerima trophy
     * @param user user yang akan ditest apakah dapat menerima.
     * @return
     */
    override def canReceive(user: User): Boolean = {
        var rv = false

        calcAttribute match {
            case "reputation" =>
                rv = calcRange._1 <= user.getReputationCount && calcRange._2 > user.getReputationCount

            case "articles" =>
                val count = user.getArticleCount //.getCounter.get(User.CounterKeys.articleCountKey).toInt
                rv = calcRange._1 <= count && calcRange._2 > count

            case "rate" =>
                rv = calcRange._1 <= user.getCounter.get("rate-count") && calcRange._2 > user.getCounter.get("rate-count")

            case "responses" =>
                rv = calcRange._1 <= user.getReplyCount && calcRange._2 > user.getReplyCount

            case "likes" =>
                rv = calcRange._1 <= user.getCounter.get("got_likes_count") && calcRange._2 > user.getCounter.get("got_likes_count")

            case "posts" =>
                //jumlah post threads dan reply nya
//                val threadCount = user.getArticleCount
//                val responseCount = user.getCounter.get(Responable.responseCountKey)
                val totalPost = user.getPostCount
                rv = calcRange._1 <= totalPost && calcRange._2 > totalPost

            case "pm" =>
                rv = calcRange._1 <= user.getCounter.get("chats") && calcRange._2 > user.getCounter.get("chats")

            case x =>
                if (MC2Trophy.rulesParsers.nonEmpty)
                    rv = MC2Trophy.compiledRulesParser(RuleParam(x, this, user))
        }

        if (rv)
            return rv


        false
    }

    override def __save__(v: Vertex){
        super.__save__(v)
        v.setProperty("calcRange", "%d,%d".format(calcRange._1, calcRange._2))

        val s = name.split(" ")
        val normIconName = (s.slice(1, s.length).mkString(" ").trim + " " + s.head)
            .trim.replaceAll("\\W+", "-")
            .trim.toLowerCase

        v.setProperty("icon", normIconName)
        icons = normIconName
    }

    override def __load__(vertex: Vertex): Unit ={
        super.__load__(vertex)
        val rv = vertex.getProperty[String]("calcRange")
        val s = rv.split(",")
        calcRange = (s(0).toInt, s(1).toInt)
    }
}

object MC2Trophy extends DaoBase[GraphType, MC2Trophy] {

    override val ROOT_VERTEX_CLASS: String = "com.ansvia.digaku.model.MC2TrophyRootVertex"

    case class RuleParam(attribute:String, trophy:MC2Trophy, user:User)

    type RulesParser = PartialFunction[RuleParam, Boolean]

    /**
     * mutate this list buffer if you want to extend
     * custom rules parser.
     */
    // once run, modification on this variable is useless
    @volatile var rulesParsers = ListBuffer[RulesParser]()


    lazy val compiledRulesParser = {
        if (rulesParsers.isEmpty)
            rulesParsers.append({case (x:RuleParam) => false}) // to prevent reduce error when empty
        rulesParsers.reduce(_ orElse _)
    }

}

