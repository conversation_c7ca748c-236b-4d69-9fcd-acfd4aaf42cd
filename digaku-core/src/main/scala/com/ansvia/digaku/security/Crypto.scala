/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.security

import org.apache.commons.codec.digest.DigestUtils

/**
 * Author: nadir
 * Date: 1/3/13
 * Time: 2:11 PM
 *
 */
object Crypto {
    /**
     * generate md5
     * @param text text to generate md5
     * @return
     */
    def md5(text:String):String = {
        DigestUtils.md5Hex(text)
    }

    /**
     * generate sha1
     * @param text text to generate
     * @return
     */
    def sha1(text:String):String = {
//        DigestUtils.shaHex(text)
        DigestUtils.sha1Hex(text)
    }

    /**
     * encrypt crc32
     * @param text text to encrypt
     * @return
     */
    def crc32(text:String):Long = {
        val crc32 = new java.util.zip.CRC32()
        crc32.reset()
        crc32.update(text.getBytes)
        crc32.getValue
    }
}
