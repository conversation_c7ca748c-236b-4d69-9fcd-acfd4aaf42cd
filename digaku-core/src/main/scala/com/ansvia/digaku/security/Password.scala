/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.security

import com.ansvia.digaku.exc.DigakuException

/**
 * Author: nadir, robin
 * Date: 1/3/13
 * Time: 2:13 PM
 *
 */
object Password {


//    private val DEFAULT_SALT = "NDBjMmE4MGQwMzI3Y2RkMzY4ODUyZWU4N2Y0MWYyZDgwOWU2NWM1OQ=="
    private val PASSV_2_SALT = "e6cbf8e53e5afbf2a1262f64fb8d056239a7e3816df3f6a338406157b32a1bbb4100f1bd2a068c2a4171a17d2cd424680973d93b3a6d5e775ec926cc07274e44"
    private val PASS_RE = """^[\w\.\,\"\'\?\!\;\:\#\$\%\&\(\)\*\+\-\/\<\>\=\@\[\]\\\^\_\{\}\|\~\`]+$""".r

    /**
     * generate pass
     * @param prefix prefix
     * @return
     */
    def generate(prefix:String=""):String = {
//        println(Crypto.crc32(System.currentTimeMillis().toString).toString)
        prefix + new sun.misc.BASE64Encoder()
            .encode(Crypto.crc32(System.currentTimeMillis().toString).toString.getBytes)
            .replaceAll("=","")
            .substring(0, 6)
    }

    /**
     * Encrypt plain password.
     * @param password password to encrypt.
     * @return
     */
    def encrypt(password:String):String = {
        BCrypt.hashpw(password, BCrypt.gensalt())
    }

    /**
     * Check is password match for given encrypted password.
     * @param password password to check.
     * @param hashed encrypted password.
     * @return
     */
    def isMatch(password:String, hashed:String, passV:Int):Boolean = {
        passV match {
            case 3 =>
                BCrypt.checkpw(password, hashed)

            case 2 =>
                Crypto.sha1(password + PASSV_2_SALT) == hashed

            case x =>
                throw new DigakuException("Unknown passv: " + x)
        }
    }

    private val WEAK_PASSWORDS = Seq(
        "123", "1234", "12345",
        "abc", "abc123", "asdf",
        "aoeu"
    )

    def isStrong(password:String):Boolean = {
        password.length > 4 &&
        !WEAK_PASSWORDS.contains(password)
    }

    def validStrong(password:String){
        if (!isStrong(password))
            throw new DigakuException("Weak password, try another one.")
    }

    def validFormat(password:String):Boolean = {
        PASS_RE.pattern.matcher(password).matches()
    }

    def validate(password:String) {
        if (!isStrong(password))
            throw new DigakuException("Weak password, try another one.")

        if (!validFormat(password))
            throw new DigakuException("Invalid format password.")

    }

}

