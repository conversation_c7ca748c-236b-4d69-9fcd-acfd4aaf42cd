/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.algo

import com.ansvia.digaku.algo.MathHelpers.log2
import scala.math.{ceil, pow}

/**
 * Author: robin
 *
 * Algoritma point leveling, sama dengan yang di digaku-core versi Python
 *
 */
object PointLevel {

    /**
     * Kalkulasikan level berdasarkan points.
     * @param points points.
     * @return
     */
    def calculateLevel(points:Int): Int = {
        (log2( (points + 60).toFloat / 60 ) * 4).toInt
    }

    /**
     * kalkuasi untuk menghitung point yang dibutuhkan untuk level tertentu
     * perhitungan ini kebalikannya dari calculateLevel
     * @param level
     * @return points untuk level
     */
    def calculatePoints(level:Int): Int = {
        (ceil(60 * pow(2, level.toFloat / 4)) - 60).toInt
    }

    /**
     * kalkulasi untuk Mendapatkan poin yang dibutuhkan
     * untuk naik ke level ( n+1 ) dari sebuah level ( n )
     * kalkukasi ini point dari (level + 1) di kurangi point level
     * @param level
     * @return point yang dibutuhkan untuk menuju level selanjutnya
     */
    def calculateNextLevelPoints(level:Int): Int = {
        calculatePoints(level + 1) - calculatePoints(level)
    }

    /**
     * kalkulasi untuk mendapatkan Percentage Progress to Next level
     * example point = 15 :
     * point 15 = level 1 (level saat ini)
     * level 1 = 12 point
     * level 2 = 25 point
     * toNextLevelPoint = 25 - 12 = 13
     * userToNextLevelPoint = 15 - 12 = 3
     * jadi :
     * (toNextLevelPoint / userToNextLevelPoint) * 100
     * (3 / 13) * 100 = 23 % untuk ke level selanjutnya.
     * @param points
     * @return percentage next level
     */
    def calculatePercentageNextLevel(points:Int): Int = {
        math.floor(100 * (points - calculatePoints(calculateLevel(points))).toFloat /
            calculateNextLevelPoints(calculateLevel(points))).toInt
    }

}
