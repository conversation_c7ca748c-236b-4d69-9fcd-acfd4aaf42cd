/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.algo

/**
 * Author: robin
 *
 */
object MathHelpers {

    private val _logBase2 = scala.math.log(2)

    /**
     * Log basis 2 helper.
     * @param d input value.
     * @return
     */
    def log2(d:Double): Double = {
        scala.math.log(d) / _logBase2
    }


}
