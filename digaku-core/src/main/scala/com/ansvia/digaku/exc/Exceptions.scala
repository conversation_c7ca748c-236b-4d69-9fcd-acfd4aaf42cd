/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.exc


class DigakuException(msg: String) extends Exception(msg)

case class InvalidParameterException(msg: String) extends DigakuException(msg)

case class InvalidStateException(msg: String) extends DigakuException(msg)

case class NotImplementedException(msg: String) extends DigakuException(msg)

case class UnsupportedException(msg: String) extends DigakuException(msg)

case class AlreadyExistsException(msg: String) extends DigakuException(msg)

case class NotExistsException(msg: String) extends DigakuException(msg)

case class NotSupportedException(msg: String = "not supported") extends DigakuException(msg)

case class LimitationReachedException(msg: String) extends DigakuException(msg)

case class PermissionDeniedException(msg: String) extends DigakuException(msg)

case class UnauthorizedException(msg: String) extends DigakuException(msg)

case class IgnoredException(msg: String = "ignored") extends DigakuException(msg)

case class MaintenanceModeException(msg: String = "Not supported in maintenance mode") extends DigakuException(msg)

case class ConflictException(msg: String) extends DigakuException(msg)

case class RestrictedException(msg: String) extends DigakuException(msg)

case class ResourceNotReadyException(msg: String) extends DigakuException(msg)

case class CorruptedException(msg: String) extends DigakuException(msg)

// @TODO(fajr): taruh di project digaku-restapi-v2
case class BadRequestException(msg: String) extends DigakuException(msg)

case class BiometricException(code: Int, msg: String) extends DigakuException(msg)
