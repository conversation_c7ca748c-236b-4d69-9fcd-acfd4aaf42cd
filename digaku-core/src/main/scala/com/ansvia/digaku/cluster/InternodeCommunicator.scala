/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.cluster


/**
 * Author: robin (<EMAIL>)
 */


trait InternodeMessageHandler {
    def handleInternodeMessage(initiator:String, payload:Array[Byte])
}

trait InternodeCommunicator {

    /**
     * Send payload to specific destination node using address.
     * @param addr node address where payload should send to,
     *             this is node id set in configuration file (id).
     *
     * @param payload payload to send in bytes.
     */
    def send(addr:String, payload:Array[Byte])

    /**
     * Send payload to specific group or all nodes.
     * @param prefix node id prefix, eg: "api-",
     *               or for broadcast to all nodes use wildcard "*" as the prefix.
     *
     *
     * @param payload payload to send in bytes.
     */
    def sendPrefix(prefix:String, payload:Array[Byte])

    /**
     * Just shorthand for sendPrefix("*", payload)
     * @param payload payload to send in bytes.
     */
    def broadcast(payload:Array[Byte]){
        sendPrefix("*", payload)
    }

    /**
     * Just shorthand for broadcast(string.getBytes("UTF-8)).
     * @param payload payload to send in string.
     */
    def broadcast(payload:String){
        broadcast(payload.getBytes("UTF-8"))
    }


    /**
     * called when receive data from other node(s)
     * @param initiator addr in string.
     * @param payload incoming payload data.
     */
    protected def receive(initiator:String, payload:Array[Byte]):Boolean = {
        _handlers.map { h =>
            try {
                h.handleInternodeMessage(initiator, payload)
            } catch {
                case e:Exception =>
                    e.printStackTrace()
            }
        }

        true
    }


    private var _handlers = Array.empty[InternodeMessageHandler]

    /**
     * Add listeners.
     * @param listeners listener to add.
     */
    def registerHandlers(listeners:InternodeMessageHandler*){
        synchronized {
            for(lsn <- listeners){
                if(!this._handlers.contains(lsn)){
                    this._handlers +:= lsn

                    //                    info(lsn + " registered.")
                }
            }
        }
    }

    /**
     * Remove listeners.
     * @param lsn listener to remove.
     */
    def removeHandler(lsn:InternodeMessageHandler){
        synchronized {
            this._handlers = this._handlers.filterNot(_ != lsn)
        }
    }

    /**
     * Clear all listeners.
     */
    def clearHandlers(){
        synchronized {
            this._handlers = Array.empty
        }
    }


    def close()
}

class NopInternodeCommunicator() extends  InternodeCommunicator {
    /**
     * Send payload to specific destination node using address.
     * @param addr node address where payload should send to,
     *             this is node id set in configuration file (id).
     *
     * @param payload payload to send in bytes.
     */
    override def send(addr: String, payload: Array[Byte]){} // do nothing

    /**
     * Send payload to specific group or all nodes.
     * @param prefix node id prefix, eg: "api-",
     *               or for broadcast to all nodes use wildcard "*" as the prefix.
     *
     *
     * @param payload payload to send in bytes.
     */
    override def sendPrefix(prefix: String, payload: Array[Byte]){} // do nothing
    /**
     * called when receive data from other node(s)
     * @param initiator addr in string.
     * @param payload incoming payload data.
     */
    override protected def receive(initiator: String, payload: Array[Byte]):Boolean = false

    def close(){}
}

trait InternodeCommunicatorComponent {
    lazy val interNodeComm:InternodeCommunicator = new NopInternodeCommunicator
}

