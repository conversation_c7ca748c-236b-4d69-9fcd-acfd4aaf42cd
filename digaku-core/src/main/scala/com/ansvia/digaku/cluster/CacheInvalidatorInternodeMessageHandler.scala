/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.cluster

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.Types.IDType
import com.ansvia.digaku.event.impl.InvalidateCacheEvent
import com.ansvia.digaku.exc.NotExistsException
import com.ansvia.digaku.helpers.DbAccess
import com.ansvia.digaku.model._
import com.ansvia.digaku.utils.AsLong

/**
 * Author: robin (<EMAIL>)
 */

class CacheInvalidatorInternodeMessageHandler(nodeID:String) extends InternodeMessageHandler
    with DbAccess
    with Slf4jLogger {

    import com.ansvia.digaku.database.GraphCompat._
    import com.ansvia.digaku.model.Label.INVITE
    import com.ansvia.graph.BlueprintsWrapper._

    import scala.collection.JavaConversions._


    override def handleInternodeMessage(initiator: String, payload: Array[Byte]){
        val str = new String(payload)

        str.split("=").toList match {
            case cmd :: params :: Nil =>
                cmd match {
                    case "invalidate-cache" =>
                        params.split("\\:").toList match {
                            case "streamable" :: AsLong(id) :: Nil =>
                                tx { t =>
                                    val v = t.getVertex(id)
                                    if (v != null){
                                        v.toCC[Streamable[IDType]]
                                            .foreach(st => invalidateStreamableCache(st, initiator))
                                    }
                                }
                            case "invitation" :: AsLong(id) :: Nil =>
                                User.getById(id).foreach(invalidateInvitationCache)
                            case "group" :: AsLong(id) :: Nil =>
                                Forum.getById(id).foreach(invalidateGroupCache)
                            case "user" :: AsLong(id) :: Nil =>
                                User.getById(id).foreach(invalidateUserCache)
                            case x =>
                                error("unknown cache type: " + x)
                                Thread.dumpStack()
                        }
                    case _ =>
                }
            case _ =>
        }

    }

    private def invalidateStreamableCache(streamable:Streamable[IDType], initiator:String){

        /**
         * routine berikut akan dieksekusi secara otomatis apabila ada event InvalidateCache
         */


        if (initiator != nodeID){

            tx { t =>


                val _streamable = streamable.reload()(t)

                //            val lastUpdatedTime = _streamable match {
                //                case pic:PictureBase => pic.lastUpdate
                //                case post:PostBase => post.getVertex.getProperty[Long]("lastUpdatedTime")
                //            }


                // dilakukan update nilai dari lastUpdate dari pictureBase dan postBase dengan tujuan
                // mendapatkan nilai yang fresh dari Titan (invalidate cache di layer Titan)
                _streamable match {
                    case pic:PictureBase =>
                        pic.lastUpdate = Digaku.engine.dateUtils.nowMilis
                        pic.save()
                    case post:PostBase =>
                        post.getVertex.setProperty("lastUpdatedTime", Digaku.engine.dateUtils.nowMilis)

                }

                debug("got it, lets invalidate " + _streamable)

            }

        }


        // invalidate cache di layer applikasi
        Digaku.engine.eventStream emit InvalidateCacheEvent(streamable.reload())



    }

    def invalidateInvitationCache(invitedUser:User){

        /**
         * routine berikut akan dieksekusi secara otomatis apabila ada event InvalidateInvitationCache
         *
         */

        tx { t  =>

            invitedUser.reload()(t).getVertex.pipe.inE(INVITE)
                .iterator().foreach { ed =>
                    ed.setProperty("lastUpdatedTime", Digaku.engine.dateUtils.nowMilis)
            }
//            db.commit()
            debug(s"got user invited user: $invitedUser, lets invalidate")

        }


    }

    def invalidateUserCache(user:User): Unit = {
        tx { t =>
            user.reload()(t).getVertex.setProperty("lastUpdatedTime", Digaku.engine.dateUtils.nowMilis)
        }
        user.reload().refreshUserCache()
        debug(s"got user to be invalidated: $user, lets invalidate")
    }

    def invalidateGroupCache(group:Forum){

        /**
         * routine berikut akan dieksekusi secara otomatis apabila ada event InvalidateChannelCache
         *
         */
        debug(s"got group: $group, lets invalidate")

        tx { t =>
            val _ch = t.getVertex(group.getId).toCC[Forum].getOrElse {
                throw NotExistsException(s"Group ${group.getId} not found")
            }
            val updateTo = Digaku.engine.dateUtils.nowMilis
            _ch.getVertex.setProperty("lastUpdatedTime", updateTo)
        }

    }
}
