/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.concurrent

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.exc.NotSupportedException
import java.util.concurrent.TimeUnit

/**
 * Author: robin
 *
 * Locking feature seperti milik-nya Java dan Scala bedanya ini bisa
 * deteksi kemungkinan dead-lock dan menampilkan stack-trace-nya.
 *
 * @param maxCount -- max counted to treated as deadlock. Default 150.
 */
class Lock(maxCount:Int=150) extends java.util.concurrent.locks.Lock with Slf4jLogger {
    var available = true

    def lock() {
        // scalastyle:off regex.line
        synchronized {
            var counted = 0
            while(!available) {
                wait(100)
                counted += 1
                if(counted > maxCount){
                    warn("POTENTIAL DEAD-LOCK DETECTED!!!")
                    warn("stack trace:")
                    Thread.currentThread().getStackTrace.foreach(x => println(" - " + x))
                    onDeadlock()
                    counted = 0
                }
            }
            available = false
        }
        // scalastyle:on regex.line
    }

    def lockInterruptibly() {
        throw NotSupportedException()
    }

    def tryLock() = {
        throw NotSupportedException()
    }

    def tryLock(timeout: Long, timeUnit: TimeUnit) = {
        throw NotSupportedException()
    }

    def unlock() {
        synchronized {
            available = true
            notify()
        }
    }

    def newCondition() = {
        throw NotSupportedException()
    }

    /**
     * override this for custom deadlock handler.
     */
    protected def onDeadlock(){
        // do nothing
    }
}
