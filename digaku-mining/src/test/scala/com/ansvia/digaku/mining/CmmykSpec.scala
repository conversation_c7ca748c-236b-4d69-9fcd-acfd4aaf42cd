///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.mining
//
//import com.ansvia.digaku.DigakuTest
//import com.ansvia.digaku.model.{Forum, User}
//import org.apache.commons.configuration.BaseConfiguration
//import org.specs2.Specification
//
///**
// * Author: robin
// *
// */
//class CmmykSpec extends Specification with DigakuTest {
//
//    import com.ansvia.graph.BlueprintsWrapper._
//    import scala.collection.JavaConversions._
//
//    def is = "user mutual join group functionality should" ^
//        sequential ^
//        "get mutual users who join the group" ! trees.test1 ^
//        //         "calling group.getMemberMayUserKnow will automatically creating edge `mutual_join_channel`" ! trees.test2 ^
//        "get mutual users who join the group again" ! trees.test1 ^ // kali ini harusnya diambil dari edges
//        "edges removed after user leave group" ! trees.test3 ^
//        end
//
//    object trees {
//
//        val u1 = genUser
//        val u2 = genUser
//        val u3 = genUser
//        val u4 = genUser
//
//        val ch1 = genForumWithOwner(u2)
//
//        u1.support(u2)
//        u1.support(u3)
//
//        ch1.addMembers(u2, u3)
//
//        db.commit()
//
//        u1.reload()
//        val conf = new BaseConfiguration()
//        val cmmyk = new Cmmyk(conf).setDb(db)
//        cmmyk.calculate(u1)
//        db.commit()
//
//        def test1 = {
//
//            db.commit()
//            u1.reload()
//
//            println("ch1: " + ch1)
//            val users = ch1.getMemberMayUserKnow(u1, 10).toList
//
//            (users must contain(u3)) and
//                ((users must not contain(u4)))
//
//        }
//
//        //         def test2 = {
//        //
//        //             val users = u1.getVertex.pipe.outE(User.mutualJoinChannelLabel)
//        //                 .has("channelId", ch1.getId).inV().iterator().flatMap(_.toCC[User]).toList
//        //
//        //             (users must contain(u3)) and
//        //                 ((users must not contain(u4)))
//        //         }
//
//        def test3 = {
//            ch1.removeMembers(u3)
//
//            db.commit()
//
//            val users = u1.getVertex.pipe.outE(Forum.CMMYK)
//                .has("channelId", ch1.getId).inV().iterator().flatMap(_.toCC[User]).toList
//
//            (users must not contain(u3)) and
//                ((users must not contain(u4)))
//        }
//
//
//    }
//
//}
//
