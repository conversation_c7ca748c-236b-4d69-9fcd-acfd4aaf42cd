//package com.ansvia.digaku.mining
//
//import java.io.FileReader
//
//import com.ansvia.digaku.ml.TextNormalizer
//import org.apache.commons.io.IOUtils
//import org.specs2.mutable.Specification
//
///**
// * Author: robin (<EMAIL>)
// *
// */
//class NaiveBayesSpec extends Specification {
//
//    import scala.collection.JavaConversions._
//
//    lazy val rawInput = IOUtils.readLines(new FileReader("etc/ml-data/naivebayes.txt"))
//
//    lazy val classifier = {
//        val classifier = new Classifier()
//
//        rawInput.foreach { line =>
//            line.split(",").toList match {
//                case List(label, tail) =>
//                    println("   trained: " + label + " || " + tail)
//                    classifier.train(label, tail)
//                case _ =>
//            }
//        }
//        classifier
//    }
//
//    "NaiveBayes" should {
//        def test(text:String, expectation:String) = {
//            val rv = classifier.classify(TextNormalizer(text))
//            println(" test: " + text + " =====> " + rv)
//            rv must_== expectation
//        }
//
//        "working" in {
//            println("============= testing =======================")
//            test("ada yang bisa scala?", "programming")
//            test("semua menteri sama saja.", "politik")
//            test("ternyata sex itu sehat", "sex")
//            test("unclassifiable statement", "unknown")
//        }
//    }
//
////    def main(args:Array[String]){
////        println(classifier.classify("ada yang bisa scala?"))
////        println(classifier.classify("semua menteri sama saja."))
////        println(classifier.classify("ternyata sex itu sehat"))
////        println(classifier.classify("unclassifiable statement"))
////    }
//}
