/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.mining

import java.util.Date

import com.ansvia.digaku.Digaku
import com.ansvia.digaku.utils.DateUtils
import com.netflix.astyanax.model.ColumnList
import com.netflix.astyanax.util.RangeBuilder

import scala.collection.mutable.ArrayBuffer

/**
 * Author: robin (<EMAIL>)
 *
 */
object MlData {

    import scala.collection.JavaConversions._

    object Filter {
        val ALL = 0
        val CLASSIFIED = 1
        val UNCLASSIFIED = 2
    }


    sealed case class Data(id:Long, text:String, classification:String, time:Date)

    def getRawDataList(maxId:Long, filter:Int, limit:Int=30) = {

        import com.ansvia.digaku.mining.MlData.Filter._

        val year = Digaku.engine.dateUtils.getCurrentYear
        var month = Digaku.engine.dateUtils.getCurrentMonth

        var ab = ArrayBuffer[Data]()

        def dataKey = s"ml-training-data-$year-$month"
        def classKey = s"ml-training-classification-$year-$month"

        var collected = 0

        Digaku.engine.analytic.cassandraDriver.usingQuery { q =>

            var done = false

            while (!done){
                val cq = q.getKey(dataKey)
                    .autoPaginate(true)
                    .withColumnRange(new RangeBuilder().setReversed(true).setEnd(maxId).setLimit(100).build())

                var opRes = cq.execute()
                var colList: ColumnList[java.lang.Long] = null

                while( { colList = opRes.getResult; !colList.isEmpty && !done } ){

                    //                val colList = opRes.getResult

                    colList.foreach { col =>
                        val classificationStr = q.getKey(classKey).getColumn(col.getName).execute().getResult.getStringValue
                        filter match {
                            case CLASSIFIED =>
                                if (classificationStr.length > 2) {
                                    ab += Data(col.getName, col.getStringValue, classificationStr, new Date(col.getTimestamp / 1000))
                                    collected += 1
                                }
                            case UNCLASSIFIED =>
                                if (classificationStr.length == 0) {
                                    ab += Data(col.getName, col.getStringValue, classificationStr, new Date(col.getTimestamp / 1000))
                                    collected += 1
                                }
                            case ALL =>
                                ab += Data(col.getName, col.getStringValue, classificationStr, new Date(col.getTimestamp / 1000))
                                collected += 1
                        }
                    }

                    if (collected < limit){
                        opRes = cq.execute()
                    }else{
                        done = true
                    }
                }

                if (!done){
                    month = month - 1

                    done = month < 1

                    if (!done){
                        done = q.getKey(dataKey)
                            .withColumnRange(new RangeBuilder().setReversed(true).setEnd(maxId).setLimit(30).build())
                            .getCount.execute().getResult == 0
                    }
                }
            }

        }

        ab.toList
    }


    def findByClassification(maxId:Long, classification:String, limit:Int=30) = {

        val year = Digaku.engine.dateUtils.getCurrentYear
        var month = Digaku.engine.dateUtils.getCurrentMonth

        var ab = ArrayBuffer[Data]()

        def dataKey = s"ml-training-data-$year-$month"
        def classKey = s"ml-training-classification-$year-$month"

        var collected = 0

        Digaku.engine.analytic.cassandraDriver.usingQuery { q =>

            var done = false

            while (!done){
                val cq = q.getKey(dataKey)
                    .autoPaginate(true)
                    .withColumnRange(new RangeBuilder().setReversed(true).setEnd(maxId).setLimit(100).build())

                var opRes = cq.execute()
                var colList: ColumnList[java.lang.Long] = null

                while( { colList = opRes.getResult; !colList.isEmpty && !done } ){

                    colList.foreach { col =>
                        val clsStr = q.getKey(classKey).getColumn(col.getName).execute().getResult.getStringValue
                        if (clsStr.length > 0 && clsStr.equalsIgnoreCase(classification)){
                            ab += Data(col.getName, col.getStringValue, clsStr, new Date(col.getTimestamp / 1000))
                            collected += 1
                        }
                    }

                    if (collected < limit){
                        opRes = cq.execute()
                    }else{
                        done = true
                    }
                }

                if (!done){
                    month = month - 1

                    done = month < 1

                    if (!done){
                        done = q.getKey(dataKey)
                            //                        .autoPaginate(true)
                            .withColumnRange(new RangeBuilder().setReversed(true).setStart(maxId).setLimit(30).build())
                            .getCount.execute().getResult == 0
                    }
                }
            }

        }

        ab.toList
    }

    private val classificationListKey = "ml-classification-list"

    def getFeatureList:List[String] = {

        var ab = ArrayBuffer[String]()

//        def classKey = s"ml-training-classification-$year-$month"

        var collected = 0

        Digaku.engine.analytic.cassandraDriver.usingQuery { q =>

            val cq = q.getKey(classificationListKey)
                .autoPaginate(true)
                .withColumnRange(new RangeBuilder().setReversed(true).setLimit(500).build())

            val opRes = cq.execute()
            val colList: ColumnList[java.lang.Long] = opRes.getResult

//            while( { colList = opRes.getResult; !colList.isEmpty } ){

                colList.foreach { col =>
                    val classificationStr = col.getStringValue
                    ab += classificationStr
                    collected += 1
                }
//            }
        }

        ab.toList.distinct
    }

}
