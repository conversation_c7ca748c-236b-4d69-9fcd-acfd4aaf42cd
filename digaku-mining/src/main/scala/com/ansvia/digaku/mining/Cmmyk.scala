/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.mining

import org.apache.commons.configuration.Configuration
import com.ansvia.digaku.Types.GraphType
import com.ansvia.digaku.model.{Forum, User}
import com.tinkerpop.blueprints.{Direction, Edge}

/**
 * Author: robin
 *
 * Based on contrib/faunus/map/cmmyk.groovy
 */


trait Calculator {
    def calculate()
}

abstract class MiningComponent extends Calculator {
    protected implicit var db:GraphType = _

    def setDb(db:GraphType):this.type = {
        this.db = db
        this
    }
}




class Cmmyk(conf:Configuration) extends MiningComponent {

    import com.ansvia.graph.BlueprintsWrapper._
    import com.ansvia.digaku.model.Label._
    import com.ansvia.graph.gremlin._

    def calculate(){
        require(db != null)

        User.paging(None, None, 100, None){ case (page, vertices) =>
            vertices.foreach { v =>
                v.toCC[User].foreach(calculate)
            }
            db.commit()
            true
        }
    }

    def calculate(user:User) = {
        val uV = user.getVertex

        // remove old cmmyk
        uV.pipe.outE(Forum.CMMYK).remove()

        uV.pipe.out(SUPPORT).outE(JOIN).sideEffect { (ed:Edge) =>
            val targetUser = ed.getVertex(Direction.OUT)
            if (uV.pipe.out(Forum.CMMYK).has("id", targetUser.getId).count() < 100){

                val cmmykEd = uV --> Forum.CMMYK --> targetUser <()

                cmmykEd.setProperty("channelId", ed.getVertex(Direction.IN).getId)
                cmmykEd.setProperty("time", System.currentTimeMillis())
            }
        }.iterate()


    }

}

