///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.mining
//
///**
// * Author: robin (<EMAIL>)
// */
//
//
//import com.ansvia.commons.logging.Slf4jLogger
//import com.ansvia.digaku.exc.NotSupportedException
////import com.ansvia.digaku.thrift.ml.DigakuMlService
//import org.apache.commons.io.IOUtils
//import org.apache.thrift.protocol.TBinaryProtocol
//import org.apache.thrift.transport.{TFramedTransport, TSocket}
//
//import scala.collection.mutable.HashMap
//
///**
// * <p>Extract words from a given text <code>t</code> after removing <code>stopWords</code>.</p>
// * Keep duplicate words.
// * Able to stem the words in a given language <code>l</code> before returning them.
// * @param t some text
// * @param l language for stemming. if null, no stemming
// */
//class Tokenizer(t: String, l: String) {
//    private val _text = t
//    private val _lang = l
//
//    private def words(text: String): Array[String] = text.toLowerCase.
//        replaceAll("""['`]""", "").replaceAll("""[^a-zA-Z]+""", " ") split ("""\s+""")
//
//    def eachWord(text: String, lang:String): List[String] = {
//        var stemLang: String = null
//        if (lang != null) stemLang = lang.toLowerCase
//        if (stemLang != null && !(Tokenizer.languages contains stemLang))
//            sys.error("The stemming language specified is not available.")
//        var tokens = List[String]()
//        if (text == "") return tokens
//        if (stemLang != null) {
//            val stem = Class.forName(Tokenizer.stemPackage + stemLang +
//                Tokenizer.stemFile).newInstance.asInstanceOf[{
//                def setCurrent(name: String)
//                def stem()
//                def getCurrent: String
//            }]
//            words(text).foreach((word: String) =>
//                if (!(Tokenizer.stopWords contains word)) {
//                    stem.setCurrent(word)
//                    stem.stem()
//                    tokens = tokens ::: List(stem.getCurrent)
//                }
//            )
//        } else {
//            words(text).foreach((word: String) =>
//                if (!(Tokenizer.stopWords contains word)) {
//                    tokens = tokens ::: List(word)
//                }
//            )
//        }
//        tokens
//    }
//
//    def eachWord(text: String): List[String] = {
//        eachWord(text, _lang)
//    }
//
//    def eachWord(): List[String] = {
//        eachWord(_text)
//    }
//}
//
//object Tokenizer {
//    val languages = Array[String]("english", "french", "danish", "dutch", "finnish", "german",
//        "hungarian", "italian", "norwegian", "porter", "portuguese", "romanian",
//        "russian", "spanish", "swedish", "turkish")
//    val stemPackage = "lib.snowball.ext."
//    val stemFile = "Stemmer"
//
//    lazy val stopWords = IOUtils.toString(getClass.getClassLoader.getResourceAsStream("stop-words.txt")).split("\\W+")
//        .toSeq.filter(_.trim.length > 1)
//
//}
//
///**
// * Naive Bayes Classifier.
// */
//class Classifier(lang: String) {
//    def this() = this("english")
//
//    private val _stemming     = lang
//    private var _wcount       = HashMap[Pair[String, String], Int]()    // [[category, word], count]
//    private var _ccount       = HashMap[String, Int]()                  // [category, count]
//    private var _probs        = HashMap[String, Double]()               // [category, probability]
//
//    def wcount = _wcount
//    def ccount = _ccount
//
//    def train(category: String, text: String) {
//        val tokenizer = new Tokenizer(text.toLowerCase, _stemming)
//        tokenizer.eachWord().foreach(word => _wcount.put((category, word),
//            1+_wcount.get((category, word)).getOrElse(0)))
//        _ccount.put(category, 1+_ccount.get(category).getOrElse(0))
//    }
//
//    private def wordProb(word: String,  cat: String): Double = {
//        if(!_wcount.contains((cat,word)))
//            return 0.0
//        _wcount((cat,word)) / _ccount(cat).floatValue()
//    }
//
//    private def wordWeightedAverage(word: String, cat: String): Double = {
//        val weight = 1
//        val assumed_prob = 0.5
//        // current probability
//        val basic_prob = wordProb(word, cat)
//        // count the number of times this word has appeared in all categories
//        var totals = 0
//        _ccount foreach (c =>
//            if(_wcount.contains((c._1, word)))
//                totals += _wcount((c._1, word))
//            )
//        // final weighted average
//        (weight * assumed_prob + totals * basic_prob) / (weight + totals)
//    }
//
//    private def totalCountCat(): Int = {
//        _ccount.foldLeft(0)(_+_._2)
//    }
//
//    private def docProb(text: String, cat: String): Double = {
//        var prob = 1.0
//        val tokenizer = new Tokenizer(text, _stemming)
//        tokenizer.eachWord().foreach(word =>
//            prob *= wordWeightedAverage (word, cat)
//        )
//        prob
//    }
//
//    private def textProb(text: String, cat: String): Double = {
//        val catProb = _ccount(cat) / totalCountCat().floatValue()
//        val wProb = docProb(text, cat)
//        catProb * wProb
//    }
//
//    private def catScores(text: String) {
//        _ccount foreach (c => _probs.put(c._1, textProb(text, c._1)))
//    }
//
//    def classify(text: String): String = {
//        catScores(text)
////        if (_probs.maxBy(_._2)._2 < 0.00001)
////            "unknown"
////        else
//        if (_probs.nonEmpty)
//            _probs.maxBy(_._2)._1
//        else
//            "unknown"
//    }
////
////    def classifyTop2(text: String): Seq[String] = {
////        catScores(text)
////        if (_probs.nonEmpty)
////            _probs.toList.sortBy(_._2).reverse.map(_._1).slice(0, 2)
////        else
////            Nil
////    }
//}
//
//
//object NaiveBayesMiner extends Slf4jLogger {
//
//    import com.ansvia.digaku.utils.RichString._
//
//    var usingExternalMlService = false
//    var externalMlServiceHostNPort = "127.0.0.1:3181"
//
//    private var mlServiceTransport: TFramedTransport = null
//    private var mlServiceProtocol: TBinaryProtocol = null
//
//    lazy val mlService = {
//        val s = externalMlServiceHostNPort.split("\\:")
//        val host = s(0)
//        val port = s(1).toInt
//        val socket = new TSocket(host, port)
//        mlServiceTransport = new TFramedTransport(socket, 512)
//        mlServiceProtocol = new TBinaryProtocol(mlServiceTransport, true, true)
//        mlServiceTransport.open()
//        val client = new DigakuMlService.Client(mlServiceProtocol)
//        client
//    }
//
//    private def rawInput = {
//        MlData.getRawDataList(0L, MlData.Filter.CLASSIFIED, limit=10000)
//    }
//
//    // Abstraction
//    class MyClassifier {
//        lazy val _internalClassifier:Classifier = new Classifier()
//
//        def reload(){
////            rawInput.foreach { d =>
////                val normedText = stripMetadata(d.text).trim
////                if (normedText.length > 3){
////                    _internalClassifier.train(d.classification, normedText)
////                    debug(s"   trained: ${d.classification} <- ${normedText.truncate(30)}")
////                }
////            }
//            if (usingExternalMlService) {
//                mlService.reload()
//            }else {
//                var count = 0
//                rawInput.foreach { d =>
//                    val normedText = stripMetadata(d.text).trim
//                    if (normedText.length > 3){
//                        _internalClassifier.train(d.classification, normedText)
//                        debug(s"   trained: ${d.classification} <- ${normedText.truncate(30)}")
//                        count += 1
//                    }
//                }
//                info(" %d samples trained.".format(count))
//            }
//        }
//
//        def classify(text:String):String = {
//            if (!usingExternalMlService){
////                if (_internalClassifier == null){
////                    _internalClassifier = new Classifier()
////                    debug(s"training data...")
////                    this.reload()
////                }
//                _internalClassifier.classify(text)
//            }else{
//                try {
//                    mlService.classifyText(text)
//                }catch{
//                    case e:org.apache.thrift.transport.TTransportException if e.getMessage.contains("Broken pipe")=>
//                        println("reconnecting and retrying...")
//                        mlServiceTransport.close()
//                        mlServiceTransport.open()
//                        mlService.classifyText(text)
//
//                }
//            }
//        }
//
//        def train(label:String, text:String){
//            if (!usingExternalMlService){
//                _internalClassifier.train(label, text)
//            }else{
//                // unsupported
//                throw NotSupportedException("train is not supported in ML external service mode")
//            }
//        }
//
//    }
//
//    private lazy val _classifier: MyClassifier = new MyClassifier()
//    def classifier:MyClassifier = {
////        assert(_classifier != null, "_classifier not loaded yet, please reload first.")
//        _classifier
//    }
//
//
//    def reload() = {
////        _classifier = new MyClassifier()
//
//        _classifier.reload()
//
//        _classifier
//    }
//
//    private val metaRe = """^\$.*?\$""".r
//
//    private def stripMetadata(text:String) = {
//        metaRe.replaceAllIn(text, "").trim
//    }
//
//
//}
//
