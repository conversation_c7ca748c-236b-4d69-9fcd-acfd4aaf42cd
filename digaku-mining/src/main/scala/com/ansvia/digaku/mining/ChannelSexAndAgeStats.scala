///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.mining
//
//import com.ansvia.digaku.model.{SexType, Group}
//import com.ansvia.digaku.model.Label._
//import com.tinkerpop.pipes.PipeFunction
//import com.tinkerpop.blueprints.Vertex
//import com.ansvia.digaku.model
//import org.joda.time.{DateTime, Years}
//
///**
// * Author: robin
// *
// * OLAP function untuk memproses users
// * sex count dan age pada group's member.
// *
// */
//class ChannelSexAndAgeStats extends MiningComponent {
//
//    import scala.collection.JavaConversions._
//    import com.ansvia.graph.BlueprintsWrapper._
//
//    class AgeStats {
//        var bellow16 = 0
//        var between16to20 = 0
//        var between21to25 = 0
//        var between26to30 = 0
//        var between31to35 = 0
//        var between36to40 = 0
//        var between41to45 = 0
//        var between46to50 = 0
//        var above50 = 0
//    }
//
//    private val STATS = "stats"
//
//    def calculate(){
//        require(db != null)
//
//        var count = 0
//        Group.paging(None, None, 10, None){ case (page, vertices) =>
//
//            vertices.foreach { v =>
//                count = count + 1
//                val name = v.getOrElse("name", "")
//                println(s"   $count. processing $name")
//               calculateInternal(v)
//            }
//
//            db.commit()
//
//            true
//        }
//
//    }
//
//    def calculate(v:Vertex) = {
//        val name = v.getOrElse("name", "")
//        println("processing " + name)
//        calculateInternal(v)
//        db.commit()
//    }
//
//    private def calculateInternal(v:Vertex) = {
//        require(db != null)
//
//        val m = new AgeStats()
//        val f = new AgeStats()
//        var maleCount = 0
//        var femaleCount = 0
//
//        v.pipe.in(JOIN).sideEffect(new PipeFunction[Vertex, Unit]{
//            def compute(v: Vertex){
//
//                val bd = v.getOrElse("birthDate", "")
//                val sex = v.getProperty[Int]("sex")
//
//                if (bd != ""){
//                    val date = model.User.BIRTH_DATE_FORMAT.parseDateTime(bd)
//                    val yb = Years.yearsBetween(date, new DateTime)
//                    yb.getYears match {
//                        case age if age < 16 =>
//                            if (sex == SexType.MALE)
//                                m.bellow16 += 1
//                            else if (sex == SexType.FEMALE)
//                                f.bellow16 += 1
//                        case age if age >= 16 && age <= 20 =>
//                            if (sex == SexType.MALE)
//                                m.between16to20 += 1
//                            else if (sex == SexType.FEMALE)
//                                f.between16to20 += 1
//                        case age if age > 20 && age <= 25 =>
//                            if (sex == SexType.MALE)
//                                m.between21to25 += 1
//                            else if (sex == SexType.FEMALE)
//                                f.between21to25 += 1
//                        case age if age > 25 && age <= 30 =>
//                            if (sex == SexType.MALE)
//                                m.between26to30 += 1
//                            else if (sex == SexType.FEMALE)
//                                f.between26to30 += 1
//                        case age if age > 30 && age <= 35 =>
//                            if (sex == SexType.MALE)
//                                m.between31to35 += 1
//                            else if (sex == SexType.FEMALE)
//                                f.between31to35 += 1
//                        case age if age > 35 && age <= 40 =>
//                            if (sex == SexType.MALE)
//                                m.between36to40 += 1
//                            else if (sex == SexType.FEMALE)
//                                f.between36to40 += 1
//                        case age if age > 40 && age <= 45 =>
//                            if (sex == SexType.MALE)
//                                m.between41to45 += 1
//                            else if (sex == SexType.FEMALE)
//                                f.between41to45 += 1
//                        case age if age > 45 && age < 50 =>
//                            if (sex == SexType.MALE)
//                                m.between46to50 += 1
//                            else if (sex == SexType.FEMALE)
//                                f.between46to50 += 1
//                        case age if age >= 50 =>
//                            if (sex == SexType.MALE)
//                                m.above50 += 1
//                            else if (sex == SexType.FEMALE)
//                                f.above50 += 1
//                    }
//                }
//
//                sex match {
//                    case SexType.MALE =>
//                        maleCount = maleCount + 1
//                    case SexType.FEMALE =>
//                        femaleCount = femaleCount + 1
//                    case _ =>
//                }
//
//            }
//        }).iterate()
//
//        // fix memberCount, maleCount, and femaleCount
//        v.setProperty("maleCount", maleCount)
//        v.setProperty("femaleCount", femaleCount)
//        v.setProperty("memberCount", maleCount + femaleCount)
//
//        var newVStat = false
//
//        val vStatMale = v.pipe.out(STATS).has("kind", 112).headOption
//            .getOrElse {
//            val _vStat = db.addVertex(null)
//            _vStat.setProperty("_class_", "com.ansvia.digaku.model.ChannelStats")
//            _vStat.setProperty("kind", 112) // 112 adalah kind untuk statistic age male
//            v --> STATS --> _vStat
//            newVStat = true
//            _vStat
//        }
//        val vStatFemale = v.pipe.out(STATS).has("kind", 113).headOption
//            .getOrElse {
//            val _vStat = db.addVertex(null)
//            _vStat.setProperty("_class_", "com.ansvia.digaku.model.ChannelStats")
//            _vStat.setProperty("kind", 113) // 113 adalah kind untuk statistic age female
//            v --> STATS --> _vStat
//            newVStat = true
//            _vStat
//        }
//
//        vStatMale.setProperty("bellow16", m.bellow16)
//        vStatMale.setProperty("between16to20", m.between16to20)
//        vStatMale.setProperty("between21to25", m.between21to25)
//        vStatMale.setProperty("between26to30", m.between26to30)
//        vStatMale.setProperty("between31to35", m.between31to35)
//        vStatMale.setProperty("between36to40", m.between36to40)
//        vStatMale.setProperty("between41to45", m.between41to45)
//        vStatMale.setProperty("between46to50", m.between46to50)
//        vStatMale.setProperty("above50", m.above50)
//
//        vStatFemale.setProperty("bellow16", f.bellow16)
//        vStatFemale.setProperty("between16to20", f.between16to20)
//        vStatFemale.setProperty("between21to25", f.between21to25)
//        vStatFemale.setProperty("between26to30", f.between26to30)
//        vStatFemale.setProperty("between31to35", f.between31to35)
//        vStatFemale.setProperty("between36to40", f.between36to40)
//        vStatFemale.setProperty("between41to45", f.between41to45)
//        vStatFemale.setProperty("between46to50", f.between46to50)
//        vStatFemale.setProperty("above50", f.above50)
//
//
//    }
//
//}
