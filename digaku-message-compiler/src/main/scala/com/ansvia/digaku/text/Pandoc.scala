/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.text

import sys.process._

/**
 * Author: robin
 * Date: 2/6/14
 * Time: 9:54 PM
 *
 * Pandoc adalah aplikasi untuk convert berbagai format dokumen
 * ke format lainnya, lebih detail baca http://johnmacfarlane.net/pandoc/
 *
 * Ini adalah class wrapper-nya untuk bekerja dengan pandoc.
 *
 */

/**
 * Pandoc wrapper class.
 * @param pandocBin absolute path to pandoc bin, eg: /usr/bin/pandoc
 */
class Pandoc(pandocBin:String) {


    /**
     * Convert text format to another text format using Pandoc.
     * http://johnmacfarlane.net/pandoc/
     *
     * @param formatSource source format @see [[com.ansvia.digaku.text.Pandoc.formats]]
     * @param formatTarget target format @see [[com.ansvia.digaku.text.Pandoc.formats]]
     * @param source source text to convert.
     * @return
     */
    def convert(formatSource:String, formatTarget:String, source:String) = {

//        val cmd = Seq(Config.PANDOC_BIN, "-f " + formatSource, "-t " + formatTarget)
//        ShellExec().setWorkDir("/tmp").execGetOutput(cmd)

        val pandocCmd = "%s -f %s -t %s".format(pandocBin, formatSource, formatTarget)
        val echoCmd = "echo " + source

        val result = (echoCmd #| pandocCmd).!!

        val fixedResult = fix(result)

        fixedResult.trim
    }


    private def fix(text:String) = {

        val rv = new StringBuffer()
        val lines = text.split("\n")
        var i = 0
        var previousIsListLike = false

        while (i < lines.length){

            val line = lines(i)

            if (previousIsListLike && line.trim == ""){

                i = i + 1
                previousIsListLike = false

            } else {

                if (line.startsWith("# ")){
                    previousIsListLike = true
                }

                rv.append(line + "\n")

                i = i + 1

            }

        }

        rv.toString
    }

}

object Pandoc {
    object formats {
        val HTML = "html"
        val TEXTILE = "textile"
        val MARKDOWN = "markdown"
    }
}
