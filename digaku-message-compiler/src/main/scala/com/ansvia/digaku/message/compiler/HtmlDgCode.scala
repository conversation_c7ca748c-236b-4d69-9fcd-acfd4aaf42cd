/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.message.compiler

import scala.util.matching.Regex
import com.ansvia.digaku.model._
import com.ansvia.commons.logging.Slf4jLogger
import java.util.regex.Matcher
import builder._
import com.ansvia.digaku.model
import com.ansvia.digaku.Types._
import com.ansvia.digaku.notifications.impl.NotifKind
import com.google.common.cache.CacheBuilder
import java.util.concurrent.{Callable, TimeUnit}
import com.ansvia.digaku.utils.RichString._

abstract class HtmlDgCode(text:String) extends DgCodeBase(text) with Slf4jLogger {

    import HtmlDgCode._

    val baseUrl:String
    /**
     * WAJIB DI-OVERRIDE AGAR BISA DIGUNAKAN.
     * @param p
     * @return
     */
    implicit def postBuilder(p:Post):PostBuilder.PostNodeSeqWrapperBase
        // = new PostBuilder.UnimplementedPostNodeSeqWrapper(p)

    implicit def responseBuilder(resp:Response):ResponseBuilder.ResponseNodeSeqWrapperBase
        // = new PostBuilder.UnimplementedResponseNodeSeqWrapper(resp)

    implicit def picBuilder(p:PictureBase):PictureBuilder.PictureWrapperBase
        // = new PicBuilder.UnimplementedPictureWrapperBase(p)

    //di-comment dulu sebelum dikerjain UI nya
//    implicit def eventBuilder(ev:Event):EventBuilder.EventNodeSeqWrapperBase
        // = new EventBuilder.UnimplementedEventBuilderWrapper(ev)
    implicit def userBuilder(user:model.User):UserBuilder.UserWrapperBase

    implicit def subForumBuilder(forum:model.Forum):SubForumBuilder.SubForumWrapperBase

//    implicit def privateMessageBuilder(pm:PrivateMessage):PrivateMessageBuilder.MessageNodeSeqWrapperBase
//
//    implicit def messageResponseBuilder(mr:MessageResponse):PrivateMessageBuilder.ResponseNodeSeqWrapperBase

    protected implicit def originBuilder(origin:model.Origin[GraphType]):OriginBuilder.OriginWrapperBase

    protected implicit def trophyBuilder(trophy:model.Trophy):TrophyBuilder.TrophyWrapperBase


    /**
     * Digunakan untuk merender notification/dialog
     * @param linkUser
     * @param renderEmail digunakan untuk merender email template notifications
     *                    karena pada email templates, menggunakan style yang berbeda
     * @return
     */
    def compile(linkUser:Boolean=true, renderEmail:Boolean=false, withLink:Boolean=true):String = {
//        debug("compiling: " + text)
        var rv = HtmlDgCode.USER_REGEX.replaceAllIn(text, replacer(renderEmail, _, withLink))
        rv = HtmlDgCode.USER_GROUP_REGEX.replaceAllIn(text, replacer(false, _))
        rv = HtmlDgCode.CHANNEL_REGEX.replaceAllIn(rv, replacer(false, _))
        rv = HtmlDgCode.SIMPLE_POST_REGEX.replaceAllIn(rv, replacer(false, _))
        rv = HtmlDgCode.RESPONSE_REGEX.replaceAllIn(rv, replacer(false, _))
        rv = HtmlDgCode.INVITATION_REGEX.replaceAllIn(rv, replacer(false, _))
        rv = HtmlDgCode.MESSAGE_RESPONSE_REGEX.replaceAllIn(rv, replacer(false, _, false))
        rv = HtmlDgCode.PRIVATE_MESSAGE_REGEX.replaceAllIn(rv, replacer(false, _, false))
        rv = HtmlDgCode.PICTURE_GROUP_REGEX.replaceAllIn(rv, replacer(false, _))
        rv = HtmlDgCode.TROPHY_REGEX.replaceAllIn(rv, replacer(false, _))
        if (linkUser) {
            rv = HtmlDgCode.USER_NAME_REGEX.replaceAllIn(rv, """@<a href="%s/u/$1" class="user-link" title="$1">$1</a>""".format(baseUrl))
        }
        rv
    }

    def compileNotLink:String = {
        var rv = HtmlDgCode.USER_REGEX.replaceAllIn(text, """$3""")
        rv = HtmlDgCode.USER_GROUP_REGEX.replaceAllIn(rv, """$3""")
        rv = HtmlDgCode.CHANNEL_REGEX.replaceAllIn(rv, """$3""")
        rv = HtmlDgCode.SIMPLE_POST_REGEX.replaceAllIn(rv, "")
        rv = HtmlDgCode.RESPONSE_REGEX.replaceAllIn(rv, "")
        rv = HtmlDgCode.INVITATION_REGEX.replaceAllIn(rv, "")
        rv = HtmlDgCode.MESSAGE_RESPONSE_REGEX.replaceAllIn(rv, "")
        rv = HtmlDgCode.PRIVATE_MESSAGE_REGEX.replaceAllIn(rv, "")
        rv = HtmlDgCode.PICTURE_GROUP_REGEX.replaceAllIn(rv, "")
        rv = HtmlDgCode.TROPHY_REGEX.replaceAllIn(rv, "")
        rv = HtmlDgCode.USER_NAME_REGEX.replaceAllIn(rv, """@<a href="%s/u/$1" class="user-link" title="$1">$1</a>""".format(baseUrl))
        rv
    }

    /**
     * Ini akan meng-compile dgcode ke bentuk plain text, no link no mess.
     * @return
     */
    def compileToPlainText:String = {
        var rv = HtmlDgCode.USER_REGEX.replaceAllIn(text, """$3""")
        rv = HtmlDgCode.USER_GROUP_REGEX.replaceAllIn(rv, """$3""")
        rv = HtmlDgCode.CHANNEL_REGEX.replaceAllIn(rv, """$3""")
        rv = HtmlDgCode.SIMPLE_POST_REGEX.replaceAllIn(rv, "")
        rv = HtmlDgCode.RESPONSE_REGEX.replaceAllIn(rv, "")
        rv = HtmlDgCode.USER_NAME_REGEX.replaceAllIn(rv, "$1")
        rv.trim
    }

    /**
     * men-compile ke bentuk plain text dan text di bold-kan untuk
     * message yang pentingnya
     * @return
     */
    def compileToPlainTextWithBolder:String = {
        var rv = HtmlDgCode.USER_REGEX.replaceAllIn(text, bolderReplacer _)
        rv = HtmlDgCode.USER_GROUP_REGEX.replaceAllIn(rv, bolderReplacer _)
        rv = HtmlDgCode.CHANNEL_REGEX.replaceAllIn(rv, bolderReplacer _)
        rv = HtmlDgCode.SIMPLE_POST_REGEX.replaceAllIn(rv, bolderReplacer _)
        rv = HtmlDgCode.RESPONSE_REGEX.replaceAllIn(rv, "<b>post</b>")
        rv = HtmlDgCode.INVITATION_REGEX.replaceAllIn(rv, "invitation")
        rv = HtmlDgCode.MESSAGE_RESPONSE_REGEX.replaceAllIn(rv, "private message")
        rv = HtmlDgCode.PRIVATE_MESSAGE_REGEX.replaceAllIn(rv, "private message")
        rv = HtmlDgCode.PICTURE_GROUP_REGEX.replaceAllIn(rv, "picture")
        rv = HtmlDgCode.TROPHY_REGEX.replaceAllIn(rv, "trophy")
        rv = HtmlDgCode.USER_NAME_REGEX.replaceAllIn(rv, "$1")
        rv
    }

    /**
     * Digunakan untuk compile notif/dialogue dengan image
     * @param kind see [[com.ansvia.digaku.notifications.impl.NotifKind]]
     * @return
     */
    def compileImage(kind:Int):String = {
        <img src={compileImageUrl(kind)} />.toString()
    }

    /**
     * Digunakan untuk compile notif/dialogue dengan image tanpa img src
     * @param kind see [[com.ansvia.digaku.notifications.impl.NotifKind]]
     * @return
     */
    def compileImageUrl(kind:Int):String = {
        import NotifKind._

        kind match {
            case USER_HAS_TROPHY =>
                baseUrl + "/assets/img/default-badges-icon.png"
            case JOIN | ADD_MODERATOR | FORUM_ANNOUNCEMENT =>
                replacerImage(HtmlDgCode.CHANNEL_REGEX.findAllIn(text).matchData.toList.headOption)
            case PROMOTED_THREAD =>
                baseUrl + "/assets/img/default-promoted-thread-icon.png"
            case RANK =>
                baseUrl + "/assets/img/default-rank-icon.png"
            case GLOBAL_ANNOUNCEMENT =>
                baseUrl + "/assets/img/icon/announcement.png"
            case _ =>
                replacerImage(HtmlDgCode.USER_OR_USER_GROUP_REGEX.findAllIn(text).matchData.toList.headOption)
        }
    }

    private def replacerImage(m:Option[Regex.Match]):String = {
        m.map{ r =>
            r.group(2) match {
                case "user" =>
                    processUserWithImage(r.group(3), r.group(4))

                case "ug" =>
                    processUserGroupImage(r.group(4))
//                case "trophy" =>
//                    processTrophyImage(r.group(3))

                case "ch" =>
                    processForumImage(r.group(4))

                case x =>
                    "NotHandled(%s)".format(x.toString)
            }
        }.getOrElse("")
    }

    private def replacer(renderEmail:Boolean, m:Regex.Match, withLink:Boolean=true):String = {
//        debug("m.groupCount: " + m.groupCount)
        m.group(2) match {
            case "user" =>
                processUser(m.group(3), m.group(4), renderEmail, withLink)

            case "ug" =>
                processUserGroup(m.group(4))

            case "ch" =>
                processChannel(m.group(3), m.group(4))

            case "simple-post" | "post" =>
                processSimplePost(m.group(3), m.group(4), m.group(5))

            case "picture" =>
                processPicture(m.group(3), m.group(4), m.group(5))

//            case "picture-group" =>
//                processPictureGroup(m.group(3), m.group(4), m.group(5))

            case "event" =>
                processEvent(m.group(3), m.group(4), m.group(5))

            case "response" =>
                processResponse(m.group(3), m.group(4), m.group(5))

            case "invitation" =>
                processInvitation(m.group(3), m.group(4), m.group(5))

//            case "private-message" =>
//                processPrivateMessage(m.group(3), withLink)
//
//            case "message-response" =>
//                processMessageResponse(m.group(3), withLink)

            case "trophy" =>
                processTrophy(m.group(3))

            case x =>
                "NotHandled(%s)".format(x.toString)
        }
    }

    /**
     * digunakan untuk menebalkan (bold) title sebuah post
     * @param m
     * @return
     */
    def bolderReplacer(m:Regex.Match):String = {
        m.group(2) match {
            case "post" =>
                Matcher.quoteReplacement(getPostCached(m.group(3).toLong).map { post =>
                    <b>{post.getTitle(20)}</b>.toString()
                }.getOrElse("---"))
            case "user" =>
                Matcher.quoteReplacement(getUserCached(m.group(4)).map { user =>
                    <b>
                        {
                        if (user.fullName.trim.nonEmpty) {
                            user.fullName.truncate(20)
                        } else {
                            user.name
                        }
                        }
                    </b>.toString()
                }.getOrElse("---"))
            case "ug" =>
                <b>{m.group(3)}</b>.toString()
            case "ch" =>
                <b>{m.group(3)}</b>.toString()
            case x =>
                "NotHandled(%s)".format(x.toString)
        }
    }

    def processUser(userName:String, userId:String, renderEmail:Boolean=false, withLink:Boolean=true):String = {
        getUserCached(userId).map { user =>
            if (renderEmail)
                if (withLink) {
                    <span style="font-size: 20px; font-family:roboto_slabregular, arial; color: #4c4c4c;">{user.snipLinkNoSign}</span>.toString()
                } else {
                    <span style="font-size: 20px; font-family:roboto_slabregular, arial; color: #4c4c4c;">{user.getName}</span>.toString()
                }
            else {
                if (withLink) {
                    <span class="user-link">{user.snipLinkNoSign}</span>.toString()
                } else {
                    <span class="user-link">{user.getName}</span>.toString()
                }
            }

        }.getOrElse("---")
    }

    def processUserGroup(id:String):String = {
        getUserGroupCached(id).map { ug =>
            <span class="user-link">{ug.name}</span>.toString()
        }.getOrElse("---")
    }

    def processUserGroupImage(id:String):String = {
        getUserGroupCached(id).map { ug =>
            if (ug.logo.isEmpty) {
                baseUrl + "/assets/img/icon/user-group.png"
            } else {
                ug.logo
            }
        }.getOrElse("")
    }

    private def processUserWithImage(userName:String, userId:String):String = {
        getUserCached(userId).map { u =>
            u.normalizedPhotoMedium
        }.getOrElse("")
    }

    def processForumImage(forumId:String):String = {
        getForumCached(forumId).map { forum =>
            forum.normalizedLogoMedium
        }.getOrElse("")
    }

    def processChannel(channelName:String, channelId:String):String = {
//        getChannelCached(channelName).map(ch => <span class="group-link">{ch.snipLinkNoSign}</span>.toString())
//            .getOrElse("---")
        // @TODO(robin): fix this?
        "forum"
    }

    /**
     * Digunakan untuk mem-process link dari post (simple post, article)
     * @param postId id dari post
     * @param originKind see [[com.ansvia.digaku.model.OriginKind]]
     * @param originName see [[com.ansvia.digaku.model.Origin]]
     * @return
     */
    def processSimplePost(postId:String, originKind:String, originName:String):String = {
        Matcher.quoteReplacement(getPostCached(postId.toLong).map(_.snipLinkNoSign(false).toString())
            .getOrElse("---"))
    }

    /**
     * Digunakan untuk mem-process link dari post picture
     * @param picId id dari post picture
     * @param originKind see [[com.ansvia.digaku.model.OriginKind]]
     * @param originName see [[com.ansvia.digaku.model.Origin]]
     * @return
     */
    def processPicture(picId:String, originKind:String, originName:String):String = {
        Matcher.quoteReplacement(Picture.getById(picId.toLong).map( pic => <a href={pic.url(false)}>picture</a>.toString() ).getOrElse("---"))
    }

//    def processPictureGroup(picGroupId:String, originKind:String, originName:String):String = {
//        Matcher.quoteReplacement(PictureGroup.getById(picGroupId.toLong).map( picGroup => <a href={picGroup.url(false)}>pictures</a>.toString() ).getOrElse("---"))
//    }

    def processEvent(eventId:String, originKind:String, originName:String):String = {
        //di-comment dulu sebelum dikerjain UI nya
//        Matcher.quoteReplacement(Event.getById(eventId.toLong).map( event => <a href={event.url(false)}>event</a>.toString()).getOrElse("---"))
        "---"
    }

    /**
     * Digunakan untuk mem-process link response dari post
     * @param respId id response dari post
     * @param originKind see [[com.ansvia.digaku.model.OriginKind]]
     * @param originName see [[com.ansvia.digaku.model.Origin]]
     * @return
     */
    def processResponse(respId:String, originKind:String, originName:String):String = {
        try {
            Matcher.quoteReplacement(getResponseCached(respId.toLong).map { resp =>
                <a href={resp.url(false)}>response</a>.toString
            }.getOrElse("---"))
        } catch {
            case e:NumberFormatException =>
                Matcher.quoteReplacement("---")
        }
    }

//    def processMessageResponse(messRespId:String, withLink:Boolean=true):String = {
//        try {
//            if (withLink) {
//                Matcher.quoteReplacement(MessageResponse.getById(messRespId.toLong).map(_.snipLink(false).toString()).getOrElse("---"))
//            } else {
//                import com.ansvia.digaku.utils.RichString._
//                Matcher.quoteReplacement(MessageResponse.getById(messRespId.toLong).map(_.content.truncate(20)).getOrElse("---"))
//            }
//        }catch{
//            case e:NumberFormatException =>
//                Matcher.quoteReplacement("---")
//        }
//    }
//
//    def processPrivateMessage(privateMessageId:String, withLink:Boolean=true):String = {
//        try {
//            if (withLink) {
//                Matcher.quoteReplacement(PrivateMessage.getById(privateMessageId.toLong).map(_.snipLink(false).toString()).getOrElse("----"))
//            } else {
//                import com.ansvia.digaku.utils.RichString._
//                Matcher.quoteReplacement(PrivateMessage.getById(privateMessageId.toLong).map(_.content.truncate(20)).getOrElse("----"))
//            }
//
//        }catch{
//            case e:NumberFormatException =>
//                Matcher.quoteReplacement("---")
//        }
//    }

    def processTrophy(trophyId:String):String = {
        try {
            Matcher.quoteReplacement(Trophy.getById(trophyId.toLong).map(_.getName).getOrElse("----"))
        }catch{
            case e:NumberFormatException =>
                Matcher.quoteReplacement("---")
        }
    }

    def processInvitation(invCode:String, chId:String, chName:String):String = {
        """<a href="%s/forum/%s/invitation/accept/%s">here</a>""".format(baseUrl, chName.toLowerCase, invCode)
    }
}

object HtmlDgCode {

    /**
     * Format $[user:USER-NAME;USER-ID]
     */
    private lazy val USER_REGEX = """(\$\[(user)\:(.*?);(.*?)\])""".trim.r
    private lazy val USER_GROUP_REGEX = """(\$\[(ug)\:(.*?);(.*?)\])""".trim.r
    private lazy val USER_OR_USER_GROUP_REGEX = """(\$\[(user|ug)\:(.*?);(.*?)\])""".trim.r

    /**
     * Format $[ch:FORUM-NAME;GROUP-ID]
     */
    private lazy val CHANNEL_REGEX = """(\$\[(ch)\:(.*?);(.*?)\])""".trim.r

    private lazy val SIMPLE_POST_REGEX = """(\$\[(simple\-post|post|picture|event)\:(.*?);(.*?);(.*?)\])""".trim.r
    private lazy val RESPONSE_REGEX = """(\$\[(response)\:(.*?);(.*?);(.*?)\])""".trim.r
    private lazy val INVITATION_REGEX = """(\$\[(invitation)\:(.*?);(.*?);(.*?)\])""".trim.r
    private lazy val USER_NAME_REGEX = """\B@([a-zA-Z0-9\.\_]+)""".trim.r
    private lazy val PRIVATE_MESSAGE_REGEX = """(\$\[(private-message)\:(.*?)\])""".trim.r
    private lazy val MESSAGE_RESPONSE_REGEX = """(\$\[(message-response)\:(.*?)\])""".trim.r
    private lazy val PICTURE_GROUP_REGEX = """(\$\[(picture-group)\:(.*?);(.*?);(.*?)\])""".trim.r
    private lazy val TROPHY_REGEX = """(\$\[(trophy)\:(.*?)\])""".trim.r

//    implicit def strToHtmlDgCode(text:String) = new HtmlDgCode(text)

    private lazy val userCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build[String, Option[User]]()


    protected def getUserCached(idStr:String) = {
        userCache.get(idStr, new Callable[Option[User]]{
            def call(): Option[User] = {
                User.getById(idStr.toLong)
            }
        })
    }

    private lazy val userGroupCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build[String, Option[UserGroup]]()


    protected def getUserGroupCached(idStr:String) = {
        userGroupCache.get(idStr, new Callable[Option[UserGroup]]{
            def call(): Option[UserGroup] = {
                UserGroup.getById(idStr.toLong)
            }
        })
    }

    private lazy val forumCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build[String, Option[Forum]]()

//    protected def getChannelCached(channelName:String) = {
//        channelCache.get(channelName, new Callable[Option[Forum]]{
//            def call(): Option[Forum] = {
//                Forum.getByName(channelName)
//            }
//        })
//    }

    private lazy val postCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(30, TimeUnit.MINUTES)
        .build[String, Option[Post]]()

    def getPostCached(id:IDType) = {
        postCache.get(id.toString, new Callable[Option[Post]]{
            def call(): Option[Post] = {
                Post.getPostById(id.toLong)
            }
        })
    }

    private lazy val responseCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build[String, Option[Response]]()

    def getResponseCached(id:IDType) = {
        responseCache.get(id.toString, new Callable[Option[Response]]{
            def call(): Option[Response] = {
                Response.getById(id.toLong)
            }
        })
    }

    private lazy val trophyCache = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(1, TimeUnit.HOURS)
        .build[String, Option[Trophy]]()

    protected def getTrophyCached(id:IDType) = {
        trophyCache.get(id.toString, new Callable[Option[Trophy]]{
            def call(): Option[Trophy] = {
                Trophy.getById(id.toLong)
            }
        })
    }

    protected def getForumCached(idStr:String) = {
        forumCache.get(idStr, new Callable[Option[Forum]]{
            def call(): Option[Forum] = {
                Forum.getById(idStr.toLong)
            }
        })
    }

}
