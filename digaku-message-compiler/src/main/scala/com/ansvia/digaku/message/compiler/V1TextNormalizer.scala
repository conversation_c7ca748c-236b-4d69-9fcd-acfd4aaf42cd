///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.message.compiler
//
//import java.util.regex.{<PERSON><PERSON>, <PERSON><PERSON>}
//
///**
// * Author: robin
// * Date: 2/15/14
// * Time: 3:24 PM
// *
// * normalizer untuk normalisasi text format versi BCA MC2 1 (yang lama / Markdown)
// * ke versi yang baru (Anstile)
// */
//object V1TextNormalizer {
//
//
//    private val alignmentRe = """\[\/?:(center|justify)\]""".r
//    private val boldRe = """\*\*\*?(.*?)\*\*\*?""".r
//    private val linkRe = """\[(.*?)\]\((.*?)\)""".r
//
//    private def removeAlignments(text:String) = {
//        alignmentRe.replaceAllIn(text, "")
//    }
//
//
//    def normalize(text:String) = {
//        var rv = text
//        rv = removeAlignments(rv)
////        println(rv)
//        rv = boldRe.replaceAllIn(rv, { x =>
//            if (x.groupCount > 0){
//                Matcher.quoteReplacement("*%s*".format(x.group(1)))
//            }else{
//                Matcher.quoteReplacement(x.group(0))
//            }
//        })
//        rv = linkRe.replaceAllIn(rv, { x =>
//            if (x.groupCount > 1){
//                Matcher.quoteReplacement(""" "%s":%s """.format(x.group(1), x.group(2)).trim)
//            }else if (x.groupCount > 0){
//                Matcher.quoteReplacement(""" "":%s """.format(x.group(1)))
//            }else{
//                Matcher.quoteReplacement(x.group(0))
//            }
//        })
//
//        // trim trailing spaces after before new line
//        rv = rv.split("\n").map(_.replaceFirst("\\s+$","")).mkString("\n")
//
//        rv.trim
//    }
//
//
//}
