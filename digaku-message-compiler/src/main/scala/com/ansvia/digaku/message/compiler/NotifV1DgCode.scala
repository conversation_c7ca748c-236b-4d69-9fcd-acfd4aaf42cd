///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.message.compiler
//
//
//import com.ansvia.digaku.model._
//import com.ansvia.commons.logging.Slf4jLogger
//import java.util.regex.Matcher
//import builder._
//import com.ansvia.digaku.model
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.helpers.DbAccess
//
//import scala.util.matching.Regex
//
//// @TODO(*): remove this backward compatibility
//// safe to remove after 28 Jan 2016
//// begin backward compatibility ---------------------------
//abstract class NotifV1DgCode(text:String) extends DgCodeBase(text) with <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ger with DbAccess {
//
//    // @TODO(fajr): make unitest
//
//    /**
//     * Berguna untuk mengubah format dgcode V2 ke dgcode v1
//     */
//
//    val baseUrl:String
//    /**
//     * WAJIB DI-OVERRIDE AGAR BISA DIGUNAKAN.
//     * @param p
//     * @return
//     */
//    implicit def postBuilder(p:Post):PostBuilder.PostNodeSeqWrapperBase
//        // = new PostBuilder.UnimplementedPostNodeSeqWrapper(p)
//
//    implicit def responseBuilder(resp:Response):ResponseBuilder.ResponseNodeSeqWrapperBase
//        // = new PostBuilder.UnimplementedResponseNodeSeqWrapper(resp)
//
//    implicit def picBuilder(p:Picture):PictureBuilder.PictureWrapperBase
//        // = new PicBuilder.UnimplementedPictureWrapperBase(p)
//
//    implicit def eventBuilder(ev:Event):EventBuilder.EventNodeSeqWrapperBase
//        // = new EventBuilder.UnimplementedEventBuilderWrapper(ev)
//    implicit def userBuilder(user:model.User):UserBuilder.UserWrapperBase
//
//    implicit def privateMessageBuilder(pm:PrivateMessage):PrivateMessageBuilder.MessageNodeSeqWrapperBase
//
//    implicit def messageResponseBuilder(mr:MessageResponse):PrivateMessageBuilder.ResponseNodeSeqWrapperBase
//
//    protected implicit def originBuilder(origin:model.Origin[GraphType]):OriginBuilder.OriginWrapperBase
//
//
//    def compileApiV1:String = {
//        //        debug("compiling: " + text)
//        var rv = NotifV1DgCode.USER_REGEX.replaceAllIn(text, replacerV1 _)
//        rv = NotifV1DgCode.CHANNEL_REGEX.replaceAllIn(rv, replacerV1 _)
//        rv = NotifV1DgCode.SIMPLE_POST_REGEX.replaceAllIn(rv, replacerV1 _)
//        rv = NotifV1DgCode.HISHER_REGEX.replaceAllIn(rv, replacerV1 _)
//        rv = NotifV1DgCode.RESPONSE_REGEX.replaceAllIn(rv, replacerV1 _)
//        rv = NotifV1DgCode.INVITATION_REGEX.replaceAllIn(rv, replacerV1 _)
//        rv = NotifV1DgCode.MESSAGE_RESPONSE_REGEX.replaceAllIn(rv, replacerV1 _)
//        rv = NotifV1DgCode.PRIVATE_MESSAGE_REGEX.replaceAllIn(rv, replacerV1 _)
//        rv
//    }
//
//    def strippedV1DgCode:String = {
//        var rv = NotifV1DgCode.USER_REGEX_STRIPPER.replaceAllIn(compileApiV1, {re =>
//            re.group(1)
//        })
//        rv = NotifV1DgCode.ORIGIN_REGEX_STRIPPER.replaceAllIn(rv, {re =>
//            "post"
//        })
//        rv = NotifV1DgCode.PICTURE_REGEX_STRIPPER.replaceAllIn(rv, {re =>
//            "picture"
//        })
//        rv = NotifV1DgCode.PICTURE_GROUP_REGEX_STRIPPER.replaceAllIn(rv, {re =>
//            "pictures"
//        })
//        rv = NotifV1DgCode.PRIVATE_MESSAGE_REGEX_STRIPPER.replaceAllIn(rv, {re =>
//            "whisper"
//        })
//
//        rv = NotifV1DgCode.RESPONSE_REGEX_STRIPPER.replaceAllIn(rv, {re =>
//            "response"
//        })
//
//        rv = NotifV1DgCode.GENDER_FEMALE_STRIPPER.replaceAllIn(rv, {re =>
//            "her"
//        })
//
//        rv = NotifV1DgCode.GENDER_MALE_STRIPPER.replaceAllIn(rv, {re =>
//            "his"
//        })
//
//        rv
//    }
//
//    private def replacerV1(m:Regex.Match):String = {
//        //        debug("m.groupCount: " + m.groupCount)
//        m.group(2) match {
//            case "user" =>
//                processUser(m.group(3), m.group(4))
//
//            case "ch" =>
//                processChannel(m.group(3), m.group(4))
//
//            case "his" | "her" | "your" =>
//                processGender(m.group(2))
//
//            case "simple-post" | "post" =>
//                processSimplePost(m.group(3), m.group(4), m.group(5))
//
//            case "picture" =>
//                processPicture(m.group(3), m.group(4), m.group(5))
//
//            case "picture-group" =>
//                processPictureGroup(m.group(3), m.group(4), m.group(5))
//
//            case "response" =>
//                processResponse(m.group(3), m.group(4), m.group(5))
//
//            case "private-message" =>
//                processPrivateMessage(m.group(3))
//
//            case "message-response" =>
//                processMessageResponse(m.group(3))
//
//            case x =>
//                "NotHandled(%s)".format(x.toString)
//        }
//    }
//
//    def processUser(userName:String, userId:String):String = {
//        User.getByName(userName).map(user => "\\$[user:" + user.getId.toString + ";" + user.name + "]").getOrElse("---")
//    }
//
//    def processChannel(channelName:String, channelId:String):String = {
//        Group.getByName(channelName).map(ch => ch.name ).getOrElse("---")
//    }
//
//    def normalizeKind(kind:Int):String = {
//        kind match {
//            case PostKind.SIMPLE_POST => "UserStatus"
//            case x => PostKind.kindIntToStr(x)
//        }
//    }
//
//    def processSimplePost(postId:String, originKind:String, originName:String):String = {
//        Matcher.quoteReplacement(Post.getPostById(postId.toLong).map( post => "$[post:" + post.getId + ";" + post.origin.getId + ";" + post.creator.name + ";" + normalizeKind(post.kind) + "]").getOrElse("---"))
//    }
//
//    /**
//     * Digunakan untuk mem-process link dari post picture
//     * @param picId id dari post picture
//     * @param originKind see [[com.ansvia.digaku.model.OriginKind]]
//     * @param originName see [[com.ansvia.digaku.model.Origin]]
//     * @return
//     */
//    def processPicture(picId:String, originKind:String, originName:String):String = {
//        Matcher.quoteReplacement(Picture.getById(picId.toLong).map( post => "$[picture:" + post.getId + ";" + post.origin.getId + ";" + post.creator.name + ";" + normalizeKind(post.kind) + "]").getOrElse("---"))
//    }
//
//    def processPictureGroup(picGroupId:String, originKind:String, originName:String):String = {
//        Matcher.quoteReplacement(PictureGroup.getById(picGroupId.toLong).map( post => "$[picture-group:" + post.getId + ";" + post.origin.getId + ";" + post.creator.name + ";" + normalizeKind(post.kind) + "]").getOrElse("---"))
//    }
//
//    def processResponse(respId:String, originKind:String, originName:String):String = {
//        try {
//            Matcher.quoteReplacement(Response.getById(respId.toLong).map { resp =>
//                val responable = resp.getRespondedObject.get
//
//                Post.getPostById(responable.getId).map { post =>
//                    "$[post:" + post.getId + ";" + post.origin.getId + ";" + post.creator.name + ";" + post.kind + "]"
//                }.getOrElse {
//                    Picture.getPictureFromBaseById(responable.getId).map { pict =>
//                        "$[post:" + pict.getId + ";" + pict.origin.getId + ";" + pict.creator.name + ";" + pict.kind + "]"
//                    }.getOrElse("----")
//                }
//            }.getOrElse("---"))
//        }catch{
//            case e:NumberFormatException =>
//                Matcher.quoteReplacement("---")
//        }
//    }
//
//    def processGender(gender:String):String = {
//
//        val genderMatch = gender match {
//            case "his" => "\\$[he:1]"
//            case "her" => "\\$[he:0]"
//            case "your" => "your"
//            case _ => "---"
//        }
//
//        "responded on " + genderMatch
//
//        }
//
//    def processMessageResponse(messRespId:String):String = {
//        try {
//            MessageResponse.getById(messRespId.toLong).map{ msgResp =>
//                "responded on"
//            }.getOrElse("---")
//        }catch{
//            case e:NumberFormatException =>
//                Matcher.quoteReplacement("---")
//        }
//    }
//
//    def processPrivateMessage(privateMessageId:String):String = {
//        try {
//            PrivateMessage.getById(privateMessageId.toLong).map { message =>
//                "\\$[whisper:" + message.getId + "]"
//            }.getOrElse("----")
//        }catch{
//            case e:NumberFormatException =>
//                Matcher.quoteReplacement("---")
//        }
//    }
//}
//// end of backward compatibility ---------------------------
//
//
//object NotifV1DgCode {
//
//    /**
//     * Format $[user:USER-NAME;USER-ID]
//     */
//    private lazy val USER_REGEX = """(\$\[(user)\:(.*?);(.*?)\])""".trim.r
//
//    /**
//     * Format $[ch:FORUM-NAME;GROUP-ID]
//     */
//    private lazy val CHANNEL_REGEX = """(\$\[(ch)\:(.*?);(.*?)\])""".trim.r
//
//    private lazy val HISHER_REGEX = """(responded to )((?:[a-z][a-z]+))""".trim.r
//
//    private lazy val SIMPLE_POST_REGEX = """(\$\[(simple\-post|picture\-group|post|picture|event)\:(.*?);(.*?);(.*?)\])""".trim.r
//    private lazy val RESPONSE_REGEX = """(\$\[(response)\:(.*?);(.*?);(.*?)\])""".trim.r
//    private lazy val INVITATION_REGEX = """(\$\[(invitation)\:(.*?);(.*?);(.*?)\])""".trim.r
//    private lazy val USER_NAME_REGEX = """\B@([a-zA-Z0-9\.\_]+)""".trim.r
//    private lazy val PRIVATE_MESSAGE_REGEX = """(message \$\[(private-message)\:(.*?)\])""".trim.r
//    private lazy val MESSAGE_RESPONSE_REGEX = """(reply \$\[(message-response)\:(.*?)\])""".trim.r
//
//    private lazy val USER_REGEX_STRIPPER = """\$\[user:\d+;([a-zA-Z0-9\.\_\-]+)\]""".trim.r
//    private lazy val ORIGIN_REGEX_STRIPPER = """\$\[post:\d+;\d+;([a-zA-Z0-9\-\.\_]+);([\w]+)\]""".trim.r
//    private lazy val PICTURE_REGEX_STRIPPER = """\$\[picture:\d+;\d+;([a-zA-Z0-9\-\.\_]+);([\w]+)\]""".trim.r
//    private lazy val PICTURE_GROUP_REGEX_STRIPPER = """\$\[picture\-group:\d+;\d+;([a-zA-Z0-9\-\.\_]+);([\w]+)\]""".trim.r
//    private lazy val PRIVATE_MESSAGE_REGEX_STRIPPER = """\$\[whisper:\d+\]""".trim.r
//    private lazy val RESPONSE_REGEX_STRIPPER ="""\$\[(post\-response|whisper\-response):.*\]""".trim.r
//    private lazy val GENDER_FEMALE_STRIPPER = """\$\[he:0\]""".trim.r
//    private lazy val GENDER_MALE_STRIPPER = """\$\[he:1\]""".trim.r
//
////    implicit def strToNotifV1DgCode(text:String) = new NotifV1DgCode(text)
//}
