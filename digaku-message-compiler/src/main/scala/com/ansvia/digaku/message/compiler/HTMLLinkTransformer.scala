/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.message.compiler

import java.net.{MalformedURLException, URI}
import com.ansvia.digaku.utils.LinkTransformer
import xml.{Null, UnprefixedAttribute, XML}

/**
 * Author: ubai
 * 
 */
class HTMLLinkTransformer extends LinkTransformer {
    def handle(linkStr:String):String = {
        try {
            val elm = XML.loadString(linkStr.replaceAll("&(amp;)*", "&amp;"))
            val url = elm.attribute("href").map(_.toString()).getOrElse("")
            val className = elm.attribute("class").map(_.toString()).getOrElse("")
            val link = new URI(url)

            // apabila link ada di local domain kita maka abaikan
            // apabila link adalah link embed maka abaikan
            if (("http://" + link.getHost) == HTMLLinkTransformer.BASE_URL ||
                ("https://" + link.getHost) == HTMLLinkTransformer.BASE_URL ||
                link.getHost == null ||
                className == "embedded-object"
            ) {
                linkStr
            } else {
                // apabila link adalah external maka tambah rel=nofollow
                (elm % new UnprefixedAttribute("rel", "nofollow", Null)).toString()
            }
        } catch {
            case e:MalformedURLException =>
                linkStr
            case e:Exception =>
                e.getStackTraceString.replaceAll("\n", "\n - ")
                linkStr
        }
    }
}

object HTMLLinkTransformer {
    var BASE_URL = "http://notset"
}

