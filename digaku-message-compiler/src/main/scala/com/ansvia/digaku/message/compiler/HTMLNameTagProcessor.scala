/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.message.compiler

import com.ansvia.digaku.utils.{NameCompiler, NameTagProcessor}

/**
 * Author: robin
 * 
 */
class HTMLNameTagProcessor extends NameTagProcessor {
    def handle(tag: String) = {
        tag(0) match {
            case '@' =>
                """<a data-hc-kind="user" class="user-mention" href="/%s/%s"><s>@</s><b>%s</b></a>""".format(HTMLNameTagProcessor.USER_URL_PREFIX,
                    tag.substring(1), tag.substring(1))
            case '#' =>
                """<a data-hc-kind="group" class="group-mention" href="/%s/%s"><s>#</s><b>%s</b></a>""".format(HTMLNameTagProcessor.CHANNEL_URL_PREFIX,
                    tag.substring(1), tag.substring(1))
        }
    }
}

object HTMLNameTagProcessor {
    var USER_URL_PREFIX = "url-prefix-not-set"
    var CHANNEL_URL_PREFIX = "url-prefix-not-set"
}

class HTMLNameCompiler extends NameCompiler {
    import HTMLNameTagProcessor._

    def compileUserName(str: String) = """<a href="/%s/%s">%s</a>""".format(USER_URL_PREFIX, str, str)

    def compileChannelName(str: String) = """<a href="/%s/%s">%s</a>""".format(CHANNEL_URL_PREFIX, str, str)
}