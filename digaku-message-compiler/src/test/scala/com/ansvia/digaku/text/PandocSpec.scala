/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.text

import org.specs2.mutable.Specification
import org.specs2.specification.Scope


/**
 * Author: robin
 * Date: 2/6/14
 * Time: 10:26 PM
 *
 */
class PandocSpec extends Specification {

    import Pandoc.formats._

    class Ctx extends Scope {
        val pandoc = new Pandoc("/usr/bin/pandoc")
    }

    "Pandoc convert" should {
        "convert html to textile #1" in new Ctx {
            val s =
                """
                  |<h1>hello</h1>
                  |<ol>
                  |   <li>satu</li>
                  |   <li>dua</li>
                  |</ol>
                """.stripMargin
            val t =
                """
                  |h1. hello
                  |
                  |# satu
                  |# dua
                """.stripMargin.trim

            val rv = pandoc.convert(HTML, TEXTILE, s)

            rv must_== t
        }
//        "convert html te textile (underline)" in new Ctx {
//            val s =
//                """
//                  |<u>satu</u>
//                  |<span style="text-decoration: underline;">dua</span>
//                """.stripMargin
//            val t =
//                """
//                  |+satu+
//                  |+dua+
//                """.stripMargin
//
//            pandoc.convert(HTML, TEXTILE, s) must_== t
//        }
    }
}

