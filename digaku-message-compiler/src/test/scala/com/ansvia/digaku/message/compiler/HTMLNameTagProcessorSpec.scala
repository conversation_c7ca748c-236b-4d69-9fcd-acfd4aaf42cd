/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.message.compiler

/**
 * Author: robin
 *
 */

import org.specs2.Specification

class HTMLNameTagProcessorSpec extends Specification {

    def is = "HTML name tag processor harusnya" ^
        sequential ^
        "bisa compile user name tag" ! trees.canCompileUserNameTag ^
        "bisa compile group name tag" ! trees.canCompileChannelNameTag ^
        "tidak memproses username tag di dalam link" ! trees.noCompileUserNameTagInLink ^
        "tidak memproses group tag di dalam link" ! trees.noCompileChannelTagInLink ^
        end

    object trees {

        HTMLNameTagProcessor.CHANNEL_URL_PREFIX = "group"
        HTMLNameTagProcessor.USER_URL_PREFIX = "u"

        val htmlTagProcessor = new HTMLNameTagProcessor()


        def canCompileUserNameTag = {
            htmlTagProcessor.compile("hello @robin keren") must_== "hello <a data-hc-kind=\"user\" class=\"user-mention\" href=\"/u/robin\"><s>@</s><b>robin</b></a> keren"
        }

        def canCompileChannelNameTag = {
            htmlTagProcessor.compile("hello #group keren") must_== "hello <a data-hc-kind=\"group\" class=\"group-mention\" href=\"/forum/group\"><s>#</s><b>group</b></a> keren"
        }


        def noCompileUserNameTagInLink = {
            val text = "hello ini @robin dan ini http://website.ku.com/@robin/yang/keren/banget.html"
            val expected = "hello ini <a data-hc-kind=\"user\" class=\"user-mention\" href=\"/u/robin\"><s>@</s><b>robin</b></a> dan ini http://website.ku.com/@robin/yang/keren/banget.html"
            htmlTagProcessor.compile(text) must_== expected
        }

        def noCompileChannelTagInLink = {
            val text = "hello ini #group dan ini http://website.ku.com/#fragment"
            val expected = "hello ini <a data-hc-kind=\"group\" class=\"group-mention\" href=\"/forum/group\"><s>#</s><b>group</b></a> dan ini http://website.ku.com/#fragment"
            htmlTagProcessor.compile(text) must_== expected
        }

    }

}


