/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */
//
//package com.ansvia.digaku.message.compiler
//
//import org.specs2.Specification
//import com.ansvia.digaku.model._
//import com.ansvia.digaku.web.builder._
//import com.ansvia.digaku.model.builder.UserBuilder.UserWrapperBase
//import com.ansvia.digaku.Types._
//import com.ansvia.digaku.model.builder.OriginBuilder.OriginWrapperBase
//
// @TODO(robin): fix this
///**
// * Author: robin
// * Date: 2/26/13
// * Time: 11:07 AM
// *
// */
//class HtmlDgCodeSpec extends Specification {
//
//    def is =
//        "HtmlDgCode compiler should" ^
//        p ^
//            "Compile message $[user:name,id]" ! trees.user ^
//            "Compile message $[ch:name,id]" ! trees.group ^
//            "Compile user name @robin" ! trees.compileUserName ^
//        end
//
//    class MyHtmlDgCode(text:String) extends HtmlDgCode(text) {
//        /**
//         * WAJIB DI-OVERRIDE AGAR BISA DIGUNAKAN.
//         * @param p
//         * @return
//         */
//        implicit def postBuilder(p: Post) = {
//            MyPostBuilder.postModelWrapper(p)
//        }
//
//        implicit def responseBuilder(resp: Response) = MyPostBuilder.responseModelWrapper(resp)
//
//        implicit def picBuilder(p: Picture) = MyPicBuilder.picModelWrapper(p)
//
//        implicit def eventBuilder(ev: Event) = MyEventBuilder.eventModelWrapper(ev)
//
//        implicit def privateMessageBuilder(pm: PrivateMessage) = MyMessageBuilder.messageModelWrapper(pm)
//
//        implicit def messageResponseBuilder(mr: MessageResponse) = MyMessageBuilder.responseModelWrapper(mr)
//
//        implicit def userBuilder(user: User): UserWrapperBase = MyUserBuilder.userToUserWrapper(user)
//
//        protected implicit def originBuilder(origin: Origin[GraphType]): OriginWrapperBase = {
//            new MyOriginBuilder.MyOriginWrapper(origin)
//        }
//
//        val baseUrl: String = "http://digakutest.com"
//    }
//
//    object MyHtmlDgCode {
//        implicit def stringToHtmlDgCodeCompiler(text:String):MyHtmlDgCode = new MyHtmlDgCode(text)
//    }
//
//    object trees {
//
//        def user = {
//            new HtmlDgCode("hello $[user:robin;5]").compile must beEqualTo("hello <a href=\"/u/robin\">robin</a>")
//        }
//
//        def group = {
//            new HtmlDgCode("hello $[ch:motor;5123]").compile must beEqualTo("hello <a href=\"/forum/motor\">motor</a>")
//        }
//
//        def compileUserName = {
//            new HtmlDgCode("hi @robin he").compile must beEqualTo("""hi @<a href="/u/robin" title="robin">robin</a> he""")
//        }
//    }
//}
