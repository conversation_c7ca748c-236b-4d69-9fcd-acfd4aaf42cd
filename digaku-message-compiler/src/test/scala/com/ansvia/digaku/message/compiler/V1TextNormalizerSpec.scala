///*
// * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
// *
// * This file is part of Digaku project.
// *
// * Unauthorized copying of this file, via any medium is strictly prohibited
// * Proprietary and confidential.
// */
//
//package com.ansvia.digaku.message.compiler
//
//import org.specs2.mutable.Specification
//import org.specs2.specification.Scope
//import org.apache.commons.io.IOUtils
//import scala.collection.JavaConversions._
//
///**
// * Author: robin
// * Date: 2/15/14
// * Time: 3:38 PM
// *
// */
//class V1TextNormalizerSpec extends Specification {
//
//
//
//    class Ctx extends Scope {
//
//        case class Result(before:String, after:String)
//
//        def get(name:String) = {
//            val in = Thread.currentThread().getContextClassLoader.getResourceAsStream("test/" + name + "_before.txt")
//            val in2 = Thread.currentThread().getContextClassLoader.getResourceAsStream("test/" + name + "_after.txt")
//            val before = IOUtils.readLines(in).mkString("\n")
//            val after = IOUtils.readLines(in2).mkString("\n")
//            Result(before, after)
//        }
//
//    }
//
//    "V1 text normalizer" should {
//        for (i <- 1 to 5){
//            ("normalize sample #" + i) in new Ctx {
//
//                val rv = get("sample" + i)
//
//                val after = V1TextNormalizer.normalize(rv.before)
//
//                after must_== rv.after
//
//            }
//        }
//    }
//}
