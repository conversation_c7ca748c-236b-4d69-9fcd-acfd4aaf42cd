/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.plugin

/**
 * Digunakan untuk menandai sebuah plugin
 * adalah plugin yang perlu direstart.
 *
 * Author: robin
 * Date: 3/12/13
 * Time: 3:36 PM
 *
 */
class NeedRestartPlugin(plugin:DigakuPlugin) extends DigakuPlugin(plugin.name, plugin.desc, plugin.version) {
    def init() {}

    def close() {}

//    def install() {}

    def uninstall() {}

    override def toString = "NeedRestartPlugin(%s,%s)".format(name, version)
}
