/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.plugin

import com.ansvia.commons.logging.Slf4jLogger
import net.liftweb.sitemap.Menu.Menuable
import scala.xml.NodeSeq
import java.io.{FileOutputStream, File}
import net.liftweb.http.LiftRules
import com.ansvia.digaku.web.DigakuWebSession
import org.apache.commons.io.{IOUtils, FileUtils}


/**
 * Author: robin
 * Date: 3/11/13
 * Time: 3:16 PM
 * 
 */
abstract class DigakuPlugin(val name:String, val desc:String, val version:String)
    extends Slf4jLogger with WebAppGetter {

    import com.ansvia.digaku.plugin.DigakuPlugin.WidgetPosition

    var active = true

    debug("%s loaded".format(this))


    private var session:Option[DigakuWebSession] = None

    var file:Option[File] = None

    lazy val fileName = {
        file.map { f =>
            f.getName
        }.getOrElse(name + "-plugin-" + version + ".jar")
    }

    def init()
    def close()
    def install(){
        ensureMeInLibDir()
    }
    def uninstall()

    private def ensureMeInLibDir(){
        val onLibJar = new File(libDir + "/" + fileName)
        debug("checking for existent " + onLibJar.getAbsolutePath)
        if (!onLibJar.exists()){
            debug("not exists, trying to copy from `" + mySelfJar + "` to `" + onLibJar.getAbsolutePath + "`")
            copyMySelfToLibDir()
        }
    }
    
    
    /************************************************
     * STATE
     ***********************************************/

    def installNeedRestart:Boolean = false
    def uninstallNeedRestart:Boolean = false

    /**
     * Apakah plugin ini ready?
     * return true apabila ready, sebaliknya return false kalo broken.
     * @return
     */
    def isReady:Boolean = false


    /************************************************
     * FOR SITEMAP MANIPULATION
     ***********************************************/

    /**
     * Apabila plugin ingin memanipulasi sitemap
     * maka return-kan true
     * @see [[com.ansvia.digaku.plugin.DigakuPlugin.buildSitemapEntries]]
     * @return
     */
    def handleSitemap = false

    /**
     * Akan dieksekusi apabila [[com.ansvia.digaku.plugin.DigakuPlugin.handleSitemap]] == true
     * @param entries new menu entries.
     * @return
     */
    def buildSitemapEntries(entries:List[Menuable]):List[Menuable] = List.empty[Menuable]



    /************************************************
     * WIDGET
     ***********************************************/

    def hasWidget(pos:WidgetPosition):Boolean = false
    def getWidget(pos:WidgetPosition, ref:Any):NodeSeq = NodeSeq.Empty



    /************************************************
     * HOOKS
     ***********************************************/

    /**
     * Called before rewrite operation performed.
     * @param liftRules
     */
    def beforeRewrite(liftRules:LiftRules){
        // do nothing by default
    }

    /**
     * Dipanggil sebelum digaku engine di-init.
     * @param liftRules
     */
    def beforeSetupDigakuEngine(liftRules:LiftRules){

    }


    /**
     * Called after digaku engine initialized.
     */
    def afterSetupDigakuEngine(liftRules:LiftRules){
        // do nothing by default
    }

    /**
     * session ini dipanggil otomatis
     * setelah digaku engine terinisialisasi.
     */
    def setSession(sess:DigakuWebSession){
        session = Some(sess)
    }



    /************************************************
     * HELPERS FUNCTION
     ***********************************************/

    /**
     * Copy from resources to specific target
     *
     * @param path path relative to resources.
     * @param target target dir.
     */
    protected def copyResTo(path:String, target:String){
        
//        val cl = getClass.getClassLoader()
//        if (cl == null){
//            error("cannot get class loader for copying file from resources to " + target)
//            return
//        }

        val in = this.getClass.getResourceAsStream(path)
        
        if (in == null){
            error("cannot get file %s from resources/".format(path))
            return
        }

        if(!new File(target).getParentFile.exists())
            FileUtils.forceMkdir(new File(target).getParentFile)

        debug("extracting " + path + " to " + target)

        val out = new FileOutputStream(target)
        
        if (out == null){
            error("cannot get output file stream %s".format(target))
            return
        }

        try {
            IOUtils.copy(in, out)
        }catch{
            case e:Exception =>
                error(e.getMessage)
                e.printStackTrace()
        }finally{
            in.close()
            out.close()            
        }

    }

    /**
     * Copy from resources to webapp relative dir.
     * @param path path relative to resources.
     * @param targetRelPath target dir relative to webapp dir.
     */
    protected def copyResToWebApp(path:String, targetRelPath:String){
        val target = webappDir + targetRelPath
        copyResTo(path, target)
    }


    /**
     * Hapus file dari webapp.
     * @param path path relative ke webapp dir, harus diawali dengan `/`
     */
    def removeFileFromWebapp(path:String){
        debug("removing " + webappDir + path + "...")
        new File(webappDir + path).delete()
    }

    /**
     * File ke jar plugin ini.
     */
    protected lazy val mySelfJar = new File(getClass.getProtectionDomain.getCodeSource.getLocation.toURI)

    /**
     * Kopi file jar plugin ini ke lib directory yang di-recognize oleh
     * aplikasi.
     */
    protected def copyMySelfToLibDir(){
        FileUtils.copyFileToDirectory(mySelfJar, new File(libDir))
    }


    override def hashCode() = (name + version).hashCode

    override def toString = "Plugin(%s,%s)".format(name, version)
}



/************************************************
 * PLUGIN COMMONS TYPE DATA
 ***********************************************/

object DigakuPlugin {
    trait WidgetPosition
    object WidgetPosition {
        object ChannelRightHead extends WidgetPosition
        object ChannelRightTail extends WidgetPosition
        object HomeRightHead extends WidgetPosition
        object HomeRightTail extends WidgetPosition
        object HomeFooter extends WidgetPosition
    }
}


trait FileCommons {


    def fileExists(fileName:String):Boolean = {
        fileExists(new File(fileName))
    }

    def fileExists(file:File) = file.exists()

}

