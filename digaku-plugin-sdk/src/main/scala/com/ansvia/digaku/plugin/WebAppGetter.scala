/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.plugin

import java.io.File
import com.ansvia.digaku.exc.DigakuException
import net.liftweb.util.Props
import net.liftweb.util.Props.RunModes

/**
 * Author: robin
 * Date: 3/12/13
 * Time: 3:36 PM
 *
 * Helper untuk mendapatkan webapp directory
 * hanya berlaku untuk non jar/war project.
 */
trait WebAppGetter {

    /**
     * Helper for getting current directory.
     */
    protected lazy val currentDir = {
        new File(
            new File(
                new File(
                    (new File(
                        getClass.getProtectionDomain.getCodeSource.getLocation.getPath)).getParent
                ).getParent).getAbsolutePath)
    }



    /**
     * Helper for getting webapp dir.
     */
    protected lazy val webappDir:String = {
        var webapp = currentDir.getAbsolutePath

        if(webapp.endsWith("/WEB-INF")){
            webapp = currentDir.getParent
        }else{

            // if ends with /root assume to in jetty instance / container webapps
            if (!currentDir.getAbsolutePath.endsWith("/webapps/root")){

                val targets = Array(
                    currentDir.getAbsolutePath + "/webapp",
                    currentDir.getAbsolutePath + "/digaku-web/src/main/webapp",
                    currentDir.getAbsolutePath + "/war",
                    currentDir.getParentFile.getAbsolutePath + "/webapp",
                    currentDir.getParentFile.getAbsolutePath + "/digaku-web/src/main/webapp",
                    currentDir.getParentFile.getAbsolutePath + "/war",
                    currentDir.getParentFile.getParentFile.getAbsolutePath + "/webapp",
                    currentDir.getParentFile.getParentFile.getAbsolutePath + "/digaku-web/src/main/webapp",
                    currentDir.getParentFile.getParentFile.getAbsolutePath + "/war"
                )

                val webappO = targets.find(path => new File(path).exists())

                if(webappO.isDefined)
                    webapp = webappO.get
                else
                    throw new DigakuException("Cannot find webapp dir, curdir: " + currentDir.getAbsolutePath +
                        ". tried: \n" + targets.reduceLeft( _ + "   \n" + _))
            }

        }

        webapp
    }

    /**
     * Get lib directory.
     */
    protected lazy val libDir:String = {
        val ld = webappDir + "/WEB-INF/lib"
        if(!new File(ld).exists())
            throw new DigakuException("Cannot access lib dir: " + ld)
        ld
    }

}
