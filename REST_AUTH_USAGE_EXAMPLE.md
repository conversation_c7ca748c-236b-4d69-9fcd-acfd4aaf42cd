# REST Authentication Provider Usage Guide

## Configuration

Add the following configuration to your `digaku.conf` file:

```hocon
# REST Authentication Configuration
rest-auth {
    token-url = "https://sso-apigw-int.dti.co.id/auth/realms/3scaledev/protocol/openid-connect/token"
    auth-url = "https://api.devapps.ocp.dti.co.id/eai/adgateway/v2/api/verify"
    user-property-url = "https://api.devapps.ocp.dti.co.id/eai/adgateway/v2/api/user-property"
    client-id = "your-client-id"
    client-secret = "your-client-secret"
    source-client-id = "217CB44DEBAE6E66E0540021281A5568"
}
```

## Usage in Code

```scala
import com.ansvia.digaku.auth.{RESTAuthProvider, RESTConfig}

// Create configuration
val config = RESTConfig(
    applicationId = "MyCollaborationCommunity",
    cryptoKey = "your-crypto-key",
    path = "/your/path"
)

// Create provider instance
val restAuthProvider = new RESTAuthProvider(
    clientId = "your-client-id",
    endpoint = "https://api.example.com",
    config = config
)

// Authenticate user
val isAuthenticated = restAuthProvider.authenticate("username", "password")

// Get user properties
val userEmail = restAuthProvider.getUserProperty("username", "mail")
val displayName = restAuthProvider.getUserProperty("username", "displayName")
```

## API Endpoints

### 1. Token Endpoint
- **URL**: `https://sso-apigw-int.dti.co.id/auth/realms/3scaledev/protocol/openid-connect/token`
- **Method**: POST
- **Headers**: 
  - `Authorization: Basic {base64(client_id:client_secret)}`
  - `Content-Type: application/x-www-form-urlencoded`
- **Body**: `grant_type=client_credentials`

### 2. Authentication Endpoint
- **URL**: `https://api.devapps.ocp.dti.co.id/eai/adgateway/v2/api/verify`
- **Method**: POST
- **Headers**:
  - `Authorization: Bearer {access_token}`
  - `x-source-client-id: {source_client_id}`
  - `x-source-transaction-id: MC2-{user_id}_{random}`
  - `Content-Type: application/json`
- **Body**: `{"user_id":"username","password":"encrypted_password","application_id":"MyCollaborationCommunity"}`

### 3. User Property Endpoint
- **URL**: `https://api.devapps.ocp.dti.co.id/eai/adgateway/v2/api/user-property`
- **Method**: POST
- **Headers**: Same as authentication endpoint
- **Body**: `{"user_id":"username","property_name":"property_name"}`

## Response Formats

### Token Response (Success)
```json
{
    "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600,
    "refresh_expires_in": 0,
    "token_type": "Bearer",
    "scope": "email profile"
}
```

### Authentication Response (Success)
```json
{
    "error_schema": {
        "error_code": "EAI-000",
        "error_message": {
            "english": "Success",
            "indonesian": "Berhasil"
        }
    },
    "output_schema": {
        "status": "0"
    }
}
```

### User Property Response (Success)
```json
{
    "error_schema": {
        "error_code": "EAI-000",
        "error_message": {
            "english": "Success",
            "indonesian": "Berhasil"
        }
    },
    "output_schema": {
        "value": {
            "mail": "<EMAIL>",
            "displayName": "John Doe",
            "employeeId": "12345",
            "memberOf": "CN=Users,DC=example,DC=com",
            "department": "IT",
            "title": "Developer"
        }
    }
}
```

## Error Handling

The provider handles various error scenarios:
- Network timeouts
- Invalid credentials
- Server errors (500, 504)
- Authentication failures
- Token expiration

All errors are logged with appropriate detail levels for debugging.

## Caching

- Access tokens are cached in Redis for 3000 seconds (50 minutes)
- Cache key: `rest-auth-token`
- Automatic token refresh when expired
