/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.auth

import org.specs2.mutable.Specification
import org.specs2.specification.Scope

/**
 * Test for RESTAuthProvider
 * Author: robin (<EMAIL>)
 */
class RESTAuthProviderSpec extends Specification {

    "RESTAuthProvider" should {
        
        "create instance with proper configuration" in new TestContext {
            restAuthProvider.toString must contain("RESTAuthProvider")
            restAuthProvider.userIsAdmin("admin") must beTrue
            restAuthProvider.userIsAdmin("user") must beFalse
        }

        "have proper case class serialization" in {
            val tokenResponse = AccessTokenResponse("test_token", 3600, 0, "Bearer", "email profile")
            tokenResponse.access_token must equalTo("test_token")
            tokenResponse.expires_in must equalTo(3600)
            tokenResponse.token_type must equalTo("Bearer")
            
            val jsonString = tokenResponse.toJsonString
            jsonString must contain("test_token")
            jsonString must contain("Bearer")
            
            val parsed = AccessTokenResponse.fromJsonString(jsonString)
            parsed.access_token must equalTo("test_token")
            parsed.expires_in must equalTo(3600)
        }

        "have proper error response serialization" in {
            val errorResponse = TokenErrorResponse("invalid_client", "Client authentication failed")
            errorResponse.error must equalTo("invalid_client")
            errorResponse.error_description must equalTo("Client authentication failed")
            
            val jsonString = errorResponse.toJsonString
            jsonString must contain("invalid_client")
            jsonString must contain("Client authentication failed")
            
            val parsed = TokenErrorResponse.fromJsonString(jsonString)
            parsed.error must equalTo("invalid_client")
            parsed.error_description must equalTo("Client authentication failed")
        }

        "have proper auth response serialization" in {
            val errorMessage = ErrorMessage("Berhasil", "Success")
            val errorSchema = ErrorSchema("EAI-000", errorMessage)
            val outputSchema = AuthOutputSchema("0")
            val authResponse = AuthResponse(errorSchema, Some(outputSchema))
            
            authResponse.error_schema.error_code must equalTo("EAI-000")
            authResponse.error_schema.error_message.english must equalTo("Success")
            authResponse.output_schema.get.status must equalTo("0")
            
            val jsonString = authResponse.toJsonString
            jsonString must contain("EAI-000")
            jsonString must contain("Success")
            
            val parsed = AuthResponse.fromJsonString(jsonString)
            parsed.error_schema.error_code must equalTo("EAI-000")
            parsed.output_schema.get.status must equalTo("0")
        }

        "have proper user property response serialization" in {
            val userPropertyValue = UserPropertyValue(
                mail = Some("<EMAIL>"),
                displayName = Some("Test User"),
                employeeId = Some("12345"),
                memberOf = Some("CN=Users,DC=example,DC=com"),
                department = Some("IT"),
                title = Some("Developer")
            )
            val outputSchema = UserPropertyOutputSchema(userPropertyValue)
            val errorMessage = ErrorMessage("Berhasil", "Success")
            val errorSchema = ErrorSchema("EAI-000", errorMessage)
            val propertyResponse = UserPropertyResponse(errorSchema, Some(outputSchema))
            
            propertyResponse.output_schema.get.value.mail.get must equalTo("<EMAIL>")
            propertyResponse.output_schema.get.value.displayName.get must equalTo("Test User")
            
            val jsonString = propertyResponse.toJsonString
            jsonString must contain("<EMAIL>")
            jsonString must contain("Test User")
            
            val parsed = UserPropertyResponse.fromJsonString(jsonString)
            parsed.output_schema.get.value.mail.get must equalTo("<EMAIL>")
            parsed.output_schema.get.value.displayName.get must equalTo("Test User")
        }
    }

    trait TestContext extends Scope {
        val config = RESTConfig("MyCollaborationCommunity", "test-crypto-key", "/test/path")
        val restAuthProvider = new RESTAuthProvider("test-client-id", "https://test.example.com", config)
    }
}
