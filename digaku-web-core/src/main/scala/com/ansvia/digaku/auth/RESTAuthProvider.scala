/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.auth

import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.crypto.EnDeCipherDESTriple
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.exc.{DigakuException, UnauthorizedException}
import com.ansvia.digaku.web.lib.ToJsonString
import com.ansvia.digaku.web.WebConfig
import com.ansvia.util.AnsHttp
import net.liftweb.json.{Extraction, parse}
import org.apache.commons.io.IOUtils
import org.apache.commons.codec.binary.Base64

import java.io.{InputStream, OutputStream}
import java.net.HttpURLConnection
import java.nio.charset.StandardCharsets


/**
 * Author: robin (<EMAIL>)
 */
case class RESTConfig(applicationId: String, cryptoKey: String, path: String)

// Case classes for token management
case class AccessTokenResponse(access_token: String, expires_in: Int, refresh_expires_in: Int,
                              token_type: String, scope: String) extends ToJsonString

case class TokenErrorResponse(error: String, error_description: String) extends ToJsonString

// Case classes for authentication
case class ErrorMessage(indonesian: String, english: String) extends ToJsonString
case class ErrorSchema(error_code: String, error_message: ErrorMessage) extends ToJsonString
case class AuthResponse(error_schema: ErrorSchema, output_schema: Option[AuthOutputSchema]) extends ToJsonString
case class AuthOutputSchema(status: String) extends ToJsonString

// Case classes for user properties
case class UserPropertyValue(mail: Option[String], displayName: Option[String], employeeId: Option[String],
                           memberOf: Option[String], department: Option[String], title: Option[String]) extends ToJsonString
case class UserPropertyOutputSchema(value: UserPropertyValue) extends ToJsonString
case class UserPropertyResponse(error_schema: ErrorSchema, output_schema: Option[UserPropertyOutputSchema]) extends ToJsonString


class RESTAuthProvider(clientId: String, endpoint: String, config: RESTConfig)
    extends AuthProvider[String, Boolean]("REST") with Slf4jLogger {

    private val restAuthTokenKey = "rest-auth-token"
    private lazy val regCustom = Digaku.engine.kvStoreProvider.build("custom_config")

    // Configuration values - these should be set in WebConfig
    lazy val tokenUrl = WebConfig.REST_AUTH_TOKEN_URL
    lazy val authUrl = WebConfig.REST_AUTH_URL
    lazy val userPropertyUrl = WebConfig.REST_AUTH_USER_PROPERTY_URL
    lazy val authClientId = WebConfig.REST_AUTH_CLIENT_ID
    lazy val authClientSecret = WebConfig.REST_AUTH_CLIENT_SECRET
    lazy val sourceClientId = WebConfig.REST_AUTH_SOURCE_CLIENT_ID

    def userIsAdmin(username: String): Boolean = username == "admin"

    override def toString: String = {
        getClass.getSimpleName + s"(endpoint=$endpoint, clientId=$clientId)"
    }

    private def getStoredToken(): Option[AccessTokenResponse] = {
        regCustom.getOption(restAuthTokenKey)
            .map(AccessTokenResponse.fromJsonString)
    }

    def getAccessToken: String = {
        debug("Getting access token for REST authentication")

        getStoredToken().map(_.access_token).getOrElse {
            debug("No cached token found, requesting new token")

            var conn: HttpURLConnection = null
            var os: OutputStream = null
            var is: InputStream = null

            try {
                val url = tokenUrl
                debug(s"REST Auth token URL: $url")
                val http = new AnsHttp(url)
                http.setTls("TLSv1.2")

                // Prepare Basic Auth header
                val headerAuth = s"$authClientId:$authClientSecret".getBytes(StandardCharsets.UTF_8)
                val base64 = Base64.encodeBase64(headerAuth)
                val base64Str = new String(base64)
                debug("Base64 authorization prepared")

                conn = http.openConnection()
                conn.setConnectTimeout(5000)
                conn.setRequestMethod("POST")
                conn.setDoOutput(true)
                conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded")
                conn.setRequestProperty("Authorization", s"Basic $base64Str")

                val data = "grant_type=client_credentials"
                val out = data.getBytes(StandardCharsets.UTF_8)
                os = conn.getOutputStream
                os.write(out)

                val responseCode = conn.getResponseCode
                debug(s"Token request response code: $responseCode")

                responseCode match {
                    case 200 =>
                        is = conn.getInputStream
                        val str = IOUtils.toString(is, "UTF-8")
                        debug(s"Token response: $str")

                        val tokenResponse = AccessTokenResponse.fromJsonString(str)
                        // Cache token for 3000 seconds as requested
                        regCustom.set(restAuthTokenKey, tokenResponse.toJsonString, Some(3000))
                        tokenResponse.access_token

                    case 400 | 401 =>
                        is = conn.getErrorStream
                        val str = IOUtils.toString(is, "UTF-8")
                        val errorResponse = TokenErrorResponse.fromJsonString(str)
                        if (responseCode == 401) {
                            throw UnauthorizedException(s"[401-${errorResponse.error}] ${errorResponse.error_description}")
                        } else {
                            throw DigakuException(s"[400-${errorResponse.error}] ${errorResponse.error_description}")
                        }

                    case _ =>
                        is = conn.getErrorStream
                        val str = IOUtils.toString(is, "UTF-8")
                        throw DigakuException(s"Unexpected response code $responseCode: $str")
                }

            } catch {
                case e: Exception =>
                    error(s"Error getting access token: ${e.getMessage}")
                    throw DigakuException(s"Failed to get access token: ${e.getMessage}")
            } finally {
                if (conn != null) conn.disconnect()
                if (os != null) os.close()
                if (is != null) is.close()
            }
        }
    }

    override def authenticate[T <: String](authUserId: T, password: String): Boolean = {
        try {
            debug(s"Authenticating user: $authUserId")

            val accessToken = getAccessToken
            val DES = new EnDeCipherDESTriple(config.cryptoKey.getBytes)
            val encrypted = DES.encrypt(password)

            var conn: HttpURLConnection = null
            var os: OutputStream = null
            var is: InputStream = null

            try {
                val url = authUrl
                debug(s"Authentication URL: $url")
                val http = new AnsHttp(url)
                http.setTls("TLSv1.2")

                conn = http.openConnection()
                conn.setConnectTimeout(5000)
                conn.setRequestMethod("POST")
                conn.setDoOutput(true)
                conn.setRequestProperty("Content-Type", "application/json")
                conn.setRequestProperty("Authorization", s"Bearer $accessToken")
                conn.setRequestProperty("x-source-client-id", sourceClientId)

                // Generate transaction ID
                val randomNumber = util.Random.nextInt(10000)
                val transactionId = s"MC2-${authUserId}_$randomNumber"
                conn.setRequestProperty("x-source-transaction-id", transactionId)

                val requestData = s"""{"user_id":"$authUserId","password":"$encrypted","application_id":"${config.applicationId}"}"""
                debug(s"Authentication request data: $requestData")

                val out = requestData.getBytes(StandardCharsets.UTF_8)
                os = conn.getOutputStream
                os.write(out)

                val responseCode = conn.getResponseCode
                debug(s"Authentication response code: $responseCode")

                val str = if (responseCode == 200) {
                    is = conn.getInputStream
                    IOUtils.toString(is, "UTF-8")
                } else {
                    is = conn.getErrorStream
                    IOUtils.toString(is, "UTF-8")
                }

                debug(s"Authentication response: $str")

                responseCode match {
                    case 200 =>
                        val authResponse = AuthResponse.fromJsonString(str)
                        val errorCode = authResponse.error_schema.error_code

                        if (errorCode == "EAI-000") {
                            authResponse.output_schema.map(_.status) match {
                                case Some("0") =>
                                    debug("Authentication successful")
                                    true
                                case _ =>
                                    debug("Authentication failed - invalid status")
                                    false
                            }
                        } else {
                            debug(s"Authentication failed with error code: $errorCode")
                            false
                        }

                    case 400 =>
                        debug("Authentication failed - Bad Request")
                        false
                    case 401 =>
                        debug("Authentication failed - Unauthorized")
                        false
                    case 403 =>
                        debug("Authentication failed - Forbidden")
                        false
                    case 404 =>
                        debug("Authentication failed - Not Found")
                        false
                    case 500 =>
                        debug("Authentication failed - Internal Server Error")
                        false
                    case 504 =>
                        debug("Authentication failed - Gateway Timeout")
                        false
                    case _ =>
                        debug(s"Authentication failed - Unexpected response code: $responseCode")
                        false
                }

            } finally {
                if (conn != null) conn.disconnect()
                if (os != null) os.close()
                if (is != null) is.close()
            }

        } catch {
            case e: Throwable =>
                error(s"Authentication error for user $authUserId: ${e.getMessage}")
                e.printStackTrace()
                false
        }
    }


    def getUserProperty(userID: String, propertyName: String): Array[String] = {
        try {
            debug(s"Getting user property for userID: $userID, property: $propertyName")

            val accessToken = getAccessToken

            var conn: HttpURLConnection = null
            var os: OutputStream = null
            var is: InputStream = null

            try {
                val url = userPropertyUrl
                debug(s"User property URL: $url")
                val http = new AnsHttp(url)
                http.setTls("TLSv1.2")

                conn = http.openConnection()
                conn.setConnectTimeout(5000)
                conn.setRequestMethod("POST")
                conn.setDoOutput(true)
                conn.setRequestProperty("Content-Type", "application/json")
                conn.setRequestProperty("Authorization", s"Bearer $accessToken")
                conn.setRequestProperty("x-source-client-id", sourceClientId)

                // Generate transaction ID
                val randomNumber = util.Random.nextInt(10000)
                val transactionId = s"MC2-${userID}_$randomNumber"
                conn.setRequestProperty("x-source-transaction-id", transactionId)

                val requestData = s"""{"user_id":"$userID","property_name":"$propertyName"}"""
                debug(s"User property request data: $requestData")

                val out = requestData.getBytes(StandardCharsets.UTF_8)
                os = conn.getOutputStream
                os.write(out)

                val responseCode = conn.getResponseCode
                debug(s"User property response code: $responseCode")

                val str = if (responseCode == 200) {
                    is = conn.getInputStream
                    IOUtils.toString(is, "UTF-8")
                } else {
                    is = conn.getErrorStream
                    IOUtils.toString(is, "UTF-8")
                }

                debug(s"User property response: $str")

                responseCode match {
                    case 200 =>
                        val propertyResponse = UserPropertyResponse.fromJsonString(str)
                        val errorCode = propertyResponse.error_schema.error_code

                        if (errorCode == "EAI-000") {
                            propertyResponse.output_schema.map { outputSchema =>
                                val value = outputSchema.value

                                // Extract the requested property value
                                val propertyValue = propertyName.toLowerCase match {
                                    case "mail" => value.mail.getOrElse("")
                                    case "displayname" => value.displayName.getOrElse("")
                                    case "employeeid" => value.employeeId.getOrElse("")
                                    case "memberof" => value.memberOf.getOrElse("")
                                    case "department" => value.department.getOrElse("")
                                    case "title" => value.title.getOrElse("")
                                    case _ => ""
                                }

                                if (propertyValue.nonEmpty) {
                                    Array(propertyValue)
                                } else {
                                    Array.empty[String]
                                }
                            }.getOrElse(Array.empty[String])
                        } else {
                            debug(s"User property request failed with error code: $errorCode")
                            Array.empty[String]
                        }

                    case _ =>
                        debug(s"User property request failed with response code: $responseCode")
                        Array.empty[String]
                }

            } finally {
                if (conn != null) conn.disconnect()
                if (os != null) os.close()
                if (is != null) is.close()
            }

        } catch {
            case x: Throwable =>
                error(s"Cannot get user property, userID: $userID, property: $propertyName")
                error(x.getMessage)
                x.printStackTrace()
                Array.empty[String]
        }
    }
}

// Companion objects for JSON serialization/deserialization
object AccessTokenResponse {
    implicit val formats = net.liftweb.json.DefaultFormats

    def fromJsonString(str: String): AccessTokenResponse = {
        Extraction.extract[AccessTokenResponse](parse(str))
    }
}

object TokenErrorResponse {
    implicit val formats = net.liftweb.json.DefaultFormats

    def fromJsonString(str: String): TokenErrorResponse = {
        Extraction.extract[TokenErrorResponse](parse(str))
    }
}

object ErrorMessage {
    implicit val formats = net.liftweb.json.DefaultFormats

    def fromJsonString(str: String): ErrorMessage = {
        Extraction.extract[ErrorMessage](parse(str))
    }
}

object ErrorSchema {
    implicit val formats = net.liftweb.json.DefaultFormats

    def fromJsonString(str: String): ErrorSchema = {
        Extraction.extract[ErrorSchema](parse(str))
    }
}

object AuthResponse {
    implicit val formats = net.liftweb.json.DefaultFormats

    def fromJsonString(str: String): AuthResponse = {
        Extraction.extract[AuthResponse](parse(str))
    }
}

object AuthOutputSchema {
    implicit val formats = net.liftweb.json.DefaultFormats

    def fromJsonString(str: String): AuthOutputSchema = {
        Extraction.extract[AuthOutputSchema](parse(str))
    }
}

object UserPropertyValue {
    implicit val formats = net.liftweb.json.DefaultFormats

    def fromJsonString(str: String): UserPropertyValue = {
        Extraction.extract[UserPropertyValue](parse(str))
    }
}

object UserPropertyOutputSchema {
    implicit val formats = net.liftweb.json.DefaultFormats

    def fromJsonString(str: String): UserPropertyOutputSchema = {
        Extraction.extract[UserPropertyOutputSchema](parse(str))
    }
}

object UserPropertyResponse {
    implicit val formats = net.liftweb.json.DefaultFormats

    def fromJsonString(str: String): UserPropertyResponse = {
        Extraction.extract[UserPropertyResponse](parse(str))
    }
}
