/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.auth

import bca.ecm.ADGateway._
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.crypto.EnDeCipherDESTriple


/**
 * Author: robin (<EMAIL>)
 */
case class RESTConfig(applicationId: String, cryptoKey: String, path: String)


class RESTAuthProvider(clientId: String, endpoint: String, config: RESTConfig)
    extends AuthProvider[String, Boolean]("LDAP") with Slf4jLogger {

    def userIsAdmin(username: String): Boolean = username == "admin"

    override def toString: String = {
        getClass.getSimpleName + s"(endpoint=$endpoint, clientId=$clientId)"
    }

    def getAccessToken: String = {
        // check redis for active access_token

        // make a request for new access_token if not found

        // request example
        // curl --location 'https://sso-apigw-int.dti.co.id/auth/realms/3scaledev/protocol/openid-connect/token' --header 'Authorization: Basic ********************************************************' --header 'Content-Type: application/x-www-form-urlencoded' --data-urlencode 'grant_type=client_credentials'}

        // host `https://sso-apigw-int.dti.co.id` and authorization token `********************************************************` should be saved in env or config

        // response example
        // { 
        //     "access_token": "secret_access_token", 
        //     "expires_in": 3600, 
        //     "refresh_expires_in": 0, 
        //     "token_type": "Bearer", 
        //     "not-before-policy": 0, 
        //     "scope": "email profile" 
        // }

        // response error example
        // {  
            // "error": "Error name",  
            // "error_description": "error brief description"  
        // } 

        // cache access token for 3000s

        return access_token
    }

    override def authenticate[T <: String](authUserId: T, password: String): Boolean = {
        try {
            val accessToken = getAccessToken

            val DES = new EnDeCipherDESTriple(config.cryptoKey.getBytes)
            val encrypted = DES.encrypt(password)

            // authenticate request
            // curl --location 'https://api.devapps.ocp.dti.co.id/eai/adgateway/v2/api/verify' --header 'x-source-client-id: 217CB44DEBAE6E66E0540021281A5568' --header 'x-source-transaction-id: MC2-123abcd123' --header 'Content-Type: application/json' --header 'Authorization: Bearer {accessToken} --data '{ "user_id": $authUserId, "password": $encrypted, "application_id":"MyCollaborationCommunity"}'

            // x-source-client-id and x-source-transaction-id should be saved in env or config

            // response success is if error_code EAI: 000
            // example
            // { 
            //     "error_schema": { 
            //         "error_code": "EAI-000", 
            //         "error_message": { 
            //             "english": "Success", 
            //             "indonesian": "Berhasil" 
            //         }
            //     }, 
            //     "output_schema": { 
            //         "status": "0" 
            //     } 
            // } 

            // return true if success
            true
        } catch {
            case e:Throwable =>
                error("something wrong!")
                error(e.getMessage)
                false
        }
    }


    def getUserProperty(userID: String, propertyName: String): Array[String] = {
        try {
            // request = curl --location 'https://api.devapps.ocp.dti.co.id/eai/adgateway/v2/api/verify' --header 'x-source-client-id: 217CB44DEBAE6E66E0540021281A5568' --header 'x-source-transaction-id: MC2-123abcd123' --header 'Content-Type: application/json' --header 'Authorization: Bearer {accessToken from sso api} --data '{"user_id": $userID, "property_name": $propertyName }' 
            
            // response success is if error_code EAI: 000
            // example
            // { 
            //     "error_schema": { 
            //         "error_code": "EAI-000", 
            //         "error_message": { 
            //             "english": "Success", 
            //             "indonesian": "Berhasil" 
            //         } 
            //     }, 
            //     "output_schema": { 
            //         "value": { 
            //             "mail": null, 
            //             "displayName": "Vincentius Dian Asa Putra", 
            //             "employeeId": null, 
            //             "memberOf": "CN=M365_E3,OU=Groups,DC=dti,DC=co,DC=id~~CN=GSIT 
            // Internet,OU=Groups,DC=dti,DC=co,DC=id~~CN=RO 
            // Protection,OU=Groups,DC=dti,DC=co,DC=id~~CN=Digital Signature 
            // Internal,OU=Groups,DC=dti,DC=co,DC=id~~CN=Internet 
            // User,OU=KantorPusat,DC=dti,DC=co,DC=id", 
            //             "department": null, 
            //             "title": null 
            //         } 
            //     } 
            // } 

            rv = request.response.json['output_schema']['value']
            val newRv = rv.map { value =>
                if key == propertyName {
                    value
                } elsif value is null {
                    ""
                } else {
                    ""
                }
            }

            newRv
        } catch {
            case x:Throwable =>
                error(s"cannot get user property, userID: $userID, property: $propertyName")
                error(x.getMessage)
                x.printStackTrace()
                Array.empty[String]
        }
    }
}
