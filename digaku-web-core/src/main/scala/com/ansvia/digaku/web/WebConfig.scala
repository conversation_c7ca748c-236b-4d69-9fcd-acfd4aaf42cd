/*
 * Copyright (c) 2013-2015 Ansvia, Inc - All Rights Reserved
 *
 * This file is part of Digaku project.
 *
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential.
 */

package com.ansvia.digaku.web

import java.util.regex.Pattern
import com.ansvia.commons.logging.Slf4jLogger
import com.ansvia.digaku.Digaku
import com.ansvia.digaku.exc.DigakuException
import com.ansvia.digaku.model.ResponsePaginationMode._
import com.ansvia.digaku.persistence.CassandraDriver
import com.ansvia.digaku.utils.emo.EmoticonProcessor
import com.ansvia.digaku.web.util.NodeUtil
import com.twitter.ostrich.stats.CassandraBackedStatsConstant
import org.apache.commons.configuration.PropertiesConfiguration
import org.streum.configrity.Configuration
import scala.util.matching.Regex


/**
 * Author: robin
 *
 */

/**
 * Semua konfigurasi yang berhubungan dengan web
 * taruh di sini.
 */
object WebConfig extends Slf4jLogger {

    // digaku-web version
    val VERSION = "3.3.16" // auto generated, don't change this.

    lazy val clusterReg = Digaku.engine.kvStoreProvider.build("cluster_reg")

    /**
     * Ini harus diset dulu sebelum bisa digunakan
     * apabila tidak diset maka akan muncul error
     * NullPointerException ketika akses config seperti:
     * CASSANDRA_HOST, CASSANDRA_KEYSPCAE, CASSANDRA_REPLICATION_FACTOR,
     * CASSANDRA_CLUSTER_NAME, etc.
     */
    var conf: Configuration = _

    // ini harus diset dulu sebelum digunakan.
    var supportedEmos: Seq[EmoticonProcessor] = _

    lazy val BRAND_NAME = conf[String]("site.brand", "Digaku")
    lazy val DOMAIN_NAME = conf[String]("site.domain", "digaku.ansvia.com")
    lazy val HTTP_PROTOCOL = if (conf[Boolean]("site.use-ssl", false)) "https" else "http"
    lazy val AWS_HTTP_PROTOCOL = if (conf[Boolean]("aws.use-ssl", false)) "https" else "http"
    lazy val BASE_URL = HTTP_PROTOCOL + "://" + DOMAIN_NAME
    lazy val DIGAKU_SHORT_URL = conf[String]("site.short-url", "http://dgk.st")
    lazy val COOKIE_DOMAIN = conf[String]("site.cookie-domain", ".digakusite.com")
    lazy val NOTIF_EMAIL_DOMAIN = conf[String]("notif.email-domain", "notification.digaku.com")
    lazy val NOTIF_EMAIL_SENDER_NAME = conf[String]("notif.email-sender", "Digaku")

    lazy val CASSANDRA_HOST = conf[String]("database.cassandra.host", "unset")
    lazy val CASSANDRA_KEYSPACE = conf[String]("database.cassandra.keyspace", "titan")
    lazy val CASSANDRA_REPLICATION_STRATEGY = conf[String]("database.cassandra.replication-strategy-class", "SimpleStrategy")
    lazy val CASSANDRA_REPLICATION_STRATEGY_OPTS = conf[String]("database.cassandra.replication-strategy-options", "replication_factor:1")
    lazy val CASSANDRA_CLUSTER_NAME = conf[String]("database.cassandra.cluster-name", "digaku")

    lazy val LOG_FILE_CORE = conf[String]("log.file.core", "/var/log/digaku/core.log")
    lazy val LOG_FILE_WEB = conf[String]("log.file.web", "/var/log/digaku/web.log")
    lazy val LOG_FILE_SERVICE_CURRENT = conf[String]("log.file.service-current",
        "/etc/service/%s/log/main/current".format(BRAND_NAME.toLowerCase))

    lazy val NODE_ID = conf[String]("id", BRAND_NAME.toLowerCase)

    lazy val FB_CLIENT_ID = conf[String]("connect.fb.client-id", "")
    lazy val FB_CLIENT_SECRET = conf[String]("connect.fb.client-secret", "")
    lazy val TW_CONSUMER_KEY = conf[String]("connect.tw.consumer-key", "")
    lazy val TW_CONSUMER_SECRET = conf[String]("connect.tw.consumer-secret", "")
    lazy val GOOGLE_CLIENT_ID = conf[String]("connect.google.client-id", "")
    lazy val GOOGLE_CLIENT_SECRET = conf[String]("connect.google.client-secret", "")

    // group creation setting
    lazy val CHANNEL_CREATION_SETTINGS = conf[String]("group.creation.settings", "admin-only")
    lazy val CHANNEL_CREATION_USER_LEVEL_GT = conf[Int]("group.creation.user_level_gt", 5)
    lazy val CHANNEL_CREATION_USER_ABILITY = conf[String]("group.creation.user_ability", "create-group")

    // theme setting
    lazy val RESPONSE_PAGINATION_MODE = conf[String]("theme.response-pagination-mode", "page-number") match {
        case "page-number" => PAGE_NUMBER
        case x =>
            throw new DigakuException(s"Response pagination mode not supported: `$x`, only support `page-number` (default), " +
                s"please fix your config file for `theme.response-pagination-mode`")
    }

    val SUPPORT_CLOUDINARY = false

    /**
     * prefix url untuk group
     * akan muncul seperti: /forum/[NAMA-GROUP]
     */
    val CHANNEL_URL_PREFIX = "forum"

    /**
     * prefix urrl untuk user
     * akan muncul seperti: /u/[NAMA-USER]
     */
    val USER_URL_PREFIX = "u"

    // GRAPH FACEBOOK URL
    lazy val FB_GRAPH_URL = "https://graph.facebook.com/v2.3"

    // FACEBOOK URL
    lazy val FB_URL = "https://www.facebook.com/v2.3"

    // GAMMU SMS
    lazy val SUPPORT_SMS_GATEWAY = conf[Boolean]("engine.sms-gateway", false)
    lazy val GAMMU_REDIS = conf[String]("engine.gammu-redis","localhost:6379:0")
    lazy val DISABLE_MULTIPLE_LOGIN = conf[Boolean]("engine.disable-multiple-login", false)

    lazy val SUPPORT_DWH = conf[Boolean]("engine.support-dwh", false)

    // CHATBOT
    lazy val CHATBOT_BASE_URL = conf[String]("chatbot.base-url","")
    lazy val CHATBOT_APP_NAME = conf[String]("chatbot.app-name","")
    lazy val CHATBOT_APP_KEY = conf[String]("chatbot.app-key","")

    // EventHub
    lazy val EVENT_HUB_ENDPOINT = conf[String]("engine.eventhub.endpoint", "")

    // Node Util
    lazy val nodeUtil = new NodeUtil(clusterReg)

    lazy val NSQ_LOOKUP_HOST = conf[String]("nsq.lookup-host","")
    lazy val NSQ_PUBLISHER_HOST = conf[String]("nsq.publisher-host","")
    val NSQ_TOPIC = "digaku-notif"
    lazy val supportNsq = NSQ_LOOKUP_HOST != ""

    lazy val CSRF_PROTECTION = conf[Boolean]("engine.csrf-protection", true)

    lazy val ostrichCtx = {
        val c = conf.detach("database.analytics.cassandra")
        val cassandraHost = c("host", CASSANDRA_HOST)
        val cassandraClusterName = c("cluster-name", CASSANDRA_CLUSTER_NAME)
        val cassandraKeyspaceName = c("keyspace", "ostrich_digaku")
        val replicationStrategy = c("replication-strategy", CASSANDRA_REPLICATION_STRATEGY)
        val replicationStrategyOpts = c("replication-strategy-opts", CASSANDRA_REPLICATION_STRATEGY_OPTS)

        if (WebConfig.CASSANDRA_HOST == cassandraHost) {
            warn("It's not recommended using main " +
                "and ostrich analytics in single db, please separate it by setting " +
                "database.analytics.cassandra config")
        }

        info("ostrich cassandra using host: " + cassandraHost +
            ", cluster name: " + cassandraClusterName +
            ", keyspace: " + cassandraKeyspaceName +
            ", repl strategy: " + replicationStrategy +
            ", repl strategy opts: " + replicationStrategyOpts)

        val _ostrichCtx = CassandraDriver.getContext(cassandraClusterName,
            cassandraKeyspaceName, cassandraHost, replicationStrategy, replicationStrategyOpts)

        _ostrichCtx.start()
        _ostrichCtx.ensureKeyspaceExists(cassandraKeyspaceName)
        _ostrichCtx.ensureColumnFamilyExists(CassandraBackedStatsConstant.COLUMN_FAMILY_NAME,
            cassandraKeyspaceName,"org.apache.cassandra.db.marshal.TimeUUIDType")

        _ostrichCtx
    }

    def getPreconfiguredDBConfig(c: Configuration) = {

        val dbConf = new PropertiesConfiguration()

        val cassandraDriver = c("database.cassandra.driver", "astyanax")
        val storageBackend = cassandraDriver match {
            case "astyanax" => "cassandra"
            case "thrift" => "cassandrathrift"
        }

        dbConf.setProperty("storage.backend", storageBackend)
        dbConf.setProperty("cache.db-cache", true)
        dbConf.setProperty("cache.db-cache-time", 60000) // 1 minutes

        val hostNPort = c[String]("database.cassandra.host", "unset").split("\\:")

        dbConf.setProperty("storage.hostname", hostNPort(0))
        dbConf.setProperty("storage.port", hostNPort(1).toInt)
        dbConf.setProperty("storage.cassandra.keyspace", c[String]("database.cassandra.keyspace", "titan"))
        dbConf.setProperty("storage.cassandra.replication-strategy-class", c[String]("database.cassandra.replication-strategy-class", "SimpleStrategy"))
        dbConf.setProperty("storage.cassandra.replication-strategy-options",
            c[String]("database.cassandra.replication-strategy-options", "replication_factor:1")
                .replaceAll(Pattern.quote(":"), Regex.quoteReplacement(",")))
        dbConf.setProperty("storage.cassandra.write-consistency-level", c[String]("database.cassandra.write-consistency-level", "QUORUM"))
        dbConf.setProperty("storage.cassandra.read-consistency-level", c[String]("database.cassandra.read-consistency-level", "QUORUM"))
        dbConf.setProperty("storage.cassandra.astyanax.node-discovery-type", c[String]("database.cassandra.node-discovery-type", "NONE"))
        dbConf.setProperty("storage.cluster-name", c[String]("database.cassandra.cluster-name", "digaku_unset"))
        dbConf.setProperty("storage.batch-loading", c[Boolean]("database.storage.batch-loading", false))

        dbConf
    }

    // Biometric
    lazy val BIOMETRIC_ACTIVE = conf[Boolean]("biometric.active", false)
    lazy val BIOMETRIC_APP_ID = conf[String]("biometric.app-id", "")
    lazy val BIOMETRIC_CLIENT_ID = conf[String]("biometric.client-id", "")
    lazy val BIOMETRIC_X_SOURCE_CLIENT_ID = conf[String]("biometric.x-source-client-id", "")
    lazy val BIOMETRIC_CATEGORY_ID = conf[String]("biometric.category-id", "")
    lazy val BIOMETRIC_ENV = conf[String]("biometric.environment", "100")
    lazy val BIOMETRIC_SIGN_URL = conf[String]("biometric.sign-url", "")
    lazy val BIOMETRIC_VERIFICATION_URL = conf[String]("biometric.verification-url", "")
    lazy val BIOMETRIC_EMPLOYEE_TOKEN_URL = conf[String]("biometric.employee.token-url", "")
    lazy val BIOMETRIC_EMPLOYEE_BASE_URL = conf[String]("biometric.employee.base-url", "")
    lazy val BIOMETRIC_EMPLOYEE_CLIENT_ID = conf[String]("biometric.employee.client-id", "")
    lazy val BIOMETRIC_EMPLOYEE_CLIENT_SECRET = conf[String]("biometric.employee.client-secret", "")
    lazy val BIOMETRIC_EMPLOYEE_PASSWORD = conf[String]("biometric.employee.password", "")

    // REST Authentication
    lazy val REST_AUTH_TOKEN_URL = conf[String]("rest-auth.token-url", "https://sso-apigw-int.dti.co.id/auth/realms/3scaledev/protocol/openid-connect/token")
    lazy val REST_AUTH_URL = conf[String]("rest-auth.auth-url", "https://api.devapps.ocp.dti.co.id/eai/adgateway/v2/api/verify")
    lazy val REST_AUTH_USER_PROPERTY_URL = conf[String]("rest-auth.user-property-url", "https://api.devapps.ocp.dti.co.id/eai/adgateway/v2/api/user-property")
    lazy val REST_AUTH_CLIENT_ID = conf[String]("rest-auth.client-id", "")
    lazy val REST_AUTH_CLIENT_SECRET = conf[String]("rest-auth.client-secret", "")
    lazy val REST_AUTH_SOURCE_CLIENT_ID = conf[String]("rest-auth.source-client-id", "217CB44DEBAE6E66E0540021281A5568")
}


/**
 * Email settings ini diambil dari
 * configrity `conf`, di-set di Boot.scala.
 */
object MailSettings {
    lazy val SMTP_HOST = conf[String]("mail.smtp.host", "***************")
    lazy val SMTP_PORT = conf[Int]("mail.smtp.port", 25)
    private lazy val DOMAIN = {
        val s = WebConfig.DOMAIN_NAME.split("\\.")
        // remove www if any
        s.filterNot(_.toLowerCase == "www").mkString(".")
    }
    lazy val FROM_ADDRESS = "noreply@" + DOMAIN
    val PASSWORD = ""
    lazy val FROM_NAME = WebConfig.BRAND_NAME

    var conf:Configuration = _
}
